package com.howbuy.tms.counter.fundservice.trade;

import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.QueryAgreementDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/26 15:02
 * @since JDK 1.8
 */
public interface TmsAgreementCounterService {

    /**
     * 查询服务协议
     * @param custInfoDto
     * @param productCode
     * @param fundCodes
     * @return
     */
    QueryAgreementDto queryAgreement(CustInfoDto custInfoDto, String productCode, List<String> fundCodes);

}