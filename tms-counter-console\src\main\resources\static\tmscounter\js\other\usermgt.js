/**
*
* 系统-用户管理
*
*<AUTHOR>
*@date 2018-07-24 10:50
**/
$(function(){
         //初始化
       UserMgt.init();
 });

var UserMgt = {	
		
		/**
		 * 初始化
		 */
		init:function(){
			
			// 修改密码
			$("#openModifyPwdPageBtn").on('click',function(){
				UserMgt.openModifyPwdPage();
	        });
		
			$("#modifyPwdBtn").on('click',function(){
				UserMgt.modifyPwd();
	        });
			
			$(".closePageBtn").on('click',function(){
				UserMgt.closePopupPage();
	        });
			
			// 查看个人信息
			$("#openUserAcctInfoPageBtn").on('click',function(){
				UserMgt.queryLoginUserInfo();
	        });
			
			
			
		},
		
		/**
		 * 弹出密码修改页面
		 */
		openModifyPwdPage:function(){

			CommonUtil.clearForm("popupModifyPwdPage");
			
			layer.open({
				  type: 1 //Page层类型
				  ,area: ['400px', '300px']
				  ,title: '密码修改'
				  ,shade: 0.6 //遮罩透明度
				  ,maxmin: true //允许全屏最小化
				  ,anim: 1 //0-6的动画形式，-1不开启
				  ,content: $("#popupModifyPwdPage")
			});  
		},
		
		/**
		 * 关闭弹出框
		 */
		closePopupPage:function(){
			layer.closeAll('page');
		},
		
		/**
		 * 修改密码
		 */
		modifyPwd:function(){
			var oldPwd = $("#oldPwd").val();
			var newPwdOne = $("#newPwdOne").val();
			var newPwdTwo = $("#newPwdTwo").val();
			if(newPwdTwo != newPwdOne){
				CommonUtil.layer_tip("两个新密码不相同，请重新输入！");
				return;
			}
			
			if(newPwdOne.length < 6 || newPwdTwo.length < 6){
				CommonUtil.layer_tip("新密码长度不能小于6位！");
				return;
			}
			
			var uri= TmsCounterConfig.MODIFY_PWD_INDEX_URL  ||  {};
			var reqparamters ={"oldPwd":oldPwd,"newPwdOne":newPwdOne,"newPwdTwo":newPwdTwo};
    		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
    		CommonUtil.ajaxAndCallBack(paramters, UserMgt.modifyCallBack);
			
		},
		
		modifyCallBack:function(data){
			var respCode = data.code || '';
			var respDesc = data.desc || '';
			
			if(CommonUtil.isSucc(respCode)){
				CommonUtil.layer_tip("修改成功");
				
				UserMgt.closePopupPage();
			}else{
				CommonUtil.layer_tip("修改失败,"+respDesc+"("+respCode+")");
			}
		},
		
		queryLoginUserInfo:function(){
			var uri= TmsCounterConfig.QUERY_LOGIN_USER_INFO_URL ||  {};
			var reqparamters = {};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, UserMgt.openUserAcctInfoPage);
		},
		
		/**
		 * 弹出个人信息框
		 */
		openUserAcctInfoPage:function(data){
			var bodyData = data.body || {};
			
			var userAccount = bodyData.userAccount;
			$("#popupUserAccInfoPage #userName").val(userAccount.userName);
			$("#popupUserAccInfoPage #lastLogTime").val(CommonUtil.formatDateToStr(userAccount.lastLogTime, 'yyyy-MM-dd hh:mm:ss'));
			
			layer.open({
				  type: 1 //Page层类型
				  ,area: ['400px', '250px']
				  ,title: '个人信息'
				  ,shade: 0.6 //遮罩透明度
				  ,maxmin: true //允许全屏最小化
				  ,anim: 1 //0-6的动画形式，-1不开启
				  ,content: $("#popupUserAccInfoPage")
			});  
		}
		
		
};

