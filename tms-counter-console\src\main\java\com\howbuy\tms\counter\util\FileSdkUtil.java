package com.howbuy.tms.counter.util;

import com.alibaba.fastjson.JSON;
import com.howbuy.dfile.*;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.counter.common.FilePathStoreBusinessCodeConfig;
import org.springframework.beans.BeanUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * @Description:文件Sdk工具类
 * @Author: yun.lu
 * Date: 2024/1/18 9:24
 */
public class FileSdkUtil {

    public static final String URL_SEPARATOR = "/";

    /**
     * 获取绝对路径
     *
     * @param fileSdkPathInfo 路径信息
     * @return 绝对路径
     * @throws Exception
     */
    public static String getAbsolutePath(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return HFileService.getInstance().getAbsolutePath(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    public static byte[] read2Bytes(FileSdkPathInfo fileSdkPathInfo) throws IOException {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return HFileService.getInstance().read2Bytes(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    public static void parseFileSdkPathInfo(FileSdkPathInfo fileSdkPathInfo) {
        String middlePath = fileSdkPathInfo.getMiddlePath();
        // 如果middlePath不是以"/"结尾，则在后面拼接一个"/"
        if (StringUtils.isNotEmpty(middlePath) && !middlePath.endsWith(File.separator)) {
            middlePath += File.separator;
            fileSdkPathInfo.setMiddlePath(middlePath);
        }

        String fileName = fileSdkPathInfo.getFileName();
        // 如果fileName以"/"结尾，则去除这个"/"
        if (StringUtils.isNotEmpty(fileName) && fileName.startsWith(File.separator)) {
            fileName = fileName.substring(1);
            fileSdkPathInfo.setFileName(fileName);
        }
    }


    /**
     * 除最后一个元素，其余均会拼接【/】
     */
    public static String joinPath(String... paths) {
        StringBuilder sb = new StringBuilder();
        int limit = paths.length - 1;
        for (int i = 0; i < limit; i++) {
            String path = paths[i];
            if (StringUtils.isEmpty(path)) {
                continue;
            }
            sb.append(path);
            if (!path.endsWith(URL_SEPARATOR) && !path.endsWith(File.separator)) {
                sb.append(URL_SEPARATOR);
            }
        }
        sb.append(paths[limit]);
        return sb.toString();
    }

    /**
     * 生成文件目录
     *
     * @param fileSdkPathInfo 路径信息
     * @throws Exception
     */
    public static void mkdir(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        HFileService.getInstance().mkdir(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath());
    }

    /**
     * 获取reader
     *
     * @param fileSdkPathInfo 路径信息
     * @return reader
     * @throws Exception
     */
    public static HTextFileReader buildHTextFileReader(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return new HTextFileReader(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    public static HOutputStream buildHOutputStream(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return new HOutputStream(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    /**
     * 将文件绝对路径转换为FileSdkPathInfo
     *
     * @param absolutePath 绝对路径
     * @param businessCode 业务编码
     * @param relativeDir  配置中的配置路径
     */
    public static FileSdkPathInfo convertUrlToFileSdkPathInfo(String absolutePath, String businessCode, String relativeDir) {
        // 提取文件名
        int lastSlashIndex = absolutePath.lastIndexOf("/");
        String fileName = absolutePath.substring(lastSlashIndex + 1);

        // 找到 relativeDir 的结束位置
        int targetPathIndex = absolutePath.indexOf(relativeDir);
        if (targetPathIndex == -1) {
            throw new IllegalArgumentException("URL 中不包含relativeDir");
        }
        int startIndex = targetPathIndex + relativeDir.length();

        // 提取中间路径
        String middlePath = absolutePath.substring(startIndex, lastSlashIndex);
        if (StringUtils.isNotBlank(middlePath)) {
            middlePath = "/" + middlePath + "/";
            middlePath = middlePath.replace("//", "");
        }
        // 创建并返回 FileSdkPathInfo 实例
        FileSdkPathInfo info = new FileSdkPathInfo();
        info.setBusinessCode(businessCode);
        info.setMiddlePath(middlePath);
        info.setFileName(fileName);
        return info;
    }


    public static void main(String[] args) {
        FileSdkPathInfo fileSdkPathInfo = convertUrlToFileSdkPathInfo("http://webdav-nfs02.inner.ehowbuy.com/cc_certificate_file/20250623/9201311761-2-8809_shading.pdf", "CC_CERTIFICATE", "cc_certificate_file");
        System.out.println("fileSdkPathInfo:"+JSON.toJSON(fileSdkPathInfo));
    }


    public static HInputStream buildHInputStream(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return new HInputStream(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    public static void write(FileSdkPathInfo fileSdkPathInfo, InputStream srcInputStream) throws IOException {
        parseFileSdkPathInfo(fileSdkPathInfo);
        HFileService.getInstance().write(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName(),srcInputStream);
    }


    /**
     * webDav读文件
     */
    public static String[] readLines(FileSdkPathInfo fileSdkPathInfo) throws IOException {
        return HFileService.getInstance().readLines(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    /**
     * 获取reader
     *
     * @param fileSdkPathInfo 路径信息
     * @return reader
     * @throws Exception
     */
    public static HTextFileReader buildHTextFileReader(FileSdkPathInfo fileSdkPathInfo, String charset) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        if (StringUtils.isEmpty(charset)) {
            return new HTextFileReader(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
        } else {
            return new HTextFileReader(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName(), charset);
        }
    }

    /**
     * 获取Writer
     *
     * @param fileSdkPathInfo 路径信息
     * @return Writer
     * @throws Exception
     */
    public static HTextFileWriter buildHTextFileWriter(FileSdkPathInfo fileSdkPathInfo, String charset) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        if (StringUtils.isEmpty(charset)) {
            return new HTextFileWriter(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
        } else {
            return new HTextFileWriter(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName(), charset);
        }
    }

    /**
     * 获取Writer
     *
     * @param fileSdkPathInfo 路径信息
     * @return Writer
     * @throws Exception
     */
    public static HTextFileWriter buildHTextFileWriter(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return new HTextFileWriter(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    /**
     * 删除文件
     *
     * @param fileSdkPathInfo 路径信息
     * @return 是否成功
     * @throws Exception
     */
    public static Boolean deleteFile(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return HFileService.getInstance().deleteFile(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    /**
     * 文件是否存在
     *
     * @param fileSdkPathInfo 路径信息
     * @return 是否存在
     * @throws Exception
     */
    public static Boolean exists(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return HFileService.getInstance().exists(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName());
    }

    public static Boolean isDir(FileSdkPathInfo fileSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        return HFileService.getInstance().isDir(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath());
    }

    /**
     * 文件重命名
     *
     * @param fileSdkPathInfo 原文件路径信息
     * @param newFileName     新文件名
     * @return 新文件全路径
     * @throws Exception
     */
    public static String rename(FileSdkPathInfo fileSdkPathInfo, String newFileName) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        FileSdkPathInfo newFileSdkPathInfo = new FileSdkPathInfo();
        BeanUtils.copyProperties(fileSdkPathInfo, newFileSdkPathInfo);
        newFileSdkPathInfo.setFileName(newFileName);
        if (exists(newFileSdkPathInfo)) {
            deleteFile(newFileSdkPathInfo);
        }
        HFileService.getInstance().moveFile(fileSdkPathInfo.getBusinessCode(), fileSdkPathInfo.getMiddlePath(), fileSdkPathInfo.getFileName(), newFileSdkPathInfo.getMiddlePath(), newFileSdkPathInfo.getFileName());
        return getAbsolutePath(newFileSdkPathInfo);
    }

    /**
     * 将文件移动到新路径下,但是注意,必须是同一个businessCode下的
     */
    public static String move(FileSdkPathInfo srcSdkPathInfo, FileSdkPathInfo targetSdkPathInfo) throws Exception {
        parseFileSdkPathInfo(targetSdkPathInfo);
        HFileService.getInstance().moveFile(srcSdkPathInfo.getBusinessCode(), srcSdkPathInfo.getMiddlePath(), srcSdkPathInfo.getFileName(), targetSdkPathInfo.getMiddlePath(), targetSdkPathInfo.getFileName());
        return getAbsolutePath(targetSdkPathInfo);
    }

    /**
     * 解析url获取文件名
     */
    public static String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            // 如果 URL 中没有斜杠，说明整个字符串可能就是文件名
            return url;
        }
        return url.substring(lastSlashIndex + 1);
    }
    /**
     * 创建一个文件,并写入内容
     *
     * @param fileSdkPathInfo 路径信息
     * @param content         文件内容
     * @throws Exception
     */
    public static void createFile(FileSdkPathInfo fileSdkPathInfo, String content) throws Exception {
        parseFileSdkPathInfo(fileSdkPathInfo);
        HTextFileWriter hTextFileWriter = null;
        try {
            hTextFileWriter = buildHTextFileWriter(fileSdkPathInfo);
            hTextFileWriter.write(content);
        } finally {
            if (hTextFileWriter != null) {
                hTextFileWriter.flush();
                hTextFileWriter.close();
            }
        }


    }

    public static byte[] getBytes(String fileName) throws IOException {
        // 创建文件对象
        File file = new File(fileName);
        // 获取文件路径
        Path path = file.toPath();
        // 读取文件到字节数组
        return Files.readAllBytes(path);
    }
}
