package com.howbuy.tms.counter.common.remote;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.facade.BaseRequest;
import com.howbuy.common.facade.BaseResponse;
import com.howbuy.common.utils.MfDate;
import com.howbuy.common.utils.MfDateTime;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.hsb.txio.BaseTxResponse;
import com.howbuy.paycommon.model.enums.ProdLqdTypeEnum;
import com.howbuy.paycommon.model.enums.TermTypeEnum;
import com.howbuy.payonline.facade.base.model.Request;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.HttpUtil;
import com.howbuy.tms.counter.common.util.RequestUtil;

/**
 * <pre>
 *  todo:
 *  1.要记录下用户的调用接口日志，单独放一个日志文件，包含custNo或者idNo，调用时间，调用接口，返回代码和描述
 *  2.要记录所有交易的日志
 *  3.对于查询的日志如果返回代码不为0000，则记录下日志
 * </pre>
 *
 * <AUTHOR>
 * @create  2018-02-09 16:01:01
 */
public class TradeInvoker {

    private final static Logger log = LoggerFactory.getLogger(TradeInvoker.class);

    /**
     * 调用HSB，此方法不对返回做处理，需要调用者根据具体业务自行处理
     *
     * @param request TxRequest
     * @return BaseTxResponse
     */
//    public static BaseTxResponse call(TxRequest request) {
//        //设置分销商信息
//        setCommonParameters(request);
//        
//        //关联request、response
//        String txKeyTime = UUID.randomUUID().toString().replaceAll("-", "");
//        
//        log.info("###### request:[txKeyTime:{},txName:{}]{}", new Object[]{txKeyTime, request.getClass().getSimpleName(), JSON.toJSONString(request)});
//        long start = System.currentTimeMillis();
//        BaseTxResponse response = HSB.getInstance().callService(request, null);
//        long cost = System.currentTimeMillis() - start;
//        
//        log.info("###### response:[txKeyTime:{},txName:{},code:{},desc:{},cost:{}]{}", new Object[]{txKeyTime, request.getTxName(), response.getReturnCode(), response.getDescription(), String.valueOf(cost), JSON.toJSONString(response)});
//
//        return response;
//
//    }
    
    /**
     * <pre>
     * Call HSB
     * 如果returnCode返回非0000时，此方法抛出BizException运行时异常
     * </pre>
     *
     * @param request TxRequest
     * @return BaseTxResponse
     */
//    public static BaseTxResponse callWithThrowException(TxRequest request) {
//
//        BaseTxResponse response = call(request);
//
//        TmsCounterException exception = getException(response);
//        if (exception != null) {
//            throw exception;
//        }
//
//        return response;
//    }

    /**
     * 判断是否需要抛出异常
     *
     * @param response 检查的BaseTxResponse
     * @throws IllegalAccessException 
     * @throws InstantiationException 
     */
    public static <T> TmsCounterException getException(T response) {
    	String returnCode = "";
    	String description = "";
    	if(response instanceof BaseTxResponse){
    		BaseTxResponse res = (BaseTxResponse) response;
    		if (!isSuccess(res)) {
    			returnCode = res.getReturnCode();
    			description = res.getDescription();
                return new TmsCounterException(returnCode, description);
            }
    	}else if(response instanceof BaseResponse){
    		BaseResponse res = (BaseResponse) response;
    		if (!isSuccess(res)) {
    			returnCode = res.getReturnCode();
    			description = res.getDescription();
                //如果是系统异常则抛出
                return new TmsCounterException(returnCode, description);
            }
    	}
        return null;
    }


    /**
     * 判断与fd 交易是否正确,正确true.
     * @param response BaseTxResponse
     * @return boolean
     */
    public static boolean isSuccess(BaseTxResponse response) {
        if (response == null) {
            return false;
        }
        return isSuccess(response.getReturnCode());
    }

    /**
     * 判断与fd 交易是否正确,正确true.
     * @param returnCode .
     * @return boolean
     */
    public static boolean isSuccess(String returnCode) {
        return ReturnCodeEnum.SUCC.getCode().equals(returnCode) || ReturnCodeEnum.SUCC_NEW.getCode().equals(returnCode);
    }
    
    /**
     * 统一设置分销商信息
     *
     * @param request tx
     */
//    public static void setCommonParameters(TxRequest request) {
//
//    	if(StringUtil.isEmpty(request.getAppCode())){
//    		request.setAppCode("20");
//    	}
//    	if(StringUtil.isEmpty(request.getOperCode())){
//            request.setOperCode("00");
//        }
//        request.setTradeChannel("1");//柜台
//        request.setOperatorNo("counter");
//        request.setRegionCode("counter");
//        MfDateTime currDate = new MfDateTime();
//        request.setAppDt(currDate.toString(MfDate.strPatternYYYYMMDD));
//        request.setAppTm(currDate.toString(MfDate.strPatternHHMMSS));
//
//        if (StringUtil.isEmpty(request.getDisCode())) {
//            request.setDisCode("HB000A001");
//        }
//
//        if (StringUtil.isEmpty(request.getOutletCode())) {
//            request.setOutletCode("*********");
//        }
//
//        if (StringUtil.isEmpty(request.getOperatorNo())) {
//            request.setOperatorNo("counter");
//        }
//
//        String operIp = HttpUtil.getIpAddr(RequestUtil.getHttpRequest());
//        request.setOperIp(operIp);
//
//    }

    /**联机新接口返回码判断**/
    public static boolean isSuccess(BaseResponse response) {
        if (response == null) {
            return false;
        }
        return isSuccess(response.getReturnCode());
    }
    
    public static boolean isSuccessWithThrowException(BaseResponse response) {
        if(isSuccess(response)){
        	return true;
        }else{
        	 TmsCounterException exception = getException(response);
        	 if (exception != null) {
                 throw exception;
             }
        }
		return false;
    }
    
    public static <T extends BaseRequest> T createRequest(Class<T> t){
    	try {
    		T ret = t.newInstance();
			setCommonParameters(ret);
			log.info("retClass:{},retStr:{}",ret.getClass() ,JSON.toJSONString(ret));
			return ret;
		} catch (Exception e) {
			log.error("createRequestError:{}-{}", new Object[]{e.getClass(), e.getMessage()});
		} 
    	return null;
    }
    
    public static <T extends BaseResponse> T printResponse(T resonse){
    	log.info("{},{}",((BaseResponse)resonse).getClass() ,JSON.toJSONString(resonse));
    	return resonse;
    }
    
    /**payonline新接口公共参数**/
    public static void setCommonParameters(Request request){
    	setCommonParameters((BaseRequest)request);
    	if (null == request.getProdLqdType()) {
    		request.setProdLqdType(ProdLqdTypeEnum.HOWBUY_FUND);
    	}
    	request.setTermType(TermTypeEnum.WAP);
    }
    
    /**联机新接口公共参数**/
    public static void setKycParameters(com.howbuy.cc.center.feature.base.BaseRequest request) {
        request.setOutletCode("*********");
        String operIp = HttpUtil.getIpAddr(RequestUtil.getHttpRequest());
        request.setIp(operIp);
    }
    
    /**联机新接口公共参数**/
    public static void setCommonParameters(BaseRequest request) {

        request.setOperCode("00");
        //柜台
        request.setTradeChannel("1");
        request.setOperatorNo("counter");
        request.setRegionCode("counter");
        MfDateTime currDate = new MfDateTime();
        request.setAppDt(currDate.toString(MfDate.strPatternYYYYMMDD));
        request.setAppTm(currDate.toString(MfDate.strPatternHHMMSS));

        if (StringUtil.isEmpty(request.getDisCode())) {
            request.setDisCode("HB000A001");
        }

        if (StringUtil.isEmpty(request.getOutletCode())) {
            request.setOutletCode("*********");
        }


        String operIp = HttpUtil.getIpAddr(RequestUtil.getHttpRequest());
        request.setOperIp(operIp);

    }
    
    /**中台接口公共参数**/
    public static void setCommonParameters(com.howbuy.tms.common.client.BaseRequest request) {

        MfDateTime currDate = new MfDateTime();
        request.setAppDt(currDate.toString(MfDate.strPatternYYYYMMDD));
        request.setAppTm(currDate.toString(MfDate.strPatternHHMMSS));

        if (StringUtil.isEmpty(request.getDisCode())) {
            request.setDisCode("HB000A001");
        }

        if (StringUtil.isEmpty(request.getOutletCode())) {
            request.setOutletCode("*********");
        }

        if (StringUtil.isEmpty(request.getTxChannel())) {
            request.setTxChannel("1");
        }
        
        String operIp = HttpUtil.getIpAddr(RequestUtil.getHttpRequest());
        request.setOperIp(operIp);
        
        String uuid = UUID.randomUUID().toString();
        request.setDataTrack(uuid);

    }

}
