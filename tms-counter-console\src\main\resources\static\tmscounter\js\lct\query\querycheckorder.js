/**
*查询柜台复核订单
*<AUTHOR>
*@date 2017-04-11 16:17
*/

var QueryLctCheckOrder ={
		queryCheckOrderById:function(dealAppNo, callBack){
			var  uri= TmsCounterConfig.LCT_QUERY_CHECK_ORDER_BY_ID_URL  ||  {};
			var reqparamters = {};
			reqparamters.dealAppNo = dealAppNo;
			reqparamters.pageNum = 1;
			reqparamters.pageSize = 100;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
			CommonUtil.ajaxAndCallBack(paramters, callBack);
		},
		queryCheckOrderList:function(custNo){
			
		},
		queryCheckOrderListBack:function(data){
			
		},
		
};
