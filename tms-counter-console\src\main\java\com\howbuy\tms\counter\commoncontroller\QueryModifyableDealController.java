/**
 *Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlFacade;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlRequest;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlResponse;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.bean.FundDealOrderDtlBean;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.bean.QueryFundDealOrderDtlCondition;
import com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealFacade;
import com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealRequest;
import com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealResponse;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/***
 * 
 * @description:(查询可修改赎回方向的订单)
 * <AUTHOR>
 * @date 2023年5月23日 上午9:34:42
 * @since JDK 1.8
 */
@Controller
public class QueryModifyableDealController extends AbstractController {
    private static Logger logger = LogManager.getLogger(QueryModifyableDealController.class);

    @Autowired
    private QueryModifyableDealFacade queryCanModifyDealFacade;

    @Autowired
    private QueryFundDealOrderDtlFacade queryFundDealOrderDtlFacade;

    /**
     *
     * ModifyRedeemDirection:(查询可修改赎回方向的订单)
     * @param request
     * @param response
     * <AUTHOR>
     * @date 2023年05月23日 下午10:20:24
     * @since JDK 1.8
     */
    @RequestMapping("tmscounter/fund/querymodifyredeemdirection.htm")
    public void queryModifyRedeemDirectionDealList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("querymodifyredeemdirection.........");
        QueryModifyableDealRequest canModifyDealRequest = new QueryModifyableDealRequest();
        String custNo = request.getParameter("custNo");
        String dealNo = request.getParameter("dealNo");
        String appBeginDt = request.getParameter("appBeginDt");
        String appEndDt = request.getParameter("appEndDt");
        String idNo = request.getParameter("idNo");
        String disCode = request.getParameter("disCode");
        canModifyDealRequest.setTxAcctNo(custNo);
        canModifyDealRequest.setDealNo(dealNo);
        canModifyDealRequest.setAppBeginDt(appBeginDt);
        canModifyDealRequest.setAppEndDt(appEndDt);
        canModifyDealRequest.setIdNo(idNo);
        canModifyDealRequest.setDisCode(disCode);
        QueryModifyableDealResponse queryCanModifyDealResponse = queryCanModifyDealFacade.execute(canModifyDealRequest);
        List<QueryModifyableDealResponse.FundDealOrderDtlBean> dealList = null;
        if(queryCanModifyDealResponse!= null){
             dealList = queryCanModifyDealResponse.getFundDealOrderDtlPoList();
        }
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(dealList);
        WebUtil.write(response, tmsCounterResult);

    }


    /**
     *
     * ModifyRedeemDirection:(查)
     * @param request
     * @param response
     * <AUTHOR>
     * @date 2023年05月23日 下午10:20:24
     * @since JDK 1.8
     */
    @RequestMapping("tmscounter/fund/querydealorderdtl.htm")
    public void queryDealOrderDtl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("querydealorderdtl.........");
        // 订单明细信息
        QueryFundDealOrderDtlCondition queryCondition = new QueryFundDealOrderDtlCondition();
        String dealNo = request.getParameter("dealNo");
        queryCondition.setDealNo(dealNo);
        // 外部订单号
        QueryFundDealOrderDtlRequest dtlRequest = new QueryFundDealOrderDtlRequest();
        dtlRequest.setQueryCondition(queryCondition);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryFundDealOrderDtlFacade, dtlRequest, null);

        List<FundDealOrderDtlBean> fundDealOrderDtlBeanList = null;
        String redeemDirection = "";
        if (baseResp != null) {
            QueryFundDealOrderDtlResponse resp = (QueryFundDealOrderDtlResponse) baseResp;
            fundDealOrderDtlBeanList = resp.getFundDealOrderDtlBeanList();
            if(!CollectionUtils.isEmpty(fundDealOrderDtlBeanList)){
                FundDealOrderDtlBean dtlBean = fundDealOrderDtlBeanList.get(0);
                redeemDirection = dtlBean.getRedeemDirection();
            }
        }
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(redeemDirection);
        WebUtil.write(response, tmsCounterResult);
    }
}
