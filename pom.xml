<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>

	<groupId>com.howbuy.tms</groupId>
	<artifactId>tms-counter</artifactId>
	<version>20250707-001-RELEASE</version>
	<packaging>pom</packaging>
	<name>tms-counter</name>
	<properties>
		<!-- Generic test -->
		<!-- Generic properties -->
		<skipTests>true</skipTests>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<!-- Version select -->
		<sitemesh.version>2.4.2</sitemesh.version>
		<scheduler.version>0.0.3-SNAPSHOT</scheduler.version>
		<druid.version>0.2.9</druid.version>
		<cglib.version>2.2.0</cglib.version>
		<mybatis.version>3.2.8</mybatis.version>
		<mysql.version>5.1.14</mysql.version>
		<activemq.version>5.10.0</activemq.version>
		<xson.version>1.0.1</xson.version>
		<fastjson.version>1.2.17</fastjson.version>
		<ojdbc6.version>********.0</ojdbc6.version>
		<cache.version>1.2.1-RELEASE</cache.version>
		<zkclient.version>0.1</zkclient.version>
		<hessian.version>4.0.7</hessian.version>
		<pagehelper.version>3.6.3</pagehelper.version>
		<atomikos.version>3.9.3</atomikos.version>
		<taglibs.version>1.1.2</taglibs.version>
		<org.mybatis.version>1.2.2</org.mybatis.version>
		<commons.pool.version>1.6</commons.pool.version>
		<commons.beanutils.version>1.9.2</commons.beanutils.version>
		<junit.version>4.9</junit.version>
		<org.springframework.security.version>3.2.6.RELEASE</org.springframework.security.version>
		<com.howbuy.tms.common.version>1.0.0-release</com.howbuy.tms.common.version>
		<commons-fileupload.version>1.2</commons-fileupload.version>
		<org.codehaus.jackson.version>1.9.13</org.codehaus.jackson.version>
		<org.springframework.boot.version>1.2.0.RELEASE</org.springframework.boot.version>
		<log4j.version>2.15.0</log4j.version>
		<cluster4spring.version>0.85</cluster4spring.version>
		<supervisor.version>1.0.0-release</supervisor.version>
		<tms.counter>1.0.0-release</tms.counter>
		<tms.version>1.0.0-release</tms.version>
		<tms.high.batch.version>3.4.48-RELEASE</tms.high.batch.version>
		<jboss.netty.version>3.2.5.Final</jboss.netty.version>
		<org.apache.poi.version>3.15</org.apache.poi.version>
		<com.howbuy.high-order-center-client.version>4.8.70-RELEASE</com.howbuy.high-order-center-client.version>
		<com.howbuy.high-batch-center-client.version>4.8.70-RELEASE</com.howbuy.high-batch-center-client.version>
		<com.howbuy.tms-counter.version>20250707-001-RELEASE</com.howbuy.tms-counter.version>
		<com.howbuy.tms-common-validator.version>4.8.19-RELEASE</com.howbuy.tms-common-validator.version>
		<com.howbuy.tms-common-client.version>4.8.19-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.tms-common-enums.version>4.8.19-RELEASE</com.howbuy.tms-common-enums.version>
		<com.howbuy.tms-common-lang.version>4.8.19-RELEASE</com.howbuy.tms-common-lang.version>
		<com.howbuy.tms-common-security.version>4.7.99-RELEASE</com.howbuy.tms-common-security.version>
		<com.howbuy.tms-common-service.version>4.8.19-RELEASE</com.howbuy.tms-common-service.version>
		<com.howbuy.tms-common-service.version>4.8.19-RELEASE</com.howbuy.tms-common-service.version>
		<com.howbuy.tms-common-outerservice.version>4.8.19-RELEASE</com.howbuy.tms-common-outerservice.version>
		<com.howbuy.batch-center-client.version>4.8.48-RELEASE</com.howbuy.batch-center-client.version>
		<com.howbuy.fbs-online-search-facade.version>3.40.1-RELEASE</com.howbuy.fbs-online-search-facade.version>
        <com.howbuy.order-center-client.version>20250616001-RELEASE</com.howbuy.order-center-client.version>
		<com.howbuy.robot-order-center-client.version>20250616001-RELEASE</com.howbuy.robot-order-center-client.version>
		<com.howbuy.fin-online-facade.version>3.3.2-RELEASE</com.howbuy.fin-online-facade.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.product-center-client.version>4.8.48-RELEASE</com.howbuy.product-center-client.version>
		<com.howbuy.product-center-model.version>4.8.48-RELEASE</com.howbuy.product-center-model.version>
        <com.howbuy.pension-order-client.version>4.7.13-RELEASE</com.howbuy.pension-order-client.version>
        <com.howbuy.center-client.version>4.6.53-RELEASE</com.howbuy.center-client.version>

		<spring-cloud.version>Hoxton.SR12</spring-cloud.version>
		<alibaba.nacos.version>2.2.0.RELEASE</alibaba.nacos.version>
		<dubbo.version>2.7.15</dubbo.version>
		<zookeeper.version>3.4.13</zookeeper.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
		<com.howbuy.spring-context-support.version>1.0.11</com.howbuy.spring-context-support.version>
		<jedis.version>2.9.3</jedis.version>
		<com.howbuy-boot-actuator.version>1.1.4-RELEASE</com.howbuy-boot-actuator.version>
		<com.howbuy.howbuy-message-service.version>2.2.3.1-RELEASE</com.howbuy.howbuy-message-service.version>
		<com.howbuy.howbuy-message-amq-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-amq-2.version>
		<com.howbuy.howbuy-message-rocket-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-rocket-2.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
		<com.howbuy.webUtil.version>week-20231007-tgzhqzqh-RELEASE</com.howbuy.webUtil.version>
		<com.howbuy.howbuy-dfile.version>1.18.1-RELEASE</com.howbuy.howbuy-dfile.version>
		<com.howbuy.howbuy-session.version>1.0.1-RELEASE</com.howbuy.howbuy-session.version>
</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-service</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>logback-classic</artifactId>
						<groupId>ch.qos.logback</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-nfs</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-webdav</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>


			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>pension-order-client</artifactId>
				<version>${com.howbuy.pension-order-client.version}</version>
			</dependency>
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.cc</groupId>
				<artifactId>center-client</artifactId>
				<version>${com.howbuy.center-client.version}</version>
			</dependency>
			<dependency>
				<groupId>taglibs</groupId>
				<artifactId>standard</artifactId>
				<version>${taglibs.version}</version>
			</dependency>
			<!-- Spring??? -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-batch</artifactId>
				<version>${org.springframework.boot.version}</version>
			</dependency>

			<dependency>
				<groupId>net.sourceforge.cglib</groupId>
				<artifactId>com.springsource.net.sf.cglib</artifactId>
				<version>${cglib.version}</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjweaver</artifactId>
				<version>${aspectj.version}</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjrt</artifactId>
				<version>${aspectj.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>atomikos-util</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions-jdbc</artifactId>
				<version>${atomikos.version}</version>
			</dependency>

			<!-- ActiveMQ -->
			<dependency>
				<groupId>org.apache.activemq</groupId>
				<artifactId>activemq-all</artifactId>
				<version>${activemq.version}</version>
			</dependency>
			<!-- ??????? -->
			<dependency>
				<groupId>com.alibaba.druid</groupId>
				<artifactId>druid-wrapper</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>${mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.version}</version>
			</dependency>
			<dependency>
				<groupId>com.caucho</groupId>
				<artifactId>hessian</artifactId>
				<version>${hessian.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>${org.mybatis.version}</version>
			</dependency>
			<!-- ??????? -->
			<dependency>
				<groupId>com.github.xsonorg</groupId>
				<artifactId>xson-core</artifactId>
				<version>${xson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>opensymphony</groupId>
				<artifactId>sitemesh</artifactId>
				<version>${sitemesh.version}</version>
			</dependency>
			<dependency>
				<groupId>com.oracle</groupId>
				<artifactId>ojdbc6</artifactId>
				<version>${ojdbc6.version}</version>
			</dependency>

			<dependency>
				<groupId>org.javassist</groupId>
				<artifactId>javassist</artifactId>
				<version>3.18.2-GA</version>
			</dependency>
			<dependency>
				<groupId>com.github.sgroschupf</groupId>
				<artifactId>zkclient</artifactId>
				<version>${zkclient.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>${zookeeper.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>${commons.pool.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons.beanutils.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-core</artifactId>
				<version>${org.springframework.security.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jackson</groupId>
				<artifactId>jackson-mapper-asl</artifactId>
				<version>${org.codehaus.jackson.version}</version>
			</dependency>

			<!-- log4j2 -->

			<!--????log4j2jar?? -->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-api</artifactId>
				<version>${log4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-core</artifactId>
				<version>${log4j.version}</version>
			</dependency>

			<!--web?????????log4j-web????web???????? -->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-web</artifactId>
				<version>${log4j.version}</version>
			</dependency>

			<!--??????slf4j??????? -->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-slf4j-impl</artifactId>
				<version>${log4j.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-1.2-api</artifactId>
				<version>${log4j.version}</version>
			</dependency>
			<!-- log4j2 -->
			<dependency>
				<groupId>org.softamis</groupId>
				<artifactId>cluster4spring</artifactId>
				<version>${cluster4spring.version}</version>
			</dependency>
			<!-- asyncqueue -->
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-asyncqueue</artifactId>
				<version>1.0.0-release</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- asyncqueue -->
			
			<dependency>
				<groupId>com.howbuy.cc</groupId>
				 <artifactId>cim-web-client</artifactId>
				<version>3.6.4</version>
			</dependency>

			<!-- cache -->
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cachemanagement</artifactId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>servlet-api</artifactId>
						<groupId>javax.servlet</groupId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-api</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy</groupId>
						<artifactId>HowbuyMessage</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- cache -->


			<!-- 监控
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>supervisor-client</artifactId>
				<version>${supervisor.version}</version>
			</dependency>
			-->
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-session</artifactId>
				<version>${com.howbuy.howbuy-session.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-counter-service</artifactId>
				<version>${com.howbuy.tms-counter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-counter-client</artifactId>
				<version>${com.howbuy.tms-counter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-counter-common</artifactId>
				<version>${com.howbuy.tms-counter.version}</version>
			</dependency>
			
			<!-- <dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>howbuy-interlayer-product-client</artifactId>
				<version>${com.howbuy.howbuy-interlayer-product-client.version}</version>
			</dependency> -->

			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
		    	<artifactId>product-center-client</artifactId>
		    	<version>${com.howbuy.product-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-model</artifactId>
				<version>${com.howbuy.product-center-model.version}</version>
			</dependency>


			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>batch-center-client</artifactId>
				<version>${com.howbuy.batch-center-client.version}</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-client</artifactId>
				<version>${com.howbuy.tms-common-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-enums</artifactId>
				<version>${com.howbuy.tms-common-enums.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-lang</artifactId>
				<version>${com.howbuy.tms-common-lang.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-security</artifactId>
				<version>${com.howbuy.tms-common-security.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-service</artifactId>
				<version>${com.howbuy.tms-common-service.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-outerservice</artifactId>
				<version>${com.howbuy.tms-common-outerservice.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-aop</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-expression</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-validator</artifactId>
				<version>${com.howbuy.tms-common-validator.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-aop</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			
			
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-search-facade</artifactId>
				<version>${com.howbuy.fbs-online-search-facade.version}</version>
			</dependency>
			
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-client</artifactId>
			<version>${com.howbuy.high-order-center-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-batch-center-client</artifactId>
			<version>${com.howbuy.high-batch-center-client.version}</version>
		</dependency>
			<!-- <dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-search-client</artifactId>
				<version>${com.howbuy.order-center-search-client.version}</version>
			</dependency> -->
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-client</artifactId>
				<version>${com.howbuy.order-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>robot-order-center-client</artifactId>
				<version>${com.howbuy.robot-order-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>regular-order-center-client</artifactId>
				<version>${tms.version}</version>
			</dependency>


			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>regular-batch-center-client</artifactId>
				<version>${tms.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>simu-reallocate-client</artifactId>
				<version>${tms.version}</version>
			</dependency>
			<dependency>
				<groupId>org.jboss.netty</groupId>
				<artifactId>netty</artifactId>
				<version>${jboss.netty.version}</version>
			</dependency>
			
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${org.apache.poi.version}</version>
			</dependency>
			
			<!--资金 -->
			<dependency>
				<groupId>com.howbuy.finonline</groupId>
				<artifactId>fin-online-facade</artifactId>
				<version>${com.howbuy.fin-online-facade.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-log-pattern</artifactId>
				<version>RELEASE</version>
			</dependency>

			<!-- springcloud相关 -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>${alibaba.nacos.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>${alibaba.nacos.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.spring</groupId>
				<artifactId>spring-context-support</artifactId>
				<version>${com.howbuy.spring-context-support.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-dependencies-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<type>pom</type>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-amq-2</artifactId>
				<version>${com.howbuy.howbuy-message-amq-2.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket-2</artifactId>
				<version>${com.howbuy.howbuy-message-rocket-2.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<repositories>
		<repository>
			<id>maven2-repository.dev.java.net</id>
			<name>Java.net Repository for Maven</name>
			<url>http://download.java.net/maven/2/</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>ReleaseRepo</id>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/thirdparty</url>
		</repository>
		<repository>
			<id>nexus</id>
			<name>local private nexus</name>
			<url>http://maven.oschina.net/content/groups/public/</url>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>nexus</id>
			<name>local private nexus</name>
			<url>http://maven.oschina.net/content/groups/public/</url>
			<releases>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>howbuy-releases</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>howbuy-snapshots</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
<!--					<skipTests>true</skipTests>-->
					<includes>
						<include>**/*TestM.java</include>
					</includes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestEntries>
							<Package-Stamp>${parelease}</Package-Stamp>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<modules>
		<module>tms-counter-client</module>
		<module>tms-counter-common</module>
		<module>tms-counter-service</module>
		<module>tms-counter-console</module>
	</modules>
</project>