<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <import resource="classpath:context/context-config.xml"/>
    <import resource="classpath:context/spring-common.xml"/>
    <import resource="classpath:context/spring-servlet.xml"/>
    <import resource="classpath:context/spring-shiro.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-acc-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-batch-center-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-cc-assert-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-cc-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-crm-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-fund-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-high-batch-center-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-high-order-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-high-order-search-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-product-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-regular-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-rproduct-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-simu-consumer.xml"/>
    <import resource="classpath:META-INF/spring/dubbo-trade-consumer.xml"/>
    <import resource="classpath:dispatcher.xml"/>
    <import resource="classpath:hsb.xml"/>

    <!--依赖包中的配置文件-->
    <import resource="classpath*:context/spring-facade-crm.xml"/>
    <import resource="classpath*:context/spring-facade-paramcenter.xml"/>
    <import resource="classpath*:context/spring-facade-finonline.xml"/>
</beans>