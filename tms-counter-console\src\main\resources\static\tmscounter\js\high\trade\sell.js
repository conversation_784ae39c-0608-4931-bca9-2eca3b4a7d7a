/**
*赎回
*<AUTHOR>
*@date 2017-04-01 15:12
*/
$(function(){
	Sell.init();

});
var Sell = {
	init:function(){
		
		//初始化数据
		Sell.initData();

		// 初始绑定
        Sell.initBind();
		
		var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
		$(".selectconsCode").html(selectConsCodesHtml);
		
        // 初始化CRM参数数据
        OnLineOrderFile.initCrmUrl(Sell.urlParams,Sell.initCustQuery);
		
	},
    initCustQuery:function(hboneNo){

        /**
         * 初始化数据
         */
        Sell.initData();

        /**
         * 初始化购买表单
         */
        Sell.initDealForm();

        /**
         * 初始化预约信息列表
         */
        Sell.initTbList("rsList");

        /**
         * 初始化份额列表
         */
        Sell.initTbList("dtlList");

        /**
         * 查询客户基本信息
         */
        HighCustInfo.queryCustInfo(hboneNo);

        /**
         * 绑定客户选择事件
         */
        Sell.sellCustInfoBind();
    },

    initBind:function(){
        $("#confimSellBtn").on('click',function(){
            Sell.confirmNoAppointment();
        });
        $("#queryCustInfoBtn").on('click',function(){
            Sell.initCustQuery();
        });

        $("#custNo").on('dblclick',function(){
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        $(".searchIcon").on('click',function(){
            var fundCode = $("#fundCode").val();
            Sell.queryCustHodlInfo();
            Sell.queryFundInfo(fundCode);
            //查询赎回预约开放日历
            var appDt = $("#appDt").val();
            var appTm = $("#appTm").val();
            Sell.queryHighproductAppointinfo(fundCode, appDt, appTm, "1");
        });

        $("#fundCode").on('blur',function(){
            var fundCode = $("#fundCode").val();
            Sell.queryCustHodlInfo();
            Sell.queryFundInfo(fundCode);
            //查询赎回预约开放日历
            var appDt = $("#appDt").val();
            var appTm = $("#appTm").val();
            Sell.queryHighproductAppointinfo(fundCode, appDt, appTm, "1");
        });
    },
	
	/**
	 * 初始化下拉框
	 */
	initSelect:function(){
		
		//初始化异常赎回
		var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP, '0', null, null, true);
		$(".selectUnusualTransType").html(selectTransTypeHtml);
		
		//回款去向
		var selectRedeemDirHtml = CommonUtil.selectOptionsHtml(CONSTANTS.REDEEM_DIR_MAP);
		$("#redeemCapitalFlag").html(selectRedeemDirHtml);
		
		// 巨额赎回顺延标识 默认顺延
		var selectLargeRedmFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.LARGE_REDM_FLAG_MAP, '1', null, null, true);
		$("#selectLargeRedmFlag").html(selectLargeRedmFlagHtml);

		// 回可用备注
		$("#selectRedeemMemo").html(CommonUtil.selectOptionsHtml(CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP));


	},
	
	/**
	 * 初始化数据
	 */
	initData:function(){
		Sell.custInfo = {};//客户信息
		Sell.custHold = {};//客户持仓信息
		Sell.sellVol = {};//用户选择份额
		Sell.fundInfo = {};//产品信息
		Sell.currDate = '';//当前工作日
		Sell.appointment = {};//投顾预约信息
		Sell.highproductAppointinfo = {};//产品预约开放日历信息
        // url 参数
        Sell.urlParams = CommonUtil.getParamJson() || {};
	},
	
	/**
	 * 初始化列表
	 */
	initTbList:function(rsListId){
		// 预约列表清空
		$("#"+rsListId).html('');
	},
	
	/**
	 * 初始化表单数据
	 */
	initDealForm:function(){
		// 初始化用户选择份额
		Sell.sellVol = {};
		
		// 初始化产品信息
		Sell.fundInfo = {};
		
		// 初始化产品预约开放日历信息
		Sell.highproductAppointinfo = {};
		
		// 初始化购买formInput
		CommonUtil.cleanForm('sellConfirmForm');
		
		// 初始化只读文本
		CommonUtil.initReadeText('readText');
		
		// 初始化下拉框
		Sell.initSelect();

		// 删除赎回表单中的份额信息
        $("tbody[id^='sellFormTable']").each(function () {
            $(this).remove();
        });
	},
	
	/**
	 * 赎回客户绑定事件
	 */
	sellCustInfoBind : function(){
		
		$(".selectcust").click(function(){
			
			 //选中客户
			 $(this).attr('checked','checked').siblings().removeAttr('checked');
			 var selectIndex = $(this).attr("index");
			 Sell.custInfo =  HighCustInfo.custList[selectIndex] || {};
			 
			 //查询客户持仓明细
			 Sell.custBalDtl(Sell.custInfo.custNo, Sell.custInfo.disCode);
			 
			 //赎回预约信息查询
			 var tradeType = "3";// 赎回
			 Appoint.queryAppointmentInfo(Sell.custInfo.custNo, tradeType);

            // 绑定预约信息选择
            setTimeout(Sell.selectAppointBind(), 2000);
			 
			 //显示代理信息
			 Sell.showAgenInfo(Sell.custInfo.invstType);
		});
	   
	},
	
	/**
	 * @param invstType 0-机构 1-个人
	 * 机构用户显示代理信息
	 */
	showAgenInfo : function(invstType){
		if('0' == invstType || '2' == invstType){
			// 显示经办人信息
    	    HighCustInfo.queryCustTransInfo(Sell.custInfo.custNo, Sell.custInfo.disCode);
			 //代理
			 $(".selectAgened").val('1');
			 $("#transactorInfoForm").show();
		 }
	},
	
	/**
	 * 客户份额明细查询
	 */
	custBalDtl : function(txAcctNo, disCode, productCode){
		
		var  uri= TmsCounterConfig.HIGH_QUERY_CUST_BAL_DTL ||  "";
		var custNo = Sell.custInfo.custNo || '';
		var disCode = Sell.custInfo.disCode || '';
		var appDt = $("#appDt").val() || '';
		var appTm = $("#appTm").val() || '';
		productCode = productCode || '';
		
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		
		var reqparamters = {"custNo":custNo, "disCode":disCode, "productCode":productCode};
		reqparamters.appDt = appDt;
		reqparamters.appTm = appTm;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Sell.queryCustDtlCallBack);
	},
	
	/**
	 * 处理客户份额明细信息
	 */
	queryCustDtlCallBack:function(data){
		var bodyData = data.body || {};
		var dtlResp = bodyData || {};
		
		var dtlList = dtlResp.dtlList || [];
		Sell.dtlList = dtlList;
		
		if(Sell.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
			return false;
		}
		
		var appendHtml = "";
		$(Sell.dtlList).each(function(index,element){
			
			var trList = [];
	    	trList.push('<input class="selectcustbal" name="checkBalDtl" type="checkbox" index="'+index+'"></input>');
	    	trList.push(CommonUtil.formatData(element.productCode, '--'));// 产品代码
	    	trList.push(CommonUtil.formatData(element.productName, '--'));// 产品名称
	    	trList.push(CommonUtil.formatData(element.bankName, '--'));// 银行名称
	    	trList.push(CommonUtil.formatData(element.bankAcctNo, '--'));// 银行账号
	    	trList.push(CommonUtil.formatData(element.balanceVol, '--'));// 总份额
	    	trList.push(CommonUtil.formatAmount(element.availVol, '--'));// 可用份额
	    	trList.push(CommonUtil.formatAmount(element.unconfirmedVol, '--'));// 冻结份额
	    	trList.push(CommonUtil.formatAmount(element.lockVol, '--'));// 锁定份额
	    	trList.push(CommonUtil.formatData(element.memo, '--'));
	    	trList.push("<a class='btn btn-secondary radius viewdtl' href='#' txacctno = '"+element.txAcctNo+"' productcode = '"+element.productCode+"' discode='"+element.disCode+"'>查看</a>");
	    	
	    	var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
	    	appendHtml +=trHtml;
		});
		
		$("#dtlList").html(appendHtml);
		
		//绑定客户份额选择事件
		$(".selectcustbal").click(function(){
			var selectIndex = $(this).attr("index");
			var sellFormTableId = "sellFormTable"+selectIndex;
			// 处理选中、取消
			if ($(this)[0].checked === true) {
			    // 判断选择的是否是同一只基金的份额
                Sell.sellVol = Sell.dtlList[selectIndex] || {};
                var fundCodeTd = $("#fundCodeId");
                if (!isEmpty(fundCodeTd.html()) && fundCodeTd.html() !== Sell.sellVol.productCode) {
                    $(this).removeAttr('checked');
                    layer.alert("请勾选相同基金的份额明细");
                    return;
                }
                // 添加赎回明细
				$("#sellConfirmFormFoot").before(Sell.generateSellFormTable(sellFormTableId, selectIndex));
				Sell.selectCustVol(selectIndex, sellFormTableId);

				// 赎回去向11 储蓄罐银行卡
				var redeemDirectionList = Sell.fundInfo.redeemDirectionList || [];
				if(redeemDirectionList.length !=2){
					CommonUtil.layer_tip("赎回去向配置不正确");
				}
				// 回款方式
				var selectHtml =Sell.buildSelectRedeemHtml(Sell.custInfo.collectProtocolMethod, redeemDirectionList.charAt(1), redeemDirectionList.charAt(0));
				$("#redeemCapitalFlag"+selectIndex).html(selectHtml);
			} else {
				$("#"+sellFormTableId).remove();
				if ($("tbody[id^='sellFormTable']").length === 0) {
					CommonUtil.cleanForm('sellConfirmForm');
					// 清空通过id直接赋值到td中的数据
					$("#sellConfirmForm").find("td[id]").each(function(index, element){
						$(element).html("");
					});
				}
			}
		});
		
		//绑定份额明细查看
		$(".viewdtl").off();
		$(".viewdtl").on('click', function(){
			var txAcctNo = $(this).attr("txacctno");
			var productCode = $(this).attr("productcode");
			var disCode = $(this).attr("discode");
			
			//查询份额明细
			Sell.viewDtl(txAcctNo, productCode, disCode);
		});
		
	},

	/**
	 * 回可用备注选择
	 * @param redeemDirection
	 */
	refundDirectionOnChange: function(dir, index) {
		$(".refundTr" + index).html("");
		if (dir == '5' || dir == '6' || dir == '7') {
			$("#sellFormTable" + index).append(Sell.generateRefundFormTable(dir, index));
			// 回可用备注
			$("#selectRedeemMemo" + index).html(CommonUtil.selectOptionsHtml(CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP));
			$("#selectRedeemMemo" + index).on('change', function () {
				Sell.refundMemoSelectOnChange(index);
			});
		} else {
			$(".refundTr" + index).html("");
		}
	},

	/**
	 * 回可用备注
	 * @param redeemDirection
	 */
	refundMemoSelectOnChange: function(index) {
		var memoSelect = $("#selectRedeemMemo" + index).val();
		if ("1" == memoSelect) {
			$("#refundMemo" + index).html('<input class="" name="refundFinaAvailMemo" isnull="false" datatype="s">');
		}
		if ("0" == memoSelect) {
			var selectRefundMemoId = "selectRefundMemo"+index;
			$("#refundMemo" + index).html('<select id="'+selectRefundMemoId+'" name="refundFinaAvailMemo" class="select" isnull="false" datatype="s">');
			// 初始化基金代码多选
			CommonUtil.singleSelectForRefund(selectRefundMemoId,TmsCounterConfig.HIGH_QUERY_PRODUCT_CODEANDNAME_URL, Sell.sellVol.productChannel);
		}
	},



	sellAll:function(sellTableId) {
		var tbody = $("#"+sellTableId);
		var appVolInput = tbody.find("input[name='appVol']");
		var availText = tbody.find(".availVol").text();
		appVolInput.val(availText);
		//触发change事件
		appVolInput.change();
	},
	appVolChange:function(sellTableId) {
		let appVolInput = $("#"+sellTableId).find("input[name='appVol']");
		let unFormatVol = CommonUtil.unFormatAmount(appVolInput.val());
		// 检查是否数字
		if (!CommonUtil.validFloat(unFormatVol)) {
			layer.alert("只能输入数字");
			return;
		}

		// 格式化份额
		appVolInput.val(CommonUtil.formatAmount(unFormatVol));

		// 计算总份额
		let totalAppVol = 0;
		$("tbody[id^='sellFormTable'] input[name='appVol']").each(function () {
			let appVol = $(this).val();
			if (isEmpty(appVol)) {
				return;
			}
			// 求和
			totalAppVol += parseFloat(CommonUtil.unFormatAmount(appVol));
		});
		$("#volSum").html(CommonUtil.formatAmount(totalAppVol));

		// 更新大写总份额
		$("#convertVolSum").html(CommonUtil.digit_vol_uppercase(totalAppVol));
	},


	generateRefundFormTable:function(dir, index) {
		var table = '<tr class="text-c refundTr'+index+'">' +
			'                <td>回可用余额备注选择</td>' +
			'                <td>' +
			'					<span class="select-box inline">' +
			'						<select id="selectRedeemMemo'+index+'" class="select" isnull="false" datatype="s"></select>' +
			'					</span>'+
			'				 </td>' +
			'                <td>回可用余额备注</td>' +
			'                <td><span class="select-box inline" id="refundMemo'+index+'"></span></td>' +
			'              </tr>';
			if (dir == '6' || dir == '7') {
				table += '   <tr class="text-c refundTr'+index+'">' +
				'                <td>回可用余额金额</td>' +
				'                <td>' +
				'					<input type="text" placeholder="请输入" id="refundFinaAvailAmt'+index+'" class="refundFinaAvailAmt" name="refundFinaAvailAmt" isnull="false" datatype="s" errormsg="回可用金额">'+
				'				 </td>' +
				'                <td></td>' +
				'                <td></td>' +
				'              </tr>';
			}
		return table;
	},

	/** 生成赎回编辑表单的table */
	generateSellFormTable:function(sellFormTableId, selectIndex) {
		var table = '<tbody id="'+sellFormTableId+'">' +
			'              <tr class="text-c">' +
			'                <td>银行卡账号</td>' +
			'                <td class="readText bankAcctInfoId"></td>' +
			'                <td>当前可用份额</td>' +
			'                <td class="readText availVol"></td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'                <td>当前总份额</td>' +
			'                <td class="readText balanceVol"></td>' +
			'                <td>申请份额（份）</td>' +
			'                <td>' +
			'                  <input type="hidden" name="cpAcctNo" >' +
			'                  <input type="text" placeholder="请输入" name="appVol" isnull="false" datatype="s" errormsg="申请份额" onchange="Sell.appVolChange(\''+sellFormTableId+'\')">' +
			'                  <a href="javascript:void(0)" class="btn radius btn-secondary sellAll" onclick="Sell.sellAll(\''+sellFormTableId+'\')">全赎</a>' +
			'                </td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'                <td>冻结份额</td>' +
			'                <td class="readText unconfirmedVol"></td>' +
			'                <td>费用</td>' +
			'                <td class="fee"></td>' +
			'              </tr>' +
			'			   <tr class="text-c">' +
			'				<td>交易回款方式</td>' +
			'               <td>' +
			'               	<span class="select-box inline">' +
			'                   	<select id="redeemCapitalFlag'+selectIndex+'" name="redeemCapitalFlag" class="select redeemCapitalFlag" isnull="false" datatype="s" errormsg="交易回款方式" onchange="Sell.refundDirectionOnChange($(this).val(), \''+selectIndex+'\')">' +
			'                       </select>' +
			'                   </span>' +
			'                </td>' +
			'                <td></td>' +
			'                <td></td>' +
			'              </tr>' +
			'        </tbody>';
		return table;
	},

	/**
	 * 选择用户份额
	 */
	selectCustVol:function(selectIndex, sellFormTableId){
        var formTable = $("#"+sellFormTableId);
		// 设置选中的可赎回信息
		Sell.buildSellForm(Sell.sellVol, formTable);

        var fundCodeTd = $("#fundCodeId");
		// 仅在第一次选择时赋值基金信息
		if (isEmpty(fundCodeTd.text())) {
			//产品代码
            fundCodeTd.html(Sell.sellVol.productCode);
			//基金信息
			Sell.queryFundInfo(Sell.sellVol.productCode);
			// 查询赎回预约开放日历
			var appDt = $("#appDt").val();
			var appTm = $("#appTm").val();
			Sell.queryHighproductAppointinfo(Sell.sellVol.productCode, appDt, appTm, "1");
			// 获取当前系统工作日
			if(CommonUtil.isEmpty(appDt)){
				CommonUtil.getWorkDay();
			}
		}
	},
	/**
	 * 查看份额明细 
	 */
	viewDtl:function(txAcctNo, productCode, disCode){
		
		var  uri= TmsCounterConfig.HIGH_VIEW_BAL_DTL ||  "";
		
		var appDt = $("#appDt").val() || '';
		var appTm = $("#appTm").val() || '';
		var reqparamters = {"custNo":txAcctNo, "productCode":productCode, "disCode":disCode};
		reqparamters.appDt = appDt;
		reqparamters.appTm = appTm;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Sell.viewCustDtlCallBack);
		 
	},
	/***
	 * 份额明细查询处理
	 * @param data
	 */
	viewCustDtlCallBack:function(data){
		
		var bodyData = data.body || {};
		var dtlList = bodyData.dtlList || [];
		
		var contentHtml = Sell.createCustDtlContent(dtlList);
		
		layer.open({
	        type: 1,
	        area: ['600px', '360px'],
	        shade: false,
	        title: false,
	        skin: 'layui-layer-rim',
	        content: contentHtml
	    });
		
	},
	/**
	 * 查看份额明细contentHtml
	 * @param rsList
	 * @returns {String}
	 */
	createCustDtlContent:function(rsList){
		var contentHtml = '<div class="result2_tab">'+
        '<table class="table table-border table-bordered table-hover table-bg table-sort">'+
           '<thead>'+
               '<tr class="text-c">'+
                    '<th>基金代码</th>'+
                    '<th>基金名称</th>'+
                    '<th>总份额</th>'+
                    '<th>可用份额</th>'+
                    '<th>冻结份额</th>'+
                    '<th>开放赎回日期</th>'+
                '</tr>'+
           '</thead>';
           '<tbody class="text-c" id="dtlList">';
           
           var appendHtml ='';
           $(rsList).each(function(index, element){
        	   var trList = [];
   	    	   trList.push(CommonUtil.formatData(element.productCode, '--'));
   	    	   trList.push(CommonUtil.formatData(element.productName, '--'));
   	    	   trList.push(CommonUtil.formatAmount(element.balanceVol, '--'));
   	    	   trList.push(CommonUtil.formatAmount(element.availVol, '--'));
   	    	   trList.push(CommonUtil.formatAmount(element.unconfirmedVol, '--'));
   	    	   trList.push(CommonUtil.formatData(element.openRedeDt, '--'));
   	    	   var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
   	    	   appendHtml += trHtml;
           });
           
           contentHtml += (appendHtml + '</tbody></table></div>');
           
           return contentHtml;
	},
	
	/**
	 * 构建赎回份额明细
	 */
	buildSellForm:function(sellVol, formTable){
		formTable.find(".bankAcctInfoId").html(sellVol.bankAcctNo);//银行卡号
		formTable.find("[name='cpAcctNo']").val(sellVol.cpAcctNo);//资金账号，审核时用于选中持仓
		formTable.find(".balanceVol").html(CommonUtil.formatAmount(sellVol.balanceVol));//总份额
		formTable.find(".availVol").html(CommonUtil.formatAmount(sellVol.availVol));//可赎份额
		formTable.find(".unconfirmedVol").html(CommonUtil.formatAmount(sellVol.unconfirmedVol));//待确认份额
	},
	
	
	/**
	 * 查询产品预约开放日历信息
	 */
	queryHighproductAppointinfo:function(fundCode, appDt, appTm, busyType){
		
		//查询预约开放日历
		QueryHighProduct.queryHighproductAppointinfo(fundCode, appDt, appTm, busyType);
		
		//构建预约开放日历信息
		Sell.buildHighproductAppointinfo(QueryHighProduct.highproductAppointinfo);
		
		
	},
	
	/**
	 * 构建产品预约开放预约日历信息
	 */
	buildHighproductAppointinfo:function(highproductAppointinfo){
		
		Sell.highproductAppointinfo = highproductAppointinfo || {};
		$("#appointStartDtId").html(highproductAppointinfo.appointStartDt);
		$("#apponitEndDtId").html(highproductAppointinfo.apponitEndDt);
		$("#openStartDtId").html(highproductAppointinfo.openStartDt);
		$("#openEndDtId").html(highproductAppointinfo.openEndDt);
	},
	
	
	/**
	 * 查询基金信息
	 * @param fundCode 基金名称
	 */
	queryFundInfo:function(fundCode){
		var  uri= TmsCounterConfig.QUERY_HIGH_PRODUCT_INFO_URL ||  {};
		
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入基金代码");
			return false;
		}
		
		var reqparamters = {"productCode":fundCode, 'busyType':'1'};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Sell.queryFundInfoCallBack);
	},
	/**
	 * 处理基金信息
	 */
	queryFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		Sell.fundInfo = fundInfo;

		if(CommonUtil.isEmpty(fundInfo.fundCode)){
			CommonUtil.layer_tip("没有查询到此产品");
			return false;
		}

		$("#fundName").html(fundInfo.fundAttr || '');
		$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundInfo.fundStat));
		$("#taCodeId").html(fundInfo.taCode);
		$("#productType").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, fundInfo.fundType));
		$("#shareClassId").html(CommonUtil.getMapValue(CONSTANTS.FUND_SHARECLASS_MAP, fundInfo.shareClass));
		$("#productChannelId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, fundInfo.productChannel));
		$("#feeCalModeId").html(CommonUtil.getMapValue(CONSTANTS.FEE_CAL_MODE_MAP, fundInfo.feeCalMode));
		$("#appointStartDtId").html(fundInfo.appointStartDt);
		$("#apponitEndDtId").html(fundInfo.apponitEndDt);
		$("#openStartDtId").html(fundInfo.openStartDt);
		$("#openEndDtId").html(fundInfo.openEndDt);

		// 查询工作日
		QueryHighProduct.queryHighproductWorkDay(fundInfo.fundCode);
	
	},

	/**
	 * 预约单确认
	 * @returns {Boolean}
	 */
	confirmNoAppointment : function(){
		var productCode =$("#fundCodeId").text();
		if(!Sell.validSelectAppoint(productCode)){
			CommonUtil.layer_alert("请选择客户预约信息");
			return false;
		}else{
			if($("#isContainAppointmentFlag").val() == "false"){
				layer.open({
		            title: ['无预约信息', true],
		            type: 1,
		            area: ['320px', 'auto'],
		            btn: ['确定', '取消'],
		            skin: 'layui-layer-rim', //加上边框
		            btnAlign: 'l',
		            content: "高端交易没有预约信息,确认继续吗",
		            yes: function (index, layero) { //或者使用btn1
		            	layer.close(index);
		            	Sell.confirm();
		            },
		            cancel: function (index) { //或者使用btn2
		                //按钮【取消】的回调
		            	CommonUtil.enabledBtn("confimSellBtn");
		            }
		        });
			} else {
				Sell.confirm();
			}
		}
		
	},
	/**
	 * 是否未选择赎回份额明细
	 * @returns
	 */
	isNotSelectSellVol:function(){
		return $("tbody[id^='sellFormTable']").length === 0;
	},
	/***
	 * 确认赎回
	 */	
	confirm : function(){
		CommonUtil.disabledBtn("confimSellBtn");
		var SellConfirmForm = $("#sellConfirmForm").serializeObject();
		var othetInfoForm = $("#othetInfoForm").serializeObject();
		//代理人OR经办人信息
		var transactorInfoForm = {};
		if('0' == Sell.custInfo.invstType || '2' == Sell.custInfo.invstType){
			transactorInfoForm = $("#transactorInfoForm").serializeObject();
		}else{
			transactorInfoForm = $("#agenInfoForm").serializeObject();
		}
		
		// 是否选中赎回份额校验
		if(Sell.isNotSelectSellVol()){
			CommonUtil.enabledBtn("confimSellBtn");
			CommonUtil.layer_tip("请选择一条赎回份额明细", null, null);
			return false;
		}
		
		var validRst = Valid.valiadateFrom($("#sellConfirmForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}
		//反格式化金额，并校验
		let appList = [];
		let appVolSum = 0;
		let volCheckRes = true;
		$("tbody[id^='sellFormTable']").each(function () {
			let appVol = CommonUtil.unFormatAmount($(this).find("input[name='appVol']").val());
			let availVol = CommonUtil.unFormatAmount($(this).find(".availVol").text());
			appVol = parseFloat(appVol);
			availVol = parseFloat(availVol);
			if(appVol > availVol){
				CommonUtil.layer_tip("申请份额不能大于可用份额");
				CommonUtil.enabledBtn("confimSellBtn");
				volCheckRes = false;
				return false;
			}
			let appDetail = {};
			appDetail.cpAcctNo = $(this).find("[name='cpAcctNo']").val();
			appDetail.appVol = appVol;
			appDetail.redeemCapitalFlag = $(this).find("[name='redeemCapitalFlag']").val();
			appDetail.refundFinaAvailAmt = $(this).find("input[name='refundFinaAvailAmt']").val();
			appDetail.refundFinaAvailMemo = $(this).find("input[name='refundFinaAvailMemo']").val();
			if (typeof(appDetail.refundFinaAvailMemo) == "undefined") {
				appDetail.refundFinaAvailMemo = $(this).find("[name='refundFinaAvailMemo']").val();
			}

            appList.push(appDetail);
			appVolSum += appVol;
		});
		if (!volCheckRes) {
			return ;
		}
		SellConfirmForm.appList = appList;
		SellConfirmForm.appVol = appVolSum;

		SellConfirmForm.appDtm =SellConfirmForm.appDt + '' + SellConfirmForm.appTm;
		if(!Valid.valiadTradeTime(SellConfirmForm.appTm)){
			CommonUtil.layer_tip("请重新输入下单时间，时间范围为9:00:00-14:59:59");
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}
		
		if(CommonUtil.formatDateToStr(Sell.currDate, 'yyyyMMdd') > SellConfirmForm.appDt){
			CommonUtil.layer_tip("申请日期不能在当前日期之前");
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}
		
		if(othetInfoForm.agentFlag == '1'){
			var validTransactorRst = Valid.valiadateFrom($("#transactorInfoForm"));
			if(!validTransactorRst.status){
				CommonUtil.layer_tip(validTransactorRst.msg);
				CommonUtil.enabledBtn("confimSellBtn");
				return false;
			}
			
			if('0' == transactorInfoForm.transactorIdType && !Valid.id_rule(transactorInfoForm.transactorIdNo)){
				CommonUtil.layer_tip("经办人证件号格式不正确");
				CommonUtil.enabledBtn("confimSellBtn");
				return false;
			}
		}
		
		// 重复单校验
		var repeatValidRst = HighValid.repeatValid(Sell.custInfo.custNo, Sell.fundInfo.fundCode, 
				Sell.custInfo.disCode, SellConfirmForm.appDt, SellConfirmForm.appTm, '1', 'confimSellBtn');
		if(!repeatValidRst.status){
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}
		if(repeatValidRst.status && "1" == repeatValidRst.repeateFlag){
			layer.open({
	            title: ['重复单校验', true],
	            type: 1,
	            area: ['320px', 'auto'],
	            btn: ['确定', '取消'],
	            skin: 'layui-layer-rim', //加上边框
	            btnAlign: 'l',
	            content: "该客户重复下单，请确认",
	            yes: function (index, layero) { //或者使用btn1
	            	CommonUtil.enabledBtn("confimSellBtn");
	            	Sell.sellSubmit(SellConfirmForm, othetInfoForm, transactorInfoForm);
	            	layer.close(index);
	            },
	            cancel: function (index) { //或者使用btn2
	                //按钮【取消】的回调
	            	CommonUtil.enabledBtn("confimSellBtn");
	            }
	        });
		}else{
			Sell.sellSubmit(SellConfirmForm, othetInfoForm, transactorInfoForm);
		}
		
	},
	
	/**
	 * 赎回提交
	 */
	sellSubmit:function(SellConfirmForm, othetInfoForm, transactorInfoForm){
		var  uri= TmsCounterConfig.SELL_CONFIRM_URL  ||  {};
		let balList = [];
		$("#dtlList .selectcustbal:checked").each(function () {
			let index = $(this).attr("index");
			balList.push(Sell.dtlList[index])
		});
		var reqparamters ={"appointmentForm":JSON.stringify(Sell.appointment),"sellConfirmForm": JSON.stringify(SellConfirmForm),
				"custInfoForm":JSON.stringify(Sell.custInfo),"fundDtlForm": JSON.stringify(balList),
				"fundName":Sell.fundInfo.fundAttr || '',"othetInfoForm":JSON.stringify(othetInfoForm),
				"productAppointmentInfoForm":JSON.stringify(Sell.highproductAppointinfo),
                "materialinfoForm":JSON.stringify(OnLineOrderFile.buildOrderCheckFile())};
		reqparamters.transactorInfoForm = JSON.stringify(transactorInfoForm);
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Sell.callBack);
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
            layer.confirm('赎回提交成功', {
                btn: ['确定'] //按钮
            }, function(){
                layer.closeAll();
                // 刷新页面
                if(OnLineOrderFile.isCrm()){
                    CommonUtil.closeCurrentUrl();
                }else{
                    // 刷新页面
                    CommonUtil.reloadUrl();
                }
            });
		}else{
			CommonUtil.layer_tip("提交失败,"+respDesc+"("+respCode+")");
		}
		CommonUtil.enabledBtn("confimSellBtn");
	},
	
	/**
	 * 高端查询客户持仓
	 */
	queryCustHodlInfo:function(){
		var  uri= TmsCounterConfig.QUERY_FUND_REDEEM_INFO_URL ||  {};
		var custNo = Sell.custInfo.custNo || '';
		var disCode = Sell.custInfo.disCode || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		var fundCode = $("#fundCode").val();
		var reqparamters = {"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Sell.queryCustHoldFundInfoCallBack);
	},
	/**
	 * 处理基金持仓信息
	 */
	queryCustHoldFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		var dtlResp = bodyData || {};
		Sell.fundHold = dtlResp || {};
		Sell.dtlList = dtlResp.balanceDtlList || [];

		if(Sell.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
		}
		
		
		if(Sell.dtlList.length >0){
			var selectDtl = Sell.dtlList[0] || {} ;
			$("#balanceVol").html(CommonUtil.formatAmount(selectDtl.balanceVol));
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
			$("#unconfirmedVol").html(CommonUtil.formatAmount(selectDtl.unconfirmedVol));
			
		}
	},
	
	/**
	 * 构造交易回款方式选择selectHtml
	 * @param collectProtocolMethod 用户回款方式 1-回款至银行卡（系统默认）2-回款至银行卡（用户选择）3-回款至储蓄罐（系统默认）4-回款至储蓄罐（用户选择）
     * @param redeemDirectionIsSupCardFlag 产品是否支持赎回到银行卡 0-不支持 1-支持
     * @param redeemDirectionIsSupCxgFlag 产品是否支持赎回到储蓄罐 0-不支持 1-支持
     *
	 */
	buildSelectRedeemHtml:function(collectProtocolMethod,redeemDirectionIsSupCardFlag,redeemDirectionIsSupCxgFlag){

		var defalutSelect = "0"; //默认回款到银行卡
		
		if("1" == redeemDirectionIsSupCardFlag && ("1" == collectProtocolMethod || "2" == collectProtocolMethod)){
			//用户默认回款方式是银行卡
			defalutSelect = "0";
		}
		
		if("1" == redeemDirectionIsSupCxgFlag && ("3" == collectProtocolMethod || "4" == collectProtocolMethod)){
			//用户默认回款方式是储蓄罐
			defalutSelect = "1";
		}
		
		var appendHtml = '';
		$.each(CONSTANTS.REDEEM_DIR_MAP,function(key,value){
			if(!isEmpty(key) && !isEmpty(value)){
				if(!isEmpty(defalutSelect) && defalutSelect == key){
					appendHtml +='<option class="redeemClass" value=\"'+key+'\"  selected>'+value+'</option>';
				}else{
					appendHtml +='<option class="redeemClass" value=\"'+key+'\" >'+value+'</option>';
				}
			}
		});

		
		return appendHtml;
	},
	
    /**
     *
     * @Description 绑定预约信息选择
     *
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 15:58
     **/
    selectAppointBind:function(){
        $(".selectAppointmentInfo").change(function(){
            Sell.selectAppointmentInfo($(this));
        });
    },
	
	selectAppointmentInfo:function (obj){
		var allChecked = $("input[type='checkbox'][name='appointmentInfoIndex']:checked");
		if(allChecked.length > 1){
			$(".selectAppointmentInfo").prop("checked", false);
			$(obj).prop("checked", true);
			return false;
		}
		// 初始化表单数据
		if(allChecked.length < 1){
			$("#isContainAppointmentFlag").val("false");
			// 初始化表单信息
			Sell.initDealForm();
			// 初始化投顾预约信息
			Sell.appointment = {};
			//查询份额明细
			Sell.custBalDtl(Sell.custInfo.custNo, Sell.custInfo.disCode, null);
			return false;
		}
		
		// 初始化订单表单
		Sell.initDealForm();
		// 初始化投顾预约信息
		Sell.appointment = {};
		
		var obj = allChecked[0];
		if(obj.checked){
			$("#isContainAppointmentFlag").val("true");
			var tr = $(obj).closest("tr");
			var dataLine = tr.children();
			/**预约ID*/
			Sell.appointment.appointId = $.trim(dataLine.eq(1).text());
			/**预约类型*/
			var preType = $.trim(dataLine.eq(15).text());
			Sell.appointment.preType = preType;
			/**产品代码*/
			var productCode = $.trim(dataLine.eq(2).text());
            Sell.appointment.productCode = productCode;
			// 预约份额
			var appVol = $.trim(dataLine.eq(8).text());
			Sell.appointment.appVol = appVol;
			
			//查询份额明细
			Sell.custBalDtl(Sell.custInfo.custNo, Sell.custInfo.disCode, productCode);

			// 初始化材料
			OnLineOrderFile.initMaterial(Sell.urlParams, Sell.getSelectCustMaterial(), OnLineOrderFile.CRM_OP_CHECK_NODE_PRE);
        }   
    },

    /**
     *
     * @Description  初始化材料
     *
     * @param null
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 17:30
     **/
    getSelectCustMaterial:function(){
        var custSelectOrder = {};
        custSelectOrder["hboneno"] = Sell.custInfo.hboneNo;// 一账通帐号
        custSelectOrder["pcode"] =  Sell.appointment.productCode;// 产品代码
        custSelectOrder["preid"] =  Sell.appointment.appointId;// 预约ID
        custSelectOrder["busiid"] =  OnLineOrderFile.CRM_SELL;// 业务类型ID // CRM业务类型 购买
        return custSelectOrder;
    },

    /**
	 * 校验预约信息是否选中
	 */
	validSelectAppoint:function(productCode){
		if(Sell.validBuyProductIsPre(Sell.appointmentInfo,productCode)){
			
			if(Sell.appointmentInfo.length > 0){
				var checkedList = $("input[type='checkbox'][name='appointmentInfoIndex']:checked") || [];
				if(checkedList.length <=0 ){
					return false;
				}
			}
			
		}
		return true;
	},
	/**
	 * 校验产品是否是预约产品
	 * @param productCode
	 */
	validBuyProductIsPre:function(appointmentInfoList,productCode){
		var isPreProduct = false;
		$(appointmentInfoList).each(function(index,element){
			
			var preProductCode =element.productCode;
			if(preProductCode == productCode){
				isPreProduct = true;
				return ;
			}
		});
		
		return isPreProduct;
	}
	
};

