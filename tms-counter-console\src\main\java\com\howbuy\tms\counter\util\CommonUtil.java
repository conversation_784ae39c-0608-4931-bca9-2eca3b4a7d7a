/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.util;

import java.util.Date;


import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.dto.OperInfoBaseDto;

/**
 * @description:(公共工具类) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年4月12日 下午3:41:55
 * @since JDK 1.6
 */
public class CommonUtil {
    public static void setCommonOperInfo(OperatorInfoCmd operatorInfoCmd,Object object){
        
        if(object instanceof OperInfoBaseDto){
            OperInfoBaseDto operInfo = (OperInfoBaseDto) object;
            Date currDate = new Date();
            if(operatorInfoCmd != null){
                if( operInfo.getCreator() == null){
                    operInfo.setCreator(operatorInfoCmd.getOperatorNo());
                }
                if(operInfo.getModifier() == null){
                    operInfo.setModifier(operatorInfoCmd.getOperatorNo());
                }
            }
            if(operInfo.getCreateDtm() == null){
                operInfo.setCreateDtm(currDate);
            }
            if(operInfo.getUpdateDtm() == null){
                operInfo.setUpdateDtm(currDate);
            }
        }
    }
}

