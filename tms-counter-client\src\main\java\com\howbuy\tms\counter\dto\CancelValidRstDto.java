/**
 *Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(撤单校验dto) 
 * @reason:
 * <AUTHOR>
 * @date 2018年4月11日 下午9:37:58
 * @since JDK 1.6
 */
public class CancelValidRstDto implements Serializable{
    
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;
    /**
     * 超过预约结束日标识
     */
    private boolean overAppointFlag;

    public boolean isOverAppointFlag() {
        return overAppointFlag;
    }

    public void setOverAppointFlag(boolean overAppointFlag) {
        this.overAppointFlag = overAppointFlag;
    }
    
    
}

