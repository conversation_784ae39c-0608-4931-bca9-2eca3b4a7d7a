var Appoint = {

    /**
     * 查询预约信息 "tradeType":"1,2"
     */
    queryAppointmentInfo:function(custNo, tradeType, disCode){
        var  uri= TmsCounterConfig.QUERY_APPOINTMENT_INFO_URL ||  {};
        var reqparamters = {"custNo":custNo, "tradeType":tradeType, "disCode":disCode};
        reqparamters.page = 1;
        reqparamters.pageSize = 20;
        var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
        CommonUtil.ajaxPaging(uri, paramters,  Appoint.queryAppointmentInfoBack, "pageView");
    },
    /**
     * 预约信息
     */
    queryAppointmentInfoBack:function(data){
       var appointmentInfo = data.appointmentInfo || [];

        $("#rsList").html('');
        if(appointmentInfo.length <=0){
            var trHtml = '<tr><td colspan="15">没有查询到预约信息</td></tr>';
            $("#rsList").append(trHtml);
            return false;
        }
        $(appointmentInfo).each(function(index,element){
            var trList = [];
            /**预约单号1*/
            trList.push(CommonUtil.formatData(element.appointId));
            /**预约产品代码2*/
            trList.push(CommonUtil.formatData(element.productCode));
            /**预约产品名称3*/
            trList.push(CommonUtil.formatData(element.productName));
            /**预约业务4*/
            trList.push(CommonUtil.getMapValue(CONSTANTS.PRE_TRADE_TYPE_NAME_MAP,element.buyStatus,'--'));
            /**预约日期5*/
            trList.push(formatDate(CommonUtil.formatData(element.appointStartDt,'')));
            /**预约时间6*/
            trList.push(formatTime(CommonUtil.formatData(element.appointStartTm,'')));
            /**预约金额7*/
            trList.push(CommonUtil.formatData(element.appAmt,''));
            /**预约份额8*/
            trList.push(CommonUtil.formatData(element.appVol,''));
            /**预约折扣9*/
            trList.push(CommonUtil.formatData(element.discountRate,''));
            /**预约单状态10*/
            trList.push(CommonUtil.getMapValue(CONSTANTS.PRE_BOOK_STATE_MAP,element.orderStatus));

            trList.push(CommonUtil.getMapValue(CONSTANTS.DOUBLE_NEED_FLAG_MAP, element.doubleNeedFlag)); // 需双录标识：0-无需双录；1-需双录
            trList.push(CommonUtil.getMapValue(CONSTANTS.DOUBLE_HANDLE_FLAG_MAP, element.doubleHandleFlag)); // 处理标识：0-无需处理；1-未处理；2-已处理
            trList.push(CommonUtil.formatDateToStr(element.doubleHandleDt, 'yyyy-MM-dd hh:mm:ss'));// 双录处理时间，带时间yyyyMMddHH24miss
            trList.push(CommonUtil.getMapValue(CONSTANTS.FIRST_PREID_MAP, element.firstPreId)); // 是否首次实缴预约 0-否 1-是
            /**认缴金额*/
            trList.push(CommonUtil.formatData(element.subsAmt,''));

            var htdHtml = '<td style="display:none" >"'+element.fundRiskLevel+'"</td><td style="display:none">"'+element.fundAttr+'"</td><td style="display:none">"'+element.fundStatus+'"</td><td style="display:none">"'+element.preType+'"</td><td style="display:none">"'+element.fundType+'"</td>';
            var trHtml = '<tr class="text-c"><td><input class="selectAppointmentInfo" type="checkbox" name="appointmentInfoIndex" appointid= "'+element.appointId+'" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td>'+htdHtml+'</tr>';
            $("#rsList").append(trHtml);
        });
    },
    /**
     * 
     * @Description 根据预约ID选择预约信息
     * 
     * @param preId 预约id
     * @return 
     * <AUTHOR>
     * @Date 2019/5/31 16:11
     **/
    selectAppointByPreId:function (preId) {
        $("#rsList").find("tr").each(function(index, element){
           var appointId =  $(element).find(".selectAppointmentInfo").attr('appointid')
            if(appointId != preId){
                $(element).remove();
            }
        });

        // 选中预约信息
        $(".selectAppointmentInfo").each(function (index, element){
            var appointid = $(element).attr("appointid");
            if(appointid == preId){
                $(element).click();
                return;
            }
        });
    }
}