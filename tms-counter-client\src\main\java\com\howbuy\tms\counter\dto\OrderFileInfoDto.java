/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.dto;

import java.util.List;

/**
 * @className OrderFileInfoDto
 * @description
 * <AUTHOR>
 * @date 2019/5/24 15:51
 */
public class OrderFileInfoDto {
    private String orderfileid;
    private String fid;
    private String filetypename;
    private String isrequire;
    private String istip;

    /**
     * 是否可修改 true-可修改;false-不可修改
     */
    private String isallowopt;

    /**
     * 最近一次驳回意见
     */
    private String lastreturndes;

    private List<OrderFile> orderFileList;

    public String getOrderfileid() {
        return orderfileid;
    }

    public void setOrderfileid(String orderfileid) {
        this.orderfileid = orderfileid;
    }

    public String getFid() {
        return fid;
    }

    public void setFid(String fid) {
        this.fid = fid;
    }

    public String getFiletypename() {
        return filetypename;
    }

    public void setFiletypename(String filetypename) {
        this.filetypename = filetypename;
    }

    public String getIsrequire() {
        return isrequire;
    }

    public void setIsrequire(String isrequire) {
        this.isrequire = isrequire;
    }

    public String getIstip() {
        return istip;
    }

    public void setIstip(String istip) {
        this.istip = istip;
    }

    public List<OrderFile> getOrderFileList() {
        return orderFileList;
    }

    public void setOrderFileList(List<OrderFile> orderFileList) {
        this.orderFileList = orderFileList;
    }

    public String getLastreturndes() {
        return lastreturndes;
    }

    public void setLastreturndes(String lastreturndes) {
        this.lastreturndes = lastreturndes;
    }

    public String getIsallowopt() {
        return isallowopt;
    }

    public void setIsallowopt(String isallowopt) {
        this.isallowopt = isallowopt;
    }

    public static class OrderFile{
        private String id;
        private String filename;
        private String filesize;
        private String filesuffix;
        private String filepath;
        private String filepathurl;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }

        public String getFilesize() {
            return filesize;
        }

        public void setFilesize(String filesize) {
            this.filesize = filesize;
        }

        public String getFilesuffix() {
            return filesuffix;
        }

        public void setFilesuffix(String filesuffix) {
            this.filesuffix = filesuffix;
        }

        public String getFilepath() {
            return filepath;
        }

        public void setFilepath(String filepath) {
            this.filepath = filepath;
        }

        public String getFilepathurl() {
            return filepathurl;
        }

        public void setFilepathurl(String filepathurl) {
            this.filepathurl = filepathurl;
        }
    }
}
