package com.howbuy.tms.counter.util;


import com.howbuy.dfile.HFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class WebDevFileUtils {

    private static final Logger logger = LoggerFactory.getLogger(WebDevFileUtils.class);

    private static HFileService hFileService = HFileService.getInstance();

    /**
     * 
     * read2Bytes:文件流读取
     * @param businessCode
     * @param path
     * @param fileName
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:52:35
     */
    public static byte[] read2Bytes(String businessCode, String path, String fileName) {
        byte[] frontBytes = null;
        // 读取身份证获取流对象
        try {
            frontBytes = HFileService.getInstance().read2Bytes(businessCode, path, fileName);
        } catch (IOException e) {
            logger.error("FileUtils>>>read2Bytes error {}", log(businessCode, path, fileName), e);
            logger.error("FileUtils>>>read2Bytes error:", e);
        }
        return frontBytes;
    }

    
    /**
     * 
     * write:写入文件 byte
     * @param businessCode
     * @param path
     * @param fileName
     * @param context
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:02
     */
    public static boolean write(String businessCode, String path, String fileName, byte[] context) {
        try {
            hFileService.write(businessCode, path, fileName, context);
            logger.info("FileUtils>>>write success {}  ", log(businessCode, path, fileName));
            return true;
        } catch (Exception e) {
            logger.error("FileUtils>>>write error {}", log(businessCode, path, fileName), e);
            logger.error("FileUtils>>>write error:", e);
        }
        return false;
    }


    /**
     * 
     * write:写入文件 String
     * @param businessCode
     * @param path
     * @param fileName
     * @param context
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:18
     */
    public static boolean write(String businessCode, String path, String fileName, String context) {
        try {
            hFileService.write(businessCode, path, fileName, context);
            logger.info("FileUtils>>>write success {}  ", log(businessCode, path, fileName));
            return true;
        } catch (Exception e) {
            logger.error("FileUtils>>>write error {}", log(businessCode, path, fileName), e);
            logger.error("FileUtils>>>write error:", e);
        }
        return false;
    }
    
    /**
     * 
     * log:路径日志
     * @param businessCode
     * @param path
     * @param fileName
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:41
     */
    private static String log(String businessCode, String path, String fileName) {
        String absolutePath = getAbsolutePath(businessCode, path, fileName);
        return String.format(" businessCode=%s, path=%s, fileName=%s, absolutePath=%s", businessCode, path, fileName, absolutePath);
    }
    
    /**
     * 
     * getAbsolutePath:获取绝对路径
     * @param businessCode
     * @param path
     * @param fileName
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:53
     */
    public static String getAbsolutePath(String businessCode, String path, String fileName) {
        try {
            return hFileService.getAbsolutePath(businessCode, path, fileName);
        } catch (Exception e) {
            logger.error("FileUtils>>>getAbsolutePath>>businessCode={} path={} fileName={} error", businessCode, path, fileName, e);
            logger.error("FileUtils>>>getAbsolutePath>>>write error:", e);
        }
        return "";
    }



    
    
}
