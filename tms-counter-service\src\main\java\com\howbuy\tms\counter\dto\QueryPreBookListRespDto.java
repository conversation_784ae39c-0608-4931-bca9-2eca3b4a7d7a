/**
 *Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.math.BigDecimal;

import java.io.Serializable;
import java.util.List;

/**
 * @description:(查询预约列表respDto) 
 * @reason:
 * <AUTHOR>
 * @date 2018年1月18日 下午5:40:31
 * @since JDK 1.6
 */
public class QueryPreBookListRespDto implements Serializable{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 6240081323072100642L;
    
    private List<PreBookListDto> preBookList;
    
    /**
    *  总记录数
    */
    private long totalCount;
   /**
    * 总页数
    */
    private long totalPage;
    /**
    * 当前页
    */
    private long pageNo;
    
    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public long getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(long totalPage) {
        this.totalPage = totalPage;
    }

    public long getPageNo() {
        return pageNo;
    }

    public void setPageNo(long pageNo) {
        this.pageNo = pageNo;
    }

    public List<PreBookListDto> getPreBookList() {
        return preBookList;
    }

    public void setPreBookList(List<PreBookListDto> preBookList) {
        this.preBookList = preBookList;
    }



    public static class PreBookListDto implements Serializable{
        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */
        
        private static final long serialVersionUID = -5583625052802168772L;
        /**
         * 预约单标识，CRM唯一
         */
        private String preId;
        /**
         * 一帐通号
         */
        private String hboneNo;

        /**
         * 客户号
         */
        private String custNo;
        /**
         * 姓名
         */
        private String custName;
        /**
         * 产品代码
         */
        private String fundCode;
        /**
         * 产品名称
         */
        private String fundName;

        /**
         * 交易类型 1-购买 2-追加 3-赎回
         */
        private String tradeType;
        /**
         * 证件类型
         */
        private String idType;
        /**
         * 证件号
         */
        private String idNo;
        /**
         * 预约单状态:1-未确认；2-已确认；4-已撤销
         */
        private String prebookState;
        /**
         * 无纸化预约单状态 1-未确认； 2-已确认； 3-驳回
         */
        private String nopaperState;
        /**
         * 预约金额
         */
        private BigDecimal ackAmt;
        /**
         * 预约份额
         */
        private BigDecimal sellVol;
        /**
         * 手续费
         */
        private BigDecimal fee;
        
        /**
         * 费率
         */
        private BigDecimal feeRate;

        /**
         * 预约折扣
         */
        private BigDecimal discount;

        /**
         * 无折扣手续费
         */
        private BigDecimal discountfee;

        /**
         * 活动折扣截止日
         */
        private String activityDiscountEndDate;

        /**
         * 预约类型 1：纸质成单； 2：电子成单； 3：无纸化；
         */
        private String preType;

        /**
         * 开放开始日
         */
        private String openStartDt;
        /**
         * 开放截止日
         */
        private String openEndDt;

        /**
         * 截止打款时间
         */
        private String payEndDate;
        /**
         * 支持提前下单 0-不支持 1-支持
         */
        private String supportAdvanceFlag;

        /**
         * 客户投资者类型 0-机构 1-个人
         * 
         */
        private String investType;

        /**
         * 预约日期
         */
        private String creDt;

        /**
         * 中台业务码
         */
        private String mBusiCode;

        /**
         * 中台订单号
         */
        private String orderId;
        /**
         * 中台订单号
         */
        private String bankAcctNo;
        
        /**
         * 份额类型 A-前收费 B-后收费
         */
        private String shareClass;

        public String getPreId() {
            return preId;
        }

        public void setPreId(String preId) {
            this.preId = preId;
        }

        public String getHboneNo() {
            return hboneNo;
        }

        public void setHboneNo(String hboneNo) {
            this.hboneNo = hboneNo;
        }

        public String getCustNo() {
            return custNo;
        }

        public void setCustNo(String custNo) {
            this.custNo = custNo;
        }

        public String getCustName() {
            return custName;
        }

        public void setCustName(String custName) {
            this.custName = custName;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getFundName() {
            return fundName;
        }

        public void setFundName(String fundName) {
            this.fundName = fundName;
        }

        public String getTradeType() {
            return tradeType;
        }

        public void setTradeType(String tradeType) {
            this.tradeType = tradeType;
        }

        public String getIdType() {
            return idType;
        }

        public void setIdType(String idType) {
            this.idType = idType;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getPrebookState() {
            return prebookState;
        }

        public void setPrebookState(String prebookState) {
            this.prebookState = prebookState;
        }

        public String getNopaperState() {
            return nopaperState;
        }

        public void setNopaperState(String nopaperState) {
            this.nopaperState = nopaperState;
        }

        public BigDecimal getAckAmt() {
            return ackAmt;
        }

        public void setAckAmt(BigDecimal ackAmt) {
            this.ackAmt = ackAmt;
        }

        public BigDecimal getSellVol() {
            return sellVol;
        }

        public void setSellVol(BigDecimal sellVol) {
            this.sellVol = sellVol;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public BigDecimal getFeeRate() {
            return feeRate;
        }

        public void setFeeRate(BigDecimal feeRate) {
            this.feeRate = feeRate;
        }

        public BigDecimal getDiscount() {
            return discount;
        }

        public void setDiscount(BigDecimal discount) {
            this.discount = discount;
        }

        public BigDecimal getDiscountfee() {
            return discountfee;
        }

        public void setDiscountfee(BigDecimal discountfee) {
            this.discountfee = discountfee;
        }

        public String getActivityDiscountEndDate() {
            return activityDiscountEndDate;
        }

        public void setActivityDiscountEndDate(String activityDiscountEndDate) {
            this.activityDiscountEndDate = activityDiscountEndDate;
        }

        public String getPreType() {
            return preType;
        }

        public void setPreType(String preType) {
            this.preType = preType;
        }

        public String getOpenStartDt() {
            return openStartDt;
        }

        public void setOpenStartDt(String openStartDt) {
            this.openStartDt = openStartDt;
        }

        public String getOpenEndDt() {
            return openEndDt;
        }

        public void setOpenEndDt(String openEndDt) {
            this.openEndDt = openEndDt;
        }

        public String getPayEndDate() {
            return payEndDate;
        }

        public void setPayEndDate(String payEndDate) {
            this.payEndDate = payEndDate;
        }

        public String getSupportAdvanceFlag() {
            return supportAdvanceFlag;
        }

        public void setSupportAdvanceFlag(String supportAdvanceFlag) {
            this.supportAdvanceFlag = supportAdvanceFlag;
        }

        public String getInvestType() {
            return investType;
        }

        public void setInvestType(String investType) {
            this.investType = investType;
        }

        public String getCreDt() {
            return creDt;
        }

        public void setCreDt(String creDt) {
            this.creDt = creDt;
        }

        public String getmBusiCode() {
            return mBusiCode;
        }

        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public String getBankAcctNo() {
            return bankAcctNo;
        }

        public void setBankAcctNo(String bankAcctNo) {
            this.bankAcctNo = bankAcctNo;
        }

        public String getShareClass() {
            return shareClass;
        }

        public void setShareClass(String shareClass) {
            this.shareClass = shareClass;
        }
        
    }
}

