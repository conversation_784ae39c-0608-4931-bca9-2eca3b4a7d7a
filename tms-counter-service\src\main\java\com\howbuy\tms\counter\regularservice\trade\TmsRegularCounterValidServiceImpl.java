/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularservice.trade;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.ProductTypeEnum;
import com.howbuy.tms.common.enums.database.ZBusiCodeEnum;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.BooleanFlagReverseEnum;
import com.howbuy.tms.counter.common.util.UUIDUtil;
import com.howbuy.tms.counter.dto.RegularCounterCancelReqDto;
import com.howbuy.tms.counter.dto.RegularCounterPurchaseReqDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.validate.CancelOrderValidateFacade;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.validate.CancelOrderValidateRequest;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.validate.SubspurValidateFacade;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.validate.SubspurValidateRequest;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 
 * @description:(定期校验)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年6月22日 上午11:30:39
 * @since JDK 1.6
 */
@Service("tmsRegularCounterValidService")
public class TmsRegularCounterValidServiceImpl implements TmsRegularCounterValidService {
    private static Logger logger = LogManager.getLogger(TmsRegularCounterValidServiceImpl.class);

    @Autowired
    private SubspurValidateFacade subspurValidateFacade;

    @Autowired
    private CancelOrderValidateFacade cancelOrderValidateFacade;

    @Override
    public boolean subsOrPurValidate(RegularCounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception {
        SubspurValidateRequest request = new SubspurValidateRequest();
        if (dto != null) {
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            request.setCpAcctNo(dto.getCpAcctNo());
            request.setPaymentType(dto.getPaymentType());
            request.setAppAmt(dto.getAppAmt());
            request.setRiskFlag(dto.getRiskFlag());
            request.setProductCode(dto.getProductCode());
            //不用校验密码
            request.setTxPasswordValidateFlag(BooleanFlagReverseEnum.NO.getCode());
            request.setProductType(ProductTypeEnum.REGULAR.getCode());
            request.setzBusiCode(ZBusiCodeEnum.BUY.getCode());
            //request.setFundShareClass(dto.getFundShareClass());
            request.setProtocolType(dto.getProtocolType());
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            // 协议号
            request.setProtocolNo(null);
            //request.setFundShareClass(dto.getFundShareClass());
            //request.setDiscount(dto.getDiscountRate());
            logger.info("requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(subspurValidateFacade, request, disInfoDto);
        return true;

    }


    @Override
    public boolean cancelOrderValidate(RegularCounterCancelReqDto dto, DisInfoDto disInfoDto) throws Exception {
        CancelOrderValidateRequest request = new CancelOrderValidateRequest();
        if (dto != null) {
            // 订单号
            request.setDealNo(dto.getDealNo());
            // 是否需要校验校验密码(柜台撤单不需要校验校验密码)
            request.setNeedValidateTxPwdFlag(BooleanFlagReverseEnum.NO.getCode());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setCancelFlag(dto.getCancelType());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            logger.info("requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(cancelOrderValidateFacade, request, disInfoDto);
        return true;
    }

}
