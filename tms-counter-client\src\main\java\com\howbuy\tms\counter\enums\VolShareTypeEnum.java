package com.howbuy.tms.counter.enums;

/**
 * <AUTHOR>
 * @date 2024/9/24 10:40
 * @since JDK 1.8
 */
public enum VolShareTypeEnum {

    /**
     * 份额合并
     */
    VOL_MERGE("1", "份额合并"),
    /**
     * 份额迁移
     */
    VOL_TRANS("2", "份额迁移");

    private String code;
    private String name;

    private VolShareTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}