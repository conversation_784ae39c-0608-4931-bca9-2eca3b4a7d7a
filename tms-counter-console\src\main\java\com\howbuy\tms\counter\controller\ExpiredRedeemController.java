/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller;

import com.howbuy.common.bean.BeanUtils;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.LoggerUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.task.RedeemTask;
import com.howbuy.tms.counter.dto.CounterOrderDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderReqDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderRespDto;
import com.howbuy.tms.counter.dto.ExpiredRedeemDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.batch.facade.query.queryexpiredredeemparams.QueryExpiredRedeemParamsFacade;
import com.howbuy.tms.high.batch.facade.query.queryexpiredredeemparams.QueryExpiredRedeemParamsRequest;
import com.howbuy.tms.high.batch.facade.query.queryexpiredredeemparams.QueryExpiredRedeemParamsResponse;
import com.howbuy.tms.high.batch.facade.query.queryexpiredredeemparams.bean.ExpiredRedeemParamBean;
import com.howbuy.tms.high.orders.facade.trade.redeem.bean.RedeemDetailBean;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterRequest;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * @className ExpiredRedeemController
 * @description 到期赎回
 * <AUTHOR>
 * @date 2019/3/25 16:12
 */
@Controller
public class ExpiredRedeemController {
    private final static Logger logger = LoggerFactory.getLogger(ExpiredRedeemController.class);

    @Autowired
    private QueryExpiredRedeemParamsFacade queryExpiredRedeemParamsFacade;

    @Autowired
    private RedeemCounterFacade redeemCounterFacade;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    @Qualifier("tmscounter.preBookService")
    private PreBookService preBookService;

    private static final int MAX_POOL_NUM = 10;

    @RequestMapping("/tmscounter/expiredRedeem.htm")
    public void expiredRedeem(@RequestParam("repurchaseProtocolNos") String repurchaseProtocolNos,
                              HttpServletRequest request, HttpServletResponse response)throws Exception{
        List<String> repurchaseProtocolNoList = Arrays.asList(repurchaseProtocolNos.split(","));

        // 校验存在待审核订单
        validateExistUnCheckOrders();

        QueryExpiredRedeemParamsRequest queryExpiredRedeemParamsRequest = new QueryExpiredRedeemParamsRequest();
        queryExpiredRedeemParamsRequest.setRepurchaseProtocolNos(repurchaseProtocolNoList);
        QueryExpiredRedeemParamsResponse queryExpiredRedeemParamsResponse = queryExpiredRedeemParamsFacade.execute(queryExpiredRedeemParamsRequest);

        String workDay = getWorkDay();
        List<RedeemCounterRequest> redeemRequestList = new ArrayList<>();
        Map<String, ExpiredRedeemDto> redeemRstMap = new HashMap<>(16);
        if(CollectionUtils.isNotEmpty(queryExpiredRedeemParamsResponse.getExpiredRedeemParams())){
            for(ExpiredRedeemParamBean expiredRedeemParamBean : queryExpiredRedeemParamsResponse.getExpiredRedeemParams()){
                // 创建赎回Request，并赋值
                RedeemCounterRequest redeemCounterRequest = new RedeemCounterRequest();
                BeanUtils.copyProperties(expiredRedeemParamBean, redeemCounterRequest);
                List<RedeemDetailBean> redeemDetailList = new ArrayList<>(1);
                RedeemDetailBean bean = new RedeemDetailBean();
                bean.setCpAcctNo(expiredRedeemParamBean.getCpAcctNo());
                bean.setAppVol(expiredRedeemParamBean.getAppVol());
                redeemDetailList.add(bean);
                redeemCounterRequest.setRedeemDetailList(redeemDetailList);

                ExpiredRedeemDto expiredRedeemDto = new ExpiredRedeemDto();
                if(!expiredRedeemParamBean.getCanOrderFlag().equals(YesOrNoEnum.YES.getCode())){
                    expiredRedeemDto.setRepurchaseProtocolNo(redeemCounterRequest.getRepurchaseProtocolNo());
                    expiredRedeemDto.setTxAcctNo(redeemCounterRequest.getTxAcctNo());
                    expiredRedeemDto.setFundCode(redeemCounterRequest.getFundCode());
                    expiredRedeemDto.setCpAcctNo(expiredRedeemParamBean.getCpAcctNo());
                    expiredRedeemDto.setAppVol(expiredRedeemParamBean.getAppVol());
                    expiredRedeemDto.setMemo(expiredRedeemParamBean.getMemo());

                    redeemRstMap.put(expiredRedeemParamBean.getRepurchaseProtocolNo(), expiredRedeemDto);
                }else{
                    redeemRequestList.add(redeemCounterRequest);
                    redeemRstMap.put(buildKey(redeemCounterRequest), expiredRedeemDto);
                }

            }
        }

        // 多线程下单
        mutiOrder(redeemRequestList, redeemRstMap, workDay);

        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("code", TmsCounterResultEnum.SUCC.getCode());
        rst.put("desc", TmsCounterResultEnum.SUCC.getDesc());

        rst.put("expiredRedeemRstList", redeemRstMap.values());
        WebUtil.write(response, rst);
    }


    /**
     * 
     * @Description 多线程下单
     * 
     * @param redeemRequestList
     * @param redeemRstMap
     * @param workDay
     * @return java.util.Map<java.lang.String,com.howbuy.tms.counter.dto.ExpiredRedeemDto>
     * <AUTHOR>
     * @Date 2019/5/30 11:00
     **/
    private Map<String, ExpiredRedeemDto> mutiOrder(List<RedeemCounterRequest> redeemRequestList,
                                                    Map<String, ExpiredRedeemDto> redeemRstMap, String workDay) {
        if (CollectionUtils.isEmpty(redeemRequestList)) {
            return redeemRstMap;
        }

        int divs = redeemRequestList.size() / MAX_POOL_NUM;
        int mods = redeemRequestList.size() % MAX_POOL_NUM;

        logger.info("mutiOrder|divs:{}, modes:{}",divs, mods);
        CountDownLatch latch = null;
        try{
            if (divs > 0) {
                latch = new CountDownLatch(MAX_POOL_NUM);
                List<RedeemCounterRequest> subList = null;
                for (int i = 0; i < MAX_POOL_NUM; i++) {
                    if (i == (MAX_POOL_NUM - 1)) {
                        subList = redeemRequestList.subList(i * divs, redeemRequestList.size());
                    } else {
                        subList = redeemRequestList.subList(i * divs, (i + 1) * divs);
                    }
                    CommonThreadPool.submit(new RedeemTask(redeemCounterFacade, preBookService, tmsCounterOutService,
                            redeemRstMap, subList, workDay, latch, LoggerUtils.getUuid()));
                }
            } else if (mods > 0) {
                latch = new CountDownLatch(redeemRequestList.size());
                List<RedeemCounterRequest> subList = null;
                for(int i = 0; i<redeemRequestList.size(); i++){
                    subList = redeemRequestList.subList(i , (i + 1));
                    CommonThreadPool.submit(new RedeemTask(redeemCounterFacade, preBookService, tmsCounterOutService,
                            redeemRstMap, subList, workDay, latch, LoggerUtils.getUuid()));
                }
            }
        }finally {
            try{
                if(latch != null){
                    latch.await();
                }
            }catch (Exception e){
                logger.error("ExpiredRedeemController|mutiOrder:{}", e.getMessage(), e);
            }
        }

        return redeemRstMap;
    }

    /**
     *
     * getWorkDay:(获取高端系统当前工作日)
     * @return
     * <AUTHOR>
     * @throws Exception
     * @date 2018年3月17日 下午1:44:35
     */
    private String getWorkDay() throws Exception{
        return tmsCounterService.getHighSystemWorkDay();
    }

    private String buildKey(RedeemCounterRequest redeemCounterRequest){
        String cpAcctNo = redeemCounterRequest.getRedeemDetailList().get(0).getCpAcctNo();
        if (StringUtils.isEmpty(cpAcctNo)) {
            cpAcctNo = "default";
        }
        return redeemCounterRequest.getTxAcctNo() + "-" +
                cpAcctNo + "-" +
                redeemCounterRequest.getFundCode();
    }

    private void validateExistUnCheckOrders() throws Exception{
        long unCheckNums = getUnCheckCouterBuyDeals();
        if(unCheckNums> 0){
            throw new TmsCounterException(TmsCounterResultEnum.EXIT_UNCHECK_CANCEL_DEAL);
        }
    }


    /**
     *
     * getUnCheckCouterBuyDeals:(获取未审核的柜台购买订单数)
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月6日 下午8:54:47
     */
    private long getUnCheckCouterBuyDeals() throws Exception{
        List<CounterOrderDto> counterOrderList = getCounterOrderList(CheckFlagEnum.NOT_CHECK.getCode());
        if(org.springframework.util.CollectionUtils.isEmpty(counterOrderList)){
            return 0L;
        }
        Set<String> unCheckPalacesSet=  new HashSet<String>(16);
        for(CounterOrderDto counterOrderDto : counterOrderList){
            unCheckPalacesSet.add(counterOrderDto.getTxAcctNo());
        }

        return unCheckPalacesSet.size();

    }
    /**
     *
     * getCounterOrderList:(查询柜台订单)
     * @param checkFlag
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月18日 上午11:17:03
     */
    private List<CounterOrderDto> getCounterOrderList(String checkFlag) throws Exception{

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(TmsCounterConstant.HOWBUY_DISCODE);

        CounterQueryOrderReqDto counterQueryOrderReqDto = new CounterQueryOrderReqDto();
        counterQueryOrderReqDto.setCheckFlag(checkFlag);
        counterQueryOrderReqDto.setTxCode(TxCodes.HIGH_COUNTER_MODIFY_REPURCHASEPROCTOL);
        CounterQueryOrderRespDto counterQueryOrder = tmsCounterService.counterQueryOrder(counterQueryOrderReqDto, disInfoDto);
        if(counterQueryOrder == null){
            return null;
        }

        return counterQueryOrder.getCounterOrderList();
    }

}
