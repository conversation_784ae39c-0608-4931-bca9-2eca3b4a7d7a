/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoResponse;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.bean.CustBaseInfoBean;
import com.howbuy.tms.common.enums.busi.CustStatEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.QueryCustBaseInfoReqDto;
import com.howbuy.tms.counter.fundservice.out.TmsFundCounterOutService;
import com.howbuy.tms.counter.fundservice.trade.TmsFundCounterService;
import com.howbuy.tms.counter.regularservice.trade.TmsRegularCounterService;
import com.howbuy.tms.counter.service.orderplan.OrderPlanService;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.HashSet;
import java.util.Set;

/**
 * @description:(抽象控制器)
 * <AUTHOR>
 * @date 2017年9月15日 上午9:41:05
 * @since JDK 1.6
 */
@Controller
public abstract class AbstractController {

    private static Logger LOG = LogManager.getLogger(AbstractController.class);
    
    @Autowired
    protected TmsCounterService tmsCounterService;

    @Autowired
    protected TmsCounterOutService tmsCounterOutService;

    @Autowired
    protected TmsFundCounterService tmsFundCounterService;

    @Autowired
    protected TmsRegularCounterService tmsRegularCounterService;

    @Autowired
    protected TmsFundCounterOutService tmsFundCounterOutService;
    
    @Autowired
    protected OrderPlanService orderPlanService;


    /**
     * 
     * getFundInfoNav: 获取公募基金信息
     * @param productCode
     * @param appDt
     * @param appDtm
     * @return
     * <AUTHOR>
     * @date 2018年1月25日 下午2:27:32
     */
    public FundInfoAndNavBean getFundInfoNav(String productCode, String appDt, String appDtm){
        FundInfoAndNavBean fundInfoAndNavBean = null;
        try {
            fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(productCode, appDt, appDtm);
        } catch (Exception e) {
            LOG.error("query fundInfoAndNavBean erro :", e);
            throw new TmsCounterException(TmsCounterResultEnum.FUND_NAV_ERR,e);
        }
        if (fundInfoAndNavBean == null) {
            throw new TmsCounterException(TmsCounterResultEnum.FUND_NAV_ERR);
        }
        return fundInfoAndNavBean;
    }
    
    /**
     * 
     * getCustNo: 根据客户证件号查询客户交易账号
     * @param idNo
     * @param operIp
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月10日 下午1:22:41
     */
    public String getCustNo(String idNo, String operIp) throws Exception{
        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        queryCustBaseInfoReqDto.setOperIp(operIp);
        queryCustBaseInfoReqDto.setIdNo(idNo);
        QueryCustBaseInfoResponse queryRsp = tmsCounterService.queryCustBaseInfoSub(queryCustBaseInfoReqDto, null);
        if(queryRsp == null ||  CollectionUtils.isEmpty(queryRsp.getCustBaseInfoBeanList())){
            return null;
        }
        Set<String> txAcctNoSet = new HashSet<String>();
        for(CustBaseInfoBean custBaseInfoBean : queryRsp.getCustBaseInfoBeanList()){
            if(CustStatEnum.NORMAL.getCode().equals(custBaseInfoBean.getCustStat())){
                txAcctNoSet.add(custBaseInfoBean.getTxAcctNo());
            }
        }
        if( txAcctNoSet.size() != 1){
            throw new TmsCounterException(TmsCounterResultEnum.ID_NO_ERROR);
        }
        return txAcctNoSet.iterator().next();
    }
}
