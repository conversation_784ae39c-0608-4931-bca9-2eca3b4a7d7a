/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:()
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年7月18日 上午9:21:06
 * @since JDK 1.6
 */
public class FeeDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 6197288127734368929L;

    /***
     * 申请金额（含费）
     */
    private BigDecimal payAmt;
    /**
     * 费率（原始费率）
     */
    private BigDecimal feeRate;

    private BigDecimal discountRate;

    private BigDecimal fundBuyFee;

    public BigDecimal getFundBuyFee() {
        return fundBuyFee;
    }

    public void setFundBuyFee(BigDecimal fundBuyFee) {
        this.fundBuyFee = fundBuyFee;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(BigDecimal payAmt) {
        this.payAmt = payAmt;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }
}
