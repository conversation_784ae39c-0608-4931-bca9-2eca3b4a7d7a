/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(客户信息)
 * <AUTHOR>
 * @date 2017年4月6日 下午6:40:20
 * @since JDK 1.6
 */
public class CustInfoDto implements Serializable {
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -7659478555597623884L;

    /**
     * 客户号
     */
    private String custNo;
    /**
     * 客户类型
     */
    private String invstType;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 是否长期有效0-否,1-是
     */
    private String idAlwaysValidFlag;
    /**
     * 证件有效截至日
     */
    private String idValidityEnd;
    /**
     * 法人代表名称
     */
    private String corporation;
    /**
     * 法人代表证件类型
     */
    private String corpIdType;
    /**
     * 法人代表证件号
     */
    private String corpIdNo;
    /**
     * 法人代表证件是否长期有效0-否,1-是
     */
    private String corpIdAlwaysValidFlag;
    /**
     * 法人代表证件有效期截止日
     */
    private String corpIdValidityEnd;
    /**
     * 开户分销机构
     */
    private String regDisCode;
    /**
     * 开户渠道
     */
    private String regTradeChan;
    /**
     * 开户日期
     */
    private String regDt;
    /**
     * 身份验证状态
     */
    private String idVrfyStat;
    /**
     * 客户状态
     */
    private String custStat;
    /**
     * 销户日期
     */
    private String canDt;

    /**
     * 已签署合格投资者承诺书 0-签署；1-未签署
     */
    private String signFlag;

    /**
     * 合作客户风险等级
     */
    private String corpCustRiskLevel;
    /**
     * 客户风险等级
     */
    private String custRiskLevel;

    /**
     * 风险评测日期
     */
    private String riskSurveyDt;
    /**
     * 投资者类型 1-专业；0-普通
     */
    private String investorType;
    /**
     * 风险已过期
     */
    private boolean overdue;
    /**
     * 所属分销
     */
    private String disCode;
    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 回款协议方式
     * 1-回款至银行卡（系统默认）
     * 2-回款至银行卡（用户选择）
     * 3-回款至储蓄罐（系统默认）
     * 4-回款至储蓄罐（用户选择
     */
    private String collectProtocolMethod;
    /**
     * 客户资管投资承诺书签署状态 1-签署；0-未签署
     */
    private String fundFlag;

    /**
     * 一帐通号
     */
    private String hboneNo;

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdAlwaysValidFlag() {
        return idAlwaysValidFlag;
    }

    public void setIdAlwaysValidFlag(String idAlwaysValidFlag) {
        this.idAlwaysValidFlag = idAlwaysValidFlag;
    }

    public String getIdValidityEnd() {
        return idValidityEnd;
    }

    public void setIdValidityEnd(String idValidityEnd) {
        this.idValidityEnd = idValidityEnd;
    }

    public String getCorporation() {
        return corporation;
    }

    public void setCorporation(String corporation) {
        this.corporation = corporation;
    }

    public String getCorpIdType() {
        return corpIdType;
    }

    public void setCorpIdType(String corpIdType) {
        this.corpIdType = corpIdType;
    }

    public String getCorpIdNo() {
        return corpIdNo;
    }

    public void setCorpIdNo(String corpIdNo) {
        this.corpIdNo = corpIdNo;
    }

    public String getCorpIdAlwaysValidFlag() {
        return corpIdAlwaysValidFlag;
    }

    public void setCorpIdAlwaysValidFlag(String corpIdAlwaysValidFlag) {
        this.corpIdAlwaysValidFlag = corpIdAlwaysValidFlag;
    }

    public String getCorpIdValidityEnd() {
        return corpIdValidityEnd;
    }

    public void setCorpIdValidityEnd(String corpIdValidityEnd) {
        this.corpIdValidityEnd = corpIdValidityEnd;
    }

    public String getRegDisCode() {
        return regDisCode;
    }

    public void setRegDisCode(String regDisCode) {
        this.regDisCode = regDisCode;
    }

    public String getRegTradeChan() {
        return regTradeChan;
    }

    public void setRegTradeChan(String regTradeChan) {
        this.regTradeChan = regTradeChan;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }

    public String getIdVrfyStat() {
        return idVrfyStat;
    }

    public void setIdVrfyStat(String idVrfyStat) {
        this.idVrfyStat = idVrfyStat;
    }

    public String getCustStat() {
        return custStat;
    }

    public void setCustStat(String custStat) {
        this.custStat = custStat;
    }

    public String getCanDt() {
        return canDt;
    }

    public void setCanDt(String canDt) {
        this.canDt = canDt;
    }

    public String getSignFlag() {
        return signFlag;
    }

    public void setSignFlag(String signFlag) {
        this.signFlag = signFlag;
    }

    public String getCorpCustRiskLevel() {
        return corpCustRiskLevel;
    }

    public void setCorpCustRiskLevel(String corpCustRiskLevel) {
        this.corpCustRiskLevel = corpCustRiskLevel;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel;
    }

    public String getRiskSurveyDt() {
        return riskSurveyDt;
    }

    public void setRiskSurveyDt(String riskSurveyDt) {
        this.riskSurveyDt = riskSurveyDt;
    }

    public String getInvestorType() {
        return investorType;
    }

    public void setInvestorType(String investorType) {
        this.investorType = investorType;
    }

    public boolean isOverdue() {
        return overdue;
    }

    public void setOverdue(boolean overdue) {
        this.overdue = overdue;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getCollectProtocolMethod() {
        return collectProtocolMethod;
    }

    public void setCollectProtocolMethod(String collectProtocolMethod) {
        this.collectProtocolMethod = collectProtocolMethod;
    }

    public String getFundFlag() {
        return fundFlag;
    }

    public void setFundFlag(String fundFlag) {
        this.fundFlag = fundFlag;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }
}
