/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.auth;

import org.apache.shiro.authc.UsernamePasswordToken;

/**
 * @className EasyTypeToken
 * @description
 * <AUTHOR>
 * @date 2019/6/13 16:18
 */
public class EasyTypeToken extends UsernamePasswordToken {
    private static final long serialVersionUID = -2564928913725078138L;

    private LoginTypeEnum type;


    public EasyTypeToken() {
        super();
    }


    public EasyTypeToken(String username, String password, LoginTypeEnum type, boolean rememberMe,  String host) {
        super(username, password, rememberMe,  host);
        this.type = type;
    }
    /**免密登录*/
    public EasyTypeToken(String username) {
        super(username, "", false, null);
        this.type = LoginTypeEnum.NOPASSWD;
    }
    /**账号密码登录*/
    public EasyTypeToken(String username, String password) {
        super(username, password, false, null);
        this.type = LoginTypeEnum.PASSWORD;
    }

    public LoginTypeEnum getType() {
        return type;
    }


    public void setType(LoginTypeEnum type) {
        this.type = type;
    }
}
