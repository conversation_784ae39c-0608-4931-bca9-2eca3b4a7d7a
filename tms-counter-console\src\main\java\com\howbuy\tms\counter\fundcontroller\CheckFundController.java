/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.interlayer.product.enums.ProductClassEnum;
import com.howbuy.interlayer.product.model.portfolio.PortfolioProductFullModel;
import com.howbuy.interlayer.product.service.AdviserProdInfoService;
import com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealFacade;
import com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealRequest;
import com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealResponse;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.fundservice.trade.AdviserService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceFacade;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceRequest;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * @description:(柜台审核控制)
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class CheckFundController extends AbstractController {
    private static Logger logger = LogManager.getLogger(CheckFundController.class);

    @Resource
    private AdviserService activeService;

    @Resource
    private AdviserProdInfoService adviserProdInfoService;

    @Autowired
    private QueryModifyableDealFacade queryCanModifyDealFacade;

    @Autowired
    @Qualifier("queryAllCustInfoOuterService")
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;

    @Resource
    private QueryCustAdviserBalanceFacade queryCustAdviserBalanceFacade;

    /**
     * 
     * checkConfirm:(审核确认)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:16:20
     */
    @RequestMapping("/tmscounter/fund/checkconfirm.htm")
    public ModelAndView checkConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String checkFaildDesc = request.getParameter("checkFaildDesc");
        String checkStatus = request.getParameter("checkStatus");
        String redeemDirection = request.getParameter("redeemDirection");
        String beforeModifyDirection = request.getParameter("beforeModifyDirection");
        String checkedOrderForm = request.getParameter("checkedOrderForm");
        String operation = request.getParameter("operation");
        String productInfoForm = request.getParameter("productInfoForm");

        logger.info("CheckFundController|checkConfirm: checkedOrderForm:{}, checkStatus:{}, productInfoForm:{}", checkedOrderForm, checkStatus, productInfoForm);
        
        CounterOrderDto counterOrderDto = JSON.parseObject(checkedOrderForm, CounterOrderDto.class);
        String dealAppNo = counterOrderDto != null ? counterOrderDto.getDealAppNo() : null;
        if(StringUtils.isEmpty(dealAppNo) || StringUtils.isEmpty(checkStatus)){
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_PARAMS_ERROR);
        }

        if (!Constants.OPERATION_ABOLISH.equals(operation)) {
            String creator = StringUtils.isEmpty(counterOrderDto.getCreator()) ? "" : counterOrderDto.getCreator();
            if (creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_REPLY);
            }
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterOrderDto);

        // 审核要通过时 再次校验
        if (CheckFlagEnum.CHECK_SUCC.getCode().equals(checkStatus)) {
            // 投顾校验 ： 该投顾产品所属的投顾供应商为模式一（投顾拆单）且批处理‘投顾交易申请处理’已完成，则不允许撤单，提示：该笔订单已经上报给投顾方，不允许撤单。
            tmsCounterService.adviserCancelValid(disInfoDto, counterOrderDto.getPartnerCode(), counterOrderDto.getProtocolType());
        }

        CounterPortfolioProductDto fundInfo = JSON.parseObject(productInfoForm, CounterPortfolioProductDto.class);

        // 主申请单
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        BeanUtils.copyProperties(counterOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setCheckFlag(checkStatus);
        submitUncheckOrderDto.setMemo(checkFaildDesc);
        submitUncheckOrderDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setAllowDt(counterOrderDto.getAllowDt());
        redeemDirection = !StringUtils.isEmpty(redeemDirection) ? redeemDirection : counterOrderDto.getWithdrawDirection();
        submitUncheckOrderDto.setWithdrawDirection(redeemDirection);
        submitUncheckOrderDto.setBeforeModifyDirection(beforeModifyDirection);
        if (StringUtils.isNotBlank(counterOrderDto.getQuestionAnswer())) {
            String answers = String.join(",", counterOrderDto.getQuestionAnswer().toUpperCase().split(""));
            submitUncheckOrderDto.setSurveyAnswer(answers);
        }

        buildSubmitUncheckOrderDto(counterOrderDto, dealAppNo, submitUncheckOrderDto, checkStatus, operatorInfoCmd, checkFaildDesc);
        // 明细申请单
        List<SubmitUncheckOrderDtlDto> checkDtlOrders = null;
        if(TxCodes.COUNTER_TRANSFER_TUBE_OUT.equals(counterOrderDto.getTxCode())
                || TxCodes.COUNTER_LCT_TRANSFER_TUBE_OUT.equals(counterOrderDto.getTxCode())
                || TxCodes.REDEEM.equals(counterOrderDto.getTxCode())){
            CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
            queryReqDto.setDealAppNo(dealAppNo);
            
            checkDtlOrders = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
            if(CollectionUtils.isEmpty(checkDtlOrders)){
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_NOT_QUERY_ORDER);
            }
        }
        
        tmsFundCounterService.checkOrder(submitUncheckOrderDto, fundInfo, checkDtlOrders, disInfoDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        WebUtil.write(response, rst);
        return null;
    }

    private void buildSubmitUncheckOrderDto(CounterOrderDto counterOrderDto, String dealAppNo, SubmitUncheckOrderDto submitUncheckOrderDto, String checkStatus,
            OperatorInfoCmd operatorInfoCmd, String checkFaildDesc) throws Exception {
        if(TxCodes.REDEEM.equals(counterOrderDto.getTxCode())){
            CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
            queryReqDto.setDealAppNo(dealAppNo);
            // 零售
            queryReqDto.setProductClassList(Lists.newArrayList(ProductClassEnum.RETAIL.getCode(), ProductClassEnum.PORTFOLIO.getCode()));
            queryReqDto.setPageNo(1);
            queryReqDto.setPageSize(1);
            // 主申请单
            CounterOrderDto queryCounterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);
            if(CheckFlagEnum.CHECK_SUCC.getCode().equals(queryCounterOrderDto.getCheckFlag()) || CheckFlagEnum.CHECK_FAILD.getCode().equals(queryCounterOrderDto.getCheckFlag())
                    || CheckFlagEnum.CHECK_REJECT.getCode().equals(queryCounterOrderDto.getCheckFlag()) || CheckFlagEnum.CHECK_ABOLISH.getCode().equals(queryCounterOrderDto.getCheckFlag())){
                throw new TmsCounterException(TmsCounterResultEnum.ORDER_ALEARY_SH);
            }

            BeanUtils.copyProperties(queryCounterOrderDto, submitUncheckOrderDto);
            submitUncheckOrderDto.setCheckFlag(checkStatus);
            submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
            submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
            submitUncheckOrderDto.setCheckDtm(new Date());
            submitUncheckOrderDto.setMemo(checkFaildDesc);
            submitUncheckOrderDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        }
    }

    /**
     * 
     * queryCheckOrder:(查询待审核订单列表)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:16:03
     */
    @RequestMapping("/tmscounter/fund/querycheckorder.htm")
    public ModelAndView queryCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String queryOrderCmd = request.getParameter("queryConditonForm");
        String pageNum = StringUtils.isEmpty(request.getParameter("page")) ? "1" : request.getParameter("page");
        String pageSize = StringUtils.isEmpty(request.getParameter("pageSize")) ? "20" : request.getParameter("pageSize");
        String owner = request.getParameter("owner");
        String checkFlag = request.getParameter("checkFlag");
        String tradeDt = request.getParameter("tradeDt");
        String queryType = request.getParameter("queryType");
        logger.debug("queryOrderCmd:{}, checkFlag:{}, tradeDt:{}", queryOrderCmd, checkFlag, tradeDt);
        
        CounterQueryOrderReqDto counterQueryOrderReqDto = JSON.parseObject(queryOrderCmd, CounterQueryOrderReqDto.class);
        counterQueryOrderReqDto.setPageNo(Integer.parseInt(pageNum));
        counterQueryOrderReqDto.setPageSize(Integer.parseInt(pageSize));
        // 零售
        counterQueryOrderReqDto.setProductClassList(Lists.newArrayList(ProductClassEnum.RETAIL.getCode(), ProductClassEnum.PORTFOLIO.getCode()));
        if (!StringUtils.isEmpty(checkFlag)) {
            counterQueryOrderReqDto.setCheckFlag(checkFlag);
        }
        
        // 我的交易申请查询需要绑定操作员号
        if (Constants.ROLE_OWNER.equals(owner)) {
            OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
            counterQueryOrderReqDto.setCreator(operatorInfoCmd.getOperatorNo());

            // 我的交易申请查询默认查询当前工作日
            if (Constants.ORDER_CMD_BRACKETS.equals(queryOrderCmd)) {
                counterQueryOrderReqDto.setTradeDt(tradeDt);
            }
        }
        
        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        if (StringUtils.isEmpty(counterQueryOrderReqDto.getTxAcctNo())) {
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getIdNo())) {
                queryCustBaseInfoReqDto.setIdNo(counterQueryOrderReqDto.getIdNo());
                QueryCustBaseInfoRespDto qeryCustBaseInfoRespDto = tmsCounterService.queryCustBaseInfo(queryCustBaseInfoReqDto, null);
                counterQueryOrderReqDto.setTxAcctNo(qeryCustBaseInfoRespDto.getTxAcctNo());
            }
        }
        logger.debug("counterQueryOrderReqDto:{}", JSON.toJSONString(counterQueryOrderReqDto));

        // 主申请单
        CounterQueryOrderRespDto counterQueryOrderRespDto = tmsFundCounterService.counterQueryOrder(counterQueryOrderReqDto, null);
        if(counterQueryOrderRespDto == null){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD);
        }
        // 脱敏
        if (!CollectionUtils.isEmpty(counterQueryOrderRespDto.getCounterOrderList())) {
            for (CounterOrderDto dto : counterQueryOrderRespDto.getCounterOrderList()) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }

        // 明细
        if ("1".equals(queryType)) {
            getCustInfoAndBankAcct(counterQueryOrderRespDto.getCounterOrderList(), request);
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("counterOrderList", counterQueryOrderRespDto.getCounterOrderList());
        body.put("totalPage", counterQueryOrderRespDto.getTotalPage());
        body.put("pageNum", counterQueryOrderRespDto.getPageNo());
        body.put("counterQueryOrderRespDto", counterQueryOrderRespDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private void getCustInfoAndBankAcct(List<CounterOrderDto> list, HttpServletRequest request) throws Exception{
        if(org.springframework.util.CollectionUtils.isEmpty(list)){
            return;
        }
        //考虑到不要多次查询接口 原有对象的数据 key=txAcctNo
        Map<String,String> cpNoAndBankAcctMap = new HashMap<String,String>(16);
        for(CounterOrderDto bean : list){
            String bankAcct = cpNoAndBankAcctMap.get(bean.getCpAcctNo());
            if(StringUtils.isEmpty(bankAcct)){
                try {
                    QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
                    queryAllCustInfoContext.setDisCode(bean.getDisCode());
                    queryAllCustInfoContext.setTxAcctNo(bean.getTxAcctNo());
                    queryAllCustInfoContext.setCpAcctNo(bean.getCpAcctNo());
                    QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);
                    if(queryAllCustInfoResult != null){
                        bean.setBankAcct(queryAllCustInfoResult.getCustBankCardInfo().getBankAcct());
                        bean.setIdNo(queryAllCustInfoResult.getCustInfo().getIdNo());
                        bean.setCustName(queryAllCustInfoResult.getCustInfo().getCustName());
                        cpNoAndBankAcctMap.put(bean.getCpAcctNo(), queryAllCustInfoResult.getCustBankCardInfo().getBankAcct());
                    }
                } catch (Exception e) {
                    logger.error("QueryBusiController|queryBusiService.queryCustBankCard error.",e);
                }
            }else{
                bean.setBankAcct(bankAcct);
            }
        }
    }

    /**
     * 
     * queryCheckOrderById:(查询单条未审核订单)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:10:22
     */
    @RequestMapping("/tmscounter/fund/querycheckorderbyid.htm")
    public ModelAndView queryCheckOrderById(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String pageNum = request.getParameter("pageNum");
        String pageSize = request.getParameter("pageSize");

        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        // 零售
        queryReqDto.setProductClassList(Lists.newArrayList(ProductClassEnum.RETAIL.getCode(), ProductClassEnum.PORTFOLIO.getCode()));
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        // 主申请单
        CounterOrderDto counterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);

        // 明细申请单
        List<SubmitUncheckOrderDtlDto> dtlOrderDto = null;
        QueryModifyableDealResponse.FundDealOrderDtlBean  dealOrderDtlBean = new QueryModifyableDealResponse.FundDealOrderDtlBean();
        if (counterOrderDto != null) {
            counterOrderDto.setBankAcct(getBankAcct(counterOrderDto.getBankAcct(), counterOrderDto.getCpAcctNo(), counterOrderDto.getTxAcctNo(), counterOrderDto.getDisCode()));
            counterOrderDto.setInBankAcct(getBankAcct(counterOrderDto.getInBankAcct(), counterOrderDto.getInCpAcctNo(), counterOrderDto.getTxAcctNo(), counterOrderDto.getDisCode()));
            if (StringUtils.isNotBlank(counterOrderDto.getSurveyAnswer())) {
                String answers = String.join("", counterOrderDto.getSurveyAnswer().split(","));
                counterOrderDto.setSurveyAnswer(answers);
            }

            if(TxCodes.COUNTER_TRANSFER_TUBE_OUT.equals(counterOrderDto.getTxCode()) || TxCodes.REDEEM.equals(counterOrderDto.getTxCode())){
                dtlOrderDto = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
                if (!CollectionUtils.isEmpty(dtlOrderDto)) {
                    for (SubmitUncheckOrderDtlDto dtlDto : dtlOrderDto) {
                        dtlDto.setBankAcct(getBankAcct(dtlDto.getBankAcct(), dtlDto.getCpAcctNo(), counterOrderDto.getTxAcctNo(), counterOrderDto.getDisCode()));
                    }
                }
            }else if(TxCodes.MODIFY_REDEEM_DIRECTION.equals(counterOrderDto.getTxCode())){
                String txAcctNo = counterOrderDto.getTxAcctNo();
                String dealNo = counterOrderDto.getDealNo();
                QueryModifyableDealRequest dealRequest = new QueryModifyableDealRequest();
                dealRequest.setTxAcctNo(txAcctNo);
                dealRequest.setDealNo(dealNo);
                QueryModifyableDealResponse queryCanModifyDealResponse = queryCanModifyDealFacade.execute(dealRequest);
                if(queryCanModifyDealResponse!= null && !CollectionUtils.isEmpty(queryCanModifyDealResponse.getFundDealOrderDtlPoList())){
                    dealOrderDtlBean = queryCanModifyDealResponse.getFundDealOrderDtlPoList().get(0);
                }
            }else if (TxCodes.ADVISER_REDEEM_COUNTER.equals(counterOrderDto.getTxCode())) {// 赎回状态
                PortfolioProductFullModel portfolioProdInfoDto = adviserProdInfoService.queryAdviserProdInfoByProductCodeV2(counterOrderDto.getFundCode());

                QueryCustAdviserBalanceRequest adviserBalanceRequest = new QueryCustAdviserBalanceRequest();
                adviserBalanceRequest.setTxAcctNo(counterOrderDto.getTxAcctNo());
                adviserBalanceRequest.setProductCode(counterOrderDto.getFundCode());
                adviserBalanceRequest.setProtocolNo(counterOrderDto.getProtocolNo());
                adviserBalanceRequest.setDisCode(counterOrderDto.getDisCode());
                adviserBalanceRequest.setAppDt(counterOrderDto.getAppDt());
                adviserBalanceRequest.setAppTm(counterOrderDto.getAppTm());
                QueryCustAdviserBalanceResponse adviserBalanceResponse = queryCustAdviserBalanceFacade.execute(adviserBalanceRequest);

                counterOrderDto.setAdviser(true);
                counterOrderDto.setRedeemStatus(YesOrNoEnum.YES.getCode().equals(portfolioProdInfoDto.getIsSoldOpen()) && adviserBalanceResponse.isFundCanRedeem() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            }
        }

        QueryRedmProductListResDto queryRedmProductListResDto = null;
        CustomSellRatioResDto customSellRatioResDto = null;
        if(counterOrderDto != null && TxCodes.REDEEM.equals(counterOrderDto.getTxCode())){
            if("1".equals(counterOrderDto.getCustomRatioType())){
                QueryRedmProductListReqDto reqDto = new QueryRedmProductListReqDto();
                reqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
                reqDto.setProductCode(counterOrderDto.getFundCode());
                reqDto.setProtocolNo(counterOrderDto.getProtocolNo());
                reqDto.setDisCode(counterOrderDto.getDisCode());
                reqDto.setCpAcctNo(counterOrderDto.getCpAcctNo());
                reqDto.setAppRatio(String.valueOf(counterOrderDto.getAppRatio()));
                queryRedmProductListResDto = activeService.queryRedmProductList(reqDto);
            }
            if("2".equals(counterOrderDto.getCustomRatioType())){
                QueryCustomSellRatioResDto reqDto = new QueryCustomSellRatioResDto();
                reqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
                reqDto.setDisCode(counterOrderDto.getDisCode());
                reqDto.setProtocolNo(counterOrderDto.getProtocolNo());
                reqDto.setCpAcctNo(counterOrderDto.getCpAcctNo());
                reqDto.setCustomOrRatio(counterOrderDto.getCustomRatioType());

                reqDto.setRedeemRatio(counterOrderDto.getAppRatio());
                RedeemTrailResDto redeemTrailResDto = null;
                List<RedeemTrailResDto> redeemTrailResDtos = new ArrayList<>();
                for(SubmitUncheckOrderDtlDto redeemReqDto : dtlOrderDto){
                    redeemTrailResDto = new RedeemTrailResDto();
                    redeemTrailResDto.setFundCode(redeemReqDto.getFundCode());
                    redeemTrailResDto.setAppVol(redeemReqDto.getAppVol());
                    redeemTrailResDtos.add(redeemTrailResDto);
                }
                reqDto.setRedeemTrailResDtos(redeemTrailResDtos);
                customSellRatioResDto = activeService.queryCustomSellRatio(reqDto);
            }
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(2);
        body.put("checkOrder", counterOrderDto);
        body.put("checkDtlOrder", dtlOrderDto);
        body.put("dealOrderDtlBean", dealOrderDtlBean);
        body.put("queryTrialResult", queryRedmProductListResDto);
        body.put("querySellVolResult", customSellRatioResDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private String getBankAcct(String oriBankAcct, String cpAcctNo, String txAcctNo, String disCode) {
        if (StringUtils.isEmpty(cpAcctNo)) {
            return oriBankAcct;
        }
        if (StringUtils.isNotEmpty(oriBankAcct) && oriBankAcct.indexOf("*") < 0) {
            return oriBankAcct;
        }

        QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
        queryAllCustInfoContext.setDisCode(disCode);
        queryAllCustInfoContext.setTxAcctNo(txAcctNo);
        queryAllCustInfoContext.setCpAcctNo(cpAcctNo);
        QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);
        if (queryAllCustInfoResult != null && queryAllCustInfoResult.getCustBankCardInfo() != null) {
            return queryAllCustInfoResult.getCustBankCardInfo().getBankAcct();
        }
        return null;
    }

}
