package com.howbuy.tms.counter.common;

public enum WebDevEnum {

    /**
     * 开户提前上传身份，临时文件存储
     */
    STORE_CONFIG("idCard", ""),
    
    /**
     * 证件文件 上传(账户中心身份证实际存储地址) 虚拟映射
     */
    ID_CARD_UP_FILE("acc.idCardUpFile", "/cim_web_server/center_feature"),
    
    /**
     * 证件文件 上传(账户中心身份证实际存储地址)
     */
    ID_CARD_UP_FILE_MAPPING("acc.idCardUpFile", "/cim_web_server"),
    
    
    /**
     * 人脸识别匹配成功后上传的图片(账户中心身份证实际存储地址)
     */
    FACE_PIC_UP_FILE("acc.facePicUpFile", "/cim_web_server/face_center_feature"),

    /**
     * 换卡视频认证
     */
    VIDEO_PROMISE_FILE("acc.videoPromiseFile", "/cim_web_server/video_promise_file"),

    ;

    private String code;
    private String name;

    private WebDevEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    
}
