<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - limitations 
	under the License. - See the License for the specific language governing 
	permissions and -->

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	
		<!-- 查询上报订单 -->
     	<dubbo:reference registry="batch-center-remote"  id="queryFundCheckOrderFacade" interface="com.howbuy.tms.batch.facade.query.queryfundcheckorder.QueryFundCheckOrderFacade" check="false"/>
	
      	<!-- 查询交易订单明细信息 -->
     	<dubbo:reference registry="batch-center-remote"  id="queryFundDealOrderDtlFacade" interface="com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlFacade" check="false"/>
       	
       	<!-- 查询客户持仓明细 -->
       	<dubbo:reference registry="order-center-remote"  id="queryFundAcctBalanceDtlFacade" interface="com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlFacade" check="false"/>
		
		<!-- 查询理财型基金明细持仓 -->
       	<dubbo:reference registry="order-center-remote"  id="queryFinancialBalanceDtlFacade" interface="com.howbuy.tms.orders.search.facade.query.queryfinancialbalancedtl.QueryFinancialBalanceDtlFacade" check="false"/>
      
       	<!-- 查询客户可撤单订单 -->
       	<dubbo:reference registry="order-center-remote"  id="queryCustCancelDealFacade" interface="com.howbuy.tms.orders.search.facade.query.querycustcanceldeal.QueryCustCancelDealFacade" check="false"/>
	
		<!-- 查询协议列表-->
		<dubbo:reference registry="order-center-remote"  id="queryProtocolListFacade" interface="com.howbuy.tms.orders.search.facade.query.fund.queryprotocollist.QueryProtocolListFacade" check="false"/>
		
		<!-- 查询默认基金交易账号 -->
		<dubbo:reference registry="order-center-remote"  id="queryAcFundTxAcctFacade" interface="com.howbuy.tms.orders.search.facade.query.fund.queryacfundacct.QueryFundTxAcctFacade" check="false"/>
    
    	<!-- 查询协议列表-->
        <dubbo:reference registry="batch-center-remote"  id="querySubmitCheckOrderDtlFacade" interface="com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlFacade" check="false"/>
		
		<!-- 查询审核主订单 -->
        <dubbo:reference registry="batch-center-remote"  id="querySubmitCheckOrderFacade" interface="com.howbuy.tms.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderFacade" check="false"/>
		
		<!-- 提交审核订单 -->
        <dubbo:reference registry="batch-center-remote"  id="submitCheckOrderFacade" interface="com.howbuy.tms.batch.facade.trade.submitcheckorder.SubmitCheckOrderFacade" check="false"/>

		<!-- 零售柜台休市 -->
	    <dubbo:reference registry="batch-center-remote"  id="counterEndFacade" interface="com.howbuy.tms.batch.facade.trade.counterend.CounterEndFacade" check="false"/>
	  
	    <!-- 查询柜台收市TA汇总信息 -->
	    <dubbo:reference registry="batch-center-remote"  id="queryCounterEndTaCountFacade" interface="com.howbuy.tms.batch.facade.query.querycounterendtacount.QueryCounterEndTaCountFacade" check="false"/>
	   
	    <!-- 查询柜台配置不收市TA信息 -->
	    <dubbo:reference registry="batch-center-remote"  id="queryCounterEndTaCfgFacade" interface="com.howbuy.tms.batch.facade.query.querycounterendtacfg.QueryCounterEndTaCfgFacade" check="false"/>
	  
	    <!-- 柜台添加和删除不收市TA -->
	    <dubbo:reference registry="batch-center-remote"  id="counterSaveOrDelNotEndTaFacade" interface="com.howbuy.tms.batch.facade.trade.counternotendta.CounterSaveOrDelNotEndTaFacade" check="false"/>
       
        <!-- 柜台认申购申请提交 -->
		<dubbo:reference registry="batch-center-remote"  id="counterFundPurchaseFacade" interface="com.howbuy.tms.batch.facade.trade.counterpurchase.CounterPurchaseFacade" check="false"/>
		
		<!-- 柜台投顾买入申请提交 -->
	    <dubbo:reference registry="batch-center-remote"  id="counterAdviserPurchaseFacade" interface="com.howbuy.tms.batch.facade.trade.counterpurchase.CounterAdviserPurchaseFacade" check="false"/>

		<!-- 柜台认申购提交校验 -->
        <dubbo:reference registry="order-center-remote"  id="subsOrPurFundValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurvalidate.SubsOrPurValidateFacade" check="false"/>
		
		<!-- 柜台认申购审核落单 -->
		<dubbo:reference registry="order-center-remote"  id="subsOrPurFundCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurcounter.SubsOrPurCounterFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote"  id="cancelPortfolioFacade" interface="com.howbuy.tms.robot.orders.facade.trade.cancelportfolio.CancelPortfolioFacade" check="false"/>

		<!--询投顾问卷上次答题风险等级匹配的产品信息-->
		<dubbo:reference registry="robot-order-center-remote"  id="queryRiskProductByLastOfAnswerFacade" interface="com.howbuy.tms.robot.orders.facade.query.adviser.queryriskproductbylastofanswer.QueryRiskProductByLastOfAnswerFacade" check="false"/>
		
		<!-- 柜台认申购提交校验 -->
		<dubbo:reference registry="robot-order-center-remote"  id="counterPurchaseAdviserFacadeFacade" interface="com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.CounterPurchaseAdviserFacadeFacade" check="false"/>
		
		<!-- 柜台认申购审核落单 -->
		<dubbo:reference registry="robot-order-center-remote"  id="counterPurchaseAdviserFacadeValidateFacade" interface="com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.CounterPurchaseAdviserFacadeValidateFacade" check="false"/>
		
		<!-- 查询投顾可用资产  -->
		<dubbo:reference registry="robot-order-center-remote" id="queryCustAdviserBalanceFacade" interface="com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceFacade" check="false" />

		<!-- 柜台投顾赎回申请校验提交 -->
		<dubbo:reference registry="robot-order-center-remote" id="counterRedeemAdviserValidateFacade" interface="com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.CounterRedeemAdviserValidateFacade" check="false" />
		
		<!-- 柜台投顾赎回申请提交 -->
		<dubbo:reference registry="robot-order-center-remote" id="counterRedeemAdviserFacade" interface="com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.CounterRedeemAdviserFacade" check="false" />

	    <!-- 柜台赎回申请提交 -->
       	<dubbo:reference registry="batch-center-remote"  id="counterFundRedeemFacade" interface="com.howbuy.tms.batch.facade.trade.counterredeem.CounterRedeemFacade" check="false"/>
		
		<!-- 柜台赎回提交校验 -->
        <dubbo:reference registry="order-center-remote"  id="redeemFundValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.redeem.redeemvalidate.RedeemValidateFacade" check="false"/>
		
		<!-- 柜台赎回 审核落单-->
	    <dubbo:reference registry="order-center-remote"  id="redeemFundCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.redeem.redeemcounter.RedeemCounterFacade" check="false"/>
		
		<!-- 柜台基金持仓全赎校验 -->
	    <dubbo:reference registry="order-center-remote"  id="queryIsOrNotAllRedeemFacade" interface=" com.howbuy.tms.orders.search.facade.query.fund.queryisornotallredeem.QueryIsOrNotAllRedeemFacade" check="false"/>
		
		<!-- 柜台基金转换申请提交 -->
       	<dubbo:reference registry="batch-center-remote"  id="counterFundTransferFacade" interface="com.howbuy.tms.batch.facade.trade.counterfundtransfer.CounterFundTransferFacade" check="false"/>
       
        <!-- 柜台基金转换校验 -->
        <dubbo:reference registry="order-center-remote"  id="exChangeValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.exchange.exchangevalidate.ExChangeValidateFacade" check="false"/>
		
		<!-- 柜台基金审核落单 -->
	    <dubbo:reference registry="order-center-remote"  id="exchangeFundCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.exchange.exchangecounter.ExchangeCounterFacade" check="false"/>
		
		<!-- 柜台修改分红方式申请提交  -->
       	<dubbo:reference registry="batch-center-remote"  id="counterFundModifyDivFacade" interface="com.howbuy.tms.batch.facade.trade.countermodifydiv.CounterModifyDivFacade" check="false"/>
		
		<!-- 柜台修改分红方式校验 -->
        <dubbo:reference registry="order-center-remote"  id="modifyDivFundValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivvalidate.ModifyDivValidateFacade" check="false"/>
		
		<!-- 柜台修改分红方式审核落单 -->
	    <dubbo:reference registry="order-center-remote"  id="modifyDivFundCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivcounter.ModifyDivCounterFacade" check="false"/>
		
		<!-- 柜台撤单申请提交 -->
		<dubbo:reference registry="batch-center-remote"  id="counterTradeCancelFacade" interface="com.howbuy.tms.batch.facade.trade.countertradecancel.CounterTradeCancelFacade" check="false"/>
		
		<!-- 柜台撤单校验 -->
        <dubbo:reference registry="order-center-remote"  id="cancelFundOrderValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.cancelorder.cancelordervalidate.CancelOrderValidateFacade" check="false"/>
      
        <!-- 柜台撤单审核落单 -->
	    <dubbo:reference registry="order-center-remote"  id="cancelFundOrderFacade" interface="com.howbuy.tms.orders.facade.trade.fund.cancelorder.CancelOrderFacade" check="false"/>

		<dubbo:reference registry="order-center-remote"  id="batchCancelOrderFacade" interface="com.howbuy.tms.orders.facade.trade.fund.cancelorder.BatchCancelOrderFacade" check="false"/>

		<!-- 查询客户份额合并和迁移的持仓明细 -->
        <dubbo:reference registry="order-center-remote"  id="queryCustBalanceDetailFacade" interface="com.howbuy.tms.orders.search.facade.query.querycustbalancedetail.QueryCustBalanceDetailFacade" check="false"/>
       
        <!-- 柜台份额合并/迁移申请提交-->
        <dubbo:reference registry="batch-center-remote"  id="counterShareMergeFacade" interface="com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeFacade" check="false"/>

		<dubbo:reference registry="batch-center-remote" id="counterPortfolioRedeemFacade" interface="com.howbuy.tms.batch.facade.trade.counterportfolioredeem.CounterPortfolioRedeemFacade" check="false"/>

        <!-- 柜台份额合并提交校验-->
        <dubbo:reference registry="order-center-remote"  id="fundShareMergeValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergevalidate.ShareMergeValidateFacade" check="false"/>
       
        <!-- 柜台份额迁移提交校验-->
        <dubbo:reference registry="order-center-remote"  id="fundShareTransferValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfervalidate.ShareTransferValidateFacade" check="false"/>
       
        <!-- 柜台份额合并审核落单-->
        <dubbo:reference registry="order-center-remote"  id="fundShareMergeCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergecounter.ShareMergeCounterFacade" check="false"/>
       
        <!-- 柜台份额迁移审核落单-->
        <dubbo:reference registry="order-center-remote"  id="fundShareTransferCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterFacade" check="false"/>

        <!-- 柜台转托管转入/转出申请提交-->
        <dubbo:reference registry="batch-center-remote"  id="counterTransferTubeFacade" interface="com.howbuy.tms.batch.facade.trade.countertransfertube.CounterTransferTubeFacade" check="false"/>
       
        <!-- 柜台转托管转入校验-->
        <dubbo:reference registry="order-center-remote"  id="transManageInValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.transfertubein.validate.TransManageInValidateFacade" check="false"/>
       
        <!-- 柜台转托管转出校验-->
        <dubbo:reference registry="order-center-remote"  id="transManageOutValidateFacade" interface="com.howbuy.tms.orders.facade.trade.fund.transfertubeout.validate.TransManageOutValidateFacade" check="false"/>
      
        <!-- 柜台转托管转入审核落单-->
        <dubbo:reference registry="order-center-remote"  id="transManageInCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.transfertubein.counter.TransManageInCounterFacade" check="false"/>
       
        <!-- 柜台转托管转出审核落单-->
        <dubbo:reference registry="order-center-remote"  id="transManageOutCounterFacade" interface="com.howbuy.tms.orders.facade.trade.fund.transfertubeout.counter.TransManageOutCounterFacade" check="false"/>

	    <!-- 理财通转托管转出-->
	   <dubbo:reference registry="batch-center-remote"  id="lctCounterTransferTubeFacade" interface="com.howbuy.tms.batch.facade.trade.lctcountertransfertube.LctCounterTransferTubeFacade" check="false"/>

	   <dubbo:reference registry="batch-center-remote"  id="counterModifyDicountFacade" interface="com.howbuy.tms.batch.facade.trade.countermodifydicount.CounterModifyDicountFacade" check="false"/>

	   <dubbo:reference registry="order-center-remote"  id="queryInTransitFacade" interface="com.howbuy.tms.orders.search.facade.query.queryintransit.QueryInTransitFacade" check="false"/>

	   <dubbo:reference registry="batch-center-remote"  id="queryModifyableDealFacade" interface="com.howbuy.tms.batch.facade.query.querymodifyabledeal.QueryModifyableDealFacade" check="false"/>

		<dubbo:reference registry="order-center-remote"  id="queryGmInTransitFacade" interface="com.howbuy.tms.orders.search.facade.query.querygmintransit.QueryGmInTransitFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote"  id="custProtocolFacade" interface="com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote"  id="queryRedmProductListFacade" interface="com.howbuy.tms.robot.orders.facade.query.queryredmproductlist.QueryRedmProductListFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote"  id="queryUserBalanceFacade" interface="com.howbuy.tms.robot.orders.facade.query.querybalance.QueryUserBalanceFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote" id="queryCustomSellRatioFacade" interface="com.howbuy.tms.robot.orders.facade.query.querycustomorder.customsell.QueryCustomSellRatioFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote" id="queryBalanceVolFacade" interface="com.howbuy.tms.robot.orders.facade.query.querybalancevol.QueryBalanceVolFacade" check="false"/>

		<dubbo:reference registry="robot-order-center-remote" id="counterRedeemPortfolioFacade" interface="com.howbuy.tms.robot.orders.facade.trade.redeemportfolio.CounterRedeemPortfolioFacade" check="false"/>

		<!--协议和概要查询-->
		<dubbo:reference registry="order-center-remote" id="queryAgreementFacade" interface="com.howbuy.tms.orders.search.facade.query.agreement.QueryAgreementFacade" check="false"  />

</beans>