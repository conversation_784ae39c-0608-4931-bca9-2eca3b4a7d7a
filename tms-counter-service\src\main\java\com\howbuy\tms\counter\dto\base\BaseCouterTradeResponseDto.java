/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto.base;


/**
 * @description:(公共响应) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月28日 下午9:05:37
 * @since JDK 1.7
 */
public class BaseCouterTradeResponseDto extends BaseResponseDto {


    private static final long serialVersionUID = 7922914668390772554L;

}

