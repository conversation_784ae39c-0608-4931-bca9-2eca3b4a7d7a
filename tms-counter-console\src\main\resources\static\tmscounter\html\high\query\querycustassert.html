<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css?v="+Math.random() />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/jquery-ui-1.9.2.custom.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.filter.css" />
    <title>客户资产查询</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 业务查询 <span class="c-gray en">&gt;</span>交易类业务查询（高端） <span class="c-gray en">&gt;</span>客户资产查询<a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">客户资产查询</p>
<!--                <div class="cp_top mt30">-->
                <div class="cp_top">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span">一账通号</span><input type="text" name="hboneNo" id="hboneNo" placeholder="请输入一账通号">
                    <span class="normal_span ml30">证件号：</span>
                    <input name="idNo" id="idNo" type="text" placeholder='请输入'>
                    <span class="normal_span ml30">分销机构：</span>
                    <select title="Basic example" multiple="multiple" id="multiSelectDisCode" name="disCodeList" size="5">
                    </select>
                    <span class="normal_span">资产截止日期：</span>
                    <input name="assetEndDt"  id ="assetEndDt" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYY-MM-DD'})">
                </div>

                <div class="cp_top">
                    <a href="javascript:void(0)" id="queryCustInfoBtn" class="btn radius btn-secondary ml30">查询</a>
                </div>
            </div>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title mt30 cust_info" >客户基本信息</p>
        <div class="result2_tab cust_info" >
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>风险测评结果</th>
                        <th>开户分销机构</th>
                        <th>私募合格投资者认证</th>
                        <th>资管合格投资者认证</th>
                        <th>客户状态</th>
                        <th>投资者类型</th>
                        <th>协议回款方式</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30 cust_info" >证券类产品信息</p>
        <div class="result2_tab cust_info" >
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th><input class="checkAll" id="selectAllSecutities" type="checkbox" index="'+index+'"> 全选</th>
                        <th>产品代码</th>
                        <th>产品名称</th>
                        <th>持有份额（份）</th>
                        <th>份额净值</th>
                        <th>净值日期</th>
                        <th>市值（元）</th>
                        
                    </tr>
               </thead>
                <tbody id="securitiesAssetListId">
                </tbody>
            </table>
        </div>
        
         <p class="main_title mt30 cust_info" >股权类产品信息</p>
        <div class="result2_tab cust_info" >
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th><input class="checkAll" id="selectAllStockAsset" type="checkbox" index="'+index+'">全选</th>
                        <th>产品代码</th>
                        <th>产品名称</th>
                        <th>持有份额（份）</th>
                        <th>认购/参与净额</th>
                    </tr>
               </thead>
                <tbody id="stockAssetListId">
                </tbody>
            </table>
        </div>
        
         <div class="cp_top mt30">
             <a href="javascript:void(0)" id="exportBtn" class="btn radius btn-secondary ml30">导出中台资产证明</a>
             <a href="javascript:void(0)" id="exportCrmBtn" class="btn radius btn-secondary ml30">导出CRM资产证明</a>
             <a href="javascript:void(0)" id="exportBtnCE" class="btn radius btn-secondary ml30">导出中台资产证明(中英文版)</a>
             <a href="javascript:void(0)" id="exportCrmBtnCE" class="btn radius btn-secondary ml30">导出CRM资产证明(中英文版)</a>
         </div>
        <div class="cp_top mt30">
             <a href="javascript:void(0)" id="exportTotalSecurityBtn" class="btn radius btn-secondary ml40">导出中台证券类总资产证明</a>
             <a href="javascript:void(0)" id="exportTotalCrmSecurityBtn" class="btn radius btn-secondary ml40">导出CRM证券类总资产证明</a>
         </div>
	</div>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/ui-1.2.1/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../js/common.js"></script>
    <script type="text/javascript" src="../../../js/config.js?v=3.5.28"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=3.5.28"></script>
    <script type="text/javascript" src="../../../js/valid.js"></script>
    <script type="text/javascript" src="../../../js/high/conscode.js"></script>
    <script type="text/javascript" src="../../../js/high/query/querycustinfosubpage.js"></script>
    <script type="text/javascript" src="../../../js/high/query/queryhighproductinfosubpage.js"></script>
    <script type="text/javascript" src="../../../js/high/common/init.js?v=3.5.28"></script>
    <script type="text/javascript" src="../../../js/high/common/custinfo.js?v=3.5.28"></script>
    <script type="text/javascript" src="../../../js/high/query/querycustassert.js?v=********"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.filter.js"></script>
    
</body>

</html>