package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.tms.counter.config.TmsCounterNacosConfig;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 
 * @description:柜台首页
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年6月27日 下午5:42:34
 * @since JDK 1.6
 */
@Controller
public class IndexManagementController extends AbstractController {

    private Logger logger = LoggerFactory.getLogger(IndexManagementController.class);

    @Autowired
    TmsCounterNacosConfig tmsCounterNacosConfig;

    /**
     * 
     * index:柜台首页
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年6月27日 下午5:43:17
     */
    @RequestMapping("tmscounter/index.htm")
    public ModelAndView index(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info(">>>>>>>柜台首页");

        UserAccountModel userAccountModel = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);

        // 记录操作员信息
        OperatorInfoCmd operatorInfoCmd = new OperatorInfoCmd();
        operatorInfoCmd.setOperatorNo(userAccountModel.getUserName());
        operatorInfoCmd.setOperName(userAccountModel.getUserName());
        SessionUtil.setValue(TmsCounterConstant.SESSION_OPERATORINFO, operatorInfoCmd, request);

        request.setAttribute("operatorNo", userAccountModel.getUserName());

        request.setAttribute("centerPlatformurl", tmsCounterNacosConfig.getCenterPlatformurl());
        
        request.setAttribute("otcCounterPlatformurl", tmsCounterNacosConfig.getOtcCounterPlatformurl());

        request.setAttribute("consoleUrl", tmsCounterNacosConfig.getConsoleUrl());

        ModelAndView mv = new ModelAndView("html/index");
        return mv;
    }

    @RequestMapping("tmscounter/indexparam.htm")
    public ModelAndView indexparam(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) request.getSession().getAttribute(TmsCounterConstant.SESSION_OPERATORINFO);
        if (operatorInfoCmd == null) {
            operatorInfoCmd = new OperatorInfoCmd();
            operatorInfoCmd.setOperatorNo("admin");
        }
        SessionUtil.setValue(TmsCounterConstant.SESSION_OPERATORINFO, operatorInfoCmd, request);
        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("code", TmsCounterResultEnum.SUCC.getCode());
        rst.put("desc", TmsCounterResultEnum.SUCC.getDesc());
        WebUtil.write(response, rst);
        return null;
    }

}
