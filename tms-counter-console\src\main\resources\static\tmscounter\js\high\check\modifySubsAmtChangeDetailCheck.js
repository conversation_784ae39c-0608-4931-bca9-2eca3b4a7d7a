/**
 * 初始化页面事件
 */

$(function () {
    // 订单信息查询
    var viewType = CommonUtil.getParam("viewType");
    //初始化按钮
    ModifySubsAmtChangeDetail.initBtn(viewType);

    // 查询订单
    CounterCheck.queryCounterDealOrder(viewType, ModifySubsAmtChangeDetail.modifyDataCallBack, null);
});

var ModifySubsAmtChangeDetail = {
        initBtn: function (viewType) {

            if ("1" === viewType) {
                //审核驳回
                $("#checkCacelBtn").on('click', function () {
                    CounterCheck.confirm("2");  //checkType  1-审核通过 3-审核驳回 5-修改4-作废
                });

                //审核通过
                $("#checkConfirmBtn").on('click', function () {
                    CounterCheck.confirm("1");  //checkType  1-审核通过 3-审核驳回 5-修改4-作废
                });

                // 返回
                $("#checkBackBtn").on("click", function () {
                    parent.layer.closeAll();
                });

            } else {
                //隐藏复核信息
                $("#checkResult").hide();
            }
            //查看
            if ("0" === viewType) {
                CounterCheck.initBtn(viewType);
            }
        },

        modifyDataCallBack: function (data) {
            console.log(JSON.stringify(data));
            var bodyData = data.body || {};
            CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};//柜台订单
            ModifySubsAmtChangeDetail.querySubsAmtChangeDetail();
        },

        querySubsAmtChangeDetail: function () {
            var dealAppNo = CommonUtil.getParam("dealAppNo");
            if (isEmpty(dealAppNo)) {
                showMsg("申请单号不存在");
                return false;
            }
            var uri = TmsCounterConfig.QUERY_SUBS_AMT_CHANGE_DETAIL || "";
            var reqparamters = {};
            reqparamters.dealAppNo = dealAppNo;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
            CommonUtil.ajaxAndCallBack(paramters, ModifySubsAmtChangeDetail.buildOrderInfo);
        },


        /**
         *渲染产品信息查询结果
         */
        buildOrderInfo: function (data) {
            var bodyData = data.body || {};
            var subsAmtInfo = bodyData.detail || [];
            var oldSubsAmt = CommonUtil.formatData(subsAmtInfo.oldSubsAmt, '--');
            var newSubsAmt = CommonUtil.formatData(subsAmtInfo.newSubsAmt, '--');
            var appendHtml = '';
            $("#layerrs").empty();
            appendHtml =
                '<tr className="text-c">' +
                '<td>基金代码:</td>' +
                '<td id="fundCode">' + formatData(subsAmtInfo.fundCode) + '</td>' +
                '<td>基金简称:</td>' +
                '<td id="fundName">' + formatData(subsAmtInfo.fundName) + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>原认缴金额:</td>' +
                '<td id="subsAmt">' + oldSubsAmt + '</td>' +
                '<td>原认缴金额(大写):</td>' +
                '<td id="subsAmtUp">' + CommonUtil.amtUpper(oldSubsAmt) + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>持仓份额:</td>' +
                '<td id="balanceVol">' + formatData(subsAmtInfo.balanceVol) + '</td>' +
                '<td>调整后认缴金额:</td>' +
                '<td id="newSubsAmt">' + newSubsAmt + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>调整金额:</td>' +
                '<td id="diffSubsAmt">' + ModifySubsAmtChangeDetail.getChangeAmt(oldSubsAmt, newSubsAmt) + '</td>' +
                '<td>调整后认缴金额(大写):</td>' +
                '<td id="newSubsAmtUp">' + CommonUtil.amtUpper(newSubsAmt) + '</td>' +

                '<tr className="text-c">' +
                '<td>下单日期:</td>' +
                '<td id="appDt">' + subsAmtInfo.appDt + '</td>' +
                '<td>下单时间:</td>' +
                '<td id="appTime">' + subsAmtInfo.appTime + '</td>' +
                '<td> </tr>';
            $("#layerrs").append(appendHtml);
        },

        getChangeAmt: function (oldAmt, newAmt) {
            if (oldAmt === "--" || oldAmt === "") {
                return newAmt;
            }
            if (newAmt === "" || newAmt === "--") {
                return "--"
            }
            return newAmt - oldAmt;
        },
    }
;