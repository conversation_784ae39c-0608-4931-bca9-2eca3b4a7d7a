/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileResponse;
import com.howbuy.common.date.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.finonline.facade.query.cashintransit.QueryCashInTransitDetailFacade;
import com.howbuy.finonline.facade.query.cashintransit.QueryCashInTransitDetailRequest;
import com.howbuy.finonline.facade.query.cashintransit.QueryCashInTransitDetailResponse;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.bean.CustBankModel;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.QueryFundInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.vo.CustIntransAssetVo;
import com.howbuy.tms.counter.controller.vo.QueryBankCardInfo;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;

/**
 * @description:用户资产处理
 */
@Controller
public class CustAssetHandleController {
    
    @Autowired
    private QueryCashInTransitDetailFacade queryCashInTransitDetailFacade;

    @Autowired
    private QueryFundInfoOuterService queryFundInfoOuterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    
    @Autowired
    private QueryAllBankCardInfoOuterService queryAllBankCardInfoOuterService;

    @RequestMapping("/tmscounter/queryintransasset.htm")
    public ModelAndView queryIntransAsset(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //返回结果
        CustIntransAssetVo res = new CustIntransAssetVo();
        String cpAcctNo = request.getParameter("cpAcctNo");
        String bankAcct = request.getParameter("bankAcct");
        String txAcctNo = request.getParameter("txAcctNo");

        //如果资金账号为空，通过银行卡号查询资金账号
        if(StringUtil.isEmpty(cpAcctNo)){
            QueryBankCardInfoResponse queryRes = tmsCounterOutService.queryBankCardInfo(txAcctNo,bankAcct);
            if(queryRes!= null){
                cpAcctNo = queryRes.getCpAcctNo();
            }
        }

        QueryAllBankCardInfoContext ctx = new QueryAllBankCardInfoContext();
        ctx.setTxAcctNo(txAcctNo);
        ctx.setDisCode(TradeConstant.HOWBUY_DIS_CODE);
        Map<String, CustBankModel> custBankMap = queryAllBankCardInfoOuterService.queryCustBankMap(ctx);
        
        //调用资金
        QueryCashInTransitDetailRequest queryReq = new QueryCashInTransitDetailRequest();
        queryReq.setCpAcctNo(cpAcctNo);
        QueryCashInTransitDetailResponse queryRes =  queryCashInTransitDetailFacade.execute(queryReq);

        //资产明细
        List<CustIntransAssetVo.TransitDetailVo> detailVoList = new ArrayList<>();

        if(null != queryRes && CollectionUtils.isNotEmpty(queryRes.getDetailList())){
            for(QueryCashInTransitDetailResponse.TransitDetailPo po : queryRes.getDetailList()){
                CustIntransAssetVo.TransitDetailVo vo = new CustIntransAssetVo.TransitDetailVo();
                BeanUtils.copyProperties(po,vo);
                CustBankModel custBankModel = custBankMap.get(po.getCpAcctNo());
                if(custBankModel != null) {
                    vo.setBankAcct(custBankModel.getBankAcctMask());
                }
                
                //调用产品中心查询基金信息
                FundInfoAndNavBean fundInfoAndNavBean = queryFundInfoOuterService.getFundInfoAndNav(vo.getProdCode(), DateUtil.getAppDt());
                if(null != fundInfoAndNavBean){
                    vo.setFundName(fundInfoAndNavBean.getFundName());
                    vo.setProductChannel(fundInfoAndNavBean.getProductChannel());
                }
                detailVoList.add(vo);
            }
        }
        res.setDetailList(detailVoList);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("batchStatList", res);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/querycustbankacctstat.htm")
    public ModelAndView queryBankAcctStat(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String txAcctNo = request.getParameter("txAcctNo");
        String bankAcct = request.getParameter("bankAcct");

        QueryBankCardInfoResponse queryRes = tmsCounterOutService.queryBankCardInfo(txAcctNo,bankAcct);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        QueryBankCardInfo queryBankCardInfo = new QueryBankCardInfo();
        if(queryRes != null) {
            //由于账户中心接口不返回注销的银行卡信息，所以通过returnCode等于成功，银行号状态等于空来判断是否销卡
            if(TradeConstant.HB_TRADE_SUCC_NEW.equals(queryRes.getReturnCode()) && StringUtil.isEmpty(queryRes.getBankAcctStatus())){
                queryRes.setBankAcctStatus("2");
            }
            queryBankCardInfo.setBankAcct(queryRes.getBankAcctMask());
        	queryBankCardInfo.setBankAcctCipher(queryRes.getBankAcctCipher());
        	queryBankCardInfo.setBankAcctDigest(queryRes.getBankAcctDigest());
        	queryBankCardInfo.setBankAcctMask(queryRes.getBankAcctMask());
        	queryBankCardInfo.setBankAcctName(queryRes.getBankAcctName());
        	queryBankCardInfo.setBankAcctStatus(queryRes.getBankAcctStatus());
        	queryBankCardInfo.setBankAcctVrfyStat(queryRes.getBankAcctVrfyStat());
        	queryBankCardInfo.setBankCode(queryRes.getBankCode());
        	queryBankCardInfo.setBankName(queryRes.getBankName());
        	queryBankCardInfo.setBankRegionCode(queryRes.getBankRegionCode());
        	queryBankCardInfo.setBankRegionName(queryRes.getBankRegionName());
        	queryBankCardInfo.setCityCode(queryRes.getCityCode());
        	queryBankCardInfo.setCpAcctNo(queryRes.getCpAcctNo());
        	queryBankCardInfo.setMobileBank(queryRes.getMobileBankMask());
        	queryBankCardInfo.setMobileBankCipher(queryRes.getBankAcctCipher());
        	queryBankCardInfo.setMobileBankDigest(queryRes.getMobileBankDigest());
        	queryBankCardInfo.setMobileBankMask(queryRes.getMobileBankMask());
        	queryBankCardInfo.setMobileVrfyStat(queryRes.getMobileVrfyStat());
        	queryBankCardInfo.setPaySign(queryRes.getPaySign());
        	queryBankCardInfo.setPaySignDt(queryRes.getPaySignDt());
        	queryBankCardInfo.setProvCode(queryRes.getProvCode());
        	queryBankCardInfo.setTxAcctNo(queryRes.getTxAcctNo());
        }
        
        //银行卡明文
        if(!StringUtil.isEmpty(queryBankCardInfo.getCpAcctNo())){
            QueryBankAcctSensitiveInfoResponse bankAcctSensitiveInfo = tmsCounterOutService.queryBankAcctSensitiveInfoFacade(txAcctNo, queryBankCardInfo.getCpAcctNo());
            if(bankAcctSensitiveInfo != null && bankAcctSensitiveInfo.getBankAcctSensitiveInfo() != null && !StringUtil.isEmpty(bankAcctSensitiveInfo.getBankAcctSensitiveInfo().getBankAcct())) {
            	queryBankCardInfo.setBankAcct(bankAcctSensitiveInfo.getBankAcctSensitiveInfo().getBankAcct());
            }
            
            QueryCustMobileResponse queryCustMobile = tmsCounterOutService.queryCustMobileFacade(txAcctNo, queryBankCardInfo.getCpAcctNo());
            if(queryCustMobile != null && queryCustMobile.getCustMobile() != null) {
            	 Map<String,String> bankMobileMap = queryCustMobile.getCustMobile().getBankMobileMap();
            	 if(bankMobileMap != null && bankMobileMap.get(queryBankCardInfo.getCpAcctNo()) != null) {
            		 String mobileBank = bankMobileMap.get(queryBankCardInfo.getCpAcctNo());
            		 queryBankCardInfo.setMobileBank(mobileBank);
            	 }
            }
        }

        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("batchStatList", queryBankCardInfo);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
}

