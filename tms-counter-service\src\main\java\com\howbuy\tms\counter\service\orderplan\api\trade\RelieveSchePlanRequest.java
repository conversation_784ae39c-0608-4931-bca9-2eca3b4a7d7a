/**
 *Copyright (c) 2020, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.service.orderplan.api.trade;

import java.util.List;

import com.howbuy.tms.counter.service.orderplan.api.BaseRequest;

/**
 * 
 * @description:解除定投合约（销户||换卡）
 * <AUTHOR>
 * @date 2020年2月17日 下午2:42:44
 * @since JDK 1.6
 */

public class RelieveSchePlanRequest extends BaseRequest {
    private static final long serialVersionUID = 1L;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 交易密码
     */
    private String txPassword;
    
    /**
     * 资金账号
     */
    private List<String> cpAcctNos;

    public List<String> getCpAcctNos() {
        return cpAcctNos;
    }

    public void setCpAcctNos(List<String> cpAcctNos) {
        this.cpAcctNos = cpAcctNos;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    @Override
    public String getTxPassword() {
        return txPassword;
    }

    @Override
    public void setTxPassword(String txPassword) {
        this.txPassword = txPassword;
    }

}
