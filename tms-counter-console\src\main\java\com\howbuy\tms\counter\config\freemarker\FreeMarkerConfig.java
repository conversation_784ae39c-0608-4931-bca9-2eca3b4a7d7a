package com.howbuy.tms.counter.config.freemarker;

import com.howbuy.web.tag.freemarker.CacheTag;
import com.howbuy.tms.counter.freemarker.ShiroTagFreeMarkerConfigurer;
import com.howbuy.web.freemarker.method.StaticVersionMethod;
import com.howbuy.web.html.FreemarkerTemplate;
import com.howbuy.web.html.HtmlGenerator;
import com.howbuy.web.html.StaticGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.ui.freemarker.FreeMarkerConfigurationFactoryBean;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import org.springframework.web.servlet.view.freemarker.FreeMarkerView;
import org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xin.jiang.cn
 * @date: 2023年09月18日 11:23:27
 * @description:
 */
@Configuration
public class FreeMarkerConfig {
    
    @Bean
    public FreeMarkerConfigurer freeMarkerConfigurer(CacheTag fmCacheTag,
                                                     HostMethod fmHostMethod,
                                                     StaticVersionMethod fmStaticVersionMethod){
        FreeMarkerConfigurer configurer = new ShiroTagFreeMarkerConfigurer();
        configurer.setTemplateLoaderPaths(
                "file:@{common.includeProjectPath}",
                "classpath:static/WEB-INF/"
        );
        configurer.setConfigLocation(new ClassPathResource("context/freemarker.properties"));
        

        Map<String, Object> variables = new HashMap<>();
        variables.put("cache", fmCacheTag);
        variables.put("gethost", fmHostMethod);
        variables.put("version", fmStaticVersionMethod);
        configurer.setFreemarkerVariables(variables);
        
        return configurer;
    }

    @Bean
    public FreeMarkerConfigurationFactoryBean freemarkerConfiguration(
            CacheTag fmCacheTag,
            HostMethod fmHostMethod,
            StaticVersionMethod fmStaticVersionMethod) {

        FreeMarkerConfigurationFactoryBean config = new FreeMarkerConfigurationFactoryBean();
        config.setTemplateLoaderPaths(
                "file:@{common.includeProjectPath}",
                "classpath:static/WEB-INF/"
        );
        config.setConfigLocation(new ClassPathResource("context/freemarker.properties"));

        Map<String, Object> variables = new HashMap<>();
        variables.put("cache", fmCacheTag);
        variables.put("gethost", fmHostMethod);
        variables.put("version", fmStaticVersionMethod);

        config.setFreemarkerVariables(variables);

        return config;
    }

    @Bean
    public CacheTag fmCacheTag() {
        return new CacheTag();
    }

    @Bean
    public StaticVersionMethod fmStaticVersionMethod() {
        return new StaticVersionMethod();
    }

    @Bean
    public FreemarkerTemplate freemarkerTemplate(freemarker.template.Configuration freemarkerConfiguration){
        FreemarkerTemplate template = new FreemarkerTemplate();
        template.setCharset("UTF-8");
        template.setConfiguration(freemarkerConfiguration);
        return template;
    }
    
    @Bean
    public HtmlGenerator htmlGenerator(FreemarkerTemplate freemarkerTemplate){
        HtmlGenerator htmlGenerator = new HtmlGenerator();
        htmlGenerator.setTemplate(freemarkerTemplate);
        return htmlGenerator;
    }
    
    @Bean
    public StaticGenerator staticGenerator(freemarker.template.Configuration freemarkerConfiguration){
        StaticGenerator staticGenerator = new StaticGenerator();
        staticGenerator.setConfiguration(freemarkerConfiguration);
        staticGenerator.setEncoding("UTF-8");
        return staticGenerator;
    }
    
    @Bean
    public FreeMarkerViewResolver freeMarkerViewResolver(){
        FreeMarkerViewResolver viewResolver = new FreeMarkerViewResolver();
        viewResolver.setViewClass(FreeMarkerView.class);
        viewResolver.setCache(false);
        //viewResolver.setPrefix("classpath:static/WEB-INF/view");
        viewResolver.setSuffix(".ftl");
        viewResolver.setContentType("text/html; charset=UTF-8");
        viewResolver.setExposeSpringMacroHelpers(true);
        viewResolver.setRequestContextAttribute("rc");
        viewResolver.setOrder(0);
        return viewResolver;
    }

}
