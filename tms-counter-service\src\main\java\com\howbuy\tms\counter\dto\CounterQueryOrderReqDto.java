/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.counter.dto.base.BaseRequestDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:(柜台查询订单请求)
 * <AUTHOR>
 * @date 2017年3月30日 下午6:28:47
 * @since JDK 1.6
 */
public class CounterQueryOrderReqDto extends BaseRequestDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 297838474257359068L;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 
     * /** 交易码
     */
    private List<String> txCodeList;

    /**
     * 交易码
     */
    private String txCode;
    /**
     * /** 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;
    /**
     * 审核状态,0-未审核；1-审核通过；3-驳回；4-作废；
     */
    private List<String> checkFlagLsit;
    /**
     * 申请状态，0-未申请，1-申请通过，2-申请失败
     */
    private String appFlag;

    /**
     * 查询开始时间
     */
    private Date beginDtm;

    /**
     * 查询结束时间
     */
    private Date endDtm;

    /**
     * 证件号
     */
    private String idNo;
    /**
     * 预申请单号
     */
    private String dealAppNo;
    /**
     * 客户名
     */
    private String custName;
    /**
     * ta日期
     */
    private String tradeDt;
    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 产品通道 3-群济私募 5-好买公募 6-好买高端公募
     */
    private String productChannel;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    private String appDt;

    /**
     * 客户订单号
     */
    private String dealNo;
    /**
     * ta代码
     */
    private String taCode;
    /**
     * 银行卡号
     */
    private String bankAcct;
    /**
     * 申请T日
     */
    private String beginTrandeDtm;
    /**
     * 结束T日
     */
    private String endTradeDtm;

    /**
     * 资产范围
     */
    private String assetScope;
    
    /**
     * 支付状态
     */
    private List<String> paymemtTypes;
    
    /**
     * 后台业务代码
     */
    private List<String> busiCodes;
    
    /**
     * 申请TA日开始
     */
    private String taTradeDtStart;
    /**
     * 申请TA日结束
     */
    private String taTradeDtEnd;
    
    /**
     * 修改后的折扣费率
     */
    private BigDecimal afterDiscountRate;
    /**
     * 代销场检授权
     */
    private String isHBJGAuth= YesOrNoEnum.NO.getCode();


    /**
     * 产品类别
     */
    private List<String> productClassList;

    public List<String> getProductClassList() {
        return productClassList;
    }

    public void setProductClassList(List<String> productClassList) {
        this.productClassList = productClassList;
    }

    public String getIsHBJGAuth() {
        return isHBJGAuth;
    }

    public void setIsHBJGAuth(String isHBJGAuth) {
        this.isHBJGAuth = isHBJGAuth;
    }

    public BigDecimal getAfterDiscountRate() {
		return afterDiscountRate;
	}

	public void setAfterDiscountRate(BigDecimal afterDiscountRate) {
		this.afterDiscountRate = afterDiscountRate;
	}

	public List<String> getBusiCodes() {
		return busiCodes;
	}

	public void setBusiCodes(List<String> busiCodes) {
		this.busiCodes = busiCodes;
	}

	public List<String> getPaymemtTypes() {
		return paymemtTypes;
	}

	public void setPaymemtTypes(List<String> paymemtTypes) {
		this.paymemtTypes = paymemtTypes;
	}

	public String getTaTradeDtStart() {
		return taTradeDtStart;
	}

	public void setTaTradeDtStart(String taTradeDtStart) {
		this.taTradeDtStart = taTradeDtStart;
	}

	public String getTaTradeDtEnd() {
		return taTradeDtEnd;
	}

	public void setTaTradeDtEnd(String taTradeDtEnd) {
		this.taTradeDtEnd = taTradeDtEnd;
	}

	public List<String> getCheckFlagLsit() {
        return checkFlagLsit;
    }

    public void setCheckFlagLsit(List<String> checkFlagLsit) {
        this.checkFlagLsit = checkFlagLsit;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public List<String> getTxCodeList() {
        return txCodeList;
    }

    public void setTxCodeList(List<String> txCodeList) {
        this.txCodeList = txCodeList;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }

    public Date getBeginDtm() {
        return beginDtm;
    }

    public void setBeginDtm(Date beginDtm) {
        this.beginDtm = beginDtm;
    }

    public Date getEndDtm() {
        return endDtm;
    }

    public void setEndDtm(Date endDtm) {
        this.endDtm = endDtm;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBeginTrandeDtm() {
        return beginTrandeDtm;
    }

    public void setBeginTrandeDtm(String beginTrandeDtm) {
        this.beginTrandeDtm = beginTrandeDtm;
    }

    public String getEndTradeDtm() {
        return endTradeDtm;
    }

    public void setEndTradeDtm(String endTradeDtm) {
        this.endTradeDtm = endTradeDtm;
    }

    public String getAssetScope() {
        return assetScope;
    }

    public void setAssetScope(String assetScope) {
        this.assetScope = assetScope;
    }
}
