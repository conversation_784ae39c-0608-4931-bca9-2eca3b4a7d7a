<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.tms</groupId>
		<artifactId>tms-counter</artifactId>
		<version>4.8.50-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>tms-counter-service</artifactId>
	<packaging>jar</packaging>
	<name>tms-counter-service</name>
	
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
				<!-- 排除自带的logback依赖 -->
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-dependencies-zookeeper</artifactId>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
		</dependency>



		<!-- howbuy middleware -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>nacos-client</artifactId>
					<groupId>com.alibaba.nacos</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-asyncqueue</artifactId>
		</dependency>

		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		
		<!-- log4j2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-counter-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-counter-common</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>javax.servlet-api</artifactId>
					<groupId>javax.servlet</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-client</artifactId>
		</dependency>
		     
		<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-validator</artifactId>
				<exclusions>
					<exclusion>
						<groupId>com.howbuy.fbs</groupId>
						<artifactId>fbs-online-facade</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>dubbo</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>acc-center-facade</artifactId>
						<groupId>com.howbuy.acccenter</groupId>
					</exclusion>
				</exclusions>
		</dependency>
			
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>order-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>robot-order-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>regular-order-center-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
			<artifactId>product-center-model</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-beans</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
	    	<artifactId>product-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>batch-center-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>regular-batch-center-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-batch-center-client</artifactId>
		</dependency>
		<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-outerservice</artifactId>
				<exclusions>
					<exclusion>
						<artifactId>zkclient</artifactId>
						<groupId>com.101tec</groupId>
					</exclusion>
					<exclusion>
						<artifactId>dubbo</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.cc</groupId>
			<artifactId>cim-web-client</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito</artifactId>
			<version>1.6.6</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>1.6.6</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.powermock</groupId>
					<artifactId>powermock-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.powermock</groupId>
					<artifactId>powermock-reflect</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.javassist</groupId>
					<artifactId>javassist</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-log-pattern</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-message-service</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-message-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-message-amq-2</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>activemq-all</artifactId>
					<groupId>org.apache.activemq</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-message-rocket-2</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

</project>