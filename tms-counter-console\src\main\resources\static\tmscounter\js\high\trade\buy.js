/**

 \*购买
 *<AUTHOR>
 *@date 2017-03-28 10:39
 */
$(function () {
    Buy.init();
});

var Buy = {
    /**
     * 初始化参数
     */
    initData: function () {
        Buy.custInfo = {};//客户基信息
        Buy.custBanks = [];//客户绑定银行卡
        Buy.fundInfo = {};//产品信息
        Buy.appointment = {};//投顾预约信息
        Buy.selectAppointment = {};//选中的投顾预约信息
        Buy.buyPreValidRst = {};//购买校验结果
        QueryHighProduct.fundInfo = {};//产品信息
        Buy.confirmParams = {};//购买确认参数
        Buy.clicked = "0";// 防止重复点击 0-可点击 1-不可点击
        Buy.riskFlag = "0"; // 风险确认标识
        // 好臻认缴金额信息
        Buy.hzSubscribeAmtInfo = {};
        // url 参数
        Buy.urlParams = CommonUtil.getParamJson() || {};
    },

    init: function () {
        /**
         * 初始化参数
         */
        Buy.initData();

        /**
         * 初始化按钮事件
         */
        Buy.initBtn();

        // 初始化CRM参数数据
        OnLineOrderFile.initCrmUrl(Buy.urlParams, Buy.initCustQuery);

        document.getElementById("subsAmt").addEventListener("change", Buy.calFee);
    },


    initCustQuery: function (hboneNo) {
        /**
         * 初始化数据
         */
        Buy.initData();

        /**
         * 初始化购买表单
         */
        Buy.initBuyDealForm();

        /**
         * 初始化预约信息列表
         */
        Buy.initAppointmentList();

        /**
         * 查询客户基本信息
         */
        HighCustInfo.queryCustInfo(hboneNo);

        /**
         * 绑定客户选择事件
         */
        setTimeout(Buy.buyCustInfoBind(), 2000);
    },


    /**
     * 初始化按钮事件
     */
    initBtn: function () {
        /**
         * 确认购买
         */
        $("#confimBuyBtn").on('click', function () {
            Buy.confirm();
        });

        /**
         * 双击客户号查询客户信息
         */
        $("#custNo").on('dblclick', function () {
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        /**
         * 查询客户基本信息
         */
        $("#queryCustInfoBtn").on('click', function () {
            Buy.initCustQuery();
        });

        /**
         * 双击查询产品信息
         */
        $("#fundCode").on('dblclick', function () {
            QueryHighProdInfoSubPage.selectProductCode($(this));
        });

        $("#fundCode").on('blur', function () {

            var fundCodeDisabled = $("#fundCode").attr("disabled");
            if (fundCodeDisabled === 'disabled' || fundCodeDisabled === true) {
                return;
            }

            var fundCode = $("#fundCode").val();
            var appDt = $("#appDt").val();
            var appTm = $("#appTm").val();
            var busiType = "0";//购买

            Buy.queryFundInfo(fundCode);//查询产品信息
            Buy.queryHighproductAppointinfo(fundCode, appDt, appTm, busiType);//查询预约开放日历信息

        });

        /**
         * 查询基金基本信息
         */
        $(".searchIcon").on('blur click', function () {

            var fundCodeDisabled = $("#fundCode").attr("disabled");
            if (fundCodeDisabled === 'disabled' || fundCodeDisabled === true) {
                return;
            }

            var fundCode = $("#fundCode").val();
            var appDt = $("#appDt").val();
            var appTm = $("#appTm").val();
            var busiType = "0";//购买
            var invstType = Buy.custInfo.invstType || '';//客户类型
            Buy.queryFundInfo(fundCode);//查询产品信息
            Buy.queryHighproductAppointinfo(fundCode, appDt, appTm, busiType, invstType);//查询预约开放日历信息

        });

        //初始化投顾列表
        var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
        $(".selectconsCode").html(selectConsCodesHtml);

        //是否代理
        $(".selectAgened").change(function () {
            var selectAgened = $(this).val();
            Buy.agenedChange(selectAgened);

        });
        // 金额变化计算手续费
        $("#applyAmount").change(function () {
            var appAmount = CommonUtil.unFormatAmount($(this).val());
            // 产品最高申请金额校验
            var maxAppAmt = Buy.fundInfo.maxAppAmt;
            if (appAmount > maxAppAmt) {
                showMsg("申请金额[" + appAmount + "]大于产品最高申请金额[" + maxAppAmt + "]");
                return false;
            }
            Buy.calFundBuyFee(null);
        });

        // 金额格式化
        $("#applyAmount").on("blur", function () {
            if ("HZ000N001" === Buy.fundInfo.disCode) {
                if (Buy.fundInfo.peDivideCallFlag !== '1') {
                    $('#subsAmt').val(CommonUtil.formatAmount($("#applyAmount").val()));
                }
            }
            Buy.calculatePayRatioAmount($("#subsAmt").val());
            Buy.calFundBuyFee(null);
            Buy.appAmtUpper($("#applyAmount").val());

        });

        // 认缴金额格式化
        $("#subsAmt").on("blur", function () {
            var subsAmt = $("#subsAmt").val();
            if (!CommonUtil.isEmpty(subsAmt)) {
                $("#subsAmt").val(CommonUtil.formatAmount(subsAmt));
                Buy.calculatePayRatioAmount(subsAmt);
                Buy.calFundBuyFee(null);
            }
            Buy.appAmtUpper(str);
        });

        var lastValue = '';
        //大小写转换
        $('.applyAmount').on('keyup focus', function () {
            var str = $(this).val() || '';
            // 还原格式化金额
            str = CommonUtil.unFormatAmount(str);
            if (!CommonUtil.validFloat(str)) {
                $(this).val(lastValue);
                return false;
            }
            ;

            Buy.appAmtUpper(str);
            lastValue = str;
        });

        //银行卡选择事件
        $('#selectBank').change(function () {
            var applyAmount = $("#applyAmount").val() || '';
            applyAmount = CommonUtil.unFormatAmount(applyAmount);
            if (CommonUtil.isEmpty(applyAmount)) {
                return false;
            }
            Buy.calFundBuyFee(null);
        });

        //修改折扣，计算手续费
        var lastDiscountRate = '';
        $("#discountRate").on('keyup blur', function () {

            var discountRate = $("#discountRate").val();
            if (CommonUtil.isEmpty(discountRate)) {
                lastDiscountRate = '';
                return false;
            }
            //小数正则
            var reg = new RegExp("^(([0]{1}\\.[0-9]{0,2})|([0-1]{1}))$");

            if (reg.test(discountRate)) {
                lastDiscountRate = discountRate;
            } else {
                $("#discountRate").val(lastDiscountRate);
                return false;
            }
            Buy.calFundBuyFee(null);
        });
    },
    /**
     * 申请金额转大写
     **/
    appAmtUpper: function (str) {
        str = str.replace(/\,/g, '');
        var re = /([0-9]+\.[0-9]{2})[0-9]*/;
        str = str.replace(re, "$1");
        var convertStr = CommonUtil.digit_uppercase(str);
        $('#convertAmtId').html(convertStr);
    },

    /**
     * 是否代理
     */
    agenedChange: function (selectAgened) {

        if ("1" === selectAgened) {
            $("#transactorInfoForm").show();

        } else {
            $("#transactorInfoForm").hide();
        }
    },
    /**
     * 初始化预约信息列表
     */
    initAppointmentList: function () {
        // 预约列表清空
        $("#rsList").html('');
    },

    /**
     * 初始化购买订单表单
     */
    initBuyDealForm: function (excludeInputArr) {
        // 初始化购买formInput
        CommonUtil.cleanForm('buyConfirmForm', excludeInputArr);

        // 折扣可输入
        $('#discountRate').removeAttr("readonly", "readonly");

        // 产品代码可输入
        $('#fundCode').removeAttr("disabled", "disabled");

        // 认缴金额可输入
        $('#subsAmt').removeAttr("readonly", "readonly");

        // 初始化只读文本
        CommonUtil.initReadeText('readText');

        // 初始化产品信息
        Buy.fundInfo = {};

        // 初始化投顾预约信息
        Buy.selectAppointment = {};

        // 初始化投顾预约信息
        Buy.appointment = {};

        // 初始化产品信息
        QueryHighProduct.fundInfo = {};

        //初始化材料信息
        OnLineOrderFile.clearOrder();
    },

    /**
     *
     * @Description 绑定预约信息选择
     *
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 15:58
     **/
    selectAppointBind: function () {
        $(".selectAppointmentInfo").change(function () {
            Buy.selectAppointmentInfo();
        });
    },

    /**
     * 购买客户信息绑定事件
     */
    buyCustInfoBind: function () {

        $(".selectcust").click(function () {
            $(this).attr('checked', 'checked').siblings().removeAttr('checked');
            // 查询产品认缴金额信息
            var selectIndex = $(this).attr("index");
            Buy.custInfo = HighCustInfo.custList[selectIndex] || {};
            var fundCodeValue = $("#fundCode").val() || '';
            if ("HZ000N001" === Buy.fundInfo.disCode) {
                Buy.queryHzSubscribeAmtInfo(fundCodeValue);
            }
            Buy.queryCustBankInfo(Buy.custInfo.custNo, Buy.custInfo.disCode);


            // 购买
            var tradeType = "1,2";
            Appoint.queryAppointmentInfo(Buy.custInfo.custNo, tradeType, Buy.custInfo.disCode);

            // 绑定预约信息选择
            setTimeout(Buy.selectAppointBind(), 2000);

            // 机构用户
            if ('0' === Buy.custInfo.invstType || '2' === Buy.custInfo.invstType) {
                // 查询经办人信息
                HighCustInfo.queryCustTransInfo(Buy.custInfo.custNo, Buy.custInfo.disCode);
                // 代理
                $(".selectAgened").val('1');
                $("#transactorInfoForm").show();
            } else {
                $("#transactorInfoForm").hide();
            }

            if (CommonUtil.isEmpty(Buy.custInfo.collectProtocolMethod) || '3' === Buy.custInfo.collectProtocolMethod) {
                // 3-系统默认回款到储蓄罐
                CommonUtil.layer_alert("客户未签署回款协议，请联系客户签署");
            }
        });
    },


    /**
     * 计算支付金额
     * @param discountRate 实际折扣率
     */
    calFundBuyFee: function (productCode) {
        var fundCode = $("#fundCode").val();
        if (!CommonUtil.isEmpty(productCode)) {
            fundCode = productCode;
        }
        if (CommonUtil.isEmpty(fundCode)) {
            $("#applyAmount").val();
            return false;
        }

        // 校验是否绑卡
        var validBindFlag = Buy.valideCustBindBank();
        if (!validBindFlag) {
            return false;
        }

        //选中银行卡
        var bankCode = $('#selectBank').find('option:selected').attr('bankCode');
        if (CommonUtil.isEmpty(bankCode) && Buy.custBanks.length > 0) {
            bankCode = Buy.custBanks[0].bankCode;
        }

        var applyAmount = $("#applyAmount").val() || '';
        applyAmount = CommonUtil.unFormatAmount(applyAmount);
        if (CommonUtil.isEmpty(applyAmount)) {
            CommonUtil.layer_tip("请填写净申请金额");
            return false;
        }
        var appAmt = Buy.selectAppointment.appAmt;//预约申请金额
        var custInfoForm = JSON.stringify(Buy.custInfo);
        if (CommonUtil.isEmpty(custInfoForm)) {
            showMsg("请先选择客户信息");
            return false;
        }

        var reqparamters = {};
        reqparamters.custInfoForm = custInfoForm;
        reqparamters.fundCode = fundCode;
        reqparamters.applyAmount = applyAmount;

        // 计算手续费
        Buy.calFee();
    },
    /**
     * 计算手续费
     */
    calFee: function () {

        var uri = TmsCounterConfig.CAL_FUND_BUY_FEE_URL || {};
        var fundCode = $("#fundCode").val();
        if (CommonUtil.isEmpty(fundCode)) {
            $("#applyAmount").val();
            return false;
        }

        //选中银行卡
        var bankCode = $('#selectBank').find('option:selected').attr('bankCode');
        if (CommonUtil.isEmpty(bankCode) && Buy.custBanks.length > 0) {
            bankCode = Buy.custBanks[0].bankCode;
        }

        var applyAmount = $("#applyAmount").val() || '';
        applyAmount = CommonUtil.unFormatAmount(applyAmount);
        if (CommonUtil.isEmpty(applyAmount)) {
            CommonUtil.layer_tip("请填写净申请金额");
            return false;
        }
        if (applyAmount === "--") {
            CommonUtil.layer_tip("请填写净申请金额");
            return false;
        }
        var custInfoForm = JSON.stringify(Buy.custInfo);
        if (CommonUtil.isEmpty(custInfoForm)) {
            showMsg("请先选择客户信息");
            return false;
        }

        var subsAmt = $("#subsAmt").val() || '';
        subsAmt = CommonUtil.unFormatAmount(subsAmt);
        if (subsAmt === "--") {
            subsAmt = '';
        }
        var buyConfirmForm = $("#buyConfirmForm").serializeObject();
        var discountRate = $("#discountRate").val();
        var reqparamters = {};
        reqparamters.custInfoForm = custInfoForm;
        reqparamters.fundCode = fundCode;
        reqparamters.appointmentDealNo = fundCode;
        reqparamters.bankCode = bankCode;
        reqparamters.disCode = Buy.fundInfo.disCode;
        reqparamters.appointmentDealNo = Buy.appointment.appointId
        reqparamters.applyAmount = applyAmount;
        reqparamters.subsAmt = subsAmt;
        reqparamters.feeRateMethod = Buy.hzSubscribeAmtInfo.feeRateMethod;
        reqparamters.isFirstPay = Buy.hzSubscribeAmtInfo.isFirstPay;
        reqparamters.discountRate = discountRate || '';
        reqparamters.queryDateStr=buyConfirmForm.appDt + '' + buyConfirmForm.appTm;
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, Buy.calFundBuyFeeCallBack);
    },

    calFundBuyFeeCallBack: function (data) {
        var bodyData = data.body || {};
        var respCode = data.code || '';
        var respDesc = data.desc || '';
        if (CommonUtil.isSucc(respCode)) {
            var respData = bodyData.respData || [];
            $("#applyAmountIncluFeeId").html(CommonUtil.formatAmount(respData.payAmt));
            $("#applyAmountIncluFee").val(respData.payAmt);
            $("#originalFeeRate").val(respData.feeRate);
            $("#discountRate").val(respData.discountRate);
            $("#feeRate").val(respData.feeRate);
            $("#feeId").html(CommonUtil.formatAmount(respData.fundBuyFee));
            Buy.fundInfo.fundBuyFee = respData.fundBuyFee;
        } else {
            CommonUtil.layer_tip(respDesc);
        }
    },

    /**
     * 购买确认预处理
     */
    buyConfirmPretreatment: function () {
        // 机构/产品用户提醒
        if ('0' === Buy.custInfo.invstType || '2' === Buy.custInfo.invstType) {
            if (Buy.custInfo.custName.indexOf("合伙") !== -1) {
                CommonUtil.layerConfrim("确认", "<div style='margin: 30px 20px 0 20px;'>请注意该客户是否需要穿透计算人数</div>", null, Buy.buyConfirmValid);
            } else {
                Buy.buyConfirmValid();
            }
        } else {
            Buy.buyConfirmValid();
        }
    },
    /**
     * @param btn 确认按钮
     * 购买确认校验
     */
    buyConfirmValid: function () {
        console.log("buyConfirmValid|start");

        var btn = "confimBuyBtn";
        CommonUtil.disabledBtn("confimBuyBtn");

        var buyConfirmForm = $("#buyConfirmForm").serializeObject();
        //用户校验
        if (CommonUtil.isEmpty(Buy.custInfo.custNo)) {
            CommonUtil.layer_tip("请先选择用户");
            CommonUtil.enabledBtn("confimBuyBtn");
            return false;
        }

        //购买表单参数校验
        var validRst = Valid.valiadateFrom($("#buyConfirmForm"));
        if (!validRst.status) {
            CommonUtil.layer_tip(validRst.msg);
            CommonUtil.enabledBtn(btn);
            return false;
        }


        //机构用户代理信息校验
        if ('0' === Buy.custInfo.invstType || '2' === Buy.custInfo.invstType) {
            var selectAgened = $(".selectAgened").val();
            if ("1" !== selectAgened) {
                CommonUtil.layer_tip("机构、产品用户必须选择代理人信息");
                CommonUtil.enabledBtn(btn);
                return false;
            }
        }

        //经办人信息校验
        if (othetInfoForm.agentFlag === '1') {
            var validTransactorRst = Valid.valiadateFrom($("#transactorInfoForm"));
            if (!validTransactorRst.status) {
                CommonUtil.layer_tip(validTransactorRst.msg);
                CommonUtil.enabledBtn(btn);
                return false;
            }

            if ('0' === transactorInfoForm.transactorIdType && !Valid.id_rule(transactorInfoForm.transactorIdNo)) {
                CommonUtil.layer_tip("经办人证件号格式不正确");
                CommonUtil.enabledBtn(btn);
                return false;
            }
        }

        var appAmount = CommonUtil.unFormatAmount($("#applyAmount").val());
        // 产品最高申请金额校验
        var maxAppAmt = Buy.fundInfo.maxAppAmt;
        if (appAmount > maxAppAmt) {
            CommonUtil.layer_tip("申请金额[" + appAmount + "]大于产品最高申请金额[" + maxAppAmt + "]");
            CommonUtil.enabledBtn(btn);
            return false;
        }

        //折扣率校验
        if (!CommonUtil.isEmpty(buyConfirmForm.discountRate) && !BuyValid.validDisCount(buyConfirmForm.discountRate)) {
            CommonUtil.layer_tip("申请折扣率错误");
            CommonUtil.enabledBtn(btn);
            return false;
        }


        //申请时间校验
        if (!Valid.highValiadTradeTime(buyConfirmForm.appTm)) {
            CommonUtil.layer_tip("请重新输入下单时间，时间范围为00:00:01-14:59:59");
            CommonUtil.enabledBtn(btn);
            return false;
        }

        if (CommonUtil.formatDateToStr(Buy.currDate, 'yyyyMMdd') > buyConfirmForm.appDt) {
            CommonUtil.layer_tip("申请日期不能在当前日期之前");
            CommonUtil.enabledBtn(btn);
            return false;
        }

        //用户预约信息选择校验
        var productCode = $("#fundCode").val();
        if (!Buy.validSelectAppoint(productCode)) {
            CommonUtil.layer_alert("请选择客户预约信息");
            CommonUtil.enabledBtn(btn);
            return false;
        }

        // 设置风险确认标识
        if ('1' === Buy.custInfo.investorType) {
            Buy.riskFlag = "1"; //专业投资者默认风险确认
        }
        // 专业投资者默认做了二次风险确认
        if ('1' !== Buy.custInfo.investorType && BuyValid.validRisk(Buy.custInfo.custRiskLevel, Buy.fundInfo.fundRiskLevel)) {
            Buy.riskFlagLayer("基金风险高于客户风险等级承受能力，请联系客户提供风险等级不匹配警示函",
                function () {
                    Buy.riskFlag = "1";
                    // 购买预校验参数对象
                    var buyPreValidParam = {
                        "txAcctNo": Buy.custInfo.custNo,
                        "cpAcctNo": buyConfirmForm.cpAcctNo,
                        "productCode": Buy.fundInfo.fundCode,
                        "appAmt": $("#applyAmountIncluFee").val(),
                        "disCode": Buy.custInfo.disCode,
                        "investorType": Buy.custInfo.investorType,
                        "invstType": Buy.custInfo.invstType,
                        "appointId": Buy.appointment.appointId,
                        "riskFlag": Buy.riskFlag
                    };

                    //购买预校验
                    var buyPreValidRst = Buy.buyPreValid(buyPreValidParam) || {};

                    Buy.buyPreValidRst = buyPreValidRst || {};
                    if (!buyPreValidRst.status) {
                        CommonUtil.enabledBtn(btn);
                        return false;
                    }
                    //kyc状态
                    Buy.kycFlagConfirm();
                },
                function () {
                    return false;
                });
        } else {
            // 购买预校验参数对象
            var buyPreValidParam = {
                "txAcctNo": Buy.custInfo.custNo,
                "cpAcctNo": buyConfirmForm.cpAcctNo,
                "productCode": Buy.fundInfo.fundCode,
                "appAmt": $("#applyAmountIncluFee").val(),
                "disCode": Buy.custInfo.disCode,
                "investorType": Buy.custInfo.investorType,
                "invstType": Buy.custInfo.invstType,
                "appointId": Buy.appointment.appointId,
                "riskFlag": Buy.riskFlag
            };

            //购买预校验
            var buyPreValidRst = Buy.buyPreValid(buyPreValidParam) || {};

            Buy.buyPreValidRst = buyPreValidRst || {};
            if (!buyPreValidRst.status) {
                CommonUtil.enabledBtn(btn);
                return false;
            }
            //kyc状态
            Buy.kycFlagConfirm();
        }

    },

    /***
     * 确认购买
     */
    confirm: function () {
        CommonUtil.disabledBtn("confimBuyBtn");
        console.log("Buy.fundInfo.peDivideCallFlag==" + Buy.fundInfo.peDivideCallFlag);
        var applyAmount = CommonUtil.convertAmountToNumber($("#applyAmount").val());
        var payRatioAmount = CommonUtil.convertAmountToNumber($("#payRatioAmount").val());
        var callFlag = Buy.fundInfo.peDivideCallFlag;

        if ("1" === callFlag && applyAmount !== payRatioAmount) {
            Buy.buyConfrimLayer("净申请金额和缴款金额不等，是否继续?", function () {
                Buy.buyConfirmPretreatment()
            });
        } else {
            Buy.buyConfirmPretreatment();
        }
        CommonUtil.enabledBtn("confimBuyBtn");
    },


    /**
     * kyc状态确认
     */
    kycFlagConfirm: function () {
        //kyc状态确认
        if ('1' !== Buy.buyPreValidRst.kycFlag) {
            var tip = '请签署私募合格投资者承诺书';
            if (Buy.fundInfo.productChannel === '6') {
                tip = '请签署资管合格投资者承诺书';
            }
            CommonUtil.layer_tip(tip);
            CommonUtil.enabledBtn(btn);
            return false;
        } else {
            // 预约确认
            // Buy.confirmNoAppointment();
            // 高风险确认
            Buy.highRiskConfirm();
        }
    },

    /**
     * 预约确认
     * @param 预约确认
     * @returns {Boolean}
     */
    confirmNoAppointment: function () {

        if ($("#isContainAppointmentFlag").val() === "false") {
            Buy.buyConfrimLayer("高端交易没有预约,确认继续吗", function () {
                Buy.replyDealConfirm();
            }, function () {
                //取消函数
                CommonUtil.enabledBtn("confimBuyBtn");
            });

        } else {
            // 重复订单校验
            Buy.replyDealConfirm();
        }

    },

    /**
     * 重复订单校验
     */
    replyDealConfirm: function () {
        //重复下单校验
        if (Buy.buyPreValidRst.matchReplyDealFlag !== '1') {

            Buy.buyConfrimLayer("该客户重复下单，请确认", Buy.certificateStatusConfirm);
        } else {
            // 客户资产证明状态校验
            Buy.certificateStatusConfirm();
        }
    },

    /**
     * 资产证明状态确认
     */
    certificateStatusConfirm: function () {
        //资产证明状态校验
        if (Buy.buyPreValidRst.matchCertificateFlag === '2') {
            Buy.buyConfrimLayer("客户的私募资产证明无效，是否继续", Buy.dubboStatusConfirm);
        } else if (Buy.buyPreValidRst.matchCertificateFlag === '3') {
            Buy.buyConfrimLayer("客户的资管资产证明无效，是否继续", Buy.dubboStatusConfirm);
        } else {
            // 双录状态校验
            Buy.dubboStatusConfirm();
        }
    },

    /**
     *双录状态确认
     */
    dubboStatusConfirm: function () {
        //双录状态校验
        if (Buy.buyPreValidRst.dubboStatusMatchFlag !== '1') {

            Buy.buyConfrimLayer("该客户未完成双录，交易是否继续", Buy.buyQuotoConfirm);
        } else {
            // 购买限额校验
            Buy.buyQuotoConfirm();
        }
    },


    /**
     *购买限额确认
     */
    buyQuotoConfirm: function () {
        //购买人数校验
        if (Buy.buyPreValidRst.matchBuyQuotoFlag !== '1') {
            Buy.buyConfrimLayer("当前购买成功和提交申请的客户数已经大于等于产品认申购客户数限额，请确认", Buy.buyPeMaxAgeConfirm);
        } else {
            Buy.buyPeMaxAgeConfirm();
        }
    },
    /**
     *股权可购买最大年龄确认
     */
    buyPeMaxAgeConfirm: function () {
        //股权可购买最大年龄校验
        if (Buy.buyPreValidRst.peProductMaxAgeFlag !== '1') {
            Buy.buyConfrimLayer("该客户年龄大于等于70岁，需签署特殊风险提示函，请确认", Buy.riskConfirm);
        } else {
            Buy.riskConfirm();
        }
    },

    /**
     *高风险提示
     */
    highRiskConfirm: function () {
        // 高风险提示函
        if ('1' !== Buy.custInfo.investorType && Buy.fundInfo.fundRiskLevel === '5') {
            Buy.buyConfrimLayer("客户购买的为R5产品，请确认是否已签署”《高风险警示函及投资者确认书》",
                Buy.confirmNoAppointment,
                function () {
                });
        } else {
            Buy.confirmNoAppointment();
        }
    },

    /**
     * 风险确认
     */
    riskConfirm: function () {

        var buyConfirmForm = $("#buyConfirmForm").serializeObject();
        var bankAcct = $('#selectBank').find('option:selected').attr('bankacct');
        buyConfirmForm.bankAcct = bankAcct || '';
        buyConfirmForm.appAmt = CommonUtil.unFormatAmount(buyConfirmForm.appAmt);
        buyConfirmForm.appDtm = buyConfirmForm.appDt + '' + buyConfirmForm.appTm;
        buyConfirmForm.riskFlag = Buy.riskFlag;
        buyConfirmForm.esitmateFee = Buy.fundInfo.fundBuyFee;
        buyConfirmForm.subsAmt = $("#subsAmt").val();
        buyConfirmForm.subsAmt = CommonUtil.unFormatAmount(buyConfirmForm.subsAmt);
        buyConfirmForm.payRatioAmount = CommonUtil.unFormatAmount(buyConfirmForm.payRatioAmount);
        var custInfoForm = JSON.stringify(Buy.custInfo);//客户信息
        var fundInfoForm = JSON.stringify(Buy.fundInfo);//产品信息
        var appointmentForm = JSON.stringify(Buy.appointment);//投顾预约信息
        var othetInfoForm = $("#othetInfoForm").serializeObject();//其他信息
        //经办人信息
        var transactorInfoForm = $("#transactorInfoForm").serializeObject();//经办人信息
        var appointmentInfoForm = JSON.stringify(Buy.highproductAppointinfo);//预约开放日历信息

        //构建购买确认参数
        Buy.confirmParams = Buy.buildBuyConfirmParams(appointmentForm,
            buyConfirmForm, custInfoForm, fundInfoForm, othetInfoForm,
            transactorInfoForm, appointmentInfoForm);

        //购买确认提交
        Buy.confirmSubmit();

    },

    /**
     * 构建购买提交参数
     * @param  appointmentForm  投顾预约信息
     * @param  buyConfirmForm 购买确认信息
     * @param  custInfoForm 客户信息
     * @param  fundInfoForm 产品信息
     * @param  othetInfoForm 其他信息
     * @param  transactorInfoForm 经办人信息
     * @param  appointmentInfoForm 预约开放日历信息
     *
     */
    buildBuyConfirmParams: function (appointmentForm, buyConfirmForm, custInfoForm,
                                     fundInfoForm, othetInfoForm, transactorInfoForm, appointmentInfoForm) {

        return {
            "appointmentForm": appointmentForm,
            "buyConfirmForm": JSON.stringify(buyConfirmForm),
            "custInfoForm": custInfoForm,
            "fundInfoForm": fundInfoForm,
            "othetInfoForm": JSON.stringify(othetInfoForm),
            "transactorInfoForm": JSON.stringify(transactorInfoForm),
            "appointmentInfoForm": appointmentInfoForm,
            "materialinfoForm": JSON.stringify(OnLineOrderFile.buildOrderCheckFile())
        };
    },

    /**
     * 确认提交
     *
     */
    confirmSubmit: function () {

        var uri = TmsCounterConfig.BUY_CONFIRM_URL || {};
        var reqparamters = Buy.confirmParams || {};//购买确认参数

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, Buy.callBack);
    },

    callBack: function (data) {
        var respCode = data.code || '';
        var respDesc = data.desc || '';
        Buy.clicked = '0';// 还原点击控制
        if (CommonUtil.isSucc(respCode)) {
            layer.confirm('提交成功', {
                btn: ['确定'] //按钮
            }, function () {
                layer.closeAll();
                if (OnLineOrderFile.isCrm()) {
                    CommonUtil.closeCurrentUrl();
                } else {
                    // 刷新页面
                    CommonUtil.reloadUrl();
                }
            });
        } else {
            if ("Z3000116" === respCode) {
                CommonUtil.layer_tip("产品极差为零，请确认");
            } else if ("Z3000067" === respCode) {
                //定向开放标识校验不通过
                CommonUtil.layer_tip("该产品必须有预约单才能下单");
            } else {
                CommonUtil.layer_tip("提交失败，" + respDesc);
            }

        }

        CommonUtil.enabledBtn("confimBuyBtn");
    },

    /**
     * 确认提示
     * @param content
     * @param btn
     * @param ok_cb  成功回调函数
     * @param cancle_cb 取消回调函数
     */
    buyConfrimLayer: function (content, ok_cb, cancle_cb) {

        layer.confirm(content, {
            btn: ['确定', '取消'] //按钮
        }, function () {
            if (ok_cb) {
                layer.closeAll();
                ok_cb();
            }

        }, function () {
            if (cancle_cb) {
                cancle_cb();
                Buy.clicked = '0';// 还原点击控制
                CommonUtil.enabledBtn("confimBuyBtn");
                layer.closeAll();
            } else {
                Buy.clicked = '0';// 还原点击控制
                CommonUtil.enabledBtn("confimBuyBtn");
                layer.closeAll();
            }

        });
    },
    riskFlagLayer: function (content, ok_cb, cancle_cb) {

        layer.confirm(content, {
            btn: ['确定', '取消'] //按钮
        }, function () {
            if (ok_cb) {
                layer.closeAll();
                ok_cb();
            }

        }, function () {
            if (cancle_cb) {
                cancle_cb();
                Buy.clicked = '0';// 还原点击控制
                CommonUtil.enabledBtn("confimBuyBtn");
                layer.closeAll();
            } else {
                Buy.clicked = '0';// 还原点击控制
                CommonUtil.enabledBtn("confimBuyBtn");
                layer.closeAll();
            }

        });
    },

    /**
     * 查询产品预约开放日历信息
     */
    queryHighproductAppointinfo: function (fundCode, appDt, appTm, busyType) {

        QueryHighProduct.queryHighproductAppointinfo(fundCode, appDt, appTm, busyType);
        Buy.highproductAppointinfo = QueryHighProduct.highproductAppointinfo || {};
        //构建预约开放日历信息
        setTimeout(Buy.buildHighproductAppointinfo(QueryHighProduct.highproductAppointinfo), 1000);


    },

    /**
     * 构建产品预约开放预约日历信息
     */
    buildHighproductAppointinfo: function (highproductAppointinfo) {

        highproductAppointinfo = highproductAppointinfo || {};
        $("#appointStartDtId").html(highproductAppointinfo.appointStartDt);
        $("#apponitEndDtId").html(highproductAppointinfo.apponitEndDt);
        $("#openStartDtId").html(highproductAppointinfo.openStartDt);
        $("#openEndDtId").html(highproductAppointinfo.openEndDt);
        $("#payRatioId").val(highproductAppointinfo.payRatio);
    },

    /**
     * 计算缴款金额
     */
    calculatePayRatioAmount: function (value) {
        if("HZ000N001" !== Buy.fundInfo.disCode){
            $("#payRatioAmount").val(CommonUtil.formatAmount($("#applyAmount").val()));
            return;
        }
        if(value == null){
            return;
        }
        if ("HZ000N001" === Buy.fundInfo.disCode && Buy.fundInfo.peDivideCallFlag !== '1') {
            $("#payRatioAmount").val(CommonUtil.formatAmount(value));
            $('#applyAmount').val(CommonUtil.formatAmount(value));
            return;
        }
        if (Buy.fundInfo.peDivideCallFlag !== '1') {
            return;
        }
        var payRatioAmount = CommonUtil.unFormatAmount(value);

        var payRatio = $("#payRatioId").val();
        if (payRatio) {
            payRatioAmount = math.multiply(math.bignumber(payRatioAmount), math.bignumber(payRatio)).toString();
        }
        $("#payRatioAmount").val(CommonUtil.formatAmount(payRatioAmount))
    },

    /**
     * 查询高端产品基本信息
     * @param fundCode 产品代码
     * @param appDt 申请日期
     * @param appTm 申请时间
     * @param busyType 业务类型 0-购买 1-赎回
     */
    queryFundInfo: function (fundCode) {

        var excludeInputArr = [];
        excludeInputArr.push('fundCode');
        //初始化订单信息
        Buy.initBuyDealForm(excludeInputArr);

        // 客户类型 0-机构 1-个人
        var invstType = Buy.custInfo.invstType || '';

        QueryHighProduct.queryFundInfo(fundCode, null, null, '0', invstType);
        Buy.fundInfo = QueryHighProduct.fundInfo || {};

        if (CommonUtil.isEmpty(Buy.fundInfo.fundCode)) {
            CommonUtil.layer_tip("没有查询到此产品");
            return false;
        }

        //构建产品基本信息
        Buy.buildFundInfo(Buy.fundInfo);

        //查询工作日
        QueryHighProduct.queryHighproductWorkDay(fundCode);
        if ("HZ000N001" === Buy.fundInfo.disCode) {
            Buy.queryHzSubscribeAmtInfo(fundCode);
        }

    },
    /**
     * 查询认缴金额
     * @param fundCode
     */
    queryHzSubscribeAmtInfo: function (fundCode) {
        if ("HZ000N001" !== Buy.fundInfo.disCode) {
            return;
        }
        var buyConfirmForm = $("#buyConfirmForm").serializeObject();
       var queryDateStr= buyConfirmForm.appDt + '' + buyConfirmForm.appTm;
        var uri = TmsCounterConfig.QUERY_HZ_BUY_INFO || {};
        var reqparamters = {"fundCode": fundCode, "txAcctNo": Buy.custInfo.custNo,"queryDateStr":queryDateStr};
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
        CommonUtil.ajaxAndCallBack(paramters, Buy.buildHzSubscribeAmtInfo);
    },

    /**
     * 构建好臻金额认缴金额信息
     */
    buildHzSubscribeAmtInfo: function (data) {
        var bodyData = data.body || {};
        Buy.hzSubscribeAmtInfo = bodyData.data || {};
        // 分次call
        if (Buy.hzSubscribeAmtInfo.peDivideCallFlag !== null && Buy.hzSubscribeAmtInfo.peDivideCallFlag === '1') {
            $('#subsAmt').val(Buy.hzSubscribeAmtInfo.subscribeAmt);
            $('#applyAmount').val(Buy.hzSubscribeAmtInfo.paidAmt);
            $('#payRatioAmount').val(Buy.hzSubscribeAmtInfo.paidAmt);
        } else {
            // 非分次call
            $('#subsAmt').val(Buy.hzSubscribeAmtInfo.buyAmt);
            $('#applyAmount').val(Buy.hzSubscribeAmtInfo.buyAmt);
            $('#payRatioAmount').val(Buy.hzSubscribeAmtInfo.buyAmt);
        }
        if ($("#subsAmt").val() && $("#subsAmt").val() !== '--') {
            Buy.calFundBuyFee(null);
            Buy.calFee();
        }

    },

    /**
     * 构建产品信息
     */
    buildFundInfo: function (fundInfo) {
        $("#buyBusiTypeId").html(CommonUtil.getMapValue(CONSTANTS.BUY_BUYS_TYPE_MAP, fundInfo.buyBusiType));
        $("#fundName").html(Buy.fundInfo.fundAttr || '');
        $("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, fundInfo.fundRiskLevel, ''));
        $("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundInfo.fundStat));
        $("#taCodeId").html(fundInfo.taCode);
        $("#productTypeId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, fundInfo.fundType));
        $("#shareClassId").html(CommonUtil.getMapValue(CONSTANTS.FUND_SHARECLASS_MAP, fundInfo.shareClass));
        $("#productChannelId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, fundInfo.productChannel));
        $("#netMinAppAmtId").html(CommonUtil.formatAmount(fundInfo.netMinAppAmt));
        $("#netMinSuppleAmtId").html(CommonUtil.formatAmount(fundInfo.netMinSuppleAmt));
        $("#feeCalModeId").html(CommonUtil.getMapValue(CONSTANTS.FEE_CAL_MODE_MAP, fundInfo.feeCalMode));

        // 处理股权分次call产品
        if ("1" === fundInfo.peDivideCallFlag) {
            $("#subsAmt").attr("isnull", "false");
            $("#payRatioAmount").attr("isnull", "false");
        } else {
            $("#subsAmt").removeAttr("isnull");
            $("#payRatioAmount").removeAttr("payRatioAmount");
        }

        if ("1" === fundInfo.fixedIncomeFlag) {
            $("#redeemExpireTr").removeAttr("hidden");
            $("#redeemExpireDescId").html("到期是否赎回");
            $("#redeemExpireTd").html(CommonUtil.buildRedeemExpireHtml());
        } else {
            $("#redeemExpireDescId").html("");
            $("#redeemExpireTd").html("");
            $("#redeemExpireTr").attr("hidden", "hidden");
        }
    },

    /**
     * 查询客户银行卡信息
     */
    queryCustBankInfo: function (custNo, disCode) {
        var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
        var reqparamters = {"custNo": custNo, "disCode": disCode};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                Buy.custBanks = body.custBanks || [];
                var selectBankHtml = '';
                $(Buy.custBanks).each(function (index, element) {
                    selectBankHtml += '<option bankacct= "' + element.bankAcct + '" value="' + element.cpAcctNo + '" bankCode="' + element.bankCode + '">' + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                });
                $("#selectBank").empty();
                $("#selectBank").html(selectBankHtml);

                // 校验是否绑卡
                Buy.valideCustBindBank();
            }
        });
    },

    /**
     * 校验客户是否绑卡
     */
    valideCustBindBank: function () {
        if (Buy.custBanks.length <= 0) {
            CommonUtil.layer_tip("客户银行卡没绑卡");
            return false;
        } else {
            return true;
        }
    },

    selectAppointmentInfo: function () {
        var allChecked = $("input[type='checkbox'][name='appointmentInfoIndex']:checked");

        if (allChecked.length > 1) {
            showMsg("不能多选，只能选择一条预约信息");
            allChecked.removeAttr("checked");
            return false;
        }
        if (allChecked.length < 1) {
            $("#isContainAppointmentFlag").val("false");
            Buy.initBuyDealForm(null);
            return false;
        }
        var obj = allChecked[0];
        if (obj.checked) {

            $("#isContainAppointmentFlag").val("true");
            var tr = $(obj).closest("tr");
            var dataLine = tr.children();
            /**产品代码*/
            var productCode = $.trim(dataLine.eq(2).text());
            //查询产品信息
            Buy.queryFundInfo(productCode);
            Buy.fundInfo.fundCode = productCode;

            /**预约ID*/
            var appointId = $.trim(dataLine.eq(1).text());
            Buy.appointment.appointId = $.trim(dataLine.eq(1).text());
            /**预约产品名称*/
            Buy.appointment.productName = $.trim(dataLine.eq(3).text());
            // 预约产品代码
            Buy.appointment.productCode = productCode;
            if (!CommonUtil.isEmpty(productCode)) {

                $('#fundCode').val(productCode);
            }
            /**预约折扣*/
            var discountRate = $.trim(dataLine.eq(9).text());
            Buy.appointment.discountRate = discountRate;
            $('#discountRate').removeAttr("readonly", "readonly");
            if (!CommonUtil.isEmpty(discountRate)) {
                $('#discountRate').val(discountRate);
                $('#discountRate').attr("readonly", "readonly");
            }

            /**预约类型*/
            var preType = $.trim(dataLine.eq(14).text().replace(/\"/g, ""));
            Buy.appointment.preType = preType;

            /**净申请金额*/
            var appAmt = $.trim(dataLine.eq(7).text());
            Buy.appointment.appAmt = appAmt;//预约申请金额
            if (!CommonUtil.isEmpty(appAmt)) {
                // 格式化金额
                var formatAmt = CommonUtil.formatAmount(appAmt);
                $('#applyAmount').val(formatAmt);
                //申请金额大写
                Buy.appAmtUpper(appAmt);
            }

            /**认缴金额 有预约时已预约为准，不允许修改*/
            if ("HZ000N001" !== Buy.fundInfo.disCode) {
                $('#subsAmt').attr("readonly", "readonly");
            }

            var subsAmt = $.trim(dataLine.eq(15).text());
            Buy.appointment.subsAmt = subsAmt;
            if (!CommonUtil.isEmpty(subsAmt)) {
                // 格式化金额
                $('#subsAmt').val(CommonUtil.formatAmount(subsAmt));
            }

            Buy.selectAppointment = {
                "appointId": appointId,
                "productCode": productCode,
                "discountRate": discountRate,
                "preType": preType,
                "appAmt": appAmt
            };
            // 设置产品代码不可修改
            $("#fundCode").attr("disabled", "disabled");

            // 计算手续费
            Buy.calFundBuyFee(productCode);

            // 查询产品信息
            var appDt = $("#appDt").val();
            var appTm = $("#appTm").val();

            //查询预约开放日历
            Buy.queryHighproductAppointinfo(productCode, appDt, appTm, "0");
            var subsAmtTmp = $("#subsAmt").val();
            if (!CommonUtil.isEmpty(subsAmtTmp)) {
                Buy.calculatePayRatioAmount(subsAmtTmp);
            }

            // 查询材料
            OnLineOrderFile.initMaterial(Buy.urlParams, Buy.getSelectCustMaterial(), OnLineOrderFile.CRM_OP_CHECK_NODE_PRE);
        } else {
            Buy.initBuyDealForm();
        }
    },

    /**
     *
     * @Description  初始化材料
     *
     * @param null
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 17:30
     **/
    getSelectCustMaterial: function () {
        var custSelectOrder = {};
        custSelectOrder["hboneno"] = Buy.custInfo.hboneNo;// 一账通帐号
        custSelectOrder["pcode"] = Buy.selectAppointment.productCode;// 产品代码
        custSelectOrder["preid"] = Buy.selectAppointment.appointId;// 预约ID
        custSelectOrder["busiid"] = OnLineOrderFile.CRM_BUY;// 业务类型ID // CRM业务类型 购买
        return custSelectOrder;
    },

    queryDiscount: function (fundCode, discountRate, bankCode, applyAmount, appAmt) {
        var uri = TmsCounterConfig.CAL_DISCOUNT_RATE_URL || {};
        var custInfoForm = JSON.stringify(Buy.custInfo);
        if (CommonUtil.isEmpty(custInfoForm)) {
            showMsg("请先选择客户信息，以計算費率");
            return false;
        }
        if (CommonUtil.isEmpty(fundCode)) {
            showMsg("请先选择基金信息，以計算費率");
            return false;
        }
        if (CommonUtil.isEmpty(applyAmount)) {
            showMsg("请先填写净申请金额，以計算費率");
            return false;
        }
        var reqparamters = {};
        reqparamters.custInfoForm = custInfoForm;
        reqparamters.fundCode = fundCode;
        reqparamters.discountRate = discountRate;
        reqparamters.bankCode = bankCode;
        //净申请金额
        reqparamters.applyAmount = applyAmount;
        //预约申请金额
        reqparamters.appAmt = appAmt;
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, Buy.queryDiscountCallBack);
    },

    queryDiscountCallBack: function (data) {

        var bodyData = data.body || {};
        var respCode = data.code || '';
        var respDesc = data.desc || '';
        if (CommonUtil.isSucc(respCode)) {
            var respData = bodyData.respData || [];
            $("#discountRate").val(respData.discountRate);
            // 计算手续费
            Buy.calFee();
        } else {
            CommonUtil.layer_tip(respDesc);
        }

    },
    /**
     * 校验预约信息是否选中
     */
    validSelectAppoint: function (productCode) {
        var appointmentInfoList = $("input[type='checkbox'][name='appointmentInfoIndex']") || [];
        if (Buy.validBuyProductIsPre(appointmentInfoList, productCode)) {

            if (appointmentInfoList.length > 0) {
                var checkedList = $("input[type='checkbox'][name='appointmentInfoIndex']:checked") || [];
                if (checkedList.length <= 0) {
                    return false;
                }
            }
        }

        return true;
    },
    /**
     * 校验产品是否是预约产品
     * @param appointmentInfoList
     * @param productCode
     */
    validBuyProductIsPre: function (appointmentInfoList, productCode) {
        var isPreProduct = false;
        $(appointmentInfoList).each(function (index, element) {
            var tr = $(element).closest("tr");
            var dataLine = tr.children();
            /**产品代码*/
            var preProductCode = $.trim(dataLine.eq(2).text());
            if (preProductCode === productCode) {
                isPreProduct = true;
            }
        });

        return isPreProduct;
    },

    /**
     * 购买预校验
     * @param buyPreValidParam 校验参数
     * @param btn
     */
    buyPreValid: function (buyPreValidParam) {

        var uri = TmsCounterConfig.HIGH_BUY_PRE_VALID;
        var appDt = $("#appDt").val();
        var appTm = $("#appTm").val();
        var reqParams = buyPreValidParam || {};
        reqParams.appDt = appDt;
        reqParams.appTm = appTm;

        var paramters = CommonUtil.buildReqParams(uri, reqParams, false);
        var rst = {"status": false};

        CommonUtil.disabledBtn("confimBuyBtn");
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            CommonUtil.enabledBtn("confimBuyBtn");

            var respCode = data.code || '';
            var desc = data.desc || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                // 双录状态校验
                rst.dubboStatusMatchFlag = body.dubboStatusMatchFlag;
                // 资产证明校验
                rst.matchCertificateFlag = body.matchCertificateFlag;
                // 购买限额校验
                rst.matchBuyQuotoFlag = body.matchBuyQuotoFlag;

                // 金额重复订单校验
                rst.matchReplyDealFlag = body.matchReplyDealFlag;

                rst.kycFlag = body.kycFlag;

                // 股权可购买最大年龄
                rst.peProductMaxAgeFlag = body.peProductMaxAgeFlag;

                rst.status = true;

            } else {
                CommonUtil.layer_tip(respCode + "(" + desc + ")", 5000);
            }
        });

        return rst;
    }
}



