<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>赎回</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易审核 <span class="c-gray en">&gt;</span> 赎回审核 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    
    <div class="page-container w1000">
        <p class="main_title">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>风险测评结果</th>
                        <th>开户分销机构</th>
                        <th>私募合格投资者认证</th>
                        <th>资管合格投资者认证</th>
                        <th>客户状态</th>
                        <th>投资者类型</th>
                        <th>协议回款方式</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                    <tr class="text-c">
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <p class="main_title mt30">客户预约信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>预约单号</th>
                        <th>预约产品代码</th>
                        <th>预约产品名称</th>
                        <th>预约业务</th>
                        <th>预约日期</th>
                        <th>预约时间</th>
                        <th>预约金额(不含费)</th>
                        <th>预约份额</th>
                        <th>预约折扣</th>
                        <th>预约单状态</th>
                    </tr>
               </thead>
                <tbody class="text-c" id="rsList">
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                </tbody>
            </table>
            <div class="clear page_all">
            <div class="fy_part fr mt20" id="pageView"></div>
        </div>
        </div>

        <p class="main_title mt30" id="showMaterial">柜台材料信息</p>
        <div class="result2_tab" id="onLineMaterial">
        </div>

        <form action="" id="sellConfirmForm">
        <p class="main_title mt30">录入订单信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                	<tr class="text-c">
                		<td>基金代码</td>
                        <td id="fundCodeId" class="readText"></td>
                        <td>TA代码</td>
                        <td id="taCodeId" class="readText"></td>
                	</tr>
                    <tr class="text-c">
                        <td>基金简称</td>
                        <td id="fundName" class="readText"></td>
                        <td>产品通道</td>
                        <td id="productChannelId" class="readText"></td>
                    </tr>
                      <tr class="text-c">
                        <td>产品类别</td>
                        <td id="productTypeId" class="readText"></td>
                        <td>产品状态</td>
                        <td id="fundStatus" class="readText"></td>
                    </tr>
                    <tr class="text-c">
                        <td>巨额赎回顺延</td>
                        <td>
                            <span class="select-box inline">
                                <select name="largeRedmFlag" class="select selectLargeRedmFlag" isnull="false" datatype="s" errormsg="巨额赎回顺延标识">
                                </select>
                            </span>
                        </td>
                        <td></td>
                        <td>
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>币种</td>
                        <td>人民币</td>
                        <td>异常赎回标识</td>
                        <td>
                        	<span class="select-box inline">
                                <select name="unusualTransType" class="select selectUnusualTransType" isnull="false" datatype="s" errormsg="异常交易标识">
                                </select>
                            </span>
                            <input name="cpAcctNo" type="hidden"/>
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>标准费率</td>
                        <td></td>
                        <td>申请折扣率</td>
                        <td></td>
                    </tr>
                </tbody>
                <tbody id="sellConfirmFormFoot">
                    <tr class="text-c">
                        <td>合计申请份额（份）</td>
                        <td id="volSum" class="readText"></td>
                        <td>大写合计申请份额</td>
                        <td id="convertVolSum" class="readText"></td>
                    </tr>
                    <tr class="text-c">
                    	<td>预约开始日期</td>
                    	<td class="readText" id="appointStartDtId"></td>
                    	<td>下单日期</td>
                        <td>
                            <input class="input-text laydate-icon" onclick="laydate({istime: false, format: 'YYYYMMDD'})"  id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8">
                        </td>
                    </tr>
                    <tr class="text-c">
                     	<td>预约截止日期</td>
                    	<td id="apponitEndDtId" class="readText"></td>
                        <td>下单时间</td>
                        <td>
                            <input class="" type="text" id="appTm" name="appTm" isnull="false" datatype="s" errormsg="下单时间" maxlength = "6">
                        </td>
                     </tr>
                    <tr class="text-c">
                       <td >开放开始日期</td>
                       <td class="readText" id="openStartDtId"></td>
                       <td>开放结束日期</td>
                       <td class="readText" id="openEndDtId"></td>
                    </tr>
                </tbody>
            </table>
        </div>
         </form>
        <p class="main_title mt30">其他信息</p>
           <form id="othetInfoForm">
        <div class="info">
            <span>网点：中台柜台</span>
            <span class="ml30">投资顾问代码：
                <span class="select-box inline">
                    <select name="consCode" class="select selectconsCode">
                    </select>
                </span>
            </span>
            <span class="ml30">是否代理：
                <span class="select-box inline">
                   <select name="agentFlag" class="select selectAgened">
                      <option value="0">否</option>
                      <option value="1">是</option>
                   </select>
                </span>
            </span>
             <span class="ml30">录入人：
               <span id="operatorNo"></span>
            </span>
        </div>
        </form>
     
           <form id="transactorFormId">
         <p class="main_title mt30">经办人信息</p>
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                 <tr class="text-c">
                        <td>经办人姓名：</td>
                        <td>
                       <input type="text" placeholder="请输入" id="transactorName" name="transactorName" isnull="false" datatype="s" errormsg="经办人姓名">
                       </td>
                        <td>经办人证件类型：</td>
              <td>
                  <span class="select-box inline">
                    <select id="transactorIdType" name="transactorIdType" class="select selectTransactorIdType"  isnull="false" datatype="s" errormsg="经办人证件类型" >
                    </select>
                </span>
                </td>
             <tr class="text-c">
              <td>经办人证件号：</td>
               <td> <input type="text" placeholder="请输入" id="transactorIdNo" name="transactorIdNo" isnull="false" datatype="s" errormsg="经办人证件号" ></td>
                <td></td>
                 <td></td>
            </tbody>
          </table>
        </div>
         </form>
         
     
     <form action="" id="checkResult">
        <div class="checkIn mt20">
           <p class="reCheckTitle">复核信息</p>
            <table>
                <tr>
                    <td>
                        <span>基金代码：<input type="text" placeholder='请确认输入' name="fundCode">（仅申购、赎回业务）</span>
                    </td>
                    <td>
                        <span>驳回原因：<input type="text" placeholder='请输入驳回原因，可选' name="checkFaildDesc">（仅审核驳回使用）</span>
                    </td>
                </tr>
            </table>
        </div>
        </form>  
     
         <p class="mt30 text-c" id="submitDiv">
            <a href="javascript:void(0)" id ="checkRejectBtn" class="btn radius  btn-danger ml30">审核驳回</a>
            <a href="javascript:void(0)" id ="checkModifyBtn" class="btn radius btn-warning ml30">提交</a>
            <a href="javascript:void(0)" id ="checkCacelBtn" class="btn radius btn-secondary ml30">作废</a>
            <a href="javascript:void(0)" id ="checkConfirmBtn" class="btn radius btn-success ml30">审核通过</a>
            <a href="javascript:void(0)" id ="checkBackBtn" class="btn radius btn-success ml30">返回</a>
        </p>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../js/common.js"></script>
    <script type="text/javascript" src="../../../js/config.js"></script>
    <script type="text/javascript" src="../../../js/commonutil.js"></script>
    <script type="text/javascript" src="../../../js/valid.js"></script>
    <script type="text/javascript" src="../../../js/high/conscode.js"></script>
    <script type="text/javascript" src="../../../js/high/common/onlineorderfile.js"></script>
    <script type="text/javascript" src="../../../js/high/check/viewcounterdeal.js"></script>
    <script type="text/javascript" src="../../../js/high/common/onlineorderfile.js"></script>
    <script type="text/javascript" src="../../../js/high/check/countercheck.js?v=11"></script>
    <script type="text/javascript" src="../../../js/high/check/sellcheck.js?v=10"></script>
    <script type="text/javascript" src="../../../js/high/common/init.js"></script>
</body>

</html>