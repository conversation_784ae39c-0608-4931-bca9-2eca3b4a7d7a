/**
 *柜台审核
 *<AUTHOR>
 *@date 2017-04-01 15:23
 */
$(function () {
    //通过
    CounterCheck.Succ = '1';
    //失败
    CounterCheck.Faild = '2';
    //驳回
    CounterCheck.Reject = '3';
    //作废
    CounterCheck.Cancel = '4';
    //修改
    CounterCheck.Modify = '5';

    if ('crm' === CommonUtil.getParam("source")) {
        OnLineOrderFile.selfLogin(CommonUtil.getParamJson());
    }

    //禁用所有input,不含复核信息输入框
    $('form').not("#checkResult").find('input').each(function (index, element) {
        $(element).attr("disabled", "disabled");
    });

    $('form').not("#checkResult").find('select').each(function (index, element) {
        $(element).attr("disabled", "disabled");
    });

});
var CounterCheck = {

    //viewType 0-查看；1-审核；2-修改
    viewTypeAndCheckNodeMap: {
        "0": OnLineOrderFile.CRM_OP_CHECK_NODE_VIEW,
        "1": OnLineOrderFile.CRM_OP_CHECK_NODE_RE,
        "2": OnLineOrderFile.CRM_OP_CHECK_NODE_MODIFY
    },

    /** 查询柜台订单
     * @param dealAppNo
     */
    queryCounterDealOrder: function (viewType, succFunc, completeFunc) {
        var dealAppNo = CommonUtil.getParam("dealAppNo") || CommonUtil.getParam("forId");
        var uri = TmsCounterConfig.HIGH_VIEW_CHECKORDER;

        var reqparamters = {};
        reqparamters.dealAppNo = dealAppNo;
        reqparamters.checkNode = CounterCheck.viewTypeAndCheckNodeMap[viewType];

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, succFunc, completeFunc);
    },


    initBtn: function (viewType) {

        //隐藏按钮
        CommonUtil.higdeAllBtnOfDiv("submitDiv");
        if ("1" === viewType) {
            //复核
            //显示复核信息
            $("#checkResult").show();

            //显示审核按钮
            ViewCounterDeal.showCheckBtn();

            //绑定审核事件
            //审核驳回
            $("#checkRejectBtn").on('click', function () {
                CounterCheck.confirm("3");  //checkType  1-审核通过 3-审核驳回 5-修改4-作废
            });

            //审核通过
            $("#checkConfirmBtn").on('click', function () {
                CounterCheck.confirm("1");  //checkType  1-审核通过 3-审核驳回 5-修改4-作废
            });

        } else {
            //隐藏复核信息
            $("#checkResult").hide();

        }

        if ("2" === viewType) {
            //修改

            //显示修改按钮
            $("#checkModifyBtn").show();
            //显示作废按钮
            $("#checkCacelBtn").show();

            //修改
            $("#checkModifyBtn").on('click', function () {
                CounterCheck.confirm("5");  //checkType 1-审核通过 3-审核驳回 5-修改4-作废
            });

            //作废
            $("#checkCacelBtn").on('click', function () {
                CounterCheck.confirm("4");
            });
        }

        //查看
        if ("0" === viewType) {

            // 显示返回按钮
            $("#checkBackBtn").show();

            // 返回
            $("#checkBackBtn").on("click", function () {
                parent.layer.closeAll();
            });
        }

        /**
         * 双击查询产品信息
         */
        $("#searchFundId").on('dblclick', function () {
            QueryHighProdInfoSubPage.selectProductCode($(this), 'searchFundId');
        });

    },

    /***
     * checkType  审核状态 1-审核通过 2-审核驳回
     * checkedOrder 待审核订单
     * 审核确认
     */
    confirm: function (checkType) {

        var checkedOrder = CounterCheck.counterOrderDto;
        if (window.checkedClick === '1') {
            return false;
        }

        //防止重复点击
        window.checkedClick = '1';

        var checkResult = $("#checkResult").serializeObject();
        if (CounterCheck.Reject === checkType) {
            if (CommonUtil.isEmpty(checkResult.checkFaildDesc)) {
                window.checkedClick = '0';
                CommonUtil.layer_tip("请填写驳回原因");
                return false;
            }
        } else if (CounterCheck.Succ === checkType) {
            //审核确认校验
            var txCode = checkedOrder.txCode;
            if ('Z900011' === txCode) {
                var applyAmount = $("#applyAmount").val();
                var payRatioAmount = $("#payRatioAmount").val();
                if (payRatioAmount && (applyAmount !== payRatioAmount)) {
                    var continueSubmit = confirm("净申请金额和缴款金额不等，是否继续?");
                    if (!continueSubmit) {
                        window.checkedClick = '0';
                        layer.closeAll();
                        return false;
                    } else {
                        layer.closeAll();
                        //购买
                        var checkResultReply = CounterCheck.checkBuyValid(checkResult, checkedOrder);
                        if (!checkResultReply.status) {
                            if(checkResultReply.tip!==""){
                                CommonUtil.layer_tip(checkResultReply.tip);
                            }
                            window.checkedClick = '0';
                            return false;
                        }
                        CounterCheck.submit(checkResult, checkedOrder, checkType);
                        return;
                    }
                } else {
                    //购买
                    var checkResultReply = CounterCheck.checkBuyValid(checkResult, checkedOrder);
                    if (!checkResultReply.status) {
                        if(checkResultReply.tip!==""){
                            CommonUtil.layer_tip(checkResultReply.tip);
                        }
                        window.checkedClick = '0';
                        return false;
                    }
                    CounterCheck.submit(checkResult, checkedOrder, checkType);
                    return;
                }

            } else if ('Z900012' === txCode) {
                //赎回
                var checkResultReply = CounterCheck.checkSellValid(checkResult, checkedOrder);
                if (!checkResultReply.status) {
                    window.checkedClick = '0';
                    CommonUtil.layer_tip(checkResultReply.tip);
                    return false;
                }
                CounterCheck.submit(checkResult, checkedOrder, checkType);
                return;
            }
        } else if (CounterCheck.Modify === checkType || CounterCheck.Faild === checkType) {

        } else {
            CommonUtil.layer_tip("无效操作", null, null);
            return false;
        }

        CounterCheck.submit(checkResult, checkedOrder, checkType);

        return true;
    },
    /**
     * 确认提示
     * @param checkResult
     * @param checkedOrder
     * @param checkType
     */
    submit: function (checkResult, checkedOrder, checkType) {
        var uri = TmsCounterConfig.CHECK_CONFIRM_URL || {};
        var reqparamters = {
            "checkFaildDesc": checkResult.checkFaildDesc || '',
            "checkType": checkType,
            "checkResult": JSON.stringify(checkResult),
            "checkedOrderForm": JSON.stringify(checkedOrder),
            "materialinfoForm": JSON.stringify(OnLineOrderFile.buildOrderCheckFile())
        };

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, CounterCheck.callBack);
    },
    callBack: function (data) {
        window.checkedClick = '0';
        var respCode = data.code || '';
        var respDesc = data.desc || '';

        if (CommonUtil.isSucc(respCode)) {
            layer.confirm('成功', {
                btn: ['确定'] //按钮
            }, function () {
                layer.closeAll();
                if (OnLineOrderFile.isCrm()) {
                    CommonUtil.closeCurrentUrl();// 关闭当前页面
                } else {
                    //刷新父页面
                    window.parent.location.reload();

                    //获取窗口索引
                    var index = parent.layer.getFrameIndex(window.name);
                    //关闭弹窗
                    parent.layer.close(index);
                }
            });
        } else {
            CommonUtil.layer_tip(respDesc);
        }
    },

    /**
     * 审核购买校验校验
     */
    checkBuyValid: function (checkForm, orderForm) {
        var fundCode = checkForm.fundCode || '';
        var appAmt = checkForm.appAmt || '';
        appAmt = appAmt.replace(/\,/g, "");
        var bankAcct = checkForm.bankAcct || '';
        var orderAmt = (orderForm.appAmt - (orderForm.esitmateFee || 0)).toFixed(2);
        var result = {"status": true, "tip": ''};
        if (fundCode !== (orderForm.fundCode || '')) {
            result.status = false;
            result.tip = "基金代码不匹配，请重新确认";
            return result;
        }
        if (appAmt !== orderAmt) {
            result.status = false;
            result.tip = "申请金额不匹配，请重新确认";
            return result;
        }
        var orderBankAcct = orderForm.bankAcct || '';
        if (bankAcct !== orderBankAcct) {
            result.status = false;
            result.tip = "银行卡不匹配，请重新确认";
            return result;
        }

        // 检查上报日是否变更（使用页面加载时查询的结果）
        if (CounterCheck.buyOrderInfoResult && CounterCheck.buyOrderInfoResult.submitDtChanged === "1") {
            var continueSubmit = confirm("最新上报日已经更新为:" + CounterCheck.buyOrderInfoResult.submitDt + ",是否继续?");
            if (!continueSubmit) {
                result.status = false;
                result.tip = "";
                return result;
            } else {
                layer.closeAll();
            }
        }

        return result;
    },
    /**
     * 审核赎回校验
     */
    checkSellValid: function (checkForm, orderForm) {
        let result = {"status": true, "tip": ''};
        // 校验基金代码
        let fundCode = checkForm.fundCode || '';
        if (fundCode !== (orderForm.fundCode || '')) {
            result.status = false;
            result.tip = "基金代码不匹配，请重新确认";
            return result;
        }
        // 校验总份额
        if ($("#checkResult [name='totalAppVol']").length > 0) {
            let totalAppVol = checkForm.totalAppVol || '';
            totalAppVol = totalAppVol.replace(/\,/g, "");
            if (totalAppVol !== (orderForm.appVol || '')) {
                result.status = false;
                result.tip = "总申请份额 不匹配，请重新确认";
                return result;
            }
        }
        // 生成份额list（支持卡号重复，重复时有一个份额能对上就行）
        let sellDtl = [];
        $(CounterCheck.dtlBeanList).each(function (index, data) {
            sellDtl.push({"bank": data.bankAcctNo, "vol": data.appVol});
        });
        // 校验份额明细
        $("#checkResult [id^='checkVolRow']").each(function (index, item) {
            // 上一条校验不过不继续校验
            if (!result.status) {
                return;
            }
            let bank = $(item).find("[name='bankAcct']").val();
            if (isEmpty(bank)) {
                result.status = false;
                result.tip = "银行卡号 必须全部输入";
                return result;
            }
            // 循环校验卡号及份额，卡号有多个匹配时有一个份额一致则校验通过
            let vol = $(item).find("[name='appVol']").val();
            if (!isEmpty(vol)) {
                vol = vol.replace(/\,/g, "");
            }
            let cardExists = false;
            let checkVolPass = false;
            for (index in sellDtl) {
                let formData = sellDtl[index];
                if (formData.bank == bank) {
                    cardExists = true;
                    if (formData.vol == vol) {
                        checkVolPass = true;
                    }
                }
            }
            if (!cardExists) {
                result.status = false;
                result.tip = "银行卡" + bank + "不存在，请重新确认";
                return result;
            }
            if (!checkVolPass) {
                result.status = false;
                result.tip = "银行卡" + bank + "对应的 申请份额 不匹配，请重新确认";
                return result;
            }
        });
        return result;
    },

    /**
     * 查询购买订单信息（检查上报日是否变更）
     * @param dealAppNo 订单号
     * @param appDt 申请日期
     * @param appTm 申请时间
     */
    queryBuyOrderInfo: function (dealAppNo, appDt, appTm) {
        var uri = TmsCounterConfig.HIGH_CHECK_BUY_ORDER_INFO;
        var reqparamters = {
            "dealAppNo": dealAppNo,
            "appDt": appDt,
            "appTm": appTm
        };
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);

        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                CounterCheck.buyOrderInfoResult = body.checkBuyInfoResult || {};
            }
        });
    },

    /**
     * 查询客户银行卡信息
     * @param custNo  客户号
     * @param bankAcct 默认选中银行卡
     * @param disCode  分销机构号
     */
    queryCustBankInfo: function (custNo, bankAcct, disCode) {
        var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
        var reqparamters = {"custNo": custNo, "disCode": disCode};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                var custBanks = body.custBanks || [];
                var selectBankHtml = '<option value="">请选择</option>';
                $(custBanks).each(function (index, element) {
                    selectBankHtml += '<option bankacct= "' + element.bankAcct + '" value="' + element.bankAcct + '">'
                        + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                });
                $("#selectBank").empty();
                $(".selectBank").attr("style", "width:300px");
                $("#selectBank").attr("style", "width:280px");
                $("#selectBank").html(selectBankHtml);

                //下单所属银行卡
                var selectCustBankHtml = '';
                $(custBanks).each(function (index, element) {
                    if (element.bankAcct == bankAcct) {
                        selectCustBankHtml += '<option bankacct= "' + element.bankAcct + '" value="' + element.bankAcct + '" selected>'
                            + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                    } else {
                        selectCustBankHtml += '<option bankacct= "' + element.bankAcct + '" value="' + element.bankAcct + '">'
                            + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                    }

                });
                $("#selectCustBank").empty();
                $("#selectCustBank").html(selectCustBankHtml);
                $("#selectCustBank").attr("style", "width:280px");

            }
        });
    }

};
