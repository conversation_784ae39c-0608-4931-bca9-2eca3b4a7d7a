/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:(客户基金持仓信息) 
 * <AUTHOR>
 * @date 2017年4月12日 下午7:58:18
 * @since JDK 1.6
 */
public class FundBalDtlDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 分行名称
     */
    private String bankRegionName;
    /**
     * 银行卡号
     */
    private String bankAcct;
    /**
     * 客户银行编号
     */
    private String custBankId;
    /**
     * 持有份额
     */
    private BigDecimal balanceVol = BigDecimal.ZERO;
    /**
     * 可用份额
     */
    private BigDecimal availVol = BigDecimal.ZERO;
    /**
     * 当前市值
     */
    private BigDecimal totalAmt = BigDecimal.ZERO;
    /**
     * 当前收益
     */
    private BigDecimal currentIncome = BigDecimal.ZERO;
    
    /**
     * 未结转收益(货币基金专属)
     */
    private BigDecimal unCarryOverIncome = BigDecimal.ZERO;

    /**
     * 组合持有份额
     */
    private BigDecimal portfolioBalanceVol = BigDecimal.ZERO;
    
    /**
     * 组合可用份额
     */
    private BigDecimal portfolioAvailVol = BigDecimal.ZERO;

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankRegionName() {
        return bankRegionName;
    }

    public void setBankRegionName(String bankRegionName) {
        this.bankRegionName = bankRegionName;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getCustBankId() {
        return custBankId;
    }

    public void setCustBankId(String custBankId) {
        this.custBankId = custBankId;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getAvailVol() {
        return availVol;
    }

    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getCurrentIncome() {
        return currentIncome;
    }

    public void setCurrentIncome(BigDecimal currentIncome) {
        this.currentIncome = currentIncome;
    }

    public BigDecimal getUnCarryOverIncome() {
        return unCarryOverIncome;
    }

    public void setUnCarryOverIncome(BigDecimal unCarryOverIncome) {
        this.unCarryOverIncome = unCarryOverIncome;
    }

    public BigDecimal getPortfolioBalanceVol() {
        return portfolioBalanceVol;
    }

    public void setPortfolioBalanceVol(BigDecimal portfolioBalanceVol) {
        this.portfolioBalanceVol = portfolioBalanceVol;
    }

    public BigDecimal getPortfolioAvailVol() {
        return portfolioAvailVol;
    }

    public void setPortfolioAvailVol(BigDecimal portfolioAvailVol) {
        this.portfolioAvailVol = portfolioAvailVol;
    }
    
}

