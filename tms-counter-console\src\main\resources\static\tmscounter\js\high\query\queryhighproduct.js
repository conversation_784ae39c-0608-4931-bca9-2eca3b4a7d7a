/**
*查询高端产品基本信息
*<AUTHOR>
*@date 2018-02-09 11:00:00
**/

var QueryHighProduct = {
	
	/**
	 * 查询高端产品信息
	 * @param fundCode 基金代码
	 * @param appDt 申请日期
	 * @param appTm 申请时间
	 * @param busyType 业务类型 0-购买 1-赎回
	 * @param invstType 客户类型 0-机构 1-个人
	 */
	queryFundInfo:function(fundCode, appDt, appTm, busyType, invstType){
		var  uri= TmsCounterConfig.QUERY_HIGH_PRODUCT_INFO_URL ||  {};
		
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入基金代码");
			return false;
		}
		
		var reqparamters = {"productCode":fundCode, "appDt":appDt, "appTm":appTm};
		reqparamters.busyType = busyType;
	    reqparamters.invstType = invstType;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryHighProduct.queryFundInfoCallBack);
	},
	
	/**
	 * 处理基金信息
	 */
	queryFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		QueryHighProduct.fundInfo = fundInfo;
	
	},
	
	/**
	 * 查询高端预约开放日历产品信息
	 * @param fundCode 基金代码
	 * @param appDt 申请日期
	 * @param appTm 申请时间
	 * @param busyType 业务类型 0-购买 1-赎回
	 */
	queryHighproductAppointinfo:function(fundCode, appDt, appTm, busyType){
		var  uri= TmsCounterConfig.HIGH_QUERY_HIGHPRODUCT_APPOINTINFO_URL ||  {};
		
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入基金代码");
			return false;
		}
		
		var reqparamters = {"productCode":fundCode, "appDt":appDt, "appTm":appTm};
		reqparamters.busiType = busyType;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryHighProduct.queryHighproductAppointinfoCallBack);
	},
	
	/**
	 * 处理产品预约开放日历信息
	 */
	queryHighproductAppointinfoCallBack:function(data){
		var bodyData = data.body || {};
		var highproductAppointinfo = bodyData.productAppointmentInfoModel || {};
		QueryHighProduct.highproductAppointinfo = highproductAppointinfo;
	
	},
	
	/**
	 * 查询高端工作日
	 * @param fundCode 基金代码
	 * @param workId 工作日Id
	 */
	queryHighproductWorkDay:function(fundCode){
		var  uri= TmsCounterConfig.QUERY_HIGH_WORKDAY_URL ||  '';
		
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入基金代码");
			return false;
		}
		
		var reqparamters = {"productCode":fundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryHighProduct.queryHighproductWorkDayCallBack);
	},
	
	/**
	 * 高端产品工作日
	 */
	queryHighproductWorkDayCallBack:function(data){
		
		var bodyData = data.body || {};
		var workDay = bodyData.workDay || '';
		QueryHighProduct.workDay = workDay;
		
		$("#appDt").val(workDay);//工作日

		$("#appDt").attr('readonly', 'readonly');
		var appTm = $("#appTm").val();
		if(CommonUtil.isEmpty(appTm)){
			$("#appTm").val(CommonUtil.formatDateToStr(new Date(), 'hhmmss'));
			if (!Valid.valiadTradeTime($("#appTm").val())) {
				$("#appTm").val(CONSTANTS.DEFAULT_TRADE_TIME);//默认交易时间
			}
		};
		
	}
	
};