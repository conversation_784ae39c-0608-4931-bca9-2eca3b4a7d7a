<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<dubbo:reference id="tmscounter.openTxAcctInstWithCounterFacade"
		interface="com.howbuy.acccenter.facade.trade.opentxacctinstwithcounter.OpenTxAcctInstWithCounterFacade"
		registry="trade" check="false" />

	<dubbo:reference id="tmscounter.queryBankCardInfoFacade"
		interface="com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoFacade"
		registry="trade" check="false" />
		
	<dubbo:reference id="tmscounter.queryCustMobileFacade"
		interface="com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileFacade"
		registry="trade" check="false" />
		
	<dubbo:reference id="tmscounter.queryBankAcctSensitiveInfoFacade"
		interface="com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoFacade"
		registry="trade" check="false" />

	<dubbo:reference id="tmscounter.custRiskSurveyFacade"
		interface="com.howbuy.acccenter.facade.trade.custrisksurvey.CustRiskSurveyFacade"
		registry="trade" check="false" />

	<dubbo:reference id="tmscounter.queryDefaultCnapsFacade"
		interface="com.howbuy.acccenter.facade.query.querydefaultcnaps.QueryDefaultCnapsFacade"
		registry="trade" check="false" />

	<dubbo:reference id="tmscounter.queryTxAcctByHboneFacade"
		interface="com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade"
		registry="trade" check="false" />

	<!--人工生成好买资产证明-->
	<dubbo:reference id="artificialGenHBAssetCertFacade" 
		interface="com.howbuy.acccenter.facade.trade.artificialgenhbassetcert.ArtificialGenHBAssetCertFacade" 
		registry="cfs" check="false" />

	<dubbo:reference id="tmscounter.queryBindCustBankCardListFacade"
					 interface="com.howbuy.acccenter.facade.query.querybindcustbankcardlist.QueryBindCustBankCardListFacade"
					 registry="trade" check="false" />
	
</beans>