/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @className ExpiredRedeemDto
 * @description
 * <AUTHOR>
 * @date 2019/3/26 14:26
 */
public class ExpiredRedeemDto implements Serializable {
    private static final long serialVersionUID = -8511002493223804351L;

    private String repurchaseProtocolNo;
    private String txAcctNo;
    private String fundCode;
    private String cpAcctNo;
    private BigDecimal appVol;
    private String memo;


    public String getRepurchaseProtocolNo() {
        return repurchaseProtocolNo;
    }

    public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
        this.repurchaseProtocolNo = repurchaseProtocolNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }
}
