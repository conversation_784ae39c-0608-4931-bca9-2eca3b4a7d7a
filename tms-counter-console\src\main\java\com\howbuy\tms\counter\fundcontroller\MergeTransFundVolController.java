/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.utils.VolShareTypeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.busi.BusiProcessProtocolTypeEnum;
import com.howbuy.tms.common.enums.busi.ShareTransferFlagEnum;
import com.howbuy.tms.common.enums.busi.ShareTypeEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.bean.CustBankModel;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.HttpUtil;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.service.orderplan.api.query.QuerySchePlanCounterRequest;
import com.howbuy.tms.counter.service.orderplan.api.query.QuerySchePlanCounterResponse;
import com.howbuy.tms.counter.service.orderplan.api.query.SchePlanCounterDto;
import com.howbuy.tms.counter.util.CommonUtil;

/**
 * @description:(份额合并或迁移控制器)
 * <AUTHOR>
 * @date 2018年5月4日 上午10:17:21
 * @since JDK 1.6
 */
@Controller
public class MergeTransFundVolController extends AbstractController {
    private Logger logger = LogManager.getLogger(MergeTransFundVolController.class);

    @Autowired
    private TradeValidateService tradeValidateService;

    /**
     * 
     * queryCustVolBal:(查询客户份额持仓)
     *      <br>份额合并或迁移使用
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月4日 上午10:24:51
     */
    @RequestMapping("/tmscounter/queryCustVolBal.htm")
    public ModelAndView queryCustVolBal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String idNo = request.getParameter("idNo");
        String disCode = request.getParameter("disCode");
        String fundCode = request.getParameter("fundCode");
        String bankAcct = request.getParameter("bankAcct");
        String shareType = request.getParameter("shareType");
        logger.debug("MergeTransFundVolController|queryCustVolBal|custNo:{},idNo:{},disCode:{},fundCode:{},bankAcct:{},shareType:{}", 
                custNo, idNo, disCode, fundCode, bankAcct, shareType);
        String operIp = HttpUtil.getIpAddr(request);
        
        QueryCustBankBalVolRespDto respData = getCustBankBals(custNo, idNo, disCode, fundCode, bankAcct, shareType, operIp);
        // 份额合并或迁移:设置定投基金合约状态
        if(respData != null){
            setCustBalScheStatus(respData);
        }

        if(respData != null){
            List<CustBankCardDto> retCustBankCardList = filterCustBankCardList(respData, shareType);
            respData.setCustBankCardList(retCustBankCardList);
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("respData", respData);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private List<CustBankCardDto> filterCustBankCardList(QueryCustBankBalVolRespDto respData, String shareType) {
        if(!VolShareTypeUtils.checkTransVol(shareType)){
            return respData.getCustBankCardList();
        }

        List<CustBankCardDto> retCustBankCardList = new ArrayList<>();
        if(respData == null || CollectionUtils.isEmpty(respData.getCustBalDtlList())){
            return retCustBankCardList;
        }

        List<String> disCodes = respData.getCustBalDtlList().stream().filter(e ->  !StringUtils.isEmpty(e.getDisCode())).map(CustBalDtlDto::getDisCode).distinct().collect(Collectors.toList());
        List<CustBankCardDto> custBankCardList = respData.getCustBankCardList();
        if(CollectionUtils.isEmpty(custBankCardList)){
            return retCustBankCardList;
        }

        List<String> cardIds = new ArrayList<>();
        for(CustBankCardDto dto : custBankCardList){
            boolean bankCardFlag = true;
            for(String disCodeStr : disCodes){
                boolean isValid = tradeValidateService.validateCustBankCard(respData.getTxAcctNo(), dto.getBankAcct(), disCodeStr);
                if(!isValid){
                    logger.info("filterCustBankCardList|validateCustBankCard txAcctNo:{}, bankAcct:{}, disCode:{}", respData.getTxAcctNo(), dto.getBankAcct(), disCodeStr);
                    bankCardFlag = false;
                    break;
                }
            }
            if(bankCardFlag && !cardIds.contains(dto.getCpAcctNo())){
                cardIds.add(dto.getCpAcctNo());
                retCustBankCardList.add(dto);
            }
        }
        return retCustBankCardList;
    }

    /**
     * 
     * setCustBalScheStatus:(份额迁移:设置定投基金合约状态)
     * @param respData
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月21日 下午4:11:47
     */
    private void setCustBalScheStatus(QueryCustBankBalVolRespDto respData) throws Exception{
        if(CollectionUtils.isEmpty(respData.getCustBalDtlList())){
            return;
        }
        boolean isQueryFlag = false;
        for(CustBalDtlDto balDto : respData.getCustBalDtlList()){
            isQueryFlag = isExistPlanProcolType(balDto.getProtocolType());
            if (isQueryFlag) {
                break;
            }
        }
        if(isQueryFlag){
            QuerySchePlanCounterRequest req = new QuerySchePlanCounterRequest();
            req.setTxAcctNo(respData.getTxAcctNo());
            QuerySchePlanCounterResponse res = orderPlanService.querySchePlanCounter(req);
            if(res != null && CollectionUtils.isNotEmpty(res.getResults())){
                Map<String, String> scheStatusMap = Maps.newHashMapWithExpectedSize(16);
                for (SchePlanCounterDto scheDto : res.getResults()) {
                    String key = scheDto.getProductCode() + scheDto.getProtocolNo();
                    scheStatusMap.put(key, scheDto.getScheStatus());
                }
                
                for(CustBalDtlDto balDto : respData.getCustBalDtlList()){
                    boolean isExist = isExistPlanProcolType(balDto.getProtocolType());
                    // 定投协议(公募定投，机器人定投，智能定投，推荐组合定投，货币组合)
                    if(isExist){
                        String key = balDto.getFundCode() + balDto.getProtocolNo();
                        String scheStatus = scheStatusMap.get(key);
                        // 计划状态 :1-正常；2-暂停；3-终止
                        balDto.setScheStatus(scheStatus);
                    }
                }
            }
        }
    }
    
    /**
     * 
     * getCustBankBals:(查询客户银行卡持仓)
     * @param custNo
     * @param idNo
     * @param disCode
     * @param fundCode
     * @param bankAcct
     * @param shareType
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月17日 下午6:30:13
     */
    private QueryCustBankBalVolRespDto getCustBankBals(String custNo, String idNo, String disCode, 
            String fundCode, String bankAcct, String shareType, String operIp) throws Exception{
        
        QueryCustBankBalVolReqDto reqDto = new QueryCustBankBalVolReqDto();
        String txAcctNo = null;
        if(StringUtils.isEmpty(custNo) && StringUtils.isNotEmpty(idNo)){
            txAcctNo = getCustNo(idNo, operIp);
        }else{
            txAcctNo = custNo;
        }
        
        List<String> cpAcctNos = null;
        if(!StringUtil.isEmpty(bankAcct)) {
            reqDto.setBankAcct(bankAcct);
            QueryBankCardInfoResponse queryRes = tmsCounterOutService.queryBankCardInfo(txAcctNo, bankAcct);
            cpAcctNos = getInTransitCpAcctNos(txAcctNo, queryRes.getCpAcctNo(), disCode);
        }
        reqDto.setCustNo(txAcctNo);
        reqDto.setIdNo(idNo);
        reqDto.setDisCode(disCode);
        reqDto.setFundCode(fundCode);
        reqDto.setCpAcctNos(cpAcctNos);
        reqDto.setShareType(shareType);
        DisInfoDto disDto = new DisInfoDto();
        disDto.setDisCode(disCode);

        QueryCustBankBalVolRespDto respData = tmsFundCounterService.queryCustBankBalVol(reqDto, disDto);
        
        return respData;
    }

    /**
     * 
     * custVolMergeSubmit:(份额合并提交)
     *      <br>同一个基金之间的份额合并
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月4日 下午5:28:20
     */
    @RequestMapping("/tmscounter/custVolMergeSubmit.htm")
    public ModelAndView custVolMergeSubmit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String mergeTotalVol = request.getParameter("mergeTotalVol");
        String volOutFunds = request.getParameter("volOutFunds");
        String volInFunds = request.getParameter("volInFunds");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");

        logger.debug("MergeTransFundVolController|custVolMergeSubmit|dealAppNo:{},mergeTotalVol:{},volOutFunds:{},volInFunds:{},custInfoForm:{},transactorInfoForm:{}",
                dealAppNo, mergeTotalVol, volOutFunds, volInFunds, custInfoForm, transactorInfoForm);
        
        List<ShareMergeVolOrderReqDto> outDtoList = JSON.parseArray(volOutFunds, ShareMergeVolOrderReqDto.class);
        if(CollectionUtils.isEmpty(outDtoList)){
            throw new TmsCounterException(TmsCounterResultEnum.MERGE_OUT_ORDER_NOT_SELECTED);
        }
        
        ShareMergeVolOrderReqDto inDto = JSON.parseObject(volInFunds, ShareMergeVolOrderReqDto.class);
        if(inDto ==  null){
            throw new TmsCounterException(TmsCounterResultEnum.MERGE_IN_ORDER_NOT_SELECTED);
        }
        // 银行卡柜台不支持处理税延养老卡
        tradeValidateService.validateMergeVolBank(outDtoList, inDto);

        List<ShareMergeVolOrderReqDto> fundOutDtoList = new ArrayList<ShareMergeVolOrderReqDto>();
        List<ShareMergeVolOrderReqDto> highOutDtoList = new ArrayList<ShareMergeVolOrderReqDto>();
        for(ShareMergeVolOrderReqDto outDto : outDtoList){
            // 转出存在冻结份额， 不可以份额合并
            if(outDto.getUnconfirmedVol().compareTo(BigDecimal.ZERO) != 0){
                throw new TmsCounterException(TmsCounterResultEnum.MERGE_VOL_EXIT_UNCFMVOL);
            }
            // 存在在途
            if(outDto.getBalanceVol().compareTo(outDto.getAppVol()) != 0){
                throw new TmsCounterException(TmsCounterResultEnum.CUST_EXIST_UNCONFIRMED_VOL);
            }

            // 记录转入前份额
            outDto.setPreAppVol(inDto.getAppVol());
            
            if(ProductChannelEnum.FUND.getCode().equals(outDto.getProductChannel())){
                fundOutDtoList.add(outDto);
                
            }else if (ProductChannelEnum.HIGH_FUND.getCode().equals(outDto.getProductChannel())){
                highOutDtoList.add(outDto);
            }
        }
       
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);

        // 柜台不支持Y份额基金交易
        tradeValidateService.tradeValidate(outDtoList.get(0).getFundCode(),null);

        CounterShareMergeVolReqDto reqDto = new CounterShareMergeVolReqDto();
        reqDto.setDealAppNo(dealAppNo);
        reqDto.setTxAcctNo(custInfoDto.getCustNo());
        reqDto.setCustName(custInfoDto.getCustName());
        reqDto.setDisCode(custInfoDto.getDisCode());
        reqDto.setIdNo(custInfoDto.getIdNo());
        reqDto.setIdType(custInfoDto.getIdType());
        reqDto.setInvstType(custInfoDto.getInvstType());

        reqDto.setAppDt(transactorInfoDto.getAppDt());
        reqDto.setAppTm(transactorInfoDto.getAppTm());
        reqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        reqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        reqDto.setTransactorName(transactorInfoDto.getTransactorName());
        reqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        reqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        reqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        reqDto.setMemo(transactorInfoDto.getCheckFaildDesc());
        reqDto.setConsCode(transactorInfoDto.getConsCode());
        reqDto.setOutletCode(transactorInfoDto.getOutletCode());
        reqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        CommonUtil.setCommonOperInfo(operatorInfoCmd, reqDto);

        // 转入信息
        reqDto.setInCpAcctNo(inDto.getCpAcctNo());
        reqDto.setInProtocolNo(inDto.getProtocolNo());
        reqDto.setInProtocolType(inDto.getProtocolType());
        reqDto.setBankAcct(inDto.getBankAcct());
        reqDto.setBankCode(inDto.getBankCode());
        reqDto.setFundCode(inDto.getFundCode());
        reqDto.setFundName(inDto.getFundName());
        reqDto.setFundShareClass(inDto.getFundShareClass());
        reqDto.setTaCode(inDto.getTaCode());
        // 转入后总持有份额
        reqDto.setAppVol(new BigDecimal(mergeTotalVol));
        // 份额业务类型
        reqDto.setShareType(ShareTypeEnum.FUND_SHARE_MERGE.getCode());



        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        // 按基金维度，专户和公募产品按其中一种提交
        CounterShareMergeVolRespDto responseDto = null;
        // 公募提交
        if(fundOutDtoList.size() > 0){
            reqDto.setProductChannel(ProductChannelEnum.FUND.getCode());
            reqDto.setShareMergeVolOrderList(fundOutDtoList);
            responseDto = tmsFundCounterService.counterFundShareMergeVol(reqDto, disInfoDto);
            
            // 高端公募提交
        }else if(highOutDtoList.size() > 0){
            reqDto.setProductChannel(ProductChannelEnum.HIGH_FUND.getCode());
            reqDto.setShareMergeVolOrderList(highOutDtoList);
            responseDto = tmsCounterService.counterHighShareMergeVol(reqDto, disInfoDto);
        }

        TmsCounterResult rst = null;
        if (responseDto != null) {
            rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
        } else {
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
        }

        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }
    
    /**
     * 
     * custVolTransferSubmit:(份额迁移提交)
     *      <br>同一个银行卡之间的份额迁移
     *      
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月4日 下午5:28:20
     */
    @RequestMapping("/tmscounter/custVolTransferSubmit.htm")
    public ModelAndView custVolTransferSubmit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        
        String dealAppNo = request.getParameter("dealAppNo");
        String transOutParams = request.getParameter("transOutParams");
        String transInParams = request.getParameter("transInParams");
        String transInBank = request.getParameter("transInBank");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        // 迁移产品：0-全部 1-公募 2-高端
        String shareTransferFlag = request.getParameter("shareTransferFlag");
        // 迁移高端产品
        String highFundCodes = request.getParameter("highFundCodes");
        // 是否注销卡
        String cancelCard = request.getParameter("cancelCard");
        //在途资产信息
        String intransitAssetMemo = request.getParameter("intransitAssetMemo");
        // CRM材料
        String materialinfoForm = request.getParameter("materialinfoForm");
        logger.debug("MergeTransFundVolController|custVolTransferSubmit|dealAppNo:{},transOutParams:{},transInParams:{},transInBank:{},custInfoForm:{},transactorInfoForm:{},highFundCodes:{}",
                dealAppNo, transOutParams, transInParams, transInBank, custInfoForm, transactorInfoForm, highFundCodes);
        if (StringUtils.isEmpty(shareTransferFlag) && StringUtils.isEmpty(highFundCodes)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAM_IS_ERROR);
        }

        // 线上资料
        AuditingOrderFileCmd auditingOrderFileCmd =  null;
        if(!org.springframework.util.StringUtils.isEmpty(materialinfoForm)){
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
        }

        //  校验线上资料
        validateOrderFileStatus(auditingOrderFileCmd);

        String operIp = HttpUtil.getIpAddr(request);
        List<String> highFundCodeList = new ArrayList<>();
        if (!StringUtils.isEmpty(highFundCodes)) {
            highFundCodeList = Lists.newArrayList(highFundCodes.split(","));
        }

        // 转出卡信息
        QueryCustBankBalVolReqDto outParam = JSON.parseObject(transOutParams, QueryCustBankBalVolReqDto.class);
        // 校验是否税延卡
        tradeValidateService.tradeCardValidate(outParam.getCustNo(),outParam.getBankAcct());
        
        if(StringUtils.isEmpty(outParam.getCustNo()) && StringUtils.isNotEmpty(outParam.getIdNo())){
            String txAcctNo = getCustNo(outParam.getIdNo(), operIp);
            outParam.setCustNo(txAcctNo);
        }

        QueryCustBankBalVolRespDto outBalData = getCustBankBals(outParam.getCustNo(), outParam.getIdNo(), outParam.getDisCode(), 
                outParam.getFundCode(), outParam.getBankAcct(), outParam.getShareType(), operIp);
        if(outBalData == null || CollectionUtils.isEmpty(outBalData.getCustBalDtlList())){
            throw new TmsCounterException(TmsCounterResultEnum.CUST_OUT_BALS_IS_EMPTY);
        }
        
        Map<String, BigDecimal> preAppVolMap = Maps.newHashMap();
        // 转入银行卡信息
        QueryCustBankBalVolReqDto inParam = JSON.parseObject(transInParams, QueryCustBankBalVolReqDto.class);
        // 校验是否税延卡
        tradeValidateService.tradeCardValidate(inParam.getCustNo(),inParam.getBankAcct());

        if(StringUtils.isEmpty(inParam.getCustNo()) && StringUtils.isNotEmpty(inParam.getIdNo())){
            String txAcctNo = getCustNo(inParam.getIdNo(), operIp);
            inParam.setCustNo(txAcctNo);
        }
        QueryCustBankBalVolRespDto inBalData = getCustBankBals(inParam.getCustNo(), inParam.getIdNo(), inParam.getDisCode(), 
                inParam.getFundCode(), inParam.getBankAcct(), inParam.getShareType(), operIp);
        if(inBalData != null && CollectionUtils.isNotEmpty(inBalData.getCustBalDtlList())){
            // 计算转出份额迁移后，转入份额汇总
            for (CustBalDtlDto balDto : inBalData.getCustBalDtlList()) {
                boolean flag = ShareTransferFlagEnum.FUND.getCode().equals(shareTransferFlag) && ProductChannelEnum.FUND.getCode().equals(balDto.getProductChannel());
                if (flag || highFundCodeList.contains(balDto.getFundCode())) {
                    String key = balDto.getFundCode() + balDto.getProtocolNo();
                    preAppVolMap.put(key, balDto.getBalanceVol());
                }
            }
        }
        
        List<ShareMergeVolOrderReqDto> outDtoList = new ArrayList<ShareMergeVolOrderReqDto>(16);
        
        // 是否有公募持仓
        boolean hasFundVol = false;
        // 是否有储蓄罐持仓
        boolean hasPiggyVol = false;
        // 是否有高端持仓
        boolean hasHighVol = false;
        
        // 转出银行卡资产信息
        ShareMergeVolOrderReqDto outDto = null;
        for(CustBalDtlDto balDto : outBalData.getCustBalDtlList()){
            // 不迁移公募或高端不包含
			if (ShareTransferFlagEnum.FUND.getCode().equals(shareTransferFlag)
					&& ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType())
					|| (ShareTransferFlagEnum.HIGH.getCode().equals(shareTransferFlag) && !ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType()))
					|| (!ShareTransferFlagEnum.FUND.getCode().equals(shareTransferFlag) && ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType()))
							&& !highFundCodeList.contains(balDto.getFundCode())) {
				continue;
			}
            // 存在冻结份额， 不可以份额迁移
            if(balDto.getUnconfirmedVol().compareTo(BigDecimal.ZERO) != 0){
                throw new TmsCounterException(TmsCounterResultEnum.TRANS_VOL_EXIT_UNCFMVOL);
            }

            // 存在在途
            if(balDto.getBalanceVol().compareTo(balDto.getAvailVol()) != 0){
                throw new TmsCounterException(TmsCounterResultEnum.CUST_EXIST_UNCONFIRMED_VOL);
            }
            
            outDto = new ShareMergeVolOrderReqDto();
            outDto.setDealDtlAppNo(null);
            outDto.setFundCode(balDto.getFundCode());
            outDto.setFundName(balDto.getFundAttr());
            outDto.setFundShareClass(balDto.getFundShareClass());
            outDto.setTaCode(balDto.getTaCode());
            outDto.setProtocolNo(balDto.getProtocolNo());
            outDto.setProtocolType(balDto.getProtocolType());
            outDto.setAppVol(balDto.getAvailVol());
            outDto.setUnconfirmedVol(balDto.getUnconfirmedVol());
            outDto.setProductChannel(balDto.getProductChannel());
            outDto.setDisCode(balDto.getDisCode());
            outDto.setProductChannel(balDto.getProductChannel());
            String key = balDto.getFundCode() + balDto.getProtocolNo();
            BigDecimal preAppVol = preAppVolMap.get(key);
            if(preAppVol == null){
            	preAppVol = BigDecimal.ZERO;
            }
            outDto.setPreAppVol(preAppVol);
            outDto.setCpAcctNo(balDto.getCpAcctNo());
            outDto.setBankAcct(balDto.getBankAcct());
            outDto.setBankCode(balDto.getBankCode());
            
            if(!hasPiggyVol && ProductChannelEnum.PIGGY.getCode().equals(balDto.getProductChannel())) {
            	hasPiggyVol = true;
            }
            
            if(!hasHighVol && ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType())) {
            	hasHighVol = true;
            }
            
            if(!hasFundVol && !ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType())
                    && !ProductChannelEnum.PIGGY.getCode().equals(balDto.getProductChannel())
                    //为兼容中台 资金在途资产默认为91
                    && !TradeConstant.CAPITAL_INTRANSIT_PROTOCOL_TYPE.equals(balDto.getProtocolType())) {
            	hasFundVol = true;
            }
            outDtoList.add(outDto);
        }
        Result result = new Result(hasFundVol, hasPiggyVol, hasHighVol);

        // 转入银行卡
        ShareMergeVolOrderReqDto inDto = JSON.parseObject(transInBank, ShareMergeVolOrderReqDto.class);
        if(inDto == null){
            throw new TmsCounterException(TmsCounterResultEnum.TRANS_IN_ORDER_NOT_SELECTED);
        }
        
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);

        CounterShareTransferVolReqDto reqDto = new CounterShareTransferVolReqDto();
        reqDto.setHasFundVol(result.hasFundVol);
        reqDto.setHasPiggyVol(result.hasPiggyVol);
        reqDto.setHasHighVol(result.hasHighVol);
        reqDto.setDealAppNo(dealAppNo);
        reqDto.setTxAcctNo(custInfoDto.getCustNo());
        reqDto.setCustName(custInfoDto.getCustName());
        reqDto.setDisCode(custInfoDto.getDisCode());
        reqDto.setIdNo(custInfoDto.getIdNo());
        reqDto.setIdType(custInfoDto.getIdType());
        reqDto.setInvstType(custInfoDto.getInvstType());

        reqDto.setAppDt(transactorInfoDto.getAppDt());
        reqDto.setAppTm(transactorInfoDto.getAppTm());
        reqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        reqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        reqDto.setTransactorName(transactorInfoDto.getTransactorName());
        reqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        reqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        reqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        reqDto.setMemo(transactorInfoDto.getCheckFaildDesc());
        reqDto.setConsCode(transactorInfoDto.getConsCode());
        reqDto.setOutletCode(transactorInfoDto.getOutletCode());
        reqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        CommonUtil.setCommonOperInfo(operatorInfoCmd, reqDto);
        
        // 迁移转入的银行卡
        reqDto.setInCpAcctNo(inDto.getCpAcctNo());
        reqDto.setBankAcct(inDto.getBankAcct());
        reqDto.setBankCode(inDto.getBankCode());

        Map<String, CustBankModel> custBankMap = queryAllBankCardInfoSwitch(reqDto.getTxAcctNo(), outParam.getDisCode());
        List<String> outCpAcctNos = getOutCpAcctNo(outDtoList);
        CustBankModel custBankModel = getPiggyBankModelPriority(outDtoList, custBankMap);
        if (custBankModel == null) {
            custBankModel = matchCustBankModel(outCpAcctNos, custBankMap);
        }

        if(custBankModel != null) {
            reqDto.setOutBankAcct(custBankModel.getBankAcct());
            reqDto.setOutBankCode(custBankModel.getBankCode());
            reqDto.setOutCpAcctNo(custBankModel.getCpAcctNo());
        }
        reqDto.setOutCpAcctNos(outCpAcctNos);

        // 材料ID
        if(auditingOrderFileCmd != null){
            reqDto.setMaterialId(auditingOrderFileCmd.getOrderid());
        }
        
        // 份额业务类型
        reqDto.setShareType(ShareTypeEnum.FUND_SHARE_TRANSFER.getCode());

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        // set result
        List<TmsCounterResult> rstList = new ArrayList<TmsCounterResult>(16);
        
        reqDto.setShareMergeVolOrderList(outDtoList);
        reqDto.setShareTransferFlag(shareTransferFlag);
        reqDto.setHighFundCodes(highFundCodeList);
        reqDto.setCancelCard(cancelCard);
        reqDto.setIntransitAssetMemo(intransitAssetMemo);
        
        String searchDisCode = outParam.getDisCode();
        CounterShareMergeVolRespDto responseDto = tmsFundCounterService.counterFundShareTransferVol(reqDto, searchDisCode, disInfoDto);

        String dealAppNos = "";
        if(response != null){
            dealAppNos = responseDto.getDealAppNo();
        }
        // 线上资料初审通过
        tmsCounterOutService.auditingFile(operatorInfoCmd, auditingOrderFileCmd, dealAppNos);

        TmsCounterResult rst = null;
        if (responseDto != null) {
            rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
            Map<String, Object> body = new HashMap<String, Object>(1);
            body.put("outFundList", outDtoList);
            rst.setBody(body);
            rstList.add(rst);
        }

        WebUtil.write(response, rstList);
        return null;
    }

    private static class Result {
        public final boolean hasFundVol;
        public final boolean hasPiggyVol;
        public final boolean hasHighVol;

        public Result(boolean hasFundVol, boolean hasPiggyVol, boolean hasHighVol) {
            this.hasFundVol = hasFundVol;
            this.hasPiggyVol = hasPiggyVol;
            this.hasHighVol = hasHighVol;
        }
    }

    private void validateOrderFileStatus(AuditingOrderFileCmd auditingOrderFileCmd) {
        if(auditingOrderFileCmd != null
                && !org.springframework.util.StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())) {
            tmsCounterOutService.validateOrderFileStatus(auditingOrderFileCmd);
        }
    }

    /**
     * 优先查询 储蓄罐 有的
     * @param outDtoList
     * @return
     */
    private CustBankModel getPiggyBankModelPriority(List<ShareMergeVolOrderReqDto> outDtoList, Map<String, CustBankModel> custBankMap) {
         try {
             if (CollectionUtils.isEmpty(outDtoList) || MapUtils.isEmpty(custBankMap)) {
                 return null;
             }

             CustBankModel custBankModel =  outDtoList.stream()
                     .filter(dto -> ProductChannelEnum.PIGGY.getCode().equals(dto.getProductChannel()))
                     .filter(dto -> custBankMap.containsKey(dto.getCpAcctNo()))
                     .findFirst()
                     .map(dto -> custBankMap.get(dto.getCpAcctNo()))
                     .orElse(null);

             return custBankModel;
         } catch (Exception e) {
            logger.error("getPiggyBankModelPriority error", e);
         }

         return null;
    }

    private CustBankModel matchCustBankModel(List<String> outCpAcctNos, Map<String, CustBankModel> custBankMap) {
        CustBankModel custBankModel = null;
        for(String outCpAcctNo : outCpAcctNos) {
            custBankModel = custBankMap.get(outCpAcctNo);
            if(custBankModel != null) {
                break;
            }
        }

        return custBankModel;
    }

    /**
     * 资金账号列表
     * getOutCpAcctNo:资金账号列表
     * @param outDtoList
     * @return
     * <AUTHOR>
     * @date 2022年4月17日 下午12:01:21
     */
    private List<String> getOutCpAcctNo(List<ShareMergeVolOrderReqDto> outDtoList) {
        List<String> outCpAcctNos = new ArrayList<String>();
        if(CollectionUtils.isEmpty(outDtoList)) {
            return outCpAcctNos;
        }
        for(ShareMergeVolOrderReqDto dto : outDtoList) {
            if(!outCpAcctNos.contains(dto.getCpAcctNo())) {
                outCpAcctNos.add(dto.getCpAcctNo());
            }
        }
        return outCpAcctNos;
    }

    /**
     * 查询银行卡（已注销的资金账号查询不到）
     * queryAllBankCardInfoSwitch:查询银行卡（已注销的资金账号查询不到）
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2022年4月17日 上午11:57:15
     */
    private Map<String, CustBankModel> queryAllBankCardInfoSwitch(String custNo, String disCode) {
        Map<String, CustBankModel> custBankMap = new HashMap<String, CustBankModel>();
        QueryAllBankCardInfoResult queryAllBankCardInfoResult = tmsCounterOutService.queryAllBankCardInfoSwitch(custNo, disCode);
        List<CustBankModel> custBankModelList = queryAllBankCardInfoResult.getCustBankModelList();
        if(CollectionUtils.isNotEmpty(custBankModelList)) {
            for(CustBankModel custBankModel : custBankModelList) {
                custBankMap.put(custBankModel.getCpAcctNo(), custBankModel);
            }
        }
        return custBankMap;
    }

    private boolean isExistPlanProcolType(String protocolType) {
        boolean isExist = false;
        List<String> allPlanProtocolTypes = BusiProcessProtocolTypeEnum.fetchAllProtocolTypesPlan();
        for (String pt:allPlanProtocolTypes) {
            if (pt.equals(protocolType)) {
                isExist = true;
                break;
            }
        }
        return isExist;
    }
    
    
    /**
     * 获取换卡的银行卡id
     * getInTransitCpAcctNos
     * @Title: getInTransitCpAcctNos
     * @Description: 获取换卡的银行卡id
     * @param @param cpAcctNo
     * @param @param bankCardAllList
     * @param @return 参数
     * @return List<String> 返回类型
     * <AUTHOR>
     * @date 2022年4月13日 下午2:13:32
     *
     */
    private List<String> getInTransitCpAcctNos(String custNo, String cpAcctNo, String disCode) {
        QueryAllBankCardInfoResult queryAllBankCardInfoResult = tmsCounterOutService.queryAllBankCardInfo(custNo, disCode);
        List<CustBankModel> custBankModelList = queryAllBankCardInfoResult.getCustBankModelList();
        
        List<String> cpAcctNos = new ArrayList<String>();
        if(StringUtil.isEmpty(cpAcctNo) || CollectionUtils.isEmpty(custBankModelList)) {
            return cpAcctNos;
        }
        String bankAcctDigest = null; 
        for(CustBankModel model : custBankModelList) {
            if(cpAcctNo.equals(model.getCpAcctNo())) {
                bankAcctDigest = model.getBankAcctDigest();
                break;
            }
        }
        if(StringUtil.isEmpty(bankAcctDigest)) {
            return cpAcctNos;
        }
        
        for(CustBankModel model : custBankModelList) {
            if(bankAcctDigest.equals(model.getBankAcctDigest())) {
                cpAcctNos.add(model.getCpAcctNo());
            }
        }
        return cpAcctNos;
    }
    
}