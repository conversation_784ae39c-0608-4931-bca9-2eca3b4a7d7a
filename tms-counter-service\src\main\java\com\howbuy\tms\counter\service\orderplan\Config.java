package com.howbuy.tms.counter.service.orderplan;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @description:应用接口地址配置类
 * <AUTHOR>
 * @date 2020年5月18日 下午1:53:07
 * @since JDK 1.6
 */
@Configuration
@RefreshScope
public class Config{
	
    /**
     * 查询定投计划
     */
    private static final String QUERY_SCHE_PLAN_COUNTER = "/plan/querySchePlanCounter";

    /**
     * 解除定投合约
     */
    private static final String RELIEVE_PLAN = "/plan/relieve";
    /**
     * order-plan 应用地址 不要配置结尾的“/”
     */
    @Value("${common.order.plan.center.base.url}")
    private String orderPlanCenterBaseUrl;

    private List<String> baseUrls = new ArrayList<>();
    private int index = 0;

    @PostConstruct
    public void parseBaseUrls() {
        if (isEmpty(orderPlanCenterBaseUrl)) {
            throw new IllegalArgumentException("config order.plan.center.base.url can't be null");
        }
        String[] urls = orderPlanCenterBaseUrl.split(",");
        for (String url : urls) {
            // 去除末尾/
            while (url.endsWith("/")) {
                url = url.substring(0, url.length() - 1);
            }
            baseUrls.add(url);
        }
    }

    public String getQuerySchePlanForConsoleUrl() {
        return getOrderPlanCenterBaseUrl() + QUERY_SCHE_PLAN_COUNTER;
    }
    
    public String getRelievePlanUrl(){
        return getOrderPlanCenterBaseUrl() + RELIEVE_PLAN;
    }

    public synchronized String getOrderPlanCenterBaseUrl() {
        if (index >= baseUrls.size()) {
            index = 0;
        }
        String baseUrl = baseUrls.get(index);
        index++;
        return baseUrl;
    }

    private boolean isEmpty(CharSequence cs) {
        return cs == null || cs.length() == 0;
    }
}
