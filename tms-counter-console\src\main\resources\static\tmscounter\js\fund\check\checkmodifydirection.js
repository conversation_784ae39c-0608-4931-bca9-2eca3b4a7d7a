/**
 * 赎回方向审核
 */
$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();
	$("#returnBtn").on('click',function(){
		CheckModifyDirection.confirm(CounterCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckModifyDirection.confirm(CounterCheck.Succ);
	});

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckModifyDirection.checkOrder = {};
	CheckModifyDirection.init(checkId,custNo,disCode,idNo);
});

var CheckModifyDirection = {	
	init:function(checkId, custNo, disCode,idNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		
		// 设置申请订单信息
		QueryCheckOrder.queryCheckOrderById(checkId,CheckModifyDirection.queryCheckOrderByIdBack);
	}, 
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckModifyDirection.checkFaildDesc = $("#checkFaildDesc").val();
		}

		
		var reqparamters ={"checkFaildDesc":CheckModifyDirection.checkFaildDesc || '',
			"checkStatus":checkStatus,
			"checkedOrderForm":JSON.stringify(CheckModifyDirection.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckModifyDirection.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckModifyDirection.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(CheckModifyDirection.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckModifyDirection.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		
		QueryFundInfo.queryFundInfo(CheckModifyDirection.checkOrder.fundCode,false);
		
		if($("#transferTubeBusiType").length > 0){
			$("#transferTubeBusiType").val(CheckModifyDirection.checkOrder.transferTubeBusiType);
		}
		
		if($("#tSellerTD").length > 0){
			Init.setTSellerCodeTD(CheckModifyDirection.checkOrder.transferTubeBusiType);
			//$("#tSellerCode").val(CheckModifyDirection.checkOrder.tSellerCode);
		}
		
		if($("#originalAppDealNo").length > 0){
			$("#originalAppDealNo").html(CheckModifyDirection.checkOrder.originalAppDealNo);
		}
		
		/**other*/
		//BodyView.setCheckOperInfoView(CheckModifyDirection.checkOrder);
	},
}
