package com.howbuy.tms.counter.dto;/**
 * Created by chentangqi on 21/6/2018.
 */

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 *
 * @description  定期产品类
 * <AUTHOR>
 * @date 21/6/2018 10:49 AM
 * @since JDK 1.6
 */
public class QueryRegularProductDto implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = -4780125546208700516L;
	/**
     * 记录号
     */
    private Long recordNo;
    /**
     * 产品代码
     */
    private String productId;
    /**
     * 产品全称
     */
    private String productFullName;
    /**
     * 产品简称
     */
    private String productAbbrName;
    /**
     * 产品大类
     1  基金产品类
     2  私募基金类
     3  私募股权类
     4  信托产品类
     5  资管产品类
     6  银行产品类
     7  保险产品类
     8  债务融资工具类
     9 衍生品类
     10 内部产品
     11 资产支持证券类
     12 收益凭证类
     13 服务产品
     99 其他类型
     */
    private String productCategory;
    /**
     * 发行状态
     0 募集前状态
     1 募集期
     2 开放期
     3 封闭期
     4 清盘
     */

    private String issueState;
    /**
     * 风险级别
     1 低
     2 中低
     3 中
     4 中高
     5 高
     */
    private String riskLevel;
    /**
     * 币种0 人民币
     */
    private String currency;
    /**
     * 收益结构
     1 固定型
     2 固定+浮动型
     3 浮动型
     4 二元结构型
     5 其他型
     */
    private String incomeStructure;
    /**
     * 预计（固定）收益率（%）
     */
    private BigDecimal fixedIncomeRate;
    /**
     * 浮动收益率范围最小
     */
    private BigDecimal incomeRateScopeMin;
    /**
     * 浮动收益率范围最大
     */
    private BigDecimal incomeRateScopeMax;

    /**
     * 浮动收益率范围类别1：或，2：至
     */
    private String incomeRateScopeType;
    /**
     * 实际浮动收益率
     */
    private BigDecimal incomeRate;
    /**
     * 预约开始日期yyyyMMdd
     */
    private String scheStartDate;
    /**
     * 预约开始时间
     */
    private String scheStartTime;
    /**
     * 预约结束日期yyyyMMdd
     */
    private String scheEndDate;
    /**
     * 预约结束时间
     */
    private String scheEndTime;
    /**
     * 发行开始日期yyyyMMdd
     yyyyMMdd
     yyyyMMdd
     yyyyMMdd
     yyyyMMdd
     */
    private String depositBeginDate;
    /**
     * 发行结束日期yyyyMMdd
     */
    private String depositEndDate;
    /**
     * 成立日期（存续期起始日）yyyyMMdd
     */
    private String productFoundedDt;
    /**
     * 终止日期（存续期截至日）yyyyMMdd
     */
    private String productEntDt;
    /**
     * 观察开始日期
     */
    private String observeStartDate;
    /**
     * 观察结束日期
     */
    private String observeEndDate;
    /**
     * 起息日（存续期截至日）yyyyMMdd
     */
    private String incomeStartDt;
    /**
     * 兑付日
     */
    private String payDate;
    /**
     * 管理人
     */
    private String mgrCode;
    /**
     * 管理人简称
     */
    private String mgrAbbrName;
    /**
     * 托管人
     */
    private String trusteeCode;
    /**
     * 托管人简称
     */
    private String trusteeAbbrName;
    /**
     * 产品规模（分）
     */
    private BigDecimal productScale;
    /**
     * 产品购买人数上限
     */
    private Long productPlacesMax;
    /**
     * 产品极差（分）
     */
    private BigDecimal productRange;
    /**
     * 发行面值（元）
     */
    private BigDecimal issueDenomination;
    /**
     * 产品净值（元）
     */
    private BigDecimal nav;
    /**
     * 产品合同链接
     */
    private String contractUrl;
    /**
     * 产品募集说明书链接
     */
    private String instructionUrl;
    /**
     * 是否定向产品1：定向，2：非定向
     */
    private String isFixedLimit;
    /**
     * 是否支持预约1：支持，2：不支持
     */
    private String isSupportSche;
    /**
     * 风险揭示书链接
     */
    private String riskRevealUrl;
    /**
     * ta代码
     */
    private String taCode;
    /**
     * 备注
     */
    private String memo;
    /**
     * 记录状态：0、有效，1-无效
     */
    private String recStat;
    /**
     * 复核标志 0-未复核；1-已复核
     */
    private String checkFlag;
    /**
     * 创建日期
     */
    private Date createDtm;
    /**
     * 更新日期时间
     */
    private Date updateDtm;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人
     */

    private String modifier;
    /**
     * 复核人
     */
    private String checker;

    /**
     * 单笔最低即最低认购金额
     */
    private BigDecimal singleMinAppAmt;

    public Long getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(Long recordNo) {
        this.recordNo = recordNo;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public String getIssueState() {
        return issueState;
    }

    public void setIssueState(String issueState) {
        this.issueState = issueState;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getIncomeStructure() {
        return incomeStructure;
    }

    public void setIncomeStructure(String incomeStructure) {
        this.incomeStructure = incomeStructure;
    }

    public BigDecimal getFixedIncomeRate() {
        return fixedIncomeRate;
    }

    public void setFixedIncomeRate(BigDecimal fixedIncomeRate) {
        this.fixedIncomeRate = fixedIncomeRate;
    }

    public BigDecimal getIncomeRateScopeMin() {
        return incomeRateScopeMin;
    }

    public void setIncomeRateScopeMin(BigDecimal incomeRateScopeMin) {
        this.incomeRateScopeMin = incomeRateScopeMin;
    }

    public BigDecimal getIncomeRateScopeMax() {
        return incomeRateScopeMax;
    }

    public void setIncomeRateScopeMax(BigDecimal incomeRateScopeMax) {
        this.incomeRateScopeMax = incomeRateScopeMax;
    }

    public BigDecimal getIncomeRate() {
        return incomeRate;
    }

    public void setIncomeRate(BigDecimal incomeRate) {
        this.incomeRate = incomeRate;
    }

    public String getIncomeRateScopeType() {
        return incomeRateScopeType;
    }

    public void setIncomeRateScopeType(String incomeRateScopeType) {
        this.incomeRateScopeType = incomeRateScopeType;
    }

    public String getScheStartDate() {
        return scheStartDate;
    }

    public void setScheStartDate(String scheStartDate) {
        this.scheStartDate = scheStartDate;
    }

    public String getScheStartTime() {
        return scheStartTime;
    }

    public void setScheStartTime(String scheStartTime) {
        this.scheStartTime = scheStartTime;
    }

    public String getScheEndDate() {
        return scheEndDate;
    }

    public void setScheEndDate(String scheEndDate) {
        this.scheEndDate = scheEndDate;
    }

    public String getScheEndTime() {
        return scheEndTime;
    }

    public void setScheEndTime(String scheEndTime) {
        this.scheEndTime = scheEndTime;
    }

    public String getDepositBeginDate() {
        return depositBeginDate;
    }

    public void setDepositBeginDate(String depositBeginDate) {
        this.depositBeginDate = depositBeginDate;
    }

    public String getDepositEndDate() {
        return depositEndDate;
    }

    public void setDepositEndDate(String depositEndDate) {
        this.depositEndDate = depositEndDate;
    }

    public String getProductFoundedDt() {
        return productFoundedDt;
    }

    public void setProductFoundedDt(String productFoundedDt) {
        this.productFoundedDt = productFoundedDt;
    }

    public String getProductEntDt() {
        return productEntDt;
    }

    public void setProductEntDt(String productEntDt) {
        this.productEntDt = productEntDt;
    }

    public String getObserveStartDate() {
        return observeStartDate;
    }

    public void setObserveStartDate(String observeStartDate) {
        this.observeStartDate = observeStartDate;
    }

    public String getObserveEndDate() {
        return observeEndDate;
    }

    public void setObserveEndDate(String observeEndDate) {
        this.observeEndDate = observeEndDate;
    }

    public String getIncomeStartDt() {
        return incomeStartDt;
    }

    public void setIncomeStartDt(String incomeStartDt) {
        this.incomeStartDt = incomeStartDt;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public BigDecimal getProductScale() {
        return productScale;
    }

    public void setProductScale(BigDecimal productScale) {
        this.productScale = productScale;
    }

    public Long getProductPlacesMax() {
        return productPlacesMax;
    }

    public void setProductPlacesMax(Long productPlacesMax) {
        this.productPlacesMax = productPlacesMax;
    }

    public BigDecimal getProductRange() {
        return productRange;
    }

    public void setProductRange(BigDecimal productRange) {
        this.productRange = productRange;
    }

    public BigDecimal getIssueDenomination() {
        return issueDenomination;
    }

    public void setIssueDenomination(BigDecimal issueDenomination) {
        this.issueDenomination = issueDenomination;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getContractUrl() {
        return contractUrl;
    }

    public void setContractUrl(String contractUrl) {
        this.contractUrl = contractUrl;
    }

    public String getInstructionUrl() {
        return instructionUrl;
    }

    public void setInstructionUrl(String instructionUrl) {
        this.instructionUrl = instructionUrl;
    }

    public String getIsFixedLimit() {
        return isFixedLimit;
    }

    public void setIsFixedLimit(String isFixedLimit) {
        this.isFixedLimit = isFixedLimit;
    }

    public String getIsSupportSche() {
        return isSupportSche;
    }

    public void setIsSupportSche(String isSupportSche) {
        this.isSupportSche = isSupportSche;
    }

    public String getRiskRevealUrl() {
        return riskRevealUrl;
    }

    public void setRiskRevealUrl(String riskRevealUrl) {
        this.riskRevealUrl = riskRevealUrl;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getMgrCode() {
        return mgrCode;
    }

    public void setMgrCode(String mgrCode) {
        this.mgrCode = mgrCode;
    }

    public String getTrusteeCode() {
        return trusteeCode;
    }

    public void setTrusteeCode(String trusteeCode) {
        this.trusteeCode = trusteeCode;
    }

    public String getProductFullName() {
        return productFullName;
    }

    public void setProductFullName(String productFullName) {
        this.productFullName = productFullName;
    }

    public String getProductAbbrName() {
        return productAbbrName;
    }

    public void setProductAbbrName(String productAbbrName) {
        this.productAbbrName = productAbbrName;
    }

    public String getMgrAbbrName() {
        return mgrAbbrName;
    }

    public void setMgrAbbrName(String mgrAbbrName) {
        this.mgrAbbrName = mgrAbbrName;
    }

    public String getTrusteeAbbrName() {
        return trusteeAbbrName;
    }

    public void setTrusteeAbbrName(String trusteeAbbrName) {
        this.trusteeAbbrName = trusteeAbbrName;
    }

    public BigDecimal getSingleMinAppAmt() {
        return singleMinAppAmt;
    }

    public void setSingleMinAppAmt(BigDecimal singleMinAppAmt) {
        this.singleMinAppAmt = singleMinAppAmt;
    }

}
