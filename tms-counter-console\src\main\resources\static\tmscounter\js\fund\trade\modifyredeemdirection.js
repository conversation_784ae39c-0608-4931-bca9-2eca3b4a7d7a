/**
* 修改回款方向
* <AUTHOR>
* @date 2023-05-23 14:02
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();

	ModifyRedeemDirection.init();
	ModifyRedeemDirection.currDate = '';
	
});



var ModifyRedeemDirection = {
	init:function(){

		this.getWorkDay();

		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});


		/**
		 * 双击客户号查询客户信息
		 */
		$("#submitModifyBtn").on('click',function(){
			ModifyRedeemDirection.submitModify(null);
		});


		/**
		 * 查询客户基本信息 查询修改回款方向订单
		 */
		$("#queryDealListBtn").on('click',function(){
			QueryModifyDirection.queryModifyDeal();
		});


	},


	/**
	 * 获取当前服务器端日期
	 */
	getWorkDay:function(){
		var currDate ='';
		var uri = TmsCounterConfig.QUERY_CURR_WORK_DAY_URL;
		var paramters = CommonUtil.buildReqParams(uri);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var respCode = data.code || '';
			var body = data.body || {};

			if(CommonUtil.isSucc(respCode)){
				currDate = body.currDate || '';
				$("#appBeginDt").val(currDate);
				$("#appEndDt").val(currDate);
			}
		});
		return currDate;
	},
	
	/***
	 * 修改回款方向
	 */	
	submitModify : function(dealAppNo){

		if ($("input[class='selectDeal']:checked").length == 0) {
			CommonUtil.layer_tip("请选择一条数据后再提交");
			return false;
		}

		if ($("input[class='selectDeal']:checked").length > 1) {
			CommonUtil.layer_tip("只能选择一条数据提交");
			return false;
		}

		if ($("#uncheckCount").val() > 0) {
			CommonUtil.layer_tip("该订单已存在一笔待审核记录");
			return false;
		}

		var dealNo = $($("input[class='selectDeal']:checked").get(0)).parent().siblings().eq(0).html();
		var afterModify = $("#afterModify").val();
		if(CommonUtil.isEmpty(afterModify)){
			CommonUtil.layer_tip("请选择修改后回款方向");
			return false;
		}

		var beforeModify = $("#beforeModifyId").val();
		if(beforeModify == afterModify){
			CommonUtil.layer_tip("修改前和修改后的回款方式不能相同");
			return false;
		}

		// 提交
		var uri = TmsCounterConfig.APPLY_MODIFY_REDEEM_DIRECTION ||  {};
		var reqparamters = {
			"dealNo": dealNo,
			"dealAppNo": CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
			"custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
			"redeemDirection": afterModify,
			};
		//console.log(reqparamters);
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, this.callBack);
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confirmModifyBtn");
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.enabledBtn("confirmModifyBtn");
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	}

};



