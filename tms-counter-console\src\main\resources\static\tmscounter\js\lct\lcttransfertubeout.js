/**
*理财通转托管转出
* <AUTHOR>
* @date 2018-10-09 14:02
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();
	TransfertubeOut.init();
	TransfertubeOut.currDate = '';
});

var TransfertubeOut = {
	init:function(){
		/**
		 * 确认转托管转出
		 */
		var formAction = $("#transfertubeOutForm").attr("action");
		if(formAction == 'add'){
			$("#confimTransOutBtn").on('click',function(){
				TransfertubeOut.confirm(formAction, null, null);
			});
		}
		
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询客户基本信息
		 */
		$("#queryCustInfoBtn").on('click',function(){
			var disCode = $("#selectLctDisCode").val();
			QueryCustInfo.queryCustInfo(null, null, disCode);
		});
		
		/**
		 * 查询基金信息和基金持仓
		 */
		$(".searchIcon").on('click',function(){
			QueryFundInfo.queryFundInfo();
			
			setTimeout(function(){
				// 查询客户基金持仓
				TransfertubeOut.queryCustBalInfo();
			}, 300);
			
		});	
		
		/**
		 * 业务类型与对方销售人代码联动
		 */
		Init.selectBoxTransferTubeBusiType();
	},
	
	/**
	 * 查询客户基金持仓
	 */
	queryCustBalInfo:function(){
		var custNo = $("#custNo").val();
		var idNo = $("#idNo").val();
		if(CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo)){
			CommonUtil.layer_tip("请先输入客户号或证件号");
			return false;
		}
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入基金代码");
			return false;
		}
		
		var searchDisCode = $("#selectLctDisCode").val();
		if(CommonUtil.isEmpty(searchDisCode)){
			CommonUtil.layer_tip("请选择分销机构");
			return false;
		}
		
		// 查询客户基金持仓
		TransfertubeOut.queryTransferOutHodlInfo(custNo, idNo, searchDisCode, fundCode);
	},
	
	/**
	 * 查询客户转托管转出基金持仓
	 */
	queryTransferOutHodlInfo:function(custNo, idNo, disCode, fundCode){
		var  uri= TmsCounterConfig.LCT_QUERY_TRANS_OUT_FUND_CUST_HODL_INFO_URL ||  {};
		var reqparamters = {};
		reqparamters.custNo = custNo;
		reqparamters.idNo = idNo;
		reqparamters.disCode = disCode;
		reqparamters.fundCode = fundCode;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, TransfertubeOut.queryVolBalCallBack);
	},
	
	queryVolBalCallBack:function(data){
		var bodyData = data.body || {};

		// 持仓信息
		TransfertubeOut.custBalDtlList = bodyData || [];
		
		// 转出信息
		$("#transOutCustBals").empty();
		if(TransfertubeOut.custBalDtlList.length <=0){
			var trHtml = '<tr><td colspan="10">没有查询到可以转出份额信息</td></tr>';
			$("#transOutCustBals").append(trHtml);
			return false;
			
		}else{
			var totalOutVol = 0;// 总份额
			$(TransfertubeOut.custBalDtlList).each(function(index,element){
				totalOutVol = Number(totalOutVol) + Number(element.balanceVol);
				
				var tdList = [];
				tdList.push('<td><input class="selectTransOutCustBal" id="selectTransOutCustBal_'+index+'" name="checkTransOutBal" type="checkbox" data-index="' + index + '"></input></td>');
				tdList.push('<td>'+CommonUtil.formatData(element.fundCode)+'</td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.availVol)+'</td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.unconfirmedVol)+'</td>');
				
				// 转出份额
				tdList.push('<td><input type="text" class="outVolClass" name="outVol_'+index+'" id="outVol_'+index+'" isnull="false" datatype="s" errormsg="转出份额" placeholder="请输入" onkeyup="TransfertubeOut.validatorOutVol('+index+','+element.availVol+', this);"></td>');
				tdList.push('<td><input type="text" name="outVolCapital_'+index+'" id="outVolCapital_'+index+'" isnull="false" datatype="s" errormsg="转出份额" readonly="true"></td>');
				
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.LCT_BANK_MAP, '888')+'</td>');
				tdList.push('<td>'+CommonUtil.formatData(element.lctAcctNo)+'</td>');
                tdList.push('<td>'+CommonUtil.formatData(element.protocolNo)+'</td>');
				
				var trAppendHtml = '<tr class="text-c">'+tdList.join() +'</tr>';
				$("#transOutCustBals").append(trAppendHtml);
				
				// 绑定点击事件
				$(".selectTransOutCustBal").on('click',function(){
					var selOutIndex = $(this).attr('data-index');
					var selAvailVol = TransfertubeOut.custBalDtlList[selOutIndex].availVol;
					if(selAvailVol == '0'){
						$("#selectTransOutCustBal_"+selOutIndex).attr("checked", false);
						CommonUtil.layer_tip("可用份额为0，不可转出！");
						return false;
					}
				});
				
			});
			
			// 设置录入中的总份额
			$("#totalVol").val(CommonUtil.formatAmount(totalOutVol));
		}
	},
	
	/**
	 * 校验输入份额(事件:onkeyup)
	 */
	validatorOutVol : function(index, availVol, thisObj){
		var transferTubeBusiType = $("#transferTubeBusiType").val();
		// console.log(availVol + " "+ thisObj.value);
		var outVol = thisObj.value;
		if(transferTubeBusiType == '1'){
			if(!/^[0-9]+$/.test(outVol)){CommonUtil.layer_tip('只能输入整数');thisObj.value='';}
		}
		
		var cnOutVol = CommonUtil.digit_uppercase(outVol);
		$("#outVolCapital_"+index).val(cnOutVol.replace('元', '份'));
		
		if(outVol > availVol ){
			CommonUtil.layer_tip("转出份额不能大于可用份额");
			$(thisObj).css("border-color","red");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		} else{
			$(thisObj).css("border-color","");
		}
	},
	
	/***
	 * 确认转托管转出
	 */	
	confirm : function(action, dealAppNo, checkDtlOrder){
		CommonUtil.disabledBtn("confimTransOutBtn");

		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入转出基金代码");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var transferTubeBusiType = $("#transferTubeBusiType").val();
		var tSellerCode = $("#tSellerCode").val();
		if(transferTubeBusiType == '2' && tSellerCode == '304'){
			CommonUtil.layer_tip("场外跨销售机构, 对方销售人代码不能输入好买304！");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var tOutletCode = $("#tOutletCode").val();
		if(CommonUtil.isEmpty(tOutletCode)){
			CommonUtil.layer_tip("请输入对方网点/席位号");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		 /**
	     * 转托管方式,1-一次转托管；2-两次转托管
	     */
		var chgTrusteeMode = QueryFundInfo.fundInfo.chgTrusteeMode;
		var tSellerTxAcctNo = $("#tSellerTxAcctNo").val();
		if(CommonUtil.isEmpty(tSellerTxAcctNo) && chgTrusteeMode == "1"){
			CommonUtil.layer_tip("转出基金为一次转托管，对方销售人处投资者基金交易账号不能为空！");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#transfertubeOutForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		// 转出录入信息
		var fundInfoForm = JSON.stringify(QueryFundInfo.fundInfo);
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var transfertubeOutForm = $("#transfertubeOutForm").serializeObject();
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		
		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		transfertubeOutForm.appDtm = transfertubeOutForm.appDt +'' + transfertubeOutForm.appTm;
		if(CommonUtil.isEmpty(transfertubeOutForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		if(!Valid.valiadTradeTime(transfertubeOutForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var selCount = 0;
		var selTotalOutVol = 0;
		var volOutFunds = [];
		
		// 转出的基金份额
		if(action == "add"){
			var selectedOutCheckboxs = $("#transOutCustBals").find("input[type='checkbox'][name='checkTransOutBal']:checked");
			if(selectedOutCheckboxs.length <= 0){
				CommonUtil.layer_tip("请先选择转出份额信息");
				CommonUtil.enabledBtn("confimTransOutBtn");
				return false;
			}
			
			$(selectedOutCheckboxs).each(function(index,obj){
				var selOutIndex = $(obj).attr('data-index');
				var outVolFund = {};
				outVolFund.selIndex      = selOutIndex;
				outVolFund.dealDtlAppNo  = null;
				outVolFund.appVol        = $('#outVol_'+selOutIndex).val();//转出份额
				outVolFund.cpAcctNo		 = TransfertubeOut.custBalDtlList[selOutIndex].custBankId;
				outVolFund.bankAcct      = TransfertubeOut.custBalDtlList[selOutIndex].lctAcctNo;
				outVolFund.bankCode      = "888";
				outVolFund.preAppVol	 = TransfertubeOut.custBalDtlList[selOutIndex].availVol;//转出前份额
				outVolFund.preFrznVol	 = TransfertubeOut.custBalDtlList[selOutIndex].unconfirmedVol;//转出前冻结份额
                outVolFund.protocolNo	 = TransfertubeOut.custBalDtlList[selOutIndex].protocolNo;//协议号
				outVolFund.productChannel= QueryFundInfo.fundInfo.productChannel;//转出产品渠道
			
				selTotalOutVol = Number(selTotalOutVol) + Number(outVolFund.appVol);
				volOutFunds.push(outVolFund);
			});
			selCount = selectedOutCheckboxs.length;
			
		} else if(action == "update"){
			
			$(checkDtlOrder).each(function(index,obj){
				var outVolFund = {};
				outVolFund.selIndex      = index;
				outVolFund.dealDtlAppNo  = obj.dealDtlAppNo;
				outVolFund.appVol        = $('#outVol_'+index).val();//转出份额
				outVolFund.cpAcctNo		 = obj.cpAcctNo;
				outVolFund.bankAcct      = obj.bankAcct;
				outVolFund.bankCode      = obj.bankCode;
				outVolFund.protocolNo 	 = obj.protocolNo;
				outVolFund.protocolType	 = obj.protocolType;
				outVolFund.preAppVol	 = obj.preAppVol;//转出前份额
				outVolFund.preFrznVol	 = obj.preFrznVol;//转出前冻结份额
				outVolFund.productChannel= QueryFundInfo.fundInfo.productChannel;//转出产品渠道
			
				selTotalOutVol = Number(selTotalOutVol) + Number(CommonUtil.unFormatAmount(outVolFund.appVol));
				volOutFunds.push(outVolFund);
			});
			selCount = checkDtlOrder.length;
		}
		
		// 校验提交的转托管基金转出份额是否为空
		var checkFlag = true;
		$(".outVolClass").css("border-color","");
		$(volOutFunds).each(function(index,obj){
			var outVol = obj.appVol;
			var selIndex = obj.selIndex;
			if(isEmpty(outVol)){
				CommonUtil.layer_tip("转出份额不能为空");
				$("#outVol_"+selIndex).css("border-color","red");
				checkFlag = false;
			}
		});
		if(!checkFlag){
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		//console.log(selTotalOutVol);
		var statisHtml = '转出笔数：'+selCount+'笔；总转出份额：'+CommonUtil.formatAmount(selTotalOutVol)+'份，大写：'+CommonUtil.digit_uppercase(selTotalOutVol).replace('元', '份')+"。";
		var tipmsg = statisHtml + "<br/>是否确认提交？";
		
		//询问框
		layer.confirm(tipmsg, {  
		  btn: ['确认','取消']
		}, function(index){  
			
			CommonUtil.disabledBtn("confimTransOutBtn");
			
			// 提交
			var uri = TmsCounterConfig.LCT_TRANSFERTUBE_OUT_FUND_CONFIRM_URL ||  {};
			var reqparamters = {"dealAppNo":CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
					"fundInfoForm": fundInfoForm,
					"custInfoForm":custInfoForm,
					"transOutVolCmd": JSON.stringify(volOutFunds),
					"transfertubeOutForm": JSON.stringify(transfertubeOutForm),
					"transactorInfoForm":JSON.stringify(transactorInfoForm)
				};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, TransfertubeOut.callBack);
			
		}, function(){  
			layer.closeAll();
			CommonUtil.enabledBtn("confimTransOutBtn");
		}); 
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confimTransOutBtn");
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.enabledBtn("confimTransOutBtn");
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	}

};



