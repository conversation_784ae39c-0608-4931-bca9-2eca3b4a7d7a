package com.howbuy.tms.counter.common;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.Filter;
import com.alibaba.dubbo.rpc.Invocation;
import com.alibaba.dubbo.rpc.Invoker;
import com.alibaba.dubbo.rpc.Result;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.dubbo.rpc.RpcException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.howbuy.cc.center.assetservice.pubfund.response.CurrentAssetResponse;
import com.howbuy.common.facade.BaseRequest;
import com.howbuy.fbs.fbscommon.facade.facade.FundBaseRequest;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.trace.RequestChainTrace;
import com.howbuy.tms.common.utils.MarkerUtil;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Activate(group = Constants.CONSUMER)
public class TmsCounterFilter implements Filter {
    private static final Logger LOGGER = LogManager.getLogger(TmsCounterFilter.class);

    private static final SerializerFeature[] SERIALIZER = { SerializerFeature.WriteClassName, SerializerFeature.WriteMapNullValue,
            SerializerFeature.WriteNullStringAsEmpty };

    @SuppressWarnings("rawtypes")
	@Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
		// 灰度测试支持
		RpcContext.getContext().setAttachment("isABTest", TradeParamLocalUtils.getIsABTest());
    	
    	// 账户中心, 后台交易联机 dubbo接口, 公共参数设置
        if (invocation.getArguments() != null && invocation.getArguments().length > 0) {
            Object obj = invocation.getArguments()[0];
            if (obj != null) {
            	String reqIp = "127.0.0.1";
                String disCode = TradeParamLocalUtils.getDisCode();
                disCode = StringUtils.isEmpty(disCode)? DisCodeEnum.HM.getCode() : disCode;

                String outletCode = TradeParamLocalUtils.getOutletCode();
                outletCode = StringUtils.isEmpty(outletCode)? "H20131104" : outletCode;

                String txChannel = TradeParamLocalUtils.getTxChannel();
                txChannel = StringUtils.isEmpty(txChannel)? TxChannelEnum.COUNTER.getCode() : txChannel;
                
                String appDt = TradeParamLocalUtils.getAppDt();
                String appTm = TradeParamLocalUtils.getAppTm();
                if (obj instanceof FundBaseRequest) {
                    // 后台交易联机
                    FundBaseRequest request = (FundBaseRequest) obj;
                    if (StringUtils.isEmpty(request.getDisCode())) {
                        request.setDisCode(disCode);
                    }
                    if (StringUtils.isEmpty(request.getOutletCode())) {
                        request.setOutletCode(outletCode);
                    }
                    if (StringUtils.isEmpty(request.getTradeChannel())) {
                        request.setTradeChannel(txChannel);
                    }
                    if (StringUtils.isEmpty(request.getAppDt())) {
                        request.setAppDt(appDt);
                    }
                    if (StringUtils.isEmpty(request.getAppTm())) {
                        request.setAppTm(appTm);
                    }
                    if (StringUtils.isEmpty(request.getOperIp())) {
                        request.setOperIp(reqIp);
                    }
                }
                if (obj instanceof BaseRequest) {
                    // 账户中心
                    BaseRequest request = (BaseRequest) obj;
                    if (StringUtils.isEmpty(request.getDisCode())) {
                        request.setDisCode(disCode);
                    }
                    if (StringUtils.isEmpty(request.getOutletCode())) {
                        request.setOutletCode(outletCode);
                    }
                    if (StringUtils.isEmpty(request.getTradeChannel())) {
                        request.setTradeChannel(txChannel);
                    }
                    if (StringUtils.isEmpty(request.getAppDt())) {
                        request.setAppDt(appDt);
                    }
                    if (StringUtils.isEmpty(request.getAppTm())) {
                        request.setAppTm(appTm);
                    }
                    if (StringUtils.isEmpty(request.getOperIp())) {
                        request.setOperIp(reqIp);
                    }
                }
                
                if (obj instanceof com.howbuy.tms.common.client.BaseRequest) {
                	com.howbuy.tms.common.client.BaseRequest request = (com.howbuy.tms.common.client.BaseRequest) obj;
                    if (StringUtils.isEmpty(request.getDisCode())) {
                        request.setDisCode(disCode);
                    }
                    if (StringUtils.isEmpty(request.getOutletCode())) {
                        request.setOutletCode(outletCode);
                    }
                    if (StringUtils.isEmpty(request.getTxChannel())) {
                        request.setTxChannel(txChannel);
                    }
                    if (StringUtils.isEmpty(request.getAppDt())) {
                        request.setAppDt(appDt);
                    }
                    if (StringUtils.isEmpty(request.getAppTm())) {
                        request.setAppTm(appTm);
                    }
                    if (StringUtils.isEmpty(request.getOperIp())) {
                        request.setOperIp(reqIp);
                    }
                }
            }
        }
        // dubbo接口请求日志
        String interfaceName = invoker.getInterface().getSimpleName();
        String methodName = invocation.getMethodName();
        
        // trace
		RequestChainTrace.deliverTraces();
		
        //关联RPC请求返回
        //同一查询多次调用外部相同接口
        String txKeyTime =  UUID.randomUUID().toString().replaceAll("-", "");
        
        List<Object> args = new ArrayList<Object>();
        if (invocation.getArguments() != null) {
            for (Object obj : invocation.getArguments()) {
                if (obj == null) {
                    continue;
                }
                args.add(obj);
            }
        }
        if(!RemoteConstants.UploadVoucherFileFacade.equals(interfaceName)){
            LOGGER.info("DUBBO request:[txKeyTime:{}, txInterface:{}.{}, address:{}]{}", new Object[] { txKeyTime, interfaceName, methodName, RpcContext.getContext().getRemoteAddressString(), JSON.toJSONString(args, SERIALIZER) });
        }

        long start = System.currentTimeMillis();
        Result invoke = invoker.invoke(invocation);
        long cost = System.currentTimeMillis() - start;
        Object obj = invoke.getValue();
        try {
            if (obj != null) {
                String code = "--";
                String desc = "--";
                Object objV = invoke.getValue();
                // 大对象日志优化，对象中List长度大于100的不打印日志
                if (objV instanceof List && ((List) objV).size() > 100) {
                    LOGGER.info("DUBBO response:[txKeyTime:{}, txInterface:{}.{}, code:{}, desc:{}, cost:{}]{}", new Object[] { txKeyTime, interfaceName,
                            methodName, code, desc, cost, ((List) objV).size() });
                } else if (objV instanceof CurrentAssetResponse && ((CurrentAssetResponse) objV).getIncomeList() != null
                        && ((CurrentAssetResponse) objV).getIncomeList().size() > 100) {
                    LOGGER.info("DUBBO response:[txKeyTime:{}, txInterface:{}.{}, code:{}, desc:{}, cost:{}]{}", new Object[] { txKeyTime, interfaceName,
                            methodName, code, desc, cost, ((CurrentAssetResponse) objV).getIncomeList().size() });
                } else {
                    LOGGER.info("DUBBO response:[txKeyTime:{}, txInterface:{}.{}, code:{}, desc:{}, cost:{}]{}", new Object[] { txKeyTime, interfaceName,
                            methodName, code, desc, cost, JSON.toJSONString(invoke.getValue(), SERIALIZER) });
                }
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }

        if (cost >= 500) {
            interfaceName = invoker.getInterface().getName() + "." + methodName;
            LOGGER.error(MarkerUtil.getMarker(MarkerUtil.COMMON_CALL_OUTSERVICE_ERROR_KEY + ":" + interfaceName), "cost:{}", cost);
        }

        return invoke;
    }

}
