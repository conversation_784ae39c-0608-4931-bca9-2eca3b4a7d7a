/**
*零售柜台份额合并/迁移审核
*<AUTHOR>
*@date 2018-05-19 10:12
*/
$(function(){
	Init.init();
	CounterVolCheck.init();
	
	CounterVolCheck.custInfo = {};
	CounterVolCheck.checkOrders = [];
	//通过
	CounterVolCheck.Succ = '1';
	//驳回
	CounterVolCheck.Faild = '3';
	//废单
	CounterVolCheck.Abolish = '4';
	
	var selectTxCodeHtml = '<option value="">全部</option>';
	$.each(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP,function(name,value){
		selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
	});
	$("#selectTxCode").empty();
	$("#selectTxCode").html(selectTxCodeHtml);
	
	//查询待审核订单
	CounterVolCheck.queryOrderInfo();
});
 var CounterVolCheck = {
	init:function(){
		$("#queryBtn").on('click',function(){
			CounterVolCheck.queryOrderInfo();
		});
	},
	
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_URL  ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
        var txCodeList = [];
        txCodeList.push(CONSTANTS.MERGE_VOL_TXCODE);
        txCodeList.push(CONSTANTS.TRANS_VOL_TXCODE);
        queryOrderCondition.txCodeList = txCodeList;

		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		reqparamters.checkFlag = 0; //只查未审核
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, CounterVolCheck.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		CounterVolCheck.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];
		$("#rsList").empty();
		if(CounterVolCheck.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无待审核记录</td></tr>';
			$("#rsList").append(trHtml);
			
		}else {
			var staticData = bodyData.counterQueryOrderRespDto || {};
			$("#staticId").html("当页小计：申请笔数【"+CounterVolCheck.checkOrders.length+"】申请份额【"+CommonUtil.formatAmount(staticData.pageAppVol)+"】 合计：申请笔数【"+staticData.totalCount+"】申请份额【"+CommonUtil.formatAmount(staticData.totalAppVol)+"】");
			
			var i = 1;
			$(CounterVolCheck.checkOrders).each(function(index,element){
				var trList = [];
				trList.push('<td>'+(i++)+'</td>');
				trList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP, element.txCode, '')+'</td>');
				trList.push('<td>'+CommonUtil.formatData(element.dealAppNo)+'</td>');
				trList.push('<td>'+CommonUtil.formatData(element.txAcctNo, '--')+'</td>');
				trList.push('<td>'+CommonUtil.formatData(element.custName)+'</td>');
				if(element.invstType == '0'){//属于机构
					trList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, '')+'</td>');
				}
				if(element.invstType == '1'){
					trList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, '')+'</td>');
				}
				if(element.invstType == '2'){
					trList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, '')+'</td>');
				}
				trList.push('<td>'+CommonUtil.formatData(element.idNo,'--')+'</td>');  
				trList.push('<td>'+CommonUtil.formatData(element.fundCode,'--')+'</td>');
				trList.push('<td>'+CommonUtil.formatData(element.fundName,'--')+'</td>');
				
				if(element.appVol > 0){
					trList.push('<td>'+CommonUtil.formatData(CommonUtil.formatAmount(element.appVol))+'</td>');
				}else {
					trList.push('<td>'+'--'+'</td>');
				}
				trList.push('<td>'+CommonUtil.formatData(element.appDt)+'</td>');
				trList.push('<td>'+CommonUtil.formatData(element.appTm)+'</td>');
				trList.push('<td>'+CommonUtil.formatData(element.creator,'')+'</td>');
				trList.push('<td>'+'<a class="reCheck" href="javascript:void(0);" indexvalue = '+index+'>复核</a>'+'</td>');
				var trHtml = '<tr class="text-c">'+trList.join() +'</tr>';
				$("#rsList").append(trHtml);
			});
			
			//绑定审核
			$(".reCheck").off();
			$(".reCheck").on('click',function(){			
				var indexValue = $(this).attr("indexvalue");
				var checkedOrder = CounterVolCheck.checkOrders[indexValue] || {};

				var txCode = checkedOrder.txCode;
				var param = "checkId="+checkedOrder.dealAppNo+"&custNo="+checkedOrder.txAcctNo+"&disCode="+checkedOrder.disCode+"&idNo="+checkedOrder.idNo;
				
				if(CONSTANTS.MERGE_VOL_TXCODE == txCode){
					window.open("checkmergevol.html?"+param,"_blank");
					return;
				} else if(CONSTANTS.TRANS_VOL_TXCODE == txCode){
					window.open("checktransvol.html?"+param,"_blank");
					return;
				}
			});
		}
	}
};
