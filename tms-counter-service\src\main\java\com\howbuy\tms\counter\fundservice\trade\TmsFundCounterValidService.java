/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundservice.trade;

import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.CounterCancelReqDto;
import com.howbuy.tms.counter.dto.CounterExchangeReqDto;
import com.howbuy.tms.counter.dto.CounterModifyDivReqDto;
import com.howbuy.tms.counter.dto.CounterPurchaseReqDto;
import com.howbuy.tms.counter.dto.CounterRedeemReqDto;
import com.howbuy.tms.counter.dto.CounterShareMergeVolReqDto;
import com.howbuy.tms.counter.dto.CounterShareTransferVolReqDto;
import com.howbuy.tms.counter.dto.CounterTransferTubeReqDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;

/**
 * @description:(柜台零售校验服务)
 * <AUTHOR>
 * @date 2017年4月17日 下午8:27:38
 * @since JDK 1.6
 */
public interface TmsFundCounterValidService {
    /**
     * 
     * subsOrPurValidateForFund:(零售购买校验)
     * 
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午3:55:27
     */
    public boolean subsOrPurValidate(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception;

    public boolean adviserPurValidate(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception;
        /**
         *
         * redeemValidate:(零售赎回校验)
         *
         * @param dto
         * @param disInfoDto
         * @return
         * @throws Exception
         * <AUTHOR>
         * @date 2017年9月15日 下午5:30:14
         */
    public boolean redeemValidate(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 投顾 赎回校验
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    public boolean redeemAdviserValidate(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * redeemValidate:(零售转换校验)
     * 
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午5:30:14
     */
    public boolean exchangeValidate(CounterExchangeReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * cancelOrderValidate:(零售撤单校验)
     * 
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月16日 下午7:19:42
     */
    public boolean cancelOrderValidate(CounterCancelReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * modifyDivValidate:(零售修改分红方式)
     * 
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月17日 上午11:06:49
     */
    public boolean modifyDivValidate(CounterModifyDivReqDto dto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * shareMergeVolValidate:(份额合并校验)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月8日 下午1:52:28
     */
    public boolean shareMergeVolValidate(CounterShareMergeVolReqDto dto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * shareTransferVolValidate:(份额迁移校验)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月8日 下午1:52:28
     */
    public boolean shareTransferVolValidate(CounterShareTransferVolReqDto dto, String searchDisCode, DisInfoDto disInfoDto) throws Exception;
    /**
     * 
     * highShareTransferVolValidate:高端份额迁移校验
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2019年5月21日 下午6:24:10
     */
    public boolean highShareTransferVolValidate(CounterShareTransferVolReqDto dto, String searchDisCode, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * transferTubeInVolValidate:(零售转托管转入校验)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月10日 下午3:04:58
     */
    public boolean transferTubeInVolValidate(CounterTransferTubeReqDto dto, DisInfoDto disInfoDto) throws TmsCounterException;
    
    /**
     * 
     * transferTubeOutVolValidate:(零售转托管转出校验)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月10日 下午3:04:58
     */
    public boolean transferTubeOutVolValidate(CounterTransferTubeReqDto dto, DisInfoDto disInfoDto) throws Exception;
}
