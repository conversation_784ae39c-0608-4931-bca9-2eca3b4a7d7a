$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplyExchange.checkOrder = {};	 
	ApplyExchange.init(checkId,custNo,disCode,idNo);
});

var ApplyExchange = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,ApplyExchange.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyExchange.checkOrder);
		});
	}, 
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplyExchange.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(ApplyExchange.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(ApplyExchange.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		
		QueryFundInfo.queryFundInfo(ApplyExchange.checkOrder.fundCode);
		QueryFundInfo.queryCustHodlInfo(ApplyExchange.checkOrder);

		QueryFundInfo.queryTFundInfo(ApplyExchange.checkOrder.tFundCode);
		
		if($("#selectBank").length > 0){
			$("#selectBank").val(ApplyExchange.checkOrder.cpAcctNo);
		}
		
		var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
		var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
		$("#availVol").html(selectDtl.availVol);
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(ApplyExchange.checkOrder.agentFlag);
		}
		
		if($("#riskFlag").length > 0){
			$("#riskFlag").html(CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, ApplyExchange.checkOrder.riskFlag, ''));
		}
		
		if($("#fundCode").length > 0){
			$("#fundCode").val(ApplyExchange.checkOrder.fundCode);
		}
		
		if($("#tFundCode").length > 0){
			$("#tFundCode").val(ApplyExchange.checkOrder.tFundCode);
		}
		
		if($("#appVol").length > 0){
			$("#appVol").val(CommonUtil.formatAmount(ApplyExchange.checkOrder.appVol));
		}
		
		if($("#appVolCapitalForExchange").length > 0){
			$("#appVolCapitalForExchange").val(Main.format(CommonUtil.formatAmount(ApplyExchange.checkOrder.appVol)));
		}
		
		if($("#largeRedmFlag").length > 0){
			$("#largeRedmFlag").val(ApplyExchange.checkOrder.largeRedmFlag);
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").val(ApplyExchange.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(ApplyExchange.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, ApplyExchange.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(ApplyExchange.checkOrder.transactorName);
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(ApplyExchange.checkOrder.transactorIdNo);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(ApplyExchange.checkOrder.transactorIdType);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(ApplyExchange.checkOrder.memo);
		}
	},
}
