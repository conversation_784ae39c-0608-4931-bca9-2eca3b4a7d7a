package com.howbuy.tms.counter.util;


import com.howbuy.tms.counter.common.WebDevEnum;

public class NfsToWebDavUtils {

    
    /**
     * 
     * processAccIdCardUpFile:处理nfs转webdav 例:/data/app/cim_web_server/center_feature/20240228/xxxx.jpg
     * @param path
     * @return
     * <AUTHOR>
     * @date 2024年2月29日 下午3:31:48
     */
    public static String processAccIdCardUpFile(String path) {
        int count = path.indexOf(WebDevEnum.ID_CARD_UP_FILE.getName());
        String newPath = null;
        if(count >= 0) {
            newPath = path.substring(count, path.length());
            newPath = newPath.replace(WebDevEnum.ID_CARD_UP_FILE.getName(), "");
        }
        return newPath;
    }
    
    /**
     * 
     * processAccIdCardUpFile:处理nfs转webdav 例:/data/app/cim_web_server/face_center_feature/20240228/xxxx.jpg
     * @param path
     * @return
     * <AUTHOR>
     * @date 2024年2月29日 下午3:31:48
     */
    public static String processFaceAccIdCardUpFile(String path, String picPath) {
        int count = path.indexOf(picPath);
        String newPath = null;
        if(count >= 0) {
            newPath = path.substring(count, path.length());
            newPath = newPath.replace(picPath, "");
        }
        return newPath;
    }

}
