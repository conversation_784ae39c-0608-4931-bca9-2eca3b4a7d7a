/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto.base;

import java.io.Serializable;

/**
 * @description:(公共响应) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月28日 下午9:05:37
 * @since JDK 1.7
 */
public class BaseResponseDto implements Serializable {
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -4777277960264799610L;
    /**
     * 返回码
     */
    private String returnCode;
    /**
     * 描述
     */
    private String description;
    /**
     * 总记录数
     */
    private long totalCount;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 当前页
     */
    private long pageNo;

    /**
     * 柜台申请单号
     */
    private String dealAppNo;

    public String getReturnCode() {
        return returnCode;
    }
    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    public long getTotalCount() {
        return totalCount;
    }
    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }
    public long getTotalPage() {
        return totalPage;
    }
    public void setTotalPage(long totalPage) {
        this.totalPage = totalPage;
    }
    public long getPageNo() {
        return pageNo;
    }
    public void setPageNo(long pageNo) {
        this.pageNo = pageNo;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }
}

