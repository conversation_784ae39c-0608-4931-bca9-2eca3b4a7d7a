package com.howbuy.tms.counter.common;

public final class TradeConstant {
	/**
	 * 账户中心的
	 */
	public final static String HOWBUY_DIS_CODE = "HB000A001";
	/**
	 * 账户中心的网点代码
	 */
	public final static String TRADE_ACC_OUTLET_CODE = "W20170215";
	
	/**
	 * 给群济的网店号
	 */
	public final static String NET_NO = "666666";
	
	/**
	 * 给群济的来源
	 */
	public final static String SYS_FROM_CODE = "0666";
	/**
	 * 账户中心成功返回值
	 */
	public final static String SUCCESS_ACC_TRADE = "0000000";
	
	/**
	 * hb返回成功
	 */
	public final static String HB_TRADE_SUCESS = "0000";
	
	public final static String HB_TRADE_SUCC_NEW = "0000000";
	
	/**
	 * CRM成功返回码
	 */
	public final static String CRM_TRADE_SUCESS = "0000";
	
	/**
	 * 中台成功返回码
	 */
	public final static String TMS_TRADE_SUCC_CODE = "Z0000000";

	/**
	 * 资金在途交易协议类型
	 */
	public static final String CAPITAL_INTRANSIT_PROTOCOL_TYPE = "91";
	
	
	 /**
     *  1:居民国（地区）不发放纳税人识别号
     */
    public static final String PROVIDE_NULL_CODE ="1";

    /**
     *  2:账户持卡人未能取得纳税人识别号
     */
    public static final String GET_NULL_CODE ="2";
	
}
