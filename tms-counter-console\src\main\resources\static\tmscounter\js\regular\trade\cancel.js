/**
*撤单
*<AUTHOR>
*@date 2017-04-01 14:56
*/
$(function(){
	Init.init();
	Cancel.init();	
});

var Cancel = {
	init:function(){
		$("#confimCancelBtn").on('click',function(){
			Cancel.confirm();
		});
		
		$("#queryCustInfoBtn").on('click',function(){
            RegularQueryCustInfo.queryCustInfo(null,null,null,true);
		});
		
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});	
	},
	
	/***
	 * 确认撤单
	 */	
	confirm : function(){

		var  uri= TmsCounterConfig.CANCEL_REGULAR_CONFIRM_URL ||  {};
		var selectedOrderIndex = $("input[name='orderIndex'][type='radio']:checked").val();
		
		CommonUtil.disabledBtn("confimCancelBtn");

		if(CommonUtil.isEmpty(selectedOrderIndex)){
			CommonUtil.layer_tip("请选择要撤单的订单");
			return ;
		}
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		var cancelConfirmForm = RegularQueryCanCancel.canCancelOrders[selectedOrderIndex];
		
		if(!Validate.validateTransactorInfo(transactorInfoForm,RegularQueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimCancelBtn");
			return false;
		}
		
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		transactorInfoForm.appDtm = transactorInfoForm.appDt +'' + transactorInfoForm.appTm;
		if(!Valid.valiadTradeTime(transactorInfoForm.appTm) && transactorInfoForm.cancelType == '1'){
			CommonUtil.layer_tip("自行撤单申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
		var dealAppNo ="";
		if(!(typeof RegularApplyCancel == "undefined")){
			dealAppNo = RegularApplyCancel.checkOrder.dealAppNo;
		}
		
		var reqparamters ={"dealAppNo":dealAppNo,"transactorInfoForm":JSON.stringify(transactorInfoForm),"cancelConfirmForm": JSON.stringify(cancelConfirmForm),"custInfoForm":JSON.stringify(RegularQueryCustInfo.custInfo)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);
		CommonUtil.ajaxAndCallBack(paramters, Cancel.callBack);
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			if($(".confimBtn").length > 0){
				CommonUtil.disabledBtnWithClass("confimBtn");
				CommonUtil.disabledBtn("abolishBtn");
			}
			CommonUtil.layer_tip("撤单提交成功");
			RegularQueryCanCancel.queryCanCancelForApply(RegularQueryCustInfo.custInfo.custNo);
		}else{
			CommonUtil.layer_tip("撤单提交失败,"+respDesc);
		}
		
		if(!$(".confimBtn").length > 0){
			CommonUtil.enabledBtn("confimCancelBtn");
		}
	}	
};


