package com.howbuy.tms.counter.service.trade.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.OwnershipRightTransferCheckFlagEnum;
import com.howbuy.tms.counter.dto.CounterOwnershipRightTransferReqDto;
import com.howbuy.tms.counter.dto.OwnershipRightTransferOrderDto;
import com.howbuy.tms.counter.dto.QueryOwnershipRightTransferReqDto;
import com.howbuy.tms.counter.dto.QueryOwnershipRightTransferRespDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.OwnershipRightTransferOrderService;
import com.howbuy.tms.counter.utils.ExcelExportUtils;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.QueryOwnershipRightTransferFacade;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.QueryOwnershipRightTransferRequest;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.QueryOwnershipRightTransferResponse;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.bean.OwnershipRightTransferOrderBean;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.bean.QueryOwnershipRightTransferParam;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlFacade;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlRequest;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlResponse;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.AbstractOwnershipRightTransferDtlBean;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.NoTradeOwnershipRightTransferDtlBean;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.NormalOwnershipRightTransferDtlBean;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferFacade;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferRequest;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferResponse;
import com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.bean.CounterOwnershipRightTransferBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description:股权份额转让订单相关服务
 * @Author: yun.lu
 * Date: 2023/5/19 14:39
 */
@Service
@Slf4j
public class OwnershipRightTransferOrderServiceImpl implements OwnershipRightTransferOrderService {

    @Autowired
    private QueryOwnershipRightTransferFacade queryOwnershipRightTransferFacade;
    @Autowired
    private QueryOwnershipRightTransferDtlFacade queryOwnershipRightTransferDtlFacade;
    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    @Autowired
    private CounterOwnershipRightTransferFacade counterOwnershipRightTransferFacade;

    @Override
    public QueryOwnershipRightTransferRespDto queryOwnershipRightTransfer(QueryOwnershipRightTransferReqDto reqDto) throws Exception {
        // 1.构建查询入参
        QueryOwnershipRightTransferRequest request = buildQueryOwnershipRightTransferRequest(reqDto);
        // 2.查询股权份额转让订单
        QueryOwnershipRightTransferResponse baseResp = (QueryOwnershipRightTransferResponse) TmsFacadeUtil.executeThrowException(queryOwnershipRightTransferFacade, request, null);
        // 3.脱敏
        if (!CollectionUtils.isEmpty(baseResp.getOwnershipRightTransferOrderBeanList())) {
            for (OwnershipRightTransferOrderBean dto : baseResp.getOwnershipRightTransferOrderBeanList()) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }
        // 4.构建返回实体
        QueryOwnershipRightTransferRespDto queryOwnershipRightTransferRespDto = new QueryOwnershipRightTransferRespDto();
        BeanUtils.copyProperties(baseResp, queryOwnershipRightTransferRespDto);
        if (!CollectionUtils.isEmpty(baseResp.getOwnershipRightTransferOrderBeanList())) {
            List<OwnershipRightTransferOrderDto> orderList = new ArrayList<>();
            for (OwnershipRightTransferOrderBean ownershipRightTransferOrderBean : baseResp.getOwnershipRightTransferOrderBeanList()) {
                OwnershipRightTransferOrderDto ownershipRightTransferOrderDto = new OwnershipRightTransferOrderDto();
                BeanUtils.copyProperties(ownershipRightTransferOrderBean, ownershipRightTransferOrderDto);
                ownershipRightTransferOrderDto.setCustNo(ownershipRightTransferOrderBean.getTxAcctNo());
                orderList.add(ownershipRightTransferOrderDto);
            }
            queryOwnershipRightTransferRespDto.setOwnershipRightTransferOrderDtoList(orderList);
        }
        return queryOwnershipRightTransferRespDto;
    }


    /**
     * 根据订单号查询股权份额转让详情
     *
     * @param dealDtlNo 订单号
     * @return 股权份额转让详情
     */
    @Override
    public AbstractOwnershipRightTransferDtlBean queryOwnershipRightTransferDtlByDealDtlNo(String dealDtlNo) throws Exception {
        // 1.构建查询入参
        QueryOwnershipRightTransferDtlRequest request = new QueryOwnershipRightTransferDtlRequest();
        request.setDealDtlNo(dealDtlNo);
        // 2.查询
        QueryOwnershipRightTransferDtlResponse response = (QueryOwnershipRightTransferDtlResponse) TmsFacadeUtil.executeThrowException(queryOwnershipRightTransferDtlFacade, request, null);
        String content = response.getContent();
        if (StringUtils.isNotBlank(content)) {
            if (content.contains("outTxAcctNo")) {
                return JSONObject.parseObject(content, NoTradeOwnershipRightTransferDtlBean.class);
            } else {
                return JSONObject.parseObject(content, NormalOwnershipRightTransferDtlBean.class);
            }
        } else {
            return null;
        }
    }

    /**
     * 根据订单号查询股权份额转让详情
     *
     * @param dealAppNo 订单号
     * @return 股权份额转让详情
     */
    @Override
    public AbstractOwnershipRightTransferDtlBean queryOwnershipRightTransferDtlByDealAppNo(String dealAppNo) throws Exception {
        // 1.构建查询入参
        QueryOwnershipRightTransferDtlRequest request = new QueryOwnershipRightTransferDtlRequest();
        request.setDealAppNo(dealAppNo);
        // 2.查询
        QueryOwnershipRightTransferDtlResponse response = (QueryOwnershipRightTransferDtlResponse) TmsFacadeUtil.executeThrowException(queryOwnershipRightTransferDtlFacade, request, null);
        String content = response.getContent();
        if (StringUtils.isNotBlank(content)) {
            if (content.contains("outTxAcctNo")) {
                return JSONObject.parseObject(content, NoTradeOwnershipRightTransferDtlBean.class);
            } else {
                return JSONObject.parseObject(content, NormalOwnershipRightTransferDtlBean.class);
            }
        } else {
            return null;
        }
    }

    @Override
    public String counterOwnershipRightTransfer(CounterOwnershipRightTransferReqDto counterOwnershipRightTransferReqDto) throws Exception {
        // 1.构建修改入参
        CounterOwnershipRightTransferRequest request = new CounterOwnershipRightTransferRequest();
        request.setTxCode(TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE);
        CounterOwnershipRightTransferBean counterOwnershipRightTransferBean = new CounterOwnershipRightTransferBean();
        BeanUtils.copyProperties(counterOwnershipRightTransferReqDto, counterOwnershipRightTransferBean);
        request.setCounterOwnershipRightTransferBean(counterOwnershipRightTransferBean);
        // 2.修改提交
        CounterOwnershipRightTransferResponse response = (CounterOwnershipRightTransferResponse) TmsFacadeUtil.executeThrowException(counterOwnershipRightTransferFacade, request, null);
        // 3.返回结果
        return response.getDealAppNo();
    }

    @Override
    public void downloadOwnershipRightTransfer(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.参数转换
        QueryOwnershipRightTransferReqDto queryOwnershipRightTransferReqDto = getQueryOwnershipRightTransferReqDto(request);
        // 2.参数校验
        queryOwnershipRightTransferReqDto.check();
        // 3.查询订单信息
        QueryOwnershipRightTransferRespDto respDto = this.queryOwnershipRightTransfer(queryOwnershipRightTransferReqDto);
        // 4.构建excel信息
        ExcelExportUtils.ExcelColumConfig columsConfig = getProductControlColumsConfig();
        String[] excelHeader = columsConfig.getHeaders();
        String[] dsTitles = columsConfig.getTitles();
        int[] dsFormat = columsConfig.getFormats();

        List<OwnershipRightTransferOrderDto> orderList = respDto.getOwnershipRightTransferOrderDtoList();
        List<Map<String, Object>> dataList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(orderList)) {
            for (OwnershipRightTransferOrderDto order : orderList) {
                Map<String, Object> map = new HashMap<>();
                map.put("getCheckFlag", OwnershipRightTransferCheckFlagEnum.getDescByFlag(order.getCheckFlag()));
                map.put("getCustNo", order.getCustNo());
                map.put("getCustName", order.getCustName());
                map.put("getMBusinessCode", BusinessCodeEnum.getName(order.getMBusinessCode()));
                map.put("getFundCode", order.getFundCode());
                map.put("getFundName", order.getFundName());
                map.put("getFundType", order.getFundType());
                map.put("getFundSubType", order.getFundSubType());
                map.put("getTransferPrice", order.getTransferPrice() != null ? order.getTransferPrice().toString() : "");
                map.put("getAckVol", order.getAckVol() != null ? order.getAckVol().toString() : "");
                map.put("getAckAmt", order.getAckAmt() != null ? order.getAckAmt().toString() : "");
                map.put("getAckDtm", order.getAckDtm());
                map.put("getIsNoTradeTransfer", YesOrNoEnum.YES.getCode().equals(order.getIsNoTradeTransfer()) ? "是" : "否");
                map.put("getUpdateDtm", order.getUpdateDtm() != null ? order.getUpdateDtm() : "");
                map.put("getModifier", order.getModifier() != null ? order.getModifier() : "");
                map.put("getModifyDtm", order.getModifyDtm() != null ? order.getModifyDtm() : "");
                map.put("getChecker", order.getChecker());
                dataList.add(map);
            }
        }
        String fileName = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD) + "股权份额维护订单.xls";
        ExcelExportUtils.export(fileName, fileName, excelHeader, dsTitles, dsFormat, null, dataList, request, response);
    }

    private QueryOwnershipRightTransferReqDto getQueryOwnershipRightTransferReqDto(HttpServletRequest request) {
        String conditions = request.getParameter("conditions");
        QueryOwnershipRightTransferReqDto queryOwnershipRightTransferReqDto = JSON.parseObject(conditions, QueryOwnershipRightTransferReqDto.class);
        JSONObject param = JSONObject.parseObject(conditions);
        if (StringUtils.isNotBlank(param.getString("mBusiCodes"))) {
            queryOwnershipRightTransferReqDto.setMBusinessCodeList(Arrays.asList(param.getString("mBusiCodes").split(",")));
        } else {
            List<String> mBusinessCodeList = new ArrayList<>();
            mBusinessCodeList.add(BusinessCodeEnum.FORCE_REDEEM.getMCode());
            mBusinessCodeList.add(BusinessCodeEnum.FORCE_ADD.getMCode());
            mBusinessCodeList.add(BusinessCodeEnum.FORCE_SUBTRACT.getMCode());
            mBusinessCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode());
            mBusinessCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode());
            queryOwnershipRightTransferReqDto.setMBusinessCodeList(mBusinessCodeList);
        }
        if (StringUtils.isNotBlank(queryOwnershipRightTransferReqDto.getMBusiCode())) {
            queryOwnershipRightTransferReqDto.setMBusinessCodeList(Collections.singletonList(queryOwnershipRightTransferReqDto.getMBusiCode()));
        } else {
            List<String> list = new ArrayList<>();
            list.add(BusinessCodeEnum.FORCE_REDEEM.getMCode());
            list.add(BusinessCodeEnum.FORCE_ADD.getMCode());
            list.add(BusinessCodeEnum.FORCE_SUBTRACT.getMCode());
            list.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode());
            list.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode());
            queryOwnershipRightTransferReqDto.setMBusinessCodeList(list);
        }
        return queryOwnershipRightTransferReqDto;
    }

    private ExcelExportUtils.ExcelColumConfig getProductControlColumsConfig() {
        ExcelExportUtils.ExcelColumConfig config = new ExcelExportUtils.ExcelColumConfig();
        config.addStringCenter("审核状态", "getCheckFlag");
        config.addStringCenter("客户号", "getCustNo");
        config.addStringCenter("客户姓名", "getCustName");
        config.addStringCenter("业务名称", "getMBusinessCode");
        config.addStringCenter("基金代码", "getFundCode");
        config.addStringCenter("基金名称", "getFundName");
        config.addStringCenter("基金类型", "getFundType");
        config.addStringCenter("基金二级类型", "getFundSubType");
        config.addStringCenter("转让价格", "getTransferPrice");
        config.addStringCenter("确认份额", "getAckVol");
        config.addStringCenter("确认金额", "getAckAmt");
        config.addStringCenter("确认日期", "getAckDtm");
        config.addStringCenter("是否转译后非交易过户", "getIsNoTradeTransfer");
        config.addStringCenter("更新时间", "getUpdateDtm");
        config.addStringCenter("修改人", "getModifier");
        config.addStringCenter("修改时间", "getModifyDtm");
        config.addStringCenter("审核人", "getChecker");

        return config;
    }

    /**
     * 构建查询入参
     */
    private QueryOwnershipRightTransferRequest buildQueryOwnershipRightTransferRequest(QueryOwnershipRightTransferReqDto reqDto) {
        // 1.相同字段名,直接赋值
        QueryOwnershipRightTransferParam param = new QueryOwnershipRightTransferParam();
        BeanUtils.copyProperties(reqDto, param);
        // 2.客户号,一账通赋值
        param.setTxAcctNo(reqDto.getCustNo());
        // 3.如果客户号传参是空的,一账通账号非空,那么用一账通查询出客户号
        if (StringUtils.isBlank(reqDto.getCustNo()) && StringUtils.isNotBlank(reqDto.getHboneNo())) {
            String txAcctNo = tmsCounterOutService.queryTxAccountNoByHboneNo(reqDto.getHboneNo());
            if (StringUtils.isNotBlank(txAcctNo)) {
                param.setTxAcctNo(txAcctNo);
            }
        }
        // 4.是否是待维护审核
        if (StringUtils.isNotBlank(reqDto.getCheckFlag())) {
            param.setCheckFlag(reqDto.getCheckFlag());
        }

        QueryOwnershipRightTransferRequest request = new QueryOwnershipRightTransferRequest();
        request.setQueryOwnershipRightTransferParam(param);
        return request;


    }
}
