<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <id>distribution</id>
    <formats>
        <format>dir</format>
    </formats>
    <baseDirectory>/</baseDirectory>
    <fileSets>
        <fileSet>
            <directory>src/main/resources/</directory>
            <outputDirectory>conf/</outputDirectory>
            <excludes>
                <exclude>**/dubbo.properties</exclude>
                <exclude>**/spring-all.xml</exclude>
                <exclude>**/freemarker.properties</exclude>
                <exclude>**/ehcache-cluster.xml</exclude>
                <exclude>**/static/**</exclude>
                <exclude>**/template/**</exclude>
                <exclude>**/context/**</exclude>
                <exclude>**/META-INF/**</exclude>
                <exclude>**/message_zh_CN.properties</exclude>
                <exclude>**/hsb.xml</exclude>
                <exclude>**/dispatcher.xml</exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>bin</directory>
            <outputDirectory>bin/</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>