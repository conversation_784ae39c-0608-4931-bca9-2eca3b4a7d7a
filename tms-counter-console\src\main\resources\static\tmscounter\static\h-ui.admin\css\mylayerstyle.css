@charset "utf-8";
/* 你自己的弹窗提示 */

.main_title_layer {font-size:15px;border-bottom:1px solid #ccc;}
.cp_top_layer {margin-top:5px;line-height:15px;font-size:12px;}
.cp_top_layer input {border:none;display:inline-block;font-size:12px;padding:2px;border:1px solid #ccc;margin-left:5px;}
.cp_top_layer .normal_span {display:inline-block;line-height:15px;}
.cp_top_layer .input-text {display: inline-block;width: 60px;margin-left: 5px;padding: 10px 5px;}
.sj_box_layer {width:100%;overflow-x: scroll;margin:0 auto;}
.sj_box_layer table tr th {white-space:nowrap;}
.sj_box_layer table tr td {height:15px;white-space:nowrap;}
.main_title1 {font-size:20px;border-bottom:1px solid #ccc;}
.word_date {text-align:center;font-size:12px;line-height:30px;background:#FFC;color:#333;margin:10px 20px 0;}
.fl {float:left;}
.fr {float:right;}
.clear{ clear: both; *zoom:1}
.clear:after{content:"."; display:block; height: 0; clear: both; visibility:hidden;}
/*翻页结构*/
.page_all {width:100%;margin:0 auto;}
.page_item {text-align:right;padding-bottom:20px;}
.page_item a {width:58px;height:22px; text-align:center; text-decoration:none;line-height:22px;display:inline-block;color:#000;border:1px solid #c5c5c5;border-radius:3px;
background:#EDF0F4;
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee'); /* for IE */
background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#eee)); /* for webkit browsers */
background: -moz-linear-gradient(top,  #fff,  #eee); /* for firefox 3.6+ */}
.page_item a:hover {color:#000;background:#f2f2f2;}
.page_item .pg_1 {width:40px;height:22px;}
.page_item .pg_gray,.page_item .pg_gray:hover {color:#999; text-decoration:none; cursor:default;background:#fafafa; }
.page_item .it01 {width:50px;height:22px;line-height:22px;margin:0 3px;border:1px solid #ddd;padding:0 2px;}
.page_item .it02 {width:50px;height:24px;cursor:pointer;}
.page_item input {*position:relative;*top:2px;}
.page_item span {text-align:left;}
.page_sj {padding-bottom:20px;}

/*业务处理页面*/
.normal_btn {position:relative;left:50%;margin-left:-41px;}
.text_con {font-size:14px;line-height:35px;margin-bottom:0;}
.text_con .date_span {position:relative;top:2px;}
.text_con .select-box {line-height:21px;margin-left:5px;}
.text_con .state_span {margin-left:100px;position:relative;top:2px;} 
.text_con input {display:inline-block;width:115px;margin-left:5px;padding:15px 5px;}

/*支付对账页面*/

.secondary_title {font-size:18px;line-height:30px;background:#FFC;color:#f00;border-top:1px solid #ccc;border-bottom:1px solid #ccc;padding-left:15px;}
/*支付对账确认页面*/
.data_box table tr th {white-space:nowrap;}
.data_box table tr td {white-space:nowrap;}

/*业务处理流程图页面*/
.flowsheet_box {padding-right:300px;position:relative;min-width:500px;}
.flowsheet_box .btn {width:130px;text-align:center;}
.flowsheet_box .two_fl {position:absolute;top:36px;left:50%;margin-left:-159px;}
.flowsheet_box .btn2 {position:absolute;top:105px;left:50%;margin-left:-215px;}
.flowsheet_box .btn3 {position:absolute;top:87px;left:50%;margin-left:-44px;}
.flowsheet_box .btn4 {position:absolute;top:153px;left:50%;margin-left:-215px;}
.flowsheet_box .btn5 {position:absolute;top:145px;left:50%;margin-left:-44px;}
.flowsheet_box .btn6 {position:absolute;top:198px;left:50%;margin-left:-215px;}
.flowsheet_box .btn7 {position:absolute;top:203px;left:50%;margin-left:-44px;}
.flowsheet_box .btn8 {position:absolute;top:275px;left:50%;margin-left:-215px;}
.flowsheet_box .btn9 {position:absolute;top:351px;left:50%;margin-left:-215px;}
.flowsheet_box .only_lf1 {position:absolute;top:136px;left:50%;margin-left:-159px;}
.flowsheet_box .only_lf2 {position:absolute;top:120px;left:50%;margin-left:8px;}
.flowsheet_box .only_lf4 {position:absolute;top:178px;left:50%;margin-left:8px;}
.flowsheet_box .only_lf5 {position:absolute;top:309px;left:50%;margin-left:-159px;}
.flowsheet_box .one_lf {position:absolute;top:231px;left:50%;margin-left:-159px;}

/*交易申请日终页面*/
.trade_box {width:800px;margin:0 auto;}
.trade_box tr td {height:21px;position:relative;font-size:14px;}
.trade_box tr td input {position:absolute;left:40px;top:16px;}
.trade_box tr td span {display:block;}

/*导入确认页面*/
.import_title {line-height:36px;font-size:16px;text-align:center;background:#ccc;border-top:1px solid #666;border-bottom:1px solid #666;margin:0 20px;}

/*产品上线参数录入页面*/
.cp_top {margin-top:10px;line-height:25px;font-size:14px;}
.cp_top input {border:none;display:inline-block;font-size:14px;padding:4px;border:1px solid #ccc;margin-left:10px;}
.cp_top .normal_span {display:inline-block;line-height:25px;}
.cp_top .input-text {display: inline-block;width: 115px;margin-left: 5px;padding: 15px 5px;}
.message_con {display:inline-block;width:700px;height:60px;margin-left:10px;border:1px solid #ccc;overflow-y: scroll;padding:4px;}

.search_box {width:1150px;}
.search_box label {position:relative;}
.search_box label input {display:inline-block;font-size:14px;line-height:normal;padding:5px;border:1px solid #ccc;width:120px;}
.search_box label span {position:absolute;white-space:nowrap;color:#999;left:3px;top:0px;}
.search_box label i {position:absolute;right:5px;top:0;}
.search_box .sp_label span {color:#333;}
.search_box .sp_label2 input {padding-right:20px;width:110px;}
.add_pz {color:#0099FF;font-size:14px;display:inline-block;}
.kt_box label {line-height:35px;}
.kt_box label input {margin-right:3px;position:relative;top:1px;}
.cp_tab table tr td {font-size:14px;white-space:nowrap;}
.cp_tab table tr td .select {display:inline-block;width:100px;margin:0 auto;}

/*登陆页面*/
.dl_box {width:350px;position:absolute;left:50%;margin-left:-175px;border:1px solid #333;top:50%;margin-top:-85px;}
.dl_box h4 {font-size:18px;color:#fff;background:rgba(51, 102, 255, 1);line-height:40px;text-align:center;margin-top:0;}
.dl_box ul li {width:100%;margin-top:10px;}
.dl_box ul li span {display:inline-block;width:100px;line-height:30px;text-align:right;padding-right:3px;}
.dl_box ul li input {font-size:14px;padding:3px;width:160px;border:1px solid #ccc;}
.dl_box p a {display:inline-block;width:80px;text-align:center;border:1px solid #333;padding:5px 0;font-size:14px;line-height:14px;background:#ccc;text-decoration:none;}

.wel_con {font-size:50px;font-weight:bold;padding-top:200px;text-align:center;}

/*产品基本信息维护页面*/
.result_tab {width:800px;margin:0 auto;}
.result_tab table tr td {height:21px;font-size:14px;white-space:nowrap;}
.result2_tab {width:100%;margin:0 auto;overflow-x: scroll;}
.result2_tab table tr td {height:21px;font-size:14px;white-space:nowrap;}

/*客户余额查询*/
.chose_box .result_btn {display:inline-block;float:left;padding:5px 10px;background:#fff;border:1px solid #333;cursor:pointer;}
.chose_box .current {background:#5a98de;color:#fff;border:1px solid #5a98de;}
.message_tab {width:100%;}
.message_tab table tr td {white-space:nowrap;height:25px;}
.message_tab table tr th {background:#ccc;}
.message_tab table .fl_part td {background:#ccc;}
.message_tab table .fl_part .no_bg {background:transparent;}

.message_tab table .fl_part_red td {background:#f51d1d;}
.message_tab table .fl_part_red .no_bg {background:transparent;}


/*确认部分弹层结构*/
.yc_tc {width:360px;height:120px;margin:0 auto;}
.yc_tc table {margin-top:10px;}
.yc_tc table tr td {height:25px;font-size:14px;}
.containner_all {width:100%;overflow-x:scroll;}
.container_box {position:relative;width:1100px;margin:0 auto;}
.container_box_layer {position:relative;width:630px;margin:0 auto;}
.sp_con_box {width:100%;margin:0 auto;overflow-x:scroll;}
.yc_have i {font-style:normal;color:#69ABF7;border-bottom:1px solid #69ABF7;}

.container_box3 {position:relative;margin:0 auto;}
.container_box3 table tr td {white-space:nowrap;height:25px;}
.container_box3 table tr .bg1 {background:#efefef;}
.container_box3 table tr:hover .bg1 {background:#efefef;}
.container_box3 table tr .bg2 {background:#FBFDDC;}
.container_box3 table tr:hover .bg2 {background:#FBFDDC;}
.container_box3 table tr .bg3 {background:#FBFDDC;}
.container_box3 table tr:hover .bg3 {background:#FBFDDC;}

.con_part {width:500px;margin:0 auto;}
.yw_box {width:320px;margin:0 auto;}
.yw_box table tr .bg1 {background:#B8CCE4;}
.yw_box table tr:hover .bg1 {background:#B8CCE4;}
.yw_box table .sp_tr td {background:#FBFDDC;}
.yw_box table .sp_tr:hover td {background:#FBFDDC;}

.heng {display:block;position:relative;}
.heng:after {content:"＞";font-size:38px;font-weight:bold;font-style:normal;}
.shu {display:block;position:relative;}
.shu:after {content:"∨";font-size:38px;font-weight:bold;font-style:normal;}
span.required {
    color: #FF0000;
    font-size: 150%;
}
