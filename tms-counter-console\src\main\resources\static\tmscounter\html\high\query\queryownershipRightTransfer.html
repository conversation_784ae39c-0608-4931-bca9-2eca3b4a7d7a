<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
    <script type="text/javascript" src="lib/html5.js"></script>
    <script type="text/javascript" src="lib/respond.min.js"></script>
    <script type="text/javascript" src="lib/PIE_IE678.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>股权份额转让维护</title>
</head>

<body>
<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易下单 <span
        class="c-gray en">&gt;</span> 股权份额转让维护 <a class="btn btn-success radius r"
                                                  style="line-height:1.6em;margin-top:3px"
                                                  href="javascript:location.replace(location.href);" title="刷新"><i
        class="Hui-iconfont">&#xe68f;</i></a></nav>
<div class="page-container">
    <div class="containner_all">
        <div class="container_box">
            <p class="mainTitle mt10">数据查询</p>
            <form action="" id="searchCheckForm">
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" placeholder="请输入" id="custNo" name="custNo">
                    <span class="normal_span">一账通号</span>
                    <input type="text" name="hboneNo" id="hboneNo" placeholder="请输入">
                    <span class="normal_span ml30">基金代码：</span>
                    <input type="text" placeholder="请输入" id="fundCode" name="fundCode">
                    <span class="normal_span ml30">业务名称：</span>
                    <span class="select-box inline">
                      <select  multiple="multiple" id="mBusiCode" name="mBusiCode">
                        <option value="1142">强赎</option>
                        <option value="1144" selected>强增</option>
                        <option value="1145" selected>强减</option>
                        <option value="1134" selected>非交易过户转入</option>
                        <option value="1135" selected>非交易过户转出</option>
                      </select>
                    </span>
                </div>

                <div class="cp_top mt30">

                </div>

                <div class="cp_top mt30">
                    <span class="normal_span">基金类型：</span>
                    <span class="select-box inline">
                       <select name="fundType" id="fundType" class="select">
                       </select>
                    </span>

                    <span class="normal_span ml30">基金二级类型：</span>
                    <span class="select-box inline">
                       <select name="fundSubType" id="fundSubType" class="select">
                       </select>
                    </span>

                    <span class="normal_span ml30">审核状态：</span>
                    <span class="select-box inline">
                       <select name="checkFlag" id="selectCheckFlag" class="select">
                       </select>
                    </span>
                    <span class="normal_span ml30">确认日期：</span>
                    <input id="beginDtm" name="beginDtm" class="input-text laydate-icon"
                           onclick="laydate({isdate: true, format: 'YYYYMMDD'})" >
                    -
                    <input id="endDtm" name="endDtm" class="input-text laydate-icon"
                           onclick="laydate({isdate: true, format: 'YYYYMMDD'})">

                </div>

            </form>
            <div class="cp_top mt30">
                <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryBtn">查询</a>
                <a href="javascript:void(0);" class="btn radius btn-primary ml20 resetBth" id="resetBth">重置</a>
            </div>
        </div>
    </div>
</div>
<div class="page-container">
    <p class="main_title">查询结果</p>
    <a href="javascript:void(0)" class="btn radius btn-secondary" id="exportBtn">导出</a>
    <div class="result2_tab">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
            <thead>
            <tr class="text-c">
                <th>操作</th>
                <th>审核状态</th>
                <th>客户号</th>
                <th>客户姓名</th>
                <th>业务名称</th>
                <th>基金代码</th>
                <th>基金名称</th>
                <th>基金类型</th>
                <th>基金二级类型</th>
                <th>转让价格</th>
                <th>确认份额</th>
                <th>确认金额</th>
                <th>确认日期</th>
                <th>是否转译后非交易过户</th>
                <th>更新时间</th>
                <th>修改人</th>
                <th>修改时间</th>
                <th>审核人</th>
            </tr>
            </thead>
            <tbody id="rsList">
            </tbody>
        </table>
    </div>
</div>

<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
<script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
<script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
<script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
<script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
<script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
<script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
<script type="text/javascript" src="../../../js/baseconfig.js"></script>
<script type="text/javascript" src="../../../js/common.js?v=3.3.19"></script>
<script type="text/javascript" src="../../../js/config.js"></script>
<script type="text/javascript" src="../../../js/commonutil.js"></script>
<script type="text/javascript" src="../../../js/high/common/viewdealcommon.js?v=3.3.19"></script>
<script type="text/javascript" src="../../../js/high/query/queryownershipRightTransfer.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.filter.js"></script>
</body>

</html>