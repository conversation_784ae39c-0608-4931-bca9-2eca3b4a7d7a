/**
 *Copyright (c) 2017, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.enums;

/**
 * 
 * @description:(审核类型)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年3月8日 下午4:28:17
 * @since JDK 1.6
 */
public enum CheckTypeEnum {
    
    /**
     * 审核通过
     */
    CHECK_SUCC("1", "审核通过"),
    
    /**
     * 审核失败
     */
    CHECK_FAILD("2", "审核失败"),
    /**
     * 审核驳回
     */
    CHECK_REJECT("3", "审核驳回"),
    
    /**
     * 审核作废
     */
    CHECK_CANCEL("4", "审核作废"),
    
    /**
     * 审核修改
     */
    CHECK_MODIFY("5", "审核修改"),

    /**
     * 材料复审退回，订单驳回
     */
    CHECK_MATERIAL_REJECT("6", "材料复审退回，订单驳回");


    private String code;
    private String name;

    private CheckTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
