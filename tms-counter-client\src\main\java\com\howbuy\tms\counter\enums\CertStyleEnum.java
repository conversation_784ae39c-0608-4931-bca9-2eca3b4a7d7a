/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.enums;

/**
 * 
 * @description:资产证明类型
 * <AUTHOR>
 * @date 2019年6月19日 下午2:23:49
 * @since JDK 1.6
 */
public enum CertStyleEnum {
    /**
     * 无底纹
     */
    NO_SEAL_SUFF("1", "无底纹"),
    /**
     * 有底纹
     */
    SHADING_SEAL_SUFF("2", "有底纹"),
    /**
     * 无底纹中英文
     */
    ENG_NO_SEAL_SUFF("3", "无底纹中英文"),
    /**
     * 有底纹中英文
     */
    ENG_SHADING_SEAL_SUFF("4", "有底纹中英文"),

    /**
     * 中台证券类总资产证明
     */
    MID_FUND_ASSERT_SUFF("5", "中台证券类总资产证明"),
    /**
     * CRM证券类总资产证明
     */
    CRM_FUND_ASSERT_SUFF("6", "CRM证券类总资产证明");
    
    private String code;
    private String name;

    private CertStyleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
