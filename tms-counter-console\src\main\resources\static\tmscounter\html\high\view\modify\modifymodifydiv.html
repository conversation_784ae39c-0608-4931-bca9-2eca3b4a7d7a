<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/style.css" />
    <title>修改分红方式（高端）</title>
</head>

<body>
	<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 我的交易申请（高端）<span class="c-gray en">&gt;</span> 修改分红方式（高端） <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">修改分红方式</p>
            </div>
        </div>
    </div>
    <div class="page-container">
        <p class="main_title">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>风险测评结果</th>
                        <th>开户分销机构</th>
                       <th>私募合格投资者认证</th>
                       <th>资管合格投资者认证</th>
                        <th>客户状态</th>
                        <th>投资者类型</th>
                        <th>协议回款方式</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                    <tr class="text-c">
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <p class="main_title mt30" id="showMaterial">柜台材料信息</p>
        <div class="result2_tab" id="onLineMaterial">
        </div>
        
        <p class="main_title mt30">录入订单信息</p>
        <form action="" id="buyConfirmForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                
                	<tr class="text-c">
                        <td>TA代码</td>
                        <td id="taCodeId">
                        <td>基金代码</td>
                        <td id="fundCodeId">
                        </td>
                    </tr>
                    <tr class="text-c">
                        
                        <td>基金简称</td>
                        <td id="fundName" >--</td>
                        
                        <td>当前分红方式</td>
                        <td id="divModeId" >--</td>
                        
                    </tr>
                    
                    <tr class="text-c">
                     	<td>下单日期</td>
                        <td>
                            <input class="input-text laydate-icon" onclick="laydate({istime: false, format: 'YYYYMMDD'})"  id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" />
                        </td>
                        
                        <td>目标分红方式</td>
                        <td id="targetDivId"></td>
                     </tr>   
                     
                         
                    <tr class="text-c">
                     	<td>下单时间 </td>
                        <td>
                            <input type="text"  id="appTm" name="appTm" isnull="false" datatype="s" errormsg="下单时间" maxlength = "8" />
                        </td>
                        
                        <td>产品通道</td>
                        <td id="productChannelId" >--</td>
                     </tr> 
                    
                </tbody>
            </table>
        </div>
       </form>
        
        
        <p class="main_title mt30">其他信息</p>
        <form id="othetInfoForm">
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                 <tr class="text-c">
                        <td> 网点：</td>
                        <td>
                        	柜台
                       </td>
                        <td>投资顾问代码：</td>
              			<td>
              			  <span class="select-box inline">
                   			 <select name="consCode" class="select selectconsCode">
                    		</select>
                		</span>
                		</td>
             <tr class="text-c">
              <td>是否代理：</td>
               <td> <span class="select-box inline">
                   <select name="agentFlag" class="select selectAgened">
                      <option value="0">否</option>
                      <option value="1">是</option>
                   </select>
                </span></td>
                <td></td>
                 <td></td>
                 <tr class="text-c">
                     <td>审核人</td>
                     <td id="checker"></td>
                     <td>驳回原因</td>
                     <td id="memo"></td>
                 </tr>
            </tbody>
          </table>
        </div>
        
       </form>
       
        <form id="transactorFormId">
         <p class="main_title mt30">经办人信息</p>
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                 <tr class="text-c">
                     <td>经办人姓名：</td>
                      <td>
                       <input type="text" placeholder="请输入" id="transactorName" name="transactorName" isnull="false" datatype="s" errormsg="经办人姓名">
                       </td>
                        <td>经办人证件类型：</td>
                  <td>
                  <span class="select-box inline">
                    <select id="transactorIdType" name="transactorIdType" class="select selectTransactorIdType"  isnull="false" datatype="s" errormsg="经办人证件类型" >
                    </select>
                </span>
                </td>
             <tr class="text-c">
              <td>经办人证件号：</td>
               <td> <input type="text" placeholder="请输入" id="transactorIdNo" name="transactorIdNo" isnull="false" datatype="s" errormsg="经办人证件号" ></td>
                <td></td>
                 <td></td>
                 
            </tbody>
          </table>
        </div>
         </form>
         
       <p class="mt30 text-c" id="submitDiv">
           <a href="javascript:void(0)" id ="modifyBtn" class="btn radius btn-warning ml30">提交</a>
            <a href="javascript:void(0)" id ="cancelBtn" class="btn radius btn-secondary ml30">作废</a>
            <a href="javascript:void(0)" id ="backBtn" class="btn radius btn-success ml30">返回</a>
        </p>
    </div>

    <script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../../js/common.js"></script>
    <script type="text/javascript" src="../../../../js/config.js"></script>
    <script type="text/javascript" src="../../../../js/commonutil.js"></script>
    <script type="text/javascript" src="../../../../js/valid.js"></script>
    <script type="text/javascript" src="../../../../js/high/conscode.js"></script>
    <script type="text/javascript" src="../../../../js/high/query/querycustinfosubpage.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/custinfo.js"></script>
    <script type="text/javascript" src="../../../../js/high/query/queryhighproduct.js"></script>
    <script type="text/javascript" src="../../../../js/high/check/viewcounterdeal.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/onlineorderfile.js"></script>
    <script type="text/javascript" src="../../../../js/high/modify/modify.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/init.js"></script>
    <script type="text/javascript" src="../../../../js/high/modify/modifydivmodify.js"></script>
</body>

</html>