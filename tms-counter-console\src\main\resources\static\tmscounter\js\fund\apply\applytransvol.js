$(function(){
	Init.init();

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	
	ApplyTransVol.checkOrder = {};	 
	ApplyTransVol.init(checkId,custNo,disCode,idNo);
});

var ApplyTransVol = {	
	init:function(checkId, custNo, disCode,idNo){
        var hboneNo = CommonUtil.getParam("hboneNo");
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode, hboneNo);
		QueryCheckOrder.queryMergeTransCheckOrderById(checkId, ApplyTransVol.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_MERGE_TRANS_CONFIRM_URL, CounterAbolish.Abolish, ApplyTransVol.checkOrder);
		});
	}, 
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		
		ApplyTransVol.checkOrder = bodyData.checkOrder || {};
		ApplyTransVol.checkDtlOrder = bodyData.checkDtlOrder || [];
        var orderFile = bodyData.orderFile || {};// CRM线上资料

		if(orderFile == {} || orderFile == null){

		}

		// 转入持仓
		var respData = bodyData.respData;
		ApplyTransVol.transInDisCode = respData.disCode;
		ApplyTransVol.transInBalDtl = respData.custBalDtlList || [];
				
		if(CommonUtil.isEmpty(ApplyTransVol.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(!(ApplyTransVol.checkOrder.checkFlag == 3 || ApplyTransVol.checkOrder.checkFlag == 5) ){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}

        OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料
		
		// 转出信息
		BodyView.setTransOutTableViewNew("highTransOutCustBals","transOutCustBals", ApplyTransVol.checkDtlOrder, ApplyTransVol.checkOrder.disCode,"assetBody");
		
		// 设置转入银行卡信息
		QueryCustInfo.getCustBankInfos(ApplyTransVol.checkOrder.txAcctNo, ApplyTransVol.checkOrder.disCode, ApplyTransVol.setTransInBankInfo);
		
		// 转入银行卡资产信息
		BodyView.setTransInCustBalsTableView("checkInCustBals", ApplyTransVol.transInBalDtl, ApplyTransVol.transInDisCode);
		/**other*/
		BodyView.setShowOperInfo(ApplyTransVol.checkOrder);
	},
	
	setTransInBankInfo : function(data){
		var body = data.body || {};
		var custBanks = body.custBanks || [];
		
		var checkInBanks = {};
		checkInBanks.bankAcct = ApplyTransVol.checkOrder.bankAcct;
		checkInBanks.bankCode = ApplyTransVol.checkOrder.bankCode;
		
		$(custBanks).each(function(index, element) {
			if(element.cpAcctNo == ApplyTransVol.checkOrder.cpAcctNo){
				checkInBanks.bankRegionName = element.bankRegionName;
				return;
			}		
		});
		
		// 转入银行卡
		BodyView.setTransInBankTableView("checkInBanks", checkInBanks);
	}
}
