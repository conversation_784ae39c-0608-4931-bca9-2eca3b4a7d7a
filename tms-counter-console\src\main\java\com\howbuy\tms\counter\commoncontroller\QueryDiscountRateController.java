/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.date.DateUtil;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.FeeDto;
import com.howbuy.web.util.IpUtil;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description:(TODO 请在此添加描述)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年11月24日 下午3:33:00
 * @since JDK 1.6
 */
@Controller
public class QueryDiscountRateController extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(QueryDiscountRateController.class);

    @RequestMapping("tmscounter/fund/caldiscountrate.htm")
    public void calDiscountRate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        LOGGER.info("QueryDiscountRateController|calDiscountRate|nowTime:{},ipAddr:{}", DateUtil.getSystemDate("YYYYMMDDHHMMSS"), IpUtil.getIpAddr(request));
        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);

        String fundCode = request.getParameter("fundCode");
        String bankCode = request.getParameter("bankCode");

//        DisInfoDto disInfoDto = new DisInfoDto();
//        disInfoDto.setDisCode(custInfoDto.getRegDisCode());

        FeeDto feeDto = tmsFundCounterService.calDiscountRate(custInfoDto, fundCode, bankCode);
        LOGGER.debug("tmsCounterService|calDiscountRate|resp|feeDto:{}", JSON.toJSONString(feeDto));

        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respData", feeDto);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }
}
