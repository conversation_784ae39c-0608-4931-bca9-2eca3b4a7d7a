/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.validator.fundinfo.FundInfoValidator;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.pension.order.client.facade.query.QueryAccountRelationFacade;
import com.howbuy.tms.pension.order.client.request.query.QueryAccountRelationRequest;
import com.howbuy.tms.pension.order.client.request.query.QueryAccountRelationResultRequest;
import com.howbuy.tms.pension.order.client.response.query.QueryAccountRelationResponse;
import com.howbuy.tms.pension.order.client.response.query.QueryAccountRelationResultResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 
 * @description:(转托管转入或转出)
 * <AUTHOR>
 * @date 2018年10月9日 下午3:20:24
 * @since JDK 1.6
 */
@Controller
public class TransferTubeFundController extends AbstractController {
    private static Logger logger = LogManager.getLogger(TransferTubeFundController.class);

    @Autowired
    private QueryAccountRelationFacade queryAccountRelationFacade;
    /**
     * 
     * transferTubeInConfirm:(转托管转入确认)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月9日 下午3:23:24
     */
    @RequestMapping(value = "/tmscounter/fund/transfertubeinconfirm.htm")
    public ModelAndView transferTubeInConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String transInConfirmCmd = request.getParameter("transfertubeInForm");
        String transInVolListCmd = request.getParameter("transfertubeInListForm");
        String transInVolCmd = request.getParameter("transInVolCmd");
        String custInfoForm = request.getParameter("custInfoForm");
        String fundInfoListForm = request.getParameter("fundInfoListForm");
        String fundInfoForm = request.getParameter("fundInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.debug("TransferTubeFundController|transferTubeInConfirm|transInConfirmCmd:{},transInVolListCmd:{}, custInfoForm:{},fundInfoListForm:{},transactorInfoForm:{},fundInfoForm:{}",
                transInConfirmCmd, transInVolListCmd, custInfoForm, fundInfoListForm, transactorInfoForm,fundInfoForm);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        CounterTransferTubeReqDto transferTubeInReqDto = JSON.parseObject(transInConfirmCmd, CounterTransferTubeReqDto.class);
        List<TransferTubeOrderReqDto> transInVolDtoList =  JSON.parseObject(transInVolListCmd, new TypeReference<List<TransferTubeOrderReqDto>>(){});;

        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        List<FundInfoAndNavDto> fundInfoList = JSON.parseObject(fundInfoListForm, new TypeReference<List<FundInfoAndNavDto>>(){});

        if(CollectionUtils.isEmpty(fundInfoList)){
            fundInfoList = new ArrayList<>();
            FundInfoAndNavDto fundInfo = JSON.parseObject(fundInfoForm, FundInfoAndNavDto.class);
            fundInfoList.add(fundInfo);
        }

        if(CollectionUtils.isEmpty(transInVolDtoList)){
            transInVolDtoList = new ArrayList<>();
            TransferTubeOrderReqDto transInVolDto = JSON.parseObject(transInVolCmd, TransferTubeOrderReqDto.class);
            transInVolDto.setFundCode(fundInfoList.get(0).getFundCode());
            transInVolDtoList.add(transInVolDto);
        }

        Map<String, FundInfoAndNavDto> fundInfoAndNavDtoMap = fundInfoList.stream().filter(distinctByKey(e -> e.getFundCode())).collect(Collectors.toMap(FundInfoAndNavDto::getFundCode, o -> o));
        Map<String, TransferTubeOrderReqDto> transferTubeOrderReqDtoMap = transInVolDtoList.stream().filter(distinctByKey(e -> e.getFundCode())).collect(Collectors.toMap(TransferTubeOrderReqDto::getFundCode, o -> o));


        transferTubeInReqDto.setDealAppNo(dealAppNo);
//        transferTubeInReqDto.setOriginalAppDealNo(transferTubeInReqDto.getOriginalAppDealNo());
        // 1127-转托管转入申请
        transferTubeInReqDto.setmBusiCode(BusinessCodeEnum.TRANS_MANAGE_IN_APP.getMCode());
//        transferTubeInReqDto.setTransferTubeBusiType(transferTubeInReqDto.getTransferTubeBusiType());
        // 对方销售人代码
//        transferTubeInReqDto.settSellerCode(transferTubeInReqDto.gettSellerCode());
//        transferTubeInReqDto.setAppDt(transferTubeInReqDto.getAppDt());
//        transferTubeInReqDto.setAppTm(transferTubeInReqDto.getAppTm());
        
        // 转入基金
        /*transferTubeInReqDto.setFundCode(fundInfo.getFundCode());
        transferTubeInReqDto.setFundName(fundInfo.getFundAttr());
        transferTubeInReqDto.setFundShareClass(fundInfo.getFundShareClass());
        transferTubeInReqDto.setTaCode(fundInfo.getTaCode());*/

        transferTubeInReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());

        transferTubeInReqDto.setTransferTubeDetailList(transInVolDtoList);

        // 转入客户信息
        transferTubeInReqDto.setCustName(custInfoDto.getCustName());
        transferTubeInReqDto.setTxAcctNo(custInfoDto.getCustNo());
        transferTubeInReqDto.setDisCode(custInfoDto.getDisCode());
        transferTubeInReqDto.setIdNo(custInfoDto.getIdNo());
        transferTubeInReqDto.setIdType(custInfoDto.getIdType());
        transferTubeInReqDto.setInvstType(custInfoDto.getInvstType());

        transferTubeInReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        transferTubeInReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        transferTubeInReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        
        // 柜台操作经办信息
        CommonUtil.setCommonOperInfo(operatorInfoCmd, transferTubeInReqDto);
        transferTubeInReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        transferTubeInReqDto.setConsCode(transactorInfoDto.getConsCode());
        transferTubeInReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        transferTubeInReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        transferTubeInReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        transferTubeInReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        List<CounterTransferTubeReqDto> dtoList = transInVolDtoList.stream().filter(Objects::nonNull).map(e -> {
            CounterTransferTubeReqDto dto = new CounterTransferTubeReqDto();
            BeanUtils.copyProperties(transferTubeInReqDto, dto);
            FundInfoAndNavDto fundInfoAndNavDto = fundInfoAndNavDtoMap.get(e.getFundCode());
            TransferTubeOrderReqDto info = transferTubeOrderReqDtoMap.get(e.getFundCode());
            dto.setOriginalAppDealNo(e.getOriginalAppDealNo());
            dto.setFundCode(fundInfoAndNavDto.getFundCode());
            dto.setFundName(fundInfoAndNavDto.getFundAttr());
            dto.setFundShareClass(fundInfoAndNavDto.getFundShareClass());
            dto.setTaCode(fundInfoAndNavDto.getTaCode());
            TransferTubeOrderReqDto reqDto = buildReqDto(fundInfoAndNavDto, info);
            dto.setTransferTubeDetailList(Lists.newArrayList(reqDto));
            return dto;
        }).collect(Collectors.toList());


        TmsCounterResult rst = tmsFundCounterService.counterTransferInTube(dtoList, disInfoDto);

        WebUtil.write(response, rst);
        return null;
    }

    private TransferTubeOrderReqDto buildReqDto(FundInfoAndNavDto dto, TransferTubeOrderReqDto info){
        TransferTubeOrderReqDto reqDto  = new TransferTubeOrderReqDto();
        reqDto.setFundName(dto.getFundName());
        reqDto.setFundShareClass(dto.getFundShareClass());
        reqDto.setFundName(dto.getFundAttr());
        reqDto.setProductChannel(ProductChannelEnum.FUND.getCode());
        reqDto.setTaCode(dto.getTaCode());
        reqDto.setAppVol(info.getAppVol());
        reqDto.setBankCode(info.getBankCode());
        reqDto.setBankAcct(info.getBankAcct());
        reqDto.setCpAcctNo(info.getCpAcctNo());
        return reqDto;
    }



    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
    
    /**
     * 
     * transferTubeOutConfirm:(转托管转出确认)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月9日 下午3:23:24
     */
    @RequestMapping("/tmscounter/fund/transfertubeoutconfirm.htm")
    public ModelAndView transferTubeOutConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String transfertubeOutCmd = request.getParameter("transfertubeOutForm");
        String transOutVolCmd = request.getParameter("transOutVolCmd");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.debug("TransferTubeFundController|transferTubeOutConfirm|transfertubeOutForm:{},transOutVolCmd:{}, custInfoForm:{},transactorInfoForm:{}",
                transfertubeOutCmd, transOutVolCmd, custInfoForm, transactorInfoForm);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        CounterTransferTubeReqDto transferTubeOutReqDto = JSON.parseObject(transfertubeOutCmd, CounterTransferTubeReqDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);

//        if(FundInfoValidator.isYLFof(fundInfo.getFundType(), fundInfo.getFundSubType())){
//            WebUtil.write(response, new TmsCounterResult(TmsCounterResultEnum.YL_FOF_NOT_SUPPORT_TRANSFER_OUT));
//            return null;
//        }

        List<TransferTubeOrderReqDto> outDtoList = JSON.parseArray(transOutVolCmd, TransferTubeOrderReqDto.class);
        if(CollectionUtils.isEmpty(outDtoList)){
            throw new TmsCounterException(TmsCounterResultEnum.TRANSFER_TUBE_OUT_ORDER_NOT_SELECTED);
        }
        transferTubeOutReqDto.setTransferTubeDetailList(outDtoList);

        transferTubeOutReqDto.setDealAppNo(dealAppNo);
        // 1128-转托管转出申请
        transferTubeOutReqDto.setmBusiCode(BusinessCodeEnum.TRANS_MANAGE_OUT_APP.getMCode());
        
//        // 转出基金
//        transferTubeOutReqDto.setFundCode(fundInfo.getFundCode());
//        transferTubeOutReqDto.setFundName(fundInfo.getFundAttr());
//        transferTubeOutReqDto.setFundShareClass(fundInfo.getFundShareClass());
//        transferTubeOutReqDto.setProductClass(fundInfo.getProductClass());
//        transferTubeOutReqDto.setTaCode(fundInfo.getTaCode());
        // 转出客户信息
        transferTubeOutReqDto.setCustName(custInfoDto.getCustName());
        transferTubeOutReqDto.setTxAcctNo(custInfoDto.getCustNo());
        transferTubeOutReqDto.setDisCode(custInfoDto.getDisCode());
        transferTubeOutReqDto.setIdNo(custInfoDto.getIdNo());
        transferTubeOutReqDto.setIdType(custInfoDto.getIdType());
        transferTubeOutReqDto.setInvstType(custInfoDto.getInvstType());

        transferTubeOutReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        transferTubeOutReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        transferTubeOutReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        
        // 柜台操作经办信息
        CommonUtil.setCommonOperInfo(operatorInfoCmd, transferTubeOutReqDto);
        transferTubeOutReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        transferTubeOutReqDto.setConsCode(transactorInfoDto.getConsCode());
        transferTubeOutReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        transferTubeOutReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        transferTubeOutReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        transferTubeOutReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        
        
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        TmsCounterResult rst = tmsFundCounterService.counterTransferTube(transferTubeOutReqDto, disInfoDto);

        WebUtil.write(response, rst);

        return null;
    }

    /**
     * 查询税延账户对应关系
     * @author: junkai.du
     * @date: 2023/7/30 09:56
     * @since JDK 1.8
     */
    @RequestMapping(value = "/tmscounter/fund/queryaccountrelation.htm")
    public ModelAndView queryAccountRelation(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 对方销售人处投资者基金交易账号
        String transactionAccountID = request.getParameter("transactionAccountID");
        // 对方网点/席位号
        String branchCode = request.getParameter("branchCode");
        // 交易账号
        String txAcctNo = request.getParameter("txAcctNo");
        // taCode
        String taCode = request.getParameter("taCode");

        QueryAccountRelationRequest relationRequest = new QueryAccountRelationRequest();
        relationRequest.setTxAcctNo(txAcctNo);
        relationRequest.setBranchCode(branchCode);
        relationRequest.setTransactionAccountID(transactionAccountID);
        relationRequest.setTaCode(taCode);

        QueryAccountRelationResponse relationResponse = queryAccountRelationFacade.queryAccountRelation(relationRequest);

        if(!TmsFacadeUtil.isSuccess(relationResponse.getResCode())){
            throw new TmsCounterException(relationResponse.getResCode(), relationResponse.getResMsg());
        }

        // 发送中登失败，重新提交
        if (!Objects.isNull(relationResponse) && YesOrNoEnum.NO.getCode().equals(relationResponse.getSuccessFlag())){
            throw new TmsCounterException(TmsCounterResultEnum.SEND_PENSION_RETRY.getCode(),TmsCounterResultEnum.SEND_PENSION_RETRY.getDesc());
        }

        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("partiAppSheetNo", relationResponse.getPartiAppSheetNo());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(bodyResult);
        WebUtil.write(response, rst);
        return null;
    }


    /**
     * 查询税延账户对应关系结果
     * @author: junkai.du
     * @date: 2023/7/30 09:56
     * @since JDK 1.8
     */
    @RequestMapping(value = "/tmscounter/fund/queryaccountrelationresult.htm")
    public ModelAndView queryAccountRelationResult(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 交易账号
        String partiAppSheetNo = request.getParameter("partiAppSheetNo");

        QueryAccountRelationResultRequest relationRequest = new QueryAccountRelationResultRequest();
        relationRequest.setPartiAppSheetNo(partiAppSheetNo);

        QueryAccountRelationResultResponse relationResponse = queryAccountRelationFacade.queryAccountRelationResult(relationRequest);

         String result = StringUtils.EMPTY;
        // 关系结果存在
        if (!Objects.isNull(relationResponse) && StringUtils.isNotEmpty(relationResponse.getExistFlag())){
            result = relationResponse.getExistFlag();
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC.getCode(),TmsCounterResultEnum.SUCC.getDesc(), result);

        WebUtil.write(response, rst);
        return null;
    }

}
