/**
 * 显示TA输入选择框
 * <AUTHOR>
 */
$(function(){
	//TaInputSelBox.taList = [];
});

TaInputSelBox = {
		
		/**
		 * 构建TA选择框
		 * @param id
		 * @param taList
		 */
		buildTaInputSelBox:function(id, taList){
			$("#" + id).empty();
			var appendHtml = '';
			appendHtml += '<div id="taInputSelBox" class="taInputSelBox">';
			// 记录选择的TA已逗号分隔
			appendHtml += '<input type="hidden" id="selCheckedTA" value="">';
			appendHtml += '<span class="ml20">选择TA：</span>';
			appendHtml += '<input type="text" placeholder="输入关键字过滤" class="selectInputTa" name="inputTaCode" id="inputTaCode" list="taListTbody" value="" />';
			appendHtml += '<div class="picture_click dropDowns"></div>';
			// taList_box
			appendHtml += '<div class="taList_box">';
			appendHtml += '<p class="ml10"><span class="selTATitle">已选择0个TA</span><a class="btn btn-link radius"  id="confirm">确定</a></p>';
			// table 
			appendHtml += '<div class="taTable">';
			appendHtml += '<table class="table table-border table-bordered table-hover table-bg table-sort">';
			appendHtml += '<tbody id="taListTbody">';
			if(taList.length <= 0){
				appendHtml += '<tr class="text-c"><td colspan="3">暂无数据</td></tr>';
			} else{
				$(taList).each(function(index,element){
					appendHtml += '<tr class="text-c" id="'+element.taCode+'">';
					appendHtml += '<td class="checkboxTd"><input type="checkbox" value=\"'+element.taCode+'\" taName=\"'+element.taName+'\" dataIndex="'+index+'" class="taSelCheckBox"></td>';
					appendHtml += '<td>'+formatData(element.taCode)+'</td>';
					appendHtml += '<td>'+formatData(element.taName)+'</td>';
					appendHtml += '</tr>';
			     });
			}
			appendHtml += '</tbody>';
			appendHtml += '</table>';
			// table end
			appendHtml += '</div>';
			appendHtml += '<p class="ml10"><input type="checkbox" id="all" value="all" class="checkboxInput"><span class="allSelTitle">全选</span><a class="btn btn-link radius" id="clear">清除</a></p>';
			// taList_box end
			appendHtml += '</div>';
			
			// taInputSelBox end
			appendHtml += '</div>';
			
			// add TA box
			$("#"+id).append(appendHtml);
			
			// 图片向下点击事件
			TaInputSelBox.pictureClick(id, taList);
			
			// 单点击复选框事件
			TaInputSelBox.changeTaSelCheckBox(id);
			
			 // 全选
			TaInputSelBox.allClickCheckBox(id);
			 
			 // 清除
			TaInputSelBox.clearBtnClick(id);
			 
			 // 确定
			TaInputSelBox.confirmBtnClick(id);

			// TA输入框绑定事件监听
			var inputTaCode = $("#"+id+" #inputTaCode")[0];
			//var input = document.getElementById("inputTaCode");
			inputTaCode.addEventListener("input", function(event) {
				 TaInputSelBox.fuzzySearchCall(id, taList, this);
			});
			
			// 点击空白处下拉框隐藏
			TaInputSelBox.blankAreaClick(id);
		},
		
		/**
		 * 点击空白处下拉框隐藏
		 * @param id
		 */
		blankAreaClick:function(id){
		     $(document).click(function(event){
		          var selTaArea = $('#'+id);   // 设置目标区域
		          if(!selTaArea.is(event.target) && selTaArea.has(event.target).length === 0){ 
		              //$("#"+id+" .taList_box").slideUp('slow');   //滑动消失
		        	  //$("#"+id+" .taList_box").hide(1000);     //淡出消失
		        	  $("#"+id+" .taList_box").css("display", "none");
		          }
		    });
		},
		
		/**
		 * 图片向下点击事件
		 * @param id
		 */
		pictureClick:function(id, taList){
			
			$("#"+id+" .picture_click").off();
			$("#"+id+" .picture_click").on("click",function(){
				if($("#"+id+" .taList_box").css("display")=="none"){
					$("#"+id+" .taList_box").css("display", "block");
					 // 渲染数据
		            TaInputSelBox.taListTbodyLoad(id, taList);
				} else{
					$("#"+id+" .taList_box").css("display", "none");
				}
				// init input 
				$("#"+id+" .selectInputTa").val('');
				// init checkbox
				$("#"+id+" input[type='checkbox']").each(function(index,element){
				 	$(element).prop("checked",false);
		    	});
				// set selTATitle
				 $("#"+id+" .selTATitle").html('已选择0个TA');
				 $("#"+id+" #selCheckedTA").val('');
			});
		},
		
		/**
		 * 清除按钮事件
		 * @param id
		 */
		clearBtnClick:function(id){
			 $("#"+id+" #clear").off();
			 $("#"+id+" #clear").on('click',function(){
				$("#"+id+" input[type='checkbox']").each(function(index,element){
				 	$(element).prop("checked",false);
		    	});
				
				 // set selTATitle
				 $("#"+id+" .selTATitle").html('已选择0个TA');
				 $("#"+id+" #selCheckedTA").val('');
			 });
		},
		
		/**
		 * 确定按钮事件
		 * @param id
		 */
		confirmBtnClick:function(id){
			 $("#"+id+" #confirm").off();
			 $("#"+id+" #confirm").on('click',function(){
				 // set hidden TA
				 TaInputSelBox.setSelCheckedTA(id);
				 
				 $("#"+id+" .taList_box").css("display", "none");
				 $("#"+id+" .selectInputTa").val($("#"+id+" .selTATitle").html());
			 });
		},
		
		
		/**
		 * 单点击复选框事件
		 * @param id
		 */
		changeTaSelCheckBox:function(id){
			$("#"+id+" .taSelCheckBox").off();
			$("#"+id+" .taSelCheckBox").change(function() { 
				// set hidden TA
				 TaInputSelBox.setSelCheckedTA(id);
				 
				var taCodeStr = $("#"+id+" #selCheckedTA").val();
				var selCount = (taCodeStr == '') ? 0 : taCodeStr.split(',').length;
				// set selTATitle
				 $("#"+id+" .selTATitle").html('已选择'+selCount+'个TA');
			});
		},
		
		/**
		 * 全选点击事件
		 * @param id
		 */
		allClickCheckBox:function(id){
			 $("#"+id+" #all").off();
			 $("#"+id+" #all").on('click',function(){
				 TaInputSelBox.checkedOrUncheckedAll(id);
			 });
		},
		
		/**
		 * 清除初始TA选择框
		 * @param id
		 */
		clearInitBox:function(id){
			$("#"+id+" input[type='checkbox']").each(function(index,element){
			 	$(element).prop("checked",false);
	    	});
			 // set selTATitle
			 $("#"+id+" .selTATitle").html('已选择0个TA');
			 $("#"+id+" #selCheckedTA").val('');
		},
		
		/**
		 * 设置选中的TA值
		 */
		setSelCheckedTA:function(id){
			var taCodeList = "";
			$("#"+id+" input[type='checkbox']").each(function(index,element){
				var value = $(element).val();
				if(value != 'all' && $(element).is(":checked")){
					taCodeList +=(value+",");
				}
			});
			taCodeList = taCodeList.substring(0, taCodeList.length-1);
			$("#"+id+" #selCheckedTA").val(taCodeList);
		},
		
		/**
	     * 全选或反选
	     * @param id
	     */
	     checkedOrUncheckedAll:function(id){
	    	var checked = $("#"+id+" #all").is(':checked');
	    	
	    	if(checked){
	    		$("#"+id+" input[type='checkbox']").each(function(index,element){
	    			if($(element).attr("id") != "all"){
	    				$(element).prop("checked",true);
	    			}
	    		});
	    		// -1 剔除全选按钮
	    		var selCount = $("#"+id+" input[type='checkbox']").length - 1;
	    		 // set selTATitle
				 $("#"+id+" .selTATitle").html('已选择'+selCount+'个TA');
	    	}else{
	    		$("#"+id+" input[type='checkbox']").each(function(index,element){
	    			if($(element).attr("id") != "all"){
	    				$(element).prop("checked",false);
	    			}
	    		});
	    		
	    		// set selTATitle
				$("#"+id+" .selTATitle").html('已选择0个TA');
	    	}
	    },
	    
	    /**
	     * TA输入下拉框数据渲染
	     * @param id
	     * @param taList
	     */
	    taListTbodyLoad:function(id, taList){
            $("#"+id+" #taListTbody").empty();
            if(taList.length <= 0){
				var trHtml = '<tr><td colspan="3">暂无数据</td></tr>';
		    	 $("#"+id+" #taListTbody").append(trHtml);
			} else{
				 $(taList).each(function(index,item){
			    	 var trList = [];
			    	 trList.push('<td class="checkboxTd"><input type="checkbox" value=\"'+item.taCode+'\" taName=\"'+item.taName+'\" dataIndex="'+index+'" class="taSelCheckBox"></td>');
			    	 trList.push('<td>'+formatData(item.taCode)+'</td>');
			    	 trList.push('<td>'+formatData(item.taName)+'</td>');

			    	 var trHtml = '<tr class="text-c" id="'+item.taCode+'">'+trList.join()+'</tr>';
			    	 $("#"+id+" #taListTbody").append(trHtml);
			     }); 
			}
            // 清除后重新绑定单点击复选框事件
            TaInputSelBox.changeTaSelCheckBox(id);
            
            // 清除
			TaInputSelBox.clearInitBox(id);
	    },
	    
	    /**
	     * 输入模糊查询
	     * @param thisObj
	     */
	    fuzzySearchCall:function(id, taList, thisObj){

            var searchVal = $("#"+id+" #inputTaCode").val().replace(' ','');
            var showList = [];
            if(isEmpty(searchVal)){
            	showList = taList;
            } else{
            	// 过滤数据
                $.each(taList, function(index, item){
                    if(item.taCode.indexOf(searchVal) != -1) {
                    	showList.push(item);
                    }
                });
            }
            
            // 渲染数据
            TaInputSelBox.taListTbodyLoad(id, showList);
			
	    	//列表框显示
	        $("#"+id+" .taList_box").css("display", "block");
	    }
};