/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;
import java.util.List;

/**
 * 
 * @description:(客户基金银行卡持有持仓份额)
 * <AUTHOR>
 * @date 2018年5月4日 上午11:13:12
 * @since JDK 1.6
 */
public class QueryCustBankBalVolRespDto extends BaseResponseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -895686267995398079L;

    /**
     * 交易账号
     */
    private String txAcctNo;
    
    /**
     * 分销机构号
     */
    private String disCode;
    
    /**
     * 客户持仓明细
     */
    private List<CustBalDtlDto> custBalDtlList;

    /**
     * 客户持仓银行卡信息
     */
    private List<CustBankCardDto> custBankCardList;

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public List<CustBalDtlDto> getCustBalDtlList() {
        return custBalDtlList;
    }

    public void setCustBalDtlList(List<CustBalDtlDto> custBalDtlList) {
        this.custBalDtlList = custBalDtlList;
    }

    public List<CustBankCardDto> getCustBankCardList() {
        return custBankCardList;
    }

    public void setCustBankCardList(List<CustBankCardDto> custBankCardList) {
        this.custBankCardList = custBankCardList;
    }
}
