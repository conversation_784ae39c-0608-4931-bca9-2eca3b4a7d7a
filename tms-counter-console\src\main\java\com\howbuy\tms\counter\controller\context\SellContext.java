/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;

import com.howbuy.tms.counter.dto.CounterRedeemReqDto;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto;

import java.util.List;

/**
 * @className SellContext
 * @description
 * <AUTHOR>
 * @date 2019/6/10 16:24
 */
public class SellContext extends TradeCommonContext{
    private CounterRedeemReqDto counterRedeemReqDto;
    private List<QueryAcctBalanceDtlRespDto.DtlBean> dtlList;

    public CounterRedeemReqDto getCounterRedeemReqDto() {
        return counterRedeemReqDto;
    }

    public void setCounterRedeemReqDto(CounterRedeemReqDto counterRedeemReqDto) {
        this.counterRedeemReqDto = counterRedeemReqDto;
    }

    public List<QueryAcctBalanceDtlRespDto.DtlBean> getDtlList() {
        return dtlList;
    }

    public void setDtlList(List<QueryAcctBalanceDtlRespDto.DtlBean> dtlList) {
        this.dtlList = dtlList;
    }
}
