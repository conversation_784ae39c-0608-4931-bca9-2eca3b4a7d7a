<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<!--统一异常处理类-->
	<bean id="exceptionHandler" class="com.howbuy.tms.counter.common.exception.ExceptionHandler"/>
	
	<!-- 拦截器 -->
	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/**"/>
			<mvc:exclude-mapping path="/error/**"/>
			<mvc:exclude-mapping path="/fckeditor/**"/>
			<mvc:exclude-mapping path="/static/**"/>
			<mvc:exclude-mapping path="/images/**"/>
			<mvc:exclude-mapping path="/js/**"/>
			<mvc:exclude-mapping path="/versionnewbatch/**"/>
			<mvc:exclude-mapping path="/lib/**"/>
			<mvc:exclude-mapping path="/passport/login"/>
			<mvc:exclude-mapping path="/tmscounter/loginView.htm"/>
			<mvc:exclude-mapping path="/tmscounter/logout.htm"/>
			<mvc:exclude-mapping path="/tmscounter/login.htm"/>
			<mvc:exclude-mapping path="/tmscounter/selflogin.htm"/>
			<mvc:exclude-mapping path="/login"/>
			<mvc:exclude-mapping path="/loginview"/>
			<mvc:exclude-mapping path="/passport/loginbydialog"/>
			<mvc:exclude-mapping path="/generateVerifyPicCode"/>
			<mvc:exclude-mapping path="/actuator/health"/>
			<bean class="com.howbuy.tms.counter.filter.TmsCounterFilter" />
		</mvc:interceptor>
	</mvc:interceptors>

</beans>
