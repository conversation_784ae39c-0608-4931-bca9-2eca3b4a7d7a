/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:(柜台订单查询请求)
 * <AUTHOR>
 * @date 2017年3月29日 下午8:25:00
 * @since JDK 1.6
 */
public class CounterQueryOrderRespDto extends BaseResponseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -3785906850960593473L;

    private List<CounterOrderDto> counterOrderList;

    /**
     * 当页小计-申请金额
     */
    private BigDecimal pageAppAmt;

    /**
     * 当页小计-申请份额
     */
    private BigDecimal pageAppVol;

    /**
     * 合计-申请金额
     */
    private BigDecimal totalAppAmt;

    /**
     * 合计-申请份额
     */
    private BigDecimal totalAppVol;

    public List<CounterOrderDto> getCounterOrderList() {
        return counterOrderList;
    }

    public void setCounterOrderList(List<CounterOrderDto> counterOrderList) {
        this.counterOrderList = counterOrderList;
    }

    public BigDecimal getPageAppAmt() {
        return pageAppAmt;
    }

    public void setPageAppAmt(BigDecimal pageAppAmt) {
        this.pageAppAmt = pageAppAmt;
    }

    public BigDecimal getPageAppVol() {
        return pageAppVol;
    }

    public void setPageAppVol(BigDecimal pageAppVol) {
        this.pageAppVol = pageAppVol;
    }

    public BigDecimal getTotalAppAmt() {
        return totalAppAmt;
    }

    public void setTotalAppAmt(BigDecimal totalAppAmt) {
        this.totalAppAmt = totalAppAmt;
    }

    public BigDecimal getTotalAppVol() {
        return totalAppVol;
    }

    public void setTotalAppVol(BigDecimal totalAppVol) {
        this.totalAppVol = totalAppVol;
    }

}
