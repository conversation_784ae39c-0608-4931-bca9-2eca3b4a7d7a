<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 高端产品参数配置 -->
	<dubbo:reference id="tmscounter.highProductParamConfService" interface="com.howbuy.interlayer.product.service.HighProductParamConfService" registry="product-center-remote" check="false"/>

	<!-- 查询ta信息 -->
	<dubbo:reference id="tmscounter.queryTaInfoService" interface="com.howbuy.interlayer.product.service.QueryTaInfoService" registry="product-center-remote" check="false"/>
	
	<!-- 高端产品服务 -->
	<dubbo:reference id="tmscounter.highProductService" interface="com.howbuy.interlayer.product.service.HighProductService" registry="product-center-remote" check="false" />

	<!-- 查询代销场检产品代码 -->
	<dubbo:reference id="tmscounter.queryNotHBJGFundListService" interface="com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService" registry="product-center-remote" check="false" />

	<dubbo:reference id="tmscounter.fundBaseInfoService" interface="com.howbuy.interlayer.product.service.fund.FundBaseInfoService" registry="product-center-remote" check="false" />

	<dubbo:reference id="tmscounter.fundProductService" interface="com.howbuy.interlayer.product.service.FundProductService" registry="product-center-remote" check="false" />

	<!-- 组合产品 -->
	<dubbo:reference id="tmscounter.portfolioProductService" interface="com.howbuy.interlayer.product.service.PortfolioProductService" check="false" registry="product-center-remote"/>

	<dubbo:reference id="tmscounter.portfolioUProductInfoService" interface="com.howbuy.interlayer.product.service.PortfolioUProductInfoService" check="false" registry="product-center-remote"/>

</beans>