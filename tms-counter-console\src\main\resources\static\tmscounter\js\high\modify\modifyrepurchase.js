/**
*修改复购协议审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
    Modifyrepurchase.init();
});

var Modifyrepurchase = {

    /**
     * 初始化
     */
    init:function(){
        // 查询订单信息
        Modify.queryCounterDealOrder(this.queryCounterDealOrderCallBack, null);
        
        $("#appVol").on("change", function () {
            var balanceVol =  $("#balanceVol").text();
            var repurchaseVol =  $("#appVol").val();

            $("#repurchaseType").val(CommonUtil.getRepurchaseType(repurchaseVol, balanceVol));
        });
    },

    queryCounterDealOrderCallBack:function(data){
		var bodyData = data.body || {};
        var counterOrderDto = bodyData.counterOrderDto || {};
        var counterOrder = bodyData.counterOrder || {};//订单信息
        var repurchaseInfo = counterOrder.counterRepurChaseInfoBean || {};// 复购信息
        Modify.modifyDealOrder = counterOrderDto;
		Modifyrepurchase.buildDealInfo(counterOrderDto);// 订单信息
        Modifyrepurchase.buildRepurchaseInfo(repurchaseInfo);// 复购信息
	},

    /**
     * 订单信息
     * @param checkOrder
     */
    buildDealInfo:function(checkOrder){
        $("#productCode").val(checkOrder.fundCode);//基金代码
        $("#taCode").html(checkOrder.taCode);//基金代码
        $("#fundName").html(checkOrder.fundName);//基金代码
        $("#idNo").html(checkOrder.idNo);// 证件号
        $("#custName").html(checkOrder.custName);// 客户姓名
        $("#txAcctNo").val(checkOrder.txAcctNo);//客户号
        $("#appVol").val(CommonUtil.formatAmount(checkOrder.appVol));//复购份额
        $("#repurchaseType").val(checkOrder.repurchaseType);//复购类型

        $("#checker").html(CommonUtil.formatData(checkOrder.checker));//审核人
        $("#memo").html(CommonUtil.formatData(checkOrder.memo));//退回原因

    },

    /**
     * 复购信息
     * @param checkOrder
     */
    buildRepurchaseInfo:function(repurchaseInfo){
        $("#balanceVol").html(CommonUtil.formatAmount(repurchaseInfo.balanceVol));// 持有份额
        $("#redeemVol").html(CommonUtil.formatAmount(repurchaseInfo.redeemVol));// 赎回份额
        $("#frznVol").html(CommonUtil.formatAmount(repurchaseInfo.frznVol));//
        $("#expectedDueDt").html(repurchaseInfo.expectedDueDt);// 预计到期日
    }

};
