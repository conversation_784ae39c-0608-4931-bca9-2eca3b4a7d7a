/***
*购买
*<AUTHOR>
*@date 2017-07-03 10:39
 */
$(function(){
    		$("#queryCustBtn").off();
			$("#queryCustBtn").on('click',function(){
				QueryCustInfoSubPage.queryCustInfo();
			});
			$("#resetLayerBtn").off();
			$("#resetLayerBtn").on('click',function(){
				QueryCustInfoSubPage.reset('seachCustForm');
			});
	var url = window.location.href;
	if (url.indexOf("parentCallback=")>0) {
		QueryCustInfoSubPageData.parentCallback = url.split("parentCallback=")[1].split("&")[0];
	}
});

var frameIndex = parent.layer.getFrameIndex(window.name); //获取窗口索引

//给父页面传值
$('#transmit').on('click', function(){
    var checkedInputs = $("#custPosiTable").find("input[type='checkbox']:checked");
	if(checkedInputs.length <= 0){
		showMsg("请选择客户")
		return false ;
	}else if(checkedInputs.length >1){
		showMsg("不能多选，只能选择一个客户")
		return false;
	}else{
		// 指定回调方法时调用回调方法，未指定时赋值特定输入框
		if (QueryCustInfoSubPageData.parentCallback) {
			// 获取选中的数据
			var custInfo = QueryCustInfoSubPageData.custInfoList[$(checkedInputs[0]).attr("index")];
			// 获取父类回调方法
			var fs = QueryCustInfoSubPageData.parentCallback.split(".");
			var f = parent;
			for (var i=0; i<fs.length; i++) {
				f = f[fs[i]];
			}
			// 执行回调
			f(custInfo);
			QueryCustInfoSubPageData.parentCallback = null;
		} else {
			var custNo = $(checkedInputs[0]).val();
			parent.$('#custNo').val(custNo);
		}
		$("#layerrs").empty();
		QueryCustInfoSubPage.reset('seachCustForm');
		parent.layer.close(frameIndex);		
	}
 });

var QueryCustInfoSubPageData = {
	parentCallback:null,
	custInfoList:[]
}


QueryCustInfoSubPage = {
		/**
		 * 客户信息查询弹出
		 * parentCallback 选定用户后父类的回调方法
		 */
		selectCustNo:function(targetobj, parentCallback){
			var contentUrl = '../../../html/high/query/querycustinfosubpage.html';
			if (parentCallback) {
				contentUrl = contentUrl + "?parentCallback="+parentCallback;
			}
			var offset = 'auto';
			if (targetobj != null) {
				offset = [targetobj.offset().top, targetobj.offset().left];
			}
			layer.open({
				  type: 2,
				  shade: [0.1 ,'#fff'],
				  title:'客户信息查询',
				  area: ['700px', '600px'],
				  offset: offset,
				  content: contentUrl
				});
		},
		/**
		 * 定位客户信息
		 */
		queryCustInfo:function(){
			var serachForm = $("#seachCustForm").serializeObject();
			var custNo = serachForm['custNo'];
			var custName = serachForm['custName'];
			var idNo = serachForm['idNo'];
			if(isEmpty(custNo) && isEmpty(custName) && isEmpty(idNo)){
				showMsg("客户号，客户姓名,证件号必须输入一项");
				return false;
			}
			var uri= TmsCounterConfig.QUERY_CUST_INFO_SUB_PAGE_URL  ||  {};
			var reqparamters = {};
			if(!isEmpty(custNo)){
				reqparamters.custNo = custNo;
			}else if(!isEmpty(custName) ){
				reqparamters.custName = encodeURIComponent(custName);
			}else if(!isEmpty(idNo)){
				reqparamters.idNo = idNo;
			}
			
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);

			CommonUtil.ajaxAndCallBack(paramters, QueryCustInfoSubPage.processCustInfoView);
			
		},
		/**
		 * 重置查询条件
		 * @param formId
		 */
		reset:function(formId){
			$("#"+formId).find("input").each(function(index,element){
				$(element).val('');
			})
		},
		
		/**
		 *渲染客户信息查询结果
		 */
		processCustInfoView:function(data){
			var bodyData = data.body || {};
			var respData = bodyData.respData || [];
			var custBaseInfoList = respData.custBaseInfoBeanList || [];
			QueryCustInfoSubPageData.custInfoList = custBaseInfoList;
			var len = custBaseInfoList.length;
			var appendHtml = '';
			$("#layerrs").empty();
			if(len <=0){
				appendHtml = '<tr><td colspan="5">没有查询结果</td><tr>';
				$("#layerrs").append(appendHtml);
			}
			else{
				$(custBaseInfoList).each(function(index,element){
					var appendHtml = '<tr class="text-c">'+
									'<td><input type="checkbox" value="'+element.txAcctNo+'" index="'+index+'"/></td>'+
									 '<td>'+formatData(element.txAcctNo)+'</td>'+
									 '<td>'+formatData(element.custName)+'</td>'+
							         '<td>'+getMapValue(CONSTANTS.BOTH_ID_TYPE_MAP, formatData(element.invstType +""+ element.idType))+'</td>'+
									 '<td>'+formatData(element.idNo)+'</td>'+
									 '<td>'+getMapValue(CONSTANTS.CUST_STAT_MAP,formatData(element.custStat))+'</td>'+
									 '<td>'+getMapValue(CONSTANTS.DISCODE_MAP,formatData(element.regDisCode))+'</td>'+
									'<tr>';
					$("#layerrs").append(appendHtml);
				});		
			}
		},
};