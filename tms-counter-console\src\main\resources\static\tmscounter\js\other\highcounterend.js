/**
*公募柜台收市
*<AUTHOR>
*/

$(function(){
	HighCounterEnd.taList = [];
	HighCounterEnd.notEndTaList = [];
	HighCounterEnd.fundTaList = [];
	HighCounterEnd.init();
});

var HighCounterEnd ={

	/**
	 * 初始化
	 */
	init:function(){
		
		// 初始加载查询
		HighCounterEnd.queryWorkDay();
		HighCounterEnd.queryTaList();
		HighCounterEnd.queryCounterEndTACount();
		HighCounterEnd.queryTaCounterNotEnd();
		
		// 柜台收市
		$("#counterEndBtn").off();
		$("#counterEndBtn").on('click',function(){
			HighCounterEnd.counterEnd();
		});
		
		// 添加不收市TA
		$("#addCounterNotEndTaBtn").off();
		$("#addCounterNotEndTaBtn").on('click',function(){
			HighCounterEnd.addNotEndTa();
		});
		
		// 筛选基金添加不收市TA
		$("#addFundFilNotEndTaBtn").off();
		$("#addFundFilNotEndTaBtn").on('click',function(){
			HighCounterEnd.popupSelFundTaPage();
		});
		
		// 查询基金TA
		$("#SelectFundTaPage #queryFundTaInfoBtn").off();
		$("#SelectFundTaPage #queryFundTaInfoBtn").on('click',function(){
			HighCounterEnd.queryFundTaInfo();
		});
		
		// 添加基金TA
		$("#SelectFundTaPage #addSelFundTaBtn").off();
		$("#SelectFundTaPage #addSelFundTaBtn").on('click',function(){
			HighCounterEnd.addSelFundTa();
		});
		
		// 重置
		$("#SelectFundTaPage #clearnTaInfoBtn").off();
		$("#SelectFundTaPage #clearnTaInfoBtn").on('click',function(){
			$("#SelectFundTaPage #inputfundCode").val('');
			$("#SelectFundTaPage #inputfundName").val('');
		});
		
		// 关闭
		$("#SelectFundTaPage #closeSelFundTaPage").off();
		$("#SelectFundTaPage #closeSelFundTaPage").on('click',function(){
			layerall_close();
		});
		
		HighCounterEnd.queryBatchFlowStat();// 节点状态判断收市按钮颜色
	},
	
	queryWorkDay:function(){
		$("#workDay").html(CommonUtil.getHighWorkDay());
	},
	
	queryTaList:function(){
		var uri = TmsCounterConfig.QUERY_HIGH_COUNTER_END_TA_LIST_URL;
		var paramters = CommonUtil.buildReqParams(uri, null, null,
				null, null);
		CommonUtil.ajaxAndCallBack(paramters, HighCounterEnd.queryCounterTAListCallBack);
	},
	queryCounterTAListCallBack:function(data){
		var retCode = data.code;
		if(retCode != "0000"){
			return;
		}
		// 选择TA
		HighCounterEnd.taList = data.body.result || {};
		TaInputSelBox.buildTaInputSelBox("counterEndTaSelBox", HighCounterEnd.taList);
	},
	/**
	 * 查询统计柜台列表
	 * @param uri
	 */
	queryCounterEndTACount:function(){
		var uri = TmsCounterConfig.QUERY_HIGH_TA_BUSINESS_COUNT_URL;
		var paramters = CommonUtil.buildReqParams(uri, null, null,
				null, null);
		CommonUtil.ajaxAndCallBack(paramters, HighCounterEnd.queryCounterTACountCallBack);
	},
	queryCounterTACountCallBack:function(data){
		var retCode = data.code;
		if(retCode != "0000"){
			return;
		}
		var respData = data.body.result;
		// 汇总信息
		$("#counterTaEndSum").empty();
		var appendHtml = '';
		appendHtml += '<tr class="text-c">';
		appendHtml += '<td><span>'+formatData(respData.allCount, "--")+'</span></td>';
		appendHtml += '<td><span>'+formatData(respData.doneCount, "--")+'</span></td>';
		appendHtml += '<td><span>'+formatData(respData.unDoneCount + respData.failCount, "--")+'</span></td>';
		appendHtml += '</tr>';
		$("#counterTaEndSum").append(appendHtml);
	},
	
	queryTaCounterNotEnd:function(){
		var uri = TmsCounterConfig.QUERY_HIGH_TA_COUNTER_NOT_END_URL;
		var paramters = CommonUtil.buildReqParams(uri, null, null,
				null, null);
		CommonUtil.ajaxAndCallBack(paramters, HighCounterEnd.queryTaCounterNotEndCallBack);
	},
	queryTaCounterNotEndCallBack:function(data){
		var retCode = data.code;
		if(retCode != "0000"){
			return;
		}
		// 不收市TA列表
		var notEndTaList = data.body.result || {};
		$("#notEndTaList").empty();
		if(notEndTaList.length <= 0){
			var trHtml = '<tr class="text-c"><td colspan="6">暂无不收市TA</td></tr>';
	    	 $("#notEndTaList").append(trHtml);
		} else{
			 $(notEndTaList).each(function(index,element){
		    	 var trList = [];
		    	 trList.push('<td>'+formatData(element.fundCode, "--")+'</td>');
		    	 trList.push('<td>'+formatData(element.fundAttr, "--")+'</td>');
		    	 trList.push('<td>'+formatData(element.fundType, "--")+'</td>');
		    	 trList.push('<td>'+formatData(element.taCode, "--")+'</td>');
		    	 trList.push('<td>'+formatData(element.taName, "--")+'</td>');
		    	 trList.push('<td><a class="btn btn-link radius delNotEndTa" dataTa="'+element.taCode+'">删除</a></td>');

		    	 var trHtml = '<tr class="text-c">'+trList.join()+'</tr>';
		    	 $("#notEndTaList").append(trHtml);
		     }); 
		}

		 // 删除
		 $(".delNotEndTa").off();
		 $(".delNotEndTa").on('click',function(){
			 var taCode = $(this).attr("dataTa");
			 HighCounterEnd.delNotEndTa(taCode);
		 });
	},
	
	/**
	 * 删除不收市TA(公募)
	 */
	delNotEndTa:function(taCode){
		if(isEmpty(taCode)){
			CommonUtil.layer_tip("请选择TA");
		}
		
		layer.confirm('确定删除不收市TA('+taCode+')吗？', {
            btn: ['确定', '取消']
        }, function (index) {
        	CommonUtil.disabledBtn("delNotEndTa");
        	// 提交
			var uri = TmsCounterConfig.HIGH_TA_NOT_END_PRO_URL;
			var taFunds = [{"taCode":taCode}];
			var reqparamters = {"taFunds": JSON.stringify(taFunds), "actionType":CONSTANTS.OPERATION_TYPE_DELETE};
			
			//console.log(reqparamters);
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
    		CommonUtil.ajaxAndCallBack(paramters, function(data){
    			
    			var respCode = data.code || '';
    			var respDesc = data.desc || '';
    			
    			if(CommonUtil.isSucc(respCode)){
    				CommonUtil.layer_tip("删除成功");
    				HighCounterEnd.queryTaCounterNotEnd();
    			}else{
    				CommonUtil.layer_alert("删除失败，"+respDesc);
    			}
    			
    		});
    		
        }, function(){  
 			layerall_close();
 			CommonUtil.enabledBtn("delNotEndTa");
 		});
	},
	
	/**
	 * 添加不收市TA(公募)
	 */
	addNotEndTa:function(){
		
		 var taCodeList = $("#counterEndTaSelBox #selCheckedTA").val();
		 if(taCodeList == null || taCodeList.length<=0){
			 CommonUtil.layer_tip("请选择TA");
			 return;
		 }
		 
		 var taFunds = [];
		 $("#counterEndTaSelBox input[type='checkbox']").each(function(index,element){
			 	var value = $(element).val();
			 	if(value != 'all' && $(element).is(":checked")){
				    var taInfo = {};
				    taInfo.taCode = $(element).val();
				    taInfo.taName = $(element).attr("taName");
				    taFunds.push(taInfo);
				}
			});

		 if(taFunds.length <= 0){
			 CommonUtil.layer_tip("请选择TA");
			 return;
		 }
		 
		
		 layer.confirm('确定添加不收市TA('+taCodeList+')吗？', {
	            btn: ['确定', '取消']
	        }, function (index) {
	        	CommonUtil.disabledBtn("addCounterNotEndTaBtn");
	        	// 提交
				var uri = TmsCounterConfig.HIGH_TA_NOT_END_PRO_URL;
				var reqparamters = {"taFunds": JSON.stringify(taFunds), "actionType":CONSTANTS.OPERATION_TYPE_ADD};
				
				var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
	    		CommonUtil.ajaxAndCallBack(paramters, function(data){
	    			CommonUtil.enabledBtn("addCounterNotEndTaBtn");
	    			var respCode = data.code || '';
	    			var respDesc = data.desc || '';
	    			
	    			if(CommonUtil.isSucc(respCode)){
	    				CommonUtil.layer_tip("添加成功");
	    				HighCounterEnd.queryTaCounterNotEnd();
	    			}else{
	    				CommonUtil.layer_alert("添加失败，"+respDesc);
	    			}
	    			
	    		});
	    		
	        }, function(){  
	 			layerall_close();
	 			CommonUtil.enabledBtn("addCounterNotEndTaBtn");
	 		});
	},
	
	/**
	 * 柜台收市(公募)
	 */
	counterEnd:function(){
		layer.confirm('确定高端柜台收市吗？', {
            btn: ['确定', '取消']
        }, function (index) {
			HighCounterEnd.validateUnFinishRepurchase();
        }, function(){
 			layerall_close();
 		});
	},

	validateUnFinishRepurchase:function(){
        var uri = TmsCounterConfig.HIGH_QUERY_UNFINISH_REPURCHASE_URL;
        var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
        CommonUtil.ajaxAndCallBack(paramters, function(data){
            var code = data.code;
            var desc = data.desc;
            if(CommonUtil.isSucc(code)){
            	var rsList = data.body.rsList || [];
            	var preCheckList = data.body.preCheckList || [];

                var memoList = [];
                if(!CommonUtil.isEmpty(rsList) && rsList.length > 0){
                    $(rsList).each(function (index, element) {
                        if(element.hasNotOrderProtocolNums != null && element.hasNotOrderProtocolNums !== 0 ){
                            memoList.push(element.fundCode+"产品应下单"+element.totalProtocolNums +"笔,"+"而未下单有"+element.hasNotOrderProtocolNums+"笔，请检查");
                        }

                        if(element.hasNotMatchVolProtocolNums != null && element.hasNotMatchVolProtocolNums !== 0 ){
                            memoList.push(element.fundCode+"产品赎回份额跟意向单份额不一致有"+ element.hasNotMatchVolProtocolNums+"笔");
                        }
                    });
                }

                if(!CommonUtil.isEmpty(preCheckList) && preCheckList.length > 0){
                    $(preCheckList).each(function (index, element) {
						memoList.push(element.desc);
                    });
                }

                if(!CommonUtil.isEmpty(memoList) && memoList.length > 0){
                    var content = memoList.join("<br>");
                    layer.confirm(content, {
                        btn: ['确定收市', '取消']
                    }, function (index) {
                        HighCounterEnd.callCounterEnd();
                    }, function(){
                        layerall_close();
                    });
				}else{
                   HighCounterEnd.callCounterEnd();
			   }
            }else{
                CommonUtil.layer_tip("收市失败:"+desc+"("+code+")");
            }
        });
	},

	callCounterEnd:function(){
        var uri = TmsCounterConfig.HIGH_FUND_COUNTER_END_URL;
        var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
        CommonUtil.ajaxAndCallBack(paramters, function(data){
            HighCounterEnd.init();
            var code = data.code;
            var desc = data.desc;
            if(CommonUtil.isSucc(code)){
                CommonUtil.layer_tip("收市成功");
            }else{
                CommonUtil.layer_tip("收市失败:"+desc+"("+code+")");
            }
        });
	},
	
	/**
	 * 查询基金TA信息
	 */
	queryFundTaInfo:function(){
		var fundCode = $("#inputfundCode").val();
		var fundName = $("#inputfundName").val();
		if(CommonUtil.isEmpty(fundCode) && CommonUtil.isEmpty(fundName) ){
			CommonUtil.layer_tip("请输入基金代码或基金名称查询");
			return ;
		}
		
		var  uri= TmsCounterConfig.QUERY_HIGH_PRODUCT_COUNTER_URL;
		var reqparamters  = {};
		reqparamters.fundCode = fundCode;
		reqparamters.fundName = fundName;
		reqparamters.page = 1;
		reqparamters.pageSize = 10;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, HighCounterEnd.queryFundTaInfoCallBack,"pageView");
	},
	
	queryFundTaInfoCallBack:function(data){
		
		HighCounterEnd.fundTaList = data.fundTaList || [];
		
		$("#fundTaList").empty();
		if(HighCounterEnd.fundTaList.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="5">暂无数据</td></tr>';
			$("#fundTaList").append(trHtml);
		}
	
		$(HighCounterEnd.fundTaList).each(function(index,element){
			var tdList = [];
			tdList.push('<td><input class="selectNotEndFundTa" name="selectNotEndFundTa" type="checkbox" value="'+index+'"></input></td>');
			tdList.push('<td>'+element.fundCode+'</td>');
			tdList.push('<td>'+element.fundAttr+'</td>');
			tdList.push('<td>'+element.taCode+'</td>');
			tdList.push('<td>'+element.taName+'</td>');
			
			var trAppendHtml = '<tr class="text-c">'+tdList.join() +'</tr>';
			$("#fundTaList").append(trAppendHtml);
		});
	},
	/**
	 * 添加不收市TA(公募)
	 */
	addSelFundTa:function(){
		var selectedCheckboxs = $("#SelectFundTaPage input[name='selectNotEndFundTa'][type='checkbox']:checked");
		if(CommonUtil.isEmpty(selectedCheckboxs) || selectedCheckboxs.length == 0){
			CommonUtil.layer_tip("请选择不收市基金TA");
			return ;
		}

		var taFunds = [];	 
		var taCodeList = "";
		var existTaCodeList = "";
		$("#SelectFundTaPage input[name='selectNotEndFundTa'][type='checkbox']").each(function(index,element){
			if($(element).is(":checked")){
				var selectedIndex = $(element).val();
				var selRowFundTa = HighCounterEnd.fundTaList[selectedIndex];
				 // 校验TA是否已添加
				 var checkFlag = false;
				 $(HighCounterEnd.notEndTaList).each(function(index,element){
					 if(element.taCode == selRowFundTa.taCode){
						 checkFlag = true;
						 existTaCodeList +=(selRowFundTa.taCode+",");
						 return;
					 }
				 });
				 
				 if(!checkFlag){
					 taCodeList +=(selRowFundTa.taCode+",");
					 taFunds.push(selRowFundTa);
				 }
			}
		});
		taCodeList = taCodeList.substring(0, taCodeList.length-1);
		existTaCodeList = existTaCodeList.substring(0, existTaCodeList.length-1);
		if(!CommonUtil.isEmpty(existTaCodeList)){
			CommonUtil.layer_alert("选择的TA("+existTaCodeList+")已经存在不收市TA列表中，不能重复添加，谢谢！");
			return ;
		}
		if(CommonUtil.isEmpty(taCodeList)){
			CommonUtil.layer_tip("请选择不收市基金TA");
			return ;
		}
			
		 // 询问框
		 layer.confirm('确定添加不收市TA('+taCodeList+')吗？', {
	            btn: ['确定', '取消']
	        }, function (index) {
	        	CommonUtil.disabledBtn("addSelFundTaBtn");
	        	// 提交
	        	var uri = TmsCounterConfig.HIGH_COUNTER_SAVE_OR_DEL_END_TA_URL;
	        	var reqparamters = {"taFunds": JSON.stringify(taFunds), "actionType":CONSTANTS.OPERATION_TYPE_ADD};
				
				var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
	    		CommonUtil.ajaxAndCallBack(paramters, function(data){
	    			CommonUtil.enabledBtn("addSelFundTaBtn");
	    			
	    			var respCode = data.code || '';
	    			var respDesc = data.desc || '';
	    			
	    			if(CommonUtil.isSucc(respCode)){
	    				layerall_close();
	    				HighCounterEnd.queryTaCounterNotEnd();
	    				CommonUtil.layer_tip("添加成功");
	    				
	    			}else{
	    				CommonUtil.enabledBtn("addSelFundTaBtn");
	    				CommonUtil.layer_alert("添加失败，"+respDesc);
	    			}
	    			
	    		});
	        }, function(){  
	 			layerall_close();
	 			CommonUtil.enabledBtn("addSelFundTaBtn");
	 		});
	},
	
	popupSelFundTaPage:function(){
		// POPUP
	    layer.open({
	        title: ['筛选基金添加不收市TA', false],
	        type: 1,
	        area: ['850px', '500px'],
	        skin: 'layui-layer-rim', //加上边框
	        btnAlign: 'l',
	        content: $('#SelectFundTaPage')
	    });	
	},
	
	/**
     * @param uri
     * 查询批处理节点状态
     */
    queryBatchFlowStat: function () {
        var reqData = {};
        var paramters = {'uri': TmsCounterConfig.QUERY_BATCH_STEP_STAT_URL};
        paramters.needLoad = true;
        paramters.isAsync = false;
        paramters.reqparamters = reqData || {};

        ajaxAndCallBack(paramters, function(data){
            var respData = data.body || {};
            var beanStat = respData.beanStat || [];
            HighCounterEnd.stepBtnStatus($("#counterEndBtn"), '90', '109', beanStat);
        });
    },
    
    stepBtnStatus: function (stepBtn, sysCode, taskId, beanStat) {
        // CONSTANTS.BATCH_StATUS = {"0":"未执行","1":"执行中","2":"执行成功","3":"执行失败"};

        if('2' == beanStat){
            CommonUtil.succBtn(stepBtn);
        }
    }
};
