package com.howbuy.tms.counter.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
@Getter
@Setter
public class TmsCounterNacosConfig {
    /**
     * 资产中心URL
     */
    @Value("${tms-counter.center.platform.url}")
    private String centerPlatformurl;
    
    /**
     * 机构URL
     */
    @Value("${tms-counter.otccounter.platform.url}")
    private String otcCounterPlatformurl;

    /**
     * 控制台地址
     */
    @Value("${tms-counter.console.url}")
    private String consoleUrl;

    /**
     * 线上换卡访问地址前缀
     */
    @Value("${tms-counter.online.change.card.url}")
    private String onlineChangeCardUrl;

    /**
     * 线上换卡文件路径
     */
    @Value("${tms-counter.online.change.card.file.path}")
    private String onlineChangeCardFilePath;

    @Value("${tms-counter.crm.trade.url.prefix}")
    private String crmTradeUrlPrefix;

    /**
     * 查询开始时间
     */
    @Value("${tms-counter.queue.query_assert_start_dt}")
    private String QUERY_BEGIN_DT;

    /**
     * 上传双录文件路径
     */
    @Value("${tms-counter.doubleRecord.targetDir}")
    private String targetDir;

    @Value("${tms-counter.fundRegionEnum}")
    private String fundRegionEnum;
}
