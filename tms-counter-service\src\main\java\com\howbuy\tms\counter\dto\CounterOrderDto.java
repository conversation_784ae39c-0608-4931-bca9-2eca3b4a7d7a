/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(柜台审核订单)
 * <AUTHOR>
 * @date 2017年3月30日 下午6:28:47
 * @since JDK 1.6
 */
@Data
public class CounterOrderDto extends OperInfoBaseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 3359189097876837314L;

    /**
     * 审核申请流水号
     */
    private String dealAppNo;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 客户姓名(脱敏)
     */
    private String custNameEncrypt;

    /**
     * 子交易账号
     */
    private String subTxAcctNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金份额类型
     */
    private String fundShareClass;

    /**
     * 目标基金代码
     */
    private String tFundCode;
    /**
     * 转入基金简称
     */
    private String tFundName;

    /**
     * 目标基金份额类型
     */
    private String tFundShareClass;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 交易码
     */
    private String txCode;

    /**
     * 支付方式 01-自划款 04-代扣款 06-储蓄罐
     */
    private String paymentType;

    /**
     * 手续费折扣
     */
    private BigDecimal discountRate;

    /**
     * 协议类型 1-普通公募基金投资 ;2-组合公募基金投资; 3-暴力定投; 4-高端公募协议;
     */
    private String protocolType;

    /**
     * 协议号
     */
    private String protocolNo;

    /**
     * 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;

    /**
     * 基金分红方式 0-红利再投； 1-现金红利
     */
    private String fundDivMode;

    /**
     * 客户订单号
     */
    private String dealNo;

    /**
     * 经办人证件号
     */
    private String transactorIdNo;

    /**
     * 经办人证件类型
     */
    private String transactorIdType;

    /**
     * 经办人姓名
     */
    private String transactorName;

    /**
     * 投资顾问代码
     */
    private String consCode;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回描述
     */
    private String description;

    /**
     * 备注
     */
    private String memo;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核日期时间
     */
    private Date checkDtm;

    /**
     * 申请标识
     */
    private String appFlag;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 巨额赎回顺延
     */
    private String largeRedmFlag;

    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是)
     */
    private String agentFlag;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 银行卡号
     */
    private String bankAcct;

    /**
     * 银行卡号
     */
    private String bankCode;

    /**
     * ta日期
     */
    private String taTradeDt;

    /**
     * 当前净值
     */
    private BigDecimal nav;

    private String unusualTransType;

    /**
     * 风险确认标识：0-未确认；1-已确认
     */
    private String riskFlag;

    /**
     * 预估手续费
     */
    private BigDecimal esitmateFee;

    /**
     * 预约折扣(线下预约必填)
     */
    private BigDecimal appointmentDiscount;
    /**
     * 预约金额
     */
    private BigDecimal appointmentAmt;
    /**
     * 预约订单号
     */
    private String appointmentDealNo;
    /**
     * 申请金额含费
     */
    private BigDecimal applyAmountIncluFee;

    /**
     * 原始费率
     */
    private BigDecimal feeRate;

    /**
     * 订单信息
     */
    private String orderFormMemo;

    /**
     * 用户撤销标识 0-强制 1-自行
     */
    private String forceCancelFlag;

    /**
     * 回款去向
     */
    private String redeemCapitalFlag;

    /**
     * 产品通道 3-群济私募 5-好买公募 6-高端公募
     */
    private String productChannel;

    /**
     * 撤单原因
     */
    private String cancelMemo;

    /**
     * 是否全赎1:是；0：否；
     */
    private String allRedeemFlag;

    /**
     * 份额业务类型 (份额合并或迁移)
     */
    private String shareType;
    /**
     * 是否到期赎回
     */
    private String isRedeemExpire;
    /**
     * 预计到期日期
     */
    private String preExpireDate;

    /**
     * 对方网点
     */
    private String tOutletCode;

    /**
     * 转托管业务类型1-跨市场；2-场外跨销售机构
     */
    private String transferTubeBusiType;

    /**
     * 对方销售人处投资者基金交易账号
     */
    private String tSellerTxAcctNo;

    /**
     * 对方销售人代码
     */
    private String tSellerCode;

    /**
     * 原申请单号
     */
    private String originalAppDealNo;
    /**
     * 可赎回日期
     */
    private String allowDt;
    /**
     * 复购协议号
     */
    private String repurchaseProtocolNo;

    /**
     * 复购类型
     */
    private  String repurchaseType;

    /**
     * 是否注销原卡：1-是，0-否
     */
    private String cancelCard;

    /**
     * 在途资产JSON
     */
    private String intransitAssetMemo;

    /**
     * 转出卡号
     */
    private String outBankAcct;

    /**
     * 转出银行编号
     */
    private String outBankCode;

    /**
     * 转出资金账号
     */
    private String outCpAcctNo;

    /**
     * 资产
     */
    private BigDecimal asset;



    /**
     * 双录文件路径
     */
    private String doubleRecordFilePath;

    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;

    /**
     * 总认缴金额
     */
    private BigDecimal totalSubsAmt;

    /**
     * 转入客户信息
     */
    private String inTxAcctNo;
    private String inCpAcctNo;
    private String inCustName;
    private String inIdNo;
    private String inIdType;
    private String inInvstType;
    private String inBankAcct;
    private String inDisCode;

    /**
     * 撤单实时回储蓄罐 04=银行卡 06=储蓄罐 5-回可用 6-可用+银行卡 7-可用+储蓄罐
     */
    private String withdrawDirection;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 修改前赎回方向
     */
    private String beforeModifyDirection;

    /**
     * 垫资状态 1-垫资 0-不垫资
     */
    private String advanceStatus;
    /**
     * 问卷答案
     */
    private String surveyAnswer;
    private String questionAnswer;

    /**
     * 赎回比例
     */
    private BigDecimal appRatio;

    /**
     * 赎回状态  1 可  0否
     */
    private String redeemStatus;

    private boolean isAdviser;

    /**
     * 中台详细业务码
     */
    private String zBusiCode;


    private String taCode;

    private String partnerCode;

    /**
     * 换卡是否存在白名单中：1-是；其他是否
     */
    private String existWhiteList;

    /**
     * 换卡视频承诺地址
     */
    private String videoPromiseUrl;

    /**
     * 赎回方式
     */
    private String customRatioType;

    /**
     * 赎回开关
     */
    private String openFlag;

}
