/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoResponse;
import com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:(查询批处理流程状态)
 * @reason:
 * <AUTHOR>
 * @date 2018年9月25日 下午3:27:55
 * @since JDK 1.6
 */
@Controller
public class QueryBatchStatController {
    
    //private static Logger logger = LogManager.getLogger(BuyController.class);
    
    @Autowired
    private TmsCounterService tmsCounterService;
    

    @RequestMapping("/tmscounter/querybatchstat.htm")
    public ModelAndView queryHighWorkDay(HttpServletRequest request, HttpServletResponse response) throws Exception {

        QueryBatchFlowStatResponse queryBatchFlowStatResponse = tmsCounterService.getBatchFlowStatList();

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("batchStatList", ((QueryBatchFlowStatResponse) queryBatchFlowStatResponse).getList());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    @RequestMapping("/tmscounter/querybatchstepstat.htm")
    public ModelAndView queryBatchStepStat(HttpServletRequest request, HttpServletResponse response) throws Exception {
        QueryHighBatchFlowInfoResponse.BatchFlowBean bean = tmsCounterService.getBatchStat(SysCodeEnum.BATCH_HIGH.getCode(), BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode());
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("beanStat", bean.getBatchStat());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

}

