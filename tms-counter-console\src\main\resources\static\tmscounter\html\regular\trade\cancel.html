<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>交易撤单</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 定期业务 <span class="c-gray en">&gt;</span> 收益凭证类<span class="c-gray en">&gt;</span> 交易撤单 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">交易撤单</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span ml30">证件号：</span>
                    <input type="text"  placeholder="请输入" id="idNo">
                    <span class="normal_span ml30">分销机构：</span>
                    <span class="select-box inline">
                       <select name="disCode" class="select" id="selectDisCode">
                       </select>
                    </span>
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryCustInfoBtn">查询</a>
                </div>
            </div>
        </div>
    </div>
    <div class="page-container">
        <p class="main_title">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>                   
                        <th>风险等级</th>
                        <th>分销机构</th> 
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <p class="main_title mt30">可撤销的交易申请</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>选择</th>
                        <th>产品代码</th>
                        <th>产品简称</th>
                        <th>业务类型</th>
                        <th>申请金额(元)</th>
                        <th>申请份额</th>
                        <th>交易状态</th>
                        <th>订单号</th>
                        <th>申请日期</th>
                        <th>申请时间</th>
                        <th>操作员</th>
                    </tr>
               </thead>
                <tbody id="cancelList">
                </tbody>
            </table>
        </div>
       
       <p class="main_title mt30">其他信息</p>
       <form id="transactorInfoForm" >
       <div class="result2_tab">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
                 <tr class="text-c">
         			<td>网点：</td>
         			<td>中台柜台<input type="hidden" name="outletCode" value="W20170215"/></td>
         			<td>投资顾问代码：</td>
         			<td>
         			 	<span class="select-box inline">
                			<select name="consCode" class="select selectconsCode" ></select>
            			</span>
         			</td>
         		</tr>
         		<tr class="text-c">
         			<td>申请日期：</td>
                    <td>
                        <input class="input-text laydate-icon"   id="appDt" name="appDt" isnull="false" datatype="s" errormsg="申请日期" maxlength = "8" >
                    </td>
                    <td>申请时间：</td>
                    <td>
                        <input class="input-text laydate-icon"  type="text" id="appTm" name="appTm" isnull="false" datatype="s" errormsg="申请时间" maxlength="6">
                    </td>
                 </tr>
         		 <tr class="text-c">
         			<td>撤单类型：</td>
              		<td>
                  		<span class="select-box inline">
                    		<select name="cancelType" class="select"  isnull="false" datatype="s" errormsg="撤单类型" >
                    			<option  value="1">自行撤单</option>
                    			<option  value="2">强制撤单</option>
                    		</select>
                		</span>
                	</td>
         			<td>是否经办：</td>
         			<td>
         				 <span class="select-box inline">
		           			<select name="agentFlag" class="select selectAgened">
		              			<option value="0">否</option>
		             		 	<option value="1">是</option>
		           			</select>
		           		</span>
         			</td>
         		</tr>
         	</tbody>
         </table>
        </div>
        
       <!-- 是否经办: 个人用户默认为否, 机构客户默认为是, 为是时显示经办人信息 -->
       <div class="result2_tab" id="agentInfoDiv" style="display: none;">
        <p class="main_title mt20">经办人信息</p>
         <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
            	<tr class="text-c">
                   	<td>经办人姓名：</td>
                   	<td>
                   		<input type="text" placeholder="请输入"  name="transactorName"  datatype="s" errormsg="经办人姓名">
                   	</td>
                   	<td>经办人证件类型：</td>
              		<td>
                  		<span class="select-box inline">
                    		<select name="transactorIdType" class="select selectTransactorIdType"   datatype="s" errormsg="经办人证件类型" >
                    		</select>
                		</span>
                	</td>
                </tr>
             	<tr class="text-c">
              		<td>经办人证件号：</td>
              		<td> <input type="text" placeholder="请输入"  name="transactorIdNo"  datatype="s" errormsg="经办人证件号" ></td>
               		<td>&nbsp;</td>
               		<td>&nbsp;</td>
               	</tr>
            </tbody>
          </table>
        </div>
        </form>
        
        <p class="mt30">
            <a href="javascript:void(0)" class="btn radius btn-secondary" id="confimCancelBtn">确认提交</a>
        </p>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
   	<script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/regular/trade/cancel.js?v=201806251632"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfosubpage.js?v=201806251632"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/regular/query/querycustinfo.js?v=201806251619"></script>
    <script type="text/javascript" src="../../../js/regular/query/queryproductinfo.js?v=201806251632"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
 	<script type="text/javascript" src="../../../js/regular/query/querycancancel.js?v=201806251632"></script>
    <script type="text/javascript" src="../../../js/fund/common/validate.js?v=20200301002"></script>
	<script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
</body>

</html>