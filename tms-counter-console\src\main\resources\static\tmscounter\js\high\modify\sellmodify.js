/**
*赎回修改
*<AUTHOR>
*@date 2018-03-27 11:10：01
**/
$(function(){
	SellModify.init();
});

var SellModify = {
	
	/**
	 * 初始化
	 */
	init:function(){
		// 初始化数据
		SellModify.initData();
		
		// 查询订单信息
        Modify.queryCounterDealOrder(this.queryCounterDealOrderCallBack, null);
		
		// 初始化按钮事件
		SellModify.initBtn();
	},
	/**
	 * 初始化绑定时间
	 */
	initBtn:function(){
		
		var lastInputValue = '';
		$("#appVol").on('keyup focus', function(){
			 var str = $(this).val() || '';
				if (!CommonUtil.validFloat(str)) {
					$(this).val(lastInputValue);
					return false;
				}
				lastInputValue = str;
		        str = str.replace(/\,/g,'');
		        var re = /([0-9]+\.[0-9]{2})[0-9]*/;
				 str = str.replace(re,"$1");
		        var convertStr =  CommonUtil.digit_vol_uppercase(str);
		        $('#convertVol').html(convertStr);
		});
	},
		
	/**
	 * 初始化参数
	 */
	  initData:function(){
		  SellModify.appointment = {};// 投顾预约信息
		  SellModify.highproductAppointinfo = {};//预约开放日历信息
		  SellModify.checkOrder = {};// 订单信息
		  SellModify.custInfo = {};// 客户信息
		  SellModify.modifyDeal = {};// 修改订单
	},
	
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		var checkOrder = bodyData.counterOrderDto || {};//订单信息
		var counterOrder = bodyData.counterOrder || {};
		var highProduct = counterOrder.counterHighProductBaseBean || {};//产品信息
		var dtlBeanList = counterOrder.dtlBeanList || [];//赎回明细
		var appointList = bodyData.appointList || [];//投顾预约信息
		var custInfofiList = bodyData.custInfofiList || [];//客户信息

		SellModify.counterOrderDto = checkOrder;//订单信息
		SellModify.fundInfo = highProduct;
		// SellModify.dtlBeanList = dtlBeanList;//审核订单明细信息
		SellModify.checkOrder = checkOrder;//柜台订单信息
		if(custInfofiList.length > 0){
			SellModify.custInfo = custInfofiList[0] || {};//客户信息
		}

		SellModify.builDealInfo(checkOrder);//订单信息
		SellModify.buildRedeemVolDtl(dtlBeanList, checkOrder);//构建赎回明细

		ViewCounterDeal.buildAppointmentInfo(appointList);//预约信息
		ViewCounterDeal.buildFundInfo(highProduct);//产品信息
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		ViewCounterDeal.buildCheckInfo(checkOrder);// 审核信息
		SellModify.queryHighproductAppointinfo(checkOrder.fundCode, checkOrder.appDt, checkOrder.appTm, '1');// 查询预约开放日历信息

		SellModify.initInputStatus(checkOrder);// 初始化可输入框

        var viewType = CommonUtil.getParam("viewType");
        if('0' != viewType){
            OnLineOrderFile.query(null, Modify.getSelectCustMaterial(bodyData, OnLineOrderFile.CRM_SELL), Modify.getCheckNode());// CRM线上资料
        }else{
            var orderFile = bodyData.orderFile || {};
            OnLineOrderFile.buildOrderFileHtml(orderFile);
        }
	},
	/**
	 * 赎回订单信息
	 */
	builDealInfo:function(checkOrder){
		$("#fundCodeId").html(checkOrder.fundCode);// 基金代码
		$("#appDt").val(checkOrder.appDt);// 申请日期
		$("#appTm").val(checkOrder.appTm);// 申请时间
		// 总份额
		$("#volSum").html(CommonUtil.formatAmount(checkOrder.appVol));
		$("#convertVolSum").html(CommonUtil.digit_vol_uppercase(checkOrder.appVol));

    	//初始化下拉框
    	SellModify.initSelect();
    	// 原订单信息
    	Modify.modifyDealOrder = checkOrder;

		$(".selectUnusualTransType").val(checkOrder.unusualTransType);//异常赎回标识
		// $('.redeemCapitalFlag').val(checkOrder.redeemCapitalFlag);// 赎回回款方向
		$('.selectLargeRedmFlag').val(checkOrder.largeRedmFlag);// 巨额赎回顺延标识
	},
	/**
	 * 构建赎回明细
	 * @param dtlBeanList
	 */
	buildRedeemVolDtl:function(dtlBeanList, checkOrder){
		$(dtlBeanList).each(function (index, data) {
			let html = SellModify.generateSellFormTable("sellFormTable"+index, data);
			$("#sellConfirmFormFoot").before(html);

			//回款去向
			var selectRedeemDirHtml = CommonUtil.selectOptionsHtml(CONSTANTS.REDEEM_DIR_MAP, null, null,null,true);
			$(".redeemCapitalFlag").html(selectRedeemDirHtml);
			// 赋值
			$('.redeemCapitalFlag').val(checkOrder.redeemCapitalFlag);// 赎回回款方向
			// 禁用下拉框
			$('.redeemCapitalFlag').attr('disabled', true);
		});
	},
	/** 生成赎回编辑表单的table */
	generateSellFormTable:function(sellFormTableId, data) {
		var table = '<tbody id="'+sellFormTableId+'">' +
			'              <tr class="text-c">' +
			'                <td>银行卡账号</td>' +
			'                <td class="readText bankAcctInfoId">'+data.bankAcctNo+'</td>' +
			'                <td>当前可用份额</td>' +
			'                <td class="readText availVol">'+CommonUtil.formatAmount(data.availVol)+'</td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'                <td>当前总份额</td>' +
			'                <td class="readText balanceVol">'+CommonUtil.formatAmount(data.balanceVol)+'</td>' +
			'                <td>申请份额（份）</td>' +
			'                <td class="readText"><input type="text" name="appVol" disabled="disabled" value="'+CommonUtil.formatAmount(data.appVol)+'" cpacctno="'+data.cpAcctNo+'" onchange="SellModify.appVolChange(\''+sellFormTableId+'\')"></td>' +
			'                </td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'                <td>冻结份额</td>' +
			'                <td class="readText unconfirmedVol">'+CommonUtil.formatAmount(data.unconfirmedVol)+'</td>' +
			'                <td>费用</td>' +
			'                <td class="fee"></td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'              		<td>交易回款方式</td>' +
			'              		<td>'+
			'              		<span class="select-box inline">'+
			'                   	<select id="redeemCapitalFlag" name="redeemCapitalFlag" class="select redeemCapitalFlag" isnull="false" datatype="s" errormsg="交易回款方式" onchange="SellModify.refundDirectionOnChange($(this).val(), \''+sellFormTableId+'\')">' +
			'                       </select>' +
			'              		</span>'+
			'              		</td>'+
			'              </tr>';
		if (data.redeemCapitalFlag == '5' || data.redeemCapitalFlag == '6' || data.redeemCapitalFlag == '7') {
			table += '              <tr class="text-c" id="refundFinaAvailRead">' +
			'              	 <td>回可用余额备注</td>' +
			'                <td class="readText refundFinaAvailMemoRead">' + data.refundFinaAvailMemo + '</td>' +
			'              	 <td>回可用余额金额</td>' +
			'                <td class="readText refundFinaAvailAmtRead">' + CommonUtil.formatAmount(data.refundFinaAvailAmt) + '</td>' +
			'              </tr>' ;
		}
		table += '        </tbody>';
		return table;
	},

	/**
	 * 回可用备注选择
	 * @param redeemDirection
	 */
	refundDirectionOnChange: function(dir, sellTableId) {
		$('#refundFinaAvailRead').remove();
		$(".refundTr").html("");
		if (dir == '5' || dir == '6' || dir == '7') {
			$("#"+sellTableId).append(SellModify.generateRefundFormTable(dir));
			// 回可用备注
			$("#selectRedeemMemo").html(CommonUtil.selectOptionsHtml(CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP));
			$("#selectRedeemMemo").on('change', function () {
				SellModify.refundMemoSelectOnChange();
			});
		} else {
			$(".refundTr").html("");
		}
	},

	/**
	 * 回可用备注
	 * @param redeemDirection
	 */
	refundMemoSelectOnChange: function() {
		var memoSelect = $("#selectRedeemMemo").val();
		if ("1" == memoSelect) {
			$("#refundMemo").html('<input class="" name="refundFinaAvailMemo" isnull="false" datatype="s">');
		}
		if ("0" == memoSelect) {
			var selectRefundMemoId = "selectRefundMemo";
			$("#refundMemo").html('<select id="'+selectRefundMemoId+'" name="refundFinaAvailMemo" class="select" isnull="false" datatype="s">');
			// 初始化基金代码多选
			CommonUtil.singleSelectForRefund(selectRefundMemoId,TmsCounterConfig.HIGH_QUERY_PRODUCT_CODEANDNAME_URL, SellModify.fundInfo.productChannel);
		}
	},

	generateRefundFormTable:function(dir) {
		var table = '<tr class="text-c refundTr">' +
			'                <td>回可用余额备注选择</td>' +
			'                <td>' +
			'					<span class="select-box inline">' +
			'						<select id="selectRedeemMemo" class="select" isnull="false" datatype="s"></select>' +
			'					</span>'+
			'				 </td>' +
			'                <td>回可用余额备注</td>' +
			'                <td><span class="select-box inline" id="refundMemo"></span></td>' +
			'              </tr>';
		if (dir == '6' || dir == '7') {
			table += '   <tr class="text-c refundTr">' +
				'                <td>回可用余额金额</td>' +
				'                <td>' +
				'					<input type="text" placeholder="请输入" id="refundFinaAvailAmt" class="refundFinaAvailAmt" name="refundFinaAvailAmt" isnull="false" datatype="s" errormsg="回可用金额">'+
				'				 </td>' +
				'                <td></td>' +
				'                <td></td>' +
				'              </tr>';
		}
		return table;
	},

	/**
	 * 初始化下拉框
	 */
	initSelect:function(){
		//初始化异常赎回
		var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP,'0', null, null, true);
		$(".selectUnusualTransType").html(selectTransTypeHtml);
		
		// //回款去向
		// var selectRedeemDirHtml = CommonUtil.selectOptionsHtml(CONSTANTS.REDEEM_DIR_MAP, null, null,null,true);
		// $(".redeemCapitalFlag").html(selectRedeemDirHtml);
		
		// 巨额赎回顺延标识 默认顺延
		var selectLargeRedmFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.LARGE_REDM_FLAG_MAP, '1', null, null, true);
		$(".selectLargeRedmFlag").html(selectLargeRedmFlagHtml);
	},
	/**
	 * 查询产品预约开放日历信息
	 */
	queryHighproductAppointinfo:function(fundCode, appDt, appTm, busyType){
		
		QueryHighProduct.queryHighproductAppointinfo(fundCode, appDt, appTm, busyType);
		SellModify.highproductAppointinfo = QueryHighProduct.highproductAppointinfo || {};
		//构建预约开放日历信息
		ViewCounterDeal.buildProductAppointmentInfo(SellModify.highproductAppointinfo);
	},

	/**
	 * 初始化输入框状态
	 * @param checkOrder
	 */
	initInputStatus:function(checkOrder){
		var viewType = CommonUtil.getParam("viewType");
		if('2' == viewType){
			// 可修改输入框
			var enableList = [];
			$("[name='appVol']").each(function () {
				enableList.push($(this));// 申请份额
			});
	    	enableList.push($("#appTm"));// 申请时间
	    	enableList.push($(".selectUnusualTransType"));// 异常赎回标识
	    	enableList.push($(".redeemCapitalFlag"));// 回款去向
	    	enableList.push($(".selectLargeRedmFlag"));// 巨额赎回顺延标识 默认顺延
	    	CommonUtil.enabledList(enableList);
		}
		
	},
	appVolChange:function(sellTableId) {
		let appVolInput = $("#"+sellTableId).find("input[name='appVol']");
		let unFormatVol = CommonUtil.unFormatAmount(appVolInput.val());
		// 检查是否数字
		if (!CommonUtil.validFloat(unFormatVol)) {
			layer.alert("只能输入数字");
			return;
		}

		// 格式化份额
		appVolInput.val(CommonUtil.formatAmount(unFormatVol));

		// 计算总份额
		let totalAppVol = 0;
		$("tbody[id^='sellFormTable'] input[name='appVol']").each(function () {
			let appVol = $(this).val();
			if (isEmpty(appVol)) {
				return;
			}
			// 求和
			totalAppVol += parseFloat(CommonUtil.unFormatAmount(appVol));
		});
		$("#volSum").html(CommonUtil.formatAmount(totalAppVol));

		// 更新大写总份额
		$("#convertVolSum").html(CommonUtil.digit_vol_uppercase(totalAppVol));
	}
	
};