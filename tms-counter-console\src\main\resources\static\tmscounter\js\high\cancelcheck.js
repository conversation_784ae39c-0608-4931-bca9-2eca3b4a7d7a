/**
*撤单审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
	
	if("1" == viewType){
		//绑定审核事件
		//驳回
		$("#checkRejectBtn").on('click', function(){
			CounterCheck.confirm("2", CancelCheck.checkOrder);  //checkStatus 1-审核通过 2-审核驳回
		});
		
		//审核通过
		$("#checkConfirmBtn").on('click', function(){
			CounterCheck.confirm("1", CancelCheck.checkOrder);  //checkStatus 1-审核通过 2-审核驳回
		});
	}else {
		//隐藏按钮
		$("#checkRejectBtn").hide();
		$("#checkConfirmBtn").hide();
	}
	
	var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
	$(".selectconsCode").html(selectConsCodesHtml);
	BuyCheck.queryCounterDealOrder();
	
});

var CancelCheck = {
	
	/**
	 * 查询柜台订单
	 * @param dealAppNo
	 */
	queryCounterDealOrder:function(){
		var dealAppNo = CommonUtil.getParam("dealAppNo");
		var uri = TmsCounterConfig.HIGH_VIEW_CHECKORDER;
		
		var reqparamters = {};
		reqparamters.dealAppNo = dealAppNo;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CancelCheck.queryCounterDealOrderCallBack, null);
	},
	
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		var checkOrder = bodyData.checkOrder || {};
		var custInfofiList = bodyData.custInfofiList || [];
		CancelCheck.checkOrder = checkOrder;
		
		CancelCheck.buildDealInfo(checkOrder);//订单信息
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		
	},
	
	/**
     * 订单信息
     * @param checkOrder
     */
    buildDealInfo:function(checkOrder){
    	

    	$(Cancel.canCancelOrders).each(function(index,element){
    		var trList = [];
    		var selectUserCancelFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.USER_CANCEL_FLAG_MAP, element.userCancelFlag);
    		var selectCancelHtml = '<span class="select-box inline">'+
			'<select name="userCancelFlag" class="selectUserCancelFlag" >'+
			selectUserCancelFlagHtml+
			'</select>'+
			'</span>';
    		trList.push(selectCancelHtml);
    		trList.push(CommonUtil.formatData(element.dealNo));
    		trList.push(CommonUtil.formatData(element.productCode));
    		trList.push(CommonUtil.formatData(element.productName));
    		trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME_MAP,element.mBusiCode,'--'));
    		trList.push(CommonUtil.formatData(element.appAmt,''));
    		trList.push(CommonUtil.formatData(element.appVol,''));
    		trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP,element.orderStatus));
    		trList.push('');
    		trList.push(CommonUtil.getMapValue(CONSTANTS.PAY_STATUS_MAP,element.payStatus));
    		trList.push(CommonUtil.formatData(element.appDtm));
    		var trHtml = '<tr class="text-c"><td><input type="radio" name="orderIndex" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td></tr>';
    		$("#rsList").append(trHtml);
    	});

    },
    
};
