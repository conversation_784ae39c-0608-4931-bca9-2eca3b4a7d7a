/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoResponse;
import com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckResponse;
import com.howbuy.tms.common.enums.database.AllRedeemFlagEnum;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterRedeemReqDto;
import com.howbuy.tms.counter.dto.CounterRedeemRespDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(零售柜台赎回控制器)
 * <AUTHOR>
 * @date 2017年9月15日 下午5:14:25
 * @since JDK 1.6
 */
@Controller
public class SellFundController extends AbstractController {
    private Logger logger = LogManager.getLogger(SellFundController.class);

    @Autowired
    private TradeValidateService tradeValidateService;
    
    /**
     * 
     * validateAllRedeem:(校验全赎申请单)
     *              <br>当有全赎申请单时, 校验批处理"确认处理日终"是否已经执行完成， 如果已完成就不可以下全赎单
     * @param redeemReqList
     * @param disInfoDto
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月25日 下午2:17:49
     */
    private void validateAllRedeem(List<CounterRedeemReqDto> redeemReqList, DisInfoDto disInfoDto) throws Exception{
        boolean allRedeemTag = false;
        for(CounterRedeemReqDto reqDto : redeemReqList){
            if(AllRedeemFlagEnum.IS_ALL_REDEEM.getCode().equals(reqDto.getAllRedeemFlag())){
                allRedeemTag = true;
                break;
            }
        }
        if(allRedeemTag){
            QueryBatchFlowInfoResponse batchFlowInfo = tmsCounterService.getBatchFlowInfo(BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode(), disInfoDto);
            String batchStat = null;
            if(batchFlowInfo != null && CollectionUtils.isNotEmpty(batchFlowInfo.getFlowList())){
                batchStat = batchFlowInfo.getFlowList().get(0).getBatchStat();
            }
            if(BatchStatEnum.PROCESSING.getKey().equals(batchStat)
                    || BatchStatEnum.PROCESS_SUCCESS.getKey().equals(batchStat) ){
                throw new TmsCounterException(TmsCounterResultEnum.ACK_DAY_END_IS_COMP);
            }
        }
    }
    
    /**
     * 
     * validateCurTaDtAckVol:(校验当前TA交易日有确认份额是否继续赎回)
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2019年1月15日 下午3:43:10
     */
    @RequestMapping("/tmscounter/fund/validateCurTaDtAckVol.htm")
    public void validateCurTaDtAckVol(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String sellRedeemFunds = request.getParameter("sellRedeemFunds");
        String custInfoForm = request.getParameter("custInfoForm");
        
        // 选择需要赎回的订单
        List<CounterRedeemReqDto> redeemReqList = JSON.parseArray(sellRedeemFunds, CounterRedeemReqDto.class);
        if(CollectionUtils.isEmpty(redeemReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.REDEEM_ORDER_NOT_SELECTED);
        }
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        
        List<TmsCounterResult> rstList = new ArrayList<TmsCounterResult>();
        
        for(CounterRedeemReqDto reqDto : redeemReqList){
            // do service
            QueryCurTaDtAckResponse responseDto = tmsCounterService.queryCustCurTaDtAckVol(custInfoDto.getCustNo(), reqDto.getCpAcctNo(), 
                    reqDto.getFundCode(), reqDto.getProtocolNo(), custInfoDto.getDisCode());
            
            if(responseDto != null && responseDto.getTotalAckVol().compareTo(BigDecimal.ZERO) > 0){
                CounterRedeemReqDto reqVdlDto = new CounterRedeemReqDto();
                reqVdlDto.setTxAcctNo(custInfoDto.getCustNo());
                reqVdlDto.setCpAcctNo(reqDto.getCpAcctNo());
                reqVdlDto.setFundCode(reqDto.getFundCode());
                reqVdlDto.setProtocolNo(reqDto.getProtocolNo());
                reqVdlDto.setAppVol(reqDto.getAppVol());
                
                TmsCounterResult rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getTotalAckVol().toString());
                Map<String, Object> body = new HashMap<String, Object>(16);
                body.put("reqVdlDto", reqVdlDto);
                rst.setBody(body);
                
                // set return
                rstList.add(rst);
            }
        }
        WebUtil.write(response, rstList);
    }
    
    /**
     * 
     * redeemConfirm:(赎回确认)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午5:15:04
     */
    @RequestMapping("/tmscounter/fund/sellconfirm.htm")
    public ModelAndView redeemConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String sellRedeemFunds = request.getParameter("sellRedeemFunds");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        logger.debug("SellFundController|redeemConfirm|dealAppNo:{},sellRedeemFunds:{},custInfoForm:{},transactorInfoForm:{}",
                dealAppNo, sellRedeemFunds, custInfoForm, transactorInfoForm);

        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        
        // 选择需要赎回的订单
        List<CounterRedeemReqDto> redeemReqList = JSON.parseArray(sellRedeemFunds, CounterRedeemReqDto.class);
        if(CollectionUtils.isEmpty(redeemReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.REDEEM_ORDER_NOT_SELECTED);
        }
        // 校验全赎
        validateAllRedeem(redeemReqList, disInfoDto);

        // 柜台不支持Y份额基金交易
        tradeValidateService.tradeValidate(redeemReqList.get(0).getFundCode(),null);
       
        List<TmsCounterResult> rstList = new ArrayList<TmsCounterResult>(16);

        // 循环落申请赎回单
        for(CounterRedeemReqDto reqDto : redeemReqList){
            
            // 申请份额为空或0时，不去做赎回申请
            if(reqDto.getAppVol() == null || reqDto.getAppVol().compareTo(BigDecimal.ZERO) == 0 ){
                continue;
            }

            CounterRedeemReqDto counterRedeemReqDto = new CounterRedeemReqDto();
            
            //赎回是按同一个基金不同协议的赎回(普通公募或定投)
            FundInfoAndNavBean fundInfo = getFundInfoNav(reqDto.getFundCode(), transactorInfoDto.getAppDt(), transactorInfoDto.getAppTm());
            counterRedeemReqDto.setFundCode(fundInfo.getFundCode());
            counterRedeemReqDto.setFundName(fundInfo.getFundAttr());
            counterRedeemReqDto.setFundShareClass(fundInfo.getFundShareClass());
            counterRedeemReqDto.setTaCode(fundInfo.getTaCode());
            counterRedeemReqDto.setNav(fundInfo.getNav());
            
            counterRedeemReqDto.setDealAppNo(dealAppNo);
            counterRedeemReqDto.setAppVol(reqDto.getAppVol());
            counterRedeemReqDto.setCpAcctNo(reqDto.getCpAcctNo());
            counterRedeemReqDto.setBankCode(reqDto.getBankCode());
            counterRedeemReqDto.setBankAcct(reqDto.getBankAcct());
            counterRedeemReqDto.setLargeRedmFlag(reqDto.getLargeRedmFlag());
            counterRedeemReqDto.setUnusualTransType(reqDto.getUnusualTransType());
            // 赎回去向 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
            counterRedeemReqDto.setRedeemCapitalFlag(reqDto.getRedeemCapitalFlag());
            // 是否全赎标识：1-是；0-否
            counterRedeemReqDto.setAllRedeemFlag(reqDto.getAllRedeemFlag());
            // 协议号
            counterRedeemReqDto.setProtocolNo(reqDto.getProtocolNo());
            // 协议类型(普通公募或定投)
            counterRedeemReqDto.setProtocolType(reqDto.getProtocolType());
            // 可赎回日期
            counterRedeemReqDto.setOpenRedeDt(reqDto.getOpenRedeDt());

            counterRedeemReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
            // 产品类别: 零售
            counterRedeemReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
            counterRedeemReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
            counterRedeemReqDto.setEsitmateFee(BigDecimal.ZERO);
        
            counterRedeemReqDto.setTxAcctNo(custInfoDto.getCustNo());
            counterRedeemReqDto.setDisCode(custInfoDto.getDisCode());
            counterRedeemReqDto.setIdNo(custInfoDto.getIdNo());
            counterRedeemReqDto.setIdType(custInfoDto.getIdType());
            counterRedeemReqDto.setCustName(custInfoDto.getCustName());
            counterRedeemReqDto.setInvstType(custInfoDto.getInvstType());

            counterRedeemReqDto.setAppDt(transactorInfoDto.getAppDt());
            counterRedeemReqDto.setAppTm(transactorInfoDto.getAppTm());
            counterRedeemReqDto.setConsCode(transactorInfoDto.getConsCode());
            counterRedeemReqDto.setOutletCode(transactorInfoDto.getOutletCode());
            counterRedeemReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
            counterRedeemReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
            counterRedeemReqDto.setTransactorName(transactorInfoDto.getTransactorName());
            counterRedeemReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
            counterRedeemReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());
            
            counterRedeemReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
            counterRedeemReqDto.setChecker(operatorInfoCmd.getOperatorNo());
            CommonUtil.setCommonOperInfo(operatorInfoCmd, counterRedeemReqDto);
            
            // do service
            CounterRedeemRespDto responseDto = tmsFundCounterService.counterRedeem(counterRedeemReqDto, disInfoDto);
            
            TmsCounterResult rst = null;
            if(responseDto != null ){
                rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
            } else{
                rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
            }
            
            Map<String, Object> body = new HashMap<String, Object>(16);
            body.put("responseDto", responseDto);
            body.put("requestDto", reqDto);
            rst.setBody(body);
            
            // set return
            rstList.add(rst);
        }
        WebUtil.write(response, rstList);
        return null;
    }
}
