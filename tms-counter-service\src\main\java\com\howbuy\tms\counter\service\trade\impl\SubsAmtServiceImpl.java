package com.howbuy.tms.counter.service.trade.impl;

import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.service.trade.SubsAmtService;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.high.batch.facade.query.querySubsAmtChangeDetail.CustomerFundSubsAmtChangeDetailDto;
import com.howbuy.tms.high.batch.facade.query.querySubsAmtChangeDetail.QuerySubsAmtChangeDetailFacade;
import com.howbuy.tms.high.batch.facade.query.querySubsAmtChangeDetail.QuerySubsAmtChangeDetailRequest;
import com.howbuy.tms.high.batch.facade.query.querySubsAmtChangeDetail.QuerySubsAmtChangeDetailResponse;
import com.howbuy.tms.high.batch.facade.trade.customerSubsAmtApply.CustomerSubsAmtUpdateApplyFacade;
import com.howbuy.tms.high.batch.facade.trade.customerSubsAmtApply.CustomerSubsAmtUpdateApplyRequest;
import com.howbuy.tms.high.batch.facade.trade.customerSubsAmtApply.CustomerSubsAmtUpdateApplyResponse;
import com.howbuy.tms.high.batch.facade.trade.subsAmtApplyUpdate.SubsAmtApplyUpdateFacade;
import com.howbuy.tms.high.batch.facade.trade.subsAmtApplyUpdate.SubsAmtApplyUpdateRequest;
import com.howbuy.tms.high.batch.facade.trade.subsAmtApplyUpdate.SubsAmtApplyUpdateResponse;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.CustomerFundSubsAmtInfoDto;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtFacade;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtRequest;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:认缴金额相关
 * @Author: yun.lu
 * Date: 2024/7/15 14:14
 */
@Service
@Slf4j
public class SubsAmtServiceImpl implements SubsAmtService {
    @Autowired
    private QueryCustomerFundSubsAmtFacade queryCustomerFundSubsAmtFacade;
    @Autowired
    private CustomerSubsAmtUpdateApplyFacade customerSubsAmtUpdateApplyFacade;
    @Autowired
    private QuerySubsAmtChangeDetailFacade querySubsAmtChangeDetailFacade;
    @Autowired
    private SubsAmtApplyUpdateFacade subsAmtApplyUpdateFacade;

    @Override
    public CustomerSubsAmtResultDto querySubsAmtPage(CustomerSubsAmtPageReqDto customerSubsAmtPageReqDto) throws Exception {
        QueryCustomerFundSubsAmtRequest queryCustomerFundSubsAmtRequest = new QueryCustomerFundSubsAmtRequest();
        queryCustomerFundSubsAmtRequest.setTxAcctNo(customerSubsAmtPageReqDto.getTxAcctNo());
        queryCustomerFundSubsAmtRequest.setDisCode(customerSubsAmtPageReqDto.getDisCode());
        QueryCustomerFundSubsAmtResponse response = (QueryCustomerFundSubsAmtResponse) TmsFacadeUtil.executeThrowException(queryCustomerFundSubsAmtFacade, queryCustomerFundSubsAmtRequest, null);
        CustomerSubsAmtResultDto customerSubsAmtResultDto = new CustomerSubsAmtResultDto();
        customerSubsAmtResultDto.setResultCode(response.isSuccess() ? TmsCounterResultEnum.SUCC.getCode() : TmsCounterResultEnum.FAILD.getCode());
        customerSubsAmtResultDto.setResultMsg(response.getDescription());
        if (CollectionUtils.isNotEmpty(response.getCustomerFundSubsAmtInfoDtoList())) {
            List<CustomerSubsAmtInfoDto> customerSubsAmtInfoDtoList = new ArrayList<>();
            for (CustomerFundSubsAmtInfoDto customerFundSubsAmtInfoDto : response.getCustomerFundSubsAmtInfoDtoList()) {
                CustomerSubsAmtInfoDto customerSubsAmtInfoDto = new CustomerSubsAmtInfoDto();
                BeanUtils.copyProperties(customerFundSubsAmtInfoDto, customerSubsAmtInfoDto);
                customerSubsAmtInfoDtoList.add(customerSubsAmtInfoDto);
            }
            customerSubsAmtResultDto.setCustomerSubsAmtInfoDtoList(customerSubsAmtInfoDtoList);
        }
        return customerSubsAmtResultDto;
    }

    @Override
    public CustomerSubsAmtInfoDto querySubsAmtDetail(CustomerSubsAmtDetailReqDto customerSubsAmtDetailReqDto) throws Exception {
        QueryCustomerFundSubsAmtRequest queryCustomerFundSubsAmtRequest = new QueryCustomerFundSubsAmtRequest();
        queryCustomerFundSubsAmtRequest.setTxAcctNo(customerSubsAmtDetailReqDto.getTxAcctNo());
        queryCustomerFundSubsAmtRequest.setFundCode(customerSubsAmtDetailReqDto.getFundCode());
        queryCustomerFundSubsAmtRequest.setDisCode(customerSubsAmtDetailReqDto.getDisCode());
        QueryCustomerFundSubsAmtResponse response = (QueryCustomerFundSubsAmtResponse) TmsFacadeUtil.executeThrowException(queryCustomerFundSubsAmtFacade, queryCustomerFundSubsAmtRequest, null);
        if (CollectionUtils.isNotEmpty(response.getCustomerFundSubsAmtInfoDtoList())) {
            CustomerFundSubsAmtInfoDto customerFundSubsAmtInfoDto = response.getCustomerFundSubsAmtInfoDtoList().get(0);
            CustomerSubsAmtInfoDto customerSubsAmtInfoDto = new CustomerSubsAmtInfoDto();
            BeanUtils.copyProperties(customerFundSubsAmtInfoDto, customerSubsAmtInfoDto);
            return customerSubsAmtInfoDto;
        }
        return null;
    }

    @Override
    public CustomerSubsAmtApplyResultDto subsAmtDetailApply(CustomerSubsAmtApplyReqDto customerSubsAmtApplyReqDto) throws Exception {
        CustomerSubsAmtUpdateApplyRequest customerSubsAmtUpdateApplyRequest = new CustomerSubsAmtUpdateApplyRequest();
        BeanUtils.copyProperties(customerSubsAmtApplyReqDto, customerSubsAmtUpdateApplyRequest);
        CustomerSubsAmtUpdateApplyResponse response = (CustomerSubsAmtUpdateApplyResponse) TmsFacadeUtil.executeThrowException(customerSubsAmtUpdateApplyFacade, customerSubsAmtUpdateApplyRequest, null);
        CustomerSubsAmtApplyResultDto applyResult = new CustomerSubsAmtApplyResultDto();
        if (response.isSuccess()) {
            applyResult.setApplySuccess(YesOrNoEnum.YES.getCode());
        } else {
            applyResult.setApplySuccess(YesOrNoEnum.NO.getCode());
        }
        applyResult.setMsg(response.getDescription());
        return applyResult;
    }

    @Override
    public CustomerSubsAmtChangeDetailDto querySubsAmtChangeDetail(String dealAppNo) throws Exception {
        QuerySubsAmtChangeDetailRequest querySubsAmtChangeDetailRequest = new QuerySubsAmtChangeDetailRequest();
        querySubsAmtChangeDetailRequest.setDealAppNo(dealAppNo);
        QuerySubsAmtChangeDetailResponse response = (QuerySubsAmtChangeDetailResponse) TmsFacadeUtil.executeThrowException(querySubsAmtChangeDetailFacade, querySubsAmtChangeDetailRequest, null);
        CustomerFundSubsAmtChangeDetailDto customerFundSubsAmtChangeDetailDto = response.getCustomerFundSubsAmtChangeDetailDto();
        if (customerFundSubsAmtChangeDetailDto != null) {
            CustomerSubsAmtChangeDetailDto customerSubsAmtChangeDetailDto = new CustomerSubsAmtChangeDetailDto();
            BeanUtils.copyProperties(customerFundSubsAmtChangeDetailDto, customerSubsAmtChangeDetailDto);
            return customerSubsAmtChangeDetailDto;
        }
        return null;
    }

    @Override
    public CustomerSubsAmtApplyResultDto updateApply(UpdateSubsAmtDetailApplyDto updateSubsAmtDetailApplyDto) throws Exception {
        SubsAmtApplyUpdateRequest customerSubsAmtUpdateApplyRequest = new SubsAmtApplyUpdateRequest();
        BeanUtils.copyProperties(updateSubsAmtDetailApplyDto, customerSubsAmtUpdateApplyRequest);
        SubsAmtApplyUpdateResponse response = (SubsAmtApplyUpdateResponse) TmsFacadeUtil.executeThrowException(subsAmtApplyUpdateFacade, customerSubsAmtUpdateApplyRequest, null);
        CustomerSubsAmtApplyResultDto applyResult = new CustomerSubsAmtApplyResultDto();
        if (response.isSuccess()) {
            applyResult.setApplySuccess(YesOrNoEnum.YES.getCode());
        } else {
            applyResult.setApplySuccess(YesOrNoEnum.NO.getCode());
        }
        applyResult.setMsg(response.getDescription());
        return applyResult;    }
}
