/***
*购买
*<AUTHOR>
*@date 2017-07-03 10:39
 */
$(function(){
    		$("#queryCustBtn").off();
			$("#queryCustBtn").on('click',function(){
				QueryCustInfoSubPage.queryCustInfo();
			});
			$("#resetLayerBtn").off();
			$("#resetLayerBtn").on('click',function(){
				QueryCustInfoSubPage.reset('seachCustForm');
			})
});
    	
var frameIndex = parent.layer.getFrameIndex(window.name); //获取窗口索引
    	
//给父页面传值
$('#transmit').on('click', function(){
    var checkedInputs = $("#custPosiTable").find("input[type='checkbox']:checked");
	if(checkedInputs.length <= 0){
		showMsg("请选择客户")
		return false ;
	}else if(checkedInputs.length >1){
		showMsg("不能多选，只能选择一个客户")
		return false;
	}else{
		var custNo = $(checkedInputs[0]).val();
		parent.$('#custNo').val(custNo);
		$("#layerrs").empty();
		QueryCustInfoSubPage.reset('seachCustForm');
		parent.layer.close(frameIndex);		
	}
 });


QueryCustInfoSubPage = {
		/**
		 * 客户信息查询弹出
		 */
		selectCustNo:function(targetobj){
			var top =targetobj.offset().top;
			var left =targetobj.offset().left;
			layer.open({
				  type: 2,
				  shade: [0.1 ,'#fff'],
				  title:'客户信息查询',
				  area: ['700px', 'auto'],
				  offset: [top,left],
				  content: '../../other/querycustinfosubpage.html'
				});
		},
		/**
		 * 定位客户信息
		 */
		queryCustInfo:function(){
			var serachForm = $("#seachCustForm").serializeObject();
			var custNo = serachForm['custNo'];
			var custName = serachForm['custName'];
			var idNo = serachForm['idNo'];
			if(isEmpty(custNo) && isEmpty(custName) && isEmpty(idNo)){
				showMsg("客户号，客户姓名,证件号必须输入一项");
				return false;
			}
			var uri= TmsCounterConfig.QUERY_CUST_INFO_SUB_PAGE_URL  ||  {};
			var reqparamters = {};
			if(!isEmpty(custNo)){
				reqparamters.custNo = custNo;
			}else if(!isEmpty(custName) ){
				reqparamters.custName = encodeURIComponent(custName);
			}else if(!isEmpty(idNo)){
				reqparamters.idNo = idNo;
			}
			
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);

			CommonUtil.ajaxAndCallBack(paramters, QueryCustInfoSubPage.processCustInfoView);
			
		},
		/**
		 * 重置查询条件
		 * @param formId
		 */
		reset:function(formId){
			$("#"+formId).find("input").each(function(index,element){
				$(element).val('');
			})
		},
		
		/**
		 *渲染客户信息查询结果
		 */
		processCustInfoView:function(data){
			var bodyData = data.body || {};
			var respData = bodyData.respData || [];
			var custBaseInfoList = respData.custBaseInfoBeanList || []
			var len = custBaseInfoList.length;
			var appendHtml = '';
			$("#layerrs").empty();
			if(len <=0){
				appendHtml = '<tr><td colspan="5">没有查询结果</td><tr>';
				$("#layerrs").append(appendHtml);
			}
			else{
				$(custBaseInfoList).each(function(index,element){
					var idTypeValue = '';
					if(element.invstType == '0'){//属于机构
						idTypeValue = getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, formatData(element.idType));
					}
					if(element.invstType == '1'){
						idTypeValue = getMapValue(CONSTANTS.ID_TYPE_MAP, formatData(element.idType));
					}
					if(element.invstType == '2'){
						idTypeValue = getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, formatData(element.idType));
					}
					var appendHtml = '<tr class="text-c">'+
									'<td><input type="checkbox" value="'+element.txAcctNo+'"/></td>'+
									 '<td>'+formatData(element.txAcctNo)+'</td>'+
									 '<td>'+formatData(element.custName)+'</td>'+
									 '<td>'+idTypeValue+'</td>'+
									 '<td>'+formatData(element.idNo)+'</td>'+
									 '<td>'+getMapValue(CONSTANTS.CUST_STAT_MAP,formatData(element.custStat))+'</td>'+
									 '<td>'+getMapValue(CONSTANTS.DISCODE_MAP,formatData(element.regDisCode))+'</td>'+
									'<tr>';
					$("#layerrs").append(appendHtml);
				});		
			}
		},
};