/**
*公共
*<AUTHOR>
*@date 2017-03-28 10:47
*/
var CONSTANTS = {};

/**
 * 返回码
 */

CONSTANTS.RETURN_CODE = {
		StatusCode:{
			SUCCESS:"0000", //成功
		}
		
};
/**
 * 操作类型
 */
CONSTANTS.OPERATION_TYPE_ADD = "1";
CONSTANTS.OPERATION_TYPE_MODIFY = "2";
CONSTANTS.OPERATION_TYPE_DELETE = "3";
CONSTANTS.OPERATION_TYPE_MAP={"1":"添加","2":"修改","3":"删除"};

/**
 * 协议开通标志
 */
CONSTANTS.PROTOCOL_OPEN_FLAG_MAP={"0":"关闭","1":"开启"};
/**
 * 身份证校验状态
 */
CONSTANTS.IDVRFY_STAT_MAP={"1":"已验证","0":"未验证"};
/**
 * 客户状态
 */
CONSTANTS.CUST_STAT_MAP={"1":"销户","0":"正常"};
/**
 * 是否签署合格的投资者认证
 */
CONSTANTS.CUST_SIGN_FLAG_MAP={"1":"是","0":"否"};

/**
 * 银行Code-Name Map 定义
 */
CONSTANTS.BANK_NAME_MAP = {
    "310": "上海浦东发展银行",
    "102": "中国工商银行",
    "103": "中国农业银行",
    "104": "中国银行",
    "105": "中国建设银行",
    "305": "中国民生银行",
    "307": "平安银行（原深圳发展银行）",
    "308": "招商银行",
    "309": "兴业银行",
    "403": "中国邮政储蓄银行",
    "783": "平安银行",
    "318": "渤海银行股份有限公司",
    "304": "华夏银行",
    "332": "浙江泰隆商业银行",
    "333": "浙江稠州商业银行",
    "334": "南昌银行",
    "335": "农村商业银行",
    "336": "星展银行",
    "337": "广东华兴银行",
    "338": "重庆三峡银行",
    "339": "华侨银行",
    "341": "江苏江阴农村商业银行",
    "469": "华润银行",
    "495": "浙商银行",
    "515": "张家口市城郊农村信用合作联社",
    "521": "东莞银行",
    "313": "广东南粤银行",
    "302": "中信银行",
    "306": "广东发展银行",
    "314": "上海银行",
    "321": "青岛银行",
    "496": "张家口市农村信用合作社",
    "359": "成都银行",
    "497": "深圳农村商业银行",
    "435": "盛京银行",
    "511": "齐鲁银行",
    "517": "花旗银行",
    "518": "杭州联合农村合作银行",
    "522": "晋商银行",
    "317": "温州银行",
    "300": "常熟农村商业银行",
    "324": "杭州银行",
    "325": "秦皇岛银行",
    "326": "汉口银行",
    "327": "广东顺德农村商业银行",
    "328": "四川省农村信用社",
    "329": "长安银行",
    "330": "兰州银行",
    "331": "青海银行",
    "340": "东亚银行",
    "402": "江南农村商业银行",
    "510": "贵州独山农村商业银行股份有限公司",
    "514": "大竹县农村信用合作社联合社",
    "519": "永隆银行",
    "524": "国泰银行",
    "322": "永亨银行(中国)有限公司",
    "502": "东亚银行",
    "516": "天津农商银行",
    "319": "江苏银行股份有限公司",
    "106": "交通银行",
    "112": "中国光大银行",
    "315": "北京银行",
    "316": "宁波银行",
    "320": "南京银行",
    "323": "大连银行",
    "342": "上海农村商业银行",
    "343": "湖北随州农村商业银行",
    "512": "汇丰银行",
    "513": "恒生银行",
    "520": "潍坊银行",
    "523": "台州市商业银行",
    "529": "中国包商银行股份有限公司",
    "532": "绵阳市商业银行",
    "533": "浙江义乌农村商业银行",
    "534": "桂林银行",
    "537": "江苏省农村信用社",
    "538": "鄞州银行",
    "999": "吉林九台农村商业银行",
    "282": "徽商银行股份有限公司",
    "997": "江苏靖江农村商业银行",
    "390": "吉林银行",
    "381": "哈尔滨银行",
    "415": "龙江银行",
    "525": "深圳前海微众银行",
    "392": "嘉兴银行",
    "450": "厦门银行",
    "539": "浙江网商银行股份有限公司",
    "377": "广州银行",
    "526": "山东寿光农村商业银行",
    "527": "吉林省农村信用社",
    "397": "晋城银行",
    "535": "长沙银行",
    "429": "泉州银行",
    "540": "长治银行",
    "885": "中原银行",
    "614": "江苏镇江农村商业银行股份有限公司",
    "996": "中央结算公司",
    "379": "贵阳银行",
    "528": "恒丰银行",
    "530": "厦门农村商业银行",
    "531": "广州农村商业银行",
    "536": "锦州银行",
    "998": "富滇银行股份有限公司",
    "541": "浙江萧山农村商业银行股份有限公司",
    "756": "台安县城郊农村信用合作社"
};



/**
 * 基金份额类型
 */
CONSTANTS.FUND_SHARECLASS_MAP={"A":"前收费","B":"后收费"};
/**
 * 银行账户状态
 */
CONSTANTS.BANKACCT_STATUS_MAP = {"0":"正常","1":"待审核","2":"注销","3":"冻结","4":"销户待确认","5":"冻结待确认"};
/**
 *卡验证状态  
 */
CONSTANTS.VRFYCARD_STAT_MAP={"2":"验证通过","3":"验证失败"};
/**
 *法人代表证件是否长期有效
 */
CONSTANTS.CORPID_ALWAYS_VALIDFLAG_MAP = {"0":"否","1":"是"};
/**
 * 用户是否长期有效
 */
CONSTANTS.ID_ALWAYS_VALIDFLAG_MAP ={"0":"否","1":"是"};
/**
 * 投资人类型
 */
CONSTANTS.INVST_TYPE_MAP ={"0":"机构","1":"个人","2":"产品"};

/**
 * 投资者类别
 */
CONSTANTS.QUALIFICATION_TYPE_MAP={"0":"普通","1":"专业"};

/**
 * 机构回款方向
 */
CONSTANTS.JIGOU_DIRECTION_TYPE_SHOW_MAP={"0":"回款到银行卡","1":"回款到储蓄罐","2":"回款到银行卡","3":"回款到款储蓄罐","5":"回款到余额"};

/**
 * 机构回款方向
 */
CONSTANTS.JIGOU_DIRECTION_TYPE_MAP={"2":"回款到银行卡","5":"回款到余额"};

/**
 * 个人回款方向
 */
CONSTANTS.PERSON_DIRECTION_TYPE_MAP={"2":"回款到银行卡","3":"回款到储蓄罐","5":"回款到余额"};

/**
 * 个人证件类型
 */
CONSTANTS.ID_TYPE_MAP ={"0":"身份证",
		"1": "中国护照",
		"2": "军官证",
		"3": "士兵证",
		"4": "港澳通行证",
		"5": "户口本",
		"6": "外国护照",
		"7": "个人其它",
		"8": "文职证",
		"9": "警官证",
		"A":"台胞证",
		"B":"回乡证",
		"C":"港澳台居民居住证"};
/**
 * 机构证件类型
 */
CONSTANTS.JIGOU_ID_TYPE_MAP ={"0":"机构组织代码",
		"1": "营业执照",
		"2": "行政机关",
		"3": "社会团体",
		"4": "军队",
		"5": "武警",
		"6": "下属机构",
		"7": "基金会",
		"8": "机构其他",
		"9": "登记证书",
		"A":"批文"};

CONSTANTS.PRODUCT_ID_TYPE_MAP  ={"1": "营业执照",
		"8": "其它",
		"9": "登记证书",
		"A":"批文"};

CONSTANTS.BOTH_ID_TYPE_MAP ={
		"00":"机构组织代码",
		"01": "营业执照",
		"02": "行政机关",
		"03": "社会团体",
		"04": "军队",
		"05": "武警",
		"06": "下属机构",
		"07": "基金会",
		"08": "机构其他",
		"09": "登记证书",
		"0A": "批文",
		"10":"身份证",
		"11": "护照",
		"12": "军官证",
		"13": "士兵证",
		"14": "港澳通行证",
		"15": "户口本",
		"16": "外国护照",
		"17": "个人其它",
		"18": "文职证",
		"19": "警官证",
		"1A":"台胞证",
		"1B":"回乡证"};


/**
 * 产品证件类型
 */
CONSTANTS.PRODUCT_ID_TYPE_MAP ={"1": "营业执照",
		"8": "其它",
		"9": "登记证书",
		"A":"批文"};

/**
 * 波动平衡执行状态
 */
CONSTANTS.BALANCE_EXEC_STATUS_MAP = {"0":"未执行","1":"执行中","2":"执行成功","3":"执行失败","4":"无需执行","5":"执行结束"};

/**
 * 组合产品类型别名
 */
CONSTANTS.PORTFOLIO_TYPE_ALIAS_MAP = {"1":"债券","2":"股票"};

/**
 * 风险确认标识
 */
CONSTANTS.RISK_FLAG_MAP = {"0":"无需确认","1":"已确认"};

/**
 * 组合产品类别
 */
CONSTANTS.PORTFOLIO_TYPE_MAP = {"1":"公募组合"};
/**
 * 代销基金类型
 */
CONSTANTS.CONSIGNMENT_FUNDTYPE_MAP = {"0":"股票型" ,"1":"混合型" ,"2":"债券型","3":"货币型" ,"4":"QDII","5":"封闭式","6":"结构型","7":"一对多","8":"券商资管产品-大集合","9":"券商资管产品-小集合","11":"私募"};


/**
 * 代销基金类型
 */
CONSTANTS.FUNDTYPE_MAP = {"0":"股票型" ,"1":"混合型" ,"2":"债券型","3":"货币型" ,"4":"QDII","5":"封闭式","6":"结构型","7":"一对多","8":"券商资管产品-大集合","9":"券商资管产品-小集合","10":"FOF",
    "11":"私募","A":"指数型","B":"配置型","C":"信托","D":"其他","E":"养老计划","F":"交易所","F":"交易所","H":"对冲型","I":"PE/VC","J": "其他"};

/**
 * 产品子类型
 */
CONSTANTS.FUND_SUB_TYPE_MAP = {
    "0" :"股票型",
    "1" :"混合型",
    "2" :"债券型",
    "3" :"货币型",
    "4" :"QDII",
    "5" :"封闭式",
    "6" :"结构型",
    "7" :"一对多专户",
    "8" :"券商大集合",
    "9" :"券商小集合",
    "10" :"FOF",
    "11": "私募",
    "A" :"固定收益",
    "B" :"对冲型",
    "C" :"PE/VC",
    "D": "其他"
};

/**
 * 热销基金类型
 */
CONSTANTS.HOT_fundType = {"1":"股票型","5":"债券型","3":"混合型","7":"货币型","8":"指数型","t":"结构型","9":"QDII","a":"封闭式","b":"保本型","53":"理财型"};

/**
 * 产品类别
 */
CONSTANTS.PRODUCT_TYPE_MAP = {
		"0":"股票型" ,"1":"混合型" ,"2":"债券型","3":"货币型" ,"4":"QDII","5":"封闭式","6":"结构型","7":"一对多","8":"券商资管产品-大集合","9":"券商资管产品-小集合","10":"FOF","11":"私募",
		"A":"指数型","B":"配置型","C":"信托","D":"其他","E":"养老计划","F":"交易所","H":"对冲型","I":"PE/VC","J": "其他"
};

/**
 * 基金状态
 */
CONSTANTS.FUND_STATE={"0":"交易","1":"发行","2":"发行成功","3":"发行失败","4":"停止交易","5":"停止申购","6":"停止赎回",
		"7":"权益登记","8":"红利发放","9":"基金封闭","a":"基金终止"};
/**
 * 支付方式
 */
CONSTANTS.PAYMENT_TYPE = {"01": "银行卡", "04":"银行卡","06": "储蓄罐","09": "储蓄罐一键支付"};
CONSTANTS.PAYMENT_TYPE_MAP = {"01": "自划款", "04":"协议代扣","06": "储蓄罐活期","09": "储蓄罐一键支付"};
CONSTANTS.PMT_CHECK_PAYMENT_TYPES = {"bank":"银行卡","cxg":"储蓄罐"};
CONSTANTS.PAYMENT_TYPE_CHANGE_MAP = {"01": "自划款", "04":"银行卡","06-09": "储蓄罐活期&储蓄罐一键支付","07":"基金转投","01-04-06-07-09":"全部"};
CONSTANTS.PAYMENT_ALL_TYPE = {"01": "自划款", "04":"银行卡","06": "储蓄罐","07": "基金转投","09": "储蓄罐一键支付"};


/**
 * 成交比例
 */
CONSTANTS.TX_RATIO_MAP={"0":"","1":"部分成交","2":"交易失败","3":"全部成交"};

/**
 * 赎回
 */
CONSTANTS.FUND_CAN_REDEEM_MAP={"0":"不可赎","1":"可赎"};

/**
 * 成交状态
 */
CONSTANTS.TX_STATUS_MAP = {"0":"","1":"未成交","2":"交易未结束","3":"交易结束"};

/**
 * 交易申请标记
 */
CONSTANTS.TX_APP_FLAG_MAP =  {"0":"申请成功","1":"申请失败","2":"自行撤销","3":"强制取消"};
/**
 * 上报申请标记
 */
CONSTANTS.SUBMIT_APP_FLAG_MAP = {"0":"无需上报","1":"上报中","2":"上报完成","3":"需重新上报"};

/**
 * 分红方式分红方式
 */
CONSTANTS.FUND_DIV_MODE_MAP = {"0":"红利再投","1":"现金红利", "9":"不支持修改分红方式"};

CONSTANTS.FUND_DIV_MODE_DIS_MAP = {"红利再投":"0","现金红利":"1"};

/**
 * 交易渠道
 */
CONSTANTS.TX_CHANNEL_MAP = {"1":"柜台","2":"网站","3":"电话","4":"Wap","5":"App"};

/**
 * 交易申请状态
 */
CONSTANTS.TX_APP_FLAG_MAP = {"0":"申请成功","1":"申请失败","2":"自行撤销","3":"强制取消"};

/**
 * 订单状态
 */
CONSTANTS.ORDER_STATUS_MAP = {"1":"申请成功","2":"部分确认","3":"确认成功","4":"确认失败","5":"自行撤销","6":"强制取消"};

/**
 * 预约单状态
 */
CONSTANTS.PRE_ORDER_STATUS_MAP = {"CONFIRM":"确认","UN_CONFIRM":"未确认","NORMAL":"正常","CANCEL":"取消","REJECT":"拒绝","OUTOFDATE":"过期",
	"FINISH":"完成"	};

/**
 * 预约单状态（新）
 */
CONSTANTS.PRE_BOOK_STATE_MAP = {"1":"未确认","2":"已确认","4":"已撤销"};

/**
 * 订单明细申请标志
 */
CONSTANTS.TX_APP_FLAG = {"0":"申请成功","1":"申请失败","2":"自行撤销","3":"强制取消"};

/**
 * 订单确认标志
 */
CONSTANTS.TX_ACK_FLAG = {"0":"无需确认","1":"","2":"确认中","3":"部分确认","4":"确认成功","5":"确认失败"};
/**
 * 订单明细通知标志
 */
CONSTANTS.NOTIFY_SUBMIT_FlAG = {"0":"不需通知","1":"未通知","2":"已通知","3":"重新通知"};

/**
 * 交易code
 */
CONSTANTS.TX_OPEN_CFG = {"Z310001":"买入","Z310002":"卖出","Z310003":"拉杆平衡","Z310004":"波动平衡","Z310010":"撤单","Z310005":"修改分红方式","Z310012":"观点平衡"};

CONSTANTS.BALANCE_TX_MAP={"Z310012":"观点平衡","Z310004":"波动平衡"};

/**
 * 中台交易码
 */
CONSTANTS.TX_CODE_MAP = {"Z310001":"买入","Z310002":"卖出","Z310003":"拉杆平衡","Z310004":"波动平衡","Z310005":"修改分红方式","Z310006":"强赎","Z310007":"分红",
		"Z310008":"强增","Z310009":"强减","Z310012":"观点平衡"};
/**
 * 中台交易码
 */
CONSTANTS.BUSI_CODE_MAP = {"1120":"认购","1122":"申购","1124":"赎回","1236":"基金转投"};

/**
 * 订单付款状态
 */
CONSTANTS.ORDER_PAY_STATUS_MAP  = {"0":"无需付款","1":"未付款","2":"付款中","3":"部分成功","4":"成功","5":"失败"};
/**
* 交易支付状态
*/
CONSTANTS.TX_PMT_FLAG = {"0":"无需付款","1":"未付款","2":"付款成功","3":"付款失败","4":"付款中","5":"已退款","6":"等待付款","7":"撤单成功"};

/**
 * 分销机构集合
 */
CONSTANTS.DISCODE_MAP = {
        "UNIONPAY1":"云闪付",
		"HB000A001":"好买",
		"TXW00D001":"腾讯",
		"BD000E001":"百度财富",
		"AON00H001":"怡安悠选",
		"BDGST0001":"百度股市通",
		"BDLC0A001":"百度理财",
		"XGJ00W001":"鑫管家",
		"OTC201704":"机构",
		"GLZC0F001":"上海观岭资产",
		"TGJYQHFOFH001":"铜冠金源期货FOF",
        "HZ000N001":"好臻",
        "FOF201710":"机构",
		"EFUNDS001":"易方达",
		"CJHX0001":"创金合信",
		"JXFX0A001": "建信投顾",
		"CTRIPA001": "携程金融",
		"COMPASS01":"指南针",
    	"FOF201710":"机构",
		"JFHXGA001":"会选股",
    	"JHSVIP001":"金花生",
		"LCT00K001":"理财通",
   		"YIXUEA001":"亿学",
    	"YLCXTA001":"鹰理财"
};
/**
 * 好买分销机构号
 */
CONSTANTS.HB_DISCODE = 'HB000A001';

/**
 * 
 *  导入文件状态
 */
CONSTANTS.IMPORT_STATE = {"0":"未处理","1":"导入成功","2":"导入失败","3":"生成成功","4":"生成失败","5":"处理中"};
/**
 *文件处理状态
 */
CONSTANTS.ROCESS_STATE = {"0":"未处理","1":"通知成功","2":"处理中","3":"处理成功","4":"处理失败"};
/**
 * 支付对账结果集合
 */
CONSTANTS.PAYMENT_CHECK_STATUS = {"0":"对账成功","1":"状态不一致","2":"金额不一致","3":"单边"};
/**
 * 支付对账状态
 */
CONSTANTS.PAYMENT_COMP_FLAGS = {"0":"无需对账","1":"未对账","2":"对账完成","3":"金额不一致"};
/**
 * 批处理任务状态
 */
CONSTANTS.BATCH_StATUS = {"0":"未执行","1":"执行中","2":"执行成功"};





/**
 * 交易对账状态
 */
CONSTANTS.TX_COMP_FLAG ={"0":"无需对账","1":"未对账","2":"对账完成","3":"金额不一致","4":"状态不一致"};

CONSTANTS.TX_COMP_FLAG_MAP ={"0":"无需对账","1":"未对账","2":"对账完成","3":"金额不一致","4":"状态不一致"};
/**
 * 订单上报申请标记
 */
CONSTANTS.SUBMIT_APP_FLAG = {"0":"申请成功","1":"申请失败","2":"自行撤销","3":"强制取消"};

/** 
 * 后台下拉的交易类型
 */
CONSTANTS.SELECT_BUSI_CODES_MAP= {
"120":"认购",
"122":"申购",
"124":"赎回",
"136":"基金转换",
"129":"更改基金分红",
"143":"红利发放",
"144":"强增",
"142":"强赎",
"145":"强减",
"129":"修改分红方式"
};
/** 
 * 后台交易类型
 * 
 */
CONSTANTS.BUSI_CODES_MAP= {
"020":"认购",
"120":"认购",
"021":"预约认购",
"121":"预约认购",
"022":"申购",
"122":"申购",
"023":"预约申购",
"123":"预约申购",
"024":"赎回",
"124":"赎回",
"025":"预约赎回",
"125":"预约赎回",
"026":"转托管",
"126":"转托管",
"027":"转托管转入",
"127":"转托管转入",
"028":"转托管转出",
"128":"转托管转出",
"029":"更改基金分红",
"129":"更改基金分红",
"130":"认购结果",
"031":"基金份额冻结",
"131":"基金份额冻结",
"032":"基金份额解冻",
"132":"基金份额解冻",
"033":"非交易过户",
"133":"非交易过户",
"034":"非交易过户转入",
"134":"非交易过户转入",
"035":"非交易过户转出",
"135":"非交易过户转出",
"036":"基金转换",
"136":"基金转换",
"037":"基金转换入",
"137":"基金转换入",
"038":"基金转换出",
"138":"基金转换出",
"039":"定时定额投资",
"139":"定时定额投资",
"040":"退款",
"140":"退款",
"041":"补款",
"142":"强行赎回",
"143":"红利发放",
"144":"份额调增",
"145":"份额调减",
"146":"份额调减",
"149":"募集失败",
"150":"基金清盘",
"151":"基金终止",
"052":"撤单",
"152":"撤单",
"053":"撤预约单",
"153":"撤预约单",
"054":"无效资金",
"154":"无效资金",
"301":"基金交易帐户开户",
"302":"基金交易帐户销户",
"303":"客户资料修改",
"304":"交易帐户冻结",
"305":"交易帐户解冻",
"308":"客户重要资料修改",
"309":"修改客户补充资料",
"310":"经办人维护",
"311":"开通代扣款资金支付方式",
"312":"关闭代扣款支付方式",
"313":"增加客户经办人",
"314":"修改客户经办人",
"315":"删除客户经办人",
"316":"客户风险调查",
"317":"交易密码修改",
"318":"交易密码重置",
"319":"交易密码解锁",
"320":"更改银行资料",
"321":"换卡",
"322":"身份及卡验证维护",
"323":"支付申请启动",
"324":"支付结果记录",
"325":"设置默认风险等级",
"326":"开户银行卡更改",
"327":"账户验证扣款申请",
"328":"账户验证扣款结果记录",
"351":"审核交易",
"352":"撤销交易",
"353":"强制取消",
"401":"基金账户批量开户",
"402":"基金账户批量销户",
"420":"批量认申购",
"424":"批量赎回",
"424":"批量赎回",
"436":"批量基金转换",
"429":"批量更改分红方式",
"511":"定时定额申购协议开通",
"512":"定时定额申购协议维护",
"513":"支付协议签约",
"514":"支付协议解约",
"089":"快速赎回"
};


/** 
 * 交易类型
 * 
 */
CONSTANTS.BUSI_CODES_CHANGE_MAP= {
"020-022":"认申购",
"039":"定投",
"020-022-039":"全部",
};

/**
 * 中台业务名称(M_BUSI_CODE)
 */
CONSTANTS.M_BUSI_CODE_NAME = {
		"1120":"认购",
		"1122":"申购",
		"1124":"赎回",
		"1136":"基金转换",
		"1236":"基金转投",
		"1143":"分红",
		"1144":"强增",
		"1145":"强减",
		"1142":"强赎",
		"1129":"修改分红方式",
		"1126":"转托管",
		"1127":"转托管转入",
		"1128":"转托管转出",
		"1133":"非交易过户",
		"1134":"非交易过户转入",
		"1135":"非交易过户转出",
		"1125":"预约赎回",
		"1130":"认购结果",
		"1131":"基金份额冻结",
		"1139":"普通定投",
		"1189":"股基T+1快速赎回",
		"1362":"份额合并",
		"1364":"份额迁移",
        "1367":"份额转移"

};

/**
 * 主订单---中台业务码(Z_BUSI_CODE)
 */
CONSTANTS.Z_BUSICODE_MAP = {
    "10": "买入",
    "11": "卖出",
    "12": "智能定投买入",
    "13": "智能定投卖出",
    "14": "组合定投买入",
    "15": "拉杆平衡",
    "16": "波动平衡",
    "17": "观点平衡",
    "18": "修改分红方式",
    "19": "强制赎回",
    "20": "红利下发",
    "21": "份额强增",
    "22": "份额强减",
    "23": "自定义组合买入",
    "24": "普通定投买入",
    "25": "普通定投卖出",
    "26": "基金转换",
    "27": "股基T+1快速赎回",
    "28": "基金转投",
    "29": "批量买入",
    "30": "份额合并",
    "31": "份额迁移",
    "32": "转托管转入",
    "33": "转托管转出",
    "34": "U组合调仓",
    "35": "U组合申购",
    "36": "U组合赎回",
    "37": "U组合定投",
    "38": "组合碎片整理",
    "39": "快速取现",
    "40": "手动平衡",
    "41": "T0赎回到储蓄罐",
    "42": "货币赢+转投",
    "43": "目标止盈",
    "44": "移动止盈",
    "46": "非交易过户转入",
    "47": "止盈赎回", //止盈赎回-符合预期
    "48": "到期赎回", //止盈赎回-持仓超过指定天数
    "49": "终止赎回",  //止盈赎回-持仓超过指定日期
    "52": "人工调仓",
    "53": "异常补单",
    "54": "费用扣收",
    "55": "撤单回储蓄罐",
    "56": "单基金转组合",
    "57": "份额转移",
    "58": "废单",
    "60": "组合转投顾",
    "61": "投顾调仓",
    "62": "份额转托管",
    "63": "强制赎回",
    "64": "自动跟投"
};

/**
 * 中台业务码 MAP
 */

CONSTANTS.M_BUSI_CODE_NAME_MAP = {"1120":"认购","1139":"定投","1122":"申购","1124":"赎回","1260":"拉杆平衡",
    "1261":"波动平衡","1262":"观点平衡","1136":"基金转换", "1236":"基金转投", "1129": "修改分红方式", "1367":"份额迁移" };

/**
 * 预约购买类型
 */
CONSTANTS.PRE_BUY_STATUS_NAME_MAP = {"P":"申购","S":"认购"};
/**
 * 预约shuihui类型
 */
CONSTANTS.PRE_SELL_STATUS_NAME_MAP = {"P":"赎回"};

/**
 * 预约类型
 */
CONSTANTS.PRE_TRADE_TYPE_NAME_MAP = {"1":"购买","2":"追加","3":"赎回"};

/**
 * 风险等级
 */
CONSTANTS.RISK_LEVEL_MAP = {"5": "高风险", "4": "中高风险", "3": "中风险", "2": "中低风险", "1": "低风险","0":"极低"};
/**
 * 基金风险等级
 */
CONSTANTS.FUND_RISK_LEVELS_MAP = {"5": "高风险", "4": "中高风险", "3": "中风险", "2": "中低风险", "1": "低风险"};
/**
 * 是否展示
 */
CONSTANTS.DISPLAY_FLAG_MAP = {"0":"否","1":"是"};

/**
 * 巨额赎回顺延
 */
CONSTANTS.LARGE_REDM_FLAG_MAP = {"0":"不顺延","1":"顺延"};

CONSTANTS.PRODUCT_STATUS_MAP = {"0":"无效","1":"正常"};
/**
 * 退款类型
 */
CONSTANTS.REFUND_TYPE_MAP  = {"1":"申请失败退款","2":"强制取消退款","3":"自行撤销退款","4":"确认失败退款","5":"部分确认退款"};
/**
 * 退款支付方式
 */
CONSTANTS.REPORT_PAY_TYPE_MAP = {"1":"银行卡","2":"储蓄罐"};
/**
 * 赎回去向0-银行卡1-储蓄罐
 */
CONSTANTS.REDEEM_DIRECTION_MAP = {"0":"银行卡","1":"储蓄罐", "5":"回可用余额","6":"可用余额+银行卡","7":"可用余额+储蓄罐"};
/**
 * 平衡业务类型
 */
CONSTANTS.BALANCE_TXCODE_MAP={"Z310004":"波动平衡","Z310012":"观点平衡"};
/**
 * 节假日顺延类型
 */
CONSTANTS.POSTPHONE_TYPE_MAP={"0":"不顺延","1":"顺延"};
/**
 * 执行周期
 */
CONSTANTS.EXEC_TYPE = {"1":"月"};
CONSTANTS.EXEC_TYPE_MAP = {"1":"月","2":"期间"};
/**
 * 执行日
 */
CONSTANTS.EXEC_DAY = {"1":"1",
		"2":"2",
		"3":"3",
		"4":"4",
		"5":"5",
		"6":"6",
		"7":"7",
		"8":"8",
		"9":"9",
		"10":"10",
		"11":"11",
		"12":"12",
		"13":"13",
		"14":"14",
		"15":"15",
		"16":"16",
		"17":"17",
		"18":"18",
		"19":"19",
		"20":"20",
		"21":"21",
		"22":"22",
		"23":"23",
		"24":"24",
		"25":"25",
		"26":"26",
		"27":"27",
		"28":"28",
		"29":"29",
		"30":"30",
		"31":"31"
		};
/**
 * 高端柜台交易码
 */
CONSTANTS.COUNTER_TXCODE_MAP ={
		"Z900011":"认申购",
		"Z900012":"赎回",
		"Z900013":"修改分红方式",
		"Z900015":"撤单",
        "Z900048":"修改复购协议",
        "Z900054":"非交易过户申请",
        "Z900063":"修改资金回款",
        "Z900065":"股权份额转让维护",
        "Z900068":"股权认缴调整"
};

/**
 * 股权份额转让业务码
 */
CONSTANTS.OWNERSHIP_TX_CODES_MAP = {
    "":"全部",
    "1142":"强赎",
    "1144":"强增",
    "1145":"强减",
    "1134":"非交易过户转入",
    "1135":"非交易过户转出",
};

/**
 * 股权份额转让维护审核状态
 */
CONSTANTS.OWNERSHIP_CHECK_FLAG_MAP = {
    "0":"待维护",
    "1":"待审核",
    "2":"已审核"
};

/**
 * 柜台交易码
 */
CONSTANTS.COUNTER_FUND_TXCODE_MAP ={
		"Z910042":"申购",
        "Z310086":"买入",
		"Z910058":"认购",
		"Z910043":"赎回",
		"Z310088":"卖出",
		"Z910044":"修改分红方式",
		"Z910045":"交易自行撤单",
		"Z910046":"交易强行撤单",
		"Z910057":"基金转换",
		"Z910076":"转托管转入",
		"Z910075":"转托管转出",
        "Z930023": "修改资金回款",
        "Z910136": "自建组合赎回",
};

CONSTANTS.LCT_COUNTER_FUND_TXCODE_MAP ={
    "Z910097":"转托管转出"
};

/**
 * 份额类型shareType
 */
CONSTANTS.VOL_MERGE = "1";//份额合并
CONSTANTS.VOL_TRANS = "2";//份额迁移

/**
 * 柜台份额合并或迁移交易码
 */
CONSTANTS.MERGE_VOL_TXCODE = "Z910066";
CONSTANTS.TRANS_VOL_TXCODE = "Z910067";
CONSTANTS.ONLINE_TRANS_VOL_TXCODE = "Z910105";
CONSTANTS.QUERY_SUBMIT_APP_ORDER = "Z910110";
CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP ={
	"Z910066":"份额合并",
	"Z910067":"份额迁移",
	"Z910105":"线上换卡",
	"Z910110":"折扣修改"
};

/**
 * 柜台审核标识
 */
CONSTANTS.COUNTER_CHECK_FLAG_MAP = {
		"0":"待审核",
		"1":"审核通过",
		"2":"审核失败",
		"3":"驳回",
		"4":"作废"
};

/**
 * 柜台审核标识
 */
CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP = {
		"0":"待审核",
		"1":"审核通过",
		"2":"审核失败",
		"3":"审核驳回",
		"4":"作废",
        "5":" 材料复审退回，订单驳回"

};

CONSTANTS.COUNTER_APP_FLAG_MAP = {
		"0":"待申请",
		"1":"申请成功",
		"2":"申请失败",
		"3":"申请部分成功"
};


/**
 * 巨额赎回标识
 */
CONSTANTS.LARGE_REDM_FLAG_MAP = {
		"0":"不顺延",
		"1":"顺延"
};
/**
 * 特殊交易标记,用于异常订单标记
 */
CONSTANTS.UNUSUAL_TRANS_TYPE_MAP = {
		"0":"正常交易-赎回出款",
		"1":"异常交易-赎回不出款",
		"2":"特殊补单交易",
};
CONSTANTS.IS_NOTRADE_TRANSFER = {
    "0":"否",
    "1":"是"
};

CONSTANTS.FUND_UNUSUAL_TRANS_TYPE_MAP = {
		"0":"否",
		"1":"是"
};

CONSTANTS.OWNERSHIP_RIGHT_TRANSFER_CHECK_FALG_MAP = {
    "0":"待维护",
    "1":"待审核",
    "2":"已审核"
};

/**
 * 私募-预约业务
 */
CONSTANTS.SIMU_BUY_STATUS_MAP = {
	"S":"认购",
    "P":"申购"
};
/**
 * 私募无纸化预约状态
 */
CONSTANTS.SIMU_STATUS_MAP = {
	 "NORMAL":"正常",
	 "FINISH":"完成",
	 "CANCEL":"取消",
	 "REJECT":"驳回",
	 "OUTOFDATE":"过期"
};

/**
 * 私募无纸化预约状态（新）
 */
CONSTANTS.NO_PAPER_STATE_MAP = {
	 "1":"未确认",
	 "2":"已确认",
	 "3":"驳回",
	 "4":"审核通过"
};

/**
 * 预约单状态(新)
 */
CONSTANTS.PREBOOK_STATE_MAP = {
	"1":"未确认",
	"2":"已确认",
	"4":"已撤销",
	 "5":"打回"
},

/**
 * 私募无纸化确认状态
 */
CONSTANTS.SIMU_CONFIRM_STATUS_MAP={
	"CONFIRM":"已确认",
	"UN_CONFIRM":"未确认",
};
/**
 * 性别
 */
CONSTANTS.SEX_MAP = {
	"0":"女",
	"1":"男"
};

/**
 * 转托管业务类型 
 */
CONSTANTS.TRANSFER_TUBE_TYPE_MAP =  {"1":"跨市场","2":"场外跨销售机构"};

/**
 * 对方销售人代码
 */
CONSTANTS.T_SELLER_CODE_MAP =  {"101":"101-上海","102":"102-深圳"};

/**
 * 职业
 */
CONSTANTS.VOCATIONS_MAP={"01":"政府部门",
		"02":"教科文",
		"03":"金融",
		"04":"商贸",
		"05":"房地产",
		"06":"制造业",
		"07":"自由职业",
		"08":"其它"
};


CONSTANTS.SIMU_BANK_MAP = {"102":"工商银行",
		"105":"建设银行",
		"103":"农业银行",
		"308":"招商银行",
		"305":"民生银行",
		"309":"兴业银行",
		"403":"邮储银行",
		"307":"平安银行",
		"783":"平安银行",
		"106":"交通银行",
		"310":"浦发银行",
		"314":"上海银行",
		"317":"温州银行",
		"104":"中国银行",
		"112":"光大银行",
		"302":"中信银行",
		"304":"华夏银行",
		"306":"广发银行",
		"318":"渤海银行", 
		"332":"泰隆银行", 
		"333":"浙江稠州商业银行", 
		"334":"南昌银行", 
		"336":"星展银行", 
		"337":"广东华兴银行", 
		"338":"重庆三峡银行", 
		"339":"华侨银行", 
		"341":"江苏江阴农村商业银行", 
		"313":"长沙银行", 
		"321":"青岛银行", 
		"300":"常熟农村商业银行", 
		"324":"杭州银行", 
		"325":"秦皇岛银行", 
		"326":"汉口银行", 
		"327":"广东顺德农村商业银行", 
		"328":"四川省农村信用社", 
		"329":"长安银行", 
		"330":"兰州银行", 
		"331":"青海银行", 
		"340":"东亚银行", 
		"322":"永亨银行", 
		"319":"江苏银行", 
		"315":"北京银行", 
		"316":"宁波银行", 
		"320":"南京银行", 
		"323":"大连银行", 
		"342":"上海农村商业银行", 
		"514":"张家口市农村信用合作社"
};

CONSTANTS.LCT_BANK_MAP = {
	"888":"理财通虚拟卡"
};
/**
 * 币种集合
 */
CONSTANTS.CURRENCYS_MAP={"156":"人民币",
		"840":"美元",
		"344":"港元",
		"954":"欧元",
		"392":"日元",
		"826":"英镑",
		"250":"法郎",
		"280":"马克"
};

/**
 * 机构客户性质
 */
CONSTANTS.PROPERTY_MAP={"0":"国企",
		"1":"民营",
		"2":"合资",
		"3":"其他"};
/**
 * 机构客户资质
 */
CONSTANTS.QUALIFICATION_MAP={
		"1":"金融机构",
		"2":"金融机构产品",
		"3":"社会保障基金",
		"4":"企业年金等养老基金",
		"5":"慈善基金等社会公益基金",
		"6":"合格境外机构投资者（QFII）",
		"7":"人民币合格境外机构投资者（RQFII）",
		"0":"其他"
};
/**
 * 回款协议方式
 */
CONSTANTS.COLLECT_PROTOCOL_METHOD_MAP = {
		"1":"回款至银行卡（系统默认）",
	    "2":"回款至银行卡（用户选择）",
	    "3":"回款至储蓄罐（系统默认）",
	    "4":"回款至储蓄罐（用户选择）"	
};

CONSTANTS.SELECT_COLLECT_PROTOCOL_METHOD_MAP = {
		"4":"回款至储蓄罐（用户选择）",
	    "2":"回款至银行卡（用户选择）",
	    "3":"回款至储蓄罐（系统默认）",
	    
};
/**
 * 高端-柜台交易赎回回款方向
 */
CONSTANTS.COUNTEE_REDEEM_CAPITAL_FLAG = {
		 "0":"储蓄罐（人工选择）",
		 "1":"回银行卡（人工选择）",
		 "2":"回银行卡（协议默认）",
		 "3":"回储蓄罐（协议默认）"
};

/**
 * 公募-柜台交易赎回回款方向
 * 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
 */
CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG = {
		 "0":"回款至银行卡（协议默认）",
		 "1":"回款至储蓄罐（协议默认）",
		 "2":"回款至银行卡（人工选择）",
		 "3":"回款至储蓄罐（人工选择）"
};

CONSTANTS.GM_COUNTEE_SUSTOM_RATIO_TYPE = {
    "1":"按比例赎回",
    "2":"自定义赎回"
};

/**
 * 协议类型
 */
CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP = {
		 "1":"普通公募协议",
		 "2":"普通公募智能投顾协议",
		 "3":"暴力定投协议",
		 "4":"高端公募协议",
		 "5":"推荐组合",
		 "6":"用户自定义组合",
		 "7":"普通公募定投协议",
	     "8":"定期协议",
	     "9":"货币组合",
	     "10":"短债组合",
	     "11":"U定投协议",
	     "12":"税延养老协议",
	     "13":"止盈定投",
	     "14":"科创打新组合",
	     "15":"小目标组合",
	     "16":"南方双月宝",
	     "17":"交银稳稳的幸福",
	     "18":"投顾组合",
	     "19":"简七",
	     "20":"新增协议稳健组合",
	     "90":"储蓄罐",
	     "91":"资金"
};

/**
 * 产品通道
 */
CONSTANTS.PRODUCT_CHANNEL_MAP = {
	"3":"群济-私募",
	"4":"储蓄罐",
	"5":"好买公募",
	"6":"好买高端公募",
	"7":"TP私募"
};

/**
 * 产品类型
 */
CONSTANTS.PRODUCT_TYPE_MAP = {
	    "0": "股票型",
	    "1":"混合型",
	    "2":"债券型",
	    "3": "货币型",
	    "4": "QDII",
	    "5": "封闭式",
	    "6": "结构型",
	    "7":"一对多专户",
	    "8": "券商大集合",
	    "9": "券商小集合",
	    "A": "固定收益",
	    "B": "对冲型",
	    "C": "PE/VC",
	    "D": "其他",
	    "10": "FOF",
	    "11": "私募"
};

/**
 * 手续费计算方式
 */
CONSTANTS.FEE_CAL_MODE_MAP = {
		"0":"外扣",
		"1":"内扣"
};

/**
 * 购买业务类型
 */
CONSTANTS.BUY_BUYS_TYPE_MAP = {
		"0":"认购",
		"1":"申购"
};

/**
 * 赎回去向
 */
CONSTANTS.REDEEM_DIR_MAP = {
		"1":"全部回储蓄罐（人工选择）",
		"0":"全部回银行卡（人工选择）",
        "5":"全部回可用余额",
        "6":"部分留可用余额+剩余回银行卡",
        "7":"部分留可用余额+剩余回储蓄罐"
};

/**
 * 回款去向
 */
CONSTANTS.WITHDRAW_DIR_ALL_MAP = {
    "04":"回银行卡",
    "06":"回储蓄罐",
    "5":"全部回可用余额",
    "6":"部分留可用余额+剩余回银行卡",
    "7":"部分留可用余额+剩余回储蓄罐"
};

/**
 * 认申购-自划款-回款去向
 */
CONSTANTS.WITHDRAW_DIR_BUY_SELFDRAWING_MAP = {
    "04":"回银行卡",
    "5":"全部回可用余额",
    "6":"部分留可用余额+剩余回银行卡"
};

/**
 * 回款去向
 */
CONSTANTS.WITHDRAW_DIR_MAP = {
    "5":"全部回可用余额",
    "6":"部分留可用余额+剩余回银行卡",
    "7":"部分留可用余额+剩余回储蓄罐"
};

/**
 * 回可用余额备注选择
 */
CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP = {
    "0":"选系统中的基金",
    "1":"手工输入"
};

/**
 * 支付状态
 */
CONSTANTS.PAY_STATUS_MAP = {
		"0":"无需付款",
		"1":"未付款",
		"2":"付款中",
		"3":"部分成功",
		"4":"成功",
		"5":"失败"
},
/**
 * 是否用户选择撤单
 */
CONSTANTS.USER_CANCEL_FLAG_MAP = {
		"0":"否",
		"1":"是"
};

CONSTANTS.Product_channel_map = {
		"3":"群济私募",
		"5":"好买公募",
		"6":"高端公募"
};

CONSTANTS.ASSET_SCOPE = {
    "1" : "2000以下",
    "2" : "2000-20000",
    "3" : "20000以上"
}

/**
 * 默认交易时间
 */
CONSTANTS.DEFAULT_TRADE_TIME = '090000';

/**
 * 柜台开始交易时间
 */
CONSTANTS.HIGH_COUNTER_START_TIME = '090000';
/**
 * 柜台新的开始交易时间
 */
CONSTANTS.HIGH_COUNTER_START_TIME_NEW = '000001';

/**
 * 柜台结束交易时间
 */
CONSTANTS.HIGH_COUNTER_END_TIME = '145959';

/**
 * 全赎标识
 */
CONSTANTS.IS_ALL_REDEEM = '1';//是
CONSTANTS.NOT_ALL_REDEEM = '0';//否

/**
 * 是否全赎
 */
CONSTANTS.ALL_REDEEM_FLAG_MAP =  {"1":"是","0":"否"};

/**
 * 是否需要双录
 */
CONSTANTS.DOUBLE_NEED_FLAG_MAP =  {"1":"是","0":"否"};


/**
 * 双录状态
 */
CONSTANTS.DOUBLE_HANDLE_FLAG_MAP =  {"0":"无需双录", "1":"未双录","2":"已双录","3":"已处理待审核"};


/**
 * 是否首次实缴
 */
CONSTANTS.FIRST_PREID_MAP =  {"0":"否", "1":"是"};
/**
 *是或者否
 **/
CONSTANTS.YES_OR_NO_MAP = {"0":"否", "1":"是"};

/**
 * 复购意向来源
 */
CONSTANTS.REPURCHASE_SORUCE_MAP = {"0":"线上", "1":"线下","2":"无意向"}
/**
 * 在线材料审核结果
 */
CONSTANTS.ON_LINE_ORDER_ISQUAL_MAP = {"1":"合格","0":"不合格"};




/**
 * 复购下单标识
 */
CONSTANTS.REPURCHASE_ORDER_FLAG_MAP = {"0":"未下单", "1":"已下单", "2":"部分下单"};


/**
 * 定投类型
 */
CONSTANTS.SCHEDULE_TYPE_MAP = {
		"1":"定存",
		"2":"定取",
		"3":"保险预约赎回",
		"4":"全球赢+定投",
		"5":"好买智投",
		"6":"公募定投",
		"7":"推荐组合定投",
		"8":"券商资管预约申购",
		"9":"券商资管预约赎回",
		"10":"理财型基金预约赎回",
		"13":"货币赢+定投",
		"14":"安鑫赢+定投",
		"15":"潜龙投资计划",
		"18":"止盈定投"
};
/**
 * 定投合约状态
 */
CONSTANTS.SCHEDULE_STATUS_MAP = {
		"1":"新建",
		"2":"正常",
		"3":"删除",
		"4":"暂停",
		"5":"恢复"
};

CONSTANTS.SCHE_PLAN_STATUS_MAP = {
		"1":"正常",
		"2":"暂停",
		"3":"终止"
};

CONSTANTS.MATERIAL_TYPE = {
    "1":"身份证件",
    "2":"银行凭证",
    "3":"不能提供凭证原因"
};

CONSTANTS.UPLOAD_METHOD = {
    "1":"OCR识别上传",
    "2":"拍照上传"
};

CONSTANTS.CAPTIAL_ORDER_PAY_STATUS_MAP = {
    "0":"成功",
    "1":"失败"
};

/**
 * 获取Map 值
 * @param map
 * @param defValve  默认返回值
 */
function getMapValue(map,nameStr,defValve){
	
	var returnValue = "";
	if(isEmpty(defValve)){
		returnValue =  nameStr;
	}else{
		returnValue =  defValve;
	}
	$.each(map,function(name,value){
		if(nameStr == name){
			returnValue =  value;
			return false;
		}
	});
	
	return  returnValue;
}

/**
 * 赋值
 * @param name
 * @param val
 */
function setValue(obj,name, val){
    if(!isEmpty(val)){
        var htmlType = obj.find("[name='"+name+"']").attr("type");
        if(htmlType == "text" || htmlType == "textarea" || htmlType == "select-one" || htmlType == "select" || htmlType == "hidden" || htmlType == "button"){
        	obj.find("[name='"+name+"']").val(val);
        }else if(htmlType == "radio"){
        	obj.find("input[type=radio][name='"+name+"'][value='"+val+"']").attr("checked",true);
        }else if(htmlType == "checkbox"){
            var vals = val.split(",");
            for(var i=0; i < vals.length; i++){
            	obj.find("input[type=checkbox][name='"+name+"'][value='"+vals[i]+"']").attr("checked",true);
            }
        }else{
        	obj.find("[name='"+name+"']").val(val);
        }
    }
};


/**
* 
*
*字符串为空校验
*/

function isEmpty(str){
	return (str == null || str === "" || str == "undefined" || str == "null");
};

/**
 * 判断是否空的数组集合
 * @param arr
 */
function isEmptyList(arr){
	if(arr == null || arr.length <=0){
		return true;
	}else{
		return false;
	}
};

/**
 * 将6位hhmmss 格式时间字符串格式化为hh:mm:ss
 * timeStr HHMMSS
 * @param timeStr
 */
function formatTime(timeStr){
	if(isEmpty(timeStr)){
		return timeStr;
	}else if(timeStr.length == 6){
		return timeStr.substring(0,2) +":"+timeStr.substring(2,4) +":"+timeStr.substring(4,6);
	}else {
		return timeStr;
	}
}

/**
 * 将8位yyyyMMdd 格式时间字符串格式化为yyyy-MM-dd
 * dataStr yyyy-MM-dd
 * @param dateStr
 */
function formatDate(dateStr,defaultVal){
	if(isEmpty(defaultVal)){
		defaultVal = "";
	}
	if(isEmpty(dateStr)){
		return defaultVal;
	}else if(dateStr.length == 8){
		return dateStr.substring(0,4) +"-"+dateStr.substring(4,6) +"-"+dateStr.substring(6,8);
	}else {
		return dateStr;
	}
}


/**
 * 动态生成select options
 * @param maps  options 集合
 * @param id   selectId
 */
function selectOptions(maps,obj,defaultSelect,defvalue,defText){
	$(obj).empty();
	var appendHtml = selectOptionsHtml(maps,defaultSelect,defvalue,defText);
	obj.append(appendHtml);
	
};

/**
 * 动态生成select options html
 * @param maps  options 集合
 * @defaultSelect 默认选中值
 * @param id   selectId
 */
function selectOptionsHtml(maps,defaultSelect,defvalue,defText){
	var appendHtml = '';
	if(isEmpty(defvalue)){
		appendHtml = '<option value="">请选择</option>';
	}else{
		appendHtml = '<option value=\"'+defvalue+'\">'+defText+'</option>';
	}
		
	$.each(maps,function(key,value){
		if(!isEmpty(key) && !isEmpty(value)){
			if(!isEmpty(defaultSelect) && defaultSelect == key){
				appendHtml +='<option value=\"'+key+'\"  selected>'+value+'</option>';
			}else{
				appendHtml +='<option value=\"'+key+'\" >'+value+'</option>';
			}
			
		}
		
	});
	
	return appendHtml;
};


/**
 * 显示提示信息
 */
function showMsg(text){
	alert(text);
};

/**
 * 关闭同类型的弹层
 * @param type all-所有；dialog-有页面层;iframe-关闭所有的iframe层;loading-关闭加载层;tips-关闭所有的tips层  
 */
function layerall_close(type){
	if(isEmpty(type)){
		layer.closeAll(); //关闭所有弹窗
	}else{
		layer.closeAll(type); //关闭信弹窗
	}
	
};

/**
 * 提示框
 * @param msg
 * @param timeOut    
 * @param iconIndex  图标
 */
function layer_tip(msg,timeOut,iconIndex){
	if(isEmpty(timeOut)){
		timeOut = 2000;
	}
	if(isEmpty(iconIndex)){
		iconIndex = 6;
	}
	layer.msg(msg, {
		time:timeOut,
		icon: iconIndex});
};

/**
 * 对象为空显示默认值
 * @param obj
 * @param defaultValue
 * @returns
 */
function formatData(str,defaultValue){
	if(isEmpty(defaultValue)){
		defaultValue = "";
	}
	if(isEmpty(str)){
		return defaultValue;
	}else{
		return str;
	}
	
	
};
/**
 * 金额格式转换 0.00
 * @param obj
 * @param defaultValue
 * @returns {String}
 */
function formatAmount (s,defaultVal){
	if(isEmpty(defaultVal)){
		defaultVal = "--";
	}
	if(isEmpty(s)){
		return defaultVal;
	}
    if(/[^0-9\.-]/.test(s)) {
    	return s;
    }
    
    if(!isNaN(s)) {
    	s = s.toString();
    	
    }
    
    s=s.replace(/^((-)?\d*)$/,"$1.");
    s=(s+"00").replace(/(\d*\.\d\d)\d*/,"$1");
    s=s.replace(".",",");
    var re=/(\d)(\d{3},)/;
    while(re.test(s))
        s=s.replace(re,"$1,$2");
    s=s.replace(/,(\d\d)$/,".$1");
    return s.replace(/^\./,"0.");
};
/**
 * format 净值
 */
function formatNav(s,defaultVal){
	if(isEmpty(defaultVal)){
		defaultVal = "--";
	}
	if(isEmpty(s)){
		return defaultVal;
	}
    if(/[^0-9\.-]/.test(s)) {
    	return s;
    }
    
    if(!isNaN(s)) {
    	s = s.toString();
    }
    
    s=s.replace(/^(\d*)$/,"$1.");
    s=(s+"0000").replace(/(\d*\.\d\d\d\d)\d*/,"$1");
    s=s.replace(".",",");
    var re=/(\d)(\d{5},)/;
    while(re.test(s))
        s=s.replace(re,"$1,$2");
    s=s.replace(/,(\d\d\d\d)$/,".$1");
    return s.replace(/^\./,"0.");
};
/**
 * 小数转百分比
 * @param s
 */
function formatPercent(s,defaultVal,precision){
	if(isEmpty(defaultVal)){
		defaultVal = "";
	}
	if(isEmpty(s)){
		return defaultVal;
	}
	if(/[^0-9\.-]/.test(s)) {
    	return s;
    }
	
	if(isNaN(s)){
		return s.toString();
	}else{
		if(!isNaN(precision)){
			s =(s*100).toFixed(precision);
		}else{
			s = s*100;
		}
		return s+"%";
	}
};




Date.prototype.Format = function (fmt) {
	  var o = {
	    "y+": this.getFullYear(),
	    "M+": this.getMonth() + 1,                 //月份
	    "d+": this.getDate(),                    //日
	    "h+": this.getHours(),                   //小时
	    "m+": this.getMinutes(),                 //分
	    "s+": this.getSeconds(),                 //秒
	    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
	    "S+": this.getMilliseconds()             //毫秒
	  };
	  for (var k in o) {
	    if (new RegExp("(" + k + ")").test(fmt)){
	      if(k == "y+"){
	        fmt = fmt.replace(RegExp.$1, ("" + o[k]).substr(4 - RegExp.$1.length));
	      }
	      else if(k=="S+"){
	        var lens = RegExp.$1.length;
	        lens = lens==1?3:lens;
	        fmt = fmt.replace(RegExp.$1, ("00" + o[k]).substr(("" + o[k]).length - 1,lens));
	      }
	      else{
	        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
	      }
	    }
	  }
	  return fmt;
	};
	
	/**
	 * 格式化日期
	 * @param date
	 * @param fmt
	 * @returns {String}
	 */
	function formatDateToStr(dateStr,fmt){
		if(isEmpty(dateStr)){
			return "";
		}
		var fmtDate = new Date(dateStr);
		if(fmtDate instanceof  Date){
			return fmtDate.Format(fmt);
		}else {
			return dateStr;
		}
		
	};
/**
 * 禁用按钮
 * @param targetObj
 */
function disableBtn(btn){
	if(btn == 'undefined' || btn == null){
		return false;
	}
	btn.addClass('disabled');
	btn.attr({"disabled":"disabled"});
};
/**
 * 启用按钮
 * @param btn
 */
function enableBtn(btn){
	if(btn == 'undefined' || btn == null){
		return false;
	}
	btn.removeClass('disabled');
	btn.removeAttr("disabled");
};

$.fn.spin = function(opts) {
	  this.each(function() {
	    var $this = $(this),
	        data = $this.data();

	    if (data.spinner) {
	      data.spinner.stop();
	      delete data.spinner;
	    }
	    if (opts !== false) {
	      data.spinner = new Spinner($.extend({color: $this.css('color')}, opts)).spin(this);
	    }
	  });
	  return this;
	};
	

/**
 * 加载中
 */
function loadingStart(targetObj,parentObj){
	var opts = {
			  lines: 13, // The number of lines to draw
			  length: 7, // The length of each line
			  width: 4, // The line thickness
			  radius: 10, // The radius of the inner circle
			  corners: 1, // Corner roundness (0..1)
			  rotate: 0, // The rotation offset
			  color: '#000', // #rgb or #rrggbb
			  speed: 1, // Rounds per second
			  trail: 60, // Afterglow percentage
			  shadow: false, // Whether to render a shadow
			  hwaccel: false, // Whether to use hardware acceleration
			  className: 'spinner', // The CSS class to assign to the spinner
			  zIndex: 2e9, // The z-index (defaults to 2000000000)
			  top: '50%', // Top position relative to parent in px
			  left: '50%' // Left position relative to parent in px
			};
	var spaner = new Spinner(opts).spin(targetObj);
	parentObj.show();
	return spaner;
}
/**
 * 隐藏加载中效果
 */
function loadingStop(spaner,targetObj){
	spaner.spin();
	targetObj.hide();
}

/**
 * 分页
 * @param uri  查询uri
 * @param curr 当前页
 * @@param processView 分页数据渲染
 */
function paging(uri, curr,processView,pageView,completeFun) {
	//加载中
	var index = layer.load();
	if(isEmpty(pageView)){
		pageView = "pageView";
	}
	$.ajax({
		url : uri,
		type:'post',
		cache : false,
		dataType : 'json',
		data:{"page" : curr || 1,"pageSize":10,"ajax" : "true"},
		success : function(data) {
			var code = data.code || "";
			var desc = data.desc || "";
			var detail = data.detail || {};
			
			if(CONSTANTS.RETURN_CODE.StatusCode.SUCCESS == code){
			// 显示分页
			laypage({
				cont : pageView, // 容器。值支持id名、原生dom对象，jquery对象。【如该容器为】：<div id="page1"></div>
				pages : detail.totalPage, // 通过后台拿到的总页数
				curr : curr || 1, // 当前页
				skin: 'molv',
				first: 1, //将首页显示为数字1,。若不显示，设置false即可
				last: detail.totalPage, //将尾页显示为总页数。若不显示，设置false即可
				skip: true, //是否开启跳页
				jump : function(obj, first) { // 触发分页后的回调
					if (!first) { // 点击跳页触发函数自身，并传递当前页：obj.curr
						paging(uri, obj.curr,processView);
					}
					obj.pages = obj.last = detail.totalPage; //重新获取总页数，一般不用写  
					
					processView(detail,detail.staticsData);
					
				}
			});
			}else {
				showMsg(desc+'('+code+')');
			}
		},
		commplete:function(){
			//关闭加载中
			layer.close(index);
			if($.isfunction(completeFun)){
				completeFun();
			}
		},
		error : function() {
			layer.close(index);
			showMsg("系统异常");
		}
	});
	
};


/**
 * ajax分页
 * @param uri  查询uri
 * @param curr 当前页
 * @@param processView 分页数据渲染
 */
function ajaxPaging(uri, reqParamers,processView,pageView,completeFun) {
	//加载中
	var index = layer.load();
	var reqdata = reqParamers || {};
	reqdata.ajax = "true";
	if(isEmpty(pageView)){
		pageView = "pageView";
	}
	$.ajax({
		url : uri,
		type:'post',
		cache : false,
		dataType : 'json',
		data:reqdata,
		success : function(data) {
			var code = data.code || "";
			var desc = data.desc || "";
			var detail = data.detail || {};
			if(CONSTANTS.RETURN_CODE.StatusCode.SUCCESS == code){
				// 显示分页
				laypage({
					cont : pageView, // 容器。值支持id名、原生dom对象，jquery对象。【如该容器为】：<div id="page1"></div>
					pages : detail.totalPage, // 通过后台拿到的总页数
					curr : detail.pageNum || 1, // 当前页
					skin: 'molv',
					first: 1, //将首页显示为数字1,。若不显示，设置false即可
					last: detail.totalPage, //将尾页显示为总页数。若不显示，设置false即可
					skip: true, //是否开启跳页
					jump : function(obj, first) { // 触发分页后的回调
						if (!first) { // 点击跳页触发函数自身，并传递当前页：obj.curr
							reqParamers.page = obj.curr;
							ajaxPaging(uri, reqParamers,processView);
						}
						obj.pages = obj.last = detail.totalPage; //重新获取总页数，一般不用写  
						processView(detail,detail.staticsData);
					}
				});
			}else {
				showMsg(desc+'('+code+')');
			}
		},
		complete:function(){
			//关闭加载中
			layer.close(index);
			if($.isFunction(completeFun)){
				completeFun();
			}
			
		},
		error : function() {
			layer.close(index);
			showMsg("系统异常");
		}
	});
	
};

/**
* 异步请求和返回
* paramters 请求参数
* processData 处理函数
*/
function ajaxReqAndResponse(paramters,obj,callBack,completeFun){
	var uri= paramters.uri;
	var method = paramters.method || "";
	var isAsync =  paramters.isAsync || false;
	var reqparamters = paramters.reqparamters || {};
	var timeout = paramters.timeout || '';
	
	if(isEmpty(timeout)){
		timeout = 1000 * 60 *10;
	}
	reqparamters.ajax = "true";
	if(isEmpty(method)){
		method = "post";
	}
	//默认不需要加载中效果
	var needLoad = paramters.needLoad || false;
	var index ='';
	if(needLoad){
		index = layer.load(0,{shade: [0.1,'#fff']}); 
	}
	
	$.ajax({
		url : uri,
		type:"post",
		async: isAsync,
		cache : false,
		dataType : 'json',
		timeout:timeout,
		data:reqparamters,
		success : function(data) {
			if(needLoad){
				layer.close(index);
			}
			var code = data.code || "";
			var desc = data.desc || "";
			var detail = data.detail || {};
			if(CONSTANTS.RETURN_CODE.StatusCode.SUCCESS == code){
				callBack(obj,detail);
			}else {
				showMsg(desc+'('+code+')');
			}
			
		},
		error : function(XMLHttpRequest, status, error) {
			if(needLoad){
				layer.close(index);
			}
			if(status == 'timeout'){
				showMsg("请求超时。处理超过"+(timeout/(1000*60)) +'分钟');
			}else{
				showMsg('系统异常');
			}
			
		},complete:function(){
			if(needLoad){
				layer.close(index); 
			}
			
			if($.isFunction(completeFun)){
				completeFun();
			}
			
		}
	});
};



/**
 * 异步请求和处理
 * paramters 请求参数
 * processData 处理函数
 */
function ajaxReqAndProcess(paramters,processData,completeFun){
	var uri= paramters.uri;
	var reqparamters = paramters.reqparamters || {};
	var isAsync =  paramters.isAsync || false;
	reqparamters.ajax = "true";
	var timeout = paramters.timeout || '';
	if(isEmpty(timeout)){
		timeout = 1000 * 60 *10;
	}
	//默认不需要加载中效果
	var needLoad = paramters.needLoad || false;
	var index ='';
	if(needLoad){
		index = layer.load(0,{shade: [0.1,'#fff']}); 
	}
	$.ajax({
		url : uri,
		type:"post",
		async: isAsync,
		cache : false,
		dataType : 'json',
		timeout:timeout,
		data:reqparamters,
		success : function(data) {
			var code = data.code || "";
			var desc = data.desc || "";
			var detail = data.detail || {};
			if(CONSTANTS.RETURN_CODE.StatusCode.SUCCESS == code){
				processData(detail);
			}else {
				showMsg(desc+'('+code+')');
			}
			
		},
		error : function(XMLHttpRequest, status, error) {
			if(needLoad){
				layer.close(index);
			}
			if(status == 'timeout'){
				showMsg("请求超时。处理超过"+(timeout/(1000*60)) +'分钟');
			}else{
				showMsg('系统异常');
			}
			
		},
		complete:function(){
			if(needLoad){
				layer.close(index);
			}
			if($.isFunction(completeFun)){
				completeFun();
			}
			
		}
	});
};


/**
* 异步请求和处理
* paramters 请求参数
* processData 处理函数
*/
function ajaxReqAndCallBack(paramters,callBack,completeFun){
	var uri= paramters.uri;
	var method = paramters.method || "";
	var isAsync =  paramters.isAsync || false;
	var timeout = paramters.timeout || '';
	if(isEmpty(timeout)){
		timeout = 1000 * 60 *10;
	}
	var reqparamters = paramters.reqparamters || {};
	if(isEmpty(method)){
		method = "post";
	}
	var needLoad = paramters.needLoad || false;
	if(needLoad){
		index = layer.load(0,{shade: [0.1,'#fff']}); 
	}
	$.ajax({
		url : uri,
		type:"post",
		async: isAsync,
		cache : false,
		dataType : 'json',
		timeout:timeout,
		data:reqparamters,
		success : function(data) {
			if(needLoad){
				layer.close(index);
			}
			var code = data.code || "";
			var desc = data.desc || "";
			var detail = data.detail ||{};
			if(CONSTANTS.RETURN_CODE.StatusCode.SUCCESS == code){
				callBack(detail);
			}else {
				showMsg(desc+'('+code+')');
			}
		},
		error : function(XMLHttpRequest, status, error) {
			if(needLoad){
				layer.close(index);
			}
			if(status == 'timeout'){
				showMsg("请求超时。处理超过"+(timeout/(1000*60)) +'分钟');
			}else{
				showMsg('系统异常');
			}
			
		},complete:function(){
			if(needLoad){
				layer.close(index);
			}
			if($.isFunction(completeFun)){
				completeFun();
			}
		}
	});
};

/**
* 异步请求和处理
* paramters 请求参数
* processData 处理函数
*/
function ajaxAndCallBack(paramters,callBack,completeFun){
	var uri= paramters.uri;
	var method = paramters.method || "";
	var isAsync =  paramters.isAsync || false;
	var timeout = paramters.timeout || '';
	if(isEmpty(timeout)){
		timeout = 1000 * 60 *10;
	}
	var reqparamters = paramters.reqparamters || {};
	if(isEmpty(method)){
		method = "post";
	}
	var needLoad = paramters.needLoad || false;
	if(needLoad){
		index = layer.load(0,{shade: [0.1,'#fff']}); 
	}
	$.ajax({
		url : uri,
		type:"post",
		async: isAsync,
		cache : false,
		dataType : 'json',
		timeout:timeout,
		data:reqparamters,
		success : function(data) {
			if(needLoad){
				layer.close(index);
			}
			callBack(data);
		},
		error : function(XMLHttpRequest, status, error) {
			if(needLoad){
				layer.close(index);
			}
			if(status == 'timeout'){
				showMsg("请求超时。处理超过"+(timeout/(1000*60)) +'分钟');
			}else{
				showMsg('系统异常');
			}
			
		},
		complete:function(){
			if(needLoad){
				layer.close(index);
			}
			if($.isFunction(completeFun)){
				completeFun();
			}
			
		}
	});
};


function isSucc(code){
	return CONSTANTS.RETURN_CODE.StatusCode.SUCCESS == code;
}




