/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.cmd;

import java.util.List;

/**
 * @className AuditingOrderFileCmd
 * @description
 * <AUTHOR>
 * @date 2019/5/27 13:53
 */
public class AuditingOrderFileCmd {
    /**
     * 业务订单ID
     */
    private String orderid;
    /**
     * 外部订单ID
     */
    private String forderid;
    /**
     * 审核意见
     */
    private String curcheckdes;
    /**
     * 最新审核状态
     */
    private String curstat;
    /**
     * 归档编号
     */
    private String docno;
    /**
     * 审核级别
     */
    private String checklevel;
    /**
     * 文件列表
     */
    private List<FileinfoCmd> fileinfolist;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getForderid() {
        return forderid;
    }

    public void setForderid(String forderid) {
        this.forderid = forderid;
    }

    public String getCurcheckdes() {
        return curcheckdes;
    }

    public void setCurcheckdes(String curcheckdes) {
        this.curcheckdes = curcheckdes;
    }

    public String getCurstat() {
        return curstat;
    }

    public void setCurstat(String curstat) {
        this.curstat = curstat;
    }

    public String getDocno() {
        return docno;
    }

    public void setDocno(String docno) {
        this.docno = docno;
    }

    public List<FileinfoCmd> getFileinfolist() {
        return fileinfolist;
    }

    public void setFileinfolist(List<FileinfoCmd> fileinfolist) {
        this.fileinfolist = fileinfolist;
    }

    public String getChecklevel() {
        return checklevel;
    }

    public void setChecklevel(String checklevel) {
        this.checklevel = checklevel;
    }
}
