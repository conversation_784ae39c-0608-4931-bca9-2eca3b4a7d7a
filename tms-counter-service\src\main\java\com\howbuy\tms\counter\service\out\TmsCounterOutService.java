/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.service.out;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileResponse;
import com.howbuy.common.page.Page;
import com.howbuy.common.page.PageResult;
import com.howbuy.tms.batch.facade.enums.WorkdayTypeEnum;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayResponse;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querybindcustbankcard.QueryBindCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundTaInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryportfolioproduct.bean.FundTAInfo;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.WorkDayInfo;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.CustomerAppointmentInfoRespDto;
import com.howbuy.tms.counter.dto.FeeDto;
import com.howbuy.tms.counter.dto.FeeReqDto;
import com.howbuy.tms.counter.dto.QueryCustTransInfoDto;
import com.howbuy.tms.counter.dto.QueryOrderFileDto;
import com.howbuy.tms.counter.dto.QueryPreBookListReqDto;
import com.howbuy.tms.counter.dto.QueryPreBookListRespDto;
import com.howbuy.tms.counter.dto.QueryRegularProductDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoResponse;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListRequest;

/**
 * @description:(中台柜台引用外部服务)
 * <AUTHOR>
 * @date 2017年4月12日 上午10:33:53
 * @since JDK 1.6
 */
public interface TmsCounterOutService {

    /**
     * queryAllCustInfo:(查询客户信息)
     *
     * @param txAcctNo
     * @param disCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年8月21日 上午10:34:40
     */
     QueryCustInfoResult queryCustInfo(String txAcctNo, String disCode);

    /**
     * queryAllCustInfo:(查询客户信息-柜台)
     * 
     * @param txAcctNo
     * @param disCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月12日 上午10:34:40
     */
     QueryCustInfoAndTxAcctForCounterResult queryAllCustInfo(String txAcctNo, String disCode) throws Exception;

    /**
     * 
     * queryCustBanks:(查询客户持有银行卡)
     * 
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年4月12日 上午10:34:54
     */
     List<QueryBindCustBankCardResult> queryCustBanks(String txAcctNo, String disCode, String outletCode);

    /**
     * 查询所有银行卡信息
     * queryAllBankCardInfo:查询所有银行卡信息
     * @param txAcctNo
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2022年4月15日 下午5:51:39
     */
     QueryAllBankCardInfoResult queryAllBankCardInfo(String txAcctNo, String disCode);
    
    
    /**
     * 查询所有银行卡信息
     * queryAllBankCardInfo:查询所有银行卡信息 (换卡后注销的卡不能查到)
     * @param txAcctNo
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2022年4月15日 下午5:51:39
     */
     QueryAllBankCardInfoResult queryAllBankCardInfoSwitch(String txAcctNo, String disCode);
    
    /**
     * 
     * getHighFundInfo:(查询高端基金信息)
     * 
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年4月12日 下午2:33:41
     */
     HighProductBaseInfoBean getHighFundInfo(String fundCode);

    /**
     * 
     * getFundNavInfo:(查询基金净值信息)
     * 
     * @param fundCode
     * @param appDt
     * @param appTm
     * @return
     * <AUTHOR>
     * @date 2017年5月10日 下午8:15:05
     */
     FundInfoAndNavBean getFundNavInfo(String fundCode, String appDt, String appTm);

    /**
     * 
     * getFundNavInfo:(查询基金净值信息)
     * 
     * @param fundCode
     * @param tradeDt
     * @return
     * <AUTHOR>
     * @date 2017年5月10日 下午8:10:30
     */
    FundInfoAndNavBean getFundNavInfo(String fundCode, String tradeDt);

    /***
     * 
     * getFundFeeRateByAmt:(查询产品购买费率)
     * 
     * @param midProductId
     * @param busiCode
     * @param invstType
     * @param shareClass
     * @param appAmt
     * @return
     * <AUTHOR>
     * @date 2017年9月15日 下午3:12:09
     */
    FundProductFeeRateBean getFundFeeRateByAmt(String midProductId, String busiCode, String invstType, String shareClass, BigDecimal appAmt);

    /**
     * 
     * getMBusiCode:(获取中台业务码)
     * 
     * @param custInfoDto
     * @param fundInfoAndNavBean
     * @param workDay
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月20日 下午6:26:32
     */
    String getMBusiCode(CustInfoDto custInfoDto, FundInfoAndNavBean fundInfoAndNavBean, String workDay) throws Exception;

    /**
     * 
     * getCurrTaTradeDt:(查询交易日)
     * 
     * @param appDt
     * @param appTm
     * @return
     * <AUTHOR>
     * @date 2017年4月12日 下午2:43:34
     */
    String getCurrTaTradeDt(String appDt, String appTm);
    /**
     *
     * getCurrTaTradeDt:(查询交易日)
     *
     * @return
     * <AUTHOR>
     * @date 2017年4月12日 下午2:43:34
     */
    String getCurrTaTradeDt();
    
    /**
     * 
    * @Title: getTaTradeDt 
    * @Description: 查询TA工作日
    * @param @return 参数说明 
    * @return WorkDayInfo 返回类型 
    * @auth yanming.gu
    * @date 2021年3月31日
     */
    WorkDayInfo getTaTradeDt();

    /***
     * 
     * queryAppointmentInfo:(查询客户预约信息)
     * 
     * @param parameter
     * @param cpage
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年6月29日 上午10:49:33
     */
    CustomerAppointmentInfoRespDto queryAppointmentInfo(Map<String, Object> parameter, Page cpage) throws Exception;

    /**
     * 
     * queryAppointmentInfo:(查询预约信息)
     * @param queryRequest
     * @param cpage
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月11日 下午4:42:48
     */
    CustomerAppointmentInfoRespDto queryAppointmentInfo(QueryPreBookListRequest queryRequest, Page cpage) throws Exception;
    /***
     * 
     * calFundBuyFee:(计算支付金额（含费）以及原始手续费)
     * 
     * @param feeReqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月18日 上午9:43:42
     */
    FeeDto calFundBuyFee(FeeReqDto feeReqDto) throws Exception;

    /***
     * 
     * getMBusiCode:(获取中台业务码)
     * 
     * @param custInfoDto
     * @param fundCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月18日 下午3:56:57
     */
    String getMBusiCode(CustInfoDto custInfoDto, String fundCode, String workDay) throws Exception;

    /**
     * 
     * changeFinaDirect:(修改回款方式)
     * 
     * @param custNo
     * @param finaDirect
     * <AUTHOR>
     * @date 2017年11月9日 下午3:40:59
     */
    void changeFinaDirect(String custNo, String finaDirect, DisInfoDto disInfoDto);



    /**
     * 
     * queryDefaultCnaps:(查询联行号)
     * 
     * @param bankCode
     * @param provCode
     * @param cityCode
     * @return
     * <AUTHOR>
     * @date 2018年1月4日 下午3:22:25
     */
    String queryDefaultCnaps(String bankCode, String provCode, String cityCode);


    /**
     * 
     * queryPreBookList:(查询预约列表)
     * @param queryPreBookListReqDto
     * @return
     * <AUTHOR>
     * @date 2018年1月18日 下午5:42:58
     */
    QueryPreBookListRespDto queryPreBookList(QueryPreBookListReqDto queryPreBookListReqDto) throws Exception;
    
    /**
     * 
     * queryHboneNoByTxAccountNo:(根据客户号查询一帐通号)
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年1月24日 下午4:54:48
     */
    String queryHboneNoByTxAccountNo(String txAcctNo);
    
    /**
     * 
     * @Description 根据一帐通号查询客户号
     * 
     * @param hboneNo
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2019/5/31 14:47
     **/
    String queryTxAccountNoByHboneNo(String hboneNo);

    /**
     * 
     * queryCustTransInfo:(查询代理信息)
     * @param txAcctNo
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月12日 下午8:00:46
     */
    QueryCustTransInfoDto queryCustTransInfo(String txAcctNo, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryAccKycInfo:查询账户中心kyc
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年8月10日 下午2:46:48
     */
    QueryAccKycInfoResult queryAccKycInfo(String txAcctNo);
    
    /**
     *
     * @description
     * <AUTHOR>
     * @date 21/6/2018 10:48 AM
     * @param productId 产品代码
     * @return com.howbuy.tms.counter.dto.QueryRegularProductDto
     */
    QueryRegularProductDto queryRegularProductInfo(String productId, String taDate);
    
    /**
     * 
     * queryFundTaInfo:(查询产品通道的所有TA信息)
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年12月14日 下午1:55:47
     */
    List<FundTAInfo> queryFundTaInfo(String productChannel);
   
   /**
    * 
    * getFundTaInfoListByPage:(分页查询基金TA信息)
    * @param productChannel
    * @param fundCode
    * @param fundAttr
    * @return
    * <AUTHOR>
    * @date 2018年12月15日 下午3:57:02
    */
   PageResult<FundTaInfoBean> getFundTaInfoListByPage(String productChannel, String fundCode, String fundAttr, int pageNum, int pageSize);
   
   /**
    * 
    * @Description 查询CRM线上资料
    * 
    * @param queryContext
    * @Param checkNode 当前审核节点
    * @return com.howbuy.tms.counter.dto.QueryOrderFileDto
    * <AUTHOR>
    * @Date 2019/6/4 10:09
    **/
   QueryOrderFileDto queryOrderFile(QueryOrderFileContext queryContext, String checkNode);

    /**
     * 用户资料作废
     * @param materialId 资料id
     */
   void cancelFile( String materialId);

   void auditingFile(OperatorInfoCmd operatorInfoCmd, AuditingOrderFileCmd auditingOrderFileCmd, String dealAppNo);

    /**
     * 校验资料订单状态
     * @param cmd
     */
    void validateOrderFileStatus(AuditingOrderFileCmd cmd);
    
   /**
    * 
    * @Description 校验材料订单是否存在
    * 
    * @param queryContext
    * @param checkNode
    * @return void
    * <AUTHOR>
    * @Date 2019/6/19 11:14
    **/
   void  validateOrderFileExist(QueryOrderFileContext queryContext, String checkNode);

    /**
     *
     * @Description 校验材料是否全部通过
     *
     * @param cmd
     * @return void
     * <AUTHOR>
     * @Date 2019/6/21 17:32
     **/
    void validateIsAllPass(AuditingOrderFileCmd cmd);

    /**
     * 查询银行卡信息
     * @param txAcctNo
     * @param bankAcct
     * @return
     */
    QueryBankCardInfoResponse queryBankCardInfo(String txAcctNo, final String bankAcct);

    /**
     * 查询银行卡信息
     * @param txAcctNo
     * @param bankAcctDigest 银行卡摘要
     * @return
     */
    QueryBankCardInfoResponse queryBankCardInfoByDigest(String txAcctNo, String bankAcctDigest);
    
    /**
     * 
     * queryCustMobileFacade:查询手机号
     * @param txAcctNo
     * @param cpAcctNo
     * @return
     * <AUTHOR>
     * @date 2021年9月14日 下午1:09:16
     */
    QueryCustMobileResponse queryCustMobileFacade(String txAcctNo, String cpAcctNo);
    
    /**
     * 
     * queryBankAcctSensitiveInfoFacade:查询银行卡明文信息
     * @param txAcctNo
     * @param cpAcctNo
     * @return
     * <AUTHOR>
     * @date 2021年9月14日 上午11:31:33
     */
    QueryBankAcctSensitiveInfoResponse queryBankAcctSensitiveInfoFacade(String txAcctNo, final String cpAcctNo);


    /**
     * 查询系统工作日
     *
     * @param workdayType 工作日类型
     * @param sysCodeEnum 系统码
     * @param disInfoDto 分销信息
     * @return com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayResponse
     * @throws Exception
     * @author: lianlian.deng
     * @date: 2022/3/2 15:08
     * @since JDK 1.8
     */
    QueryWorkdayResponse querySysWorkday(WorkdayTypeEnum workdayType, SysCodeEnum sysCodeEnum,
                                         DisInfoDto disInfoDto) throws Exception;

    /**
     * @description:(查询客户信息)
     * @param hboneNo
     * @return com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult
     * @author: haiguang.chen
     * @date: 2022/3/29 18:47
     * @since JDK 1.8
     */
    QueryAccHboneInfoResult queryAccHboneInfo(String hboneNo);

    QueryHzBuyOrderInfoResponse queryHzBuyInfo(String fundCode, String custNo,String ip,String queryDateStr);
}
