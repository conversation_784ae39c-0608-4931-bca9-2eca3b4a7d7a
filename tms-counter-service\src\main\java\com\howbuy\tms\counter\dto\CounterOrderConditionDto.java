/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

/**
 * @description:柜台条件
 * @author: chuanguang.tang
 * @date: 2021/8/13 11:15
 * @since JDK 1.8
 */
public class CounterOrderConditionDto {

    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 基金代码
     */
    private String productCode;
    /**
     * 客户订单号
     */
    private String dealNo;
    /**
     * 业务类型
     */
    private String mBusiCode;
    /**
     * ta日期
     */
    private String queryBeginDt;
    /**
     * ta日期
     */
    private String queryEndDt;

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getQueryBeginDt() {
        return queryBeginDt;
    }

    public void setQueryBeginDt(String queryBeginDt) {
        this.queryBeginDt = queryBeginDt;
    }

    public String getQueryEndDt() {
        return queryEndDt;
    }

    public void setQueryEndDt(String queryEndDt) {
        this.queryEndDt = queryEndDt;
    }
}
