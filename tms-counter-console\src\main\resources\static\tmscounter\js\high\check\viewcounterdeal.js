/**
 *查询柜台订单公共信息
 *
 **/
var ViewCounterDeal = {
    /**
     * 显示审核按钮
     */
    showCheckBtn: function () {
        //显示审核按钮
        $("#checkRejectBtn").show();
        $("#checkConfirmBtn").show();
    },

    /**
     * 其他信息
     * @param checkOrder
     */
    buildOtherInfo: function (checkOrder) {
        $("#consCode").val(checkOrder.consCode);//投资顾问代码
        $("#agentFlag").val(checkOrder.agentFlag);//是否代理
        $("#operatorNo").html(checkOrder.creator);//操作员
    },

    /**
     * 审核信息
     */
    buildCheckInfo: function (checkOrder) {
        $("#checker").html(checkOrder.checker);// 审核人
        $("#memo").html(checkOrder.memo);// 驳回原因
    },

    /**
     * 经办人信息
     * @param checkOrder
     */
    buildTransactor: function (checkOrder) {
        $("#transactorName").val(checkOrder.transactorName);//经办人姓名
        $("#transactorIdType").val(checkOrder.transactorIdType);//经办人证件类型
        $("#transactorIdNo").val(checkOrder.transactorIdNo);//经办人证件号
        if ('1' === checkOrder.agentFlag) {
            //1-代理 代理显示经办人信息
            $("#transactorFormId").show();
        } else {
            //0-不代理 隐藏经办人信息
            $("#transactorFormId").hide();
        }

    },

    /**
     * 预约信息
     */
    buildAppointmentInfo: function (appointmentInfo) {

        $("#rsList").html('');
        if (appointmentInfo.length <= 0) {
            var trHtml = '<tr><td colspan="11">没有查询到预约信息</td></tr>';
            $("#rsList").append(trHtml);
            return false;
        }
        $(appointmentInfo).each(function (index, element) {
            var trList = [];
            /**预约单号1*/
            trList.push(CommonUtil.formatData(element.appointId));
            /**预约产品代码2*/
            trList.push(CommonUtil.formatData(element.productCode));
            /**预约产品名称3*/
            trList.push(CommonUtil.formatData(element.productName));
            /**预约业务4*/
            trList.push(CommonUtil.getMapValue(CONSTANTS.PRE_TRADE_TYPE_NAME_MAP, element.buyStatus, '--'));
            /**预约日期5*/
            trList.push(formatDate(CommonUtil.formatData(element.appointStartDt, '')));
            /**预约时间6*/
            trList.push(formatTime(CommonUtil.formatData(element.appointStartTm, '')));
            /**预约金额7*/
            trList.push(CommonUtil.formatData(element.appAmt, ''));
            /**预约份额8*/
            trList.push(CommonUtil.formatData(element.appVol, ''));
            /**预约折扣9*/
            trList.push(CommonUtil.formatData(element.discountRate, ''));
            /**预约单状态10*/
            trList.push(CommonUtil.getMapValue(CONSTANTS.PRE_BOOK_STATE_MAP, element.orderStatus));
            /**认缴金额*/
            trList.push(CommonUtil.formatData(element.subsAmt, ''));
            var htdHtml = '<td style="display:none" >"' + element.fundRiskLevel + '"</td><td style="display:none">"' + element.fundAttr + '"</td><td style="display:none">"' + element.fundStatus + '"</td><td style="display:none">"' + element.preType + '"</td>';
            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td>' + htdHtml + '</tr>';
            $("#rsList").append(trHtml);
        });

    },


    /**
     * 高端产品基本信息
     */
    buildFundInfo: function (fundInfo) {

        $("#buyBusiTypeId").html(CommonUtil.getMapValue(CONSTANTS.BUY_BUYS_TYPE_MAP, fundInfo.buyBusiType));
        $("#fundName").html(fundInfo.fundAttr || '');
        $("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, fundInfo.fundRiskLevel, ''));
        $("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundInfo.fundStat));
        $("#taCodeId").html(fundInfo.taCode);
        $("#productTypeId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, fundInfo.fundType));
        $("#shareClassId").html(CommonUtil.getMapValue(CONSTANTS.FUND_SHARECLASS_MAP, fundInfo.shareClass));
        $("#productChannelId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, fundInfo.productChannel));
        $("#netMinAppAmtId").html(CommonUtil.formatAmount(fundInfo.netMinAppAmt));
        $("#netMinSuppleAmtId").html(CommonUtil.formatAmount(fundInfo.netMinSuppleAmt));
        $("#feeCalModeId").html(CommonUtil.getMapValue(CONSTANTS.FEE_CAL_MODE_MAP, fundInfo.feeCalMode));

        if ("1" === fundInfo.fixedIncomeFlag) {
            $("#redeemExpireTr").removeAttr("hidden");
            $("#redeemExpireDescId").html("到期是否赎回");
            $("#redeemExpireTd").html(CommonUtil.buildRedeemExpireHtml());
        }

    },

    /**
     * 客户信息
     * @param custInfoList
     * @returns {Boolean}
     */
    buildCustInfo: function (custInfoList) {
        var custInfoDtoList = custInfoList || [];

        if (custInfoDtoList.length === 0) {
            CommonUtil.layer_tip("没有查询到此用户");
            return false;
        }

        $("#custInfoId").empty();
        $(custInfoDtoList).each(function (index, element) {
            var trList = [];
            trList.push(CommonUtil.formatData(element.custNo, '--'));
            trList.push(CommonUtil.formatData(element.custName, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP, element.invstType, ''));
            if ('0' === element.invstType) {
                //机构类型用户
                trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
            }
            if (element.invstType === '1') {
                //个人类型用户
                trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
            }
            if (element.invstType === '2') {
                trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
            }
            trList.push(CommonUtil.formatData(element.idNo, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP, element.custRiskLevel, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, element.disCode, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_SIGN_FLAG_MAP, element.signFlag, '--'));
            //客户资管投资承诺书签署状态 1-签署；0-未签署
            trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_SIGN_FLAG_MAP, element.fundFlag, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP, element.custStat, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP, element.investorType, '--'));
            trList.push(CommonUtil.getMapValue(CONSTANTS.COLLECT_PROTOCOL_METHOD_MAP, element.collectProtocolMethod, '--'));
            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            $("#custInfoId").append(trHtml);
        });

    },
    /**
     * 构建预约开放预约日历信息
     * @param productAppointInfo
     */
    buildProductAppointmentInfo: function (productAppointInfo) {
        $("#appointStartDtId").html(productAppointInfo.appointStartDt);//预约开始日期
        $("#apponitEndDtId").html(productAppointInfo.apponitEndDt);//
        $("#openStartDtId").html(productAppointInfo.openStartDt);
        $("#openEndDtId").html(productAppointInfo.openEndDt);
        $("#payRatioId").val(productAppointInfo.payRatio);
    },

    buildPayRatioAmount(payRatio, subsAmt, disCode, applyAmount) {
        if ("HZ000N001" === disCode) {
            $("#payRatioAmount").val(CommonUtil.formatAmount(applyAmount));
        } else {
            if (payRatio && subsAmt) {
                var payRatioAmount = subsAmt * payRatio;
                $("#payRatioAmount").val(CommonUtil.formatAmount(payRatioAmount))
            }
        }

    }
};