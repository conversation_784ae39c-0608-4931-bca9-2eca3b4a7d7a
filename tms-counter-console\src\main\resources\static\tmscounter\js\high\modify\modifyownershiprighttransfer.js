/**
 * 初始化页面事件
 */

$(function () {
    // 订单信息查询
    // 查询订单
    Modify.queryCounterDealOrder(ModifyOwnerShipRightTransferPage.modifyDataCallBack, null);
});

function changeIsTransfer(value) {
    if (value === '0') {
        $("#transferPriceComponent").hide();
    }
    if (value === '1') {
        $("#transferPriceComponent").show();
        $("#transferPrice").val(0)
    }
}

var ModifyOwnerShipRightTransferPage = {
        modifyDataCallBack: function (data) {
            console.log(JSON.stringify(data));
            var bodyData = data.body || {};
            Modify.modifyDealOrder = bodyData.counterOrderDto || {};
            // 驳回原因
            $("#reCheckMsg").val(Modify.modifyDealOrder.memo);
            ModifyOwnerShipRightTransferPage.queryOwnershipRightTransferDtl();
        },

        queryOwnershipRightTransferDtl: function () {
            var dealAppNo = CommonUtil.getParam("dealAppNo");
            if (isEmpty(dealAppNo)) {
                showMsg("申请单号不存在");
                return false;
            }
            var uri = TmsCounterConfig.QUERY_OWNERSHIP_RIGHT_TRANSFER_DTL || "";
            var reqparamters = {};
            reqparamters.dealAppNo = dealAppNo;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
            CommonUtil.ajaxAndCallBack(paramters, ModifyOwnerShipRightTransferPage.buildOrderInfo);
        },


        /**
         *渲染产品信息查询结果
         */
        buildOrderInfo: function (data) {
            //viewType 0-查看；2-修改
            var viewType = CommonUtil.getParam("viewType");
            var bodyData = data.body || {};
            var orderInfo = bodyData.orderDtl || [];
            var appendHtml = '';
            ModifyOwnerShipRightTransferPage.mBusinessCode = orderInfo.mBusinessCode;
            ModifyOwnerShipRightTransferPage.isNoTradeTransfer = orderInfo.isNoTradeTransfer;
            ModifyOwnerShipRightTransferPage.transferPrice = orderInfo.transferPrice;

            $("#layerrs").empty();
            if (orderInfo.mBusinessCode === 1142 || orderInfo.mBusinessCode === 1144 || orderInfo.mBusinessCode === 1145) {
                appendHtml =
                    '<tr className="text-c">' +
                    '<td>客户号:</td>' +
                    '<td id="txAcctNo">' + formatData(orderInfo.txAcctNo) + '</td>' +
                    '<td>客户姓名:</td>' +
                    '<td id="custName">' + formatData(orderInfo.custName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>基金代码:</td>' +
                    '<td id="fundCode">' + formatData(orderInfo.fundCode) + '</td>' +
                    '<td>基金名称:</td>' +
                    '<td id="fundName">' + formatData(orderInfo.fundName) + '</td>' +
                    '</tr>' +
                    '<tr className="text-c">' +
                    '<td>业务名称:</td>' +
                    '<td id="busiName">' + CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, orderInfo.mBusinessCode, "--") + '</td>' +
                    '<td>确认日期:</td>' +
                    '<td id="ackDt">' + formatData(orderInfo.ackDt) + '</td>' +
                    '</tr>';
                if (viewType === '2') {
                    if (orderInfo.isNoTradeTransfer === '1') {
                        subAppendHtml =
                            '<tr className="text-c">' +
                            '<td>转让份额:</td>' +
                            '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                            '<td>是否非交易过户:</td>' +
                            '<select name="isNoTradeTransfer" id="isNoTradeTransfer" class="select" onchange="changeIsTransfer(value)">' +
                            '<option value="1" selected>是</option> ' +
                            '<option value="0">否</option> ' +
                            '</select> </td>' +
                            '</tr>' +
                            '<tr className="text-c" id="transferPriceComponent">' +
                            '<td>转让价格:</td>' +
                            '<td> <input type="text" placeholder="请输入" id="transferPrice" name="transferPrice"  value="' + formatData(orderInfo.transferPrice, 0) + '"/></td>' +
                            '<tr className="text-c">' +
                            '<td>过户份额对应的认缴金额:</td>' +
                            '<td> <input type="text" placeholder="请输入" id="subsAmt" name="subsAmt"  value="' + CommonUtil.formatData(orderInfo.subsAmt) + '"/></td>' +
                            '<td>过户的总认缴金额:</td>' +
                            '<td> <input type="text" placeholder="请输入" id="totalSubsAmt" name="totalSubsAmt"  value="' + CommonUtil.formatData(orderInfo.totalSubsAmt) + '"/></td>' +
                            '</tr>'
                    } else {
                        subAppendHtml =
                            '<tr className="text-c">' +
                            '<td>转让份额:</td>' +
                            '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                            '<td>是否非交易过户:</td>' +
                            '<select name="isNoTradeTransfer" id="isNoTradeTransfer" class="select" onchange="changeIsTransfer(value)">' +
                            '<option value="1" >是</option> ' +
                            '<option value="0" selected>否</option> ' +
                            '</select> </td>' +
                            '</tr>' +
                            '<tr className="text-c" id="transferPriceComponent">' +
                            '<td>转让价格:</td>' +
                            '<td> <input type="text" placeholder="请输入" id="transferPrice" name="transferPrice"  value="' + formatData(orderInfo.transferPrice, 0) + '"/></td>' +
                            '</tr>'
                    }
                    appendHtml = appendHtml + subAppendHtml;
                } else {
                    appendHtml = appendHtml +
                        '<tr className="text-c">' +
                        '<td>基金类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUNDTYPE_MAP, orderInfo.fundType, "--") + '</td>' +
                        '<td>基金二级类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_SUB_TYPE_MAP, orderInfo.fundSubType, "--") + '</td>' +
                        '</tr>' +
                        '<tr className="text-c">' +
                        '<td>确认金额:</td>' +
                        '<td id="ackAmt">' + formatData(orderInfo.ackAmt, "--") + '</td>' +
                        '<td>确认份额:</td>' +
                        '<td id="ackVol">' + formatData(orderInfo.ackVol, "--") + '</td>' +
                        '</tr>' +
                        '<tr className="text-c">' +
                        '<td>是否非交易过户:</td>' +
                        '<td id="isNoTradeTransfer">' + CommonUtil.getMapValue(CONSTANTS.IS_NOTRADE_TRANSFER, orderInfo.isNoTradeTransfer, "--") + '</td>' +
                        '<td>转让价格:</td>' +
                        '<td id="transferPrice">' + formatData(orderInfo.transferPrice, "--") + '</td>' +
                        '<tr className="text-c">' +
                        '<td>过户份额对应的认缴金额:</td>' +
                        '<td id="subsAmt">' + formatData(orderInfo.subsAmt, "--") + '</td>' +
                        '<td>过户的总认缴金额:</td>' +
                        '<td id="totalSubsAmt">' + formatData(orderInfo.totalSubsAmt, "--") + '</td>' +
                        '</tr>'
                }
            } else {
                // 这里是非交易转让/转入的
                appendHtml =
                    '<tr className="text-c">' +
                    '<td>转让人客户号:</td>' +
                    '<td id="outTxAcctNo">' + formatData(orderInfo.outTxAcctNo) + '</td>' +
                    '<td>转让人客户姓名:</td>' +
                    '<td id="outCustName">' + formatData(orderInfo.outCustName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>受让人客户号:</td>' +
                    '<td id="inTxAcctNo">' + formatData(orderInfo.inTxAcctNo) + '</td>' +
                    '<td>受让人客户姓名:</td>' +
                    '<td id="inCustName">' + formatData(orderInfo.inCustName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>基金代码:</td>' +
                    '<td id="fundCode">' + formatData(orderInfo.fundCode) + '</td>' +
                    '<td>基金名称:</td>' +
                    '<td id="fundName">' + formatData(orderInfo.fundName) + '</td>' +
                    '</tr>' +
                    '<tr className="text-c">' +
                    '<td>业务名称:</td>' +
                    '<td id="busiName">' + CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, orderInfo.mBusinessCode, "--") + '</td>' +
                    '<td>确认日期:</td>' +
                    '<td id="ackDt">' + formatData(orderInfo.ackDt) + '</td>' +
                    '</tr>';
                if (viewType === '2') {
                    appendHtml = appendHtml + '<tr className="text-c">' +
                        '<td>转让份额:</td>' +
                        '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                        '<td>转让价格:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="transferPrice" name="transferPrice"  value="' + formatData(orderInfo.transferPrice, 0) + '"/></td>' +
                        '<td>过户份额对应的认缴金额:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="subsAmt" name="subsAmt"  value="' + CommonUtil.formatData(orderInfo.subsAmt) + '"/></td>' +
                        '<td>过户的总认缴金额:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="totalSubsAmt" name="totalSubsAmt"  value="' + CommonUtil.formatData(orderInfo.totalSubsAmt) + '"/></td>' +
                        '</tr>'
                } else {
                    appendHtml = appendHtml +
                        '<tr className="text-c">' +
                        '<td>基金类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUNDTYPE_MAP, orderInfo.fundType, "--") + '</td>' +
                        '<td>基金二级类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_SUB_TYPE_MAP, orderInfo.fundSubType, "--") + '</td>' +
                        '</tr>' +
                        '<tr className="text-c">' +
                        '<td>确认金额:</td>' +
                        '<td id="ackAmt">' + formatData(orderInfo.ackAmt, "--") + '</td>' +
                        '<td>确认份额:</td>' +
                        '<td id="ackVol">' + formatData(orderInfo.ackVol, "--") + '</td>' +
                        '</tr>' +
                        '<tr className="text-c">' +
                        '<td>转让价格:</td>' +
                        '<td id="transferPrice">' + formatData(orderInfo.transferPrice, "--") + '</td>' +
                        '<td>过户份额对应的认缴金额:</td>' +
                        '<td id="subsAmt">' + formatData(orderInfo.subsAmt, "--") + '</td>' +
                        '<td>过户的总认缴金额:</td>' +
                        '<td id="totalSubsAmt">' + formatData(orderInfo.totalSubsAmt, "--") + '</td>' +
                        '</tr>'
                }
            }
            $("#layerrs").append(appendHtml);
            if (orderInfo.isNoTradeTransfer === '0') {
                $("#transferPriceComponent").hide();
            }
        }
    }
;