package com.howbuy.tms.counter.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:用户认缴金额修改申请入参
 * @Author: yun.lu
 * Date: 2024/7/15 17:45
 */
@Data
public class CustomerSubsAmtApplyReqDto extends BaseDto {
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 产品编码
     */
    private String fundCode;
    /**
     * 新认缴金额
     */
    private BigDecimal newSubsAmt;
    /**
     * 操作人编号
     */
    private String operatorNo;
    /**
     * 创建人账号
     */
    private String creator;
    /**
     * 申请日期,yyyyMMdd
     */
    private String appDt;
    /**
     * 申请时间,默认是,090000
     */
    private String appTime;
}
