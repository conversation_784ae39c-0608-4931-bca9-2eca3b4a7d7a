package com.howbuy.tms.counter.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:客户认缴金额信息
 * @Author: yun.lu
 * Date: 2024/7/15 14:18
 */
@Data
public class CustomerSubsAmtChangeDetailDto extends BaseDto {
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金名
     */
    private String fundName;
    /**
     * 旧的认缴金额
     */
    private BigDecimal oldSubsAmt;
    /**
     * 新的认缴金额
     */
    private BigDecimal newSubsAmt;
    /**
     * 持仓份额
     */
    private BigDecimal balanceVol;
    /**
     * 操作人编号
     */
    private String operatorNo;
    /**
     * 创建人账号
     */
    private String creator;
    /**
     * 申请日期,yyyyMMdd
     */
    private String appDt;
    /**
     * 申请时间,默认是,090000
     */
    private String appTime;
}
