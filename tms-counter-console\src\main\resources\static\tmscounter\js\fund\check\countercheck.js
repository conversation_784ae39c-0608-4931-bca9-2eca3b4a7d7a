/**
*零售柜台审核
*<AUTHOR>
*@date 2017-09-18 15:23
*/
$(function(){
	Init.init();
	CounterCheck.init();
	
	CounterCheck.custInfo = {};
	CounterCheck.checkOrders = [];
	CounterCheck.checkedOrder = {};
	//通过
	CounterCheck.Succ = '1';
	//驳回
	CounterCheck.Faild = '3';
	//废单
	CounterCheck.Abolish = '4';
	
	var selectTxCodeHtml = '<option value="">全部</option>';
	$.each(CONSTANTS.COUNTER_FUND_TXCODE_MAP,function(name,value){
		selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
	});
	$("#selectTxCode").empty();
	$("#selectTxCode").html(selectTxCodeHtml);
	
	//查询待审核订单
	CounterCheck.queryOrderInfo();
});
 var CounterCheck = {
	init:function(){
		$("#queryBtn").on('click',function(){
			CounterCheck.queryOrderInfo();
		});
	},
	
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_FUND_CHECK_ORDER_URL  ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		reqparamters.checkFlag = 0; //只查未审核
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, CounterCheck.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		CounterCheck.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];
		$("#rsList").empty();
		if(CounterCheck.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无待审核记录</td></tr>';
			$("#rsList").append(trHtml);
		}
		
		var staticData = bodyData.counterQueryOrderRespDto || {};
		$("#staticId").html("当页小计：申请笔数【"+CounterCheck.checkOrders.length+"】申请金额【"+CommonUtil.formatAmount(staticData.pageAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.pageAppVol)+"】 合计：申请笔数【"+staticData.totalCount+"】申请金额【"+CommonUtil.formatAmount(staticData.totalAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.totalAppVol)+"】");
		
		var i = 1;
		$(CounterCheck.checkOrders).each(function(index,element){
			var trList = [];
			trList.push(i++);
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custName));
			if(element.invstType == '0'){//属于机构
				trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
			} 
			if(element.invstType == '1'){//属于个人
				trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
			}
			if(element.invstType == '2'){//属于产品
				trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
			}
			
			trList.push(CommonUtil.formatData(element.idNo,'--'));  
			trList.push(CommonUtil.formatData(element.fundCode));
			trList.push(CommonUtil.formatData(element.fundName));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, element.txCode, '--'));
			trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_TYPE, element.withdrawDirection, '--'));
			if(element.appRatio > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatPercent(element.appRatio, "", 2)));
			}else {
				trList.push('--');
			}
			if(element.appAmt > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
			}else {
				trList.push('--');
			}
			if(element.appVol > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
			}else {
				trList.push('--');
			}
			trList.push(CommonUtil.formatData(element.appDt));
			trList.push(CommonUtil.formatData(element.appTm));
			trList.push(CommonUtil.formatData(element.creator,''));
			trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = '+index+'>复核</a>');
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
		});
		
		//绑定审核
		$(".reCheck").off();
		$(".reCheck").on('click',function(){			
			var indexValue = $(this).attr("indexvalue");
			var checkOrder = CounterCheck.checkOrders[indexValue] || {};
			CounterCheck.checkedOrder = checkOrder;
			var txCode = CounterCheck.checkedOrder.txCode;
			var dealAppNo = CounterCheck.checkedOrder.dealAppNo;
			var txAcctNo = CounterCheck.checkedOrder.txAcctNo;
			var disCode = CounterCheck.checkedOrder.disCode;
			var idNo = CounterCheck.checkedOrder.idNo;
			var dealNo = CounterCheck.checkedOrder.dealNo;
			var param = "checkId="+dealAppNo+"&custNo="+txAcctNo+"&disCode="+disCode+"&idNo="+idNo+"&dealNo="+dealNo;
			if('Z910042' == txCode || 'Z910058' == txCode || 'Z310086' == txCode){// 认申购
				window.open("checkbuy.html?"+param,"_blank");
				return;
			} else if('Z910043' == txCode || 'Z310088' == txCode){// 赎回
				window.open("checksell.html?"+param,"_blank");
				return;
			} else if('Z910044' == txCode){// 修改分红方式
				window.open("checkmodifydiv.html?"+param,"_blank");
				return;
			} else if('Z910045' == txCode || 'Z910046' == txCode){// 撤单
				window.open("checkcancel.html?"+param,"_blank");
				return;
			} else if('Z910057' == txCode){// 基金转换
				window.open("checkexchange.html?"+param,"_blank");
				return;
			} else if('Z910076' == txCode){// 转托管转入
				window.open("checktransfertubein.html?"+param,"_blank");
				return;
			} else if('Z910075' == txCode){// 转托管转出
				window.open("checktransfertubeout.html?"+param,"_blank");
				return;
			}else if('Z930023' == txCode){// 修改回款方向
				window.open("checkmodifyredeemdirection.html?"+param,"_blank");
				return;
			}else if('Z310002' == txCode){// 组合赎回
				window.open("checkselfsell.html?"+param,"_blank");
				return;
			}
		});
	}
};
