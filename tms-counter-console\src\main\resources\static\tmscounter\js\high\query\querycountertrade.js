/**
 *柜台交易查询
 *<AUTHOR>
 *@date 2017-04-11 16:17
 */

$(function(){
	QueryCounterTrade.order = {};
	QueryCounterTrade.orders = [];
	QueryCounterTrade.checkOrder = {};
	QueryCounterTrade.init();
});

var QueryCounterTrade ={
	/**
	 * 初始化
	 */
	init:function(){
		$("#queryTradeBtn").on('click',function(){
			QueryCounterTrade.queryCounterTrade();
		});

		$("#downLoadBtn").on('click',function(){
			QueryCounterTrade.downLoad();
		});

		var selectTxcodeHtm ='<option value=""> 全部</option>';
		$.each(CONSTANTS.COUNTER_TXCODE_MAP,function(name,value){
			selectTxcodeHtm +='<option value="'+name+'">'+value+' </option>';
		});

		$("#selectTxCode").html(selectTxcodeHtm);
	},
	/**
	 * 下载
	 */
	downLoad : function(){
		var  uri= TmsCounterConfig.QUERY_COUNTER_TRADE_DOWN_URL ||  {};

		var reqparamters  = {};
//		reqparamters.page = 1;
//		reqparamters.pageSize = 50;

		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);

		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var code = data.code || '';
			var desc = data.desc || '';
			var body = data.body || '';
			if(CommonUtil.isSucc(data.code)){
				var fileName = body.fileName;
				window.location.href=TmsCounterConfig.QUERY_COUNTER_DOWN_URL+"?fileName="+fileName;
			}else{
				CommonUtil.layer_tip(desc+'('+desc+')');
			}
		});
	},
	/**
	 * 柜台交易查询
	 */
	queryCounterTrade : function(){
		var  uri= TmsCounterConfig.QUERY_COUNTER_TRADE_URL ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 50;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxPaging(uri, paramters,  QueryCounterTrade.callBack, "pageView");
	},

	callBack:function(data){
		var bodyData = data;
		QueryCounterTrade.orders = bodyData.rsList || [];
		$("#rsList").empty();
		if(QueryCounterTrade.orders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="12">暂无交易记录</td></tr>';
			$("#rsList").append(trHtml);
		}

		$(QueryCounterTrade.orders).each(function(index,element){
			var trList = [];
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custName));
			trList.push(CommonUtil.formatData(element.fundCode));
			trList.push(CommonUtil.formatData(element.fundName));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP, element.txCode, ''));
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.dealNo));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP,element.checkFlag,''));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_APP_FLAG_MAP,element.appFlag,''));
			trList.push(CommonUtil.formatData(element.appDt,'') + ' ' +CommonUtil.formatData(element.appTm,''));
			trList.push(CommonUtil.formatData(element.creator,''));
			trList.push('<a class="details" href="javascript:void(0);" dealAppNo = '+CommonUtil.formatData(element.dealAppNo)+'>详情</a>');
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
		});
		//绑定查看
		$(".details").off();
		$(".details").on('click',function(){
			var dealAppNo = $(this).attr("dealAppNo");
			QueryCounterTrade.viewDtl(dealAppNo);
		});
	},

	/**
	 * 查询数据明细
	 */
	viewDtl:function(dealAppNo){
		var  uri= TmsCounterConfig.QUERY_COUNTER_TRADE_URL ||  {};
		var data ={};
		data.queryConditonForm = JSON.stringify({"dealAppNo":dealAppNo});
		data.queryType = "1";
		var paramters = CommonUtil.buildReqParams(uri, data,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			QueryCounterTrade.processViewDtl(data);
		});
	},
	processViewDtl:function (data) {
		var checkOrderList = data.body.rsList;
		var checkOrder = checkOrderList[0];
		QueryCounterTrade.checkOrder = checkOrder;
		var detailList = [];
		// 修改资金回款方向交易，需要特定展示
		if ('Z900063'==checkOrder.txCode) {
			QueryCounterTrade.ModifyRefundDirCheck(checkOrder);
		}else{
			detailList.push('<tr>'+
				'<td class="type" width="15%">基金代码</td>'+
				'<td width="35%">'+checkOrder.fundCode+'</td>'+
				'<td class="type" width="15%">业务类型</td>'+
				'<td width="35%">'+CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP,checkOrder.txCode)+'</td>'+
				'</tr>');

			detailList.push('<tr>'+
				'<td class="type">申请金额/份额</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.appAmt, '--')+'/'+CommonUtil.formatData(checkOrder.appVol,'--')+'</td>'+
				'<td class="type">申请折扣率</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.discountRate, '')+'</td>'+
				'</tr>');
			// 展示非交易过户信息
			if ('Z900054'==checkOrder.txCode) {
				detailList.push('<tr>'+
					'<td class="type">转入客户号</td>'+
					'<td>'+CommonUtil.formatData(checkOrder.inTxAcctNo, '--')+'</td>'+
					'<td class="type">转入客户姓名</td>'+
					'<td>'+CommonUtil.formatData(checkOrder.inCustName, '')+'</td>'+
					'</tr>');
				detailList.push('<tr>'+
					'<td class="type">转入银行卡</td>'+
					'<td>'+CommonUtil.encodeLastNum(checkOrder.inBankAcct || '', 4)+'</td>'+
					'<td class="type">过户认缴金额</td>'+
					'<td>'+CommonUtil.formatData(checkOrder.subsAmt, '')+'</td>'+
					'</tr>');
			}
			detailList.push('<tr>'+
				'<td class="type">银行卡</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.bankAcct,'')+'</td>'+
				'<td class="type">支付方式</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.PAYMENT_TYPE_MAP, checkOrder.paymentType, '')+'</td>'+
				'</tr>');
			detailList.push('<tr>'+
				'<td class="type">申请时间</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.appDt,'') + '' +CommonUtil.formatData(checkOrder.appTm,'')+'</td>'+
				'<td class="type">目标分红方式</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP, checkOrder.fundDivMode,'')+'</td>'+
				'</tr>');
			detailList.push('<tr>'+
				'<td class="type">巨额赎回顺延</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.LARGE_REDM_FLAG_MAP, checkOrder.largeRedmFlag, '')+'</td>'+
				'<td class="type">异常赎回标记</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP,checkOrder.unusualTransType,'')+'</td>'+
				'</tr>');
			detailList.push('<tr>'+
				'<td class="type">申请状态</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_APP_FLAG_MAP ,checkOrder.appFlag,'')+'</td>'+
				'<td class="type">审核状态</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP ,checkOrder.checkFlag,'')+'</td>'+
				'</tr>');
			detailList.push('<tr>'+
				'<td class="type">柜台订单号</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.dealAppNo,'')+'</td>'+
				'<td class="type">中台订单号</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.dealNo,'')+'</td>'+
				'</tr>');

			detailList.push('<tr>'+
				'<td class="type">失败原因</td>'+
				'<td>'+CommonUtil.formatData(checkOrder.memo,'')+'</td>'+
				'<td class="type"></td>'+
				'<td></td>'+
				' </tr>');

			var bodyHtml = detailList.join('');
			$(".tabPop").html(bodyHtml);

			layer.open({
				title: ['订单详情', true],
				type: 1,
				area: ['700px', 'auto'],
				btn: ['关闭'],
				skin: 'layui-layer-rim', //加上边框
				btnAlign: 'l',
				content: $('.detailInfo'),
				cancel: function (index) { //或者使用btn2
					//按钮【取消】的回调
				}
			});
		}
	},

	/**
	 * 修改回款方向交易查询
	 */
	ModifyRefundDirCheck : function(checkOrder){
		var  uri= TmsCounterConfig.QUERY_COUNTER_MODIFY_REFUND_DIRECTION_URL ||  {};
		var reqparamters  = {};
		var queryOrderCondition = {"dealNo":checkOrder.dealNo,"txAcctNo":checkOrder.txAcctNo};
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 50;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxPaging(uri, paramters,  QueryCounterTrade.ModifyRefundDirCheckCallBack, "pageView");
	},

	ModifyRefundDirCheckCallBack:function(data){
		var orders = data.orders || [];
		var order = orders[0];
		var detailList = [];
		var checkOrder = QueryCounterTrade.checkOrder;
		detailList.splice(0,detailList.length);
		//回款方向
		if ((order.mBusiCode == '1122' || order.mBusiCode == '1120') && (order.orderStatus == '5' || order.orderStatus == '6')) {
			var withdrawDirection = CommonUtil.getMapValue(CONSTANTS.WITHDRAW_DIR_ALL_MAP, order.redeemDirection, '');
		} else {
			var withdrawDirection = CommonUtil.getMapValue(CONSTANTS.REDEEM_DIRECTION_MAP, order.redeemDirection, '');
		}
		// 退款总金额
		if(order.ackAmt == undefined ){
			var refundAmount = '--';
		}else{
			var refundAmount = order.ackAmt;
		}
		// 回款备注（会可用余额金额+回可用余额备注）
		var refundAmt;
		if(order.refundAmt == undefined){
			refundAmt = '--';
		}else{
			refundAmt = order.refundAmt;
		}

		var refundMemo;
		if(order.refundMemo == undefined){
			refundMemo = '--';
		}else{
			refundMemo = order.refundMemo;
		}

		var refundMemoInfo = '回可用余额：' + refundAmt + '；回可用余额的备注：' + refundMemo;

		detailList.push('<tr>'+
			'<td class="type" width="20%">基金代码</td>'+
			'<td width="30%">'+checkOrder.fundCode+'</td>'+
			'<td class="type" width="20%">业务类型</td>'+
			'<td width="30%">'+CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP,checkOrder.txCode)+'</td>'+
			'</tr>');
		detailList.push('<tr>'+
			'<td class="type" width="20%">回款方向</td>'+
			'<td width="30%">'+withdrawDirection+'</td>'+
			'<td class="type" width="20%">回款金额</td>'+
			'<td width="30%">'+refundAmount+'</td>'+
			'</tr>');
		detailList.push('<tr>'+
			'<td class="type" width="20%">回款方向备注</td>'+
			'<td width="30%">'+refundMemoInfo+'</td>'+
			'<td width="20%"></td>'+
			'<td width="30%"></td>'+
			'</tr>');
		detailList.push('<tr>'+
			'<td class="type" width="20%">申请状态</td>'+
			'<td width="30%">'+CommonUtil.getMapValue(CONSTANTS.COUNTER_APP_FLAG_MAP ,checkOrder.appFlag,'')+'</td>'+
			'<td class="type" width="20%">审核状态</td>'+
			'<td width="30%">'+CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP ,checkOrder.checkFlag,'')+'</td>'+
			'</tr>');
		detailList.push('<tr>'+
			'<td class="type" width="20%">柜台订单号</td>'+
			'<td width="30%">'+CommonUtil.formatData(checkOrder.dealAppNo,'')+'</td>'+
			'<td class="type" width="20%">中台订单号</td>'+
			'<td width="30%">'+CommonUtil.formatData(checkOrder.dealNo,'')+'</td>'+
			'</tr>');
		var bodyHtml = detailList.join('');
		$(".tabPop").html(bodyHtml);

		layer.open({
			title: ['订单详情', true],
			type: 1,
			area: ['700px', 'auto'],
			btn: ['关闭'],
			skin: 'layui-layer-rim', //加上边框
			btnAlign: 'l',
			content: $('.detailInfo'),
			cancel: function (index) { //或者使用btn2
				//按钮【取消】的回调
			}
		});

	}
};
