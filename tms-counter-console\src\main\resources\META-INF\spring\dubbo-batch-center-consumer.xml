<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - limitations 
	under the License. - See the License for the specific language governing 
	permissions and -->

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<import resource="classpath:context/spring-facade-common.xml" />
	<import resource="classpath:context/spring-facade-fbsonline.xml"/>
	<import resource="classpath:context/spring-facade-fbsonlinesearch.xml"/>
	<import resource="classpath:context/spring-facade-ftxonline.xml"/>
	<import resource="classpath:context/spring-facade-ftxonlinesearch.xml"/>
	<import resource="classpath:context/spring-facade-payonline.xml"/>
	<import resource="classpath:context/spring-facade-acccenter.xml"/>
	<import resource="classpath:context/spring-facade-interlayer.xml"/>
	<import resource="classpath:context/spring-facade-ftxconsole.xml"/>
	<import resource="classpath:context/spring-facade-cc.xml"/>
	<import resource="classpath:context/spring-facade-lctonline.xml"/>
	<import resource="classpath:context/spring-facade-fund.xml"/>
	<import resource="classpath:context/spring-facade-dtms.xml"/>

	<!-- 查询系统工作日-->
    <dubbo:reference id="queryWorkdayFacade"  interface="com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayFacade"  registry="batch-center-remote" check="false"/>
	<!-- 查询客户基本信息 -->
	<dubbo:reference id="queryCustBaseInfoFacade" interface="com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoFacade"  registry="batch-center-remote" check="false"/>
	
	<!-- 查询批处理节点状态 -->
	<dubbo:reference id="queryBatchFlowInfoFacade" interface="com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoFacade"  registry="batch-center-remote" check="false"/>

	<!-- 查询批处理节点TA子流程状态 -->
	<dubbo:reference id="queryTaBatchFlowInfoFacade" interface="com.howbuy.tms.batch.facade.query.querytabatchflowinfo.QueryTaBatchFlowInfoFacade"  registry="batch-center-remote" check="false"/>
	
    <!--查询当前TA确认的基金份额接口-->
    <dubbo:reference id="queryCurTaDtAckFacade" interface="com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckFacade"  registry="batch-center-remote" check="false"/>

	<!--资料上传 -->
	<dubbo:reference id="uploadVoucherFileFacade" interface="com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.UploadVoucherFileFacade"  registry="batch-center-remote" check="false"/>

	<!-- 柜台收市预检查-->
	<dubbo:reference interface="com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckFacade" id="counterEndPreCheckFacade" registry="batch-center-remote" check="false"/>

	<!--查询基金账号信息-->
	<dubbo:reference interface="com.howbuy.tms.batch.facade.query.queryfundtxacct.QueryFundTxAcctFacade" id="queryFundTxAcctFacadeService" registry="batch-center-remote" check="false"/>

	<!--机构修改回款方向-->
	<dubbo:reference interface="com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionFacade" id="modifyRedeemDirectionFacade" registry="batch-center-remote" check="false"/>

	<!--机构修改回款方向-->
	<dubbo:reference interface="com.howbuy.tms.batch.facade.trade.modifyredeemdirection.applymodifyredeemdirection.ApplyModifyRedeemDirectionFacade" id="applyModifyRedeemDirectionFacade" registry="batch-center-remote" check="false"/>

	<!--删除换卡凭证资料-->
	<dubbo:reference interface="com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.DeleteVoucherFileFacade" id="deleteVoucherFileFacade" registry="batch-center-remote" check="false"/>
</beans>