/**
*修改分红方式修改
*<AUTHOR>
*@date 2018-03-27 11:10：01
**/
$(function(){
	CancelModify.init();
});

var CancelModify = {
	
	/**
	 * 初始化
	 */
	init:function(){
		// 初始化数据
		CancelModify.initData();
		
		// 查询订单信息
        Modify.queryCounterDealOrder(this.queryCounterDealOrderCallBack, null);
	},
		
	/**
	 * 初始化参数
	 */
	  initData:function(){
		  CancelModify.checkOrder = {};// 订单信息
		  CancelModify.custInfo = {};// 客户信息
		  CancelModify.modifyDeal = {};// 修改订单
	},
	
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		var counterOrder = bodyData.counterOrder || {};
		CancelModify.counterOrderDto = bodyData.counterOrderDto || {};//订单信息
		var checkOrder = CancelModify.counterOrderDto || {};//订单信息
		var sourceDealOrder = counterOrder.sourceDealOrderBean || {};//原订单
		var custInfofiList = bodyData.custInfofiList || [];//客户信息
		CancelModify.checkOrder = checkOrder;//柜台订单信息
		// 原订单信息
    	Modify.modifyDealOrder = checkOrder;
    	
		CancelModify.builDealInfo(checkOrder, sourceDealOrder);//购买订单信息
		if(custInfofiList.length > 0){
			CancelModify.custInfo = custInfofiList[0] || {};//客户信息
		}
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildCheckInfo(checkOrder);// 审核信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		var fundCode = checkOrder.fundCode;// 产品代码
		CancelModify.queryFundInfo(fundCode);// 查询产品信息
        var viewType = CommonUtil.getParam("viewType");
        if('0' != viewType){
            OnLineOrderFile.query(null, Modify.getSelectCustMaterial(bodyData, OnLineOrderFile.CRM_CANCEL),Modify.getCheckNode());// CRM线上资料
        }else{
            var orderFile = bodyData.orderFile || {};
            OnLineOrderFile.buildOrderFileHtml(orderFile);
        }
	},
	
	/**
	 * 赎回订单信息
	 */
	builDealInfo:function(checkOrder, sourceDealOrder){
		var trList = [];
		var selectUserCancelFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.USER_CANCEL_FLAG_MAP, checkOrder.forceCancelFlag);
		var selectCancelHtml = '<span class="select-box">'+
									'<select name="forceCancelFlag" class="select selectUserCancelFlag" >'+
										selectUserCancelFlagHtml+
									  '</select>'+
							     '</span>';
		//撤单信息
		trList.push(selectCancelHtml);//撤单方式
		trList.push(CommonUtil.formatData(checkOrder.dealNo));//原订单号
		trList.push(CommonUtil.formatData(checkOrder.fundCode));//基金代码
		trList.push(CommonUtil.formatData(checkOrder.fundName));//基金名称
		trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME_MAP,sourceDealOrder.mBusiCode,'--'));//中台业务码
		//原订单信息
		trList.push(CommonUtil.formatData(sourceDealOrder.appAmt,''));//申请金额
		trList.push(CommonUtil.formatData(sourceDealOrder.appVol,''));//申请份额
		trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP,sourceDealOrder.orderStatus));//订单状态
		trList.push(CommonUtil.getMapValue(CONSTANTS.PAY_STATUS_MAP,sourceDealOrder.payStatus));//支付状态
		trList.push(CommonUtil.formatDateToStr(sourceDealOrder.appDtm, 'yyyy-MM-dd hh:mm:ss'));//订单申请日期
		trList.push(CommonUtil.formatData(sourceDealOrder.submitTaDt));// 上报TA日期
		var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>')+'</td></tr>';
		$("#rsList").append(trHtml);
		
		// 初始化输入框状态
		CancelModify.initInputStatus(checkOrder);
	},
	/**
	 * 查询高端产品基本信息
	 * @param fundCode 产品代码
	 */
	queryFundInfo:function(fundCode){
		
		QueryHighProduct.queryFundInfo(fundCode);
	    CancelModify.fundInfo = QueryHighProduct.fundInfo || {};
		
		if(CommonUtil.isEmpty(CancelModify.fundInfo.fundCode)){
			CommonUtil.layer_tip("没有查询到此产品");
			return false;
		}
		//构建产品基本信息
		ViewCounterDeal.buildFundInfo(CancelModify.fundInfo);
	},
	/**
	 * 初始化输入框状态
	 * @param checkOrder
	 */
	initInputStatus:function(checkOrder){
		var viewType = CommonUtil.getParam("viewType");
		if('2' == viewType){
			// 可修改输入框
			var enableList = [];
			enableList.push($(".selectUserCancelFlag"));// 是否用户选择
			enableList.push($("#cancelMemo"));// 撤单原因
	    	CommonUtil.enabledList(enableList);
		}
		
	},
};