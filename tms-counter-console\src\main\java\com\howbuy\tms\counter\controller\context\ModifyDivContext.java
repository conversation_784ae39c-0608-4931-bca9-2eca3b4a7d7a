/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;

import com.howbuy.tms.counter.dto.FundDivDto;

import java.util.Date;

/**
 * @className ModifyDivContext
 * @description
 * <AUTHOR>
 * @date 2019/6/19 13:46
 */
public class ModifyDivContext extends TradeCommonContext {
    private String targetDiv;
    private FundDivDto fundDivDto;
    private Date appDtm;

    public String getTargetDiv() {
        return targetDiv;
    }

    public void setTargetDiv(String targetDiv) {
        this.targetDiv = targetDiv;
    }

    public FundDivDto getFundDivDto() {
        return fundDivDto;
    }

    public void setFundDivDto(FundDivDto fundDivDto) {
        this.fundDivDto = fundDivDto;
    }

    @Override
    public Date getAppDtm() {
        return appDtm;
    }

    @Override
    public void setAppDtm(Date appDtm) {
        this.appDtm = appDtm;
    }
}
