/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @description:(柜台份额合并或迁移提交DTO)
 * <AUTHOR>
 * @date 2018年5月8日 上午10:55:21
 * @since JDK 1.6
 */
public class CounterShareTransferVolReqDto extends OperInfoBaseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 2960865095941681429L;

    /**
     * 审核申请流水号
     */
    private String dealAppNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金简称
     */
    private String fundName;
    /**
     * 份额类型：A-前收费；B-后收费
     */
    private String fundShareClass;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 经办人证件号
     */
    private String transactorIdNo;

    /**
     * 经办人证件类型
     */
    private String transactorIdType;

    /**
     * 经办人姓名
     */
    private String transactorName;

    /**
     * 投资顾问代码
     */
    private String consCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;

    /**
     * 申请状态，0-未申请；1-申请通过；2-申请失败
     */
    private String appFlag;

    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是)
     */
    private String agentFlag;
    
    /**
     * 转入资金账号
     */
    private String inCpAcctNo;
    /**
     * 转入协议号
     */
    private String inProtocolNo;
    /**
     * 转入协议类型
     */
    private String inProtocolType;
    /**
     * 转入银行卡号
     */
    private String bankAcct;
    
    /**
     * 转入银行编码
     */
    private String bankCode;
    
    /**
     * 转出份额明细信息
     */
    private List<ShareMergeVolOrderReqDto> shareMergeVolOrderList;
    
    /**
     * 转入后总持有份额
     */
    private BigDecimal appVol;
    
    /**
     * 份额业务类型
     */
    private String shareType;
    
    /**
     * 交易通道
     */
    private String productChannel;
    /**
     * TACODE
     */
    private String taCode;
    /**
     * 份额迁移产品标识 0-全部 1-公募 2-高端
     */
    private String shareTransferFlag;
    /**
     * 高端迁移产品
     */
    private List<String> highFundCodes;
    
    /**
     * 转出资金账号
     */
    private String outCpAcctNo;
    
    /**
     * 转出资金账号列表
     */
    private List<String> outCpAcctNos;
    /**
     * 转出银行卡号
     */
    private String outBankAcct;
    /**
     * 转出银行编码
     */
    private String outBankCode;
    
    /**
     * 是否有公募持仓
     */
    private boolean hasFundVol = false;
    /**
     * 是否有储蓄罐持仓
     */
    private boolean hasPiggyVol = false;
    /**
     * 是否有高端持仓
     */
    private boolean hasHighVol = false;
    
    /**
     * 是否注销原卡：1-是，0-否
     */
    private String cancelCard;

    /**
     * 在途资产 JSON格式存储
     * @return
     */
    private String intransitAssetMemo;

    public List<String> getOutCpAcctNos() {
        return outCpAcctNos;
    }

    public void setOutCpAcctNos(List<String> outCpAcctNos) {
        this.outCpAcctNos = outCpAcctNos;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public String getInCpAcctNo() {
        return inCpAcctNo;
    }

    public void setInCpAcctNo(String inCpAcctNo) {
        this.inCpAcctNo = inCpAcctNo;
    }

    public String getInProtocolNo() {
        return inProtocolNo;
    }

    public void setInProtocolNo(String inProtocolNo) {
        this.inProtocolNo = inProtocolNo;
    }

    public String getInProtocolType() {
        return inProtocolType;
    }

    public void setInProtocolType(String inProtocolType) {
        this.inProtocolType = inProtocolType;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public List<ShareMergeVolOrderReqDto> getShareMergeVolOrderList() {
        return shareMergeVolOrderList;
    }

    public void setShareMergeVolOrderList(List<ShareMergeVolOrderReqDto> shareMergeVolOrderList) {
        this.shareMergeVolOrderList = shareMergeVolOrderList;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
    
    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getShareTransferFlag() {
        return shareTransferFlag;
    }

    public void setShareTransferFlag(String shareTransferFlag) {
        this.shareTransferFlag = shareTransferFlag;
    }

    public List<String> getHighFundCodes() {
        return highFundCodes;
    }

    public void setHighFundCodes(List<String> highFundCodes) {
        this.highFundCodes = highFundCodes;
    }

    public String getOutCpAcctNo() {
        return outCpAcctNo;
    }

    public void setOutCpAcctNo(String outCpAcctNo) {
        this.outCpAcctNo = outCpAcctNo;
    }

    public String getOutBankAcct() {
        return outBankAcct;
    }

    public void setOutBankAcct(String outBankAcct) {
        this.outBankAcct = outBankAcct;
    }

    public String getOutBankCode() {
        return outBankCode;
    }

    public void setOutBankCode(String outBankCode) {
        this.outBankCode = outBankCode;
    }

	public boolean isHasFundVol() {
		return hasFundVol;
	}

	public void setHasFundVol(boolean hasFundVol) {
		this.hasFundVol = hasFundVol;
	}

	public boolean isHasPiggyVol() {
		return hasPiggyVol;
	}

	public void setHasPiggyVol(boolean hasPiggyVol) {
		this.hasPiggyVol = hasPiggyVol;
	}

	public boolean isHasHighVol() {
		return hasHighVol;
	}

	public void setHasHighVol(boolean hasHighVol) {
		this.hasHighVol = hasHighVol;
	}

	public String getCancelCard() {
		return cancelCard;
	}

	public void setCancelCard(String cancelCard) {
		this.cancelCard = cancelCard;
	}

    public String getIntransitAssetMemo() {
        return intransitAssetMemo;
    }

    public void setIntransitAssetMemo(String intransitAssetMemo) {
        this.intransitAssetMemo = intransitAssetMemo;
    }
}
