/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.high.batch.facade.query.queryfixedredeemreport.QueryFixedRedeemReportFacade;
import com.howbuy.tms.high.batch.facade.query.queryfixedredeemreport.QueryFixedRedeemReportRequest;
import com.howbuy.tms.high.batch.facade.query.queryfixedredeemreport.QueryFixedRedeemReportResponse;
import com.howbuy.tms.high.batch.facade.query.queryfixedredeemreport.bean.FixedRedeemReportBean;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @className QueryFixedRedeemReportController
 * @description
 * <AUTHOR>
 * @date 2019/3/26 14:40
 */
@Controller
public class QueryFixedRedeemReportController {
    @Autowired
    private QueryFixedRedeemReportFacade queryFixedRedeemReportFacade;

	@RequestMapping("/tmscounter/queryfixedredeemreport.htm")
    public void queryFixedRedeemReport(HttpServletRequest request, HttpServletResponse response)throws Exception{
        String queryCondition = request.getParameter("queryCondition");
        String taCodes = request.getParameter("taCodes");
        String fundCodes = request.getParameter("fundCodes");
        String pageNoStr = request.getParameter("page");
        String pageSizeStr = request.getParameter("pageSize");

        pageNoStr = StringUtils.isEmpty(pageNoStr) ? "1" : pageNoStr;
        pageSizeStr = StringUtils.isEmpty(pageSizeStr) ? "1" : pageSizeStr;

        int pageNo = Integer.parseInt(pageNoStr);
        int pageSize = Integer.parseInt(pageSizeStr);

        QueryFixedRedeemReportRequest queryFixedRedeemReportRequest = new QueryFixedRedeemReportRequest();

        if(!StringUtils.isEmpty(queryCondition)){
            queryFixedRedeemReportRequest = JSON.parseObject(queryCondition, QueryFixedRedeemReportRequest.class);
            if(StringUtils.isNotEmpty(taCodes)){
                String[] taCodeArr = taCodes.split(",");
                queryFixedRedeemReportRequest.setTaCodes(Lists.newArrayList(taCodeArr));
            }

            if(StringUtils.isNotEmpty(fundCodes)){
                String[] fundCodeArr = fundCodes.split(",");
                queryFixedRedeemReportRequest.setFundCodes(Lists.newArrayList(fundCodeArr));
            }
        }

        queryFixedRedeemReportRequest.setPageNo(pageNo);
        queryFixedRedeemReportRequest.setPageSize(pageSize);
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        queryFixedRedeemReportRequest.setIsHBJGAuth(user.getInHBJG() ? YesOrNoEnum.NO.getCode():YesOrNoEnum.YES.getCode());
        QueryFixedRedeemReportResponse queryFixedRedeemReportResponse = queryFixedRedeemReportFacade.execute(queryFixedRedeemReportRequest);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        if(!ReturnCodeEnum.SUCC_TMS.getCode().equals(queryFixedRedeemReportResponse.getReturnCode())){
             rst.setCode(queryFixedRedeemReportResponse.getReturnCode());
             rst.setDesc(queryFixedRedeemReportResponse.getDescription());

        }
        // 脱敏
        if (!CollectionUtils.isEmpty(queryFixedRedeemReportResponse.getFixedRedeemReports())) {
            for (FixedRedeemReportBean bean : queryFixedRedeemReportResponse.getFixedRedeemReports()) {
                PrivacyUtil.resetCustInfoAndBankInfo(bean);
            }
        }

        Map<String, Object> body = new HashMap<>(16);

        body.put("respData", queryFixedRedeemReportResponse);
        body.put("pageNum", queryFixedRedeemReportResponse.getPageNo());
        body.put("totalPage", queryFixedRedeemReportResponse.getTotalPage());
        body.put("totalCount", queryFixedRedeemReportResponse.getTotalCount());
        rst.setBody(body);

        WebUtil.write(response, rst);
    }
}
