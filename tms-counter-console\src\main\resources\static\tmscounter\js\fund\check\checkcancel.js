$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckCancel.checkOrder = {};	 
	CheckCancel.init(checkId,custNo,disCode,idNo);
});

var CheckCancel = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,CheckCancel.queryCheckOrderByIdBack);
		
		$("#returnBtn").on('click',function(){
			CheckCancel.confirm(CounterCheck.Faild);
		});
		
		$("#succBtn").on('click',function(){
			CheckCancel.confirm(CounterCheck.Succ);
		});
	},
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';
		
		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckCancel.checkFaildDesc = $("#checkFaildDesc").val();
		}


		var reqparamters ={"checkFaildDesc":CheckCancel.checkFaildDesc || '',
				"checkStatus":checkStatus,
				"checkedOrderForm":JSON.stringify(CheckCancel.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckCancel.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckCancel.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(CheckCancel.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckCancel.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		
		QueryCanCancel.queryCanCancel(QueryCustInfo.custInfo.custNo, CheckCancel.checkOrder.dealNo,CheckCancel.checkOrder.operatorNo);
		
		$("#cancelType").html(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, CheckCancel.checkOrder.txCode, ''));
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(CheckCancel.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").html(CheckCancel.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckCancel.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(CheckCancel.checkOrder.transactorName);
		}
				
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckCancel.checkOrder.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(CheckCancel.checkOrder.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(CheckCancel.checkOrder.memo);
		}

		if($("#withdrawDirection").length > 0){
			$("#withdrawDirection").html(CommonUtil.getMapValue(CONSTANTS.WITHDRAW_DIR_ALL_MAP, CheckCancel.checkOrder.withdrawDirection));

		}
	},
}
