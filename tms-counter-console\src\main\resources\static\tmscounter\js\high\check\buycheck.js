/**
*购买审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
	if('crm' === CommonUtil.getParam("source")){
		viewType = '1';
	}

	CounterCheck.viewType = viewType;

	CounterCheck.initBtn(viewType);
	
	var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
	$(".selectconsCode").html(selectConsCodesHtml);

	// 查询订单
	CounterCheck.queryCounterDealOrder(viewType, BuyCheck.queryCounterDealOrderCallBack, null);
	// 金额格式化
	$("#appAmt").on("blur", function(){
		$("#appAmt").val(CommonUtil.formatAmount($("#appAmt").val()));
	});
	
});

var BuyCheck = {
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		var counterOrder = bodyData.counterOrder || {};
		CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};
		var checkOrder = CounterCheck.counterOrderDto || {};//订单信息
		var highProduct = counterOrder.counterHighProductBaseBean || {};//产品信息
		var productAppointInfo = counterOrder.counterProductAppointmentInfoBean || {};//产品预约开放日信息
		var orderFile = bodyData.orderFile || {};// CRM线上资料
		var appointList = bodyData.appointList || [];//投顾预约信息
		var custInfofiList = bodyData.custInfofiList || [];//客户信息
		BuyCheck.checkOrder = checkOrder;//柜台订单信息
		CounterCheck.checkOrder = checkOrder;//柜台订单信息
		
		BuyCheck.buildBuyDealInfo(checkOrder);//购买订单信息
		ViewCounterDeal.buildAppointmentInfo(appointList);//预约信息
		ViewCounterDeal.buildProductAppointmentInfo(productAppointInfo);//预约开放日历信息
		var applyAmount= CommonUtil.formatAmount($("#applyAmount").val())
		ViewCounterDeal.buildPayRatioAmount(productAppointInfo.payRatio, checkOrder.subsAmt,checkOrder.disCode,applyAmount);//缴款金额
		ViewCounterDeal.buildFundInfo(highProduct);//产品信息
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料
		
		//查询银行卡信息
		CounterCheck.queryCustBankInfo(checkOrder.txAcctNo,checkOrder.bankAcct, checkOrder.disCode);
		
	},
	
	
	
	/**
     * 购买订单信息
     * @param checkOrder
     */
    buildBuyDealInfo:function(checkOrder){
    	$("#fundCode").val(checkOrder.fundCode);//基金代码
    	var appAmt = (checkOrder.appAmt - checkOrder.esitmateFee).toFixed(2); //净申请金额
    	$("#applyAmount").val(CommonUtil.formatAmount(appAmt));//申请金额
    	var convertStr =  CommonUtil.digit_uppercase(appAmt);
    	$("#convertAmtId").html(convertStr);//大写金额
    	$("#discountRate").val(checkOrder.discountRate);//折扣
    	$("#originalFeeRate").val(checkOrder.feeRate);//费率
    	$("#feeId").html(CommonUtil.formatAmount(checkOrder.esitmateFee));//手续费
    	$("#applyAmountIncluFee").html(CommonUtil.formatAmount(checkOrder.appAmt));//总金额
    	$("#appDt").val(checkOrder.appDt);//申请日期
    	$("#appTm").val(checkOrder.appTm);//申请时间
    	$("#isRedeemExpire").val(checkOrder.isRedeemExpire);//是否到期赎回
    	$("#preExpireDate").val(checkOrder.preExpireDate);//预计到期日期
		$("#subsAmt").val(CommonUtil.formatAmount(checkOrder.subsAmt));//认缴金额
    }
	
};
