package com.howbuy.tms.counter.dto;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/1 11:04
 * @since JDK 1.8
 */
public class QueryCustomSellRatioResDto implements Serializable {

    private static final long serialVersionUID = 7276179514798242021L;
    private String txAcctNo;

    private String disCode;

    private String protocolNo;

    private String productCode;

    private BigDecimal redeemRatio;

    private String cpAcctNo;

    private String customOrRatio;

    /**
     * 巨额赎回顺延
     */
    private String largeRedmFlag;

    private String unusualTransType;

    /**
     * 赎回去向 0-储蓄罐（人工选择），1-回银行卡（人工选择），2-回银行卡（协议默认）,3-回储蓄罐（协议默认）
     */
    private String redeemCapitalFlag;

    /**
     * 开关
     */
    private String openFlag;

    private List<RedeemTrailResDto> redeemTrailResDtos;

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag;
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag;
    }

    public String getUnusualTransType() {
        return unusualTransType;
    }

    public void setUnusualTransType(String unusualTransType) {
        this.unusualTransType = unusualTransType;
    }

    public String getRedeemCapitalFlag() {
        return redeemCapitalFlag;
    }

    public void setRedeemCapitalFlag(String redeemCapitalFlag) {
        this.redeemCapitalFlag = redeemCapitalFlag;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public BigDecimal getRedeemRatio() {
        return redeemRatio;
    }

    public void setRedeemRatio(BigDecimal redeemRatio) {
        this.redeemRatio = redeemRatio;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getCustomOrRatio() {
        return customOrRatio;
    }

    public void setCustomOrRatio(String customOrRatio) {
        this.customOrRatio = customOrRatio;
    }

    public List<RedeemTrailResDto> getRedeemTrailResDtos() {
        return redeemTrailResDtos;
    }

    public void setRedeemTrailResDtos(List<RedeemTrailResDto> redeemTrailResDtos) {
        this.redeemTrailResDtos = redeemTrailResDtos;
    }
}