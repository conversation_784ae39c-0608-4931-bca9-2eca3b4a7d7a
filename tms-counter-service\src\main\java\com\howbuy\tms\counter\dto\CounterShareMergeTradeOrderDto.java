/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @description:(柜台份额合并或迁移交易订单信息DTO)
 * <AUTHOR>
 * @date 2018年5月14日 下午2:24:12
 * @since JDK 1.6
 */
public class CounterShareMergeTradeOrderDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = 8237839580808319399L;

    /**
     * 订单号
     */
    private String dealNo;
    
    /**
     * 客户号
     */
    private String txAcctNo;
    
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 中台业务名称
     */
    private String zBusiCode;
    
    /**
     * 中台详细业务名称
     */
    private String mBusiCode;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 基金名称
     */
    private String fundName;
    
    /**
     * 转出银行卡号
     */
    private String outBankAcct;
    
    /**
     * 转出协议号
     */
    private String outProtocolNo;

    /**
     * 转出卡资金账号
     */
    private String outCpAcctNo;

    /**
     * 申请份额
     */
    private BigDecimal appVol;
    
    /**
     * 转入银行卡号
     */
    private String inBankAcct;
    
    /**
     * 转入协议号
     */
    private String inProtocolNo;
    
    /**
     * 订单明细申请状态
     */
    private String txAppFlag;
    
    /**
     * 订单明细确认状态
     */
    private String txAckFlag;
    
    /**
     * 转入卡合并前可用份额
     */
    private BigDecimal beforeInAvailVol;
    
    /**
     * 转入卡合并后可用份额
     */
    private BigDecimal afterInAvailVol;
    
    /**
     * 交易返回结果
     */
    private String retDesc;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getzBusiCode() {
        return zBusiCode;
    }

    public void setzBusiCode(String zBusiCode) {
        this.zBusiCode = zBusiCode;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getOutBankAcct() {
        return outBankAcct;
    }

    public void setOutBankAcct(String outBankAcct) {
        this.outBankAcct = outBankAcct;
    }

    public String getOutProtocolNo() {
        return outProtocolNo;
    }

    public void setOutProtocolNo(String outProtocolNo) {
        this.outProtocolNo = outProtocolNo;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getInBankAcct() {
        return inBankAcct;
    }

    public void setInBankAcct(String inBankAcct) {
        this.inBankAcct = inBankAcct;
    }

    public String getInProtocolNo() {
        return inProtocolNo;
    }

    public void setInProtocolNo(String inProtocolNo) {
        this.inProtocolNo = inProtocolNo;
    }

    public String getTxAppFlag() {
        return txAppFlag;
    }

    public void setTxAppFlag(String txAppFlag) {
        this.txAppFlag = txAppFlag;
    }

    public String getTxAckFlag() {
        return txAckFlag;
    }

    public void setTxAckFlag(String txAckFlag) {
        this.txAckFlag = txAckFlag;
    }

    public BigDecimal getBeforeInAvailVol() {
        return beforeInAvailVol;
    }

    public void setBeforeInAvailVol(BigDecimal beforeInAvailVol) {
        this.beforeInAvailVol = beforeInAvailVol;
    }

    public BigDecimal getAfterInAvailVol() {
        return afterInAvailVol;
    }

    public void setAfterInAvailVol(BigDecimal afterInAvailVol) {
        this.afterInAvailVol = afterInAvailVol;
    }

    public String getRetDesc() {
        return retDesc;
    }

    public void setRetDesc(String retDesc) {
        this.retDesc = retDesc;
    }

    public String getOutCpAcctNo() {
        return outCpAcctNo;
    }

    public void setOutCpAcctNo(String outCpAcctNo) {
        this.outCpAcctNo = outCpAcctNo;
    }

}
