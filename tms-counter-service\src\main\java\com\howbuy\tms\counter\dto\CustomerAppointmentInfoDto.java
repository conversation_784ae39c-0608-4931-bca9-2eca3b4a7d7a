/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(预约信息)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年7月10日 上午9:17:57
 * @since JDK 1.6
 */
public class CustomerAppointmentInfoDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 2071625239691832058L;

    private String appointId;
    /**
     * 约产品代码
     */
    private String productCode;

    /**
     * 预约产品名称
     */
    private String productName;
    /***
     * 预约金额
     */
    private BigDecimal appAmt;
    /***
     * 预约份额
     */
    private BigDecimal appVol;
    /***
     * 预约折扣
     */
    private BigDecimal discountRate;
    /***
     * 预约单状态
     */
    private String orderStatus;
    /**
     * 基金风险等级
     */
    private String fundRiskLevel;
    /***
     * 基金简称
     */
    private String fundAttr;
    /***
     * 基金状态
     */
    private String fundStatus;
    /***
     * 预约业务
     */
    private String mBusiCode;
    /***
     * 预约日期
     */
    private String appointStartDt;
    /***
     * 预约时间
     */
    private String appointStartTm;

    /**
     * 持有份额 / 总份额
     */
    private BigDecimal balanceVol;
    /***
     * 可用份额
     */
    private BigDecimal availVol;
    /**
     * 可购买状态
     */
    private String buyStatus;
    
    /**
     * 预约类型
     */
    private String preType;
    
    /**
     * 产品类型
     */
    private String fundType;


    /**
     * 需双录标识：0-无需双录；1-需双录;
     */
    private String doubleNeedFlag;
    /**
     * 处理标识：0-无需处理；1-未处理；2-已处理
     */
    private String doubleHandleFlag;
    /**
     * 双录处理时间，带时间yyyyMMddHH24miss
     */
    private Date doubleHandleDt;
    /**
     * 是否首次实缴预约 0-否 1-是
     */
    private String firstPreId;

    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;

    public String getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(String buyStatus) {
        this.buyStatus = buyStatus;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getAvailVol() {
        return availVol;
    }

    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }

    public String getAppointId() {
        return appointId;
    }

    public void setAppointId(String appointId) {
        this.appointId = appointId == null ? null : appointId.trim();
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode == null ? null : mBusiCode.trim();
    }

    public String getAppointStartDt() {
        return appointStartDt;
    }

    public void setAppointStartDt(String appointStartDt) {
        this.appointStartDt = appointStartDt == null ? null : appointStartDt.trim();
    }

    public String getAppointStartTm() {
        return appointStartTm;
    }

    public void setAppointStartTm(String appointStartTm) {
        this.appointStartTm = appointStartTm == null ? null : appointStartTm.trim();
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(String fundStatus) {
        this.fundStatus = fundStatus;
    }

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getDoubleNeedFlag() {
        return doubleNeedFlag;
    }

    public void setDoubleNeedFlag(String doubleNeedFlag) {
        this.doubleNeedFlag = doubleNeedFlag;
    }

    public String getDoubleHandleFlag() {
        return doubleHandleFlag;
    }

    public void setDoubleHandleFlag(String doubleHandleFlag) {
        this.doubleHandleFlag = doubleHandleFlag;
    }

    public Date getDoubleHandleDt() {
        return doubleHandleDt;
    }

    public void setDoubleHandleDt(Date doubleHandleDt) {
        this.doubleHandleDt = doubleHandleDt;
    }

    public String getFirstPreId() {
        return firstPreId;
    }

    public void setFirstPreId(String firstPreId) {
        this.firstPreId = firstPreId;
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }
}
