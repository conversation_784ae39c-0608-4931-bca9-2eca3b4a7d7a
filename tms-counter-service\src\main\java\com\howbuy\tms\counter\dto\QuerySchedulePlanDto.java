/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * 
 * @description:(定投合约DTO)
 * 
 * <AUTHOR>
 * @date 2018年5月21日 下午2:30:48
 * @since JDK 1.6
 */
public class QuerySchedulePlanDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -6910925919504850077L;

    /**
     * 合约计划Id
     */
    private String scheId;

    /**
     * 客户号
     */
    private String custNo;
    /**
     * 计划类型<br>
     * 1-定存;2-定取;4-机器人定投;5-好买智投(暴力定投);6-普通定投;
     */
    private String scheType;
    /**
     * 计划状态：(1-正常；2-暂停；3-终止)
     */
    private String scheStatus;
    
    /**
     * 协议号
     */
    private String protocolNo;
    
    /**
     * 定投基金代码
     */
    private String fundCode;

    public String getScheId() {
        return scheId;
    }

    public void setScheId(String scheId) {
        this.scheId = scheId;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getScheType() {
        return scheType;
    }

    public void setScheType(String scheType) {
        this.scheType = scheType;
    }

    public String getScheStatus() {
        return scheStatus;
    }

    public void setScheStatus(String scheStatus) {
        this.scheStatus = scheStatus;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
    
}
