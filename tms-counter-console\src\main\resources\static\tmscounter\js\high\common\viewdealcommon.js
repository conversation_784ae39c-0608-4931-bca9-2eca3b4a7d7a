/**柜台交易查看公共
 *<AUTHOR>
 *@date 2018-02-23 14:24
 */
ViewDealCommon = {};
/**
 * 柜台订单查看url
 */
ViewDealCommon.txCodeUrlMap = {
		"Z900011":'../view/counterbuydeal.html',
		"Z900012":"../view/counterselldeal.html",
		"Z900013":"../view/countermodifydiv.html",
		"Z900014":"../view/countercancel.html",
		"Z900015":"../view/countercancel.html",
        "Z900048":"../view/countermodifyrepurchasedeal.html",
        "Z900054":"../view/counternotradeoveraccount.html",
		"Z900063":"../view/countermodifyrefunddirection.html",
		"Z900065":"../view/counterownershiprighttransfer.html",
	    "Z900068":"../view/counterSubsAmtChangeDetail.html"
};


/**
 * 修改订单查看URL
 */
ViewDealCommon.txCodeUrlModifyMap = {
		"Z900011":'../view/modify/modifybuydeal.html',
		"Z900012":"../view/modify/modifyselldeal.html",
		"Z900013":"../view/modify/modifymodifydiv.html",
		"Z900014":"../view/modify/modifycancel.html",
		"Z900015":"../view/modify/modifycancel.html",
        "Z900048":"../view/modify/modifyrepurchasedeal.html",
        "Z900054":"../view/modify/modifynotradeoveraccount.html",
		"Z900063":"../view/modify/modifyrefunddirection.html",
	    "Z900065":"../view/modify/modifyownershiprighttransfer.html",
	    "Z900068":"../view/modify/modifySubsAmtChangeDetail.html"
};

/**
 * 构建查询参数
 * @param params 数组
 */
ViewDealCommon.buildParams = function(params){
	if(CommonUtil.isEmpty(params) || params.length <=0 ){
		return "v=1";
	}

	return params.join("&");
},


/**
 * 获取查看url
 */
ViewDealCommon.getGetViewUrl = function(txCode, params){
	
	var url = CommonUtil.getMapValue(ViewDealCommon.txCodeUrlMap, txCode);
	console.log("txCode:"+txCode+" ,"+"url:"+url);
	return url+"?"+params;
};

/**
 * 获取修改查看url
 */
ViewDealCommon.getGetModifyViewUrl = function(txCode, params){
	
	var url = CommonUtil.getMapValue(ViewDealCommon.txCodeUrlModifyMap, txCode);
	console.log("txCode:"+txCode+" ,"+"url:"+url);
	return url+"?"+params;
};

/**
 *显示订单
 */
ViewDealCommon.showDeal = function(viewUrl){
	
	var checkLayerIndex = layer.open({
		  type: 2,
		  skin: 'layui-layer-demo', //样式类名
		  closeBtn: 1, //不显示关闭按钮 0-不显示 1-显示
		  anim: 2,
		  maxmin: true,
		  shadeClose: true, //开启遮罩关闭
		  content: viewUrl
	});
	
	//全屏显示
	layer.full(checkLayerIndex);
};


	
