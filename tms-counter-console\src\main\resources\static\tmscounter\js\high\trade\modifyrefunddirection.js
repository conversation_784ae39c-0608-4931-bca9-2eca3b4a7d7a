/**
*柜台交易查询
*<AUTHOR>
*@date 2017-04-11 16:17
*/

$(function(){
	ModifyRefundDirection.order = {};
	ModifyRefundDirection.orders = [];
	ModifyRefundDirection.init();
});

var ModifyRefundDirection ={
	/**
	 * 初始化
	 */
	init:function(){
		$("#queryTradeBtn").on('click',function(){
			ModifyRefundDirection.query();
		});
		$("#confirmBtn").on('click',function(){
			ModifyRefundDirection.confirm();
		});
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});

        //回款去向
        var refundDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.WITHDRAW_DIR_ALL_MAP);
        $("#refundDirection").html(refundDirectionHtml);
        // 回可用备注
        $("#selectRedeemMemo").html(CommonUtil.selectOptionsHtml(CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP));
        $("#selectRedeemMemo").on('change', function () {
            ModifyRefundDirection.refundMemoSelectOnChange();
        });
	},

	/**
	 * 柜台交易查询
	 */
	query : function(){
			var  uri= TmsCounterConfig.QUERY_COUNTER_MODIFY_REFUND_DIRECTION_URL ||  {};

			var custNo = $("#custNo").val();
			if (CommonUtil.isEmpty(custNo)) {
				layer.alert("请输入客户号");
				return;
			}
			var reqparamters  = {};
			var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
			var queryOrderCondition = {};
			$.each(queryOrderConditionForm,function(name,value){
				if(!CommonUtil.isEmpty(value)){
					queryOrderCondition[name] = value;
				}
			});
			reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
			reqparamters.page = 1;
			reqparamters.pageSize = 50;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
			CommonUtil.ajaxPaging(uri, paramters,  ModifyRefundDirection.callBack, "pageView");
	},

	callBack:function(data){
		ModifyRefundDirection.orders = data.orders || [];

		$("#rsList").html('');
		if(ModifyRefundDirection.orders.length <=0){
			var trHtml = '<tr><td colspan="15">没有查询到交易</td></tr>';
			$("#rsList").append(trHtml);
			return false;
		}
		$(ModifyRefundDirection.orders).each(function(index,element){
			var trList = [];
			trList.push(CommonUtil.formatData(element.taTradeDt));
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custName));
			trList.push(CommonUtil.formatData(element.idNo));
			trList.push(CommonUtil.formatData(element.productCode));
			trList.push(CommonUtil.formatData(element.productName));
			trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME, element.mBusiCode, ''));
			trList.push(CommonUtil.formatData(element.dealNo));
			trList.push(CommonUtil.formatData(element.appAmt));
			trList.push(CommonUtil.formatData(element.appVol));
			trList.push(CommonUtil.formatData(element.ackAmt));
			trList.push(CommonUtil.formatData(element.ackVol));
			trList.push(CommonUtil.formatData(element.appDate));
			trList.push(CommonUtil.formatData(element.appTime));
			trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP, element.orderStatus, ''));
			var trHtml = '<tr class="text-c"><td><input type="radio" class="selectCancelOrder" name="orderIndex" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td></tr>';
			$("#rsList").append(trHtml);
		});

		$(".selectCancelOrder").off();
		$(".selectCancelOrder").on('click', function () {
			var selectedOrderIndex = $(this).val();
			var element = ModifyRefundDirection.orders[selectedOrderIndex];
			ModifyRefundDirection.order = element;
			$("#orderStatusId").html(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP, element.orderStatus, ''));
			$("#refundAmtId").html(element.refundAmt);
			$("#refundMemoId").html(element.refundMemo);

			if ((element.mBusiCode == '1122' || element.mBusiCode == '1120') && (element.orderStatus == '5' || element.orderStatus == '6')) {
                $("#refundDirectionId").html(CommonUtil.getMapValue(CONSTANTS.WITHDRAW_DIR_ALL_MAP, element.withdrawDirection, ''));
			} else {
                $("#refundDirectionId").html(CommonUtil.getMapValue(CONSTANTS.REDEEM_DIRECTION_MAP, element.redeemDirection, ''));
			}

            // 显示回可用配置项
            var payType = element.paymentType || '';
            ModifyRefundDirection.refundDirectionOnChange(element.mBusiCode, payType);
		});
	},

    /**
     * 回可用备注选择
     * @param redeemDirection
     */
    refundDirectionOnChange: function(mBusiCode, payType) {
		$("#newRefundAmtId").html("");
		$("#newRefundMemoId").html("");
		// 回可用备注
		$("#selectRedeemMemo").html("");
		if ((mBusiCode == '1122' || mBusiCode == '1120') && ("01" == payType || "04" == payType) || mBusiCode == '1124') {
			$("#newRefundDirectionId").html(ModifyRefundDirection.generateDirection());
			//回款去向
			if((mBusiCode == '1122' || mBusiCode == '1120') && ("01" == payType)){
				// 认申购-自划款-回款去向
				var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.WITHDRAW_DIR_BUY_SELFDRAWING_MAP);
				$("#withdrawDirection").html(withdrawDirectionHtml);
			}else {
				var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.WITHDRAW_DIR_ALL_MAP);
				$("#withdrawDirection").html(withdrawDirectionHtml);
			}

			$("#withdrawDirection").on('change', function () {
				var dir = $(this).val();
				if (dir == '5' || dir == '6' || dir == '7') {
					if (dir == '6' || dir == '7') {
						$("#newRefundAmtId").html(ModifyRefundDirection.generateAmt());
					}else{
						$("#newRefundAmtId").html("");
					}
					$("#newRefundMemoId").html(ModifyRefundDirection.generateMemo());
					// 回可用备注
					$("#selectRedeemMemo").html(CommonUtil.selectOptionsHtml(CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP));
					$("#selectRedeemMemo").on('change', function () {
						ModifyRefundDirection.refundMemoSelectOnChange();
					});
				}else {
					$("#newRefundAmtId").html("");
					$("#newRefundMemoId").html("");
					// 回可用备注
					$("#selectRedeemMemo").html("");
				}
			});
		}
    },

    /**
     * 回可用备注
     * @param redeemDirection
     */
    refundMemoSelectOnChange: function() {
        var memoSelect = $("#selectRedeemMemo").val();
        if ("1" == memoSelect) {
            $("#refundMemo").html('<input id="selectRefundMemo" class="" name="refundFinaAvailMemo" isnull="false" datatype="s">');
        }
        if ("0" == memoSelect) {
            $("#refundMemo").html('<select id="selectRefundMemo" name="refundFinaAvailMemo" class="select" isnull="false" datatype="s">');
            // 初始化基金代码多选
            CommonUtil.singleSelectForRefund("selectRefundMemo",TmsCounterConfig.HIGH_QUERY_PRODUCT_CODEANDNAME_URL, ModifyRefundDirection.order.productChannel);
        }
    },

    generateDirection:function() {
        var table = '<span class="select-box inline">' +
            '						<select id="withdrawDirection" name="withdrawDirection" class="select" isnull="false" datatype="s"></select>' +
            '					</span>';
        return table;
    },
    generateAmt:function() {
        var table = '<input id="refundFinaAvailAmt" name="refundFinaAvailAmt" isnull="false" datatype="s" errormsg="回可用金额">';
        return table;
    },
    generateMemo:function() {
        var table = '<span class="select-box inline">' +
                    '	<select id="selectRedeemMemo" class="select" isnull="false" datatype="s"></select>' +
                    '</span>'+
                    '<span class="select-box inline" id="refundMemo"></span>';
        return table;
    },

	/***
	 * 确认修改
	 */
	confirm : function(){
		var flag = ModifyRefundDirection.refundValid();
		if (!flag) {
			return;
		}
		var uri= TmsCounterConfig.COUNTER_MODIFY_REFUND_DIRECTION_URL ||  {};
		var refundForm = $("#refundForm").serializeObject();

		var reqparamters ={"counterOrderForm":JSON.stringify(ModifyRefundDirection.order),
			"refundForm":JSON.stringify(refundForm),"dbFlag":"1"};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ModifyRefundDirection.confirmCallBack);
	},
	confirmCallBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			layer.confirm('提交成功', {
				btn: ['确定'] //按钮
			}, function(){
				layer.closeAll();
				// 下单成功，刷新页面
				if(OnLineOrderFile.isCrm()){
					CommonUtil.closeCurrentUrl();
				}else{
					// 刷新页面
					CommonUtil.reloadUrl();
				}
			});
		}else{
			CommonUtil.layer_tip("提交失败,"+respDesc);
		}
	},

	refundValid:function(){
		var order = ModifyRefundDirection.order;
		var mBusiCode = order.mBusiCode;
		var orderStatus = order.orderStatus;
		var realRefundAmt = null;
		// 购买
		if ("1120" == mBusiCode || "1122" == mBusiCode) {
			// 申请成功or确认成功
			if ("1" == orderStatus || "3" == orderStatus) {
				layer.alert("订单状态为撤单、部分确认、确认失败才允许修改");
				return false;
			}
			realRefundAmt = order.appAmt;
			// 部分确认
			if ("2" == orderStatus) {
				realRefundAmt = order.appAmt - order.ackAmt;
			}
		}
		if ("1124" == mBusiCode) {
			// 申请成功or确认成功
			if ("2" == orderStatus || "3" == orderStatus) {
				realRefundAmt = order.ackAmt;
			}
		}

		var length = $("#withdrawDirection").length;
		if (length > 0) {
			var withdrawDirection = $("#withdrawDirection").val();
			if (withdrawDirection == "5" || withdrawDirection == "6" || withdrawDirection == "7") {
				if (withdrawDirection == "6" || withdrawDirection == "7") {
					var refundAmtInput = $("#refundFinaAvailAmt").val();
					var refundAmt = CommonUtil.unFormatAmount(refundAmtInput);
					// 检查是否数字
					if (refundAmt == "" || !CommonUtil.validFloat(refundAmt)) {
						layer.alert("回可用余额金额只能输入数字");
						return false;
					}

					if (realRefundAmt != null && refundAmt > realRefundAmt) {
						layer.alert("回可用余额金额不能大于可退款金额");
						return false;
					}
				}

				var refundMemo = $("#selectRefundMemo").val();
				if (refundMemo == null || $.trim(refundMemo) == '') {
					layer.alert("回可用余额备注不能为空");
					return false;
				}
			}
		}
		return true;
	},
};
