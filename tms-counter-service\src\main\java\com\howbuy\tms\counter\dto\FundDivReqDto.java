/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(基金分红方式)
 * <AUTHOR>
 * @date 2017年4月1日 下午4:01:16
 * @since JDK 1.6
 */
public class FundDivReqDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 9010654893324291378L;
    /** 基金简称. **/
    private String fundAttr;
    /** 基金代码. **/
    private String fundCode;

    /** 分红方式. 0-红利再投，1-现金红利 **/
    private String divMode;

    /** 份额类型. A-前收费；B-后收费 **/
    private String shareClass;

    /** 是否准许修改分红方式 :0-不准许修改，1-准许修改 **/
    private String allowModifyDivMode;

    /** 预计确认日期 **/
    private String expCnfmDt;

    /**
     * 基金状态：0-交易；1-发行；2-发行成功；3-发行失败；4-停止交易；5-停止申购；6-停止赎回；7-权益登记；8-红利发放；9-基金封闭；
     */
    private String fundStat;
    /**
     * 目标修改分红方式
     */
    private String targetDiv;
    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;
    
    public String getAppDt() {
		return appDt;
	}

	public void setAppDt(String appDt) {
		this.appDt = appDt;
	}

	public String getAppTm() {
		return appTm;
	}

	public void setAppTm(String appTm) {
		this.appTm = appTm;
	}

	public String getTargetDiv() {
        return targetDiv;
    }

    public void setTargetDiv(String targetDiv) {
        this.targetDiv = targetDiv;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getDivMode() {
        return divMode;
    }

    public void setDivMode(String divMode) {
        this.divMode = divMode;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public String getAllowModifyDivMode() {
        return allowModifyDivMode;
    }

    public void setAllowModifyDivMode(String allowModifyDivMode) {
        this.allowModifyDivMode = allowModifyDivMode;
    }

    public String getExpCnfmDt() {
        return expCnfmDt;
    }

    public void setExpCnfmDt(String expCnfmDt) {
        this.expCnfmDt = expCnfmDt;
    }

}
