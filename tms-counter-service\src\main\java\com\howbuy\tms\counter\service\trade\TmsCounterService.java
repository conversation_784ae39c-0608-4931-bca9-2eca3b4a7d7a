/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.service.trade;

import java.util.List;

import com.howbuy.interlayer.product.model.HighActiDiscountRatioModel;
import com.howbuy.tms.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoResponse;
import com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckResponse;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoResponse;
import com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckResponse;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoResponse;
import com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatResponse;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountResponse;
import com.howbuy.tms.high.batch.facade.query.querytabusinesslist.QueryHighTaBusinessListResponse;
import com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCountNotEndResponse;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionResponse;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.bean.CounterModifyRefundDirectionBean;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundResponse;

/**
 * @description:(中台柜台控制台服务)
 * <AUTHOR>
 * @date 2017年3月28日 下午8:49:54
 * @since JDK 1.6
 */
public interface TmsCounterService {

    /***
     * 
     * queryCustBaseInfoSub:(子页面查询客户信息)
     * 
     * @param queryCustBaseInfoReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月3日 下午4:19:44
     */
    public QueryCustBaseInfoResponse queryCustBaseInfoSub(QueryCustBaseInfoReqDto queryCustBaseInfoReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 查询客户基本信息
     * @param queryCustBaseInfoReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    public QueryCustBaseInfoRespDto queryCustBaseInfo(QueryCustBaseInfoReqDto queryCustBaseInfoReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * CounterPurchase:(高端柜台购买)
     * 
     * @param counterPurchaseReqDto
     * @return
     * <AUTHOR>
     * @date 2017年3月31日 上午9:21:51
     */
    public CounterPurchaseRespDto counterPurchase(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * CounterPurchase:(柜台购买)
     * 
     * @param counterPurchaseReqDto
     * @return
     * <AUTHOR>
     * @date 2017年3月31日 上午9:21:51
     */
    public CounterPurchaseRespDto counterPurchaseForFund(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterRedeem:(高端柜台赎回)
     * 
     * @param counterRedeemReqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年3月31日 上午9:52:58
     */
    public CounterRedeemRespDto counterRedeem(CounterRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 高端非交易过户
     * @param counterReqDto
     * @param disInfoDto
     * @return com.howbuy.tms.counter.dto.CounterNoTradeOverAccountRespDto
     * @author: huaqiang.liu
     * @date: 2020/9/24 16:41
     * @since JDK 1.8
     */
    CounterNoTradeOverAccountRespDto counterNoTradeOverAccount(CounterNoTradeOverAccountReqDto counterReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterModifyDiv:(高端柜台修改分红方式)
     * 
     * @param counterModifyDivReqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年3月31日 上午10:01:58
     */
    public CounterModifyDivRespDto counterModifyDiv(CounterModifyDivReqDto counterModifyDivReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterForceCancel:(高端柜台强制撤单)
     * 
     * @param reqDto
     * @return
     * <AUTHOR>
     * @date 2017年4月14日 下午8:18:53
     */
    public CounterForceCancelRespDto counterForceCancel(CounterForceCancelReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterQueryOrder:(柜台订单查询)
     * 
     * @param counterQueryOrderReqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年3月31日 上午10:41:27
     */
    public CounterQueryOrderRespDto counterQueryOrder(CounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryCanCancelOrder:(查询可撤单订单)
     * 
     * @param custNo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午3:55:33
     */
    public List<OrderDto> queryCanCancelOrder(String custNo, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryCanCancelOrder:(查询可强撤单订单)
     * 
     * @param custNo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午3:55:33
     */
    public List<OrderDto> queryCanForceCancelOrder(String custNo, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * exitUnCheckOrder:(存在未审核订单)
     * @param dealNo
     * @param txAcctNo
     * @param txCode
     * @return
     * <AUTHOR>
     * @date 2018年3月30日 下午3:02:44
     */
    public boolean exitUnCheckOrder(String dealNo, String txAcctNo, String txCode);
    /**
     * 
     * queryFundDiv:(查询基金分红方式)
     * 
     * @param custNo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午4:06:32
     */
    public List<FundDivDto> queryFundDiv(String custNo, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryFundBalDtl:(查询基金持仓明细)
     * 
     * @param reqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月12日 下午8:07:00
     */
    public QueryAcctBalanceDtlRespDto queryAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * querySubBalanceDtl:查询子账本明细)
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月12日 下午3:54:46
     */
    public QueryAcctBalanceDtlRespDto querySubBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    /**
     * 
     * queryAcctBalance:(查询客户持仓)
     * 
     * @param reqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月12日 下午8:41:02
     */
    public QueryAcctBalanceRespDto queryAcctBalance(QueryAcctBalanceReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * checkOrder:(高端审核确认)
     * @param  submitUncheckOrderDto 审核订单信息
     * @param  checkType 审核类型 
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午6:31:36
     */
    public boolean checkOrder(SubmitUncheckOrderDto submitUncheckOrderDto, String checkType, DisInfoDto disInfoDto) throws Exception;
   
    /**
     * 
     * ModifyCheckOrder:(修改柜台订单)
     * @param submitUncheckOrderDto
     * @param checkType
     * @param disInfoDto
     * @return
     * <AUTHOR>
     * @date 2018年3月26日 下午4:59:18
     */
    public boolean modifyCheckOrder(SubmitUncheckOrderDto submitUncheckOrderDto, String checkType, DisInfoDto disInfoDto) throws Exception;
    
    /**
     *  queryCounterTrade:(查询柜台统计交易)
     * 
     * @param reqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月11日 下午4:00:56
     */
    public QueryCounterTradeRespDto queryCounterTrade(QueryCounterTradeReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * isCounterEnd:(查询控制台跑批节点，校验是否收市)
     * @param sysCode 系统码 91-公募 ;90-高端；95-定期
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月14日 下午4:11:37
     */
    public boolean isCounterEnd(String sysCode, String taCode, DisInfoDto disInfoDto) throws Exception;

    public void adviserCancelValid(DisInfoDto disInfoDto, String partnerCode, String protocolType);
    /**
     * 
     * getBatchStat:查询节点状态
     * @param sysCode
     * @param taskId
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月27日 下午4:19:54
     */
    public QueryHighBatchFlowInfoResponse.BatchFlowBean getBatchStat(String sysCode, String taskId) throws Exception;

    public void counterEndCheckFacade();

    /**
     * 
     * counterEnd:(私募柜台收市)
     * 
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月22日 上午8:59:48
     */
    public boolean simuCounterEnd() throws Exception;
    
    /**
     * 
     * getSystemWorkDay:(查询系统工作日)
     * 
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月14日 下午4:12:26
     */
    public String getSystemWorkDay(DisInfoDto disInfoDto) throws Exception;
    
    /**
     *
     * getSystemWorkDay:(获取系统工作日)
     * @param sysCode 系统码 91-公募 92-私募 93-高端公募
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年3月17日 下午1:36:04
     */
    public String getSystemWorkDay(String sysCode, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * getHighSystemWorkDay:(获取高端系统工作日)
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年9月25日 下午8:12:04
     */
    public String getHighSystemWorkDay() throws Exception;
    
    /**
     * 
     * getCounterWorkDay:(查询柜台工作日)
     * 
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年5月10日 下午4:36:37
     */
    public String getCounterWorkDay(String sysCode, DisInfoDto disInfoDto) throws Exception;

    /***
     * 
     * calDiscountRate:(计算折扣率)
     * 
     * @param feeReqDto
     * @param custInfoDto
     * @param fundCode
     * @param bankCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月18日 下午3:11:12
     */
    public FeeDto calDiscountRate(FeeReqDto feeReqDto, CustInfoDto custInfoDto, String fundCode, String bankCode, String mBusiCode) throws Exception;

    /***
     * 
     * calDiscountRate:(计算折扣率)
     * 
     * @param feeReqDto
     * @param custInfoDto
     * @param fundCode
     * @param bankCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月18日 下午3:11:12
     */
    public FeeDto calDiscountRateForFund(FeeReqDto feeReqDto, CustInfoDto custInfoDto, String fundCode, String bankCode, String mBusiCode) throws Exception;

    /**
     * 
     * getBatchFlowInfo:(查询中台批处理节点信息)
     * @param taskId
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月25日 下午1:13:33
     */
    public QueryBatchFlowInfoResponse getBatchFlowInfo(String taskId, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * counterHighShareMergeVol:(专户柜台份额合并或迁移申请提交)
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月8日 上午10:43:27
     */
    public CounterShareMergeVolRespDto counterHighShareMergeVol(CounterShareMergeVolReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * queryHighShareMergeTradeOrder:(查询份额合并或迁移交易订单信息)
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月14日 下午2:44:54
     */
    public List<CounterShareMergeTradeOrderDto> queryHighShareMergeTradeOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 查询储蓄罐订单信息
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    List<CounterShareMergeTradeOrderDto> queryPiggyShareMergeTradeOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * checkVolMergeTransOrder:(份额合并或迁移审核)
     * @param submitUncheckOrderDto
     * @param dtlOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月22日 下午1:42:53
     */
    public boolean checkVolMergeTransOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 份额迁移
     * @param submitUncheckOrderDto
     * @param dtlOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    public BaseResponse checkVolShareTransOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * querySubBalanceList:柜台赎回列表
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年7月19日 上午9:36:45
     */
    QueryAcctBalanceDtlRespDto querySubBalanceList(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * getHighActiDiscountRatio:查询活动折扣
     * @param fundCode
     * @param shareClass
     * @param paySource
     * @param invstType
     * @param businessCode
     * @param bankCode
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2018年8月27日 下午1:46:28
     */
    public HighActiDiscountRatioModel getHighActiDiscountRatio(String fundCode, String shareClass, String paySource, String invstType, String businessCode, String bankCode, String disCode);


    /**
     * getBatchFlowStatList:查询批处理流程节点状态
     * @return
     * @throws Exception
     */
    public QueryBatchFlowStatResponse getBatchFlowStatList() throws Exception;
    /**
     * 
     * queryTaBusinessList:查询TALIST
     * @param taskId
     * @param flowStat
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月17日 上午11:09:02
     */
    public QueryHighTaBusinessListResponse queryTaBusinessList(String taskId, String flowStat) throws Exception;
    /**
     * 
     * queryTaBusinessBatchCount:查询TA处理汇总信息
     * @param taskId
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月17日 下午4:17:56
     */
    public QueryTaBusinessBatchCountResponse queryTaBusinessBatchCount(String taskId) throws Exception;
    /**
     * 
     * queryTaCounterNotEnd:查询柜台不收市TA
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月19日 下午6:37:56
     */
    public QueryTaCountNotEndResponse queryTaCounterNotEnd() throws Exception;
    /**
     * 
     * counterSaveOrDelNotEndTa:不收市TA维护
     * @param actionType
     * @param sysCode
     * @param taDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月19日 下午6:54:59
     */
    public CounterRespDto counterSaveOrDelNotEndTa(String actionType, String sysCode, List<FundTaInfoDto> taDtoList, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * queryCustCurTaDtAckVol: 查询客户当前TA交易日确认份额
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2019年1月15日 下午3:58:34
     */
    public QueryCurTaDtAckResponse queryCustCurTaDtAckVol(String txAcctNo, String cpAcctNo, String fundCode, String protocolNo, String disCode) throws Exception;

    /**
     *
     * @Description 校验是否重复使用预约或者材料
     *
     * @param condition
     * @param disInfoDto
     * @return void
     * <AUTHOR>
     * @Date 2019/6/26 17:03
     **/
    public void validateReplyOrder(CounterOrderDto condition, DisInfoDto disInfoDto) throws  Exception;


    /**
     * 审核线上份额迁移订单
     * @param submitUncheckOrderDto
     * @param materialDtlDtoList
     * @return
     */
    BaseResponse checkVolOnlineTransOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<ExchangeCardMaterialDtlDto> materialDtlDtoList);

    /**
     * 保存银行凭证文件
     * @param dealAppNo
     * @param fileName
     * @param fileBytes
     * @return
     */
    BaseResponse saveVoucherFile(String dealAppNo, String materialType, String fileName, byte[] fileBytes);

    /**
     * 删除银行凭证文件
     * @param dealDtlAppNo 订单号
     * @return
     */
    BaseResponse deleteVoucherFile(String dealDtlAppNo);

    CounterEndPreCheckResponse queryCounterEndPreCheck(String code);

    /**
     * @description:审核修改折扣订单
     * @param submitUncheckOrderDto
     * @return com.howbuy.tms.common.client.BaseResponse
     * @author: dejun.gu
     * @date: 2021/2/25 18:17
     * @since JDK 1.8
     */
    BaseResponse checkChangeDiscountOrder(SubmitUncheckOrderDto submitUncheckOrderDto);

    /**
     * @description:查询订单
     * @param request
     * @return com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderRefundResponse
     * @author: chuanguang.tang
     * @date: 2021/8/3 10:26
     * @since JDK 1.8
     */
    QueryDealOrderRefundResponse queryDealOrder(QueryDealOrderRefundRequest request) throws Exception;

    /**
     * @description:修改回款方向
     * @param bean
     * @return com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionResponse
     * @author: chuanguang.tang
     * @date: 2021/8/4 16:12
     * @since JDK 1.8
     */
    CounterModifyRefundDirectionResponse modifyRefundDir(CounterModifyRefundDirectionBean bean, DisInfoDto disInfoDto) throws Exception;

    /**
     * 查询柜台订单
     * @param dealAppNo 申请单号
     * @param inHbJg 是否是黑名单用户  true  黑名单用户,false  非黑名单用户
     * @return 柜台订单
     */
    CounterOrderDto queryCounterOrder(String dealAppNo,boolean inHbJg ) throws Exception;


    /**
     * 校验购买下单信息
     * @param appDt 申请日期
     * @param appTm 申请时间
     * @param counterOrderDto 柜台订单
     * @return 校验结果
     * @throws Exception
     */
    CheckBuyInfoResultDto checkBuyInfo(String appDt, String appTm,CounterOrderDto counterOrderDto) throws Exception;

}
