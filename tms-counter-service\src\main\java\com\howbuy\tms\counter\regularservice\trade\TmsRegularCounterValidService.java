/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularservice.trade;

import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;

/**
 * @description:(校验服务)
 * <AUTHOR>
 * @date 2017年4月17日 下午8:27:38
 * @since JDK 1.6
 */
public interface TmsRegularCounterValidService {
    /**
     * 
     * subsOrPurValidateForFund:(定期购买校验)
     * 
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年6月22日 上午11:27:27
     */
    boolean subsOrPurValidate(RegularCounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception;



    /**
     * 
     * cancelOrderValidate:(定期撤单校验)
     * 
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年6月22日 上午11:27:27
     */
    boolean cancelOrderValidate(RegularCounterCancelReqDto dto, DisInfoDto disInfoDto) throws Exception;


}
