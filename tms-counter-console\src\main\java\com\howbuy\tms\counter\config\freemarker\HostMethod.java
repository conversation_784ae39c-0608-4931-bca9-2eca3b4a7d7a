package com.howbuy.tms.counter.config.freemarker;

import freemarker.template.SimpleScalar;
import freemarker.template.TemplateMethodModelEx;
import freemarker.template.TemplateModelException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RefreshScope
public class HostMethod implements TemplateMethodModelEx {
	
    private static HostMethod instance;
    
    public static HostMethod getInstance(){
		return instance;
	}
	
	@Value("${common.domain}")
	private String domain;
	
	@Value("${common.howbuyHost}")
	private String howbuyHost;
	
	@Value("${common.simuHowbuyHost}")
	private String simuHowbuyHost;
	
	@Value("${common.ehowbuyHost}")
	private String ehowbuyHost;
	
	@Value("${common.tradeHowbuyHost}")
	private String tradeHowbuyHost;
	
	@Value("${common.staticHowbuyHost}")
	private String staticHowbuyHost;
	
	@Value("${common.staticEHowbuyHost}")
	private String staticEHowbuyHost;
	
	@Value("${common.mhowbuyHost}")
	private String mhowbuyHost;

	@Value("${common.smOrghost}")
	private String smOrghost;

	@Value("${common.hwcmsHost}")
    private String hwcmsHost;
    
	@Value("${common.memberHost}")
    private String memberHost;

	@Value("${common.hwsHost}")
    private String hwsHost;

	@Value("${common.hgSta}")
    private String hgSta;

	@Value("${common.regHost}")
    private String regHost;

	@Value("${common.imagesHowbuyHost}")
    private String imagesHowbuyHost;

	@Value("${common.cssHowbuyHost}")
    private String cssHowbuyHost;

	@Value("${common.jsHowbuyHost}")
    private String jsHowbuyHost;

	@Value("${common.httpsStaticHowbuyHost}")
    private String httpsStaticHowbuyHost;

	@Value("${common.cmsHost}")
    private String cmsHost;
    
	
	public HostMethod(){
		instance = this;
	}
	
	public String getDomain() {
		return domain;
	}

	public String getHowbuyHost() {
		return howbuyHost;
	}

	public String getSimuHowbuyHost() {
		return simuHowbuyHost;
	}

	public String getEhowbuyHost() {
		return ehowbuyHost;
	}

	public String getTradeHowbuyHost() {
		return tradeHowbuyHost;
	}

	public String getStaticHowbuyHost() {
		return staticHowbuyHost;
	}

	public String getStaticEHowbuyHost() {
		return staticEHowbuyHost;
	}

	public String getMhowbuyHost() {
		return mhowbuyHost;
	}

	public String getSmOrghost() {
		return smOrghost;
	}

	public String getHwcmsHost() {
		return hwcmsHost;
	}

	public String getMemberHost() {
		return memberHost;
	}

	public String getHwsHost() {
		return hwsHost;
	}

	public String getHgSta() {
		return hgSta;
	}

	public String getRegHost() {
		return regHost;
	}

	public String getImagesHowbuyHost() {
		return imagesHowbuyHost;
	}

	public String getCssHowbuyHost() {
		return cssHowbuyHost;
	}

	public String getJsHowbuyHost() {
		return jsHowbuyHost;
	}

	public String getHttpsStaticHowbuyHost() {
		return httpsStaticHowbuyHost;
	}

	public String getCmsHost() {
		return cmsHost;
	}


	@Override
	public Object exec(List args) throws TemplateModelException {
		if(args == null || args.isEmpty() || args.size()<1){
			return SimpleScalar.EMPTY_STRING;
		}
		String key = ((SimpleScalar) args.get(0)).getAsString();
		if(StringUtils.isEmpty(key)){
			throw new TemplateModelException("key is null!");
		}
		if ("domain".equals(key)) {
			return domain;
		}else if ("howbuy".equals(key)) {
			return howbuyHost;
		}else if("simuHowbuy".equals(key)){
			return simuHowbuyHost;
		}else if("ehowbuy".equals(key)){
			return ehowbuyHost;
		}else if("mhowbuy".equals(key)){
			return mhowbuyHost;
		}else if("staticHowbuy".equals(key)){
			return staticHowbuyHost;
		}else if("staticEHowbuy".equals(key)){
			return staticEHowbuyHost;
		}else if("tradeHowbuy".equals(key)){
			return tradeHowbuyHost;
		}else if("smOrghost".equals(key)){
			return smOrghost;
        }else if("hwcms".equals(key)){
            return hwcmsHost;
        }else if("hws".equals(key)){
            return hwsHost;		
        }else if("hgSta".equals(key)){
            return hgSta;		
        }else if("memberHost".equals(key)){
        	return memberHost;
        }else if("regHost".equals(key)){
        	return regHost;
        }else if("imagesHowbuyHost".equals(key)){
        	return imagesHowbuyHost;
        }else if("cssHowbuyHost".equals(key)){
        	return cssHowbuyHost;
        }else if("jsHowbuyHost".equals(key)){
        	return jsHowbuyHost;
        }else if("httpsStaticHowbuyHost".equals(key)){
        	return httpsStaticHowbuyHost;
        }else if("cmsHost".equals(key)){
        	return cmsHost;
        }
		return SimpleScalar.EMPTY_STRING;
	}

}
