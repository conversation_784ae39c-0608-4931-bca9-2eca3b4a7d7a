<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	
	<dubbo:reference id="regularityService" interface="com.howbuy.cc.ucenter.ragularity.service.RegularityService" registry="tms-counter-console" check="false"/>
		
	<!-- 根据一帐通查询客户信息 -->
	<dubbo:reference id="tmscounter.querySingleService" interface="com.howbuy.cc.hbone.service.QuerySingleService" check="false" registry="acc-center-server" />
	
	<!-- 查询资产证明状态 -->
	<dubbo:reference id="counter.queryCurrentAssetCertificateStatusService" interface="com.howbuy.cc.center.feature.asset.service.QueryCurrentAssetCertificateStatusService" registry="acc-center-server" check="false" />

    <!-- 查询资产证明列表 -->
    <dubbo:reference id="queryAssetCertificateListService" interface="com.howbuy.cc.center.feature.asset.service.QueryAssetCertificateListService" registry="acc-center-server" check="false" />

</beans>