<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
    <script type="text/javascript" src="lib/html5.js"></script>
    <script type="text/javascript" src="lib/respond.min.js"></script>
    <script type="text/javascript" src="lib/PIE_IE678.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/style.css" />
    <title>非交易过户审核(高端)</title>
</head>

<body>
<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易审核<span class="c-gray en">&gt;</span> 非交易过户审核 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>

<div class="page-container w1000">
    <p class="main_title">转出方客户持仓信息</p>
    <div class="result2_tab">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
            <thead>
            <tr class="text-c">
                <!--                        <th>选择</th>-->
                <th>基金代码</th>
                <th>基金名称</th>
                <th>开户银行</th>
                <th>银行卡号</th>
                <th>总份额（份）</th>
                <th>可用份额（份）</th>
                <th>冻结份额（份）</th>
            </tr>
            </thead>
            <tbody id="dtlList">
            </tbody>
        </table>
    </div>

    <form action="" id="orderFormId">
        <p class="main_title mt30">录入订单信息</p>
        <span class="normal_span ml30">业务类型：</span>
        <span class="select-box inline">
               <select name="busiType" class="select" id="selectBusiType">
                   <option value="Z900054" >非交易过户申请</option>
               </select>
            </span>
        <div class="result2_tab" style="padding-top: 10px">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                <tr>
                    <td class="main_title mt30" colspan="2">转出方信息:</td>
                    <td class="main_title mt30" colspan="2">转入方信息:</td>
                </tr>
                <tr>
                    <td>转出方客户号：</td>
                    <td>
                        <input type="text" name="txAcctNo" id="outCustNo" isnull="false" datatype="s" errormsg="转出方客户号" readonly="readonly">
                    </td>
                    <td>转入方客户号：</td>
                    <td>
                        <input type="text" name="inTxAcctNo" id="inCustNo" isnull="false" datatype="s" errormsg="转入方客户号" placeholder="双击查询客户号">
                    </td>
                </tr>
                <tr>
                    <td>转出方证件号码：</td>
                    <td>
                        <input type="text" name="idNo" id="outIdNo" isnull="false" datatype="s" errormsg="转出方证件号码" readonly="readonly">
                    </td>
                    <td>转入方证件号码：</td>
                    <td>
                        <input type="text" name="inIdNo" id="inIdNo" isnull="false" datatype="s" errormsg="转入方证件号码" readonly="readonly">
                    </td>
                </tr>
                <tr>
                    <td>转出方证件类型：</td>
                    <td>
                        <input type="text" id="outIdTypeName" isnull="false" datatype="s" errormsg="转出方证件类型" readonly="readonly">
                        <input type="hidden" name="idType" id="outIdType" isnull="false" datatype="s" errormsg="转出方证件类型代码">
                    </td>
                    <td>转入方证件类型：</td>
                    <td>
                        <input type="text" id="inIdTypeName" isnull="false" datatype="s" errormsg="转入方证件类型" readonly="readonly">
                        <input type="hidden" name="inIdType" id="inIdType" isnull="false" datatype="s" errormsg="转入方证件类型代码">
                    </td>
                </tr>
                <tr>
                    <td>转出方客户名称：</td>
                    <td>
                        <input type="text" name="custName" id="outCustName" isnull="false" datatype="s" errormsg="转出方客户名称" readonly="readonly">
                    </td>
                    <td>转入方客户名称：</td>
                    <td>
                        <input type="text" name="inCustName" id="inCustName" isnull="false" datatype="s" errormsg="转入方客户名称" readonly="readonly">
                    </td>
                </tr>
                <tr>
                    <td>转出方银行卡号：</td>
                    <td>
                        <input type="text" name="bankAcct" id="outBankAcctNo" isnull="false" datatype="s" errormsg="转出方银行卡号" readonly="readonly">
                        <input type="hidden" name="cpAcctNo" id="outCpAcctNo" isnull="false" datatype="s" errormsg="转出方资金账号" readonly="readonly">
                    </td>
                    <td>转入方银行卡号：</td>
                    <td>
                        <input type="text" name="inBankAcct" id="inBankAcctNo" isnull="false" datatype="s" errormsg="转入方银行卡号" readonly="readonly">
                        <input type="hidden" name="inCpAcctNo" id="inCpAcctNo" isnull="false" datatype="s" errormsg="转入方资金账号" readonly="readonly">
                    </td>
                </tr>
                <tr>
                    <td>基金代码：</td>
                    <td colspan="3">
                        <input type="text" name="fundCode" id="fundCode" isnull="false" datatype="s" errormsg="基金代码" readonly="readonly">
                    </td>
                </tr>
                <tr>
                    <td>过户份额：</td>
                    <td>
                        <input type="text" name="appVol" id="appVol" isnull="false" datatype="s" errormsg="过户份额">（份）
                    </td>
                    <td>过户份额（大写）：</td>
                    <td id="appVolUpper">
                    </td>
                </tr>
                <tr>
                    <td>过户份额对应的认缴金额：</td>
                    <td>
                        <input type="text" name="subsAmt" id="subsAmt" isnull="false" datatype="s" errormsg="过户份额对应的认缴金额">（元）
                    </td>
                    <td>过户份额对应的认缴金额（大写）：</td>
                    <td id="subsAmtUpper">
                    </td>
                </tr>
                <tr>
                    <td>过户的总认缴金额：</td>
                    <td>
                        <input type="text" name="totalSubsAmt" id="totalSubsAmt" isnull="true" datatype="s" errormsg="过户的总认缴金额">（元）
                    </td>
                    <td>过户的总认缴金额（大写）：</td>
                    <td id="totalSubsAmtUpper">
                    </td>
                </tr>
                <tr>
                    <td>转让价格：</td>
                    <td>
                        <input type="text" name="transferPrice" id="transferPrice" isnull="false" datatype="s" errormsg="转让价格">（元）
                    </td>
                    <td>转让价格（大写）：</td>
                    <td id="transferPriceUpper">
                    </td>
                </tr>
                <tr>
                    <td>下单日期：</td>
                    <td>
                        <input type="text" name="appDt" id="appDt" isnull="false" datatype="s" maxlength = "8" errormsg="下单日期" readonly="readonly">
                    </td>
                    <td>下单时间：</td>
                    <td>
                        <input type="text" name="appTm" id="appTm" isnull="false" datatype="s" maxlength = "6" errormsg="下单时间">
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </form>

    <form action="" id="checkResult">
        <div class="checkIn mt20">
            <p class="reCheckTitle">复核信息</p>
            <span>&nbsp;驳回原因：<input type="text" placeholder='请输入驳回原因，可选' name="checkFaildDesc">（仅审核驳回使用）</span>
        </div>
    </form>

    <p class="mt30 text-c" id="submitDiv">
        <a href="javascript:void(0)" id ="modifyBtn" class="btn radius btn-warning ml30">提交</a>
        <a href="javascript:void(0)" id ="cancelBtn" class="btn radius btn-secondary ml30">作废</a>
        <a href="javascript:void(0)" id ="backBtn" class="btn radius btn-success ml30">返回</a>
    </p>
</div>

<script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
<script type="text/javascript" src="../../../../lib/layer/2.1/layer.js"></script>
<script type="text/javascript" src="../../../../lib/laydate/laydate.js"></script>
<script type="text/javascript" src="../../../../lib/laypage/laypage.js"></script>
<script type="text/javascript" src="../../../../static/h-ui/js/H-ui.js"></script>
<script type="text/javascript" src="../../../../static/h-ui.admin/js/H-ui.admin.js"></script>
<script type="text/javascript" src="../../../../static/h-ui.admin/js/main.js"></script>
<script type="text/javascript" src="../../../../js/baseconfig.js"></script>
<script type="text/javascript" src="../../../../js/common.js"></script>
<script type="text/javascript" src="../../../../js/config.js"></script>
<script type="text/javascript" src="../../../../js/commonutil.js?v=1"></script>
<script type="text/javascript" src="../../../../js/valid.js"></script>
<script type="text/javascript" src="../../../../js/high/conscode.js"></script>
<script type="text/javascript" src="../../../../js/high/query/queryhighproduct.js"></script>
<script type="text/javascript" src="../../../../js/high/common/onlineorderfile.js"></script>
<script type="text/javascript" src="../../../../js/high/modify/modify.js?v=4.1.11"></script>
<script type="text/javascript" src="../../../../js/high/modify/modifynotradeoveraccount.js?v=1"></script>
<script type="text/javascript" src="../../../../js/high/common/init.js"></script>
</body>

</html>