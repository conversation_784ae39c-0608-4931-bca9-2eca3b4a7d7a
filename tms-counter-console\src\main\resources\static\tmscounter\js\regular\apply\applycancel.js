$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	RegularApplyCancel.checkOrder = {};
	RegularApplyCancel.init(checkId,custNo,disCode,idNo);
});

var RegularApplyCancel = {
	init:function(checkId, custNo, disCode,idNo){
		RegularQueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		RegularQueryCheckOrder.queryCheckOrderById(checkId,RegularApplyCancel.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
            RegularCounterAbolish.abolish(TmsCounterConfig.CHECK_REGULAR_CONFIRM_URL, RegularCounterCheck.Abolish, RegularApplyCancel.checkOrder);
		});
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		RegularApplyCancel.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(RegularApplyCancel.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(RegularApplyCancel.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		
		QueryCanCancel.queryCanCancel(QueryCustInfo.custInfo.custNo, RegularApplyCancel.checkOrder.dealNo, RegularApplyCancel.checkOrder.operatorNo);
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(RegularApplyCancel.checkOrder.agentFlag);
		}
		
		var txCode = RegularApplyCancel.checkOrder.txCode;
		if($("#cancelType").length > 0){
			if('Z910045' == txCode){
				$("#cancelType").val(1);
			}else {
				$("#cancelType").val(2);
			}
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").val(RegularApplyCancel.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(RegularApplyCancel.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, RegularApplyCancel.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(RegularApplyCancel.checkOrder.transactorName);
		}

		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(RegularApplyCancel.checkOrder.transactorIdNo);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(parseInt(RegularApplyCancel.checkOrder.transactorIdType));
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(RegularApplyCancel.checkOrder.memo);
		}
		
	},

}
