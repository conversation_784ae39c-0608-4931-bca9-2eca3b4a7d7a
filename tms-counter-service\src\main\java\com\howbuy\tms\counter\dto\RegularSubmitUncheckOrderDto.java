/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseRequestDto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(柜台提交审核订单)
 * <AUTHOR>
 * @date 2017年3月28日 下午8:58:29
 * @since JDK 1.6
 */
public class RegularSubmitUncheckOrderDto extends BaseRequestDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 1L;

    private String dealAppNo;

    private String txAcctNo;

    private String subTxAcctNo;

    private String cpAcctNo;

    private String productCode;

    private BigDecimal appAmt;

    private BigDecimal appVol;

    private String appDt;

    private String appTm;

    private String txCode;

    private String paymentType;

    private String protocolType;

    private String protocolNo;

    private String checkFlag;

    private String dealNo;

    private String transactorIdNo;

    private String transactorIdType;

    private String transactorName;

    private String operatorNo;

    private String consCode;

    private String returnCode;

    private String description;

    private String memo;

    private String creator;

    private String checker;

    private String modifier;

    private String appFlag;

    private String custName;

    private String productName;

    private String agentFlag;

    private String idNo;

    private Date createDtm;

    private Date checkDtm;

    private Date updateDtm;

    private String bankAcct;

    private String bankCode;

    private String taTradeDt;

    private BigDecimal nav;

    private String unusualTransType;

    private String riskFlag;

    private BigDecimal appointmentDiscount;

    private String appointmentDealNo;

    private BigDecimal advanceAmt;

    private String idType;

    private BigDecimal appointmentAmt;
    
    /**
     * 强制撤单标识(柜台校验必传): 0-强制; 1-非强制
     */
    private String forceCancelFlag;
    
    /**
     * 预约类型0-系统生成 1-投顾生成
     */
    private String appointmentDealNoType;
    
    /**
     * 撤单原因
     */
    private String cancelMemo;

    public BigDecimal getAppointmentAmt() {
        return appointmentAmt;
    }

    public void setAppointmentAmt(BigDecimal appointmentAmt) {
        this.appointmentAmt = appointmentAmt;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo == null ? null : dealAppNo.trim();
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo == null ? null : txAcctNo.trim();
    }

    public String getSubTxAcctNo() {
        return subTxAcctNo;
    }

    public void setSubTxAcctNo(String subTxAcctNo) {
        this.subTxAcctNo = subTxAcctNo == null ? null : subTxAcctNo.trim();
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo == null ? null : cpAcctNo.trim();
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt == null ? null : appDt.trim();
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm == null ? null : appTm.trim();
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode == null ? null : txCode.trim();
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType == null ? null : paymentType.trim();
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType == null ? null : protocolType.trim();
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo == null ? null : protocolNo.trim();
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag == null ? null : checkFlag.trim();
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo == null ? null : dealNo.trim();
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo == null ? null : transactorIdNo.trim();
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType == null ? null : transactorIdType.trim();
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName == null ? null : transactorName.trim();
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo == null ? null : operatorNo.trim();
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode == null ? null : consCode.trim();
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode == null ? null : returnCode.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker == null ? null : checker.trim();
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag == null ? null : appFlag.trim();
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag == null ? null : agentFlag.trim();
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo == null ? null : idNo.trim();
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getCheckDtm() {
        return checkDtm;
    }

    public void setCheckDtm(Date checkDtm) {
        this.checkDtm = checkDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct == null ? null : bankAcct.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt == null ? null : taTradeDt.trim();
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getUnusualTransType() {
        return unusualTransType;
    }

    public void setUnusualTransType(String unusualTransType) {
        this.unusualTransType = unusualTransType == null ? null : unusualTransType.trim();
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag == null ? null : riskFlag.trim();
    }

    public BigDecimal getAppointmentDiscount() {
        return appointmentDiscount;
    }

    public void setAppointmentDiscount(BigDecimal appointmentDiscount) {
        this.appointmentDiscount = appointmentDiscount;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo == null ? null : appointmentDealNo.trim();
    }

    public BigDecimal getAdvanceAmt() {
        return advanceAmt;
    }

    public void setAdvanceAmt(BigDecimal advanceAmt) {
        this.advanceAmt = advanceAmt;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType == null ? null : idType.trim();
    }

    public String getForceCancelFlag() {
        return forceCancelFlag;
    }

    public void setForceCancelFlag(String forceCancelFlag) {
        this.forceCancelFlag = forceCancelFlag;
    }

    public String getAppointmentDealNoType() {
        return appointmentDealNoType;
    }

    public void setAppointmentDealNoType(String appointmentDealNoType) {
        this.appointmentDealNoType = appointmentDealNoType;
    }

    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
}
