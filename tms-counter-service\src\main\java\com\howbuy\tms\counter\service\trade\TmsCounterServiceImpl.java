/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.service.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.ftxonline.facade.piggy.trade.ftxvolmigrate.FtxVolMigrateRequest;
import com.howbuy.ftxonline.facade.piggy.trade.ftxvolmigrate.FtxVolMigrateResponse;
import com.howbuy.ftxonlinesearch.facade.piggy.query.querypiggyorderdetail.QueryPiggyOrderDetailFacade;
import com.howbuy.ftxonlinesearch.facade.piggy.query.querypiggyorderdetail.QueryPiggyOrderDetailRequest;
import com.howbuy.ftxonlinesearch.facade.piggy.query.querypiggyorderdetail.QueryPiggyOrderDetailResponse;
import com.howbuy.interlayer.common.enums.AdviserSplitFlagEnum;
import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.PortfolioPartnerInfoService;
import com.howbuy.tms.batch.facade.enums.BatchStatEnum;
import com.howbuy.tms.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.batch.facade.enums.FlowStatEnum;
import com.howbuy.tms.batch.facade.enums.WorkdayTypeEnum;
import com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoFacade;
import com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoRequest;
import com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoResponse;
import com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckFacade;
import com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckRequest;
import com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckResponse;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoFacade;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoRequest;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoResponse;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.bean.CustBaseInfoBean;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlFacade;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlRequest;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlResponse;
import com.howbuy.tms.batch.facade.query.querytabatchflowinfo.QueryTaBatchFlowInfoFacade;
import com.howbuy.tms.batch.facade.query.querytabatchflowinfo.QueryTaBatchFlowInfoRequest;
import com.howbuy.tms.batch.facade.query.querytabatchflowinfo.QueryTaBatchFlowInfoResponse;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayFacade;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayRequest;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayResponse;
import com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckFacade;
import com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckRequest;
import com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckResponse;
import com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeFacade;
import com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeRequest;
import com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeResponse;
import com.howbuy.tms.batch.facade.trade.countersharemerge.bean.CounterShareMergeOrderBean;
import com.howbuy.tms.batch.facade.trade.countersharemerge.bean.CounterShareMergeOrderBean.ShareMergeOrderDetail;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.UploadVoucherFileFacade;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.UploadVoucherFileRequest;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.DeleteVoucherFileFacade;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.DeleteVoucherFileRequest;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionFacade;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionRequest;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.bean.RedeemDirectionBean;
import com.howbuy.tms.batch.facade.trade.submitcheckorder.SubmitCheckOrderFacade;
import com.howbuy.tms.batch.facade.trade.submitcheckorder.SubmitCheckOrderRequest;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.cpAcctswitch.CpAcctSwitchContext;
import com.howbuy.tms.common.outerservice.acccenter.cpAcctswitch.CpAcctSwitchOutService;
import com.howbuy.tms.common.outerservice.acccenter.cpAcctswitch.CpAcctSwitchResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.ftxonline.volmigrate.VolMigrateOuterService;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.querysavingboxvol.QuerySavingBoxVolOutService;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.querysavingboxvoldetail.QuerySavingBoxVolDetailOuterService;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.querysavingboxvoldetail.QuerySavingBoxVolDetailResult;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.counter.common.*;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.fundservice.trade.HighProductAppointService;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.aspect.BusinessAspect;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.remote.TradeInvoker;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto.DtlBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.enums.CheckTypeEnum;
import com.howbuy.tms.counter.service.orderplan.OrderPlanService;
import com.howbuy.tms.counter.service.orderplan.api.trade.RelieveSchePlanRequest;
import com.howbuy.tms.high.batch.facade.query.counterendcheck.CounterEndCheckFacade;
import com.howbuy.tms.high.batch.facade.query.counterendcheck.CounterEndCheckRequest;
import com.howbuy.tms.high.batch.facade.query.counterendcheck.CounterEndCheckResponse;
import com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoFacade;
import com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoRequest;
import com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoResponse;
import com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoResponse.BatchFlowBean;
import com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatFacade;
import com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatRequest;
import com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatResponse;
import com.howbuy.tms.high.batch.facade.query.querycounterorder.QueryCounterOrderFacade;
import com.howbuy.tms.high.batch.facade.query.querycounterorder.QueryCounterOrderRequest;
import com.howbuy.tms.high.batch.facade.query.querycounterorder.QueryCounterOrderResponse;
import com.howbuy.tms.high.batch.facade.query.querycounterorder.bean.QueryCounterOrderCondition;
import com.howbuy.tms.high.batch.facade.query.querycounterorder.bean.QueryCounterOrderRespBean;
import com.howbuy.tms.high.batch.facade.query.querycountertrade.QueryCounterTradeFacade;
import com.howbuy.tms.high.batch.facade.query.querycountertrade.QueryCounterTradeRequest;
import com.howbuy.tms.high.batch.facade.query.querycountertrade.QueryCounterTradeResponse;
import com.howbuy.tms.high.batch.facade.query.querycountertrade.bean.CounterTradeBean;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.QueryHighFundDealOrderDtlChangeCardFacade;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.QueryHighFundDealOrderDtlChangeCardRequest;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.QueryHighFundDealOrderDtlChangeCardResponse;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.bean.HighFundDealOrderDtlChangeCardBean;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.bean.QueryHighFundDealOrderDtlChangeCardCondition;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountFacade;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountRequest;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountResponse;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchflow.QueryHighTaBatchFlowInfoFacade;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchflow.QueryHighTaBatchFlowInfoRequest;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchflow.QueryHighTaBatchFlowInfoResponse;
import com.howbuy.tms.high.batch.facade.query.querytabusinesslist.QueryHighTaBusinessListFacade;
import com.howbuy.tms.high.batch.facade.query.querytabusinesslist.QueryHighTaBusinessListRequest;
import com.howbuy.tms.high.batch.facade.query.querytabusinesslist.QueryHighTaBusinessListResponse;
import com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCountNotEndResponse;
import com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCounterNotEndFacade;
import com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCounterNotEndRequest;
import com.howbuy.tms.high.batch.facade.query.queryworkday.QueryHighWorkdayFacade;
import com.howbuy.tms.high.batch.facade.query.queryworkday.QueryHighWorkdayRequest;
import com.howbuy.tms.high.batch.facade.query.queryworkday.QueryHighWorkdayResponse;
import com.howbuy.tms.high.batch.facade.trade.countercheck.CounterCheckFacade;
import com.howbuy.tms.high.batch.facade.trade.countercheck.CounterCheckRequest;
import com.howbuy.tms.high.batch.facade.trade.countercheck.bean.CounterCheckOrderBean;
import com.howbuy.tms.high.batch.facade.trade.counterend.CounterEndRequest;
import com.howbuy.tms.high.batch.facade.trade.counterforcecancel.CounterForceCancelFacade;
import com.howbuy.tms.high.batch.facade.trade.counterforcecancel.CounterForceCancelRequest;
import com.howbuy.tms.high.batch.facade.trade.counterforcecancel.CounterForceCancelResponse;
import com.howbuy.tms.high.batch.facade.trade.counterforcecancel.bean.CounterForceCancelOrderBean;
import com.howbuy.tms.high.batch.facade.trade.countermodifydiv.CounterModifyDivFacade;
import com.howbuy.tms.high.batch.facade.trade.countermodifydiv.CounterModifyDivRequest;
import com.howbuy.tms.high.batch.facade.trade.countermodifydiv.CounterModifyDivResponse;
import com.howbuy.tms.high.batch.facade.trade.countermodifydiv.bean.CounterModifyDivOrderBean;
import com.howbuy.tms.high.batch.facade.trade.counternotradeoveraccount.CounterNoTradeOverAccountFacade;
import com.howbuy.tms.high.batch.facade.trade.counternotradeoveraccount.CounterNoTradeOverAccountRequest;
import com.howbuy.tms.high.batch.facade.trade.counternotradeoveraccount.CounterNoTradeOverAccountResponse;
import com.howbuy.tms.high.batch.facade.trade.counternotradeoveraccount.bean.CounterNoTradeOverAccountOrderBean;
import com.howbuy.tms.high.batch.facade.trade.counterpurchase.CounterPurchaseFacade;
import com.howbuy.tms.high.batch.facade.trade.counterpurchase.CounterPurchaseRequest;
import com.howbuy.tms.high.batch.facade.trade.counterpurchase.CounterPurchaseResponse;
import com.howbuy.tms.high.batch.facade.trade.counterpurchase.bean.CounterPurchaseOrderBean;
import com.howbuy.tms.high.batch.facade.trade.counterredeem.CounterRedeemFacade;
import com.howbuy.tms.high.batch.facade.trade.counterredeem.CounterRedeemRequest;
import com.howbuy.tms.high.batch.facade.trade.counterredeem.CounterRedeemResponse;
import com.howbuy.tms.high.batch.facade.trade.counterredeem.bean.CounterRedeemOrderBean;
import com.howbuy.tms.high.batch.facade.trade.modifyownershiprighttransferface.ModifyOwnershipRightTransferFace;
import com.howbuy.tms.high.batch.facade.trade.modifyownershiprighttransferface.bean.ModifyOwnershipRightTransferRequest;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionFacade;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionRequest;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionResponse;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.bean.CounterModifyRefundDirectionBean;
import com.howbuy.tms.high.batch.facade.trade.noTradeUpdateSubscribeAmtInfo.NoTradeUpdateSubscribeAmtInfoFacade;
import com.howbuy.tms.high.batch.facade.trade.noTradeUpdateSubscribeAmtInfo.NoTradeUpdateSubscribeAmtInfoRequest;
import com.howbuy.tms.high.batch.facade.trade.noTradeUpdateSubscribeAmtInfo.NoTradeUpdateSubscribeAmtInfoResponse;
import com.howbuy.tms.high.batch.facade.trade.subsAmtChangeCheck.SubsAmtChangeCheckFacade;
import com.howbuy.tms.high.batch.facade.trade.subsAmtChangeCheck.SubsAmtChangeCheckRequest;
import com.howbuy.tms.high.batch.facade.trade.tacounternotend.CounterSaveOrDelNotEndTaFacade;
import com.howbuy.tms.high.batch.facade.trade.tacounternotend.CounterSaveOrDelNotEndTaRequest;
import com.howbuy.tms.high.batch.facade.trade.tacounternotend.CounterSaveOrDelNotEndTaResponse;
import com.howbuy.tms.high.batch.facade.trade.tacounternotend.bean.CounterNotEndTaBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlResponse.BalanceDtlBean;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListResponse.CancelOrderBean;
import com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivFacade;
import com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivRequest;
import com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivResponse;
import com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivResponse.CustFundDivBean;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundResponse;
import com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListResponse.ForcesCancelOrderBean;
import com.howbuy.tms.high.orders.facade.search.queryhighintransit.QueryHighInTransitFacade;
import com.howbuy.tms.high.orders.facade.search.queryhighintransit.QueryHighInTransitRequest;
import com.howbuy.tms.high.orders.facade.search.queryhighintransit.QueryHighInTransitResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterResponse.RedeemListCounterBean;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceResponse.SubBalanceDtlBean;
import com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder.ForcedCancelOrderFacade;
import com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder.ForcedCancelOrderRequest;
import com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcounter.ModifyDivCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcounter.ModifyDivCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcounter.ModifyDivCounterResponse;
import com.howbuy.tms.high.orders.facade.trade.modifyrefund.ModifyRefundFacade;
import com.howbuy.tms.high.orders.facade.trade.modifyrefund.ModifyRefundRequest;
import com.howbuy.tms.high.orders.facade.trade.modifyrepurchaseprotocol.ModifyRepurchaseProtocolFacade;
import com.howbuy.tms.high.orders.facade.trade.modifyrepurchaseprotocol.ModifyRepurchaseProtocolRequest;
import com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcounter.NoTradeOverAccountCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcounter.NoTradeOverAccountCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcounter.NoTradeOverAccountCounterResponse;
import com.howbuy.tms.high.orders.facade.trade.redeem.bean.RedeemDetailBean;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterResponse;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.BaseShareMergeRequest.ShareMergeOutDetail;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergecounter.HighShareMergeCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergecounter.HighShareMergeCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergecounter.HighShareMergeCounterResponse;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfercounter.HighShareTransferCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfercounter.HighShareTransferCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfercounter.HighShareTransferCounterResponse;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.bean.PayInfoBean;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterResponse;
import com.howbuy.tms.orders.search.facade.query.querygmintransit.QueryGmInTransitFacade;
import com.howbuy.tms.orders.search.facade.query.querygmintransit.QueryGmInTransitRequest;
import com.howbuy.tms.orders.search.facade.query.querygmintransit.QueryGmInTransitResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(柜台控制台服务)
 * @date 2017年3月28日 下午8:54:26
 * @since JDK 1.6
 */
@Service("tmsCounterService")
public class TmsCounterServiceImpl implements TmsCounterService {

    private static final Logger logger = LogManager.getLogger(TmsCounterServiceImpl.class);
    // 默认下单截止时间
    private static final String DEFAULT_TIME = "150000";
    // 默认下单截止时间-1
    private static final String BEFORE_DEFAULT_TIME = "145959";
    @Autowired
    private QueryCustBaseInfoFacade queryCustBaseInfoFacade;
    @Autowired
    private CounterPurchaseFacade counterPurchaseFacade;
    @Autowired
    private CounterRedeemFacade counterRedeemFacade;
    @Autowired
    private CounterModifyDivFacade counterModifyDivFacade;
    @Autowired
    private QueryCounterOrderFacade queryCounterOrderFacade;
    @Autowired
    private CounterCheckFacade counterCheckFacade;
    @Autowired
    private QueryCounterTradeFacade queryCounterTradeFacade;
    @Autowired
    private SubsOrPurCounterFacade subsOrPurCounterFacade;
    @Autowired
    private RedeemCounterFacade redeemCounterFacade;
    @Autowired
    private ModifyDivCounterFacade modifyDivCounterFacade;
    @Autowired
    private ForcedCancelOrderFacade forcedCancelOrderFacade;
    @Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;
    @Autowired
    private QueryAcctBalanceDtlFacade queryAcctBalanceDtlFacade;
    @Autowired
    private QueryBatchFlowInfoFacade queryBatchFlowInfoFacade;
    @Autowired
    private QueryTaBatchFlowInfoFacade queryTaBatchFlowInfoFacade;
    @Autowired
    private QueryWorkdayFacade queryWorkdayFacade;
    @Autowired
    private CounterForceCancelFacade counterForceCancelFacade;
    @Autowired
    private QueryCustFundDivFacade queryCustFundDivFacade;
    @Autowired
    private TmsCounterValidService tmsCounterValidService;
    @Autowired
    private QueryCancelOrderListFacade queryCancelOrderListFacade;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryForceCancelOrderListFacade queryForceCancelOrderListFacade;
    @Autowired
    @Qualifier("sm.counterEndFacade")
    private com.howbuy.tms.high.batch.facade.trade.counterend.CounterEndFacade smCounterEndFacade;
    @Autowired
    private CounterEndCheckFacade counterEndCheckFacade;
    @Autowired
    private QuerySubBalanceFacade querySubBalanceFacade;
    @Autowired
    private QueryRedeemFundListCounterFacade queryRedeemFundListCounterFacade;
    @Autowired
    private CounterShareMergeFacade counterShareMergeFacade;
    @Autowired
    private SubmitCheckOrderFacade submitCheckOrderFacade;
    @Autowired
    private HighShareMergeCounterFacade highShareMergeCounterFacade;
    @Autowired
    private HighShareTransferCounterFacade highShareTransferCounterFacade;
    @Autowired
    private ShareTransferCounterFacade shareTransferCounterFacade;
    @Autowired
    private QuerySubmitCheckOrderDtlFacade querySubmitCheckOrderDtlFacade;
    @Autowired
    @Qualifier("sm.querySimuBatchFlowInfoFacade")
    private QueryHighBatchFlowInfoFacade queryHighBatchFlowInfoFacade;
    @Autowired
    @Qualifier("queryHighTaBatchFlowInfoFacade")
    private QueryHighTaBatchFlowInfoFacade queryHighTaBatchFlowInfoFacade;
    @Autowired
    @Qualifier("high.queryBatchFlowStatFacade")
    private QueryBatchFlowStatFacade queryBatchFlowStatFacade;
    @Autowired
    @Qualifier("queryHighTaBusinessListFacade")
    private QueryHighTaBusinessListFacade queryHighTaBusinessListFacade;
    @Autowired
    @Qualifier("queryTaBusinessBatchCountFacade")
    private QueryTaBusinessBatchCountFacade queryTaBusinessBatchCountFacade;
    @Autowired
    @Qualifier("queryTaCounterNotEndFacade")
    private QueryTaCounterNotEndFacade queryTaCounterNotEndFacade;
    @Autowired
    @Qualifier("highCounterSaveOrDelNotEndTaFacade")
    private CounterSaveOrDelNotEndTaFacade counterSaveOrDelNotEndTaFacade;
    @Autowired
    private QueryHighWorkdayFacade querySimuWorkdayFacade;
    @Autowired
    private QueryCurTaDtAckFacade queryCurTaDtAckFacade;
    @Autowired
    private VolMigrateOuterService volMigrateOuterService;
    @Autowired
    private QueryPiggyOrderDetailFacade queryPiggyOrderDetailFacade;
    @Autowired
    private QueryHighFundDealOrderDtlChangeCardFacade queryHighFundDealOrderDtlChangeCardFacade;
    @Autowired
    private ModifyRepurchaseProtocolFacade modifyRepurchaseProtocolFacade;
    @Autowired
    private OrderPlanService orderPlanService;
    @Resource
    private UploadVoucherFileFacade uploadVoucherFileFacade;
    @Autowired
    private CounterNoTradeOverAccountFacade counterNoTradeOverAccountFacade;
    @Autowired
    private NoTradeOverAccountCounterFacade noTradeOverAccountCounterFacade;
    @Autowired
    private ModifyOwnershipRightTransferFace modifyOwnershipRightTransferFace;
    @Autowired
    private SubsAmtChangeCheckFacade subsAmtChangeCheckFacade;
    @Autowired
    private CounterEndPreCheckFacade counterEndPreCheckFacade;
    @Autowired
    private QueryDealOrderRefundFacade queryDealOrderRefundFacade;
    @Autowired
    private CounterModifyRefundDirectionFacade counterModifyRefundDirectionFacade;
    @Autowired
    private ModifyRefundFacade modifyRefundFacade;
    @Autowired
    private ModifyRedeemDirectionFacade modifyRedeemDirectionFacade;
    @Autowired
    private QuerySavingBoxVolDetailOuterService querySavingBoxVolDetailOuterService;
    @Autowired
    private QuerySavingBoxVolOutService querySavingBoxVolOutService;
    @Autowired
    private QueryGmInTransitFacade queryInTransitFacade;
    @Autowired
    private QueryHighInTransitFacade queryHighInTransitFacade;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private CpAcctSwitchOutService cpAcctSwitchOutService;
    @Autowired
    private NoTradeUpdateSubscribeAmtInfoFacade noTradeUpdateSubscribeAmtInfoFacade;
    @Autowired
    private PortfolioPartnerInfoService portfolioPartnerInfoService;
    @Autowired
    private DeleteVoucherFileFacade deleteVoucherFileFacade;
    @Autowired
    private HighProductAppointService highProductAppointService;

    @Override
    public QueryCustBaseInfoResponse queryCustBaseInfoSub(QueryCustBaseInfoReqDto queryCustBaseInfoReqDto, DisInfoDto disInfoDto) throws Exception {
        QueryCustBaseInfoRequest queryCustBaseInfoRequest = new QueryCustBaseInfoRequest();
        BeanUtils.copyProperties(queryCustBaseInfoReqDto, queryCustBaseInfoRequest);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCustBaseInfoFacade, queryCustBaseInfoRequest, disInfoDto);
        return (QueryCustBaseInfoResponse) baseResp;
    }

    @Override
    public QueryCustBaseInfoRespDto queryCustBaseInfo(QueryCustBaseInfoReqDto queryCustBaseInfoReqDto, DisInfoDto disInfoDto) throws Exception {
        QueryCustBaseInfoRespDto respDto = null;
        QueryCustBaseInfoRequest queryCustBaseInfoRequest = new QueryCustBaseInfoRequest();
        BeanUtils.copyProperties(queryCustBaseInfoReqDto, queryCustBaseInfoRequest);
        try {
            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCustBaseInfoFacade, queryCustBaseInfoRequest, disInfoDto);
            QueryCustBaseInfoResponse queryCustBaseInfoResponse = (QueryCustBaseInfoResponse) baseResp;
            respDto = new QueryCustBaseInfoRespDto();
            BeanUtils.copyProperties(queryCustBaseInfoResponse, respDto);
            List<CustBaseInfoBean> custList = queryCustBaseInfoResponse.getCustBaseInfoBeanList();
            if (!CollectionUtils.isEmpty(custList)) {
                for (CustBaseInfoBean custBaseInfoBean : custList) {
                    // 正常的用户
                    if ("0".equals(custBaseInfoBean.getCustStat())) {
                        BeanUtils.copyProperties(custBaseInfoBean, respDto);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("queryCustBaseInfo error :", e);
        }

        return respDto;
    }

    @Override
    public CounterPurchaseRespDto counterPurchase(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterPurchaseReqDto == null) {
            logger.info("counterPurchaseReqDto is null.");
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        // 判断下单时间是否在当前工作日
        if (isCounterEnd(getSysCode(counterPurchaseReqDto.getProductChannel()), counterPurchaseReqDto.getTaCode(), disInfoDto)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }
        //购买校验
        boolean validFlag = tmsCounterValidService.subsOrPurValidate(counterPurchaseReqDto, disInfoDto);
        CounterPurchaseRespDto resp = null;
        CounterPurchaseRequest request = new CounterPurchaseRequest();
        if (validFlag) {
            CounterPurchaseOrderBean counterPurchaseOrderBean = new CounterPurchaseOrderBean();

            BeanUtils.copyProperties(counterPurchaseReqDto, counterPurchaseOrderBean);
            request.setCounterPurchaseOrderBean(counterPurchaseOrderBean);

            CounterPurchaseResponse counterPurchaseResponse = (CounterPurchaseResponse) TmsFacadeUtil.executeThrowException(counterPurchaseFacade, request, disInfoDto);
            resp = new CounterPurchaseRespDto();
            BeanUtils.copyProperties(counterPurchaseResponse, resp);
        }

        return resp;
    }

    @Override
    public CounterPurchaseRespDto counterPurchaseForFund(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception {
        if (dto == null) {
            logger.info("dto is null.");
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        if (isCounterEnd(getSysCode(dto.getProductChannel()), dto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(dto.getAppDt(), dto.getAppTm());
            String workDay = getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }
        boolean validFlag = tmsCounterValidService.subsOrPurValidate(dto, disInfoDto);
        CounterPurchaseRespDto resp = null;
        CounterPurchaseRequest request = new CounterPurchaseRequest();
        if (validFlag) {
            CounterPurchaseOrderBean counterPurchaseOrderBean = new CounterPurchaseOrderBean();

            BeanUtils.copyProperties(dto, counterPurchaseOrderBean);
            request.setCounterPurchaseOrderBean(counterPurchaseOrderBean);

            CounterPurchaseResponse counterPurchaseResponse = (CounterPurchaseResponse) TmsFacadeUtil.executeThrowException(counterPurchaseFacade, request, disInfoDto);
            resp = new CounterPurchaseRespDto();
            BeanUtils.copyProperties(counterPurchaseResponse, resp);
        }
        return resp;
    }

    @Override
    public CounterRedeemRespDto counterRedeem(CounterRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterRedeemReqDto == null) {
            logger.info("counterRedeemReqDto is null.");
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        if (isCounterEnd(getSysCode(counterRedeemReqDto.getProductChannel()), counterRedeemReqDto.getTaCode(), disInfoDto)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }
        CounterRedeemRespDto resp = null;
        CounterRedeemRequest request = new CounterRedeemRequest();
        boolean validFlag = tmsCounterValidService.redeemValidate(counterRedeemReqDto, disInfoDto);
        if (validFlag) {
            CounterRedeemOrderBean counterRedeemOrderBean = new CounterRedeemOrderBean();
            BeanUtils.copyProperties(counterRedeemReqDto, counterRedeemOrderBean);
            request.setCounterRedeemOrderBean(counterRedeemOrderBean);

            CounterRedeemResponse counterRedeemResponse = (CounterRedeemResponse) TmsFacadeUtil.executeThrowException(counterRedeemFacade, request, disInfoDto);
            resp = new CounterRedeemRespDto();
            BeanUtils.copyProperties(counterRedeemResponse, resp);
        }
        return resp;
    }

    @Override
    public CounterNoTradeOverAccountRespDto counterNoTradeOverAccount(CounterNoTradeOverAccountReqDto counterReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterReqDto == null || disInfoDto == null) {
            logger.info("counterReqDto:{},disInfoDto:{}", counterReqDto, disInfoDto);
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        if (isCounterEnd(getSysCode(counterReqDto.getProductChannel()), counterReqDto.getTaCode(), disInfoDto)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }
        CounterNoTradeOverAccountRespDto resp = null;
        CounterNoTradeOverAccountRequest request = new CounterNoTradeOverAccountRequest();
        boolean validFlag = tmsCounterValidService.noTradeOverAccountValidate(counterReqDto, disInfoDto);
        if (validFlag) {
            CounterNoTradeOverAccountOrderBean orderBean = new CounterNoTradeOverAccountOrderBean();
            BeanUtils.copyProperties(counterReqDto, orderBean);
            request.setOrderBean(orderBean);

            CounterNoTradeOverAccountResponse response = (CounterNoTradeOverAccountResponse) TmsFacadeUtil.executeThrowException(counterNoTradeOverAccountFacade, request, disInfoDto);
            resp = new CounterNoTradeOverAccountRespDto();
            BeanUtils.copyProperties(response, resp);
        }
        return resp;
    }

    @Override
    public CounterModifyDivRespDto counterModifyDiv(CounterModifyDivReqDto counterModifyDivReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterModifyDivReqDto == null) {
            logger.info("counterModifyDivReqDto is null.");
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        if (isCounterEnd(getSysCode(counterModifyDivReqDto.getProductChannel()), counterModifyDivReqDto.getTaCode(), disInfoDto)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }
        CounterModifyDivRespDto resp = null;
        CounterModifyDivRequest request = new CounterModifyDivRequest();

        boolean validFlag = tmsCounterValidService.modifyDivValidate(counterModifyDivReqDto, disInfoDto);
        if (validFlag) {
            // 申请日期
            request.setAppDt(counterModifyDivReqDto.getAppDt());
            // 申请时间
            request.setAppTm(counterModifyDivReqDto.getAppTm());
            CounterModifyDivOrderBean counterModifyDivOrderBean = new CounterModifyDivOrderBean();
            BeanUtils.copyProperties(counterModifyDivReqDto, counterModifyDivOrderBean);
            request.setCounterModifyDivOrderBean(counterModifyDivOrderBean);

            CounterModifyDivResponse counterModifyDivResponse = (CounterModifyDivResponse) TmsFacadeUtil.executeThrowException(counterModifyDivFacade, request, disInfoDto);
            resp = new CounterModifyDivRespDto();
            BeanUtils.copyProperties(counterModifyDivResponse, resp);
        }
        return resp;
    }

    @Override
    public CounterForceCancelRespDto counterForceCancel(CounterForceCancelReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        if (reqDto == null || disInfoDto == null) {
            logger.info("reqDto:{},disInfoDto:{}", reqDto, disInfoDto);
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        if (isCounterEnd(getSysCode(reqDto.getProductChannel()), reqDto.getTaCode(), disInfoDto)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }
        CounterCancelReqDto counterCancelReqDto = new CounterCancelReqDto();
        BeanUtils.copyProperties(reqDto, counterCancelReqDto);

        // 撤单校验
        tmsCounterValidService.cancelOrderValidate(counterCancelReqDto, disInfoDto);

        CounterForceCancelRespDto resp = null;
        CounterForceCancelRequest request = new CounterForceCancelRequest();
        CounterForceCancelOrderBean counterCancelOrderBean = new CounterForceCancelOrderBean();
        BeanUtils.copyProperties(reqDto, counterCancelOrderBean);
        request.setCounterForceCancelOrderBean(counterCancelOrderBean);

        CounterForceCancelResponse counterCancelResponse = (CounterForceCancelResponse) TmsFacadeUtil.executeThrowException(counterForceCancelFacade, request, disInfoDto);
        resp = new CounterForceCancelRespDto();
        BeanUtils.copyProperties(counterCancelResponse, resp);
        return resp;

    }

    @Override
    public CounterQueryOrderRespDto counterQueryOrder(CounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception {

        CounterQueryOrderRespDto resp = null;
        QueryCounterOrderRequest request = new QueryCounterOrderRequest();
        String txCode = request.getTxCode();
        if (counterQueryOrderReqDto != null) {
            BeanUtils.copyProperties(counterQueryOrderReqDto, request);
            QueryCounterOrderCondition queryCounterOrderCondition = new QueryCounterOrderCondition();
            BeanUtils.copyProperties(counterQueryOrderReqDto, queryCounterOrderCondition);
            request.setQueryCounterOrderCondition(queryCounterOrderCondition);
            request.setTxCode(txCode);
        }

        QueryCounterOrderResponse queryCounterOrderResponse = (QueryCounterOrderResponse) TmsFacadeUtil.executeThrowException(queryCounterOrderFacade, request, disInfoDto);
        List<CounterOrderDto> counterOrderList = null;
        resp = new CounterQueryOrderRespDto();
        BeanUtils.copyProperties(queryCounterOrderResponse, resp);
        List<QueryCounterOrderRespBean> pageList = queryCounterOrderResponse.getQueryCounterOrderRespBeanList();
        if (!CollectionUtils.isEmpty(pageList)) {
            CounterOrderDto counterOrderDto = null;
            counterOrderList = new ArrayList<>(16);
            for (QueryCounterOrderRespBean orderBean : pageList) {
                counterOrderDto = new CounterOrderDto();
                BeanUtils.copyProperties(orderBean, counterOrderDto);
                counterOrderList.add(counterOrderDto);
            }
        }
        resp.setCounterOrderList(counterOrderList);

        return resp;
    }

    @Override
    public List<OrderDto> queryCanCancelOrder(String custNo, DisInfoDto disInfoDto) throws Exception {
        String workDay = getCounterWorkDay(SysCodeEnum.BATCH_HIGH.getCode(), disInfoDto);
        List<OrderDto> orderList = null;
        QueryCancelOrderListRequest request = new QueryCancelOrderListRequest();
        request.setPageSize(0);
        request.setTxAcctNo(custNo);
        request.setTaTradeDt(workDay);
        QueryCancelOrderListResponse queryHighCanCancelDealOrdersResponse = (QueryCancelOrderListResponse) TmsFacadeUtil.executeThrowException(queryCancelOrderListFacade, request, disInfoDto);
        List<CancelOrderBean> highOrderList = queryHighCanCancelDealOrdersResponse.getCancelOrderList();
        if (!CollectionUtils.isEmpty(highOrderList)) {
            orderList = new ArrayList<OrderDto>(16);
            OrderDto orderDto = null;
            for (CancelOrderBean cancelBean : highOrderList) {
                orderDto = new OrderDto();
                BeanUtils.copyProperties(cancelBean, orderDto);
                orderList.add(orderDto);
            }
        }

        return orderList;
    }

    @Override
    public List<OrderDto> queryCanForceCancelOrder(String custNo, DisInfoDto disInfoDto) throws Exception {
        // 中台未交易申请日终
        List<OrderDto> orderList = null;

        String workDay = getCounterWorkDay(SysCodeEnum.BATCH_HIGH.getCode(), disInfoDto);
        QueryForceCancelOrderListRequest request = new QueryForceCancelOrderListRequest();
        request.setTaTradeDt(workDay);
        request.setTxAcctNo(custNo);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryForceCancelOrderListFacade, request, disInfoDto);
        if (baseResp != null) {
            QueryForceCancelOrderListResponse queryHighCanCancelDealOrdersResponse = (QueryForceCancelOrderListResponse) baseResp;
            List<ForcesCancelOrderBean> highOrderList = queryHighCanCancelDealOrdersResponse.getForceCancelOrderList();
            if (!CollectionUtils.isEmpty(highOrderList)) {
                orderList = new ArrayList<OrderDto>(16);
                OrderDto orderDto = null;
                for (ForcesCancelOrderBean cancelBean : highOrderList) {
                    orderDto = new OrderDto();
                    BeanUtils.copyProperties(cancelBean, orderDto);
                    orderList.add(orderDto);
                }

                // 未审核订单
                Set<String> unCheckOrderSet = getUnCheckCounterCancelOrders(custNo, TxCodes.HIGH_COUNTER_FORCE_CANCEL);
                List<OrderDto> canCancelList = getCanCancelOrderList(orderList, unCheckOrderSet);
                return canCancelList;
            }
        }

        return orderList;
    }

    /**
     * exitUnCheckOrder:(存在未审核订单)
     *
     * @param dealNo
     * @param txAcctNo
     * @param txCode
     * @return
     * <AUTHOR>
     * @date 2018年3月30日 下午3:01:25
     */
    @Override
    public boolean exitUnCheckOrder(String dealNo, String txAcctNo, String txCode) {

        Set<String> unCheckOrderSet = getUnCheckCounterCancelOrders(txAcctNo, txCode);
        return unCheckOrderSet.contains(dealNo);
    }

    @Override
    public List<FundDivDto> queryFundDiv(String custNo, DisInfoDto disInfoDto) throws Exception {
        List<FundDivDto> fundDivList = null;
        QueryCustFundDivRequest request = new QueryCustFundDivRequest();
        request.setTxAcctNo(custNo);
        BaseResponse baseResposne = TmsFacadeUtil.executeThrowException(queryCustFundDivFacade, request, disInfoDto);
        if (baseResposne != null) {
            QueryCustFundDivResponse queryCustFundDivResponse = (QueryCustFundDivResponse) baseResposne;
            List<CustFundDivBean> custFundDivList = queryCustFundDivResponse.getCustFundDivList();
            if (!CollectionUtils.isEmpty(custFundDivList)) {
                fundDivList = new ArrayList<FundDivDto>(16);
                FundDivDto fundDivDto = null;
                for (CustFundDivBean custFundDivBean : custFundDivList) {
                    fundDivDto = new FundDivDto();
                    BeanUtils.copyProperties(custFundDivBean, fundDivDto);
                    fundDivList.add(fundDivDto);
                }
            }
        }
        return fundDivList;
    }

    @Override
    public QueryAcctBalanceDtlRespDto queryAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        QueryAcctBalanceDtlRespDto queryFundBalDtlRespDto = null;
        QueryAcctBalanceDtlRequest request = new QueryAcctBalanceDtlRequest();
        request.setTxAcctNo(reqDto.getCustNo());
        request.setProductCode(reqDto.getFundCode());
        request.setCpAcctNo(reqDto.getCpAcctNo());
        request.setAppDt(reqDto.getAppDt());
        request.setAppTm(reqDto.getAppTm());

        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryAcctBalanceDtlFacade, request, disInfoDto);
        if (baseResp != null) {
            queryFundBalDtlRespDto = new QueryAcctBalanceDtlRespDto();
            QueryAcctBalanceDtlResponse queryAcctBalanceDtlResponse = (QueryAcctBalanceDtlResponse) baseResp;
            BeanUtils.copyProperties(queryAcctBalanceDtlResponse, queryFundBalDtlRespDto);
            List<BalanceDtlBean> balanceDtlList = queryAcctBalanceDtlResponse.getBalanceDtlList();
            List<DtlBean> dtlList = new ArrayList<DtlBean>();
            if (!CollectionUtils.isEmpty(balanceDtlList)) {
                DtlBean dtlBean = null;
                for (BalanceDtlBean balanceDtlBean : balanceDtlList) {
                    dtlBean = new DtlBean();
                    BeanUtils.copyProperties(balanceDtlBean, dtlBean);
                    dtlList.add(dtlBean);
                }
            }
            queryFundBalDtlRespDto.setBalanceDtlList(dtlList);
        }

        return queryFundBalDtlRespDto;
    }

    @Override
    public QueryAcctBalanceDtlRespDto querySubBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        QueryAcctBalanceDtlRespDto queryFundBalDtlRespDto = null;
        QuerySubBalanceRequest request = new QuerySubBalanceRequest();
        request.setTxAcctNo(reqDto.getCustNo());
        request.setProductCode(reqDto.getFundCode());
        request.setCpAcctNo(reqDto.getCpAcctNo());
        request.setAppDt(reqDto.getAppDt());
        request.setAppTm(reqDto.getAppTm());

        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubBalanceFacade, request, disInfoDto);
        queryFundBalDtlRespDto = new QueryAcctBalanceDtlRespDto();
        QuerySubBalanceResponse queryAcctBalanceDtlResponse = (QuerySubBalanceResponse) baseResp;
        BeanUtils.copyProperties(queryAcctBalanceDtlResponse, queryFundBalDtlRespDto);
        List<SubBalanceDtlBean> balanceDtlList = queryAcctBalanceDtlResponse.getBalanceDtlList();
        List<DtlBean> dtlList = new ArrayList<DtlBean>();
        if (!CollectionUtils.isEmpty(balanceDtlList)) {
            DtlBean dtlBean = null;
            for (SubBalanceDtlBean balanceDtlBean : balanceDtlList) {
                dtlBean = new DtlBean();
                BeanUtils.copyProperties(balanceDtlBean, dtlBean);
                dtlList.add(dtlBean);
            }
        }
        queryFundBalDtlRespDto.setBalanceDtlList(dtlList);

        return queryFundBalDtlRespDto;
    }

    @Override
    public QueryAcctBalanceDtlRespDto querySubBalanceList(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        QueryAcctBalanceDtlRespDto queryFundBalDtlRespDto = null;
        QueryRedeemFundListCounterRequest request = new QueryRedeemFundListCounterRequest();
        request.setTxAcctNo(reqDto.getCustNo());
        request.setProductCode(reqDto.getFundCode());
        request.setCpAcctNo(reqDto.getCpAcctNo());
        request.setAppDt(reqDto.getAppDt());
        List<String> disCodeList = new ArrayList<>();
        disCodeList.add(reqDto.getDisCode());
        request.setDisCodeList(disCodeList);

        String appTm = DateUtil.formatNowDate(DateUtil.StR_PATTERN_HHMMSS);
        if (StringUtils.isEmpty(reqDto.getAppTm())) {
            if (DEFAULT_TIME.compareTo(appTm) < 0) {
                request.setAppTm(BEFORE_DEFAULT_TIME);
            } else {
                request.setAppTm(appTm);
            }
        } else {
            if (DEFAULT_TIME.compareTo(reqDto.getAppTm()) < 0) {
                request.setAppTm(BEFORE_DEFAULT_TIME);
            } else {
                request.setAppTm(appTm);
            }
        }

        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryRedeemFundListCounterFacade, request, disInfoDto);
        queryFundBalDtlRespDto = new QueryAcctBalanceDtlRespDto();
        QueryRedeemFundListCounterResponse queryRedeemFundListCounterResponse = (QueryRedeemFundListCounterResponse) baseResp;
        BeanUtils.copyProperties(queryRedeemFundListCounterResponse, queryFundBalDtlRespDto);
        List<RedeemListCounterBean> balanceDtlList = queryRedeemFundListCounterResponse.getBalanceList();
        List<DtlBean> dtlList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(balanceDtlList)) {
            DtlBean dtlBean = null;
            for (RedeemListCounterBean redeemListCounterBean : balanceDtlList) {
                dtlBean = new DtlBean();
                BeanUtils.copyProperties(redeemListCounterBean, dtlBean);
                dtlBean.setLockVol(redeemListCounterBean.getLockVol());
                dtlList.add(dtlBean);
            }
        }
        queryFundBalDtlRespDto.setBalanceDtlList(dtlList);

        return queryFundBalDtlRespDto;
    }

    @Override
    public QueryAcctBalanceRespDto queryAcctBalance(QueryAcctBalanceReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        QueryAcctBalanceRespDto queryAcctBalanceRespDto = null;
        QueryAcctBalanceRequest request = new QueryAcctBalanceRequest();
        queryAcctBalanceRespDto = new QueryAcctBalanceRespDto();
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryAcctBalanceFacade, request, disInfoDto);

        if (baseResp != null) {
            QueryAcctBalanceResponse queryAcctBalanceResponse = (QueryAcctBalanceResponse) baseResp;
            BeanUtils.copyProperties(queryAcctBalanceResponse, queryAcctBalanceRespDto);
        }
        return queryAcctBalanceRespDto;
    }

    @Override
    public boolean checkOrder(SubmitUncheckOrderDto submitUncheckOrderDto, String checkType, DisInfoDto disInfoDto) throws Exception {
        logger.info("TmsCounterServiceImpl-checkOrder,submitUncheckOrderDto={},checkType={}", submitUncheckOrderDto, checkType);
        String txCode = submitUncheckOrderDto.getTxCode();
        boolean flag = false;

        // 脱敏
        PrivacyUtil.resetCustInfoAndBankInfo(submitUncheckOrderDto);

        if (CheckTypeEnum.CHECK_SUCC.getCode().equals(checkType)) {
            //审核通过
            submitUncheckOrderDto.setCheckFlag(CheckFlagEnum.CHECK_SUCC.getCode());
            switch (txCode) {
                case TxCodes.HIGH_COUNTER_PURCHASE:
                    flag = subsOrPurCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.HIGH_COUNTER_REDEEM:
                    flag = redeemCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.HIGH_COUNTER_FORCE_CANCEL:
                    flag = cancelForceOrder(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.HIGH_COUNTER_MODIFYDIV:
                    flag = modifyDivCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.HIGH_COUNTER_MODIFY_REPURCHASEPROCTOL:
                    flag = modifyModifyRepurchase(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.HIGH_COUNTER_NOTRADE_OVERACCOUNT:
                    flag = noTradeOverAccountCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.HIGH_COUNTER_MODIFY_REFUND_DIRECTION:
                    flag = modifyRefundDirection(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.MODIFY_REDEEM_DIRECTION:
                    flag = modifyRedeemDirection(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE:
                    flag = modifyOwnershipRightTransfer(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.UPDATE_SUBSCRIBE_AMT_APPLY:
                    flag = modifySubsAmtChangeApply(submitUncheckOrderDto, disInfoDto);
                    break;
                default:
                    break;
            }
        } else if (CheckTypeEnum.CHECK_REJECT.getCode().equals(checkType)) {
            //审核驳回,如果股权维护转让的,驳回就等于作废
            if (TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE.equals(txCode)) {
                submitUncheckOrderDto.setCheckFlag(CheckFlagEnum.CHECK_ABOLISH.getCode());
            } else {
                submitUncheckOrderDto.setCheckFlag(CheckFlagEnum.CHECK_REJECT.getCode());
            }
            checkCounterOrder(submitUncheckOrderDto, disInfoDto);
        } else {
            //材料退回审核驳回
            submitUncheckOrderDto.setCheckFlag(checkType);
            checkCounterOrder(submitUncheckOrderDto, disInfoDto);
        }
        return flag;
    }

    /**
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return boolean
     * @Description 修改复购协议
     * <AUTHOR>
     * @Date 2019/8/20 14:51
     **/
    private boolean modifyModifyRepurchase(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        ModifyRepurchaseProtocolRequest request = new ModifyRepurchaseProtocolRequest();
        if (submitUncheckOrderDto != null) {
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 基金代码
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 复购类型
            request.setRepurchaseType(submitUncheckOrderDto.getRepurchaseType());
            // 复购份额
            request.setRepurchaseVol(submitUncheckOrderDto.getAppVol());
            // 复购协议号
            request.setRepurchaseProtocolNo(submitUncheckOrderDto.getRepurchaseProtocolNo());

            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());

        }
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(modifyRepurchaseProtocolFacade, request, disInfoDto);
        if (baseResp != null) {

            if (TmsFacadeUtil.isSuccess(baseResp)) {
                //ModifyRepurchaseProtocolResponse resp = (ModifyRepurchaseProtocolResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    @Override
    public QueryCounterTradeRespDto queryCounterTrade(QueryCounterTradeReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        QueryCounterTradeRespDto resp = null;
        QueryCounterTradeRequest request = new QueryCounterTradeRequest();
        if (reqDto != null) {
            BeanUtils.copyProperties(reqDto, request);
        }

        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCounterTradeFacade, request, disInfoDto);
        QueryCounterTradeResponse queryCounterTradeResponse = (QueryCounterTradeResponse) baseResp;
        resp = new QueryCounterTradeRespDto();
        BeanUtils.copyProperties(queryCounterTradeResponse, resp);
        if (!CollectionUtils.isEmpty(queryCounterTradeResponse.getCounterTradeBeanList())) {
            List<CounterTradeDto> list = new ArrayList<CounterTradeDto>(16);
            CounterTradeDto counterTradeDto = null;
            for (CounterTradeBean counterTradeBean : queryCounterTradeResponse.getCounterTradeBeanList()) {
                counterTradeDto = new CounterTradeDto();
                BeanUtils.copyProperties(counterTradeBean, counterTradeDto);
                list.add(counterTradeDto);
            }
            resp.setCounterTradeDtoList(list);
        }
        return resp;
    }

    /**
     * subsOrPurCounter:(柜台审核购买)
     *
     * @param submitUncheckOrderDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月10日 下午5:49:46
     */
    private boolean subsOrPurCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        SubsOrPurCounterRequest request = new SubsOrPurCounterRequest();
        if (submitUncheckOrderDto != null) {
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            // 经办人证件号
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            // 经办人证件类型
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            // 经办人姓名
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            // 操作员编号
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            // 投资顾问代码
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            // 申请金额
            request.setAppAmt(submitUncheckOrderDto.getAppAmt());
            request.setDiscountRate(submitUncheckOrderDto.getDiscountRate());
            // 风险确认标记：1-确认，0-未确认
            request.setRiskFlag(submitUncheckOrderDto.getRiskFlag());
            // 基金代码
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            // 份额类型：A-前收费；B-后收费
            request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
            request.setProtocolType("4");
            // 协议号
            request.setProtocolNo(null);
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setEsitmateFee(submitUncheckOrderDto.getEsitmateFee());
            request.setAppointmentDealNo(submitUncheckOrderDto.getAppointmentDealNo());
            // 是否到期赎回
            request.setIsRedeemExpire(submitUncheckOrderDto.getIsRedeemExpire());
            // 预计到期日期
            request.setPreExpireDate(submitUncheckOrderDto.getPreExpireDate());
            // 认缴金额
            request.setSubsAmt(submitUncheckOrderDto.getSubsAmt());

            List<PayInfoBean> payList = new ArrayList<>(1);
            PayInfoBean payInfoBean = new PayInfoBean();
            // 资金账号
            payInfoBean.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            payInfoBean.setPayAmt(submitUncheckOrderDto.getAppAmt());
            // 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
            payInfoBean.setPaymentType(submitUncheckOrderDto.getPaymentType());
            payList.add(payInfoBean);
            request.setPayList(payList);
            BaseResponse baseResp = TmsFacadeUtil.execute(subsOrPurCounterFacade, request, disInfoDto);
            if (baseResp != null) {
                submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
                if (TmsFacadeUtil.isSuccess(baseResp)) {
                    SubsOrPurCounterResponse subsOrPurCounterResponse = (SubsOrPurCounterResponse) baseResp;
                    submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                    submitUncheckOrderDto.setDealNo(subsOrPurCounterResponse.getDealNo());
                    checkCounterOrder(submitUncheckOrderDto, disInfoDto);
                } else {
                    throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
                }
            }
        }
        return true;
    }


    /**
     * redeemCounter:(柜台基金审核赎回)
     *
     * @param submitUncheckOrderDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月10日 下午5:21:33
     */
    private boolean redeemCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        RedeemCounterResponse resp = null;
        RedeemCounterRequest request = new RedeemCounterRequest();
        if (submitUncheckOrderDto != null) {
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            request.setProtocolType("4");
            request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setRedeemCapitalFlag(submitUncheckOrderDto.getRedeemCapitalFlag());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());

            request.setLargeRedeemFlag(submitUncheckOrderDto.getLargeRedmFlag());
            request.setUnusualTransType(submitUncheckOrderDto.getUnusualTransType());
            request.setAppointmentDealNo(submitUncheckOrderDto.getAppointmentDealNo());

            // 赎回明细
            CounterOrderFormDto formMemo = JSON.parseObject(submitUncheckOrderDto.getOrderFormMemo(), CounterOrderFormDto.class);
            List<RedeemDetailBean> redeemDetailList = new ArrayList<>(formMemo.getDtlBeanList().size());
            for (CounterOrderFormDto.CounterCustBalanceVolDtlBean app : formMemo.getDtlBeanList()) {
                RedeemDetailBean bean = new RedeemDetailBean();
                bean.setAppVol(app.getAppVol());
                bean.setCpAcctNo(app.getCpAcctNo());
                bean.setRedeemDirection(app.getRedeemCapitalFlag());
                bean.setRefundAmt(app.getRefundFinaAvailAmt());
                bean.setRefundMemo(app.getRefundFinaAvailMemo());
                redeemDetailList.add(bean);
            }
            request.setRedeemDetailList(redeemDetailList);
        }
        BaseResponse baseResp = TmsFacadeUtil.execute(redeemCounterFacade, request, disInfoDto);
        if (baseResp != null) {

            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                resp = (RedeemCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo((resp.getDealNo()));
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }

        }
        return true;
    }

    /**
     * noTradeOverAccountCounter:(柜台非交易过户审核)
     *
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2020/9/28 13:26
     * @since JDK 1.8
     */
    private boolean noTradeOverAccountCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        if (submitUncheckOrderDto == null) {
            logger.error("noTradeOverAccountCounter-柜台非交易过户审核,柜台申请单为空");
            return false;
        }
        // 1.判断申请份额是否为0,就只需要修改双方客户产品的认缴金额明细
        if (BigDecimal.ZERO.compareTo(submitUncheckOrderDto.getAppVol()) == 0) {
            return noTradeUpdateSubmitAmtInfo(submitUncheckOrderDto, disInfoDto);
        } else {
            // 2.否则需要走正常非交易申请处理逻辑
            noTradeApplyProcess(submitUncheckOrderDto, disInfoDto);
        }
        return true;
    }

    /**
     * 非交易转让修改认缴金额信息
     *
     * @param submitUncheckOrderDto 申请单
     * @param disInfoDto            分销信息
     * @return
     * @throws Exception
     */
    private boolean noTradeUpdateSubmitAmtInfo(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        NoTradeUpdateSubscribeAmtInfoRequest noTradeUpdateSubscribeAmtInfoRequest = new NoTradeUpdateSubscribeAmtInfoRequest();
        noTradeUpdateSubscribeAmtInfoRequest.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        noTradeUpdateSubscribeAmtInfoRequest.setDisCode(disInfoDto.getDisCode());
        NoTradeUpdateSubscribeAmtInfoResponse response = noTradeUpdateSubscribeAmtInfoFacade.execute(noTradeUpdateSubscribeAmtInfoRequest);
        if (response != null) {
            if (TmsFacadeUtil.isSuccess(response)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
                return true;
            } else {
                throw new TmsCounterException(response.getReturnCode(), response.getDescription());
            }
        } else {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "非交易过户更改认缴信息响应为空");
        }
    }

    /**
     * 非交易申请处理逻辑
     *
     * @param submitUncheckOrderDto 柜台申请单
     * @param disInfoDto            分销信息
     * @throws Exception
     */
    private void noTradeApplyProcess(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        NoTradeOverAccountCounterResponse resp = null;
        NoTradeOverAccountCounterRequest request = new NoTradeOverAccountCounterRequest();
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 资金账号
        request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        // 转入交易账号
        request.setInTxAcctNo(submitUncheckOrderDto.getInTxAcctNo());
        // 转入资金账号
        request.setInCpAcctNo(submitUncheckOrderDto.getInCpAcctNo());
        request.setInDisCode(submitUncheckOrderDto.getInDisCode());
        // 基金代码
        request.setFundCode(submitUncheckOrderDto.getFundCode());
        // 申请份额
        request.setAppVol(submitUncheckOrderDto.getAppVol());
        // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
        request.setProtocolType("4");
        // 协议号
        request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        // 操作员编号
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        BaseResponse baseResp = TmsFacadeUtil.execute(noTradeOverAccountCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                resp = (NoTradeOverAccountCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(resp.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        } else {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "非交易过户转让返回结果为空");
        }
    }

    /**
     * counterCancel:(柜台强制撤单)
     *
     * @param submitUncheckOrderDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月10日 下午5:40:00
     */
    private boolean cancelForceOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        ForcedCancelOrderRequest request = new ForcedCancelOrderRequest();
        if (submitUncheckOrderDto != null) {
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 订单号
            request.setDealNo(submitUncheckOrderDto.getDealNo());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            //强制取消标识 0-强制 1-非强制
            request.setForceCancelFlag(submitUncheckOrderDto.getForceCancelFlag());
            request.setWithdrawDirection(submitUncheckOrderDto.getWithdrawDirection());
            CounterOrderFormDto counterOrderFormDto = JSON.parseObject(submitUncheckOrderDto.getOrderFormMemo(), CounterOrderFormDto.class);
            request.setRefundAmt(counterOrderFormDto.getRefundBean().getRefundFinaAvailAmt());
            request.setRefundMemo(counterOrderFormDto.getRefundBean().getRefundFinaAvailMemo());
        }
        BaseResponse baseResp = TmsFacadeUtil.execute(forcedCancelOrderFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    /**
     * 股权转让修改
     */
    private boolean modifyOwnershipRightTransfer(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        // 1.构建修改参数
        ModifyOwnershipRightTransferRequest request = new ModifyOwnershipRightTransferRequest();
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        BaseResponse baseResp = TmsFacadeUtil.execute(modifyOwnershipRightTransferFace, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;


    }

    /**
     * 审核认缴金额申请单
     */
    private boolean modifySubsAmtChangeApply(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        // 1.构建修改参数
        SubsAmtChangeCheckRequest request = new SubsAmtChangeCheckRequest();
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        BaseResponse baseResp = TmsFacadeUtil.execute(subsAmtChangeCheckFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;


    }

    /**
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return boolean
     * @description:修改回款方向
     * @author: chuanguang.tang
     * @date: 2021/8/4 17:58
     * @since JDK 1.8
     */
    private boolean modifyRefundDirection(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        ModifyRefundRequest request = new ModifyRefundRequest();
        if (submitUncheckOrderDto != null) {
            request.setDealNo(submitUncheckOrderDto.getDealNo());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            CounterOrderFormDto.RefundBean refundBean = JSON.parseObject(submitUncheckOrderDto.getOrderFormMemo(), CounterOrderFormDto.RefundBean.class);
            request.setRefundAmt(refundBean.getRefundFinaAvailAmt());
            request.setRefundMemo(refundBean.getRefundFinaAvailMemo());
            request.setRefundDir(submitUncheckOrderDto.getWithdrawDirection());
        }
        BaseResponse baseResp = TmsFacadeUtil.execute(modifyRefundFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }


    /**
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return boolean
     * @description:机构修改回款方向
     * @author: qiang.wang01
     * @date: 2021/8/4 17:58
     * @since JDK 1.8
     */
    private boolean modifyRedeemDirection(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        RedeemDirectionBean bean = new RedeemDirectionBean();
        List<RedeemDirectionBean> redeemDirectionList = Lists.newArrayList();
        ModifyRedeemDirectionRequest request = new ModifyRedeemDirectionRequest();
        if (submitUncheckOrderDto != null) {
            bean.setDealNo(submitUncheckOrderDto.getDealNo());
            bean.setRedeemDirection(submitUncheckOrderDto.getRedeemCapitalFlag());
            redeemDirectionList.add(bean);
        }
        request.setRedeemDirectionList(redeemDirectionList);
        BaseResponse baseResp = TmsFacadeUtil.execute(modifyRedeemDirectionFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }


    /**
     * modifyDivCounter:(柜台修改分红方式)
     *
     * @param submitUncheckOrderDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月10日 下午5:49:10
     */
    private boolean modifyDivCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        ModifyDivCounterRequest request = new ModifyDivCounterRequest();
        if (submitUncheckOrderDto != null) {
            // 经办人证件号
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            // 经办人证件类型
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            // 经办人姓名
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            // 操作员编号
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            // 投资顾问代码
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 基金代码
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            // request
            request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            // 目标基金分红方式
            request.setDivMode(submitUncheckOrderDto.getFundDivMode());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());

        }
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(modifyDivCounterFacade, request, disInfoDto);
        if (baseResp != null) {

            if (TmsFacadeUtil.isSuccess(baseResp)) {
                ModifyDivCounterResponse modifyDivCounterResponse = (ModifyDivCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(modifyDivCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto);
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    @Override
    public boolean modifyCheckOrder(SubmitUncheckOrderDto submitUncheckOrderDto, String checkType, DisInfoDto disInfoDto) throws Exception {

        if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
            // 修改
            // 待审核
            submitUncheckOrderDto.setCheckFlag(CheckFlagEnum.NOT_CHECK.getCode());
        } else if (CheckTypeEnum.CHECK_CANCEL.getCode().equals(checkType)) {
            // 驳回
            // 废单
            submitUncheckOrderDto.setCheckFlag(CheckFlagEnum.CHECK_ABOLISH.getCode());
        }

        // 更新审核单状态
        CounterCheckOrderBean counterCheckOrderBean = new CounterCheckOrderBean();
        // 柜台订单号
        counterCheckOrderBean.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        // 交易账号
        counterCheckOrderBean.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 申请时间
        counterCheckOrderBean.setAppTm(submitUncheckOrderDto.getAppTm());
        // 含手续费申请金额
        counterCheckOrderBean.setAppAmt(submitUncheckOrderDto.getAppAmt());
        // 费率
        counterCheckOrderBean.setFeeRate(submitUncheckOrderDto.getFeeRate());
        //手续费
        counterCheckOrderBean.setEsitmateFee(submitUncheckOrderDto.getEsitmateFee());
        // 折扣
        counterCheckOrderBean.setDiscountRate(submitUncheckOrderDto.getDiscountRate());
        // 申请份额
        counterCheckOrderBean.setAppVol(submitUncheckOrderDto.getAppVol());
        // 赎回去向
        counterCheckOrderBean.setRedeemCapitalFlag(submitUncheckOrderDto.getRedeemCapitalFlag());
        // 是否强制取消
        counterCheckOrderBean.setForceCancelFlag(submitUncheckOrderDto.getForceCancelFlag());
        // 巨额赎回顺延标识
        counterCheckOrderBean.setLargeRedmFlag(submitUncheckOrderDto.getLargeRedmFlag());
        // 异常标识
        counterCheckOrderBean.setUnusualTransType(submitUncheckOrderDto.getUnusualTransType());
        // 银行卡号
        counterCheckOrderBean.setBankAcct(submitUncheckOrderDto.getBankAcct());
        // 银行代码
        counterCheckOrderBean.setBankCode(submitUncheckOrderDto.getBankCode());
        // 修改人
        counterCheckOrderBean.setModifier(submitUncheckOrderDto.getModifier());
        // 更新时间
        counterCheckOrderBean.setUpdateDtm(submitUncheckOrderDto.getUpdateDtm());
        // 撤单原因
        counterCheckOrderBean.setCancelMemo(submitUncheckOrderDto.getCancelMemo());
        // 审核状态
        counterCheckOrderBean.setCheckFlag(submitUncheckOrderDto.getCheckFlag());
        // 资金账号
        counterCheckOrderBean.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        counterCheckOrderBean.setIsRedeemExpire(submitUncheckOrderDto.getIsRedeemExpire());
        counterCheckOrderBean.setPreExpireDate(submitUncheckOrderDto.getPreExpireDate());
        // 材料ID
        counterCheckOrderBean.setMaterialId(submitUncheckOrderDto.getMaterialId());
        counterCheckOrderBean.setRepurchaseType(submitUncheckOrderDto.getRepurchaseType());
        if (StringUtils.isNotBlank(submitUncheckOrderDto.getOrderFormMemo())) {
            counterCheckOrderBean.setOrderFormMemo(submitUncheckOrderDto.getOrderFormMemo());
        }
        // 产品代码
        counterCheckOrderBean.setFundCode(submitUncheckOrderDto.getFundCode());
        // 认缴金额
        counterCheckOrderBean.setSubsAmt(submitUncheckOrderDto.getSubsAmt());
        // 转让金额
        counterCheckOrderBean.setTransferPrice(submitUncheckOrderDto.getTransferPrice());
        // 是否匹配非交易
        counterCheckOrderBean.setMatchedTransfer(submitUncheckOrderDto.getMatchedTransfer());

        counterCheckOrderBean.setTxCode(submitUncheckOrderDto.getTxCode());

        CounterCheckRequest request = new CounterCheckRequest();
        request.setCounterCheckOrderBean(counterCheckOrderBean);
        TmsFacadeUtil.executeThrowException(counterCheckFacade, request, disInfoDto);

        return true;
    }

    /**
     * checkCounterOrder:(更新柜台订单审核状态)
     *
     * @param submitUncheckOrderDto
     * <AUTHOR>
     * @date 2017年4月10日 下午6:55:40
     */
    private void checkCounterOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        CounterCheckOrderBean counterCheckOrderBean = new CounterCheckOrderBean();
        BeanUtils.copyProperties(submitUncheckOrderDto, counterCheckOrderBean);
        counterCheckOrderBean.setDealNo(submitUncheckOrderDto.getDealNo());
        counterCheckOrderBean.setChecker(submitUncheckOrderDto.getChecker());
        counterCheckOrderBean.setModifier(submitUncheckOrderDto.getModifier());
        counterCheckOrderBean.setAppFlag(submitUncheckOrderDto.getAppFlag());
        counterCheckOrderBean.setMemo(submitUncheckOrderDto.getMemo());
        Date currDate = new Date();
        counterCheckOrderBean.setUpdateDtm(currDate);
        counterCheckOrderBean.setCheckDtm(currDate);
        CounterCheckRequest request = new CounterCheckRequest();
        request.setCounterCheckOrderBean(counterCheckOrderBean);
        TmsFacadeUtil.executeThrowException(counterCheckFacade, request, disInfoDto);
    }

    @Override
    public boolean isCounterEnd(String sysCode, String taCode, DisInfoDto disInfoDto) throws Exception {
        boolean flag = false;
        if (SysCodeEnum.BATCH_GM.getCode().equals(sysCode)) {
            flag = isWorkFlowNodeEndForFund(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode(), sysCode, taCode, disInfoDto, null);

        } else if (SysCodeEnum.BATCH_HIGH.getCode().equals(sysCode)) {
            flag = isWorkFlowNodeEndForHigh(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode(), sysCode, taCode, disInfoDto);

        } else if (SysCodeEnum.BATCH_REGULAR.getCode().equals(sysCode)) {
            flag = isWorkFlowNodeEndForFund(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode(), sysCode, taCode, disInfoDto, null);
        }
        return flag;
    }


    @Override
    public void adviserCancelValid(DisInfoDto disInfoDto, String partnerCode, String protocolType) {
        List<String> protocolTypeList = Lists.newArrayList(ProtocolTypeEnum.ADVISER_PORTFOLIFO.getCode(), ProtocolTypeEnum.PORTFOLIO.getCode(),
                ProtocolTypeEnum.SMALL_TARGET_PORTFOLIO.getCode(), ProtocolTypeEnum.RECOMMEND_PORTFOLIFO.getCode());
        if (!protocolTypeList.contains(protocolType)) {
            return;
        }

        if (StringUtils.isBlank(partnerCode)) {
            logger.error("adviserCancelValid partnerCode is null");
            return;
        }

        // 投顾校验 ： 该投顾产品所属的投顾供应商为模式一（投顾拆单）且批处理‘投顾交易申请处理’已完成，则不允许撤单，提示：该笔订单已经上报给投顾方，不允许撤单。
        List<PortfolioPartnerInfoModel> portfolioPartnerInfoModels = portfolioPartnerInfoService.getPortfolioPartnerInfoByParCode(partnerCode);
        if (!CollectionUtils.isEmpty(portfolioPartnerInfoModels)) {
            boolean splitAdviserFlag = portfolioPartnerInfoModels.stream()
                    .anyMatch(x -> x.getAdviserSplitFlag().equals(AdviserSplitFlagEnum.SPLIT_ADVISER.getCode()));
            if (splitAdviserFlag) {
                boolean flag = isWorkFlowNodeEndForFund(BusinessProcessingStepEnum.ADVISER_APPLY_PROCESS.getCode(), SysCodeEnum.BATCH_GM.getCode(), null, disInfoDto, partnerCode);
                if (flag) {
                    throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END.getCode(), "该笔订单已经上报给投顾方，不允许撤单");
                }
            }
        }
    }


    /**
     * isWorkFlowNodeEndForFund:(公募查询批处理节点是否执行成功)
     *
     * @param taskId
     * @param taCode
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月16日 下午4:03:58
     */
    public boolean isWorkFlowNodeEndForFund(String taskId, String sysCode, String taCode, DisInfoDto disInfoDto, String partnerCode) {

        if (StringUtils.isNotEmpty(taCode) || StringUtils.isNotEmpty(partnerCode)) {

            // a. 校验TA子流程是否收市
            QueryTaBatchFlowInfoRequest request = new QueryTaBatchFlowInfoRequest();
            request.setTaskId(taskId);
            request.setSysCode(sysCode);
            request.setTaCode(taCode);
            request.setPartnerCode(partnerCode);

            QueryTaBatchFlowInfoResponse resp = null;
            BaseResponse baseResp = TmsFacadeUtil.execute(queryTaBatchFlowInfoFacade, request, disInfoDto);
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                resp = (QueryTaBatchFlowInfoResponse) baseResp;
                if (FlowStatEnum.SUCCESSFUL.getCode().equals(resp.getFlowStat())) {
                    return true;
                }
            }
        }

        // b. 校验节点主流程是否结束完成
        QueryBatchFlowInfoRequest request = new QueryBatchFlowInfoRequest();
        request.setTaskId(taskId);
        request.setSysCode(sysCode);

        QueryBatchFlowInfoResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryBatchFlowInfoFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            resp = (QueryBatchFlowInfoResponse) baseResp;
            List<QueryBatchFlowInfoResponse.BatchFlowBean> list = resp.getFlowList();
            if (!CollectionUtils.isEmpty(list)) {
                return BatchStatEnum.PROCESS_SUCCESS.getKey().equals(list.get(0).getBatchStat());
            }
        }

        return false;
    }

    /**
     * isWorkFlowNodeEndForSimu:(私募查询批处理节点是否执行成功)
     *
     * @param taskId
     * @param sysCode
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月18日 下午3:54:26
     */
    private boolean isWorkFlowNodeEndForHigh(String taskId, String sysCode, String taCode, DisInfoDto disInfoDto) throws Exception {
        // taCode不为空判断分TA业务收市节点是否完成
        if (StringUtils.isNotEmpty(taCode)) {
            QueryHighTaBatchFlowInfoRequest request = new QueryHighTaBatchFlowInfoRequest();
            request.setTaskId(taskId);
            request.setSysCode(sysCode);
            request.setTaCode(taCode);

            QueryHighTaBatchFlowInfoResponse resp = null;
            BaseResponse baseResp = TmsFacadeUtil.execute(queryHighTaBatchFlowInfoFacade, request, disInfoDto);
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                resp = (QueryHighTaBatchFlowInfoResponse) baseResp;
                if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(resp.getFlowStat())) {
                    return true;
                }
            }
        }

        QueryHighBatchFlowInfoRequest request = new QueryHighBatchFlowInfoRequest();
        request.setTaskId(taskId);
        request.setSysCode(sysCode);

        QueryHighBatchFlowInfoResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryHighBatchFlowInfoFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            resp = (QueryHighBatchFlowInfoResponse) baseResp;
            List<QueryHighBatchFlowInfoResponse.BatchFlowBean> list = resp.getFlowList();
            if (!CollectionUtils.isEmpty(list)) {
                return BatchStatEnum.PROCESS_SUCCESS.getKey().equals(list.get(0).getBatchStat());
            }
        }
        return false;
    }

    @Override
    public String getSystemWorkDay(DisInfoDto disInfoDto) throws Exception {
        QueryWorkdayRequest request = new QueryWorkdayRequest();
        request.setWorkdayType(WorkdayTypeEnum.SYS_TYPE);
        QueryWorkdayResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryWorkdayFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            resp = (QueryWorkdayResponse) baseResp;
            return resp.getWoryday();
        }

        return null;
    }

    @Override
    public String getSystemWorkDay(String sysCode, DisInfoDto disInfoDto) throws Exception {
        QueryWorkdayRequest request = new QueryWorkdayRequest();
        request.setWorkdayType(WorkdayTypeEnum.SYS_TYPE);
        request.setSysCode(sysCode);

        QueryWorkdayResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryWorkdayFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            resp = (QueryWorkdayResponse) baseResp;
            return resp.getWoryday();
        }

        return null;
    }

    /**
     * 查询高端系统工作日
     *
     * @return
     */
    @Override
    public String getHighSystemWorkDay() throws Exception {
        QueryHighWorkdayRequest req = new QueryHighWorkdayRequest();
        QueryHighWorkdayResponse resp = null;
        req.setWorkdayType(com.howbuy.tms.common.enums.database.WorkdayTypeEnum.SYS_TYPE);
        resp = (QueryHighWorkdayResponse) TmsFacadeUtil.executeThrowException(querySimuWorkdayFacade, req, null);

        return resp.getWoryday();
    }

    @Override
    public void counterEndCheckFacade() {
        CounterEndCheckRequest request = new CounterEndCheckRequest();
        request.setAppDt(DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD));
        request.setCheckFlag(CheckFlagEnum.NOT_CHECK.getCode());
        CounterEndCheckResponse response = counterEndCheckFacade.execute(new CounterEndCheckRequest());
        if (response != null && YesOrNoEnum.YES.getCode().equals(response.getHaveUnapprovedOrders())) {
            throw new TmsCounterException(TmsCounterResultEnum.EXIT_UN_CHECK_COUNTER_DEAL);
        }
    }

    @Override
    public boolean simuCounterEnd() throws Exception {
        // 私募柜台收市
        CounterEndRequest request = new CounterEndRequest();
        TmsFacadeUtil.executeThrowException(smCounterEndFacade, request, null);
        return true;
    }

    /**
     * getCounterWorkDay:(查询柜台工作日)
     *
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年5月10日 下午4:36:37
     */
    @Override
    public String getCounterWorkDay(String sysCode, DisInfoDto disInfoDto) throws Exception {
        QueryWorkdayRequest request = new QueryWorkdayRequest();
        request.setWorkdayType(WorkdayTypeEnum.SYS_TYPE);
        QueryWorkdayResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryWorkdayFacade, request, disInfoDto);
        String workDay = null;
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            resp = (QueryWorkdayResponse) baseResp;
            workDay = resp.getWoryday();
            boolean counterEnd = isCounterEnd(sysCode, null, disInfoDto);
            if (counterEnd) {
                workDay = resp.getNextWorkDay();
            }
        }
        return workDay;
    }


    @Override
    public FeeDto calDiscountRate(FeeReqDto feeReqDto, CustInfoDto custInfoDto, String fundCode, String bankCode, String mBusiCode) throws Exception {
        logger.debug("TmsCounterServiceImpl|calDiscountRate|feeReqDto:{},custInfoDto:{},fundCode:{},bankCode:{},mBusiCode:{}", feeReqDto, custInfoDto, fundCode,
                bankCode, mBusiCode);
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(mBusiCode);
        String businessCode = null;
        if (null != businessCodeEnum) {
            businessCode = businessCodeEnum.getCode();
        }

        // 查询产品信息
        HighProductBaseInfoModel highProductBaseModel = highProductService.getHighProductBaseInfo(fundCode);

        // 查询活动折扣
        HighActiDiscountRatioModel actiDiscountRatioModel = highProductService.getHighActiRateDiscount(fundCode, highProductBaseModel.getShareClass(),
                PaySourceEnum.SELF_DRAWING.getCode(), custInfoDto.getInvstType(), businessCode, bankCode, custInfoDto.getDisCode());

        if (actiDiscountRatioModel == null || actiDiscountRatioModel.getAgentDisc() == null
                || !FundActiFlagEnum.JOIN.getCode().equals(actiDiscountRatioModel.getActiFlag())
                || actiDiscountRatioModel.getActiDiscountRatio() == null || actiDiscountRatioModel.getAgentDisc().compareTo(actiDiscountRatioModel.getActiDiscountRatio()) < 0) {
            logger.info("calDiscountRate|actiDiscountRatioBean:{}", JSON.toJSONString(actiDiscountRatioModel));
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_ACTIDISCOUNT_ERROR);
        }

        // 计算最小折扣
        BigDecimal discountRate = getDiscount(feeReqDto.getNetBuyAmt(), feeReqDto.getAppointmentAmt(), feeReqDto.getDiscountRate(), actiDiscountRatioModel);
        FeeDto feeDto = new FeeDto();
        feeDto.setDiscountRate(discountRate);
        return feeDto;
    }

    @Override
    public FeeDto calDiscountRateForFund(FeeReqDto feeReqDto, CustInfoDto custInfoDto, String fundCode, String bankCode, String mBusiCode) throws Exception {
        logger.debug("TmsCounterServiceImpl|calDiscountRate|feeReqDto:{},custInfoDto:{},fundCode:{},bankCode:{},mBusiCode:{}", feeReqDto, custInfoDto, fundCode,
                bankCode, mBusiCode);
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(mBusiCode);
        String businessCode = null;
        if (null != businessCodeEnum) {
            businessCode = businessCodeEnum.getCode();
        }

        // 查询产品信息
        HighProductBaseInfoModel highProductBaseModel = highProductService.getHighProductBaseInfo(fundCode);

        // 查询活动折扣
        HighActiDiscountRatioModel actiDiscountRatioModel = highProductService.getHighActiRateDiscount(fundCode, highProductBaseModel.getShareClass(),
                PaySourceEnum.SELF_DRAWING.getCode(), custInfoDto.getInvstType(), businessCode, bankCode, custInfoDto.getDisCode());

        // 计算最小折扣
        BigDecimal discountRate = getDiscount(feeReqDto.getNetBuyAmt(), feeReqDto.getAppointmentAmt(), feeReqDto.getDiscountRate(), actiDiscountRatioModel);
        FeeDto feeDto = new FeeDto();
        feeDto.setDiscountRate(discountRate);
        return feeDto;
    }

    public BigDecimal getDiscount(BigDecimal netBuyAmt, BigDecimal appointmentAmt, BigDecimal appointmentDiscount,
                                  HighActiDiscountRatioModel actiDiscountRatioBean) {
        logger.debug("getDiscount|appointmentDiscount:{}", appointmentDiscount);
        // 代销折扣
        BigDecimal agentDisc = new BigDecimal(1);
        // 活动折扣
        BigDecimal actiDiscountRatio = new BigDecimal(1);

        // 折扣信息
        if (actiDiscountRatioBean != null) {
            logger.debug("getDiscount|actiDiscountRatioBean:{}", JSON.toJSONString(actiDiscountRatioBean));
            if (actiDiscountRatioBean.getAgentDisc() != null) {
                agentDisc = actiDiscountRatioBean.getAgentDisc();
            }

            if (actiDiscountRatioBean.getActiDiscountRatio() != null && FundActiFlagEnum.JOIN.getCode().equals(actiDiscountRatioBean.getActiFlag())) {
                actiDiscountRatio = actiDiscountRatioBean.getActiDiscountRatio();
            }
        } else {
            logger.debug("getDiscount|actiDiscountRatioBean is null");
        }
        logger.debug("getDiscount|agentDisc:{}, actiDiscountRatio:{}", agentDisc, actiDiscountRatio);

        if (appointmentDiscount != null) {
            if (appointmentAmt == null) {
                throw new ValidateException(com.howbuy.tms.common.constant.ExceptionCodes.PARAM_IS_NULL,
                        MessageSource.getMessageByCode(com.howbuy.tms.common.constant.ExceptionCodes.PARAM_IS_NULL));
            }
            // 净购买金额小于预约金额, 则取代销折扣
            if (netBuyAmt.compareTo(appointmentAmt) < 0) {

                return agentDisc;
            } else {
                // 净购买金额大于等于预约金额, 则取预约折扣
                if (appointmentDiscount.compareTo(actiDiscountRatio) >= 0) {
                    return appointmentDiscount;
                } else {
                    throw new TmsCounterException(TmsCounterResultEnum.COUNTER_DISCOUNT_ERROR);
                }

            }
        } else {
            // 没有预约折扣, 取代销折扣
            return agentDisc;
        }

    }

    /**
     * getUnCheckCounterCancelOrders:(获取未审核的柜台撤单)
     *
     * @param txAcctNo 交易账号
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年3月30日 下午1:44:23
     */
    private Set<String> getUnCheckCounterCancelOrders(String txAcctNo, String txCode) {

        Set<String> unCheckCancelOrderSet = new HashSet<String>();

        CounterQueryOrderReqDto counterQueryOrderReqDto = new CounterQueryOrderReqDto();
        // 交易账号
        counterQueryOrderReqDto.setTxAcctNo(txAcctNo);
        // 审核状态
        counterQueryOrderReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECK.getCode());
        // 交易码
        counterQueryOrderReqDto.setTxCode(txCode);
        counterQueryOrderReqDto.setPageNo(1);
        counterQueryOrderReqDto.setPageSize(1000);
        try {
            CounterQueryOrderRespDto counterQueryOrderRespDto = counterQueryOrder(counterQueryOrderReqDto, null);
            if (counterQueryOrderRespDto != null) {
                if (CollectionUtils.isEmpty(counterQueryOrderRespDto.getCounterOrderList())) {
                    return unCheckCancelOrderSet;
                }

                for (CounterOrderDto counterOrderDto : counterQueryOrderRespDto.getCounterOrderList()) {
                    // 中台订单号
                    unCheckCancelOrderSet.add(counterOrderDto.getDealNo());
                }
            }

        } catch (Exception e) {
            logger.error("TmsCounterServiceImpl|getUnCheckCounterCancelOrders|error");
        }

        return unCheckCancelOrderSet;
    }

    /**
     * getCanCancelOrderList:(过滤可撤单列表)
     *
     * @param orderList
     * @param unCheckOrderSet
     * @return
     * <AUTHOR>
     * @date 2018年3月30日 下午2:24:54
     */
    private List<OrderDto> getCanCancelOrderList(List<OrderDto> orderList, Set<String> unCheckOrderSet) {

        List<OrderDto> cancelList = new ArrayList<OrderDto>();
        for (OrderDto orderDto : orderList) {
            if (!unCheckOrderSet.contains(orderDto.getDealNo())) {
                cancelList.add(orderDto);
            }
        }
        return cancelList;
    }

    /**
     * getSysCode:(根据产品通道获取系统码)
     *
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年4月18日 下午3:52:08
     */
    private String getSysCode(String productChannel) {
        if (ProductChannelEnum.FUND.getCode().equals(productChannel)) {
            return SysCodeEnum.BATCH_GM.getCode();
        }
        return SysCodeEnum.BATCH_HIGH.getCode();
    }

    @Override
    public QueryBatchFlowInfoResponse getBatchFlowInfo(String taskId, DisInfoDto disInfoDto) throws Exception {
        QueryBatchFlowInfoRequest request = new QueryBatchFlowInfoRequest();
        request.setTaskId(taskId);
        QueryBatchFlowInfoResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryBatchFlowInfoFacade, request, disInfoDto);
        resp = (QueryBatchFlowInfoResponse) baseResp;
        return resp;
    }

    @Override
    public CounterShareMergeVolRespDto counterHighShareMergeVol(CounterShareMergeVolReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        boolean isCounterEndFlag = false;
        if (reqDto != null && !CollectionUtils.isEmpty(reqDto.getShareMergeVolOrderList())) {
            Set<String> taCodeSet = new HashSet<String>(16);
            for (ShareMergeVolOrderReqDto dto : reqDto.getShareMergeVolOrderList()) {
                taCodeSet.add(dto.getTaCode());
            }
            // 校验只要有一个基金TA收市，都不能份额迁移或合并
            if (taCodeSet.size() > 0) {
                for (String taCode : taCodeSet) {
                    isCounterEndFlag = isCounterEnd(getSysCode(ProductChannelEnum.HIGH_FUND.getCode()), taCode, disInfoDto);
                    if (isCounterEndFlag) {
                        break;
                    }
                }
            }
        }

        if (isCounterEndFlag) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }

        CounterShareMergeVolRespDto resp = null;

        boolean validFlag = false;
        if (ShareTypeEnum.FUND_SHARE_MERGE.getCode().equals(reqDto.getShareType())) {
            validFlag = tmsCounterValidService.shareMergeVolValidate(reqDto, disInfoDto);

        } else if (ShareTypeEnum.FUND_SHARE_TRANSFER.getCode().equals(reqDto.getShareType())) {
            validFlag = tmsCounterValidService.shareTransferVolValidate(reqDto, disInfoDto);
        }
        if (validFlag) {

            CounterShareMergeRequest request = new CounterShareMergeRequest();
            BeanUtils.copyProperties(reqDto, request);

            CounterShareMergeOrderBean orderBean = new CounterShareMergeOrderBean();
            BeanUtils.copyProperties(reqDto, orderBean);
            List<ShareMergeOrderDetail> outOrderList = null;
            if (!CollectionUtils.isEmpty(reqDto.getShareMergeVolOrderList())) {
                outOrderList = new ArrayList<ShareMergeOrderDetail>(16);
                ShareMergeOrderDetail shareMergeOrder = null;
                for (ShareMergeVolOrderReqDto volOrder : reqDto.getShareMergeVolOrderList()) {
                    shareMergeOrder = new ShareMergeOrderDetail();
                    BeanUtils.copyProperties(volOrder, shareMergeOrder);
                    outOrderList.add(shareMergeOrder);
                }
            }
            orderBean.setOutShareMergeDetailList(outOrderList);
            request.setCounterShareMergeOrderBean(orderBean);

            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterShareMergeFacade, request, disInfoDto);
            if (baseResp != null) {
                CounterShareMergeResponse response = (CounterShareMergeResponse) baseResp;
                resp = new CounterShareMergeVolRespDto();
                BeanUtils.copyProperties(response, resp);
            }
        }
        return resp;
    }

    @Override
    public boolean checkVolMergeTransOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDto, DisInfoDto disInfoDto) throws Exception {
        String txCode = submitUncheckOrderDto.getTxCode();
        String checkFlag = submitUncheckOrderDto.getCheckFlag();
        boolean flag = false;
        if (CounterCheckFlagEnum.CHECKED_SUCC.getKey().equals(checkFlag)) {

            // 份额合并、迁移
            if (TxCodes.COUNTER_MERGE_VOL.equals(txCode)) {
                flag = checkCounterMergeVolOrder(submitUncheckOrderDto, dtlOrderDto, disInfoDto);

            } else if (TxCodes.COUNTER_TRANS_VOL.equals(txCode)) {
                logger.info("TmsCounterServiceImpl|dtlOrderDto:{}", dtlOrderDto != null ? dtlOrderDto.size() : 0);
                flag = checkCounterTransVolOrder(submitUncheckOrderDto, dtlOrderDto, disInfoDto, null);
            }

        } else if (CounterCheckFlagEnum.CHECKED_REJECT.getKey().equals(checkFlag)) {
            // 审核退回
            flag = checkUpdateOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        } else if (CounterCheckFlagEnum.CANCEL.getKey().equals(checkFlag)) {
            // 审核废单
            flag = checkUpdateOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        }
        return flag;
    }

    @Override
    public BaseResponse checkVolShareTransOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> checkDtlOrders, DisInfoDto disInfoDto) throws Exception {
        String checkFlag = submitUncheckOrderDto.getCheckFlag();
        BaseResponse response = new BaseResponse();
        String returnCode = ExceptionCodes.SUCCESS;
        String returnMsg = "操作成功";
        if (CounterCheckFlagEnum.CHECKED_SUCC.getKey().equals(checkFlag)) {
            List<SubmitUncheckOrderDtlDto> fundOutDtoList = new ArrayList<SubmitUncheckOrderDtlDto>();
            List<SubmitUncheckOrderDtlDto> highOutDtoList = new ArrayList<SubmitUncheckOrderDtlDto>();
            List<SubmitUncheckOrderDtlDto> piggyOutDtoList = new ArrayList<SubmitUncheckOrderDtlDto>();

            // 转出资金账号
            for (SubmitUncheckOrderDtlDto dtlDto : checkDtlOrders) {
                if (ProductChannelEnum.FUND.getCode().equals(dtlDto.getProductChannel()) && !TradeConstant.CAPITAL_INTRANSIT_PROTOCOL_TYPE.equals(dtlDto.getProtocolType())) {
                    fundOutDtoList.add(dtlDto);
                } else if (ProductChannelEnum.HIGH_FUND.getCode().equals(dtlDto.getProductChannel())
                        || ProductChannelEnum.TP_SM.getCode().equals(dtlDto.getProductChannel())) {
                    highOutDtoList.add(dtlDto);
                } else if (ProductChannelEnum.PIGGY.getCode().equals(dtlDto.getProductChannel())) {
                    piggyOutDtoList.add(dtlDto);
                }
            }
            List<String> outCpAcctNos = getOrderDtlCpAcctNos(checkDtlOrders);
            String changeCpAcctNo = getChangeCpAcctNo(submitUncheckOrderDto, outCpAcctNos);
            logger.info("changeCpAcctNo:{}", changeCpAcctNo);

            if (YesOrNoEnum.YES.getCode().equals(submitUncheckOrderDto.getCancelCard())) {
                //获取明细订单资金账号
                if (CollectionUtils.isEmpty(outCpAcctNos)) {
                    logger.error("cpAcctNos is empty");
                    response.setReturnCode(BizCounterEnum.CPACCTNO_IS_EMPTY.getCode());
                    response.setDescription(BizCounterEnum.CPACCTNO_IS_EMPTY.getDesc());
                    return response;
                }
                // 校验是否有在途
                boolean isExists = validateInTransit(submitUncheckOrderDto.getTxAcctNo(), outCpAcctNos, null);
                if (isExists) {
                    logger.error("存在在途交易");
                    response.setReturnCode(BizCounterEnum.TRANSIT_EXITS.getCode());
                    response.setDescription(BizCounterEnum.TRANSIT_EXITS.getDesc());
                    return response;
                }

                boolean transFlag = true;
                //调用账户中心换卡接口
                CpAcctSwitchContext context = new CpAcctSwitchContext();
                context.setNewCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
                context.setOldCpAcctNo(changeCpAcctNo);
                context.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
                context.setDisCode(submitUncheckOrderDto.getDisCode());
                CpAcctSwitchResult result = cpAcctSwitchOutService.process(context);
                transFlag = result.isResult();
                if (!transFlag) {
                    transFlag = false;
                    submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_FAILD.getKey());
                    submitUncheckOrderDto.setReturnCode(returnCode);
                    submitUncheckOrderDto.setDescription(returnMsg);
                    submitUncheckOrderDto.setDealNo(changeCpAcctNo);
                }

                if (!transFlag) {
                    logger.error("换卡失败");
                    response.setReturnCode(BizCounterEnum.BANK_CHANGE_ERROR.getCode());
                    response.setDescription(BizCounterEnum.BANK_CHANGE_ERROR.getDesc());
                    return response;
                }

            }

            // 当一张卡上有零售和专户产品时，有两笔审核单，但每次审核是其中一种
            boolean fundFlag = true;
            boolean highFlag = true;
            boolean piggyFlag = true;
            String fundDealNo = "";
            String highDealNo = "";
            String piggyDealNo = "";
            // step1 :  份额迁移
            logger.info("TmsCounterServiceImpl|fundOutDtoList:{}", fundOutDtoList.size());
            if (fundOutDtoList.size() > 0 && !YesOrNoEnum.YES.getCode().equals(submitUncheckOrderDto.getCancelCard())) {
                // 零售审核

                fundFlag = checkFundCounterTransVolOrder(submitUncheckOrderDto, fundOutDtoList, disInfoDto);
                fundDealNo = submitUncheckOrderDto.getDealNo();
            }
            logger.info("TmsCounterServiceImpl|highOutDtoList:{}", highOutDtoList.size());
            if (highOutDtoList.size() > 0) {
                // 专户审核
                highFlag = checkCounterTransVolOrder(submitUncheckOrderDto, highOutDtoList, disInfoDto, changeCpAcctNo);
                highDealNo = submitUncheckOrderDto.getDealNo();
            }
            logger.info("TmsCounterServiceImpl|piggyOutDtoList:{}", piggyOutDtoList.size());
            if (piggyOutDtoList.size() > 0) {
                // 储蓄罐份额迁移
                piggyFlag = checkCounterPiggyTransVolOrder(submitUncheckOrderDto, piggyOutDtoList, disInfoDto, changeCpAcctNo);
                piggyDealNo = submitUncheckOrderDto.getDealNo();
            }

            // step2 : 如果需要销卡，终止定投协议
            if (CancelCardStatusEnum.SUCC.getCode().equals(submitUncheckOrderDto.getCancelCard())) {
                stopSchedule(submitUncheckOrderDto.getTxAcctNo(), outCpAcctNos);
            }

            String appFlag = CounterAppFlagEnum.APP_SUCC.getKey();
            if (!fundFlag && !highFlag && !piggyFlag) {
                checkFlag = CounterCheckFlagEnum.CHECKED_FAILD.getKey();
                appFlag = CounterAppFlagEnum.APP_FAILD.getKey();
            } else {
                checkFlag = CounterCheckFlagEnum.CHECKED_SUCC.getKey();
            }

            logger.info("fundDealNo:{},highDealNo:{},piggyDealNo:{}", fundDealNo, highDealNo, piggyDealNo);
            if (!StringUtils.isEmpty(fundDealNo)) {
                submitUncheckOrderDto.setDealNo(fundDealNo);
            } else if (!StringUtils.isEmpty(highDealNo)) {
                submitUncheckOrderDto.setDealNo(highDealNo);
            } else if (!StringUtils.isEmpty(piggyDealNo)) {
                submitUncheckOrderDto.setDealNo(piggyDealNo);
            } else {
                submitUncheckOrderDto.setDealNo(submitUncheckOrderDto.getDealAppNo());
            }
            submitUncheckOrderDto.setAppFlag(appFlag);
            submitUncheckOrderDto.setReturnCode(returnCode);
            submitUncheckOrderDto.setDescription(returnMsg);
            checkUpdateOrder(submitUncheckOrderDto, disInfoDto, checkFlag);

        } else if (CounterCheckFlagEnum.CHECKED_REJECT.getKey().equals(checkFlag)) {
            // 审核退回
            checkUpdateOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        } else if (CounterCheckFlagEnum.CANCEL.getKey().equals(checkFlag)) {
            // 审核废单
            checkUpdateOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        }
        response.setReturnCode(returnCode);
        response.setDescription(returnMsg);
        return response;
    }

    private String getChangeCpAcctNo(SubmitUncheckOrderDto orderPo, List<String> outCpAcctNos) {
        String changeCpAcctNo = outCpAcctNos.get(0);

        QueryCustBankCardContext ctx = new QueryCustBankCardContext();
        ctx.setTxAcctNo(orderPo.getTxAcctNo());
        ctx.setDisCode(orderPo.getDisCode());
        ctx.setCpAcctNo(changeCpAcctNo);
        QueryCustBankCardResult result = queryCustBankCardOuterService.queryCudtBankCard(ctx);
        if (!StringUtils.isEmpty(result.getNewCpAcctNo())) {
            return result.getNewCpAcctNo();
        }
        return changeCpAcctNo;
    }

    private boolean validateInTransit(String txAcctNo, List<String> cpAcctNos, String disCode) {
        boolean flag = false;
        if (CollectionUtils.isEmpty(cpAcctNos)) {
            return flag;
        }
        QueryGmInTransitRequest request = new QueryGmInTransitRequest();
        request.setTxAcctNo(txAcctNo);
        request.setCpAcctNos(cpAcctNos);
        request.setSearchDisCode(TmsFacadeUtil.getAllDisCode(disCode));

        QueryGmInTransitResponse response = queryInTransitFacade.execute(request);
        if (null != response && ExceptionCodes.SUCCESS.equals(response.getReturnCode())) {
            flag = "1".equals(response.getIsUnfinlish());
        }

        if (flag) {
            return true;
        }
        QueryHighInTransitRequest request2 = new QueryHighInTransitRequest();
        request2.setTxAcctNo(txAcctNo);
        request2.setCpAcctNos(cpAcctNos);
        request2.setSearchDisCode(TmsFacadeUtil.getAllDisCode(disCode));

        QueryHighInTransitResponse response2 = queryHighInTransitFacade.execute(request2);
        if (null != response2 && ExceptionCodes.SUCCESS.equals(response2.getReturnCode())) {
            flag = "1".equals(response2.getIsUnfinlish());
        }

        if (flag) {
            return true;
        }

        // 校验储蓄罐是否需有在途
        List<String> disCodes = new ArrayList<String>();
        QuerySavingBoxVolDetailResult piggyResponse = querySavingBoxVolDetailOuterService.query(txAcctNo, TmsFacadeUtil.getAllDisCode(disCode));
        if (piggyResponse != null && piggyResponse.getDetails() != null) {
            for (QuerySavingBoxVolDetailResult.VolDetailRst vd : piggyResponse.getDetails()) {
                if (cpAcctNos.contains(vd.getCustBankId()) && !disCodes.contains(vd.getDisCode())) {
                    disCodes.add(vd.getDisCode());
                }
            }
        }
        for (String searchDisCode : disCodes) {
            boolean bFlag = querySavingBoxVolOutService.querySavingBoxVol(txAcctNo, null, cpAcctNos, searchDisCode);
            if (bFlag) {
                flag = bFlag;
                break;
            }
        }
        return flag;
    }

    private List<String> getOrderDtlCpAcctNos(List<SubmitUncheckOrderDtlDto> checkDtlOrders) {
        List<String> cpAcctNos = new ArrayList<String>(16);
        if (CollectionUtils.isEmpty(checkDtlOrders)) {
            return cpAcctNos;
        }
        for (SubmitUncheckOrderDtlDto po : checkDtlOrders) {
            if (!cpAcctNos.contains(po.getCpAcctNo())) {
                cpAcctNos.add(po.getCpAcctNo());
            }
        }
        return cpAcctNos;
    }

    /**
     * 终止定投协议
     *
     * @param txAcctNo
     * @param outCpAcctNos
     */
    private void stopSchedule(String txAcctNo, List<String> outCpAcctNos) {
        try {
            RelieveSchePlanRequest req = new RelieveSchePlanRequest();
            req.setCpAcctNos(outCpAcctNos);
            req.setTxAcctNo(txAcctNo);
            orderPlanService.relieve(req);
        } catch (Exception e) {
            logger.error("stopSchedule error:", e);
        }
    }

    /**
     * shareMergeVolCounter:(柜台审核份额合并落单)
     *
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月10日 下午3:15:59
     */
    private boolean checkCounterMergeVolOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {

        if (submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)) {
            return false;
        }

        HighShareMergeCounterRequest request = new HighShareMergeCounterRequest();
        request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
        request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
        request.setTransactorName(submitUncheckOrderDto.getTransactorName());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setConsCode(submitUncheckOrderDto.getConsCode());
        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        request.setInCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        request.setInProtocolNo(submitUncheckOrderDto.getProtocolNo());
        request.setInProtocolType(submitUncheckOrderDto.getProtocolType());
        request.setFundCode(submitUncheckOrderDto.getFundCode());
        request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());

        List<ShareMergeOutDetail> shareMergeOutDetail = new ArrayList<ShareMergeOutDetail>();
        if (!CollectionUtils.isEmpty(dtlOrderDtoList)) {
            ShareMergeOutDetail outDetail = null;
            for (SubmitUncheckOrderDtlDto dtlDto : dtlOrderDtoList) {
                outDetail = new ShareMergeOutDetail();
                outDetail.setCpAcctNo(dtlDto.getCpAcctNo());
                outDetail.setBankAcct(dtlDto.getBankAcct());
                outDetail.setBankCode(dtlDto.getBankCode());
                outDetail.setAppVol(dtlDto.getAppVol());
                outDetail.setProtocolNo(dtlDto.getProtocolNo());
                outDetail.setProtocolType(dtlDto.getProtocolType());

                shareMergeOutDetail.add(outDetail);
            }
        }
        request.setShareMergeOutDetail(shareMergeOutDetail);
        logger.debug("High | checkCounterMergeVolOrder request:{}", JSON.toJSONString(request));

        BaseResponse baseResp = TmsFacadeUtil.execute(highShareMergeCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                HighShareMergeCounterResponse shareMergeCounterResponse = (HighShareMergeCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(shareMergeCounterResponse.getDealNo());
                checkUpdateOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    /**
     * 储蓄罐份额迁移
     *
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    private boolean checkCounterPiggyTransVolOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto, String changeCpAcctNo) throws Exception {

        if (submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)) {
            return false;
        }

        Map<String, List<SubmitUncheckOrderDtlDto>> orderDtlMap = dtlOrderDtoList.stream().collect(Collectors.groupingBy(SubmitUncheckOrderDtlDto::getDisCode));
        int count = 0;
        for (List<SubmitUncheckOrderDtlDto> submitUncheckOrderDtlPos : orderDtlMap.values()) {
            SubmitUncheckOrderDtlDto uncheckOrderDtlPo = submitUncheckOrderDtlPos.get(0);

            FtxVolMigrateRequest request = new FtxVolMigrateRequest();
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactor(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(uncheckOrderDtlPo.getDisCode());
            request.setCustNo(submitUncheckOrderDto.getTxAcctNo());
            request.setCorpSno(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setTCustBankId(submitUncheckOrderDto.getCpAcctNo());
            //份额迁移，转出卡
            if (StringUtil.isEmpty(changeCpAcctNo)) {
                //获取明细订单资金账号
                List<String> outCpAcctNos = getOrderDtlCpAcctNos(dtlOrderDtoList);
                changeCpAcctNo = getChangeCpAcctNo(submitUncheckOrderDto, outCpAcctNos);
            }
            SubmitUncheckOrderDtlDto dtlDto = getDtlOrderDto(changeCpAcctNo, dtlOrderDtoList);
            request.setCustBankId(dtlDto.getCpAcctNo());
            logger.debug("Piggy | checkCounterPiggyTransVolOrder request:{}", JSON.toJSONString(request));
            BusinessAspect.setTradeCommomParams(null, null);
            FtxVolMigrateResponse response = volMigrateOuterService.volMigrate(request);

            if (response != null) {
                submitUncheckOrderDto.setReturnCode(response.getReturnCode());
                submitUncheckOrderDto.setDescription(response.getDescription());
                if (TradeInvoker.isSuccess(response)) {
                    count++;
                    submitUncheckOrderDto.setDealNo(response.getDealNo());
                } else {
                    logger.error("tmsCounterServiceImpl|checkCounterPiggyTransVolOrder|check fund error,retCode:{},retMsg:{}", response.getReturnCode(), response.getDescription());
                    return false;
                }
            }
        }
        if (count == orderDtlMap.size()) {
            submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
            return true;
        }
        submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_FAILD.getKey());
        return false;
    }

    /**
     * shareTransferVolCounter:(柜台审核份额迁移落单)
     *
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月10日 下午3:15:59
     */
    private boolean checkCounterTransVolOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto, String changeCpAcctNo) throws Exception {

        if (submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)) {
            return false;
        }

        Map<String, List<SubmitUncheckOrderDtlDto>> orderDtlMap = dtlOrderDtoList.stream().collect(Collectors.groupingBy(SubmitUncheckOrderDtlDto::getDisCode));
        int count = 0;
        for (List<SubmitUncheckOrderDtlDto> submitUncheckOrderDtlPos : orderDtlMap.values()) {
            SubmitUncheckOrderDtlDto uncheckOrderDtlPo = submitUncheckOrderDtlPos.get(0);

            HighShareTransferCounterRequest request = new HighShareTransferCounterRequest();
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(uncheckOrderDtlPo.getDisCode());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setInCpAcctNo(submitUncheckOrderDto.getCpAcctNo());

            if (StringUtil.isEmpty(changeCpAcctNo)) {
                //获取明细订单资金账号
                List<String> outCpAcctNos = getOrderDtlCpAcctNos(dtlOrderDtoList);
                changeCpAcctNo = getChangeCpAcctNo(submitUncheckOrderDto, outCpAcctNos);
            }
            SubmitUncheckOrderDtlDto dtlDto = getDtlOrderDto(changeCpAcctNo, dtlOrderDtoList);

            //份额迁移，转出卡
            request.setOutCpAcctNo(changeCpAcctNo);
            request.setOutBankAcct(dtlDto.getBankAcct());
            request.setOutBankCode(dtlDto.getBankCode());
            List<String> fundCodeList = submitUncheckOrderDtlPos.stream().map(SubmitUncheckOrderDtlDto::getFundCode).collect(Collectors.toList());

            request.setFundCodes(fundCodeList);
            logger.debug("High | checkCounterTransVolOrder request:{}", JSON.toJSONString(request));

            BaseResponse baseResp = TmsFacadeUtil.execute(highShareTransferCounterFacade, request, disInfoDto);
            if (baseResp != null) {
                submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
                submitUncheckOrderDto.setDescription(baseResp.getDescription());
                if (TmsFacadeUtil.isSuccess(baseResp)) {
                    count++;
                    HighShareTransferCounterResponse shareMergeCounterResponse = (HighShareTransferCounterResponse) baseResp;
                    submitUncheckOrderDto.setDealNo(shareMergeCounterResponse.getDealNo());
                    submitUncheckOrderDto.setFundCodeAndDtlNoMap(shareMergeCounterResponse.getMap());
                } else {
                    logger.error("tmsCounterServiceImpl|checkFundCounterTransVolOrder|check fund error,retCode:{},retMsg:{}", baseResp.getReturnCode(), baseResp.getDescription());
                }
            }
        }
        // 全部分销的都申请成功才算成功
        if (count == orderDtlMap.size()) {
            submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
            return true;
        }
        submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_FAILD.getKey());
        return false;
    }

    private SubmitUncheckOrderDtlDto getDtlOrderDto(String changeCpAcctNo, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList) {
        if (StringUtil.isEmpty(changeCpAcctNo) || CollectionUtils.isEmpty(dtlOrderDtoList)) {
            return null;
        }
        SubmitUncheckOrderDtlDto dtlDto = null;
        for (SubmitUncheckOrderDtlDto dto : dtlOrderDtoList) {
            if (changeCpAcctNo.equals(dto.getCpAcctNo())) {
                dtlDto = dto;
                break;
            }
        }
        return dtlDto;
    }

    private boolean checkFundCounterTransVolOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {
        if (submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)) {
            return false;
        }

        Map<String, List<SubmitUncheckOrderDtlDto>> orderDtlMap = dtlOrderDtoList.stream().collect(Collectors.groupingBy(SubmitUncheckOrderDtlDto::getDisCode));

        int count = 0;
        for (List<SubmitUncheckOrderDtlDto> submitUncheckOrderDtlPos : orderDtlMap.values()) {
            SubmitUncheckOrderDtlDto uncheckOrderDtlPo = submitUncheckOrderDtlPos.get(0);

            ShareTransferCounterRequest request = new ShareTransferCounterRequest();
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(uncheckOrderDtlPo.getDisCode());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setInCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            //份额迁移，转出卡
            request.setOutCpAcctNos(getCpAcctNos(submitUncheckOrderDtlPos));

            logger.info("Fund | checkCounterTransVolOrder request:{}", JSON.toJSONString(request));

            BaseResponse baseResp = TmsFacadeUtil.execute(shareTransferCounterFacade, request, disInfoDto);
            if (baseResp != null) {
                submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
                submitUncheckOrderDto.setDescription(baseResp.getDescription());
                if (TmsFacadeUtil.isSuccess(baseResp)) {
                    count++;
                    ShareTransferCounterResponse shareMergeCounterResponse = (ShareTransferCounterResponse) baseResp;
                    submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                    submitUncheckOrderDto.setDealNo(shareMergeCounterResponse.getDealNo());
                } else {
                    logger.error("tmsCounterServiceImpl|checkFundCounterTransVolOrder|check fund error,retCode:{},retMsg:{}", baseResp.getReturnCode(), baseResp.getDescription());
                }
            }
        }
        // 全部分销的都申请成功才算成功
        if (count == orderDtlMap.size()) {
            submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
            return true;
        }
        submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_FAILD.getKey());
        return false;
    }

    /**
     * 获取资金账号
     * getCpAcctNos:获取资金账号
     *
     * @param dtlOrderDtoList
     * @return
     * <AUTHOR>
     * @date 2022年4月17日 上午10:45:09
     */
    private List<String> getCpAcctNos(List<SubmitUncheckOrderDtlDto> dtlOrderDtoList) {
        List<String> cpAcctNos = new ArrayList<String>();
        if (CollectionUtils.isEmpty(dtlOrderDtoList)) {
            return cpAcctNos;
        }
        for (SubmitUncheckOrderDtlDto dto : dtlOrderDtoList) {
            if (!cpAcctNos.contains(dto.getCpAcctNo())) {
                cpAcctNos.add(dto.getCpAcctNo());
            }
        }
        return cpAcctNos;
    }

    /**
     * checkUpdateOrder:(更新订单审核状态)
     *
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @param checkFlag
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月22日 下午2:17:56
     */
    public boolean checkUpdateOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String checkFlag) throws Exception {
        logger.debug("submitUncheckOrderDto:{}", JSON.toJSONString(submitUncheckOrderDto));
        SubmitCheckOrderRequest request = new SubmitCheckOrderRequest();
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        request.setCheckFlag(checkFlag);
        request.setOrderReturnCode(submitUncheckOrderDto.getReturnCode());
        request.setOrderReturnMsg(submitUncheckOrderDto.getDescription());
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setMemo(submitUncheckOrderDto.getMemo());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setChecker(submitUncheckOrderDto.getChecker());
        request.setCheckDtm(submitUncheckOrderDto.getCheckDtm());
        request.setAppFlag(submitUncheckOrderDto.getAppFlag());
        request.setFundCodeAndDtlNoMap(submitUncheckOrderDto.getFundCodeAndDtlNoMap());
        request.setIntransitAssetMemo(submitUncheckOrderDto.getIntransitAssetMemo());
        logger.debug("checkUpdateOrder request:{}", JSON.toJSONString(request));

        BaseResponse baseResp = TmsFacadeUtil.execute(submitCheckOrderFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            return true;
        } else {
            if (baseResp != null) {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            } else {
                return false;
            }
        }
    }

    @Override
    public List<CounterShareMergeTradeOrderDto> queryHighShareMergeTradeOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        // 订单明细信息
        QueryHighFundDealOrderDtlChangeCardCondition queryCondition = new QueryHighFundDealOrderDtlChangeCardCondition();
        queryCondition.setDealAppNo(reqDto.getDealAppNo());
        QueryHighFundDealOrderDtlChangeCardRequest request = new QueryHighFundDealOrderDtlChangeCardRequest();
        request.setQueryCondition(queryCondition);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryHighFundDealOrderDtlChangeCardFacade, request, disInfoDto);

        List<HighFundDealOrderDtlChangeCardBean> fundDealOrderDtlBeanList = null;
        if (baseResp != null) {
            QueryHighFundDealOrderDtlChangeCardResponse response = (QueryHighFundDealOrderDtlChangeCardResponse) baseResp;
            fundDealOrderDtlBeanList = response.getFundDealOrderDtlBeanList();
        }

        List<CounterShareMergeTradeOrderDto> returnDto = null;
        if (!CollectionUtils.isEmpty(fundDealOrderDtlBeanList)) {
            returnDto = new ArrayList<CounterShareMergeTradeOrderDto>(16);
            CounterShareMergeTradeOrderDto dto = null;
            for (HighFundDealOrderDtlChangeCardBean dealOrderDtl : fundDealOrderDtlBeanList) {
                dto = new CounterShareMergeTradeOrderDto();
                dto.setDealNo(reqDto.getDealNo());
                dto.setTxAcctNo(dealOrderDtl.getCustNo());
                dto.setCustName(dealOrderDtl.getCustName());
                dto.setFundCode(dealOrderDtl.getFundCode());
                dto.setFundName(dealOrderDtl.getFundName());

                dto.setOutBankAcct(dealOrderDtl.getOutBankAcct());
                dto.setOutProtocolNo(dealOrderDtl.getOutProtocolNo());
                dto.setAppVol(dealOrderDtl.getAppVol());
                dto.setInBankAcct(dealOrderDtl.getSaveBankAcct());
                dto.setInProtocolNo(dealOrderDtl.getSaveProtocolNo());
                dto.setBeforeInAvailVol(MathUtils.getBigDecimal(dealOrderDtl.getSaveBeforeBankVol()));
                dto.setAfterInAvailVol(MathUtils.getBigDecimal(dealOrderDtl.getSaveBackBankVol()));
                dto.setRetDesc(dealOrderDtl.getResult());
                returnDto.add(dto);
            }
        }
        return returnDto;
    }

    @Override
    public List<CounterShareMergeTradeOrderDto> queryPiggyShareMergeTradeOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        QueryPiggyOrderDetailRequest request = new QueryPiggyOrderDetailRequest();
        request.setCorpSno(reqDto.getDealAppNo());
        QueryPiggyOrderDetailResponse response = queryPiggyOrderDetailFacade.process(request);

        List<CounterShareMergeTradeOrderDto> returnDto = new ArrayList<CounterShareMergeTradeOrderDto>();
        if (response != null && TradeInvoker.isSuccess(response)) {
            // 查询审核订单明细表，查询转入份额
            QuerySubmitCheckOrderDtlRequest checkDtlRequest = new QuerySubmitCheckOrderDtlRequest();
            checkDtlRequest.setDealAppNo(reqDto.getDealAppNo());
            BaseResponse checkDtlResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderDtlFacade, checkDtlRequest, disInfoDto);
            Map<String, BigDecimal> preAppVolMap = Maps.newHashMap();
            if (checkDtlResp != null) {
                QuerySubmitCheckOrderDtlResponse dtlOrderResponse = (QuerySubmitCheckOrderDtlResponse) checkDtlResp;
                preAppVolMap.putAll(dtlOrderResponse.getPreAppVolMap());
            }

            CounterShareMergeTradeOrderDto dto = new CounterShareMergeTradeOrderDto();
            dto.setDealNo(response.getDealNo());
            dto.setTxAcctNo(response.getCustNo());
            dto.setCustName("");
            dto.setzBusiCode("");
            dto.setmBusiCode("");
            dto.setFundCode(TmsCounterConstants.PIGGY_FUND_CODE);
            dto.setFundName(TmsCounterConstants.PIGGY_FUND_NAME);
            dto.setOutBankAcct(response.getBankAcct());
            dto.setOutProtocolNo("");
            dto.setAppVol(response.getAppVol());
            dto.setInBankAcct(response.getTBankAcct());
            dto.setInProtocolNo("");
//            String key = dto.getFundCode();
//            BigDecimal preAppVol = preAppVolMap.get(key);
//            if(preAppVol == null){
//                preAppVol = BigDecimal.ZERO;
//            }
//            dto.setBeforeInAvailVol(preAppVol);
//            dto.setAfterInAvailVol(preAppVol.add(response.getAppVol()));
            dto.setRetDesc(desc(response.getTradeStat()));
            returnDto.add(dto);
        }

        return returnDto;
    }

    /**
     * @param tradeStat
     * @return
     */
    private String desc(String tradeStat) {
        if ("1".equals(tradeStat)) {
            return "交易失败";
        } else if ("5".equals(tradeStat)) {
            return "交易成功";
        } else if ("6".equals(tradeStat)) {
            return "处理中";
        }
        return "";
    }

    @Override
    public HighActiDiscountRatioModel getHighActiDiscountRatio(String fundCode, String shareClass, String paySource, String invstType, String businessCode,
                                                               String bankCode, String disCode) {
        HighActiDiscountRatioModel actiDiscountRatioModel = highProductService.getHighActiRateDiscount(fundCode, shareClass,
                paySource, invstType, businessCode, bankCode, disCode);
        return actiDiscountRatioModel;
    }

    @Override
    public QueryBatchFlowStatResponse getBatchFlowStatList() throws Exception {
        QueryBatchFlowStatResponse resp = null;
        QueryBatchFlowStatRequest request = new QueryBatchFlowStatRequest();
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryBatchFlowStatFacade, request, null);
        resp = (QueryBatchFlowStatResponse) baseResp;

        return resp;
    }

    @Override
    public QueryHighTaBusinessListResponse queryTaBusinessList(String taskId, String flowStat) throws Exception {
        QueryHighTaBusinessListResponse queryHighTaBusinessListResponse = null;
        QueryHighTaBusinessListRequest request = new QueryHighTaBusinessListRequest();
        request.setTaskId(taskId);
        request.setFlowStat(flowStat);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryHighTaBusinessListFacade, request, null);
        queryHighTaBusinessListResponse = (QueryHighTaBusinessListResponse) baseResp;
        return queryHighTaBusinessListResponse;
    }

    @Override
    public QueryTaBusinessBatchCountResponse queryTaBusinessBatchCount(String taskId) throws Exception {
        QueryTaBusinessBatchCountResponse queryTaBusinessBatchCountResponse = null;
        QueryTaBusinessBatchCountRequest request = new QueryTaBusinessBatchCountRequest();
        request.setTaskId(taskId);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryTaBusinessBatchCountFacade, request, null);
        queryTaBusinessBatchCountResponse = (QueryTaBusinessBatchCountResponse) baseResp;
        return queryTaBusinessBatchCountResponse;
    }

    @Override
    public QueryTaCountNotEndResponse queryTaCounterNotEnd() throws Exception {
        QueryTaCountNotEndResponse queryTaCountNotEndResponse = null;
        QueryTaCounterNotEndRequest request = new QueryTaCounterNotEndRequest();
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryTaCounterNotEndFacade, request, null);
        queryTaCountNotEndResponse = (QueryTaCountNotEndResponse) baseResp;
        return queryTaCountNotEndResponse;
    }

    @Override
    public CounterRespDto counterSaveOrDelNotEndTa(String actionType, String sysCode, List<FundTaInfoDto> taDtoList, DisInfoDto disInfoDto) throws Exception {
        CounterSaveOrDelNotEndTaRequest request = new CounterSaveOrDelNotEndTaRequest();
        request.setActionType(actionType);
        request.setSysCode(sysCode);
        if (!CollectionUtils.isEmpty(taDtoList)) {
            List<CounterNotEndTaBean> notEndList = new ArrayList<CounterNotEndTaBean>(16);
            CounterNotEndTaBean bean = null;
            for (FundTaInfoDto fundTaDto : taDtoList) {
                bean = new CounterNotEndTaBean();
                bean.setTaCode(fundTaDto.getTaCode());
                bean.setTaName(fundTaDto.getTaName());
                bean.setFundCode(fundTaDto.getFundCode());
                bean.setFundAttr(fundTaDto.getFundAttr());
                bean.setFundType(fundTaDto.getFundType());

                notEndList.add(bean);
            }
            request.setNotEndTaBeanList(notEndList);
        }
        CounterRespDto resp = null;
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterSaveOrDelNotEndTaFacade, request, disInfoDto);
        if (baseResp != null) {
            CounterSaveOrDelNotEndTaResponse response = (CounterSaveOrDelNotEndTaResponse) baseResp;
            resp = new CounterRespDto();
            BeanUtils.copyProperties(response, resp);
        }
        return resp;
    }

    @Override
    public BatchFlowBean getBatchStat(String sysCode, String taskId) throws Exception {
        QueryHighBatchFlowInfoRequest request = new QueryHighBatchFlowInfoRequest();
        request.setTaskId(taskId);
        request.setSysCode(sysCode);

        QueryHighBatchFlowInfoResponse resp = null;
        BaseResponse baseResp = TmsFacadeUtil.execute(queryHighBatchFlowInfoFacade, request, null);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            resp = (QueryHighBatchFlowInfoResponse) baseResp;
            List<BatchFlowBean> list = resp.getFlowList();
            if (!CollectionUtils.isEmpty(list)) {
                return list.get(0);
            }
        }
        return new BatchFlowBean();

    }

    @Override
    public QueryCurTaDtAckResponse queryCustCurTaDtAckVol(String txAcctNo, String cpAcctNo, String fundCode, String protocolNo, String disCode) throws Exception {
        QueryCurTaDtAckResponse resp = null;
        QueryCurTaDtAckRequest request = new QueryCurTaDtAckRequest();
        request.setTxAcctNo(txAcctNo);
        request.setCpAcctNo(cpAcctNo);
        request.setFundCode(fundCode);
        request.setProtocolNo(protocolNo);
        request.setDisCode(disCode);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCurTaDtAckFacade, request, null);
        resp = (QueryCurTaDtAckResponse) baseResp;

        return resp;
    }


    @Override
    public void validateReplyOrder(CounterOrderDto condition, DisInfoDto disInfoDto) throws Exception {
        CounterQueryOrderReqDto queryCondition = new CounterQueryOrderReqDto();
        queryCondition.setTxAcctNo(condition.getTxAcctNo());
        queryCondition.setTxCode(condition.getTxCode());
        queryCondition.setFundCode(condition.getFundCode());
        CounterQueryOrderRespDto counterQueryOrderRespDto = counterQueryOrder(queryCondition, disInfoDto);
        List<CounterOrderDto> counterDealList = null;
        if (counterQueryOrderRespDto != null) {
            counterDealList = counterQueryOrderRespDto.getCounterOrderList();
        }

        checkMaterialId(condition, counterDealList);

    }

    private static void checkMaterialId(CounterOrderDto condition, List<CounterOrderDto> counterDealList) {
        if (CollectionUtils.isEmpty(counterDealList)) {
            return;
        }
        for (CounterOrderDto counterOrderDto : counterDealList) {
            if (!CheckFlagEnum.CHECK_ABOLISH.getCode().equals(counterOrderDto.getCheckFlag()) && !counterOrderDto.getDealAppNo().equals(condition.getDealAppNo())) {
                if (StringUtils.isNotEmpty(counterOrderDto.getMaterialId()) && counterOrderDto.getMaterialId().equals(condition.getMaterialId())) {
                    throw new TmsCounterException("", "该材料已使用, 请勿重复下单");
                } else if (StringUtils.isNotEmpty(counterOrderDto.getAppointmentDealNo()) && counterOrderDto.getAppointmentDealNo().equals(condition.getAppointmentDealNo())) {
                    throw new TmsCounterException("", "该预约已使用，请勿重复下单");
                }
            }
        }
    }

    @Override
    public BaseResponse checkVolOnlineTransOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<ExchangeCardMaterialDtlDto> materialDtlDtos) {
        logger.debug("submitUncheckOrderDto:{}", JSON.toJSONString(submitUncheckOrderDto));
        SubmitCheckOrderRequest request = new SubmitCheckOrderRequest();
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        request.setCheckFlag(submitUncheckOrderDto.getCheckFlag());
        request.setOrderReturnCode(submitUncheckOrderDto.getReturnCode());
        request.setOrderReturnMsg(submitUncheckOrderDto.getDescription());
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setMemo(submitUncheckOrderDto.getMemo());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setChecker(submitUncheckOrderDto.getChecker());
        request.setCheckDtm(submitUncheckOrderDto.getCheckDtm());
        request.setAppFlag(submitUncheckOrderDto.getAppFlag());
        request.setFundCodeAndDtlNoMap(new HashMap<>(16));
        request.setIntransitAssetMemo(submitUncheckOrderDto.getIntransitAssetMemo());
        Map<String, String> materialDtlMap = new HashMap<String, String>(16);
        if (!CollectionUtils.isEmpty(materialDtlDtos)) {
            for (ExchangeCardMaterialDtlDto dto : materialDtlDtos) {
                materialDtlMap.put(dto.getDealDtlAppNo(), dto.getCheckFlag());
            }
        }
        request.setMaterialCheckMap(materialDtlMap);
        logger.debug("checkUpdateOrder request:{}", JSON.toJSONString(request));


        BaseResponse baseResp = TmsFacadeUtil.execute(submitCheckOrderFacade, request, null);
        return baseResp;
    }

    @Override
    public BaseResponse saveVoucherFile(String dealAppNo, String materialType, String fileName, byte[] fileBytes) {
        UploadVoucherFileRequest request = new UploadVoucherFileRequest();
        request.setDealAppNo(dealAppNo);
        request.setFileName(fileName);
        request.setFileBytes(fileBytes);
        request.setMaterialType(materialType);
        request.setOperationSource(OperationSourceEnum.SYSTEM.getCode());

        return TmsFacadeUtil.execute(uploadVoucherFileFacade, request, null);
    }

    @Override
    public BaseResponse deleteVoucherFile(String dealDtlAppNo) {
        DeleteVoucherFileRequest request = new DeleteVoucherFileRequest();
        request.setDealDtlAppNo(dealDtlAppNo);

        return TmsFacadeUtil.execute(deleteVoucherFileFacade, request, null);
    }

    @Override
    public CounterEndPreCheckResponse queryCounterEndPreCheck(String sysCode) {
        CounterEndPreCheckRequest request = new CounterEndPreCheckRequest();
        request.setSysCode(sysCode);

        return (CounterEndPreCheckResponse) TmsFacadeUtil.execute(counterEndPreCheckFacade, request, null);
    }

    @Override
    public BaseResponse checkChangeDiscountOrder(SubmitUncheckOrderDto submitUncheckOrderDto) {
        logger.debug("submitUncheckOrderDto:{}", JSON.toJSONString(submitUncheckOrderDto));
        SubmitCheckOrderRequest request = new SubmitCheckOrderRequest();
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        request.setCheckFlag(submitUncheckOrderDto.getCheckFlag());
        request.setOrderReturnCode(submitUncheckOrderDto.getReturnCode());
        request.setOrderReturnMsg(submitUncheckOrderDto.getDescription());
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setMemo(submitUncheckOrderDto.getMemo());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setChecker(submitUncheckOrderDto.getChecker());
        request.setCheckDtm(submitUncheckOrderDto.getCheckDtm());
        request.setAppFlag(submitUncheckOrderDto.getAppFlag());
        logger.debug("checkUpdateOrder request:{}", JSON.toJSONString(request));

        return TmsFacadeUtil.execute(submitCheckOrderFacade, request, null);
    }

    @Override
    public QueryDealOrderRefundResponse queryDealOrder(QueryDealOrderRefundRequest request) throws Exception {
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryDealOrderRefundFacade, request, null);

        return (QueryDealOrderRefundResponse) baseResp;
    }

    @Override
    public CounterModifyRefundDirectionResponse modifyRefundDir(CounterModifyRefundDirectionBean bean, DisInfoDto disInfoDto) throws Exception {
        if (isCounterEnd(getSysCode(bean.getProductChannel()), bean.getTaCode(), disInfoDto)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
        }
        CounterModifyRefundDirectionRequest request = new CounterModifyRefundDirectionRequest();
        request.setCounterModifyRefundDirectionBean(bean);

        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterModifyRefundDirectionFacade, request, disInfoDto);
        return (CounterModifyRefundDirectionResponse) baseResp;
    }

    @Override
    public CounterOrderDto queryCounterOrder(String dealAppNo, boolean inHbJg) throws Exception {
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(1);
        queryReqDto.setPageSize(1);
        queryReqDto.setIsHBJGAuth(inHbJg ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        CounterQueryOrderRespDto counterQueryOrderRespDto = counterQueryOrder(queryReqDto, null);

        List<CounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();
        if (CollectionUtils.isEmpty(counterOrderList)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_DEAL_NOT_EXIT);
        }
        return getCounterOrder(counterOrderList, dealAppNo);
    }

    /**
     * 校验购买下单信息
     *
     * @param appDt           申请日期
     * @param appTm           申请时间
     * @param counterOrderDto 柜台订单
     * @return 校验结果
     * @throws Exception
     */
    @Override
    public CheckBuyInfoResultDto checkBuyInfo(String appDt, String appTm, CounterOrderDto counterOrderDto) throws Exception {
        HighProductBaseInfoModel highProductBaseModel = highProductService.getHighProductBaseInfo(counterOrderDto.getFundCode());
        if (highProductBaseModel == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }
        // 获取最新上报日期
        String newSubmitTaDt = highProductAppointService.getBuySubmitTaDt(highProductBaseModel, BusiTypeEnum.BUY.getCode(), appDt, appTm, highProductBaseModel.getDisCode());
        // 获取快照日期对应的上报日
        if (StringUtils.isBlank(counterOrderDto.getOrderFormMemo())) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "柜台订单中的快照不存在");
        }
        CounterOrderFormDto counterOrderFormDto = JSON.parseObject(counterOrderDto.getOrderFormMemo(), CounterOrderFormDto.class);
        if (counterOrderFormDto == null) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "柜台订单中的快照不存在");
        }
        // 根据快照中的申请日期,预约日历,查询旧的上报日
        CounterOrderFormDto.CounterProductAppointmentInfoBean counterProductAppointmentInfoBean = counterOrderFormDto.getCounterProductAppointmentInfoBean();
        String oldSubmitDt = highProductAppointService.getBuySubmitTaDt(highProductBaseModel, counterProductAppointmentInfoBean.getOpenStartDt(), counterOrderDto.getAppDt());
        logger.info("checkBuyInfo-校验新旧上报日,newSubmitTaDt={},oldSubmitDt={}", newSubmitTaDt, oldSubmitDt);
        // 对比上报日
        CheckBuyInfoResultDto checkBuyInfoResultDto = new CheckBuyInfoResultDto();
        checkBuyInfoResultDto.setSubmitDt(newSubmitTaDt);
        checkBuyInfoResultDto.setSubmitDtChanged(newSubmitTaDt.equals(oldSubmitDt) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        return checkBuyInfoResultDto;
    }

    /**
     * getCounterOrder:(获取柜台订单)
     *
     * @param counterOrderList 柜台订单
     * @param dealAppNo        申请单号
     * @return 柜台订单
     */
    private CounterOrderDto getCounterOrder(List<CounterOrderDto> counterOrderList, String dealAppNo) {
        if (CollectionUtils.isEmpty(counterOrderList)) {
            return null;
        }
        for (CounterOrderDto counterOrderDto : counterOrderList) {
            if (counterOrderDto.getDealAppNo().equals(dealAppNo)) {
                return counterOrderDto;
            }
        }
        return null;
    }
}
