$(function(){
	Init.init();

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	
	ApplyMergeVol.checkOrder = {};	 
	ApplyMergeVol.init(checkId,custNo,disCode,idNo);
});

var ApplyMergeVol = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryMergeTransCheckOrderById(checkId, ApplyMergeVol.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_MERGE_TRANS_CONFIRM_URL, CounterAbolish.Abolish, ApplyMergeVol.checkOrder);
		});
	}, 
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		
		ApplyMergeVol.checkOrder = bodyData.checkOrder || {};
		ApplyMergeVol.checkDtlOrder = bodyData.checkDtlOrder || [];
        var orderFile = bodyData.orderFile || {};// CRM线上资料
				
		if(CommonUtil.isEmpty(ApplyMergeVol.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		/*
		if(ApplyMergeVol.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}*/

        OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料


        // 转出信息
		BodyView.setTransOutTableView("checkOutInfo", ApplyMergeVol.checkDtlOrder, ApplyMergeVol.checkOrder.disCode);
		// 转入信息
		BodyView.setTransInTableView("checkInInfo", ApplyMergeVol.checkOrder);
		/**other*/
		BodyView.setShowOperInfo(ApplyMergeVol.checkOrder);
	},
}
