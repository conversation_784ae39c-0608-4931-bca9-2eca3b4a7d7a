/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckResponse;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterRespDto;
import com.howbuy.tms.counter.dto.FundTaInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description: 柜台收市
 * <AUTHOR>
 * @date 2017年9月20日 上午10:04:36
 * @since JDK 1.6
 */
@Controller
public class CounterFundEndController extends AbstractController {

    /**
     * 
     * counterEnd:(查询柜台收市TA信息)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月14日 下午1:16:34
     */
    @RequestMapping("tmscounter/fund/queryCounterEndTA.htm")
    public void queryCounterEndTA(HttpServletRequest request, HttpServletResponse response) throws Exception {
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(DisCodeEnum.HM.getCode());

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respDto", tmsFundCounterService.queryCounterEndTaInfo(SysCodeEnum.BATCH_GM.getCode(), disInfoDto));
        //bodyResult.put("proTaList", tmsCounterOutService.queryFundTaInfo(ProductChannelNewEnum.FUND.getCode()));
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    /**
     * 
     * saveOrDelCounterNotEndTA:(保存或删除柜台不收市TA)
     * 
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月14日 下午3:49:10
     */
    @RequestMapping("tmscounter/fund/saveOrDelCounterNotEndTA.htm")
    public void saveOrDelCounterNotEndTA(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String taFunds = request.getParameter("taFunds");
        // (同OperationTypeEnum   "1":"添加","2":"修改","3":"删除")
        String actionType = request.getParameter("actionType");
        List<FundTaInfoDto> taDtoList = JSON.parseArray(taFunds, FundTaInfoDto.class);
        if(CollectionUtils.isEmpty(taDtoList)){
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_SAVE_OR_DEL_PARAM_IS_NULL);
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(DisCodeEnum.HM.getCode());
        CounterRespDto respDto = tmsFundCounterService.counterSaveOrDelNotEndTa(actionType, SysCodeEnum.BATCH_GM.getCode(), taDtoList, disInfoDto);

        TmsCounterResult rst = null;
        if (respDto != null) {
            rst = new TmsCounterResult(respDto.getReturnCode(), respDto.getDescription());
        } else {
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
        }
        rst.setBody(respDto);
        WebUtil.write(response, rst);
    }

    /**
     * counterEnd:(公募柜台收市)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月14日 下午1:15:41
     */
    @RequestMapping("tmscounter/fund/counterend.htm")
    public void counterEnd(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Date currDate = new Date();
        String currDay = DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN);
        Date counterEndDate = DateUtil.parseDate(currDay + "150000", DateUtil.STR_PATTERN);
        if (currDate.compareTo(counterEndDate) < 0) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END_TIME);
        }
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(DisCodeEnum.HM.getCode());
        tmsFundCounterService.counterEnd(disInfoDto);

        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("code", TmsCounterResultEnum.SUCC.getCode());
        rst.put("desc", TmsCounterResultEnum.SUCC.getDesc());
        WebUtil.write(response, rst);
    }

    @RequestMapping("tmscounter/fund/endprecheck.htm")
    public void endPreCheck(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        CounterEndPreCheckResponse preCheckResponse = tmsCounterService.queryCounterEndPreCheck(SysCodeEnum.BATCH_GM.getCode());

        bodyResult.put("rsList", preCheckResponse.getCheckResultList());
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }
}
