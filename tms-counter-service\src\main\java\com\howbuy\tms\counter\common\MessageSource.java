/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;

import java.util.Locale;

import org.springframework.context.support.ResourceBundleMessageSource;


/**
 * @description:( Description:读取properties错误码对应的描述) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月27日 下午3:09:08
 * @since JDK 1.6
 */
public class MessageSource {
    private MessageSource() {
    }

    private static ResourceBundleMessageSource resourceBundleMessageSource;

    public static void setResourceBundleMessageSource(ResourceBundleMessageSource resourceBundleMessageSource) {
        MessageSource.resourceBundleMessageSource = resourceBundleMessageSource;
    }

    /**
     * 
     * getMessageByCode:根据编码获取中文信息
     * 
     * @param code
     *            编码
     * @param args
     *            参数
     * @return
     * @return String
     * <AUTHOR>
     * @date 2017年3月27日 下午15:10:18
     */
    public static String getMessageByCode(String code, Object[] args) {
        try {
            return resourceBundleMessageSource.getMessage(code, args, Locale.CHINA);
        } catch (Throwable e) {
            return null;
        }
    }

    /**
     * 
     * getMessageByCode:根据编码获取中文信息
     * 
     * @param code
     *            编码
     * @return
     * @return String
     * <AUTHOR>
     * @date 2017年3月27日 下午15:10:18
     */
    public static String getMessageByCode(String code) {
        try {
            return resourceBundleMessageSource.getMessage(code, null, Locale.CHINA);
        } catch (Throwable e) {
            return null;
        }
    }
}

