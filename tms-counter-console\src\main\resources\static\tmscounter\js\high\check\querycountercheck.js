/**
 *柜台审核
 *<AUTHOR>
 *@date 2017-04-01 15:23
 */
$(function () {

    // 查询
    QueryHighCounterCheck.init();

    //查询待审核订单
    QueryHighCounterCheck.queryOrderInfo();

});

var QueryHighCounterCheck = {
    init: function () {
        $("#queryBtn").on('click', function () {
            QueryHighCounterCheck.queryOrderInfo();
        });

        // 初始化下拉框
        QueryHighCounterCheck.initSelect();
    },

    /**
     * 初始化下拉框
     */
    initSelect: function () {
        //初始化交易码下拉框
        var selectTxCodeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.COUNTER_TXCODE_MAP);
        $("#selectTxCode").empty();
        $("#selectTxCode").html(selectTxCodeHtml);

        //初始化审核下拉框,默认 查询未审核订单
        var selectCheckFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.COUNTER_CHECK_FLAG_MAP, '0');
        $("#selectCheckFlag").html(selectCheckFlagHtml);

        // 初始化产品通道下拉框
        var selectProductChannelHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PRODUCT_CHANNEL_MAP);
        $("#selectProductChannel").html(selectProductChannelHtml);
    },

    /**
     * 查询待审核订单信息
     */
    queryOrderInfo: function () {
        var uri = TmsCounterConfig.QUERY_CHECK_ORDER_URL || {};

        var searchForm = $("#searchCheckForm").serializeObject();
        var reqparamters = {};
        reqparamters.page = 1;
        reqparamters.pageSize = 50;

        for (name in searchForm) {
            if (!CommonUtil.isEmpty(searchForm[name])) {
                reqparamters[name] = searchForm[name];
            }
        }

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxPaging(uri, paramters, QueryHighCounterCheck.queryOrderInfoCallBack, "pageView");
    },

    queryOrderInfoCallBack: function (data) {
        var bodyData = data;
        QueryHighCounterCheck.checkOrders = bodyData.counterOrderList || [];
        $("#rsList").empty();
        if (QueryHighCounterCheck.checkOrders.length <= 0) {
            var trHtml = '<tr class="text-c" ><td colspan="9">暂无待审核记录</td></tr>';
            $("#rsList").append(trHtml);
        }

        $(QueryHighCounterCheck.checkOrders).each(function (index, element) {
            var trList = [];
            var checkBtn = '<a id="recheck' + element.dealAppNo + '" class="reCheckBtn btn btn-success radius" href="javascript:void(0);" indexvalue = ' + index + '>复核</a>';
            var viewBtn = '<a id="reCheckView' + element.dealAppNo + '" class="reCheckView btn btn-secondary radius ml5" href="javascript:void(0);" indexvalue = ' + index + '>查看</a>';
            var btn = '';
            if (!QueryHighCounterCheck.isChecked(element.checkFlag)) {
                btn = checkBtn;
            }
            btn += viewBtn;
            trList.push(btn);
            trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
            trList.push(CommonUtil.formatData(element.custName));
            trList.push(CommonUtil.formatData(element.idNo));
            trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP, element.txCode, ''));
            trList.push(CommonUtil.formatData(element.fundCode));
            trList.push(CommonUtil.formatData(element.fundName));
            trList.push(CommonUtil.formatAmount(element.appAmt, '--'));
            trList.push(CommonUtil.formatAmount(element.appVol, '--'));
            // 转让价格
            trList.push(CommonUtil.formatAmount(element.transferPrice, '--'));
            trList.push(CommonUtil.formatData(element.dealAppNo));
            trList.push(CommonUtil.formatData(element.subsAmt, '--'));// 过户份额对应的认缴金额
            trList.push(CommonUtil.formatData(element.totalSubsAmt));// 过户的总认缴金额
            trList.push(CommonUtil.formatData(element.appDt) + " " + CommonUtil.formatData(element.appTm));
            trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP, element.checkFlag));
            trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
            trList.push(CommonUtil.formatData(element.creator));
            trList.push(CommonUtil.formatData(element.checker));

            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            $("#rsList").append(trHtml);
            $("#recheck" + element.dealAppNo).attr("dealAppNo", element.dealAppNo);
            $("#recheck" + element.dealAppNo).attr("txCode", element.txCode);
            $("#reCheckView" + element.dealAppNo).attr("dealAppNo", element.dealAppNo);
            $("#reCheckView" + element.dealAppNo).attr("txCode", element.txCode);

            // 资金修改回款方向的查看页面，需要传递审核属性，因为审核通过的页面需要单独设置
            if (element.txCode === 'Z900063') {
                $("#reCheckView" + element.dealAppNo).attr("checkFlag", element.checkFlag);
            }

        });

        //viewType 0-查看；1-审核；2-修改
        //审核
        $(".reCheckBtn").off();
        $(".reCheckBtn").on('click', function () {

            var dealAppNo = $(this).attr("dealAppNo");
            var txCode = $(this).attr("txCode");

            var params = [];
            params.push('dealAppNo=' + dealAppNo);
            params.push('viewType=1');
            var urlParams = ViewDealCommon.buildParams(params);

            var viewUrl = ViewDealCommon.getGetViewUrl(txCode, urlParams);
            ViewDealCommon.showDeal(viewUrl);


        });

        //查看
        $(".reCheckView").off();
        $(".reCheckView").on('click', function () {
            var dealAppNo = $(this).attr("dealAppNo");
            var txCode = $(this).attr("txCode");

            var params = [];
            params.push('dealAppNo=' + dealAppNo);
            params.push('viewType=0');

            // 资金修改回款方向的查看页面，需要传递审核属性，因为审核通过的页面需要单独设置
            if (txCode === 'Z900063') {
                var checkFlag = $(this).attr("checkFlag");
                params.push('checkFlag=' + checkFlag);
            }

            var urlParams = ViewDealCommon.buildParams(params);

            var viewUrl = ViewDealCommon.getGetViewUrl(txCode, urlParams);
            console.log("viewUrl:" + viewUrl);
            ViewDealCommon.showDeal(viewUrl);

        });

    },

    /**
     * 是否已审核
     * @param checkFlag
     *  0-待审核
     *  1-审核通过
     *  2-审核失败
     *  3-审核驳回
     *  4-作废
     */
    isChecked: function (checkFlag) {
        if ('0' === checkFlag) {
            return false;
        }
        return true;
    }
};
