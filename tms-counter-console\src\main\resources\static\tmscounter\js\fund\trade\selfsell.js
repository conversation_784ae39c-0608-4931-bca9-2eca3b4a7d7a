/**
 *赎回
 *<AUTHOR>
 *@date 2017-04-01 15:12
 */
$(function () {
    SellFund.isSelfFlag = true;
    Init.init();
    SellFund.init();
    CommonUtil.getWorkDay();
    SellFund.redeemProductDtos = {};
    SellFund.custBalanceDtos = {};
    SellFund.endTm = "";
    SellFund.canRedeemFlag = "1";
    SellFund.canRedeemMemo = "";
    SellFund.confirmModifyDetailFlag = true;
});

var SellFund = {
    init: function () {
        // 初始录入订单信息
        SellFund.initRedeemOrderInfoTable(SellFund.isSelfFlag);

        $("#confimSellBtn").on('click', function () {
            if (SellFund.validateFund()){
                SellFund.confirm();
            }
        });

        $("#modifyRedeemSellBtn").on('click', function () {
            SellFund.queryTrialSellRedeemLimitList();
        });

        $("#trialSellBtn").on('click', function () {
            SellFund.trialSellRedeemList();
        });

        // 查询客户信息
        $("#queryCustInfoBtn").on('click', function () {
            QueryCustInfo.queryCustInfo();
        });

        $("#custNo").on('dblclick', function () {
            QueryCustInfoSubPage.selectCustNo($(this));
        });
    },

    /***
     * 确认赎回
     */
    confirm: function (dealAppNo) {

        CommonUtil.disabledBtn("confimSellBtn");

        var sellProtocolNoForm = $("#sellConfirmForm").serializeObject();

        var customOrRatio = $("#customOrRatio").val();
        if(customOrRatio == '1'){
            if(SellFund.canRedeemFlag != '1'){
                CommonUtil.enabledBtn("confimSellBtn");
                CommonUtil.layer_tip("试算不可赎回，原因" + SellFund.canRedeemMemo);
                return;
            }
        }

        if (SellFund.redeemProductDtos == null || SellFund.redeemProductDtos.length <= 0) {
            CommonUtil.enabledBtn("confimSellBtn");
            CommonUtil.layer_tip("没有可赎回的持仓");
            return;
        }
        var redeemFundInfoList = [];
        $(SellFund.redeemProductDtos).each(function (index, element) {
            var holdInfo = {};
            var selectFundCode = $('#fundCode_' + index).val();
            var selectRedeemVol = $('#redeemVol_' + index).val();
            holdInfo.fundCode = selectFundCode;
            holdInfo.appVol = selectRedeemVol;
            redeemFundInfoList.push(holdInfo);
        });

        // 校验其他录入信息
        var transactorInfoForm = $("#transactorInfoForm").serializeObject();
        transactorInfoForm.appDtm = transactorInfoForm.appDt + '' + transactorInfoForm.appTm;
        if (CommonUtil.isEmpty(transactorInfoForm.appTm)) {
            CommonUtil.layer_tip("请输入赎回下单时间");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }
        if (!Valid.valiadTradeTime(transactorInfoForm.appTm)) {
            CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }

        if (!Validate.validateTransactorInfo(transactorInfoForm, QueryCustInfo.custInfo)) {
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }

        var dealAppNo = "";
        if (!(typeof ApplySell == "undefined")) {
            dealAppNo = ApplySell.checkOrder.dealAppNo;
        }

        layer.confirm('确定提交吗？', {
            btn: ['确定', '取消']
        }, function (index) {
            if (SellFund.isSelfFlag) {
                layerall_close();
                SellFund.confirmSelfSubmit(dealAppNo, sellProtocolNoForm, redeemFundInfoList, transactorInfoForm);
            }
        }, function () {
            CommonUtil.enabledBtn("confimSellBtn");
            layerall_close();
        });
    },

    /**
     * 确认提交
     * @param dealAppNo
     * @param sellRedeemFunds
     * @param sellRedeemFunds
     * @param transactorInfoForm
     */
    confirmSubmit: function (dealAppNo, sellRedeemFunds, transactorInfoForm) {
        var uri = TmsCounterConfig.SELL_SELF_CONFIRM_URL || {};
        var reqparamters = {
            "dealAppNo": dealAppNo,
            "sellRedeemFunds": JSON.stringify(sellRedeemFunds),
            "custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
            "transactorInfoForm": JSON.stringify(transactorInfoForm)
        };
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.confirmSubmitCallBack);
    },

    confirmSubmitCallBack: function (data) {
        var retMsg = "";
        if (data == null) {
            retMsg = "提交失败。";

        } else {
            if (!CommonUtil.isSucc(data.code) && !CommonUtil.isArray(data)) {
                retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
            }
            if (CommonUtil.isArray(data) && data.length > 0) {
                retMsg = "";
                $(data).each(function (index, element) {
                    //console.log(element);
                    var respDesc = element.desc || '';
                    var bodyData = element.body || {};
                    var requestDto = bodyData.requestDto || {};
                    retMsg += "基金: " + requestDto.fundCode + ", 申请赎回份额: " + requestDto.appVol + "份, 赎回提交" + respDesc + "。<br>";
                });
            }
        }

        layer.open({
            type: 1,
            skin: 'layui-layer-rim', //加上边框
            area: ['400px', '150px'], //宽高
            content: '<div style="text-align: center;margin: 10px 10px;">' + retMsg + '</div>'
        });

        if ($(".confimBtn").length > 0) {
            CommonUtil.disabledBtnWithClass("confimBtn");
            CommonUtil.disabledBtn("abolishBtn");
        }
    },

    /**
     * 确认提交
     * @param dealAppNo
     * @param sellRedeemFunds
     * @param transactorInfoForm
     */
    confirmSelfSubmit: function (dealAppNo, sellProtocolNoForm, sellRedeemForm, transactorInfoForm) {
        var uri = TmsCounterConfig.SELL_SELF_CONFIRM_URL || {};
        var reqparamters = {
            "dealAppNo": dealAppNo,
            "sellProtocolNoForm": JSON.stringify(sellProtocolNoForm),
            "sellRedeemForm": JSON.stringify(sellRedeemForm),
            "custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
            "transactorInfoForm": JSON.stringify(transactorInfoForm)
        };
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.confirmSelfCallBack);
    },

    confirmSelfCallBack: function (data) {
        var retMsg = "";
        if (data == null) {
            retMsg = "提交失败。";
        } else {
            if (!CommonUtil.isSucc(data.code)) {
                retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
            }else {
                var dealAppNo = data.body.responseDto.dealAppNo;
                if (CommonUtil.isEmpty(dealAppNo)) {
                    retMsg = "提交失败";
                } else {
                    retMsg = "提交成功";
                }
            }
        }
        CommonUtil.enabledBtn("confimSellBtn");
        CommonUtil.layer_tip(retMsg, 3000);

        if ($(".confimBtn").length > 0) {
            CommonUtil.disabledBtnWithClass("confimBtn");
            CommonUtil.disabledBtn("abolishBtn");
        }

    },

    queryTrialSellRedeemBeforeLimitList: function (){
        var confirmlFlag = SellFund.confirmModifyDetailFlag;
        console.log("SellFund.confirmModifyDetailFlag:" + confirmlFlag);
        if(confirmlFlag){
            $("#customOrRatio option[value='2']").prop("selected", true);
            if (SellFund.redeemProductDtos == null || SellFund.redeemProductDtos.length <= 0) {
                CommonUtil.layer_tip("没有可赎回的持仓");
            }
            $(SellFund.redeemProductDtos).each(function (index, element) {
                $('#redeemVol_' + index).attr("readonly",false);
            });
            $('#modifyRedeemSellBtn').html("确认修改后的份额明细");
            SellFund.confirmModifyDetailFlag = false;
        }
        console.log("SellFund.confirmModifyDetailFlag:" + SellFund.confirmModifyDetailFlag);
    },

    queryTrialSellRedeemAfterLimitList: function (){
        var confirmlFlag = SellFund.confirmModifyDetailFlag;
        console.log("SellFund.confirmModifyDetailFlag:" + confirmlFlag);
        if(!confirmlFlag){
            if (SellFund.redeemProductDtos == null || SellFund.redeemProductDtos.length <= 0) {
                CommonUtil.layer_tip("没有可赎回的持仓");
            }
            $(SellFund.redeemProductDtos).each(function (index, element) {
                $('#redeemVol_' + index).attr("readonly",true);
            });
            $('#modifyRedeemSellBtn').html("我要修改赎回明细份额");
            SellFund.confirmModifyDetailFlag = true;
        }
        console.log("SellFund.confirmModifyDetailFlag:" + SellFund.confirmModifyDetailFlag);
    },

    queryTrialSellRedeemLimitList: function (){
        var custNo = QueryCustInfo.custInfo.custNo || '';
        var disCode = QueryCustInfo.custInfo.disCode || '';
        var protocolNo  = $("#protocolNo").val();
        var cpAcctNo = $("#selectBank").val();
        var customOrRatio = $("#customOrRatio").val();
        var productCode  = $("#productCode").val();

        var uri;
        if (SellFund.isSelfFlag) {
            uri = TmsCounterConfig.QUERY_SELF_REDEEM_LIMIT_INFO_URL || {};
        }
        if (SellFund.redeemProductDtos == null || SellFund.redeemProductDtos.length <= 0) {
            CommonUtil.layer_tip("没有可赎回的持仓");
        }
        var redeemFundInfoList = [];
        $(SellFund.redeemProductDtos).each(function (index, element) {
            var holdInfo = {};
            var selectFundCode = $('#fundCode_' + index).val();
            var selectRedeemVol = $('#redeemVol_' + index).val();
            holdInfo.fundCode = selectFundCode;
            holdInfo.appVol = selectRedeemVol;
            redeemFundInfoList.push(holdInfo);
        });

        var reqparamters = {"productCode": productCode, "protocolNo": protocolNo, "cpAcctNo": cpAcctNo, "custNo": custNo, "disCode": disCode, "customOrRatio": customOrRatio, "redeemFundInfoList": JSON.stringify(redeemFundInfoList)};
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.queryTrialSellRedeemLimitCallBack);
    },

    queryTrialSellRedeemLimitCallBack: function (data){
        var bodyData = data.body || {};

        //默认可赎，如果存在不可赎，则修改为false
        var allRedeemFlag = true;
        $(SellFund.redeemProductDtos).each(function (index, element) {
            var selectFundCode = $('#fundCode_' + index).val();
            var selectRedeemVol = $('#redeemVol_' + index).val();
            var redeemFlag = SellFund.checkTrialSellRedeemLimitCallBack(selectFundCode, selectRedeemVol, data);
            if(!redeemFlag){
                allRedeemFlag = false;
            }
            SellFund.checkTrialSellRedeemRatio(selectFundCode, selectRedeemVol, data);
        });

        //初始化修改 修改成可以变更明细
        if(SellFund.confirmModifyDetailFlag){
            SellFund.queryTrialSellRedeemBeforeLimitList();
        } else {
            //只有可赎，关闭修改
            if(allRedeemFlag){
                SellFund.queryTrialSellRedeemAfterLimitList();
            }
        }

    },

    /**
     * 仅仅提示 返回是否可赎回
     * @param fundCode
     * @param redeemVol
     * @param data
     * @returns {boolean}
     */
    checkTrialSellRedeemLimitCallBack: function (fundCode, redeemVol, data){
        var bodyData = data.body || {};
        var customFundInfoResDtos = bodyData.customFundInfoResDtos;
        var redeemFlag = true;
        $(customFundInfoResDtos).each(function (index, element) {
            if(fundCode == element.fundCode){
                if(redeemVol == '0'){
                    return;
                }
                var minAcctVol = element.minAcctVol;
                var limitMax = element.limitMax;
                var limitMin = element.limitMin;

                var max = limitMax - minAcctVol;
                var min = limitMin;
                var allBalanceVol = element.availableVol;

                if(redeemVol == allBalanceVol){
                    return;
                }
                if(redeemVol >= min &&  redeemVol <=  max){
                    return;
                }
                if(allBalanceVol >= min && max >= allBalanceVol){
                    CommonUtil.layer_tip_four("基金" + fundCode + "限额大于" + min + "并且小于" + max);
                    redeemFlag = false;
                    return;
                } else {
                    CommonUtil.layer_tip_four("基金" + fundCode + "限额大于" + min + "并且小于" + max + "或者等于" + allBalanceVol);
                    redeemFlag = false;
                }
            }
        });
        return redeemFlag;
    },


    checkTrialSellRedeemRatio: function (fundCode, redeemVol, data){
        var bodyData = data.body || {};
        var totalSellRatio = bodyData.totalSellRatio;
        var totalSellRatioTwo = CommonUtil.formatPercentNo(totalSellRatio, totalSellRatio, 2);
        $("#redeemRatio").val(totalSellRatioTwo);
        var customFundInfoResDtos = bodyData.customFundInfoResDtos;
        $(customFundInfoResDtos).each(function (index, element) {
            if(fundCode == element.fundCode){
                var sellRatio = element.sellRatio;
                var sellRatioTwo= CommonUtil.formatPercent(sellRatio, sellRatio, 2);
                $('#sellRatio_' + index).val(sellRatioTwo);
            }
        });
    },

    /**
     * 零售查询客户持仓
     */
    queryCustHodlInfo: function (productCode, protocolNo, cpAcctNo, appRatio) {
        var custNo = QueryCustInfo.custInfo.custNo || '';
        var disCode = QueryCustInfo.custInfo.disCode || '';
        console.log("select custNo:" + custNo + ", disCode:" + disCode + ",productCode: " + productCode + ",protocolNo:" + protocolNo + ",cpAcctNo:" + cpAcctNo + ",appRatio:" + appRatio + ",isSelfFlag:" + SellFund.isSelfFlag);
        //初始化默认为100%
        if (CommonUtil.isEmpty(appRatio)) {
            appRatio = "100";
            $("#redeemRatio").val(appRatio);
        }

        var uri;
        if (SellFund.isSelfFlag) {
            uri = TmsCounterConfig.QUERY_SELF_REDEEM_INFO_URL || {};
        }
        var reqparamters = {"productCode": productCode, "protocolNo": protocolNo, "cpAcctNo": cpAcctNo, "appRatio": appRatio, "custNo": custNo, "disCode": disCode};
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.queryCustHoldFundInfoCallBack);
    },

    /**
     * 处理基金持仓信息
     */
    queryCustHoldFundInfoCallBack: function (data) {
        console.log("SellFund.queryCustHoldFundInfoCallBack-------" + JSON.stringify(data));
        var bodyData = data.body || {};

        SellFund.canRedeemFlag = bodyData.canRedeem;
        SellFund.canRedeemMemo = bodyData.redeemReason;

        SellFund.redeemProductDtos = bodyData.redeemProductResDtos || [];
        SellFund.custBalanceDtos = bodyData.custBalanceResDtos || [];
        var custProtocolOpenResDtos = bodyData.custProtocolOpenResDtos || [];
        var openFlag = "1";
        if (custProtocolOpenResDtos == null || custProtocolOpenResDtos.length <= 0) {
            openFlag = "1";
        } else {
            $(custProtocolOpenResDtos).each(function (index, element) {
                var txCode = element.txCode;
                if(txCode == "Z310002"){
                    openFlag = element.openFlag;
                }
            });
        }
        $("#openFlag").val(openFlag);

        SellFund.showSelfBankTable(bodyData);
        if (SellFund.redeemProductDtos == null || SellFund.redeemProductDtos.length <= 0) {
            CommonUtil.layer_tip("没有可赎回的持仓");
        } else {
            // 组装客户购买基金持仓列表
            var redeemFundInfoList = [];
            $(SellFund.redeemProductDtos).each(function (index, element) {
                var holdInfo = {};
                holdInfo.fundCode = element.fundCode;
                holdInfo.fundName = element.fundName;
                holdInfo.fundStat = element.fundStat;
                holdInfo.fundCanRedeem = element.fundCanRedeem;
                holdInfo.fundReason = element.fundReason;
                holdInfo.redeemVol = element.redeemVol;
                holdInfo.redeemAmt = element.redeemAmt;
                holdInfo.sellRatio = element.sellRatio;
                holdInfo.holdRatio = element.holdRatio;
                redeemFundInfoList.push(holdInfo);
            });

            if (SellFund.isSelfFlag) {
                var trAppendTableHtml =
                    '<tr className="text-c">' +
                    '<th>基金代码</th>'+
                    '<th>基金名称</th>'+
                    '<th>赎回比例</th>'+
                    '<th>是否可赎回</th>'+
                    '<th>不可赎回原因</th>'+
                    '<th>基金状态</th>'+
                    '<th>自定义赎回份额</th>'+
                    ' </tr>' ;
                $("#sellRedeemInfoId").empty();
                $("#sellRedeemInfoId").append(trAppendTableHtml);

                // 重新渲染列表
                $(redeemFundInfoList).each(function (index, element) {
                    if (SellFund.isSelfFlag) {
                        SellFund.showSelfRedeemTable(index, element, bodyData);
                    }
                });
            }

        }
    },

    showSelfBankTable: function (bodyData){
        SellFund.custBanks = bodyData.bankAcctSensitiveResModels || [];

        if (SellFund.custBanks == null || SellFund.custBanks.length <= 0) {
            return;
        }

        var selectBankHtml = '';
        $(SellFund.custBanks).each(
            function(index, element) {
                selectBankHtml += '<option bankacct= "'+ element.bankAcct + '" bankcode= "'+ element.bankCode + '" value="' + element.cpAcctNo + '">'+ CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP,element.bankCode) + ''+ element.bankAcct + ' </option>';
            });
        $("#selectBank").empty();
        $("#selectBank").html(selectBankHtml);

        $("#selectBank").change(function(){
            if($("#selectBank").find("option:selected")[0]){
                var indexNum = $("#selectBank").find("option:selected")[0].index;
                var custBank = SellFund.custBanks[indexNum] || {} ;
                if($("#bankCode").length > 0){
                    $("#bankCode").val(custBank.bankCode);
                }
            }
        });
        $("#selectBank").change();
    },

    showSelfRedeemTable:function (index, element, bodyData) {
        var canRedeem = bodyData.canRedeem||'0';
        var reason = bodyData.reason ||'';

        var fundCode = element.fundCode;
        var redeemVol = element.redeemVol;
        var sellRatio = element.sellRatio;
        var tdList = [];
        tdList.push('<td><input id="fundCode_' + index + '" name="fundCode_' + index + '" type="text" value="' + fundCode + '" readonly="true"></td>');
        tdList.push('<td>' + element.fundName + '</td>');

        tdList.push('<td><input id="sellRatio_' + index + '" name="sellRatio_' + index + '" type="text" value="' + sellRatio + '%" readonly="true"></td>');
        tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_CAN_REDEEM_MAP, element.fundCanRedeem) + '</td>');
        tdList.push('<td>' + element.fundReason + '</td>');
        tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_STATE, element.fundStat) + '</td>');
        tdList.push('<td><input type="text" class="redeemRatioClass" name="redeemVol_' + index + '" id="redeemVol_' + index + '" isnull="false" datatype="s" value="' + redeemVol + '" errormsg="赎回份额" placeholder="请输入" readonly="true" onkeyup="SellFund.validatorRedeemVol(' + index + ');"></td>');

        var trAppendHtml = '<tr class="text-c" id="redeemInfo_tr_' + index + '">' + tdList.join() + '</tr>';
        $("#sellRedeemInfoId").append(trAppendHtml);
    },

    /**
     * 校验输入份额(事件:onkeyup)
     */
    validatorAppVol: function (index, availVol, thisObj) {
        // console.log(availVol + " "+ thisObj.value);
        var appVol = thisObj.value;
        if (!/^[0-9]+\.?[0-9]{0,2}$/.test(appVol)) {
            CommonUtil.layer_tip('只能输入数字且小数点后两位');
            thisObj.value = '';
        }

        var cnAppVol = CommonUtil.digit_uppercase(appVol);
        $("#appVolCapitalForSell_" + index).val(cnAppVol.replace('元', '份'));

        if (appVol > availVol) {
            CommonUtil.layer_tip("申请份额不能大于可用份额");
            $(thisObj).css("border-color", "red");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        } else {
            $(thisObj).css("border-color", "");
        }
    },

    /**
     * 校验赎回份额值
     */
    validatorRedeemVol: function (index) {
        var formIndex = index;
        var fundCode = $("#fundCode_" + index).val();
        var redeemVol = $("#redeemVol_" + index).val();
        if (!/^[0-9]+\.?[0-9]{0,2}$/.test(redeemVol)) {
            redeemVol = SellFund.getCalculationRedeemVol(fundCode);
            $("#redeemVol_" + formIndex).val(redeemVol);
            CommonUtil.layer_tip('只能输入数字且小数点后两位');
            return;
        }

        if (SellFund.custBalanceDtos == null || SellFund.custBalanceDtos.length <= 0) {
            return;
        }
        $(SellFund.custBalanceDtos).each(function (index, element) {
            if(fundCode == element.fundCode){
                if (redeemVol < 0) {
                    redeemVol = SellFund.getCalculationRedeemVol(fundCode);
                    $("#redeemVol_" + formIndex).val(redeemVol);
                    CommonUtil.layer_tip('请输入大于0的份额');
                    return;
                }
                if (redeemVol > element.availVol) {
                    redeemVol = SellFund.getCalculationRedeemVol(fundCode);
                    $("#redeemVol_" + formIndex).val(redeemVol);
                    CommonUtil.layer_tip('输入的值超过可用份额');
                    return;
                }
            }
        })
    },


    getCalculationRedeemVol: function (fundCode) {
        var redeemVol = '0';
        if (SellFund.redeemProductDtos == null || SellFund.redeemProductDtos.length <= 0) {
            return redeemVol;
        }
        $(SellFund.redeemProductDtos).each(function (index, element) {
            if(fundCode == element.fundCode){
                redeemVol = element.redeemVol;
            }
        })
        return redeemVol;
    },

    /**
     * 选择了全赎触发
     */
    selectedAllRedeem: function (index, availVol, thisObj) {
        var checkFlag = $(thisObj).is(':checked');
        if (checkFlag) {
            $("#sellConfirmForm #appVol_" + index).val(availVol);
            $("#sellConfirmForm #appVol_" + index).attr('readonly', true);
            var cnAppVol = CommonUtil.digit_uppercase(availVol);
            $("#sellConfirmForm #appVolCapitalForSell_" + index).val(cnAppVol.replace('元', '份'));
        } else {
            $("#sellConfirmForm #appVol_" + index).val('');
            $("#sellConfirmForm #appVol_" + index).removeAttr('readonly');
            $("#sellConfirmForm #appVolCapitalForSell_" + index).val('');

        }
    },

    initSearchIcon: function () {
        // 绑定icon点击事件
        $(".searchIcon").on('click', function () {
            SellFund.trialSellRedeemList();
        });
    },

    trialSellRedeemList: function () {
        SellFund.confirmModifyDetailFlag = true;
        $('#modifyRedeemSellBtn').html("我要修改赎回明细份额");

        var custNo = QueryCustInfo.custInfo.custNo || '';
        if (isEmpty(custNo)) {
            CommonUtil.layer_tip("请先选择用户");
            return false;
        }
        var searchProductCode = $("#searchProductCode").val();
        if (isEmpty(searchProductCode)) {
            CommonUtil.layer_tip("请先根据产品代码/协议号查询");
            return false;
        }

        var appRatio = $("#redeemRatio").val();
        if (!isEmpty(appRatio)) {
            if (!(appRatio <= 100 && appRatio >= 0)) {
                CommonUtil.layer_tip('赎回比例请输入赎回比例1~100');
                return false;
            }
            if (!/^[0-9]+$/.test(appRatio)) {
                CommonUtil.layer_tip('赎回比例请输入整数');
                return false;
            }
        }

        QueryFundInfo.querySelfFundProduct(searchProductCode,function (productInfo){
            var isSelfFlag = productInfo.selfFlag || false;
            SellFund.isSelfFlag = isSelfFlag;
            console.info("====>" + searchProductCode + " isSelfFlag " + SellFund.isSelfFlag);

            var cpAcctNo = null;
            var protocolNo  = $("#protocolNo").val();
            if (!isEmpty(protocolNo) && !(protocolNo == '--')) {
                cpAcctNo = $("#selectBank").val();
            }

            $("#productName").html(productInfo.productName);
            $("#protocolNo").val(productInfo.protocolNo);
            $("#productCode").val(productInfo.productCode);
            $("#protocolType").html(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, productInfo.protocolType));

            SellFund.queryCustHodlInfo(productInfo.productCode, productInfo.protocolNo, cpAcctNo, appRatio);
        });
    },

    /**
     * 初始化赎回订单信息Table
     */
    initRedeemOrderInfoTable: function (isSelfFlag) {
        if (isSelfFlag) {
            SellFund.initRedeemSelfTable();
            SellFund.initSearchIcon();
        }
    },

    initRedeemSelfTable: function () {
        var trAppendTableHtml =
            '<tr className="text-c">' +
            '<th>产品代码/协议号</th>'+
            '<th>银行账户</th>'+
            '<th style="weight:250px">赎回方式(比例/自定义)</th>'+
            '<th>赎回比例</th>'+
            '<th>赎回平衡开关</th>'+
            '<th>产品名称</th>'+
            '<th>协议号</th>'+
            '<th>产品代码</th>'+
            '<th>协议类型</th>'+
            '<th>巨额赎回顺延标记</th>'+
            '<th>赎回不出款标记</th>'+
            '<th>交易回款方向</th>'+
            ' </tr>' ;

        $("#redeemOrderTableInfo").empty();
        $("#redeemOrderTableInfo").append(trAppendTableHtml);

        var tdList = [];
        tdList.push('<td><div class="searchIn"><input id="searchProductCode" type="text" ><a href="javascript:void(0)" class="searchIcon"></a></div></td>');
        tdList.push('<td><span class="select-box inline"><select name="cpAcctNo" class="select" id="selectBank"></select></span><input type="hidden" id="bankCode" name="bankCode" value="0"></td>');
        tdList.push('<td><span class="select-box inline"><select name="customOrRatio" class="select" id="customOrRatio"><option value="1">按比例赎回</option><option value="2">自定义赎回</option></select></span></td>');
        tdList.push('<td style="weight:250px"><input type="text" placeholder="请输入" style="weight:100px" name="redeemRatio" id="redeemRatio" isnull="false" datatype="s" errormsg="赎回比例"/>%  <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="trialSellBtn">试算</a></td>');
        // 赎回平衡开关
        tdList.push('<td><span class="select-box inline"><select name="openFlag" id="openFlag" class="select" isnull="false" datatype="s" errormsg="赎回平衡开关"><option value="1" selected="selected">开</option><option value="0">关</option></select></span></td>');
        tdList.push('<td id="productName">--</td>');
        tdList.push('<td><input id="protocolNo" name="protocolNo" type="text" value="" readonly="true"></td>');
        tdList.push('<td><input id="productCode" name="productCode" type="text" value="" readonly="true"></td>');
        tdList.push('<td id="protocolType">--</td>');
        // 巨额赎回顺延标记
        tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option></select></span></td>');
        // 赎回不出款标记
        tdList.push('<td><span class="select-box inline"><select name="unusualTransType" class="select" isnull="false" datatype="s" errormsg="异常交易标识"><option value="0" selected="selected">否</option><option value="1">是</option></select></span></td>');
        // 交易回款方式
        var redeemCapitalOptionsHtml = Redemption.buildSelectRedeemHtml(QueryCustInfo.custInfo.collectProtocolMethod, QueryCustInfo.custInfo.invstType, null, null);
        tdList.push('<td><span class="select-box inline"><select    name="redeemCapitalFlag" id="redeemCapitalFlag" class="select" isnull="false" datatype="s" errormsg="交易回款方式">' + redeemCapitalOptionsHtml + '</select></span>');
        var trAppendHtml = '<tr class="text-c">' + tdList.join() + '</tr>';
        $("#redeemOrderInfoId").empty();
        $("#redeemOrderInfoId").append(trAppendHtml);
    },

    validateFund:function(){
        var appTm = $("#appTm").val();
        if(appTm === undefined || appTm.length === 0){
            CommonUtil.layer_tip("下单时间不能为空");
            CommonUtil.enabledBtn("confimSellBtn");
            return false
        }
        return true;
    },

};

