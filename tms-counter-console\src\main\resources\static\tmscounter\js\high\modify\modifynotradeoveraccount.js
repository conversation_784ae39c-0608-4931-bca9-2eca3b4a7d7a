/**
* 非交易过户审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	NoTradeOverAccountModify.init();

});

var NoTradeOverAccountModify = {

	/**
	 * 初始化下拉框
	 */
	init:function(){

		/**
		 * 初始化参数
		 */
		NoTradeOverAccountModify.initData();

		/**
		 * 初始化按钮事件
		 */
		NoTradeOverAccountModify.initBtn();

		// 过户份额格式化
		$("#appVol").on("blur", function(){
			// 还原格式化金额
			var appVol = CommonUtil.unFormatAmount($(this).val());
			// 格式化
			$("#appVol").val(CommonUtil.formatAmount(appVol));
			// 大写
			var upperAppVol = CommonUtil.digit_vol_uppercase(appVol);
			$("#appVolUpper").html(upperAppVol);
			// CounterCheck.counterOrderDto.appVol=appVol;
		});
		// 过户份额对应的认缴金额格式化
		$("#subsAmt").on("blur", function(){
			// 还原格式化金额
			var subsAmt = CommonUtil.unFormatAmount($(this).val());
			// 格式化
			$("#subsAmt").val(CommonUtil.formatAmount(subsAmt));
			// 大写
			var subsAmtUpper = CommonUtil.digit_uppercase(subsAmt);
			$("#subsAmtUpper").html(subsAmtUpper);
			// CounterCheck.counterOrderDto.subsAmt=subsAmt;
		});
		// 转让价格格式化
		$("#transferPrice").on("blur", function(){
			// 还原格式化金额
			var transferPrice = CommonUtil.unFormatAmount($(this).val());
			// 格式化
			$("#transferPrice").val(CommonUtil.formatAmount(transferPrice));
			// 大写
			var transferPriceUpper = CommonUtil.digit_uppercase(transferPrice);
			$("#transferPriceUpper").html(transferPriceUpper);
		});

		// 过户的总认缴金额格式化
		$("#totalSubsAmt").on("blur", function () {
			var totalSubsAmt = CommonUtil.unFormatAmount($(this).val());
			// 格式化
			$("#totalSubsAmt").val(totalSubsAmt);
			// 大写
			if(totalSubsAmt){
				var totalSubsAmtUpper = CommonUtil.digit_uppercase(totalSubsAmt);
				$("#totalSubsAmtUpper").html(totalSubsAmtUpper);
			}

		});
		//viewType 0-查看；1-审核；2-修改
		var viewType = CommonUtil.getParam("viewType");
		NoTradeOverAccountModify.viewType = viewType;
		// 查询订单
		Modify.queryCounterDealOrder(NoTradeOverAccountModify.queryCounterDealOrderCallBack, null);

	},

	/**
	 * 初始化按钮事件
	 */
	initBtn:function(){


	},

	/**
	 * 初始化数据
	 */
	initData:function(){
		NoTradeOverAccountModify.counterOrderDto = {};//订单信息
		NoTradeOverAccountModify.checkOrder = {};
		NoTradeOverAccountModify.counterOrder= {};
		NoTradeOverAccountModify.viewType= {};
	},
	queryCounterDealOrderCallBack:function(data){
		console.log(JSON.stringify(data));
		var bodyData = data.body || {};
		NoTradeOverAccountModify.counterOrderDto = bodyData.counterOrderDto || {};
		var checkOrder = NoTradeOverAccountModify.counterOrderDto || {};//订单信息
		NoTradeOverAccountModify.checkOrder = checkOrder;//柜台订单信息
		Modify.modifyDealOrder  = checkOrder;//柜台订单信息
		NoTradeOverAccountModify.counterOrder = bodyData.counterOrder;//柜台订单信息

		NoTradeOverAccountModify.buildNTOADealInfo(checkOrder);//购买订单信息

		// 查询持仓信息
		NoTradeOverAccountModify.queryBalDtl(checkOrder.txAcctNo, checkOrder.disCode);
	},


	/**
	 * 非交易过户订单信息
	 * @param checkOrder
	 */
	buildNTOADealInfo:function(checkOrder){
		$("#outCustNo").val(checkOrder.txAcctNo);
		$("#outIdNo").val(checkOrder.idNo);
		$("#outIdTypeName").val(NoTradeOverAccountModify.getIdTypeName(checkOrder.idType, checkOrder.invstType));//客户证件类型
		$("#outIdType").val(checkOrder.idType);//客户证件类型
		$("#outCustName").val(checkOrder.custName);
		$("#outBankAcctNo").val(checkOrder.bankAcct);//银行卡号
		$("#outCpAcctNo").val(checkOrder.cpAcctNo);//资金账号

		$("#inCustNo").val(checkOrder.inTxAcctNo);
		$("#inIdNo").val(checkOrder.inIdNo);
		$("#inIdTypeName").val(NoTradeOverAccountModify.getIdTypeName(checkOrder.inIdType, checkOrder.inInvstType));//客户证件类型
		$("#inIdType").val(checkOrder.inIdType);//客户证件类型
		$("#inCustName").val(checkOrder.inCustName);
		$("#inBankAcctNo").val(checkOrder.inBankAcct);//银行卡号
		$("#inCpAcctNo").val(checkOrder.inCpAcctNo);//资金账号

		$("#fundCode").val(checkOrder.fundCode);//产品代码
		$("#appVol").attr("disabled",false);
		$("#appVol").val(CommonUtil.formatAmount(checkOrder.appVol));//申请份额
		$("#appVolUpper").html(CommonUtil.digit_vol_uppercase(checkOrder.appVol));//申请份额大写
		$("#subsAmt").attr("disabled",false);
		$("#subsAmt").val(CommonUtil.formatAmount(checkOrder.subsAmt));//认缴金额
		$("#subsAmtUpper").html(CommonUtil.digit_uppercase(checkOrder.subsAmt));//认缴金额大写
		$("#totalSubsAmt").attr("disabled",false);
		$("#totalSubsAmt").val(CommonUtil.formatAmount(checkOrder.totalSubsAmt));//认缴金额
		$("#totalSubsAmtUpper").html(CommonUtil.digit_uppercase(checkOrder.totalSubsAmt));//认缴金额大写
		$("#transferPrice").attr("disabled",false);
		$("#transferPrice").val(CommonUtil.formatAmount(checkOrder.transferPrice));//转让价格
		$("#transferPriceUpper").html(CommonUtil.digit_uppercase(checkOrder.transferPrice));//转让价格大写

		$("#appDt").val(checkOrder.appDt);//申请日期
		$("#appTm").val(checkOrder.appTm);//申请时间

	},

	/**
	 * 查询客户持仓明细
	 */
	queryBalDtl:function (custNo, disCode) {
		var  uri= TmsCounterConfig.HIGH_QUERY_CUST_BAL_DTL ||  "";

		if(CommonUtil.isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}

		var reqparamters = {"custNo":custNo, "disCode":disCode};

		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, NoTradeOverAccountModify.queryCustDtlCallBack);
	},

	/**
	 * 处理客户份额明细信息
	 */
	queryCustDtlCallBack:function(data){
		var bodyData = data.body || {};
		var dtlResp = bodyData || {};

		var dtlList = dtlResp.dtlList || [];
		NoTradeOverAccountModify.dtlList = dtlList;

		if(NoTradeOverAccountModify.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
			return false;
		}

		var appendHtml = "";
		var viewType = CommonUtil.getParam("viewType");
		$(NoTradeOverAccountModify.dtlList).each(function(index, element){

			var trList = [];
			if(viewType === '2'){
				trList.push('<input class="selectcustbal" name="checkBalDtl" type="radio" index="'+index+'">');
			}
			trList.push(CommonUtil.formatData(element.productCode, '--'));// 产品代码
			trList.push(CommonUtil.formatData(element.productName, '--'));// 产品名称
			trList.push(CommonUtil.formatData(element.bankName, '--'));// 银行名称
			trList.push(CommonUtil.formatData(element.bankAcctNo, '--'));// 银行账号
			trList.push(CommonUtil.formatData(element.balanceVol, '--'));// 总份额
			trList.push(CommonUtil.formatAmount(element.availVol, '--'));// 可用份额
			trList.push(CommonUtil.formatAmount(element.unconfirmedVol, '--'));// 冻结份额

			if(Modify.modifyDealOrder.bankAcct === element.bankAcctNo &&
				Modify.modifyDealOrder.fundCode === element.productCode){
				var trHtml = '<tr class="text-c" bgcolor="#ADD8E6"><td>'+trList.join('</td><td>') +'</td></tr>';
			}else{
				var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			}
			appendHtml +=trHtml;
		});

		$("#dtlList").html(appendHtml);

		//绑定客户份额选择事件
		$(".selectcustbal").off();
		$(".selectcustbal").click(function(){

			$(this).attr('checked','checked').siblings().removeAttr('checked');
			var selectIndex = $(this).attr("index");
			// 选择用户份额
			NoTradeOverAccountModify.selectCustVol(selectIndex);

		});

	},

	/**
	 * 获取证件类型
	 * @param idType
	 * @param invstType
	 * @returns {*}
	 */
	getIdTypeName:function(idType, invstType) {
		if ('0' == invstType) {
			// 机构证件类型
			return CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, idType, '');
		} else if ('1' == invstType) {
			// 个人证件类型
			return CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, idType, '');
		} else {
			// 产品证件类型
			return CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, idType, '');
		}
	},



	/**
	 * 选择用户份额
	 */
	selectCustVol:function(selectIndex){
		// 初始化订单信息
		// CommonUtil.cleanForm('transferConfirmForm', ['appDt','appTm']);
		CommonUtil.initReadeText('readText');

		NoTradeOverAccountModify.transferBal =  NoTradeOverAccountModify.dtlList[selectIndex] || {};

		NoTradeOverAccountModify.buildTransferForm(NoTradeOverAccountModify.transferBal);
	},

	/**
	 * 构建份额明细
	 */
	buildTransferForm:function(transferVol){
		$("#fundCode").val(transferVol.productCode);//产品代码
		NoTradeOverAccountModify.counterOrderDto.fundCode=transferVol.productCode;
		$("#subsAmt").val("");//认缴金额
		$("#appVol").val("");//申请份额

		$("#appVolUpper").html("");//申请份额大写
		$("#subsAmtUpper").html("");//认缴金额大写

		// 查询产品（打标）信息
		QueryHighProduct.queryFundInfo(NoTradeOverAccountModify.transferBal.productCode);
		NoTradeOverAccountModify.fundInfo = QueryHighProduct.fundInfo;
		// 处理股权分次call产品
		if ("1" === NoTradeOverAccountModify.fundInfo.peDivideCallFlag) {
			$("#subsAmt").attr("isnull","false");
		} else {
			$("#subsAmt").removeAttr("isnull");
		}
	}


};
