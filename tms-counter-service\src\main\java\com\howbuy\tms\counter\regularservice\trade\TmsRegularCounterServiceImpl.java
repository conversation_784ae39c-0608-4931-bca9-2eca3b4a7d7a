/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularservice.trade;

import com.google.common.base.Objects;
import com.howbuy.tms.batch.facade.trade.counterend.CounterEndFacade;
import com.howbuy.tms.batch.facade.trade.counterend.CounterEndRequest;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.database.CounterCheckFlagEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto.DtlBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.CancelTypeEnum;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlFacade;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlRequest;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlResponse;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlResponse.BalanceDtlBean;
import com.howbuy.tms.regular.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderFacade;
import com.howbuy.tms.regular.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderRequest;
import com.howbuy.tms.regular.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderResponse;
import com.howbuy.tms.regular.batch.facade.query.querysubmitcheckorder.bean.QuerySubmitCheckOrderCondition;
import com.howbuy.tms.regular.batch.facade.query.querysubmitcheckorder.bean.SubmitCheckOrderRespBean;
import com.howbuy.tms.regular.batch.facade.trade.counterpurchase.CounterPurchaseFacade;
import com.howbuy.tms.regular.batch.facade.trade.counterpurchase.CounterPurchaseRequest;
import com.howbuy.tms.regular.batch.facade.trade.counterpurchase.CounterPurchaseResponse;
import com.howbuy.tms.regular.batch.facade.trade.counterpurchase.bean.CounterPurchaseOrderBean;
import com.howbuy.tms.regular.batch.facade.trade.countertradecancel.CounterTradeCancelFacade;
import com.howbuy.tms.regular.batch.facade.trade.countertradecancel.CounterTradeCancelRequest;
import com.howbuy.tms.regular.batch.facade.trade.countertradecancel.CounterTradeCancelResponse;
import com.howbuy.tms.regular.batch.facade.trade.countertradecancel.bean.CounterTradeCancelOrderBean;
import com.howbuy.tms.regular.orders.facade.query.querycustcanceldeal.CancelDealBean;
import com.howbuy.tms.regular.orders.facade.query.querycustcanceldeal.QueryCustCancelDealFacade;
import com.howbuy.tms.regular.orders.facade.query.querycustcanceldeal.QueryCustCancelDealRequest;
import com.howbuy.tms.regular.orders.facade.query.querycustcanceldeal.QueryCustCancelDealResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:(定期柜台控制台服务)
 * <AUTHOR>
 * @date 2018年6月22日 下午3:53:34
 * @since JDK 1.6
 */
@Service("tmsRegularCounterService")
public class TmsRegularCounterServiceImpl implements TmsRegularCounterService {

    //private static final Logger logger = LoggerFactory.getLogger(TmsRegularCounterServiceImpl.class);

    @Autowired
    private CounterPurchaseFacade counterPurchaseFacade;

    @Autowired
    private CounterTradeCancelFacade counterCancelFacade;

    @Autowired
    private TmsRegularCounterValidService tmsRegularCounterValidService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private QueryAcctBalanceDtlFacade queryAcctBalanceDtlFacade;

    @Autowired
    private QueryCustCancelDealFacade queryCustCancelDealFacade;

    @Autowired
    private QuerySubmitCheckOrderFacade querySubmitCheckOrderFacade;

    @Autowired
    private RegularOrderService regularOrderService;

    @Autowired
    private CounterEndFacade counterEndFacade;
    
    @Override
    public CounterPurchaseRespDto counterPurchase(RegularCounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception {
        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_REGULAR.getCode(), null, disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterPurchaseReqDto.getAppDt(), counterPurchaseReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }
        boolean validFlag = tmsRegularCounterValidService.subsOrPurValidate(counterPurchaseReqDto, disInfoDto);
        CounterPurchaseRespDto resp = null;
        CounterPurchaseRequest request = new CounterPurchaseRequest();
        if (validFlag) {
            if (counterPurchaseReqDto != null) {
                CounterPurchaseOrderBean counterPurchaseOrderBean = new CounterPurchaseOrderBean();
                BeanUtils.copyProperties(counterPurchaseReqDto, counterPurchaseOrderBean);
                request.setCounterPurchaseOrderBean(counterPurchaseOrderBean);
            }

            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterPurchaseFacade, request, disInfoDto);
            if (baseResp != null) {
                CounterPurchaseResponse counterPurchaseResponse = (CounterPurchaseResponse) baseResp;
                resp = new CounterPurchaseRespDto();
                BeanUtils.copyProperties(counterPurchaseResponse, resp);
            }
        }
        return resp;
    }

    @Override
    public QueryAcctBalanceDtlRespDto queryAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        List<String> protocolTypeList = new ArrayList<String>();
        protocolTypeList.add(ProtocolTypeEnum.DEFAULT_FUND.getCode());
        return getAcctBalanceDtl(reqDto, disInfoDto, protocolTypeList);
    }
    
    @Override
    public QueryAcctBalanceDtlRespDto queryAcctBalDtlByMultiProtocol(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception {
        return getAcctBalanceDtl(reqDto, disInfoDto, protocolTypeList);
    }
    
    private QueryAcctBalanceDtlRespDto getAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception {
        QueryAcctBalanceDtlRespDto queryFundBalDtlRespDto = null;
        QueryAcctBalanceDtlRequest request = new QueryAcctBalanceDtlRequest();
        request.setTxAcctNo(reqDto.getCustNo());
        request.setProductCode(reqDto.getFundCode());
        
        if(CollectionUtils.isNotEmpty(protocolTypeList)){
            request.setProtocolType(null);
            request.setProtocolTypeList(protocolTypeList);
        }
        
        request.setDisCode(disInfoDto.getDisCode());
        request.setAppDt(reqDto.getAppDt());
        request.setAppTm(reqDto.getAppTm());
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryAcctBalanceDtlFacade, request, disInfoDto);
        if (baseResp != null) {
            queryFundBalDtlRespDto = new QueryAcctBalanceDtlRespDto();
            QueryAcctBalanceDtlResponse queryAcctBalanceDtlResponse = (QueryAcctBalanceDtlResponse) baseResp;
            BeanUtils.copyProperties(queryAcctBalanceDtlResponse, queryFundBalDtlRespDto);
            List<BalanceDtlBean> balanceDtlList = queryAcctBalanceDtlResponse.getBalanceDtlList();
            List<DtlBean> dtlList = new ArrayList<DtlBean>();
            if (!CollectionUtils.isEmpty(balanceDtlList)) {
                DtlBean dtlBean = null;
                for (BalanceDtlBean balanceDtlBean : balanceDtlList) {
                    dtlBean = new DtlBean();
                    BeanUtils.copyProperties(balanceDtlBean, dtlBean);
                    dtlBean.setBankAcctNo(balanceDtlBean.getBankAcct());
                    dtlBean.setTxAcctNo(queryAcctBalanceDtlResponse.getTxAcctNo());
                    dtlBean.setDisCode(queryAcctBalanceDtlResponse.getDisCode());
                    dtlBean.setProductCode(queryAcctBalanceDtlResponse.getProductCode());
                    dtlBean.setFundShareClass(queryAcctBalanceDtlResponse.getFundShareClass());
                    dtlBean.setProductName(queryAcctBalanceDtlResponse.getProductName());
                    dtlBean.setProductType(queryAcctBalanceDtlResponse.getProductType());
                    dtlBean.setNav(queryAcctBalanceDtlResponse.getNav());
                    dtlBean.setProductStatus(queryAcctBalanceDtlResponse.getProductStatus());
                    dtlBean.setNavDt(queryAcctBalanceDtlResponse.getNavDt());
                    dtlBean.setBuyStatus(queryAcctBalanceDtlResponse.getBuyStatus());
                    dtlBean.setRedeemStatus(queryAcctBalanceDtlResponse.getRedeemStatus());

                    dtlList.add(dtlBean);
                }
            }
            queryFundBalDtlRespDto.setBalanceDtlList(dtlList);
        }
        return queryFundBalDtlRespDto;
    }



    @Override
    public RegularCounterCancelRespDto counterCancel(RegularCounterCancelReqDto counterCancelReqDto, DisInfoDto disInfoDto) throws Exception {
        RegularCounterCancelRespDto resp = null;
        CounterTradeCancelRequest request = new CounterTradeCancelRequest();
        boolean validFlag = tmsRegularCounterValidService.cancelOrderValidate(counterCancelReqDto, disInfoDto);
        if (validFlag) {
            if (counterCancelReqDto != null) {
                CounterTradeCancelOrderBean counterCancelOrderBean = new CounterTradeCancelOrderBean();
                BeanUtils.copyProperties(counterCancelReqDto, counterCancelOrderBean);
                request.setCounterTradeCancelOrderBean(counterCancelOrderBean);
            }

            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterCancelFacade, request, disInfoDto);
            if (baseResp != null) {
                CounterTradeCancelResponse counterCancelResponse = (CounterTradeCancelResponse) baseResp;
                resp = new RegularCounterCancelRespDto();
                BeanUtils.copyProperties(counterCancelResponse, resp);
            }
        }
        return resp;
    }


    @Override
    public List<RegularCancelDealDto> queryCanCancelOrder(String dealNo, String custNo, DisInfoDto disInfoDto) throws Exception {
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        List<RegularCancelDealDto> orderList = null;
        QueryCustCancelDealRequest request = new QueryCustCancelDealRequest();
        request.setPageSize(0);
        request.setTxAcctNo(custNo);
        // request.setDealNo(dealNo);
        request.setTaTradeDt(workDay);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCustCancelDealFacade, request, disInfoDto);
        if (baseResp != null) {
            QueryCustCancelDealResponse queryHighCanCancelDealOrdersResponse = (QueryCustCancelDealResponse) baseResp;
            List<CancelDealBean> cancelOrderList = queryHighCanCancelDealOrdersResponse.getCancelDealList();
            if (!CollectionUtils.isEmpty(cancelOrderList)) {
                orderList = new ArrayList<RegularCancelDealDto>();
                RegularCancelDealDto orderDto = null;
                if (!StringUtils.isEmpty(dealNo)) {
                    for (CancelDealBean cancelBean : cancelOrderList) {
                        if (dealNo.equals(cancelBean.getDealNo())) {
                            orderDto = new RegularCancelDealDto();
                            BeanUtils.copyProperties(cancelBean, orderDto);
                            orderList.add(orderDto);
                        }
                    }
                } else {
                    for (CancelDealBean cancelBean : cancelOrderList) {
                        orderDto = new RegularCancelDealDto();
                        BeanUtils.copyProperties(cancelBean, orderDto);
                        orderList.add(orderDto);
                    }
                }
            }
        }
        return orderList;
    }

    @Override
    public RegularCounterOrderDto counterQueryOrderById(RegularCounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception {
        QuerySubmitCheckOrderRequest request = new QuerySubmitCheckOrderRequest();
        if (counterQueryOrderReqDto != null) {
            BeanUtils.copyProperties(counterQueryOrderReqDto, request);
            QuerySubmitCheckOrderCondition querySubmitCheckOrderCondition = new QuerySubmitCheckOrderCondition();
            BeanUtils.copyProperties(counterQueryOrderReqDto, querySubmitCheckOrderCondition);
            request.setQueryCondition(querySubmitCheckOrderCondition);
        }
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderFacade, request, disInfoDto);
        RegularCounterOrderDto counterOrderDto = null;
        if (baseResp != null) {
            QuerySubmitCheckOrderResponse querySubmitCheckOrderResponse = (QuerySubmitCheckOrderResponse) baseResp;
            List<SubmitCheckOrderRespBean> pageList = querySubmitCheckOrderResponse.getQuerySubmitCheckOrderRespBeanList();
            if (!CollectionUtils.isEmpty(pageList)) {
                for (SubmitCheckOrderRespBean orderBean : pageList) {
                    if (Objects.equal(orderBean.getDealAppNo(), counterQueryOrderReqDto.getDealAppNo())) {
                        counterOrderDto = new RegularCounterOrderDto();
                        BeanUtils.copyProperties(orderBean, counterOrderDto);
                    }
                }
            }
        }
        return counterOrderDto;
    }

    @Override
    public RegularCounterQueryOrderRespDto counterQueryOrder(RegularCounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception {
        RegularCounterQueryOrderRespDto resp = null;
        QuerySubmitCheckOrderRequest request = new QuerySubmitCheckOrderRequest();
        if (counterQueryOrderReqDto != null) {
            BeanUtils.copyProperties(counterQueryOrderReqDto, request);
            QuerySubmitCheckOrderCondition querySubmitCheckOrderCondition = new QuerySubmitCheckOrderCondition();
            BeanUtils.copyProperties(counterQueryOrderReqDto, querySubmitCheckOrderCondition);

            List<String> checkFlagList = new ArrayList<String>();
            if (!CollectionUtils.isEmpty(counterQueryOrderReqDto.getCheckFlagLsit())) {
                checkFlagList.addAll(counterQueryOrderReqDto.getCheckFlagLsit());
            }
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getCheckFlag())) {
                checkFlagList.add(counterQueryOrderReqDto.getCheckFlag());
            }
            querySubmitCheckOrderCondition.setCheckFlagList(checkFlagList);

            // 业务类型码查询设置
            List<String> txCodes = new ArrayList<String>();
            if (!CollectionUtils.isEmpty(counterQueryOrderReqDto.getTxCodeList())) {
                txCodes.addAll(counterQueryOrderReqDto.getTxCodeList());
            }
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getTxCode())) {
                txCodes.add(counterQueryOrderReqDto.getTxCode());
            }
            querySubmitCheckOrderCondition.setSearchTxCode(txCodes);

            request.setQueryCondition(querySubmitCheckOrderCondition);
        }
        // 接口调用码设置
        request.setTxCode(TxCodes.QUERY_COUNTER_ORDER);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderFacade, request, disInfoDto);

        List<RegularCounterOrderDto> counterOrderList = null;
        if (baseResp != null) {
            QuerySubmitCheckOrderResponse querySubmitCheckOrderResponse = (QuerySubmitCheckOrderResponse) baseResp;
            resp = new RegularCounterQueryOrderRespDto();
            List<SubmitCheckOrderRespBean> pageList = querySubmitCheckOrderResponse.getQuerySubmitCheckOrderRespBeanList();
            if (!CollectionUtils.isEmpty(pageList)) {
                RegularCounterOrderDto counterOrderDto = null;
                counterOrderList = new ArrayList<RegularCounterOrderDto>(16);
                for (SubmitCheckOrderRespBean orderBean : pageList) {
                    counterOrderDto = new RegularCounterOrderDto();
                    BeanUtils.copyProperties(orderBean, counterOrderDto);
                    counterOrderDto.setCustNameEncrypt(counterOrderDto.getCustName());
                    counterOrderList.add(counterOrderDto);
                }
            }
            resp.setCounterOrderList(counterOrderList);
            resp.setPageAppAmt(querySubmitCheckOrderResponse.getPageAppAmt());
            resp.setPageAppVol(querySubmitCheckOrderResponse.getPageAppVol());
            resp.setTotalAppAmt(querySubmitCheckOrderResponse.getTotalAppAmt());
            resp.setTotalAppVol(querySubmitCheckOrderResponse.getTotalAppVol());
            resp.setTotalCount(querySubmitCheckOrderResponse.getTotalCount());
            resp.setPageNo(querySubmitCheckOrderResponse.getPageNo());
            resp.setTotalPage(querySubmitCheckOrderResponse.getTotalPage());
        }
        return resp;
    }

    @Override
    public boolean checkOrder(RegularSubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        String txCode = submitUncheckOrderDto.getTxCode();
        String checkFlag = submitUncheckOrderDto.getCheckFlag();
        boolean flag = false;
        if (CounterCheckFlagEnum.CHECKED_SUCC.getKey().equals(checkFlag)) {
            // 审核成功
            switch (txCode) {
                case TxCodes.COUNTER_PURCHASE:
                    flag = regularOrderService.subsOrPurCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_CANCEL:
                    flag = regularOrderService.cancelOrder(submitUncheckOrderDto, disInfoDto, CancelTypeEnum.SELF_CANCEL.getCode());
                    break;
                case TxCodes.COUNTER_FORCE_CANCEL:
                    flag = regularOrderService.cancelOrder(submitUncheckOrderDto, disInfoDto, CancelTypeEnum.FORCE_CANCEL.getCode());
                    break;
                default:
                    flag = regularOrderService.subsOrPurCounter(submitUncheckOrderDto, disInfoDto);
                    break;
            }
        } else if (CounterCheckFlagEnum.CHECKED_REJECT.getKey().equals(checkFlag)) {
            // 审核退回
            regularOrderService.checkCounterOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        } else if (CounterCheckFlagEnum.CANCEL.getKey().equals(checkFlag)) {
            // 审核废单
            regularOrderService.checkCounterOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        }
        return flag;
    }

    @Override
    public boolean counterEnd(DisInfoDto disInfoDto) throws Exception {
        String tradeDt = tmsCounterService.getSystemWorkDay(disInfoDto);
        CounterEndRequest request = new CounterEndRequest();
        request.setTradeDt(tradeDt);
        TmsFacadeUtil.executeThrowException(counterEndFacade, request, disInfoDto);
        return true;
    }
}
