/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.RuleIdEnum;
import com.howbuy.interlayer.product.enums.RuleKycEnum;
import com.howbuy.interlayer.product.model.adviser.AdviserProductRuleCfgModel;
import com.howbuy.interlayer.product.service.AdviserProductRuleCfgService;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.busi.QualificaitionTypeEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustrisksurvey.QueryCustRiskSurveyOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustrisksurvey.QueryCustRiskSurveyResult;
import com.howbuy.tms.common.validator.account.CustRiskValidator;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.ExceptionCodes;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterPortfolioProductDto;
import com.howbuy.tms.counter.dto.CounterPurchaseReqDto;
import com.howbuy.tms.counter.dto.CounterPurchaseRespDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.ValidatorRetailRiskLevelDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Optional;

/***
 *
 * @description:(认申购)
 * <AUTHOR>
 * @date 2017年9月15日 上午9:34:42
 * @since JDK 1.6
 */
@Controller
public class BuyFundController extends AbstractController {
    private static Logger logger = LogManager.getLogger(BuyFundController.class);

    @Autowired
    private TradeValidateService tradeValidateService;
    @Autowired
    private AdviserProductRuleCfgService adviserProductRuleCfgService;

    @Autowired
    private QueryCustRiskSurveyOuterService queryCustRiskSurveyOuterService;
    

    /***
     *
     * buyConfirm:(确认购买)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 上午9:35:02
     */
    @RequestMapping("/tmscounter/fund/buyconfirm.htm")
    public ModelAndView buyConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String buyConfirmCmd = request.getParameter("buyConfirmForm");
        String custInfoForm = request.getParameter("custInfoForm");
        String productInfoForm = request.getParameter("productInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        String filePath = request.getParameter("filePath");
        logger.debug("BuyController|buyConfirm|buyConfirmCmd:{},custInfoForm:{},fundInfoForm:{},transactorInfoForm:{},filePath:{}", buyConfirmCmd, custInfoForm,
                productInfoForm, transactorInfoForm, filePath);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        CounterPurchaseReqDto counterPurchaseReqDto = JSON.parseObject(buyConfirmCmd, CounterPurchaseReqDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        CounterPortfolioProductDto fundInfo = JSON.parseObject(productInfoForm, CounterPortfolioProductDto.class);

        // 柜台不支持Y份额基金交易
        tradeValidateService.tradeValidate(fundInfo.getProductCode(), counterPurchaseReqDto.getAppDt());

        boolean adviserFlag = fundInfo.isAdviserFlag();

        counterPurchaseReqDto.setDealAppNo(dealAppNo);
        counterPurchaseReqDto.setFeeRate(counterPurchaseReqDto.getOriginalFeeRate());

        counterPurchaseReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterPurchaseReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        counterPurchaseReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterPurchaseReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterPurchaseReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterPurchaseReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterPurchaseReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

        counterPurchaseReqDto.setCustName(custInfoDto.getCustName());
        counterPurchaseReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterPurchaseReqDto.setDisCode(custInfoDto.getDisCode());
        counterPurchaseReqDto.setIdNo(custInfoDto.getIdNo());
        counterPurchaseReqDto.setIdType(custInfoDto.getIdType());
        counterPurchaseReqDto.setInvstType(custInfoDto.getInvstType());

        counterPurchaseReqDto.setFundName(fundInfo.getProductName());
        counterPurchaseReqDto.setFundCode(fundInfo.getProductCode());
        counterPurchaseReqDto.setFundShareClass(fundInfo.getFundShareClass());
        counterPurchaseReqDto.setTaCode(fundInfo.getTaCode());
        counterPurchaseReqDto.setPartnerCode(fundInfo.getPartnerCode());

        counterPurchaseReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());

        counterPurchaseReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());

        // 自划款
        counterPurchaseReqDto.setPaymentType(PaymentTypeEnum.SELF_DRAWING.getCode());
        counterPurchaseReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        counterPurchaseReqDto.setTaTradeDt(counterPurchaseReqDto.getAppDt());
        // 双录文件
        counterPurchaseReqDto.setDoubleRecordFilePath(filePath);

        counterPurchaseReqDto.setAdviserFlag(adviserFlag);
        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterPurchaseReqDto);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        CounterPurchaseRespDto responseDto = null;
        if (adviserFlag) {
            // 投顾组合协议
            counterPurchaseReqDto.setProtocolType(ProtocolTypeEnum.ADVISER_PORTFOLIFO.getCode());
            counterPurchaseReqDto.setProductClass(ProductClassEnum.PORTFOLIO.getCode());
            responseDto = tmsFundCounterService.counterAdviserPurchase(counterPurchaseReqDto, disInfoDto);

        }else {
            // 普通公募协议
            counterPurchaseReqDto.setProtocolType(ProtocolTypeEnum.DEFAULT_FUND.getCode());
            // 普通公募产品
            counterPurchaseReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
            responseDto = tmsFundCounterService.counterPurchase(counterPurchaseReqDto, disInfoDto);

        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/fund/validatorretailrisklevel.htm")
    public ModelAndView validatorRetailRiskLevel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String custInfoForm = request.getParameter("custInfoForm");

        String productInfoForm = request.getParameter("productInfoForm");

        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);

        CounterPortfolioProductDto productInfoDto = JSON.parseObject(productInfoForm, CounterPortfolioProductDto.class);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        ValidatorRetailRiskLevelDto responseDto = new ValidatorRetailRiskLevelDto();
        responseDto.setReturnCode("Z0000000");
        responseDto.setDescription("验证成功");
        rst.setBody(responseDto);

        try {
            if (StringUtils.isEmpty(custInfoDto.getCustNo()) || StringUtils.isEmpty(custInfoDto.getInvstType()) ||
                    StringUtils.isEmpty(productInfoDto.getRiskLevel()) || StringUtils.isEmpty(custInfoDto.getInvestorType())) {
                throw new ValidateException(ExceptionCodes.PARAMS_ERROR, MessageSource.getMessageByCode("Z3000000"));
            }

            if (QualificaitionTypeEnum.PRO.getCode().equals(custInfoDto.getInvestorType())) {
                WebUtil.write(response, rst);
                return null;
            }

            QueryCustRiskSurveyResult queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurveyFull(custInfoDto.getCustNo(), "0", custInfoDto.getDisCode());

            boolean adviserRiskStrongCheck = false;
            boolean adviserFlag = productInfoDto.isAdviserFlag();
            if (adviserFlag) {
                AdviserProductRuleCfgModel productRuleCfgModel = adviserProductRuleCfgService.getByRuleIdAndPartnerCode(RuleIdEnum.RULE_KYC.getCode(), productInfoDto.getPartnerCode());
                String ruleFlag = Optional.ofNullable(productRuleCfgModel).map(AdviserProductRuleCfgModel::getRuleFlag).orElse(null);
                if (RuleKycEnum.STRONG_CHECK.getCode().equals(ruleFlag)) {
                    adviserRiskStrongCheck = true;
                }
            }

            validateRiskLevel(custInfoDto, productInfoDto, queryCustRiskSurveyResult, adviserRiskStrongCheck);
        } catch (ValidateException e) {
            logger.error("", e);
            throw new TmsCounterException(e.getErrorCode(), e.getErrorDesc(), e);
        }

        WebUtil.write(response, rst);
        return null;
    }

    private static void validateRiskLevel(CustInfoDto custInfoDto, CounterPortfolioProductDto productInfoDto, QueryCustRiskSurveyResult queryCustRiskSurveyResult,
            boolean adviserRiskStrongCheck) {
        try {
            CustRiskValidator.validateRiskLevel(custInfoDto.getCustNo(), null, productInfoDto.getRiskLevel(), queryCustRiskSurveyResult);
        } catch (ValidateException e) {
            //风险问卷匹配规则若为强匹配，则不匹配的情况下，不允许用户买入，提示：客户风险等级高于产品风险等级，无法购买，请让用户修改风险等级后购买。
            if (adviserRiskStrongCheck && StringUtils.equals(e.getErrorCode(), ExceptionCodes.PRODUCT_RISK_OVER_CUST_RISK)) {
                throw new TmsCounterException(ExceptionCodes.BUS_EXCEPTION, "客户风险等级低于产品风险等级，无法购买，请让用户修改风险等级后购买。", e);
            } else {
                throw e;
            }
        }
    }

    /**
     * @description:双录文件上传
     * @param request
     * @param response
     * @return org.springframework.web.servlet.ModelAndView
     * @author: xingxing.wang
     * @date: 2020/8/21 18:26
     * @since JDK 1.8
     */
    @RequestMapping("/tmscounter/fund/uploadDoubleRecordFile.htm")
    public ModelAndView uploadDoubleRecordFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String filePath = "";
        String appDt = request.getParameter("appDt");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        CommonsMultipartFile multipartFile = (CommonsMultipartFile) multipartRequest.getFile("userFile");
        if (multipartFile != null) {
            filePath = tmsFundCounterService.uploadDoubleRecordFile(multipartFile.getInputStream(), multipartFile.getOriginalFilename(), appDt);
        }
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(filePath);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * @description:获取双录文件流
     * @param request
     * @param response
     * @return org.springframework.web.servlet.ModelAndView
     * @author: xingxing.wang
     * @date: 2020/8/21 18:26
     * @since JDK 1.8
     */
    @RequestMapping("/tmscounter/fund/getDoubleRecordFile.htm")
    public ModelAndView getDoubleRecordFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String filePath = request.getParameter("filePath");
        String fileName = filePath.substring(filePath.lastIndexOf(File.separator) + 1);
        //获取响应的输出流
        OutputStream outputStream = response.getOutputStream();
        File file = new File(filePath);
        if (file.exists()) {
            @SuppressWarnings("resource")
            RandomAccessFile targetFile = new RandomAccessFile(file, "r");
            long fileLength = targetFile.length();
            //设置响应头，把文件名字设置好
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            //设置文件长度
            response.setHeader("Content-Length", String.valueOf(fileLength));
            //解决编码问题
            response.setHeader("Content-Type", "application/octet-stream");

            byte[] cache = new byte[1024 * 300];
            int flag;
            while ((flag = targetFile.read(cache)) != -1) {
                outputStream.write(cache, 0, flag);
            }
        } else {
            String message = "file:" + fileName + " not exists";
            //解决编码问题
            response.setHeader("Content-Type", "application/json");
            outputStream.write(message.getBytes(StandardCharsets.UTF_8));
        }
        outputStream.flush();
        outputStream.close();
        return null;
    }

}
