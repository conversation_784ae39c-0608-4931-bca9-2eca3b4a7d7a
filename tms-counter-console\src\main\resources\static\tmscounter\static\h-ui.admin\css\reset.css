@charset "utf-8";
/*	reset */
/*	reset */
body,input,textarea {font:14px 'Microsoft YaHei',Arial,sans-serif;}
body, dl, dd, h1, h2, h3, h4, h5, h6, p, form ,ul , ol ,pre {margin:0;}
::-webkit-input-placeholder {color:#aaa;}
::-moz-placeholder {color:#aaa;}
textarea {resize:none;}
table {border-collapse: collapse;}
.clear{ clear: both; *zoom:1}
.clear:after{content:"."; display:block; height: 0; clear: both; visibility:hidden;}
ul {padding: 0;list-style: none;}

/* 默认超链样式 */
a{ color:#2a5894;}
a:hover{ color:#c00;}

/* color */
.red{ color:#C00;}
.green{ color:#080;}
.blue {color:#2a5894;}
.gray3 {color: #333;}
.gray6 {color:#666;}
.gray9 {color:#999;}
.orange {color:#f80;}
.black {color: #000;}
.white {color: #fff;}

/*font*/
.f12 {font-size: 12px;}
.f14 {font-size: 14px;}
.f16 {font-size: 16px;}
.f18 {font-size: 18px;}
.f20 {font-size: 20px;}
.fwb {font-weight: bold;}
.fwn {font-weight: normal;}
.fsn {font-style:normal;}
.fsi {font-style:italic;}
.fyahei {font-family:'Microsoft Yahei';}
.farial {font-family:Arial,sans-serif;}

/*float*/
.fl {float:left;}
.fr {float:right;}

/*margin padding*/
.mt0 {margin-top: 0 !important;}
.mt5 {margin-top:5px;}
.mt10 {margin-top: 10px;}
.mt15 {margin-top: 15px;}
.mt20 {margin-top: 20px;}
.mt25 {margin-top: 25px;}
.mt30 {margin-top: 30px;}
.mt60{margin-top:60px;}
.ml0 {margin-left: 0 !important;}
.mr0 {margin-right: 0 !important;}
.ml5 {margin-left: 5px;}
.ml10 {margin-left: 10px;}
.ml15 {margin-left: 15px;}
.ml20 {margin-left: 20px;}
.ml25 {margin-left: 25px;}
.ml30 {margin-left: 30px;}
.ml40 {margin-left: 40px;}
.ml80 {margin-left: 80px;}
.ml90 {margin-left: 90px;}
.ml100{margin-left:100px;}
.ml115 {margin-left:115px;}
.mr5 {margin-right: 5px;}
.mr10 {margin-right: 10px;}
.mr15 {margin-right: 15px;}
.mr20 {margin-right: 20px;}
.mr25 {margin-right: 25px;}
.mr28 {margin-right: 28px;}
.mr30 {margin-right: 30px;}
.mr100{margin-right:100px;}
.mb0 {margin-bottom: 0 !important;}
.pt0 {padding-top: 0 !important;}
.pt5 {padding-top: 5px;}
.pt10 {padding-top: 10px;}
.pt15 {padding-top: 15px;}
.pt20 {padding-top: 20px;}
.pt25 {padding-top: 25px;}
.pt30 {padding-top: 30px;}
.pt40 {padding-top: 40px;}
.pr0 {padding-right: 0 !important;}
.pr5 {padding-right: 5px;}
.pr10 {padding-right: 10px;}
.pr15 {padding-right: 15px;}
.pr20 {padding-right: 20px;}
.pr25 {padding-right: 25px;}
.pr30 {padding-right: 30px;}
.pb0 {padding-bottom: 0 !important;}
.pb5 {padding-bottom: 5px;}
.pb10 {padding-bottom: 10px;}
.pb15 {padding-bottom: 15px;}
.pb20 {padding-bottom: 20px;}
.pb25 {padding-bottom: 25px;}
.pb30 {padding-bottom: 30px;}
.pl0 {padding-left: 0 !important;}
.pl5 {padding-left: 5px;}
.pl10 {padding-left: 10px;}
.pl15 {padding-left: 15px;}
.pl20 {padding-left: 20px;}
.pl25 {padding-left: 25px;}
.pl30 {padding-left: 30px;}
.pl50 {padding-left: 50px;}
.pl80{padding-left:80px;}

/*text-align*/
.tac { text-align:center;}
.tal { text-align:left;}
.tar { text-align:right;}

/*other*/
.hide { display:none;}
.db { display:block;}
.dib { display:inline-block;}
.dt { display:table;width:100%;}
.dtc { display:table-cell;}
.vat { vertical-align:top;}
.vam { vertical-align:middle;}
.owh { overflow:hidden;}
.owh-x { overflow-x:hidden;}
.owh-y { overflow-y:hidden;}
.wp160{width:160px;}
.wp148{width:148px;}
.wp50{width:50px;}
.wp60{width:60px;}
.wp70{width:70px;}
.wp160{width:160px;}
.wp90{width:90px;}
.wp100{width:100px;}
.wp150{width:150px;}
.wp155{width:155px;}
.wp180{width:180px;}
.wp200{width:200px;}
.wp260{width:260px;}
.wp290{width:290px;}
.wp315{width:315px;}
.wp385{width:385px;}
.lh28{line-height:28px;}
.bd{border:1px solid #dbdfe4;}
.relative{position:relative;}
.absolute{position:absolute;}
.mb10{margin-bottom:10px;}
.h28{height:28px !important;}