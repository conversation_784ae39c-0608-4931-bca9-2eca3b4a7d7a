/**
 * 赎回方向审核
 */
$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	var dealNo = CommonUtil.getParam("dealNo");
	ApplyModifyDirection.checkOrder = {};
	ApplyModifyDirection.init(checkId,custNo,disCode,idNo,dealNo);
});

var ApplyModifyDirection = {
	init:function(checkId, custNo, disCode,idNo, dealNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		var directionList = {};
		// 个人用户
		if (QueryCustInfo.custInfo.invstType == '1') {
			directionList = CONSTANTS.PERSON_DIRECTION_TYPE_MAP;
		} else {
			directionList = CONSTANTS.JIGOU_DIRECTION_TYPE_MAP;
		}
		var selectModify = CommonUtil.selectOptionsHtml(directionList);
		$("#afterModify").html(selectModify);

		// 废单
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyModifyDirection.checkOrder);
		});
		// 设置申请订单信息
		QueryCheckOrder.queryCheckOrderById(checkId,ApplyModifyDirection.queryCheckOrderByIdBack);
	},


	/***
	 * 审核确认
	 */	
	confirm : function(dealAppNo){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var uri= TmsCounterConfig.APPLY_MODIFY_REDEEM_DIRECTION ||  {};
		
		if(CommonUtil.isEmpty(ApplyModifyDirection.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}

		if(ApplyModifyDirection.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		var afterModify = $("#afterModify").val();
		if(CommonUtil.isEmpty(afterModify)){
			CommonUtil.layer_tip("请选择修改后回款方向");
			return false;
		}

		var reqparamters ={
			"dealAppNo": CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
			"redeemDirection":$("#afterModify").val(),
			"dealNo":$(".dealNo").val(),
			"custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
			"checkedOrderForm":JSON.stringify(ApplyModifyDirection.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ApplyModifyDirection.callBack);
		return true;
	},

	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},


	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplyModifyDirection.checkOrder = bodyData.checkOrder || {};
		ApplyModifyDirection.dealOrderDtlBean = bodyData.dealOrderDtlBean || {};

		if(CommonUtil.isEmpty(ApplyModifyDirection.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		if(ApplyModifyDirection.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}

		$("#dealInfoId").empty();
		var element = ApplyModifyDirection.dealOrderDtlBean || {};
		var trList = [];
		trList.push(CommonUtil.formatData(element.dealNo, '--'));
		trList.push(CommonUtil.formatData(CommonUtil.getMapValue(CONSTANTS.BUSI_CODE_MAP, element.mBusiCode, ''), '--'));
		trList.push(CommonUtil.formatData(element.fundName, '--'));
		trList.push(CommonUtil.formatData(element.fundCode, '--'));
		trList.push(CommonUtil.formatData(element.appDate, '--'));
		trList.push(CommonUtil.formatData(element.appAmt, '--'));
		trList.push(CommonUtil.formatData(element.appVol, '--'));
		trList.push(CommonUtil.formatData(element.tFundCode, '--'));
		trList.push(CommonUtil.formatData(element.tFundName, '--'));
		trList.push(element.mBusiCode = '1136' ? CommonUtil.formatData(element.transferAmt,'--') : '--');
		trList.push(element.mBusiCode = '1136' ? CommonUtil.formatData(element.transferVol,'--') :'--');
		trList.push(CommonUtil.getMapValue(CONSTANTS.TX_APP_FLAG_MAP, element.txAppFlag, '--'));
		trList.push(CommonUtil.formatData(element.appDate, '--'));
		trList.push(CommonUtil.formatData(element.appTime, '--'));
		var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
		$("#dealInfoId").append(trHtml);

		$(".dealNo").val(element.dealNo);
		$.each(CONSTANTS.JIGOU_DIRECTION_TYPE_SHOW_MAP, function (key, value) {
			if (key == ApplyModifyDirection.checkOrder.beforeModifyDirection) {
				$("#beforeModify").html(value);
			}
		});
		// 修改提交
		$("#succBtn").on('click',function(){
			ApplyModifyDirection.confirm(ApplyModifyDirection.checkOrder.dealAppNo);
		});


	},
}
