/**
 * 初始化页面事件
 */

$(function () {
    //viewType 0-查看；1-审核；2-修改
    var viewType = CommonUtil.getParam("viewType");
    var txAcctNo = CommonUtil.getParam("txAcctNo");
    var fundCode = CommonUtil.getParam("fundCode");
    SubsAmtChangeDetailPage.initBtn(viewType, txAcctNo, fundCode);
    // 订单信息查询
    SubsAmtChangeDetailPage.querySubsAmtDetail(txAcctNo, fundCode);

});


var SubsAmtChangeDetailPage = {
        // 按钮初始化
        initBtn: function (viewType, txAcctNo, fundCode) {
            //隐藏按钮
            CommonUtil.higdeAllBtnOfDiv("submitDiv");
            if ("2" === viewType) {
                //修改
                $("#modifyBtn").show();
                //按钮绑定事件
                $("#modifyBtn").on('click', function () {
                    SubsAmtChangeDetailPage.confirmModify(txAcctNo, fundCode);
                });
            } else {
                $("#modifyBtn").hide();
            }
            // 显示返回按钮
            $("#checkBackBtn").show();
            // 返回
            $("#checkBackBtn").on("click", function () {
                parent.layer.closeAll();
            });
        },

        querySubsAmtDetail: function (txAcctNo, fundCode) {
            if (isEmpty(txAcctNo)) {
                showMsg("交易账号不能为空");
                return false;
            }
            if (isEmpty(fundCode)) {
                showMsg("产品编码不能为空");
                return false;
            }
            var uri = TmsCounterConfig.CUSTOMER_SUBS_AMT_DEAIL || "";
            var reqparamters = {};
            reqparamters.txAcctNo = txAcctNo;
            reqparamters.fundCode = fundCode;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
            CommonUtil.ajaxAndCallBack(paramters, SubsAmtChangeDetailPage.buildOrderInfo);
        }
        ,

        check: function () {
            var forceForm = $("#forceForm").serializeObject();
            var isNoTradeTransfer = "0";
            if (forceForm.isNoTradeTransfer != null) {
                isNoTradeTransfer = forceForm.isNoTradeTransfer;
            }
            var transferPrice = forceForm.transferPrice
            if (forceForm.isNoTradeTransfer != null) {
                if (isNoTradeTransfer === SubsAmtChangeDetailPage.isNoTradeTransfer
                    && SubsAmtChangeDetailPage.isNoTradeTransfer === "0") {
                    CommonUtil.layer_tip("没有任何修改");
                    return false;
                }
            }
            if (transferPrice === SubsAmtChangeDetailPage.transferPrice) {
                CommonUtil.layer_tip("没有任何修改");
                return false;
            }
        },
        /**
         * 提交修改
         */
        confirmModify: function (txAcctNo, fundCode) {
            if (window.checkedClick === '1') {
                CommonUtil.enabledBtn("modifyBtn");
                return false;
            }
            //防止重复点击
            window.checkedClick = '1';
            // 1.校验
            var forceForm = $("#forceForm").serializeObject();

            var uri = TmsCounterConfig.SUBS_AMT_DETAIL_APPLY || {};
            // 2.构建查询参数
            var reqparamters = {
                "txAcctNo": txAcctNo,
                "fundCode": fundCode,
                "subsAmt": forceForm.newSubsAmt,
                "appDt": forceForm.appDt,
                "appTime": forceForm.appTime
            };

            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, SubsAmtChangeDetailPage.confirmCallback);
        },

        confirmCallback: function (data) {
            var respCode = data.code || '';
            var respDesc = data.desc || '';
            if (CommonUtil.isSucc(respCode)) {
                layer.confirm('用户产品认缴金额修改成功', {
                    btn: ['确定'] //按钮
                }, function () {
                    // 回到上个页面,防止重复操作
                    layer.closeAll();
                    //刷新父页面
                    window.parent.location.reload();
                    //获取窗口索引
                    var index = parent.layer.getFrameIndex(window.name);
                    //关闭弹窗
                    parent.layer.close(index);
                });
            } else {
                CommonUtil.layer_tip("提交失败," + respDesc + "(" + respCode + ")");
            }
            CommonUtil.enabledBtn("modifyBtn");
        }
        ,

        /**
         *渲染产品信息查询结果
         */
        buildOrderInfo: function (data) {
            var bodyData = data.body || {};
            var subsAmtInfo = bodyData.detail || [];
            var currDt = bodyData.currDt || "--";
            var oldSubsAmt = CommonUtil.formatData(subsAmtInfo.subsAmt, '--');
            // 默认原认缴金额
            var newSubsAmt = oldSubsAmt
            var appendHtml = '';
            $("#layerrs").empty();
            appendHtml =
                '<tr className="text-c">' +
                '<td>基金代码:</td>' +
                '<td id="fundCode">' + formatData(subsAmtInfo.fundCode) + '</td>' +
                '<td>基金简称:</td>' +
                '<td id="fundName">' + formatData(subsAmtInfo.fundName) + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>原认缴金额:</td>' +
                '<td id="subsAmt">' + oldSubsAmt + '</td>' +
                '<td>原认缴金额(大写):</td>' +
                '<td id="subsAmtUp">' + CommonUtil.amtUpper(oldSubsAmt) + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>持仓份额:</td>' +
                '<td id="balanceVol">' + formatData(subsAmtInfo.balanceVol) + '</td>' +
                '<td>调整后认缴金额:</td>' +
                '<td> <input type="text" placeholder="请输入" id="newSubsAmt" name="newSubsAmt"  value="' + newSubsAmt + '"/></td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>调整金额:</td>' +
                '<td id="diffSubsAmt">' + SubsAmtChangeDetailPage.getChangeAmt(oldSubsAmt, newSubsAmt) + '</td>' +
                '<td>调整后认缴金额(大写):</td>' +
                '<td id="newSubsAmtUp">' + CommonUtil.amtUpper(newSubsAmt) + '</td>' +

                '<tr className="text-c">' +
                '<td>下单日期:</td>' +
                '<td id="appDt">' + currDt + '</td>' +
                '<td>下单时间:</td>' +
                '<td> <input type="text" placeholder="请输入" id="appTime" name="appTime"  value="090000"/></td>' +
                '<td> </tr>';
            $("#layerrs").append(appendHtml);
            // 添加输入框监听事件
            $('#newSubsAmt').on('input', function () {
                var newAmt = $(this).val();
                $('#diffSubsAmt').text(SubsAmtChangeDetailPage.getChangeAmt(oldSubsAmt, newAmt));
                $('#newSubsAmtUp').text(CommonUtil.amtUpper(newAmt));
            });
        },

        getChangeAmt: function (oldAmt, newAmt) {
            if (oldAmt === "--" || oldAmt === "") {
                return newAmt;
            }
            if (newAmt === "" || newAmt === "--") {
                return "--"
            }
            return newAmt - oldAmt;
        },

    }
;