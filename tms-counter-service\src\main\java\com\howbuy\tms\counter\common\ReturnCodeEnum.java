/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;
/**
 * @description:(TODO 请在此添加描述) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年2月9日 下午4:03:22
 * @since JDK 1.6
 */
public enum ReturnCodeEnum {
    /**
     * 成功
     */
    SUCC("0000", "成功"),
    
    /**
     * 成功
     */
    SUCC_NEW("0000000", "成功"),
    
    /**
     * 中台成功
     */
    SUCC_TMS("Z0000000", "成功");
    
    private String code;
    
    private String desc;
    
    ReturnCodeEnum(String code, String desc){
        this.code = code;
        this.desc =desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

