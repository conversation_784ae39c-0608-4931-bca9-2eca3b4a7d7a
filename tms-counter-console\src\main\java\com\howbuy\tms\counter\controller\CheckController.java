/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.counter.controller;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.page.Page;
import com.howbuy.crm.base.OrderFileEnum;
import com.howbuy.interlayer.product.enums.InvstTypeEnum;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.OpCheckNode;
import com.howbuy.tms.common.enums.busi.PreBookStateEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankCardSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankCardSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.bean.CustAllBankSensitiveModel;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankacctsensitiveinfo.QueryCustBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankacctsensitiveinfo.QueryCustBankCardSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankacctsensitiveinfo.QueryCustBankCardSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.bean.DisAcTxAcctBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustrisksurvey.QueryCustRiskSurveyOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustrisksurvey.QueryCustRiskSurveyResult;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.counter.aspect.BusinessAspect;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.CheckResultCmd;
import com.howbuy.tms.counter.cmd.FileinfoCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.enums.CheckActionTypeEnum;
import com.howbuy.tms.counter.enums.CheckTypeEnum;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.fundservice.trade.HighProductAppointService;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(柜台审核控制)
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class CheckController {

    private Logger logger = LogManager.getLogger(CheckController.class);

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    private QueryCustRiskSurveyOuterService queryCustRiskSurveyOuterService;

    @Autowired
    @Qualifier("queryAllCustInfoOuterService")
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;

    @Autowired
    @Qualifier("queryCustInfoOuterService")
    private QueryCustInfoOuterService queryCustInfoOuterService;
    @Autowired
    @Qualifier("queryCustBankAcctSensitiveInfoOuterService")
    private QueryCustBankAcctSensitiveInfoOuterService queryCustBankAcctSensitiveInfoOuterService;
    @Autowired
    @Qualifier("queryAllBankAcctSensitiveInfoOuterService")
    private QueryAllBankAcctSensitiveInfoOuterService queryAllBankAcctSensitiveInfoOuterService;
    @Autowired
    private HighProductAppointService highProductAppointService;
    @Autowired
    private HighProductService highProductService;
    private static final String VERSION_TYPE_GONGMU = "0";
    /**
     * 专户及私募版本
     */
    private static final String VERSION_TYPE_HIGH = "1";


    public static final Set<String> TX_CODE_SET;

    static {
        Set<String> set = new HashSet<>();
        //购买
        set.add("Z900011");
        //赎回
        set.add("Z900012");
        TX_CODE_SET = Collections.unmodifiableSet(set);
    }

    /**
     * 逻辑之前校验合法性
     *
     * @return
     * @author: jiaheng.gu
     * @date: 2022/1/18 14:43
     * @since JDK 1.8
     */
    private void validate(CounterOrderDto counterOrderDto, String checkType, OperatorInfoCmd operatorInfoCmd) {
        String creator = counterOrderDto.getCreator();
        //审核校验
        if (creator == null) {
            creator = "";
        }
        CheckActionTypeEnum checkActionTypeEnum = getCheckActionType(checkType);

        if (checkActionTypeEnum == null) {
            throw new TmsCounterException(TmsCounterResultEnum.UN_KNOW_CHECK_ACTION);
        }

        if (isCheck(checkType)) {
            if (creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_REPLY);
            }
        }

        if (isModify(checkType)) {
            if (!creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_MODIFY_ERROR);
            }
        }
    }


    /**
     * @api {GET} /tmscounter/checkBuyOrderInfo.htm checkBuyOrderInfo
     * @apiVersion 1.0.0
     * @apiGroup CheckController
     * @apiName checkBuyOrderInfo
     * @apiDescription 购买订单审核
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,TOO_EARLY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"TOO_MANY_REQUESTS"}
     */
    @RequestMapping("/tmscounter/checkBuyOrderInfo.htm")
    public ModelAndView checkBuyOrderInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        CounterOrderDto counterOrderDto = tmsCounterService.queryCounterOrder(dealAppNo, user.getInHBJG());
        CheckBuyInfoResultDto checkBuyInfoResultDto = tmsCounterService.checkBuyInfo(appDt, appTm, counterOrderDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<>(1);
        body.put("checkBuyInfoResult", checkBuyInfoResultDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * checkConfirm:(审核确认)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/checkconfirm.htm")
    public ModelAndView checkConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String checkResultForm = request.getParameter("checkResult");
        String checkedOrderForm = request.getParameter("checkedOrderForm");
        String checkType = request.getParameter("checkType");
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");
        CheckResultCmd checkResultCmd = JSON.parseObject(checkResultForm, CheckResultCmd.class);
        CounterOrderDto counterOrderDto = JSON.parseObject(checkedOrderForm, CounterOrderDto.class);
        //校验合法性
        validate(counterOrderDto, checkType, operatorInfoCmd);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterOrderDto);
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        BeanUtils.copyProperties(counterOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setMemo(checkResultCmd.getCheckFaildDesc());

        AuditingOrderFileCmd auditingOrderFileCmd = null;
        if (!StringUtils.isEmpty(materialinfoForm)) {
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
            tmsCounterOutService.validateOrderFileStatus(auditingOrderFileCmd);
        }
        if (auditingOrderFileCmd == null || StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())) {
            if (StringUtils.isNotEmpty(submitUncheckOrderDto.getMaterialId())) {
                if (CheckTypeEnum.CHECK_SUCC.getCode().equals(checkType)) {
                    AuditingOrderFileCmd newAuditingOrderFileCmd = new AuditingOrderFileCmd();
                    newAuditingOrderFileCmd.setOrderid(submitUncheckOrderDto.getMaterialId());
                    // 复审通过
                    newAuditingOrderFileCmd.setCurstat(OrderFileEnum.CURSTAT_SUCCESS.getCode());

                    // 校验预约单状态
                    validAppointDealStatus(submitUncheckOrderDto);
                    tmsCounterOutService.validateOrderFileStatus(newAuditingOrderFileCmd);
                } else if (CheckTypeEnum.CHECK_REJECT.getCode().equals(checkType)) {
                    AuditingOrderFileCmd newAuditingOrderFileCmd = new AuditingOrderFileCmd();
                    QueryOrderFileContext queryContext = new QueryOrderFileContext();
                    queryContext.setOrderid(submitUncheckOrderDto.getMaterialId());
                    QueryOrderFileDto queryOrderFileDto = tmsCounterOutService.queryOrderFile(queryContext, OpCheckNode.RE_CHECK.getCode());
                    List<FileinfoCmd> fileinfolist = new ArrayList<>();
                    BeanUtils.copyProperties(queryOrderFileDto, newAuditingOrderFileCmd);
                    //总部已审核
                    newAuditingOrderFileCmd.setCurstat(OrderFileEnum.CURSTAT_OPWAIT.getCode());
                    newAuditingOrderFileCmd.setOrderid(submitUncheckOrderDto.getMaterialId());
                    if (!CollectionUtils.isEmpty(queryOrderFileDto.getOrderinfolist())) {
                        for (OrderFileInfoDto orderFileInfoDto : queryOrderFileDto.getOrderinfolist()) {
                            FileinfoCmd fileinfoCmd = new FileinfoCmd();
                            BeanUtils.copyProperties(orderFileInfoDto, fileinfoCmd);
                            fileinfolist.add(fileinfoCmd);
                        }
                        newAuditingOrderFileCmd.setFileinfolist(fileinfolist);
                        tmsCounterOutService.auditingFile(operatorInfoCmd, newAuditingOrderFileCmd, submitUncheckOrderDto.getDealAppNo());
                    }

                }
            }
        }
        tmsCounterService.checkOrder(submitUncheckOrderDto, checkType, disInfoDto);

        if (auditingOrderFileCmd != null && !StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())) {
            if (CheckTypeEnum.CHECK_SUCC.getCode().equals(checkType)) {
                // 复审通过
                auditingOrderFileCmd.setCurstat(OrderFileEnum.CURSTAT_SUCCESS.getCode());
                tmsCounterOutService.auditingFile(operatorInfoCmd, auditingOrderFileCmd, submitUncheckOrderDto.getDealAppNo());
            } else if (CheckTypeEnum.CHECK_REJECT.getCode().equals(checkType)) {
                //总部已审核
                auditingOrderFileCmd.setCurstat(OrderFileEnum.CURSTAT_OPWAIT.getCode());
                tmsCounterOutService.auditingFile(operatorInfoCmd, auditingOrderFileCmd, submitUncheckOrderDto.getDealAppNo());
            }
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * @param submitUncheckOrderDto
     * @return void
     * @Description 校验预约单状态
     * <AUTHOR>
     * @Date 2019/12/14 13:32
     **/
    private void validAppointDealStatus(SubmitUncheckOrderDto submitUncheckOrderDto) throws Exception {
        if (StringUtils.isNotEmpty(submitUncheckOrderDto.getMaterialId()) && StringUtils.isNotEmpty(submitUncheckOrderDto.getAppointmentDealNo())) {
            QueryPreBookListReqDto queryPreBookListReqDto = new QueryPreBookListReqDto();
            queryPreBookListReqDto.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            queryPreBookListReqDto.setPreId(submitUncheckOrderDto.getAppointmentDealNo());
            QueryPreBookListRespDto queryPreBookListRespDto = tmsCounterOutService.queryPreBookList(queryPreBookListReqDto);
            if (queryPreBookListRespDto == null || CollectionUtils.isEmpty(queryPreBookListRespDto.getPreBookList())) {
                throw new TmsCounterException(TmsCounterResultEnum.HOGH_APPOINT_IS_NOT_EXIST_ERROR);
            } else {
                QueryPreBookListRespDto.PreBookListDto preBookListDto = queryPreBookListRespDto.getPreBookList().get(0);
                if (preBookListDto != null && !PreBookStateEnum.CONFIRM.getCode().equals(preBookListDto.getPrebookState())) {
                    throw new TmsCounterException(TmsCounterResultEnum.HOGH_APPOINT_IS_NOT_EXIST_ERROR);
                }
            }
        }
    }


    /**
     * queryCheckOrder:(查询待审核订单)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午5:46:00
     */
    @RequestMapping("/tmscounter/querycheckorder.htm")
    public ModelAndView queryCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String idNo = request.getParameter("idNo");
        String txCode = request.getParameter("txCode");
        String productCode = request.getParameter("productCode");
        String productChannel = request.getParameter("productChannel");
        String checkFlag = request.getParameter("checkFlag");
        String owner = request.getParameter("owner");

        String pageNum = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        logger.info("custNo:{},idNo:{},txCode:{},pageNum:{},pageSize:{}", custNo, idNo, txCode, pageNum, pageSize);
        if (StringUtils.isEmpty(pageNum)) {
            pageNum = "1";
        }
        if (StringUtils.isEmpty(pageSize)) {
            pageSize = "50";
        }
        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        if (StringUtils.isEmpty(custNo)) {
            if (!StringUtils.isEmpty(idNo)) {
                queryCustBaseInfoReqDto.setIdNo(idNo);
                QueryCustBaseInfoRespDto qeryCustBaseInfoRespDto = tmsCounterService.queryCustBaseInfo(queryCustBaseInfoReqDto, null);
                custNo = qeryCustBaseInfoRespDto.getTxAcctNo();
            }
        }
        //查询未审核订单
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setFundCode(productCode);
        queryReqDto.setTxAcctNo(custNo);
        queryReqDto.setTxCode(txCode);
        queryReqDto.setCheckFlag(checkFlag);
        queryReqDto.setProductChannel(productChannel);
        // 我的交易申请查询需要绑定操作员号
        if (Constants.ROLE_OWNER.equals(owner)) {
            OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
            queryReqDto.setCreator(operatorInfoCmd.getOperatorNo());
        }

        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        queryReqDto.setIsHBJGAuth(user.getInHBJG() ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        CounterQueryOrderRespDto counterQueryOrderRespDto = tmsCounterService.counterQueryOrder(queryReqDto, null);
        List<CounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();
        // 脱敏
        if (!CollectionUtils.isEmpty(counterOrderList)) {
            for (CounterOrderDto dto : counterOrderList) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(3);
        body.put("counterOrderList", counterOrderList);
        body.put("totalPage", counterQueryOrderRespDto.getTotalPage());
        body.put("pageNum", counterQueryOrderRespDto.getPageNo());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * viewCheckOrder:(查询柜台订单)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年10月31日 下午2:20:42
     */
    @RequestMapping("/tmscounter/high/viewcheckorder.htm")
    public ModelAndView viewCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        UserAccountModel operationUser = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);

        String dealAppNo = request.getParameter("dealAppNo");
        String checkNode = request.getParameter("checkNode");

        if (StringUtils.isEmpty(dealAppNo)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        CounterOrderDto counterOrderDto = tmsCounterService.queryCounterOrder(dealAppNo, user.getInHBJG());
        CounterOrderFormDto counterOrderFormDto = null;
        if (counterOrderDto != null) {
            counterOrderFormDto = JSON.parseObject(counterOrderDto.getOrderFormMemo(), CounterOrderFormDto.class);
            // 赎回单的卡号转为明文
            if ("Z900012".equals(counterOrderDto.getTxCode())) {
                setBankAcctSensitive(counterOrderFormDto, counterOrderDto.getTxAcctNo(), counterOrderDto.getInDisCode());
            }
        }

        // 没有查询到订单
        if (counterOrderDto == null) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_DEAL_NOT_EXIST);
        }
        //用户信息
        String versionType = getVersionType(counterOrderDto.getInvstType());
        List<CustInfoDto> custInfofiList = getCustInfo(counterOrderDto, versionType);

        // 转入方脱敏
        setAcctSensitiveInfo(counterOrderDto, operationUser);

        //已使用预约信息
        List<CustomerAppointmentInfoDto> appointList = null;
        if (TX_CODE_SET.contains(counterOrderDto.getTxCode())) {
            //只有购买赎回查询预约信息
            appointList = getAppointList(counterOrderDto.getTxAcctNo(), counterOrderDto.getAppointmentDealNo());
            // 认申购赎回需要实时获取开放日历
            updateAppointDtInfo(counterOrderDto, counterOrderFormDto);
        }

        // 查询CRM线上化资料
        QueryOrderFileDto queryOrderFileDto = new QueryOrderFileDto();
        if (StringUtils.isNotEmpty(counterOrderDto.getMaterialId())) {
            QueryOrderFileContext queryContext = new QueryOrderFileContext();
            queryContext.setOrderid(counterOrderDto.getMaterialId());
            queryOrderFileDto = tmsCounterOutService.queryOrderFile(queryContext, checkNode);
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(5);
        //柜台订单快照
        body.put("counterOrder", counterOrderFormDto);
        //柜台订单
        body.put("counterOrderDto", counterOrderDto);
        //客户信息
        body.put("custInfofiList", custInfofiList);
        //投顾预约信息
        body.put("appointList", appointList);
        //投顾预约信息
        body.put("orderFile", queryOrderFileDto);

        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 更新预约日历信息
     */
    private void updateAppointDtInfo(CounterOrderDto counterOrderDto, CounterOrderFormDto counterOrderFormDto) throws Exception {
        // 没有表单快照,不需要处理
        if (counterOrderFormDto == null) {
            return;
        }
        // 实时查询日历信息
        String busiType = TxCodes.HIGH_COUNTER_PURCHASE.equals(counterOrderDto.getTxCode()) ? BusiTypeEnum.BUY.getCode() : BusiTypeEnum.SELL.getCode();
        HighProductAppointmentInfoModel appointmentInfoModel = highProductAppointService.queryTradeProductAppointInfo(counterOrderDto.getFundCode(), counterOrderDto.getAppDt(), counterOrderDto.getAppTm(), busiType);
        // 没有日历,就不更新了
        if (appointmentInfoModel == null) {
            return;
        }
        CounterOrderFormDto.CounterProductAppointmentInfoBean counterProductAppointmentInfoBean = counterOrderFormDto.getCounterProductAppointmentInfoBean();
        if (counterProductAppointmentInfoBean != null) {
            counterProductAppointmentInfoBean.setAppointId(appointmentInfoModel.getAppointId());
            counterProductAppointmentInfoBean.setAppointStartDt(appointmentInfoModel.getAppointStartDt());
            counterProductAppointmentInfoBean.setApponitEndDt(appointmentInfoModel.getApponitEndDt());
            counterProductAppointmentInfoBean.setAppointStartDt(appointmentInfoModel.getAppointStartDt());
            counterProductAppointmentInfoBean.setAppointStartTm(appointmentInfoModel.getAppointStartTm());
            counterProductAppointmentInfoBean.setApponitEndTm(appointmentInfoModel.getApponitEndTm());
            counterProductAppointmentInfoBean.setMidProductId(appointmentInfoModel.getProductId());
            counterProductAppointmentInfoBean.setStrategyId(appointmentInfoModel.getStrategyId());
            counterProductAppointmentInfoBean.setShareClass(appointmentInfoModel.getShareClass());
            counterProductAppointmentInfoBean.setmBusiCode(appointmentInfoModel.getmBusiCode());
            counterProductAppointmentInfoBean.setOpenEndDt(appointmentInfoModel.getOpenEndDt());
            counterProductAppointmentInfoBean.setOpenEndTm(appointmentInfoModel.getOpenEndTm());
            counterProductAppointmentInfoBean.setOpenStartDt(appointmentInfoModel.getOpenStartDt());
            counterProductAppointmentInfoBean.setOpenStartTm(appointmentInfoModel.getOpenStartTm());
            counterProductAppointmentInfoBean.setPayDeadlineDtm(appointmentInfoModel.getPayDeadlineDtm());
            counterProductAppointmentInfoBean.setPayRatio(appointmentInfoModel.getPayRatio());
        }else {
            counterProductAppointmentInfoBean=new CounterOrderFormDto.CounterProductAppointmentInfoBean();
            counterProductAppointmentInfoBean.setAppointId(appointmentInfoModel.getAppointId());
            counterProductAppointmentInfoBean.setAppointStartDt(appointmentInfoModel.getAppointStartDt());
            counterProductAppointmentInfoBean.setApponitEndDt(appointmentInfoModel.getApponitEndDt());
            counterProductAppointmentInfoBean.setAppointStartDt(appointmentInfoModel.getAppointStartDt());
            counterProductAppointmentInfoBean.setAppointStartTm(appointmentInfoModel.getAppointStartTm());
            counterProductAppointmentInfoBean.setApponitEndTm(appointmentInfoModel.getApponitEndTm());
            counterProductAppointmentInfoBean.setMidProductId(appointmentInfoModel.getProductId());
            counterProductAppointmentInfoBean.setStrategyId(appointmentInfoModel.getStrategyId());
            counterProductAppointmentInfoBean.setShareClass(appointmentInfoModel.getShareClass());
            counterProductAppointmentInfoBean.setmBusiCode(appointmentInfoModel.getmBusiCode());
            counterProductAppointmentInfoBean.setOpenEndDt(appointmentInfoModel.getOpenEndDt());
            counterProductAppointmentInfoBean.setOpenEndTm(appointmentInfoModel.getOpenEndTm());
            counterProductAppointmentInfoBean.setOpenStartDt(appointmentInfoModel.getOpenStartDt());
            counterProductAppointmentInfoBean.setOpenStartTm(appointmentInfoModel.getOpenStartTm());
            counterProductAppointmentInfoBean.setPayDeadlineDtm(appointmentInfoModel.getPayDeadlineDtm());
            counterProductAppointmentInfoBean.setPayRatio(appointmentInfoModel.getPayRatio());
            counterOrderFormDto.setCounterProductAppointmentInfoBean(counterProductAppointmentInfoBean);
        }

    }

    /**
     * 设置订单明细的卡号为明文
     *
     * @param counterOrderFormDto
     * @param txAcctNo
     * @param disCode
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/6/25 15:24
     * @since JDK 1.8
     */
    private void setBankAcctSensitive(CounterOrderFormDto counterOrderFormDto, String txAcctNo, String disCode) {
        if (counterOrderFormDto != null) {
            List<CounterOrderFormDto.CounterCustBalanceVolDtlBean> dtlBeanList = counterOrderFormDto.getDtlBeanList();
            if (dtlBeanList != null) {
                QueryAllBankCardSensitiveInfoContext ctx = new QueryAllBankCardSensitiveInfoContext();
                ctx.setTxAcctNo(txAcctNo);
                ctx.setDisCode(disCode);
                QueryAllBankCardSensitiveInfoResult result = queryAllBankAcctSensitiveInfoOuterService.queryAllBankAcctSensitiveInfo(ctx);
                Map<String, String> bankMap = new HashMap<>(result.getCustBankModelList().size());
                for (CustAllBankSensitiveModel model : result.getCustBankModelList()) {
                    bankMap.put(model.getCpAcctNo(), model.getBankAcct());
                }

                for (CounterOrderFormDto.CounterCustBalanceVolDtlBean bean : dtlBeanList) {
                    String bankAcct = bankMap.get(bean.getCpAcctNo());
                    if (StringUtils.isNotBlank(bankAcct)) {
                        bean.setBankAcctNo(bankAcct);
                    }
                }
            }
        }
    }



    /**
     * 获取预约单列表
     *
     * @return
     * @author: jiaheng.gu
     * @date: 2022/1/18 14:52
     * @since JDK 1.8
     */
    private List<CustomerAppointmentInfoDto> getAppointList(String custNo, String preId) throws Exception {
        QueryPreBookListRequest queryPreBookListRequest = new QueryPreBookListRequest();
        queryPreBookListRequest.setTxAcctNo(custNo);
        queryPreBookListRequest.setPreId(preId);
        Page cpage = new Page();
        cpage.setPerPage(1000);
        cpage.setPage(1);

        CustomerAppointmentInfoRespDto customerAppointmentInfoRespDto = tmsCounterOutService.queryAppointmentInfo(queryPreBookListRequest, cpage);
        if (customerAppointmentInfoRespDto != null) {
            return customerAppointmentInfoRespDto.getCustomerAppointmentInfoDtoList();
        }
        return null;
    }

    /**
     * getVersionType:(根据客户类型获取风险问卷版本)
     *
     * @param investType
     * @return 0-零售 1-高端
     * <AUTHOR>
     * @date 2018年2月11日 下午4:28:38
     */
    private String getVersionType(String investType) {
        if (InvstTypeEnum.INST.getCode().equals(investType)) {
            return VERSION_TYPE_GONGMU;
        } else {
            return VERSION_TYPE_HIGH;
        }
    }

    private String getHboneNoByTxAcctNo(String txAcctNo) {
        return tmsCounterOutService.queryHboneNoByTxAccountNo(txAcctNo);
    }

    /**
     * 构造客户信息
     *
     * @param counterOrderDto
     * @param versionType
     * @return
     * @throws Exception
     */
    private List<CustInfoDto> getCustInfo(CounterOrderDto counterOrderDto, String versionType) throws Exception {
        String txAcctNo = counterOrderDto.getTxAcctNo();
        String disCode = counterOrderDto.getDisCode();
        String selectDisCode = counterOrderDto.getDisCode();
        String cpAcctNo = counterOrderDto.getCpAcctNo();

        List<CustInfoDto> custInfoDtoList = new ArrayList<CustInfoDto>();
        if (!StringUtils.isEmpty(txAcctNo)) {
            if (StringUtils.isEmpty(disCode)) {
                disCode = "HB000A001";
            }
            // 查询客户信息列表
            QueryCustInfoAndTxAcctForCounterResult queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);
            // 查询客户信息（敏感信息已脱敏）
            QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
            queryAllCustInfoContext.setDisCode(disCode);
            queryAllCustInfoContext.setTxAcctNo(txAcctNo);
            queryAllCustInfoContext.setCpAcctNo(cpAcctNo);
            QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);
            // 查询敏感信息
            QueryCustBankCardSensitiveInfoContext ctx = new QueryCustBankCardSensitiveInfoContext();
            ctx.setDisCode(disCode);
            ctx.setTxAcctNo(txAcctNo);
            ctx.setCpAcctNo(cpAcctNo);
            QueryCustBankCardSensitiveInfoResult realCustInfo = queryCustBankAcctSensitiveInfoOuterService.queryCustBankAcctSensitiveInfo(ctx);

            CustInfoBean custInfoBean = null;
            if (queryAllCustInfoResult != null) {
                custInfoBean = queryAllCustInfoResult.getCustInfo();
            }

            String hbOneNo = getHboneNoByTxAcctNo(txAcctNo);

            // 查询客户风险等级
            QueryCustRiskSurveyResult queryCustRiskSurveyResult = null;
            if (custInfoBean != null) {
                if (VERSION_TYPE_GONGMU.equals(versionType) || InvstTypeEnum.INST.getCode().equals(custInfoBean.getInvstType())) {
                    queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurvey(txAcctNo, VERSION_TYPE_GONGMU, disCode);
                } else {
                    queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurvey(txAcctNo, VERSION_TYPE_HIGH, disCode);
                }
                counterOrderDto.setCustName(realCustInfo.getCustName());
                counterOrderDto.setIdNo(realCustInfo.getIdNo());
            }
            counterOrderDto.setBankAcct(realCustInfo.getBankAcct());
            //查询客户基本信息
            BusinessAspect.setTradeCommomParams(disCode, null);
            QueryCustInfoResult queryCustInfoResult = queryCustInfoOuterService.queryCustInfoPlaintext(txAcctNo);
            // 回款协议
            String collectProtocolMethod = null;
            // 客户资管投资承诺书签署状态 1-签署；0-未签署
            String fundFlag = null;
            //  客户私募投资承诺书签署状态 1-签署；0-未签署
            String signFlag = null;
            if (queryCustInfoResult != null) {
                collectProtocolMethod = queryCustInfoResult.getCollectProtocolMethod();
                fundFlag = queryCustInfoResult.getFundFlag();
                signFlag = queryCustInfoResult.getSignFlag();
            }
            if (queryCustInfoAndTxAcctForCounterResult != null) {
                List<DisAcTxAcctBean> disAcTxAcctBeanList = queryCustInfoAndTxAcctForCounterResult.getDisAcTxAcctBeanList();
                if (!CollectionUtils.isEmpty(disAcTxAcctBeanList)) {
                    CustInfoDto custInfoDto = null;
                    for (DisAcTxAcctBean disAcTxAcctBean : disAcTxAcctBeanList) {
                        custInfoDto = new CustInfoDto();
                        BeanUtils.copyProperties(queryCustInfoAndTxAcctForCounterResult, custInfoDto);
                        custInfoDto.setCollectProtocolMethod(collectProtocolMethod);
                        custInfoDto.setSignFlag(signFlag);
                        custInfoDto.setFundFlag(fundFlag);
                        custInfoDto.setHboneNo(hbOneNo);
                        if (custInfoBean != null) {
                            // 投资者类型
                            custInfoDto.setInvestorType(custInfoBean.getQualificationType());
                            // 客户类型
                            custInfoDto.setInvstType(custInfoBean.getInvstType());
                        }

                        custInfoDto.setDisCode(disAcTxAcctBean.getDisCode());
                        custInfoDtoList.add(custInfoDto);

                        if (queryCustRiskSurveyResult != null) {
                            custInfoDto.setCustRiskLevel(queryCustRiskSurveyResult.getCustRiskLevel());
                            custInfoDto.setRiskSurveyDt(queryCustRiskSurveyResult.getRiskSurveyDt());
                            if (null != queryCustRiskSurveyResult.getRiskExpireDate()
                                    && new Date().compareTo(queryCustRiskSurveyResult.getRiskExpireDate()) > 0) {
                                custInfoDto.setOverdue(true);
                            } else {
                                custInfoDto.setOverdue(false);
                            }
                        } else {
                            custInfoDto.setCustRiskLevel(null);
                            custInfoDto.setRiskSurveyDt(null);
                            custInfoDto.setOverdue(true);
                        }
                    }
                }
            }

        }
        List<CustInfoDto> custInfoResultList = null;
        if (!StringUtils.isEmpty(selectDisCode)) {
            if (!CollectionUtils.isEmpty(custInfoDtoList)) {
                for (CustInfoDto custInfoDto : custInfoDtoList) {
                    if (selectDisCode.equals(custInfoDto.getDisCode())) {
                        custInfoResultList = new ArrayList<CustInfoDto>();
                        custInfoResultList.add(custInfoDto);
                        break;
                    }
                }
            }
        } else {
            custInfoResultList = custInfoDtoList;
        }
        return custInfoResultList;
    }

    /**
     * 转入方脱敏
     *
     * @param counterOrderDto
     * @param operationUser
     */
    private void setAcctSensitiveInfo(CounterOrderDto counterOrderDto, UserAccountModel operationUser) {
        String txCode = counterOrderDto.getTxCode();
        if (TxCodes.HIGH_COUNTER_NOTRADE_OVERACCOUNT.equals(txCode)) {
            String inDisCode = null;
            if (StringUtils.isNotEmpty(counterOrderDto.getInDisCode())) {
                inDisCode = counterOrderDto.getInDisCode();
            } else {
                inDisCode = DisCodeEnum.HM.getCode();
            }
            String inTxAcctNo = counterOrderDto.getInTxAcctNo();
            String inCpAcctNo = counterOrderDto.getInCpAcctNo();

            // 查询敏感信息
            QueryCustBankCardSensitiveInfoContext ctx = new QueryCustBankCardSensitiveInfoContext();
            ctx.setDisCode(inDisCode);
            ctx.setTxAcctNo(inTxAcctNo);
            ctx.setCpAcctNo(inCpAcctNo);
            QueryCustBankCardSensitiveInfoResult realCustInfo = queryCustBankAcctSensitiveInfoOuterService.queryCustBankAcctSensitiveInfo(ctx);
            if (realCustInfo != null && StringUtils.isNotEmpty(realCustInfo.getIdNo())) {
                counterOrderDto.setInIdNo(realCustInfo.getIdNo());
            }

//            sensitiveInfoAccessLog(operationUser, inTxAcctNo, inCpAcctNo);
        }
    }
    /**
     * 敏感信息访问留痕
     */
//    private void sensitiveInfoAccessLog(UserAccountModel operationUser, String custNo, String custBankId){
//        try {
//            JSONObject json = new JSONObject();
//            json.put("time", DateUtil.formatNowDate(DateUtil.DEFAULT_DATEPATTERN));
//            json.put("operation", operationUser.getUserName());
//           // json.put("operationId", operationUser.getUserId());
//            json.put("custNo", custNo);
//            json.put("custBankId", custBankId);
//            sensitiveInfoAccessLog.info(json);
//        } catch (Exception e) {
//            logger.error("sensitiveInfoAccessLog error:", e);
//        }
//    }

    /**
     * getCheckActionType:(获取操作类型)
     *
     * @return
     * <AUTHOR>
     * @date 2018年3月8日 下午5:14:17
     */
    private CheckActionTypeEnum getCheckActionType(String checkType) {

        //审核
        if (isCheck(checkType)) {
            return CheckActionTypeEnum.ACTION_CHECK;
        }

        //修改
        if (isModify(checkType)) {
            return CheckActionTypeEnum.ACTION_MODIFY;
        }
        return null;
    }

    /**
     * isCheck:(是否审核)
     *
     * @param checkType
     * @return
     * <AUTHOR>
     * @date 2018年3月8日 下午5:11:58
     */
    private boolean isCheck(String checkType) {

        return CheckTypeEnum.CHECK_REJECT.getCode().equals(checkType) || CheckTypeEnum.CHECK_SUCC.getCode().equals(checkType) || CheckTypeEnum.CHECK_FAILD.getCode().equals(checkType);
    }

    /**
     * isModify:(是否修改)
     *
     * @param checkType
     * @return
     * <AUTHOR>
     * @date 2018年3月8日 下午5:13:05
     */
    private boolean isModify(String checkType) {

        return CheckTypeEnum.CHECK_CANCEL.getCode().equals(checkType) || CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType);
    }

}
