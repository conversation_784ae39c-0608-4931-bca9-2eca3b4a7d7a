
.cover2{
	position: fixed;
	/*首先将位置固定住*/
	top:0;
	right:0;
	bottom:0;
	left:0;
	/*上下左右设置都为0*/
	background-color: rgba(0,0,0,0.2);

	z-index:99;
	/*指定一个元素的堆叠顺序,数值越大,表示在越上边*/
}
.modal2{
	width:700px;
	height:400px;
	position: absolute;
	top:50%;
	left:50%;
	margin-left: -350px;
	margin-top: -200px;
	background-color: white;
	z-index: 100;
	/*将x的位置移动*/
}
.close2{
	float: right;
	/*在这里将x移动到右上角*/
	margin-right: 15px;
	margin-top: 10px;
	font-size: 16px;
	padding:10px;
	cursor:pointer;
}
.hide2{
	display: none;
	/*表示不显示*/
}
video{
	margin: 0 auto;
	text-align: center;
	display: block;
	width: 100%;
	height: 100%;
	background: #fff;
}