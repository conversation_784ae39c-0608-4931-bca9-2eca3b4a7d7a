/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;

import java.util.List;

/**
 * @description:(TODO 请在此添加描述)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年7月17日 下午2:57:23
 * @since JDK 1.6
 */ 
public class CustomerAppointmentInfoRespD<PERSON> extends BaseResponseDto{
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 4870246973212134585L;

    private List<CustomerAppointmentInfoDto> customerAppointmentInfoDtoList;

    public List<CustomerAppointmentInfoDto> getCustomerAppointmentInfoDtoList() {
        return customerAppointmentInfoDtoList;
    }

    public void setCustomerAppointmentInfoDtoList(List<CustomerAppointmentInfoDto> customerAppointmentInfoDtoList) {
        this.customerAppointmentInfoDtoList = customerAppointmentInfoDtoList;
    }
}
