/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.counter.controller;


import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.trade.artificialgenhbassetcert.ArtificialGenHBAssetCertFacade;
import com.howbuy.acccenter.facade.trade.artificialgenhbassetcert.ArtificialGenHBAssetCertRequest;
import com.howbuy.acccenter.facade.trade.artificialgenhbassetcert.ArtificialGenHBAssetCertResponse;
import com.howbuy.cc.center.assetservice.centralization.request.QueryCurrentBalanceWithCacheRequest;
import com.howbuy.cc.center.assetservice.centralization.response.QueryCurrentBalanceWithCacheResponse;
import com.howbuy.cc.center.assetservice.centralization.service.QueryCurrentBalanceWithCacheService;
import com.howbuy.cc.center.express.asset.domain.SecuritiesAssetIndexDomain;
import com.howbuy.cc.center.express.asset.domain.StockAssetIndexDomain;
import com.howbuy.cc.center.feature.asset.domain.AssetCertificateDomain;
import com.howbuy.cc.center.feature.asset.domain.AssetCertificateFileDomain;
import com.howbuy.cc.center.feature.asset.request.QueryAssetCertificateListRequest;
import com.howbuy.cc.center.feature.asset.response.QueryAssetCertificateListResponse;
import com.howbuy.cc.center.feature.asset.service.QueryAssetCertificateListService;
import com.howbuy.ccms.independent.CcmsMapper;
import com.howbuy.tms.common.ccms.CCMSRegister;
import com.howbuy.tms.common.outerservice.common.TradeFacadeUtils;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.counter.config.TmsCounterNacosConfig;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.enums.CertStyleEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.util.FileSdkPathInfo;
import com.howbuy.tms.counter.util.FileSdkUtil;
import com.howbuy.tms.counter.util.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(查询客户资产)
 * @date 2018年2月23日 下午4:58:42
 * @since JDK 1.6
 */
@Controller
public class QueryCustAssertController implements CCMSRegister {
    private Logger logger = LogManager.getLogger(QueryCustAssertController.class);
    @Autowired
    private QueryCurrentBalanceWithCacheService queryCurrentBalanceWithCacheService;

    @Autowired
    private ArtificialGenHBAssetCertFacade artificialGenHBAssetCertFacade;

    @Autowired
    private QueryAssetCertificateListService queryAssetCertificateListService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    /**
     * 等待时间1s
     */
    private final static long WAIT_TIME = 1000L;

    /**
     * 无章的电子签名后缀
     */
    private final static String NO_SEAL_SUFF = "noSeal.pdf";

    /**
     * 有章有底纹的电子签名后缀
     */
    private final static String SHADING_SEAL_SUFF = "shading.pdf";

    private final static String SHADING_NO_SEAL_SUFF = "shading_noSeal.pdf";
    /**
     * 英文版有章有底纹的后缀
     */
    private final static String ENG_SHADING_SEAL_SUFF = "engShading.pdf";
    /**
     * 中台证券类总资产证明
     */
    private final static String MID_FUND_ASSERT_SUFF = "noDetail_noSeal.pdf";

    /**
     * CRM证券类总资产证明
     */
    private final static String CRM_FUND_ASSERT_SUFF = "noDetailShading.pdf";


    /**
     * 英文版无章有底纹的后缀
     */
    private final static String ENG_NO_SEAL_SUFF = "eng_noSeal.pdf";

    /**
     * 查询最大次数
     */
    private final int MAX_CHECK_NUM = 10;

    
    @Autowired
    private TmsCounterNacosConfig tmsCounterNacosConfig;

    /**
     * queryCounterTrade:(查询客户资产)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年3月28日 上午10:26:58
     */
    @SuppressWarnings("deprecation")
    @RequestMapping("tmscounter/high/querycustassert.htm")
    public ModelAndView queryCustAssert(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String disCodes = request.getParameter("disCodeList");
        String assetEndDt = request.getParameter("assetEndDt");
        if (!StringUtils.isBlank(assetEndDt)) {
            assetEndDt = DateUtils.formatToString(DateUtils.formatToDate(assetEndDt, DateUtils.YYYY_MM_DD), DateUtils.YYYYMMDD);
        }
        List<String> disCodeList = new ArrayList<>();
        disCodeList = JSON.parseArray(disCodes, String.class);
        if (CollectionUtils.isEmpty(disCodeList)) {
            disCodeList.add(disCode);
        }
        logger.info("QueryCustAssertController-queryCustAssert,查询客户资产,custNo={},disCodeList={},assetEndDt={}", custNo, disCodeList, assetEndDt);
        // 参数校验
        paramCheck(custNo, assetEndDt);

        //查询客户号对应一帐通号
        String hbOneNo = tmsCounterOutService.queryHboneNoByTxAccountNo(custNo);
        if (StringUtils.isEmpty(hbOneNo)) {
            throw new TmsCounterException(TmsCounterResultEnum.HBONENO_IS_NOT_EXIT);
        }

        //查询客户资产
        QueryCurrentBalanceWithCacheRequest queryCurrentBalanceWithCacheRequest = new QueryCurrentBalanceWithCacheRequest();
        queryCurrentBalanceWithCacheRequest.setHboneNo(hbOneNo);
        queryCurrentBalanceWithCacheRequest.setDisCodeList(disCodeList);
        queryCurrentBalanceWithCacheRequest.setAssetCutOffDate(assetEndDt);
        QueryCurrentBalanceWithCacheResponse queryCurrentBalanceWithCacheResponse = queryCurrentBalanceWithCacheService.execute(queryCurrentBalanceWithCacheRequest);

        List<StockAssetIndexDomain> stockAssetList = null;
        List<SecuritiesAssetIndexDomain> securitiesAssetList = null;
        //流水号
        String serialNo = null;
        if (queryCurrentBalanceWithCacheResponse.isSuccessful()) {
            //股权类资产
            stockAssetList = queryCurrentBalanceWithCacheResponse.getStockAssetList();
            //证券类资产列表
            securitiesAssetList = queryCurrentBalanceWithCacheResponse.getSecuritiesAssetList();
            serialNo = queryCurrentBalanceWithCacheResponse.getSerialNo();
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>();
        body.put("stockAssetList", stockAssetList);
        body.put("securitiesAssetList", securitiesAssetList);
        body.put("hboneNo", hbOneNo);
        body.put("serialNo", serialNo);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private void paramCheck(String custNo, String assetEndDt) {
        if (StringUtils.isEmpty(custNo)) {
            logger.error("custNo is null");
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        if (!StringUtils.isBlank(assetEndDt)) {
            String QUERY_BEGIN_DT = tmsCounterNacosConfig.getQUERY_BEGIN_DT();
            // 1.资产截止日期必须是大于20230101
            logger.info("paramCheck-配置资产截止日期,QUERY_BEGIN_DT={}", QUERY_BEGIN_DT);
            if (QUERY_BEGIN_DT.compareTo(assetEndDt) > 0) {
                throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "资产截止日期不能选择小于" + QUERY_BEGIN_DT);
            }
            // 2.必须小于系统时间
            String currentDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
            if (currentDt.compareTo(assetEndDt) < 0) {
                throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "资产截止日期不能大于当前时间");
            }
        }
    }

    /**
     * generateCustAssert:(生成客户资产证明)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月24日 上午10:01:42
     */
    @RequestMapping("tmscounter/high/generatecustassert.htm")
    public ModelAndView generateCustAssert(HttpServletRequest request, HttpServletResponse response) throws Exception {

        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);

        //一帐通号
        String hboneNo = request.getParameter("hboneNo");
        //流水号
        String serialNo = request.getParameter("serialNo");
        //证券类
        String secutitiesRowKeys = request.getParameter("secutitiesRowKeys");
        //股权类
        String stockAssetRowKeys = request.getParameter("stockAssetRowKeys");
        //资产证明样式:1-无底纹；2-有底纹；3-无底纹中英文版；4-有底纹中英文版
        String certStyle = request.getParameter("showShadingAndQRCodeFlag");
        //分销机构代码
        String disCode = request.getParameter("disCode");
        //分销机构代码列表
        String disCodes = request.getParameter("disCodeList");
        List<String>  disCodeList = JSON.parseArray(disCodes, String.class);
        if (CollectionUtils.isEmpty(disCodeList)) {
            disCodeList.add(disCode);
        }
        logger.error("disCodeList={}", disCodeList.toString());

        List<String> securitiesAssetRowKeyList = null;
        if (StringUtils.isNotEmpty(secutitiesRowKeys)) {
            securitiesAssetRowKeyList = Arrays.asList(secutitiesRowKeys.split(","));

        }

        List<String> stockAssetRowKeyList = null;
        if (StringUtils.isNotEmpty(stockAssetRowKeys)) {
            stockAssetRowKeyList = Arrays.asList(stockAssetRowKeys.split(","));
        }

        //生成好买客户资产证明
        ArtificialGenHBAssetCertRequest artificialGenHbAssetCertRequest = new ArtificialGenHBAssetCertRequest();
        //一帐通号
        artificialGenHbAssetCertRequest.setHboneNo(hboneNo);
        //流水号
        artificialGenHbAssetCertRequest.setSerialNo(serialNo);
        //证券类
        artificialGenHbAssetCertRequest.setSecuritiesAssetRowKeyList(securitiesAssetRowKeyList);
        //股票类
        artificialGenHbAssetCertRequest.setStockAssetRowKeyList(stockAssetRowKeyList);
        // 资产证明样式:1-无底纹；2-有底纹；3-无底纹中英文版；4-有底纹中英文版,5-指定截止日期的无底纹无明细,6--指定截止日期的有底纹无明细
        artificialGenHbAssetCertRequest.setCertStyle(certStyle);
        artificialGenHbAssetCertRequest.setDisCodeList(disCodeList);
        //操作员信息
        artificialGenHbAssetCertRequest.setOperator(operatorInfoCmd.getOperatorNo());
        //操作来源
        artificialGenHbAssetCertRequest.setOutletCode("W20170215");
        ArtificialGenHBAssetCertResponse resp = artificialGenHBAssetCertFacade.execute(artificialGenHbAssetCertRequest);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        TradeFacadeUtils.isSuccessWithThrowException(resp);
        //好买资产证明文件
        List<String> fileNames = getAssetCertificateFileName(hboneNo, certStyle);
        List<String> absoluteFilePathList = new ArrayList<>(fileNames.size());
        for (String fileName : fileNames) {
            FileSdkPathInfo fileSdkPathInfo = FileSdkUtil.convertUrlToFileSdkPathInfo(fileName, FilePathStoreBusinessCodeConfig.CC_CERTIFICATE, "certificate_file");
            absoluteFilePathList.add(FileSdkUtil.getAbsolutePath(fileSdkPathInfo));
        }
        logger.info("QueryCustAssertController|generateCustAssert|fileNames:{}", JSON.toJSONString(absoluteFilePathList));

        Map<String, Object> body = new HashMap<>();
        body.put("fileName", absoluteFilePathList);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * exportCustAssert:(导出客户资产证明)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月24日 上午10:25:45
     */
    @RequestMapping("tmscounter/high/exportcustassert.htm")
    public ModelAndView exportCustAssert(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //一帐通号
        String fileName = request.getParameter("fileName");
        FileSdkPathInfo fileSdkPathInfo = FileSdkUtil.convertUrlToFileSdkPathInfo(fileName, FilePathStoreBusinessCodeConfig.CC_CERTIFICATE, "cc_certificate_file");
        if (fileIsExit(1, fileSdkPathInfo)) {
            FileUtil.downLoadFile(fileSdkPathInfo, response);
        }

        return null;
    }

    private boolean fileIsExit(int count, FileSdkPathInfo fileSdkPathInfo) throws Exception {
        if (!FileSdkUtil.exists(fileSdkPathInfo)) {
            if (count > MAX_CHECK_NUM) {
                throw new TmsCounterException(TmsCounterResultEnum.ASSETCERTIFICATE_FILE_NOT_FIND);
            }

            count = count + 1;
            Thread.sleep(WAIT_TIME);

            return fileIsExit(count, fileSdkPathInfo);
        }

        return true;

    }

    private String getAssetCertificateAbsoluteFilePath(String filePath, String fileName) {

        logger.info("getAssetCertificateAbsoluteFilePath|filePath:{}, fileName:{}", filePath, fileName);
        return filePath + File.separator + fileName;
    }

    /**
     * getAssetCertificateFileNo:(获取客户好买资产证明文件)
     *
     * @param hboneNo
     * @return
     * <AUTHOR>
     * @date 2018年2月24日 上午10:38:48
     */
    @SuppressWarnings("deprecation")
    private List<String> getAssetCertificateFileName(String hboneNo, String certStyle) {
        logger.info("QueryCustAssertController|getAssetCertificateFileNo|hboneNo:{}, certStyle:{}", hboneNo, certStyle);

        QueryAssetCertificateListRequest queryAssetCertificateListRequest = new QueryAssetCertificateListRequest();
        //一帐通号
        queryAssetCertificateListRequest.setHboneNo(hboneNo);
        //资产证明来源  null全部，1外部，2好买
        queryAssetCertificateListRequest.setSource("2");
        //资产证明类型，1收入证明，2资产证明，null全部
        queryAssetCertificateListRequest.setCertificateType("2");
        //审核状态 null全部，1审核中，2全部通过，3部分通过，4全部不通过，5已作废，6已过期
        queryAssetCertificateListRequest.setAuditStatus("2");

        QueryAssetCertificateListResponse queryAssetCertificateListResponse = queryAssetCertificateListService.execute(queryAssetCertificateListRequest);
        if (queryAssetCertificateListResponse.isSuccessful()) {
            List<AssetCertificateDomain> certificateList = queryAssetCertificateListResponse.getCertificateList();
            if (CollectionUtils.isEmpty(certificateList)) {
                return null;
            }

            List<AssetCertificateFileDomain> fileList = certificateList.get(0).getFileList();
            //获取无章的资产证明
            List<String> files = getSealFile(fileList, certStyle);
            logger.info("QueryCustAssertController|getAssetCertificateFileNo|noSealFiles:{}", JSON.toJSONString(files));
            return files;
        }
        return null;
    }

    private List<String> getSealFile(List<AssetCertificateFileDomain> fileList, String certStyle) {
        if (CollectionUtils.isEmpty(fileList)) {
            return Collections.emptyList();
        }

        List<String> pathList = new ArrayList<>(fileList.size());
        for (AssetCertificateFileDomain assetCertificateFileDomain : fileList) {
            if ("1".equals(assetCertificateFileDomain.getAuditStatus())) {
                //审核状态 null未审核,1通过，2不通过
                if (assetCertificateFileDomain.getFilePath() != null
                        && isEqFilePath(assetCertificateFileDomain.getFilePath(), certStyle)) {
                    pathList.add(assetCertificateFileDomain.getFilePath());
                }

            }
        }

        return pathList;
    }

    private boolean isEqFilePath(String filePath, String certStyle) {
        if (CertStyleEnum.NO_SEAL_SUFF.getCode().equals(certStyle)) {
            return isNoSeal(filePath);
        } else if (CertStyleEnum.SHADING_SEAL_SUFF.getCode().equals(certStyle)) {
            return isShading(filePath);
        } else if (CertStyleEnum.ENG_NO_SEAL_SUFF.getCode().equals(certStyle)) {
            return isNoSealEng(filePath);
        } else if (CertStyleEnum.ENG_SHADING_SEAL_SUFF.getCode().equals(certStyle)) {
            return isShadingEng(filePath);
        } else if (CertStyleEnum.MID_FUND_ASSERT_SUFF.getCode().equals(certStyle)) {
            return isMidFundAssert(filePath);
        } else if (CertStyleEnum.CRM_FUND_ASSERT_SUFF.getCode().equals(certStyle)) {
            return isCrmFundAssert(filePath);
        }
        return false;
    }

    private boolean isNoSeal(String fileName) {
        return fileName.contains(NO_SEAL_SUFF) && !fileName.contains(SHADING_NO_SEAL_SUFF);
    }

    private boolean isShading(String fileName) {
        return fileName.contains(SHADING_SEAL_SUFF) && !fileName.contains(SHADING_NO_SEAL_SUFF);
    }

    private boolean isNoSealEng(String fileName) {
        return fileName.contains(ENG_NO_SEAL_SUFF) && !fileName.contains(SHADING_NO_SEAL_SUFF);
    }

    private boolean isShadingEng(String fileName) {
        return fileName.contains(ENG_SHADING_SEAL_SUFF) && !fileName.contains(SHADING_NO_SEAL_SUFF);
    }

    private boolean isMidFundAssert(String fileName) {
        return fileName.contains(MID_FUND_ASSERT_SUFF) && !fileName.contains(SHADING_NO_SEAL_SUFF);
    }

    private boolean isCrmFundAssert(String fileName) {
        return fileName.contains(CRM_FUND_ASSERT_SUFF) && !fileName.contains(SHADING_NO_SEAL_SUFF);
    }

}

