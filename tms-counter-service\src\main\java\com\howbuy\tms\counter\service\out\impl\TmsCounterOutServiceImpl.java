/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.service.out.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.common.AccBaseRequest;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoFacade;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoRequest;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.acccenter.facade.query.querydefaultcnaps.QueryDefaultCnapsFacade;
import com.howbuy.acccenter.facade.query.querydefaultcnaps.QueryDefaultCnapsRequest;
import com.howbuy.acccenter.facade.query.querydefaultcnaps.QueryDefaultCnapsResponse;
import com.howbuy.acccenter.facade.query.querydefaultcnaps.bean.CityBean;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneRequest;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneResponse;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileFacade;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileRequest;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileResponse;
import com.howbuy.acccenter.facade.trade.changecustcollectprotocolmethod.ChangeCustCollectProtocolMethodFacade;
import com.howbuy.acccenter.facade.trade.changecustcollectprotocolmethod.ChangeCustCollectProtocolMethodRequest;
import com.howbuy.common.page.Page;
import com.howbuy.common.page.PageResult;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.MfDate;
import com.howbuy.common.utils.MfDateTime;
import com.howbuy.crm.base.OrderFileEnum;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.interlayer.product.enums.portfolio.BusinessCodeEnum;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.trustreceipt.TrustReceiptProductModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.trustreceipt.TrustReceiptProductService;
import com.howbuy.tms.batch.facade.enums.WorkdayTypeEnum;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayFacade;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayRequest;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayResponse;
import com.howbuy.tms.common.ccms.CCMSRegister;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.OpCheckNode;
import com.howbuy.tms.common.enums.busi.PreBookUseFlagEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querybindcustbankcard.QueryBindCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querybindcustbankcard.QueryBindCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfoforprivate.QueryCustInfoForPrivateOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfoforprivate.QueryCustInfoForPrivateResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.crm.td.auditingorderfile.AuditingOrderFileContext;
import com.howbuy.tms.common.outerservice.crm.td.auditingorderfile.AuditingOrderFileOuterService;
import com.howbuy.tms.common.outerservice.crm.td.auditingorderfile.AuditingOrderFileResult;
import com.howbuy.tms.common.outerservice.crm.td.auditingorderfile.bean.FileinfoBean;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileOuterService;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileResult;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.bean.OrderFileDetailBean;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.bean.OrderFileInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.QueryFundInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundTaInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryportfolioproduct.QueryProductInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryportfolioproduct.bean.FundTAInfo;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.WorkDayInfo;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.HttpConnectionPoolUtil;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.aspect.BusinessAspect;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.FileinfoCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterConstants;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.config.TmsCounterNacosConfig;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.QueryPreBookListRespDto.PreBookListDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoRequest;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoResponse;
import com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacadeRequest;
import com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacadeResponse;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListRequest;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse.PreBookListBean;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(中台柜台外部服务)
 * @date 2017年4月12日 上午10:30:25
 * @since JDK 1.6
 */
@Service("tmsCounterOutService")
public class TmsCounterOutServiceImpl implements TmsCounterOutService, CCMSRegister {
    private final static Logger LOGGER = LoggerFactory.getLogger(TmsCounterOutServiceImpl.class);

    @Autowired
    @Qualifier("queryCustInfoAndTxAcctForCounterOuterService")
    private QueryCustInfoAndTxAcctForCounterOuterService queryCustInfoAndTxAcctForCounterOuterService;

    @Autowired
    @Qualifier("queryBindCustBankCardOuterService")
    private QueryBindCustBankCardOuterService queryBindCustBankCardOuterService;

    @Autowired
    @Qualifier("queryAllBankCardInfoOuterService")
    private QueryAllBankCardInfoOuterService queryAllBankCardInfoOuterService;

    @Autowired
    @Qualifier("queryHighProductOuterService")
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    @Qualifier("queryTradeDayOuterService")
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    @Qualifier("queryFundInfoOuterService")
    private QueryFundInfoOuterService queryFundInfoOuterService;

    @Autowired
    @Qualifier("queryProductInfoOuterService")
    private QueryProductInfoOuterService queryProductInfoOuterService;

    @Autowired
    @Qualifier("queryCustInfoOuterService")
    private QueryCustInfoOuterService queryCustInfoOuterService;

    @Autowired
    private HighProductService highProductService;

    @Autowired
    private QueryHzBuyOrderInfoFacade queryHzBuyOrderInfoFacade;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    @Qualifier("tmscounter.queryDefaultCnapsFacade")
    private QueryDefaultCnapsFacade queryDefaultCnapsFacade;

    @Autowired
    @Qualifier("tmscounter.queryTxAcctByHboneFacade")
    private QueryTxAcctByHboneFacade queryTxAcctByHboneFacade;

    @Autowired
    private QueryPreBookListFacade queryPreBookListFacade;

    @Autowired
    private ChangeCustCollectProtocolMethodFacade changeCustCollectProtocolMethodFacade;

    @Autowired
    @Qualifier("tmscounter.queryBankCardInfoFacade")
    private QueryBankCardInfoFacade queryBankCardInfoFacade;

    @Autowired
    @Qualifier("tmscounter.queryCustMobileFacade")
    private QueryCustMobileFacade queryCustMobileFacade;

    @Autowired
    @Qualifier("tmscounter.queryBankAcctSensitiveInfoFacade")
    private QueryBankAcctSensitiveInfoFacade queryBankAcctSensitiveInfoFacade;

    @Autowired
    @Qualifier("tmscounter.preBookService")
    private PreBookService preBookService;

    @Autowired
    private QueryFeeInfoFacade queryFeeInfoFacade;

    @Autowired
    private QueryCustInfoForPrivateOuterService queryCustInfoForPrivateOuterService;

    @Autowired
    @Qualifier("queryAccKycInfoOuterService")
    private QueryAccKycInfoOuterService queryAccKycInfoOuterService;

    @Autowired
    private TrustReceiptProductService trustReceiptProductService;

    @Autowired
    private QueryOrderFileOuterService queryOrderFileOuterService;

    @Autowired
    private AuditingOrderFileOuterService auditingOrderFileOuterService;

    @Autowired
    private QueryWorkdayFacade queryWorkdayFacade;

    @Autowired
    @Qualifier("queryAccHboneInfoOuterService")
    private QueryAccHboneInfoOuterService queryAccHboneInfoOuterService;

    @Autowired
    private TmsCounterNacosConfig tmsCounterNacosConfig;

    private static final String SUCCESS_CODE = "0000";

    @Override
    public QueryCustInfoResult queryCustInfo(String disCode, String txAcctNo) {
        BusinessAspect.setTradeCommomParams(disCode, null);
        return queryCustInfoOuterService.queryCustInfoPlaintext(txAcctNo);
    }

    @Override
    public QueryCustInfoAndTxAcctForCounterResult queryAllCustInfo(String txAcctNo, String disCode) throws Exception {
        BusinessAspect.setTradeCommomParams(null, null);
        QueryCustInfoAndTxAcctForCounterContext queryCustInfoAndTxAcctForCounterContext = new QueryCustInfoAndTxAcctForCounterContext();
        queryCustInfoAndTxAcctForCounterContext.setCustNo(txAcctNo);
        queryCustInfoAndTxAcctForCounterContext.setDisCode(disCode);
        queryCustInfoAndTxAcctForCounterContext.setTxChannel(TxChannelEnum.COUNTER.getCode());
        return queryCustInfoAndTxAcctForCounterOuterService
                .queryCustInfoPlaintext(queryCustInfoAndTxAcctForCounterContext);
    }

    @Override
    public List<QueryBindCustBankCardResult> queryCustBanks(String txAcctNo, String disCode, String outletCode) {
        BusinessAspect.setTradeCommomParams(disCode, outletCode);
        List<QueryBindCustBankCardResult> bankList = queryBindCustBankCardOuterService.queryBindBankCardsPlaintext(txAcctNo);
        return getPerfectCustBanks(bankList);
    }

    @Override
    public QueryAllBankCardInfoResult queryAllBankCardInfo(String txAcctNo, String disCode) {
        BusinessAspect.setTradeCommomParams(disCode, null);
        QueryAllBankCardInfoContext ctx = new QueryAllBankCardInfoContext();
        ctx.setTxAcctNo(txAcctNo);
        ctx.setDisCode(disCode);
        if (DefaultParamsConstant.DEFULT_DIS_CODE.equals(disCode)) {
            ctx.setDisCode(DefaultParamsConstant.All_DIS_CODE);
        }
        return queryAllBankCardInfoOuterService.queryCustBankList(ctx);
    }

    @Override
    public QueryAllBankCardInfoResult queryAllBankCardInfoSwitch(String txAcctNo, String disCode) {
        BusinessAspect.setTradeCommomParams(disCode, null);
        QueryAllBankCardInfoContext ctx = new QueryAllBankCardInfoContext();
        ctx.setTxAcctNo(txAcctNo);
        ctx.setDisCode(disCode);
        if (DefaultParamsConstant.DEFULT_DIS_CODE.equals(disCode)) {
            ctx.setDisCode(DefaultParamsConstant.All_DIS_CODE);
        }
        return queryAllBankCardInfoOuterService.queryCustBankList(ctx, false);
    }

    /**
     * getPerfectCustBanks:(过滤用户绑定的可用银行卡)
     *
     * @param bankList
     * @return
     * <AUTHOR>
     * @date 2017年4月20日 下午7:20:59
     */
    private List<QueryBindCustBankCardResult> getPerfectCustBanks(List<QueryBindCustBankCardResult> bankList) {
        List<QueryBindCustBankCardResult> perfectCustBanks = null;
        if (!CollectionUtils.isEmpty(bankList)) {
            perfectCustBanks = new ArrayList<>();
            for (QueryBindCustBankCardResult bank : bankList) {
                // 0-正常2-验证成功；4-鉴权成功；
                if ("0".equals(bank.getBankAcctStatus()) && "2".equals(bank.getBankAcctVrfyStat()) && "4".equals(bank.getAcctIdentifyStat())) {
                    perfectCustBanks.add(bank);
                }
            }
        }
        return perfectCustBanks;
    }

    @Override
    public FundInfoAndNavBean getFundNavInfo(String fundCode, String tradeDt) {
        return queryFundInfoOuterService.getFundInfoAndNav(fundCode, tradeDt);
    }

    @Override
    public FundProductFeeRateBean getFundFeeRateByAmt(String midProductId, String busiCode, String invstType, String shareClass, BigDecimal appAmt) {
        return queryFundInfoOuterService.getFundFeeRateByAmt(midProductId, busiCode, invstType, shareClass, appAmt);
    }

    @Override
    public String getMBusiCode(CustInfoDto custInfoDto, FundInfoAndNavBean fundInfoAndNavBean, String workDay) throws Exception {
        String mBusiCode = null;
        if (null != fundInfoAndNavBean) {
            if (MDataDic.CAN_BUY_SET.contains(fundInfoAndNavBean.getFundStat())) {
                HighProductAppointmentInfoModel productAppointmentInfoModel = highProductService.getAppointmentInfo(fundInfoAndNavBean.getFundCode(), "0",
                        fundInfoAndNavBean.getFundShareClass(), custInfoDto.getRegDisCode(), new Date());
                if (null != productAppointmentInfoModel) {
                    mBusiCode = productAppointmentInfoModel.getmBusiCode();
                    return mBusiCode;
                }
            }

            if (MDataDic.CAN_PUR_SET.contains(fundInfoAndNavBean.getFundStat())) {
                return BusinessCodeEnum.PURCHASE.getMCode();
            } else if (MDataDic.CAN_SUR_SET.contains(fundInfoAndNavBean.getFundStat())) {
                return BusinessCodeEnum.SUBS.getMCode();
            }
        }
        return null;
    }

    @Override
    public FundInfoAndNavBean getFundNavInfo(String fundCode, String appDt, String appTm) {
        String tradeDt = getCurrTaTradeDt(appDt, appTm);
        return getFundNavInfo(fundCode, tradeDt);
    }

    @Override
    public HighProductBaseInfoBean getHighFundInfo(String fundCode) {
        return queryHighProductOuterService.getHighProductBaseInfo(fundCode);
    }

    @Override
    public String getCurrTaTradeDt(String appDt, String appTm) {
        return queryTradeDayOuterService.getWorkDay(appDt, appTm);
    }

    @Override
    public String getCurrTaTradeDt() {
        WorkDayInfo workDayInfo = queryTradeDayOuterService.getWorkDayModel(new Date());
        if (null != workDayInfo) {
            return workDayInfo.getWorkday();
        }
        throw new TmsCounterException(TmsCounterResultEnum.FAILD);
    }


    @Override
    public WorkDayInfo getTaTradeDt() {
        return queryTradeDayOuterService.getWorkDayModel(new Date());
    }


    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public CustomerAppointmentInfoRespDto queryAppointmentInfo(Map<String, Object> parameter, Page cpage) throws Exception {
        LOGGER.info("tmsCounterOutService|queryAppointmentInfo|parameter:{}", parameter);
        if (null == parameter || parameter.size() < 1) {
            LOGGER.info("queryAppointmentInfo|parameter:{}", parameter);
            return null;
        }
        CustomerAppointmentInfoRespDto customerAppointmentInfoRespDto = getCustomerAppointmentInfoRespDto(parameter, cpage);
        if (customerAppointmentInfoRespDto == null){
            return null;
        }
        return customerAppointmentInfoRespDto;
    }

    private CustomerAppointmentInfoRespDto getCustomerAppointmentInfoRespDto(Map<String, Object> parameter, Page cpage) throws Exception {
        CustomerAppointmentInfoRespDto customerAppointmentInfoRespDto = new CustomerAppointmentInfoRespDto();
        List<PreBookListBean> preBookList = null;
        try {
            QueryPreBookListRequest queryRequest = new QueryPreBookListRequest();
            queryRequest.setTxAcctNo((String) parameter.get("custNo"));
            queryRequest.setPreType((List) parameter.get("preType"));
            queryRequest.setTradeType((List) parameter.get("tradeType"));
            queryRequest.setPreBookState((List) parameter.get("statusList"));
            queryRequest.setUseFlag(PreBookUseFlagEnum.NOT_USED.getCode());
            queryRequest.setPageNo(cpage.getPage());
            queryRequest.setPageSize(cpage.getPerPage());
            BaseResponse baseResp = TmsFacadeUtil.execute(queryPreBookListFacade, queryRequest, null);
            QueryPreBookListResponse queryPreBookListResponse = null;

            if (TmsFacadeUtil.isSuccess(baseResp)) {
                queryPreBookListResponse = (QueryPreBookListResponse) baseResp;
            }

            if (queryPreBookListResponse == null || CollectionUtils.isEmpty(queryPreBookListResponse.getPreBookList())) {
                LOGGER.info("queryAppointmentInfo|querySubscriptionFacade|querySubscriptionWithSynchronizedCrm|parameter:{},queryPreBookListResponse:{}",
                        parameter, queryPreBookListResponse);
                return null;
            }
            preBookList = queryPreBookListResponse.getPreBookList();

            DisInfoDto disInfoDto = new DisInfoDto();
            String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);

            List<CustomerAppointmentInfoDto> customerAppointmentInfoDtoList = new ArrayList<CustomerAppointmentInfoDto>(16);
            for (PreBookListBean bean : preBookList) {
                CustomerAppointmentInfoDto customerAppointmentInfoDto = new CustomerAppointmentInfoDto();
                customerAppointmentInfoDto.setAppointId(bean.getPreId());
                customerAppointmentInfoDto.setProductCode(bean.getFundCode());
                customerAppointmentInfoDto.setProductName(bean.getFundName());
                customerAppointmentInfoDto.setFundAttr(bean.getFundName());
                customerAppointmentInfoDto.setAppAmt(bean.getAckAmt());
                customerAppointmentInfoDto.setDiscountRate(bean.getDiscount());
                customerAppointmentInfoDto.setOrderStatus(bean.getPrebookState());
                customerAppointmentInfoDto.setBuyStatus(bean.getTradeType());
                customerAppointmentInfoDto.setmBusiCode(bean.getmBusiCode());
                customerAppointmentInfoDto.setAppVol(bean.getSellVol());
                customerAppointmentInfoDto.setPreType(bean.getPreType());
                // 查询基金信息
                getFundInfoAndNav(bean.getFundCode(), customerAppointmentInfoDto, workDay);
                Date appointDate = null;
                // 处理crm返回的日期格式是yyyyMMddHHmmssSSS
                if (bean.getCreDt().length() > DateUtils.YYYYMMDDHHMMSS.length()) {
                    appointDate = DateUtils.formatToDate(bean.getCreDt().substring(0, 14), DateUtils.YYYYMMDDHHMMSS);
                } else {
                    appointDate = DateUtils.formatToDate(bean.getCreDt(), DateUtils.YYYYMMDDHHMMSS);
                }
                // querySubscriptionFacade接口没有返回字段
                customerAppointmentInfoDto.setAppointStartTm(DateUtils.formatToString(appointDate, "HH:mm:ss"));
                customerAppointmentInfoDto.setAppointStartDt(DateUtils.formatToString(appointDate, "yyyy-MM-dd"));
                customerAppointmentInfoDto.setDoubleHandleDt(bean.getDoubleHandleDt());
                customerAppointmentInfoDto.setDoubleHandleFlag(bean.getDoubleHandleFlag());
                customerAppointmentInfoDto.setDoubleNeedFlag(bean.getDoubleNeedFlag());
                customerAppointmentInfoDto.setFirstPreId(bean.getFirstPreId());
                customerAppointmentInfoDto.setSubsAmt(bean.getSubsAmt());
                customerAppointmentInfoDtoList.add(customerAppointmentInfoDto);
            }

            customerAppointmentInfoRespDto.setCustomerAppointmentInfoDtoList(customerAppointmentInfoDtoList);
            customerAppointmentInfoRespDto.setTotalCount(queryPreBookListResponse.getTotalCount());
            if (0 == queryPreBookListResponse.getTotalCount()) {
                customerAppointmentInfoRespDto.setTotalPage(0);
                customerAppointmentInfoRespDto.setPageNo(1);
            } else {
                customerAppointmentInfoRespDto.setTotalPage((queryPreBookListResponse.getTotalCount() / cpage.getPerPage()) + 1);
                customerAppointmentInfoRespDto.setPageNo(queryPreBookListResponse.getPageNo());
            }
        } catch (BusinessException e) {
            LOGGER.error(e.getMessage(), e);
            throw new TmsCounterException(e.getErrorCode(), e.getMessage(), e);
        }
        return customerAppointmentInfoRespDto;
    }

    @Override
    public CustomerAppointmentInfoRespDto queryAppointmentInfo(QueryPreBookListRequest queryRequest, Page cpage) throws Exception {

        CustomerAppointmentInfoRespDto customerAppointmentInfoRespDto = new CustomerAppointmentInfoRespDto();
        List<PreBookListBean> preBookList = null;
        try {
            queryRequest.setPageNo(cpage.getPage());
            queryRequest.setPageSize(cpage.getPerPage());
            BaseResponse baseResp = TmsFacadeUtil.execute(queryPreBookListFacade, queryRequest, null);
            QueryPreBookListResponse queryPreBookListResponse = null;

            if (TmsFacadeUtil.isSuccess(baseResp)) {
                queryPreBookListResponse = (QueryPreBookListResponse) baseResp;
            }

            if (queryPreBookListResponse == null || CollectionUtils.isEmpty(queryPreBookListResponse.getPreBookList())) {
                LOGGER.info("queryAppointmentInfo|queryPreBookListFacade|is null");
                return null;
            }
            preBookList = queryPreBookListResponse.getPreBookList();

            DisInfoDto disInfoDto = new DisInfoDto();
            String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);

            List<CustomerAppointmentInfoDto> customerAppointmentInfoDtoList = new ArrayList<CustomerAppointmentInfoDto>(16);
            for (PreBookListBean bean : preBookList) {
                CustomerAppointmentInfoDto customerAppointmentInfoDto = new CustomerAppointmentInfoDto();
                customerAppointmentInfoDto.setAppointId(bean.getPreId());
                customerAppointmentInfoDto.setProductCode(bean.getFundCode());
                customerAppointmentInfoDto.setProductName(bean.getFundName());
                customerAppointmentInfoDto.setFundAttr(bean.getFundName());
                customerAppointmentInfoDto.setAppAmt(bean.getAckAmt());
                customerAppointmentInfoDto.setSubsAmt(bean.getSubsAmt());
                customerAppointmentInfoDto.setDiscountRate(bean.getDiscount());
                customerAppointmentInfoDto.setOrderStatus(bean.getPrebookState());
                customerAppointmentInfoDto.setBuyStatus(bean.getTradeType());
                customerAppointmentInfoDto.setmBusiCode(bean.getmBusiCode());
                customerAppointmentInfoDto.setAppVol(bean.getSellVol());
                customerAppointmentInfoDto.setPreType(bean.getPreType());
                // 查询基金信息
                getFundInfoAndNav(bean.getFundCode(), customerAppointmentInfoDto, workDay);
                Date appointDate = DateUtils.formatToDate(bean.getCreDt(), DateUtils.YYYYMMDDHHMMSS);
                // querySubscriptionFacade接口没有返回字段
                customerAppointmentInfoDto.setAppointStartTm(DateUtils.formatToString(appointDate, "HH:mm:ss"));
                customerAppointmentInfoDto.setAppointStartDt(DateUtils.formatToString(appointDate, DateUtils.YYYY_MM_DD));
                customerAppointmentInfoDtoList.add(customerAppointmentInfoDto);
            }

            customerAppointmentInfoRespDto.setCustomerAppointmentInfoDtoList(customerAppointmentInfoDtoList);
            customerAppointmentInfoRespDto.setTotalCount(queryPreBookListResponse.getTotalCount());
            if (0 == queryPreBookListResponse.getTotalCount()) {
                customerAppointmentInfoRespDto.setTotalPage(0);
                customerAppointmentInfoRespDto.setPageNo(1);
            } else {
                customerAppointmentInfoRespDto.setTotalPage((queryPreBookListResponse.getTotalCount() / cpage.getPerPage()) + 1);
                customerAppointmentInfoRespDto.setPageNo(queryPreBookListResponse.getPageNo());
            }
        } catch (BusinessException e) {
            LOGGER.error(e.getMessage(), e);
            throw new TmsCounterException(e.getErrorCode(), e.getMessage(), e);
        }
        return customerAppointmentInfoRespDto;
    }

    /***
     *
     * getFundInfoAndNav:(查询基金信息)
     *
     * @param productCode
     * @param customerAppointmentInfoDto
     * <AUTHOR>
     * @date 2017年8月2日 上午10:51:36
     */
    private void getFundInfoAndNav(String productCode, CustomerAppointmentInfoDto customerAppointmentInfoDto, String workDay) {
        LOGGER.info("tmsCounterOutService|queryAppointmentInfo|getFundInfoAndNav|productCode:{},customerAppointmentInfoDto:{}", productCode,
                JSON.toJSONString(customerAppointmentInfoDto));
        FundInfoAndNavBean fundInfoAndNavBean = queryFundInfoOuterService.getFundInfoAndNav(productCode, workDay);
        customerAppointmentInfoDto.setFundRiskLevel(fundInfoAndNavBean != null ? fundInfoAndNavBean.getFundRiskLevel() : "");
        customerAppointmentInfoDto.setFundStatus(fundInfoAndNavBean != null ? fundInfoAndNavBean.getFundStat() : "");
        customerAppointmentInfoDto.setFundType(fundInfoAndNavBean != null ? fundInfoAndNavBean.getFundType() : "");
    }

    @Override
    public FeeDto calFundBuyFee(FeeReqDto feeReqDto) throws Exception {
        LOGGER.info("TmsCounterOutService|calFundBuyFee|feeReqDto:{}", JSON.toJSONString(feeReqDto));
        if (null == feeReqDto || StringUtils.isEmpty(feeReqDto.getFeeCalMode())) {
            throw new ValidateException(com.howbuy.tms.common.constant.ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL,
                    MessageSource.getMessageByCode(com.howbuy.tms.common.constant.ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL));
        }
        QueryFeeInfoFacadeRequest queryFeeInfoFacadeRequest = new QueryFeeInfoFacadeRequest();
        queryFeeInfoFacadeRequest.setAppointmentDealNo(feeReqDto.getAppointmentDealNo());
        queryFeeInfoFacadeRequest.setDisCode(feeReqDto.getDisCode());
        queryFeeInfoFacadeRequest.setTxChannelCode(TxChannelEnum.COUNTER.getCode());
        queryFeeInfoFacadeRequest.setTxAcctNo(feeReqDto.getTxAcctNo());
        queryFeeInfoFacadeRequest.setFundCode(feeReqDto.getFundCode());
        queryFeeInfoFacadeRequest.setSubsAmt(feeReqDto.getSubsAmt());
        queryFeeInfoFacadeRequest.setPaidAmt(feeReqDto.getNetBuyAmt());
        queryFeeInfoFacadeRequest.setOutletCode("counter");
        queryFeeInfoFacadeRequest.setOperIp(feeReqDto.getOperIp());
        queryFeeInfoFacadeRequest.setDiscountRate(feeReqDto.getDiscountRate());
        queryFeeInfoFacadeRequest.setBankCode(feeReqDto.getBankCode());
        queryFeeInfoFacadeRequest.setmBusinessCode(feeReqDto.getBusinessCode());
        queryFeeInfoFacadeRequest.setQueryDate(feeReqDto.getQueryDate());
        QueryFeeInfoFacadeResponse response = queryFeeInfoFacade.execute(queryFeeInfoFacadeRequest);
        LOGGER.info("calFundBuyFee-查询费用信息,queryFeeInfoFacadeRequest={},返回结果:{}", JSON.toJSONString(queryFeeInfoFacadeRequest), JSON.toJSONString(response));
        FeeDto feeDto = new FeeDto();
        feeDto.setFundBuyFee(response.getFee());
        feeDto.setDiscountRate(response.getDiscountRate());
        feeDto.setPayAmt(response.getActualPayAmt());
        feeDto.setFeeRate(response.getOriginalFeeRate());
        return feeDto;
    }

    @Override
    public String getMBusiCode(CustInfoDto custInfoDto, String fundCode, String workDay) throws Exception {
        LOGGER.info("getMBusiCode|custInfoDto；{}, fundCode:{}, workDay:{}", JSON.toJSONString(custInfoDto), fundCode, workDay);
        // 查询产品基本信息
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(fundCode);

        if (highProductBaseBean == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }

        String submitTaDt = calSubmitTaDt(highProductBaseBean, workDay, custInfoDto.getDisCode());
        LOGGER.info("getMBusiCode|submitTaDt:{}, productCode:{}", submitTaDt, highProductBaseBean.getFundCode());
        // 查询产品状态
        HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(fundCode, submitTaDt);
        if (null != highProductStatInfoBean) {
            if (MDataDic.CAN_PUR_SET.contains(highProductStatInfoBean.getFundStat())) {
                return BusinessCodeEnum.PURCHASE.getMCode();
            } else if (MDataDic.CAN_SUR_SET.contains(highProductStatInfoBean.getFundStat())) {
                return BusinessCodeEnum.SUBS.getMCode();
            }
        }
        return null;

    }

    /**
     * calSubmitTaDt: 计算上报TA日
     *
     * @param highProductBaseInfoBean
     * @param workDay
     * @param disCode
     * @return
     * @throws Exception
     */
    private String calSubmitTaDt(HighProductBaseInfoBean highProductBaseInfoBean, String workDay, String disCode) throws Exception {

        String submitTaDt = null;
        String appDtmStr = workDay + TmsCounterConstants.DEFAULT_APP_TM;
        Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
        String isScheduledTrade = highProductBaseInfoBean.getIsScheduledTrade();
        String mBusiCode = null;
        // 核心业务处理

        // 根据当前TaTradeDt与募集结束日比较, 得出具体业务码
        // 募集结束日期
        String ipoEndDt = highProductBaseInfoBean.getIpoEndDt();
        if (StringUtils.isEmpty(ipoEndDt)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAM_IS_ERROR.getCode(), "参数错误，没有配置产品募集截止日");
        }
        mBusiCode = com.howbuy.tms.common.enums.busi.BusinessCodeEnum.SUBS.getMCode();
        if (workDay.compareTo(ipoEndDt) > 0) {
            mBusiCode = com.howbuy.tms.common.enums.busi.BusinessCodeEnum.PURCHASE.getMCode();
        }

        // 预约信息处理
        if (com.howbuy.tms.common.enums.busi.BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) || IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(isScheduledTrade) || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade)) {
            // 查询预约信息
            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(
                    highProductBaseInfoBean.getFundCode(), "0", highProductBaseInfoBean.getShareClass(), disCode, appDtm);
            if (productAppointmentInfoBean == null) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_HIGH_APPOINT_DAY_IS_NULL);
            }
            submitTaDt = productAppointmentInfoBean.getOpenEndDt();
        } else {
            submitTaDt = workDay;
        }

        return submitTaDt;
    }

    @Override
    public void changeFinaDirect(String custNo, String finaDirect, DisInfoDto disInfoDto) {
        ChangeCustCollectProtocolMethodRequest request = new ChangeCustCollectProtocolMethodRequest();
        request.setTxAcctNo(custNo);
        // （2-回款至银行卡（用户选择）
        // 4-回款至储蓄罐（用户选择））
        request.setCollectProtocolMethod(finaDirect);

        request.setDisCode(disInfoDto.getDisCode());
        request.setOutletCode("H20131104");
        request.setTradeChannel(TxChannelEnum.COUNTER.getCode());
        Date currDate = new Date();
        String appDt = DateUtils.formatToString(currDate, DateUtils.YYYYMMDD);
        String appTm = DateUtils.formatToString(currDate, DateUtils.HHMMSS);
        TradeParamLocalUtils.setAppDt(appDt);
        TradeParamLocalUtils.setAppTm(appTm);

        changeCustCollectProtocolMethodFacade.execute(request);
    }

    @Override
    public String queryDefaultCnaps(String bankCode, String provCode, String cityCode) {
        QueryDefaultCnapsRequest request = new QueryDefaultCnapsRequest();
        request.setProvCode(provCode);
        request.setCityCode(cityCode);
        request.setBankCode(bankCode);
        setCommonParameters(request);
        // 查询联行号
        QueryDefaultCnapsResponse queryDefaultCnapsResponse = queryDefaultCnapsFacade.execute(request);
        if (null != queryDefaultCnapsResponse && TradeConstant.SUCCESS_ACC_TRADE.equals(queryDefaultCnapsResponse.getReturnCode())) {
            for (List<CityBean> cityBeans : queryDefaultCnapsResponse.getProvCitMap().values()) {
                for (CityBean cityBean : cityBeans) {
                    if (cityCode.equals(cityBean.getCityCode())) {
                        return cityBean.getCnapsNo();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据交易账号查询一帐通号
     *
     * @param txAcctNo
     * @return
     */
    @Override
    public String queryHboneNoByTxAccountNo(String txAcctNo) {
        QueryTxAcctByHboneRequest request = new QueryTxAcctByHboneRequest();
        request.setTxAcctNo(txAcctNo);
        setCommonParameters(request);
        QueryTxAcctByHboneResponse response = queryTxAcctByHboneFacade.execute(request);
        if (response.getHboneNo() != null && TradeConstant.SUCCESS_ACC_TRADE.equals(response.getReturnCode())) {
            return response.getHboneNo();
        } else {
            return null;
        }
    }

    @Override
    public String queryTxAccountNoByHboneNo(String hboneNo) {
        QueryTxAcctByHboneRequest request = new QueryTxAcctByHboneRequest();
        request.setHboneNo(hboneNo);
        setCommonParameters(request);
        QueryTxAcctByHboneResponse response = queryTxAcctByHboneFacade.execute(request);
        if (response.getTxAcctNo() != null && TradeConstant.SUCCESS_ACC_TRADE.equals(response.getReturnCode())) {
            return response.getTxAcctNo();
        } else {
            return null;
        }
    }


    @Override
    public QueryCustTransInfoDto queryCustTransInfo(String txAcctNo, DisInfoDto disInfoDto) throws Exception {
        QueryCustTransInfoDto queryCustTransInfoDto = new QueryCustTransInfoDto();
        try {
            String disCode = null;
            if (disInfoDto != null) {
                disCode = disInfoDto.getDisCode();
            }
            if (StringUtils.isEmpty(disCode)) {
                disCode = TradeConstant.HOWBUY_DIS_CODE;
            }
            QueryCustInfoForPrivateResult response = queryCustInfoForPrivateOuterService.queryCustInfoWithLinkInfoPlaintext(txAcctNo, disCode);
            queryCustTransInfoDto.setLinkIdNo(response.getLinkIdNo());
            queryCustTransInfoDto.setLinkIdType(response.getLinkIdType());
            queryCustTransInfoDto.setLinkMan(response.getLinkMan());

        } catch (Exception e) {
            LOGGER.error("queryCustBaseInfo error :", e);
        }

        return queryCustTransInfoDto;
    }

    /**
     * 查询银行卡号
     *
     * @param txAcctNo 交易账号
     * @param bankAcct 银行卡号
     * @return
     */
    @Override
    public QueryBankCardInfoResponse queryBankCardInfo(String txAcctNo, final String bankAcct) {
        QueryBankCardInfoRequest request = new QueryBankCardInfoRequest();
        request.setTxAcctNo(txAcctNo);
        request.setBankAcctDigest(DigestUtil.digest(bankAcct));
        setCommonParameters(request);
        return queryBankCardInfoFacade.execute(request);
    }

    /**
     * 查询银行卡信息
     *
     * @param txAcctNo
     * @param bankAcctDigest 银行卡摘要
     * @return
     */
    @Override
    public QueryBankCardInfoResponse queryBankCardInfoByDigest(String txAcctNo, String bankAcctDigest) {
        QueryBankCardInfoRequest request = new QueryBankCardInfoRequest();
        request.setTxAcctNo(txAcctNo);
        request.setBankAcctDigest(bankAcctDigest);
        setCommonParameters(request);
        return queryBankCardInfoFacade.execute(request);
    }

    @Override
    public QueryCustMobileResponse queryCustMobileFacade(String txAcctNo, String cpAcctNo) {
        QueryCustMobileRequest request = new QueryCustMobileRequest();
        request.setCustNo(txAcctNo);
        request.setCpAcctNoList(Lists.newArrayList(cpAcctNo));
        setCommonParameters(request);
        return queryCustMobileFacade.execute(request);
    }

    @Override
    public QueryBankAcctSensitiveInfoResponse queryBankAcctSensitiveInfoFacade(String txAcctNo, final String cpAcctNo) {
        QueryBankAcctSensitiveInfoRequest request = new QueryBankAcctSensitiveInfoRequest();
        request.setCustNo(txAcctNo);
        request.setCpAcctNo(cpAcctNo);
        ;
        setCommonParameters(request);
        request.setCpAcctSwitch(true);
        return queryBankAcctSensitiveInfoFacade.execute(request);
    }

    private static void setCommonParameters(AccBaseRequest request) {
        request.setOperCode("00");
        request.setTradeChannel("6");
        MfDateTime currDate = new MfDateTime();
        request.setAppDt(currDate.toString(MfDate.strPatternYYYYMMDD));
        request.setAppTm(currDate.toString(MfDate.strPatternHHMMSS));
        if (StringUtils.isEmpty(request.getDisCode()) || DefaultParamsConstant.DEFULT_DIS_CODE.equals(request.getDisCode())) {
            request.setDisCode(TradeConstant.HOWBUY_DIS_CODE);
        }
        request.setOutletCode(TradeConstant.TRADE_ACC_OUTLET_CODE);
    }

    @Override
    public QueryPreBookListRespDto queryPreBookList(QueryPreBookListReqDto queryPreBookListReqDto) throws Exception {
        QueryPreBookListRequest request = new QueryPreBookListRequest();
        if (queryPreBookListReqDto != null) {
            BeanUtils.copyProperties(queryPreBookListReqDto, request);
        }

        QueryPreBookListRespDto queryPreBookListRespDto = new QueryPreBookListRespDto();
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryPreBookListFacade, request, null);
        QueryPreBookListResponse resp = (QueryPreBookListResponse) baseResp;

        if (!CollectionUtils.isEmpty(resp.getPreBookList())) {
            List<PreBookListDto> rstList = new ArrayList<PreBookListDto>(16);
            PreBookListDto preBookListDto = null;
            for (PreBookListBean preBookListBean : resp.getPreBookList()) {
                preBookListDto = new PreBookListDto();
                BeanUtils.copyProperties(preBookListBean, preBookListDto);

                rstList.add(preBookListDto);
            }
            queryPreBookListRespDto.setPreBookList(rstList);
            queryPreBookListRespDto.setTotalPage(resp.getTotalPage());
            queryPreBookListRespDto.setTotalCount(resp.getTotalCount());
            queryPreBookListRespDto.setPageNo(resp.getPageNo());
        }
        return queryPreBookListRespDto;
    }

    @Override
    public QueryRegularProductDto queryRegularProductInfo(String productId, String taDate) {
        QueryRegularProductDto queryRegularProductDto = null;
        String unique = UUID.randomUUID().toString().replaceAll("-", "");
        LOGGER.info("#####tmscounter-查询定期产品-request data:{},key:{}", productId, unique);
        TrustReceiptProductModel trustReceiptProductModel = trustReceiptProductService.queryTrustReceiptProduct(productId, taDate);
        LOGGER.info("#####tmscounter-查询定期产品-response data:{},key:{}", JSON.toJSONString(trustReceiptProductModel), unique);
        if (trustReceiptProductModel != null) {
            queryRegularProductDto = new QueryRegularProductDto();
            BeanUtils.copyProperties(trustReceiptProductModel, queryRegularProductDto);
        }
        return queryRegularProductDto;
    }

    @Override
    public QueryAccKycInfoResult queryAccKycInfo(String txAcctNo) {
        QueryAccKycInfoResult queryAccKycInfoResult = null;
        try {
            queryAccKycInfoResult = queryAccKycInfoOuterService.queryAccKycInfoByTxAcctNo(txAcctNo);
        } catch (Exception e) {
            LOGGER.error("queryAccKycInfo error :", e);
        }

        return queryAccKycInfoResult;
    }

    @Override
    public List<FundTAInfo> queryFundTaInfo(String productChannel) {
        return queryProductInfoOuterService.getAllFundTAInfo(productChannel);
    }

    @Override
    public PageResult<FundTaInfoBean> getFundTaInfoListByPage(String productChannel, String fundCode, String fundAttr, int pageNum, int pageSize) {
        return queryFundInfoOuterService.getFundTaInfoListByPage(productChannel, fundCode, fundAttr, pageNum, pageSize);
    }

    private static Map<String, List<String>> CRM_TRADE_TYPE_MAP = new HashMap<>();

    /**
     * 审核节点，和依赖CRM材料状态关系
     */
    private final static Map<String, String> CAN_CHECK_STATUS_MAP = new HashMap<>();

    /**
     * 审核节点和CRM审核级别关系
     */
    private final static Map<String, String> CHECKLEVELMAP = new HashMap<>();

    /**
     * 分次call 打款
     */
    private final static String BUSI_CALL_BUY = "20";


    static {
        // 购买
        CRM_TRADE_TYPE_MAP.put("0", Lists.newArrayList(OrderFileEnum.BUSS_NOFINITE.getCode(),
                OrderFileEnum.BUSS_FINITE.getCode(),
                OrderFileEnum.BUSS_APPEND.getCode(),
                BUSI_CALL_BUY));

        // 赎回
        CRM_TRADE_TYPE_MAP.put("1", Lists.newArrayList(OrderFileEnum.BUSS_REDEEM.getCode()));
        // 修改分红方式
        CRM_TRADE_TYPE_MAP.put("2", Lists.newArrayList(OrderFileEnum.BUSS_CHANGEBONUS.getCode()));
        // 撤单
        CRM_TRADE_TYPE_MAP.put("3", Lists.newArrayList(OrderFileEnum.BUSS_CANCEL.getCode()));

        CAN_CHECK_STATUS_MAP.put(OpCheckNode.PRE_CHECK.getCode(), OrderFileEnum.CURSTAT_OPWAIT.getCode());
        CAN_CHECK_STATUS_MAP.put(OpCheckNode.RE_CHECK.getCode(), OrderFileEnum.CURSTAT_OPFIRSTADOPT.getCode());
        CAN_CHECK_STATUS_MAP.put(OpCheckNode.MODIFY.getCode(), OrderFileEnum.CURSTAT_OPWAIT.getCode());

        // 5-OP初审，6-OP终审
        CHECKLEVELMAP.put(OpCheckNode.PRE_CHECK.getCode(), "5");
        CHECKLEVELMAP.put(OpCheckNode.RE_CHECK.getCode(), "6");
        CHECKLEVELMAP.put(OpCheckNode.MODIFY.getCode(), "5");
    }

    @Override
    public QueryOrderFileDto queryOrderFile(QueryOrderFileContext queryContext, String checkNode) {
        // 审核级别
        queryContext.setChecklevel(CHECKLEVELMAP.get(checkNode));
        if (!StringUtils.isEmpty(queryContext.getBusiid())) {
            if (!CollectionUtils.isEmpty(CRM_TRADE_TYPE_MAP.get(queryContext.getBusiid()))) {
                if (CRM_TRADE_TYPE_MAP.get(queryContext.getBusiid()).size() == 1) {
                    queryContext.setBusiid(CRM_TRADE_TYPE_MAP.get(queryContext.getBusiid()).get(0));

                    QueryOrderFileResult respResult = queryOrderFileOuterService.queryOrderFile(queryContext);
                    return buildRst(respResult, checkNode);
                } else {
                    List<QueryOrderFileDto> orderList = new ArrayList<>();
                    for (String busiid : CRM_TRADE_TYPE_MAP.get(queryContext.getBusiid())) {
                        queryContext.setBusiid(busiid);
                        QueryOrderFileResult respResult = queryOrderFileOuterService.queryOrderFile(queryContext);
                        if (respResult != null && StringUtils.isNotEmpty(respResult.getOrderid())) {
                            orderList.add(buildRst(respResult, checkNode));
                        }
                    }
                    if (orderList.size() == 1) {
                        return orderList.get(0);
                    } else if (orderList.size() > 1) {
                        throw new TmsCounterException("", "资料笔数大于1");
                    } else {
                        return null;
                    }
                }
            }
        }
        QueryOrderFileResult respResult = queryOrderFileOuterService.queryOrderFile(queryContext);
        return buildRst(respResult, checkNode);


    }

    @Override
    public void cancelFile(String materialId) {
        LOGGER.info("TmsCounterOutServiceImpl-cancelFile,资料作废,materialId:{}", materialId);
        String url = tmsCounterNacosConfig.getCrmTradeUrlPrefix() + "/counterorder/tocancelorder";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("id", materialId);
        String resp = HttpConnectionPoolUtil.post(url, paramMap);
        LOGGER.info("TmsCounterOutServiceImpl-cancelFile,资料作废,url={}, paramMap:{}, response:{}", url, JSON.toJSONString(paramMap), JSON.toJSONString(resp));
        if (StringUtils.isBlank(resp)) {
            throw new TmsCounterException(ExceptionCodes.CENTER_ACCESS_DUBBO_TXIO_FAILED, "调用crm作废资料异常");
        }
        String returnCode;
        try {
            JSONObject respObject = JSONObject.parseObject(resp);
            returnCode = respObject.getString("returnCode");
        } catch (Exception e) {
            throw new TmsCounterException(ExceptionCodes.CENTER_ACCESS_DUBBO_TXIO_FAILED, "调用crm作废资料返回结果,解析异常");
        }
        if (!SUCCESS_CODE.equals(returnCode)) {
            throw new TmsCounterException(ExceptionCodes.CENTER_ACCESS_DUBBO_TXIO_FAILED, "crm作废资料返回失败");
        }
    }


    private String canCheckCrmOrder(String currCheckStatus, String checkNode) {
        if (StringUtils.isNotEmpty(CAN_CHECK_STATUS_MAP.get(checkNode))
                && CAN_CHECK_STATUS_MAP.get(checkNode).equals(currCheckStatus)) {
            return YesOrNoEnum.YES.getCode();
        } else {
            return YesOrNoEnum.NO.getCode();
        }
    }

    private QueryOrderFileDto buildRst(QueryOrderFileResult respResult, String checkNode) {
        QueryOrderFileDto rstDto = new QueryOrderFileDto();
        BeanUtils.copyProperties(respResult, rstDto);
        rstDto.setCanCheck(canCheckCrmOrder(respResult.getCurstat(), checkNode));

        if (!CollectionUtils.isEmpty(respResult.getOrderinfolist())) {
            List<OrderFileInfoDto> fileList = new ArrayList<>(16);
            OrderFileInfoDto fileDto = null;
            for (OrderFileInfoBean fileBean : respResult.getOrderinfolist()) {
                fileDto = new OrderFileInfoDto();
                BeanUtils.copyProperties(fileBean, fileDto);
                if (!CollectionUtils.isEmpty(fileBean.getOrderfilefilelist())) {
                    OrderFileInfoDto.OrderFile orderFileDto = null;
                    List<OrderFileInfoDto.OrderFile> fileDetailList = new ArrayList<>(16);
                    for (OrderFileDetailBean detailFile : fileBean.getOrderfilefilelist()) {
                        orderFileDto = new OrderFileInfoDto.OrderFile();
                        BeanUtils.copyProperties(detailFile, orderFileDto);
                        fileDetailList.add(orderFileDto);
                    }
                    fileDto.setOrderFileList(fileDetailList);
                }

                fileList.add(fileDto);
            }

            rstDto.setOrderinfolist(fileList);
        }

        rstDto.setCheckNode(checkNode);
        return rstDto;
    }

    @Override
    public void auditingFile(OperatorInfoCmd operatorInfoCmd, AuditingOrderFileCmd auditingOrderFileCmd, String dealAppNo) {
        // 更新材料审核状态
        if (auditingOrderFileCmd != null && !StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())) {
            AuditingOrderFileContext context = new AuditingOrderFileContext();
            BeanUtils.copyProperties(auditingOrderFileCmd, context);
            // 审核人
            context.setCurchecker(operatorInfoCmd.getOperatorNo());
            if (!CollectionUtils.isEmpty(auditingOrderFileCmd.getFileinfolist())) {
                FileinfoBean fileinfoBean = null;
                List<FileinfoBean> fileinfoBeanList = new ArrayList<>(16);
                for (FileinfoCmd fileinfoCmd : auditingOrderFileCmd.getFileinfolist()) {
                    fileinfoBean = new FileinfoBean();
                    BeanUtils.copyProperties(fileinfoCmd, fileinfoBean);
                    fileinfoBeanList.add(fileinfoBean);
                }
                context.setFileinfolist(fileinfoBeanList);
            }

            context.setForderid(dealAppNo);
            AuditingOrderFileResult result = auditingOrderFileOuterService.auditingOrderFile(context);
            if (result != null && !ReturnCodeEnum.SUCC.getCode().equals(result.getReturnCode())) {
                throw new TmsCounterException("", result.getDescription());
            }
        }
    }

    private static final Map<String, String> CHECK_STATUS_AND_CURRENT_STATUS_MAP = new HashMap<>();
    private static final Set<String> ORDER_FILE_STATUS_FINISH_SET = new HashSet<>();
    private static final Set<String> OP_CHECK_FINISH_SET = new HashSet<>();

    static {
        CHECK_STATUS_AND_CURRENT_STATUS_MAP.put(OrderFileEnum.CURSTAT_OPFIRSTADOPT.getCode(), OrderFileEnum.CURSTAT_OPWAIT.getCode());
        CHECK_STATUS_AND_CURRENT_STATUS_MAP.put(OrderFileEnum.CURSTAT_OPFIRSTRETURN.getCode(), OrderFileEnum.CURSTAT_OPWAIT.getCode());
        CHECK_STATUS_AND_CURRENT_STATUS_MAP.put(OrderFileEnum.CURSTAT_OPTWORETURN.getCode(), OrderFileEnum.CURSTAT_OPFIRSTADOPT.getCode());
        CHECK_STATUS_AND_CURRENT_STATUS_MAP.put(OrderFileEnum.CURSTAT_SUCCESS.getCode(), OrderFileEnum.CURSTAT_OPFIRSTADOPT.getCode());
        CHECK_STATUS_AND_CURRENT_STATUS_MAP.put(OrderFileEnum.CURSTAT_OPWAIT.getCode(), OrderFileEnum.CURSTAT_OPFIRSTADOPT.getCode());


        ORDER_FILE_STATUS_FINISH_SET.add(OrderFileEnum.CURSTAT_SUCCESS.getCode());
        ORDER_FILE_STATUS_FINISH_SET.add(OrderFileEnum.CURSTAT_VOIDED.getCode());

        OP_CHECK_FINISH_SET.add(OrderFileEnum.CURSTAT_OPFIRSTADOPT.getCode());
        OP_CHECK_FINISH_SET.add(OrderFileEnum.CURSTAT_SUCCESS.getCode());
    }

    @Override
    public void validateOrderFileStatus(AuditingOrderFileCmd cmd) {
        if (cmd == null || StringUtils.isEmpty(cmd.getOrderid())) {
            LOGGER.info("TmsCounterOutServiceImpl|validateOrderFileStatus|orderId is null");
            return;
        }

        QueryOrderFileContext queryContext = new QueryOrderFileContext();
        queryContext.setOrderid(cmd.getOrderid());
        QueryOrderFileResult respResult = queryOrderFileOuterService.queryOrderFile(queryContext);

        if (respResult == null || !ReturnCodeEnum.SUCC.getCode().equals(respResult.getReturnCode())) {
            throw new TmsCounterException("", "资料不存在");
        }

        if (StringUtils.isEmpty(CHECK_STATUS_AND_CURRENT_STATUS_MAP.get(cmd.getCurstat()))) {
            throw new TmsCounterException("", "审核状态不支持," + getOrderFileStausName(cmd.getCurstat()));
        }

        if (!CHECK_STATUS_AND_CURRENT_STATUS_MAP.get(cmd.getCurstat()).equals(respResult.getCurstat())) {
            throw new TmsCounterException("", "柜台材料审核状态异常，提交失败," + getOrderFileStausName(respResult.getCurstat()));
        }
        if (OP_CHECK_FINISH_SET.contains(cmd.getCurstat())) {
            validateIsAllPass(cmd);
        }
    }

    private String getOrderFileStausName(String stat) {
        return OrderFileEnum.getEnum(stat) == null ? "" : OrderFileEnum.getEnum(stat).getDescription();
    }

    @Override
    public void validateOrderFileExist(QueryOrderFileContext queryContext, String checkNode) {
        QueryOrderFileDto queryOrderFileDto = queryOrderFile(queryContext, checkNode);
        if (queryOrderFileDto != null && !StringUtils.isEmpty(queryOrderFileDto.getCurstat())
                && !ORDER_FILE_STATUS_FINISH_SET.contains(queryOrderFileDto.getCurstat())) {
            throw new TmsCounterException("", "请等待销助审核完成后，重新下单");
        }
    }

    @Override
    public void validateIsAllPass(AuditingOrderFileCmd cmd) {
        if (cmd == null || StringUtils.isEmpty(cmd.getOrderid()) || CollectionUtils.isEmpty(cmd.getFileinfolist())) {
            return;
        }
        if (StringUtils.isNotEmpty(cmd.getCurcheckdes())) {
            throw new TmsCounterException("", "订单提交失败，存在材料驳回意见不为空");
        }
        for (FileinfoCmd fileCmd : cmd.getFileinfolist()) {
            if (!YesOrNoEnum.YES.getCode().equals(fileCmd.getIsqual())) {
                throw new TmsCounterException("", "订单提交失败， 存在审核不通过的材料，请检查");
            }
        }
    }

    @Override
    public QueryWorkdayResponse querySysWorkday(WorkdayTypeEnum workdayType, SysCodeEnum sysCodeEnum,
                                                DisInfoDto disInfoDto) throws Exception {
        QueryWorkdayRequest request = new QueryWorkdayRequest();
        request.setWorkdayType(workdayType);
        if (sysCodeEnum == null) {
            sysCodeEnum = SysCodeEnum.BATCH_GM;
        }
        request.setSysCode(sysCodeEnum.getCode());
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryWorkdayFacade, request, disInfoDto);
        return (QueryWorkdayResponse) baseResp;
    }

    @Override
    public QueryAccHboneInfoResult queryAccHboneInfo(String hboneNo) {
        return queryAccHboneInfoOuterService.queryAccHboneInfo(hboneNo);
    }

    @Override
    public QueryHzBuyOrderInfoResponse queryHzBuyInfo(String fundCode, String txAcctNo, String ip, String queryDateStr) {
        LOGGER.info("查询好臻订单信息,fundCode={},custNo={}", fundCode, txAcctNo);
        QueryHzBuyOrderInfoRequest queryHzBuyOrderInfoRequest = new QueryHzBuyOrderInfoRequest();
        queryHzBuyOrderInfoRequest.setDisCode(DisCodeEnum.HZ.getCode());
        queryHzBuyOrderInfoRequest.setTxChannel(TxChannelEnum.COUNTER.getCode());
        queryHzBuyOrderInfoRequest.setTxAcctNo(txAcctNo);
        queryHzBuyOrderInfoRequest.setFundCode(fundCode);
        queryHzBuyOrderInfoRequest.setOutletCode("counter");
        queryHzBuyOrderInfoRequest.setOperIp(ip);
        if (StringUtils.isNotBlank(queryDateStr)) {
            queryHzBuyOrderInfoRequest.setQueryDate(DateUtil.parseDate(queryDateStr, DateUtils.YYYYMMDDHHMMSS));
        }
        QueryHzBuyOrderInfoResponse response = queryHzBuyOrderInfoFacade.execute(queryHzBuyOrderInfoRequest);
        LOGGER.info("查询好臻订单信息-结果,queryHzBuyOrderInfoRequest={},response={}", JSON.toJSONString(queryHzBuyOrderInfoRequest), JSON.toJSONString(response));
        return response;
    }

}
