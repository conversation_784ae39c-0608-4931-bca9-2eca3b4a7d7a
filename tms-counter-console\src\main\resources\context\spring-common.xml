<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
    <context:annotation-config />
	<!-- 异常描述文件加载 -->
	<bean id="resourceBundleMessageSource" class="org.springframework.context.support.ResourceBundleMessageSource">
		<property name="basename">
			<!-- 定义消息资源文件的相对路径 -->
			<value>message</value>
		</property>
	</bean>

	<bean id="bmessageSource" class="com.howbuy.tms.counter.common.MessageSource">
		<property name="resourceBundleMessageSource" ref="resourceBundleMessageSource"/>
	</bean>
	
	<bean id="SpringContextUtil" class="com.howbuy.tms.common.utils.SpringContextUtil"/>

	<!-- 公共线程池 -->
	<bean id="commonThreadPool" class="com.howbuy.tms.common.threadpool.CommonThreadPool">
	</bean>
	
</beans>