/**
 *Copyright (c) 2017, Shang<PERSON>ai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

/**
 * @description:柜台产品信息dto
 */
public class CounterPortfolioProductDto {
    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 产品名称
     */
    private String productName;


    /**
     * 基金类型
     */
    private String fundType;

    /**
     * 基金份额类型
     */
    private String fundShareClass;

    /**
     * ta代码
     */
    private String taCode;


    /**
     * 基金风险等级
     */
    private String riskLevel;

    /**
     * 基金状态
     */
    private String fundStat;

    /**
     * 是否开放申购
     */
    private String isBuyOpen;

    /**
     * 是否开放赎回
     */
    private String isSoldOpen;

    /**
     * 协议号
     */
    private String protocolNo;

    /**
     * 协议名称
     */
    private String protocolName;

    /**
     * 协议类型
     */
    private String protocolType;

    /**
     * 1 表示投顾
     */
    private boolean adviserFlag;

    /**
     * 自建组合标识
     */
    private boolean isSelfFlag;

    /**
     * 当该投顾供应商配置了投顾问卷的时候，该字段为1，否则为0
     */
    private boolean adviserQuestRuleFlag;

    /**
     * 问卷答案
     */
    private String questionAnswer;

    /**
     * 投顾供应商
     */
    private String partnerCode;

    /**
     * 交易截止时间
     */
    private String endTm;

    /**
     * 共同交易地区
     * 1-日本东京
     * 2-韩国首尔
     */
    private String jointOpenDayRegion;

    /**
     * 文件
     */
    private QueryAgreementDto queryAgreementDto;

    public QueryAgreementDto getQueryAgreementDto() {
        return queryAgreementDto;
    }

    public void setQueryAgreementDto(QueryAgreementDto queryAgreementDto) {
        this.queryAgreementDto = queryAgreementDto;
    }

    public boolean isSelfFlag() {
        return isSelfFlag;
    }

    public void setSelfFlag(boolean selfFlag) {
        isSelfFlag = selfFlag;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getJointOpenDayRegion() {
        return jointOpenDayRegion;
    }

    public void setJointOpenDayRegion(String jointOpenDayRegion) {
        this.jointOpenDayRegion = jointOpenDayRegion;
    }

    public String getIsBuyOpen() {
        return isBuyOpen;
    }

    public void setIsBuyOpen(String isBuyOpen) {
        this.isBuyOpen = isBuyOpen;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public boolean isAdviserFlag() {
        return adviserFlag;
    }

    public void setAdviserFlag(boolean adviserFlag) {
        this.adviserFlag = adviserFlag;
    }

    public boolean isAdviserQuestRuleFlag() {
        return adviserQuestRuleFlag;
    }

    public void setAdviserQuestRuleFlag(boolean adviserQuestRuleFlag) {
        this.adviserQuestRuleFlag = adviserQuestRuleFlag;
    }

    public String getQuestionAnswer() {
        return questionAnswer;
    }

    public void setQuestionAnswer(String questionAnswer) {
        this.questionAnswer = questionAnswer;
    }

    public String getEndTm() {
        return endTm;
    }

    public void setEndTm(String endTm) {
        this.endTm = endTm;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getIsSoldOpen() {
        return isSoldOpen;
    }

    public void setIsSoldOpen(String isSoldOpen) {
        this.isSoldOpen = isSoldOpen;
    }
}
