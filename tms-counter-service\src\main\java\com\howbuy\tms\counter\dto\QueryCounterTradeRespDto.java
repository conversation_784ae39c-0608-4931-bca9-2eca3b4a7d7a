/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.math.BigDecimal;
import java.util.List;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;

/**
 * @description:(柜台交易统计响应) 
 * <AUTHOR>
 * @date 2017年4月11日 上午11:21:38
 * @since JDK 1.6
 */
public class QueryCounterTradeRespDto extends BaseResponseDto {
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -7037005597777581056L;
    /**
     * 审核通过认申购总金额
     */
    private BigDecimal checkedBuyAmt;
    /**
     * 审核通过赎回总份额
     */
    private BigDecimal  checkedRedeemVol;
    
    private List<CounterTradeDto> counterTradeDtoList;

    public BigDecimal getCheckedBuyAmt() {
        return checkedBuyAmt;
    }

    public void setCheckedBuyAmt(BigDecimal checkedBuyAmt) {
        this.checkedBuyAmt = checkedBuyAmt;
    }

    public BigDecimal getCheckedRedeemVol() {
        return checkedRedeemVol;
    }

    public void setCheckedRedeemVol(BigDecimal checkedRedeemVol) {
        this.checkedRedeemVol = checkedRedeemVol;
    }

    public List<CounterTradeDto> getCounterTradeDtoList() {
        return counterTradeDtoList;
    }

    public void setCounterTradeDtoList(List<CounterTradeDto> counterTradeDtoList) {
        this.counterTradeDtoList = counterTradeDtoList;
    }

}

