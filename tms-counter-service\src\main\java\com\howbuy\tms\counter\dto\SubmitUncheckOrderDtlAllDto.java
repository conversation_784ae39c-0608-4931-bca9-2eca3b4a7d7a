package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.util.List;

public class SubmitUncheckOrderDtlAllDto implements Serializable{

	private static final long serialVersionUID = -2298208470224317651L;
	
	/**
     * 总记录数
     */
    private long totalCount;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 当前页
     */
    private long pageNo;
    
	public long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}

	public long getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}

	public long getPageNo() {
		return pageNo;
	}

	public void setPageNo(long pageNo) {
		this.pageNo = pageNo;
	}

	private List<SubmitUncheckOrderDtlDto> submitUncheckOrderDtlList;

	public List<SubmitUncheckOrderDtlDto> getSubmitUncheckOrderDtlList() {
		return submitUncheckOrderDtlList;
	}

	public void setSubmitUncheckOrderDtlList(List<SubmitUncheckOrderDtlDto> submitUncheckOrderDtlList) {
		this.submitUncheckOrderDtlList = submitUncheckOrderDtlList;
	}
	
	
	
}
