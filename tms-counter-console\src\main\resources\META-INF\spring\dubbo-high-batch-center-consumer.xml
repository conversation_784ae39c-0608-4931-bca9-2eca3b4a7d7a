<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - limitations 
	under the License. - See the License for the specific language governing 
	permissions and -->

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 高端柜台购买 -->
	<dubbo:reference id="counterPurchaseFacade" interface="com.howbuy.tms.high.batch.facade.trade.counterpurchase.CounterPurchaseFacade" registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端柜台赎回 -->
	<dubbo:reference id="counterRedeemFacade" interface="com.howbuy.tms.high.batch.facade.trade.counterredeem.CounterRedeemFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端柜台撤单 -->
	<dubbo:reference id="counterCancelFacade" interface="com.howbuy.tms.high.batch.facade.trade.countercancel.CounterCancelFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端柜台强制测单 -->
	<dubbo:reference id="counterForceCancelFacade" interface= "com.howbuy.tms.high.batch.facade.trade.counterforcecancel.CounterForceCancelFacade"  registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端柜台修改分红方式 -->
	<dubbo:reference id="counterModifyDivFacade" interface="com.howbuy.tms.high.batch.facade.trade.countermodifydiv.CounterModifyDivFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端查询柜台订单 -->
	<dubbo:reference id="queryCounterOrderFacade" interface="com.howbuy.tms.high.batch.facade.query.querycounterorder.QueryCounterOrderFacade"  registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端柜台审核 -->
	<dubbo:reference id="counterCheckFacade" interface="com.howbuy.tms.high.batch.facade.trade.countercheck.CounterCheckFacade"   registry="high-batch-center-remote"  check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 高端柜台报表查询 -->
	<dubbo:reference id="queryCounterTradeFacade" interface="com.howbuy.tms.high.batch.facade.query.querycountertrade.QueryCounterTradeFacade"  registry="high-batch-center-remote"  check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 私募柜台收市 -->
	<dubbo:reference id="sm.counterEndFacade"	interface="com.howbuy.tms.high.batch.facade.trade.counterend.CounterEndFacade"  registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 私募柜台收市检查 -->
	<dubbo:reference id="counterEndCheckFacade"	interface="com.howbuy.tms.high.batch.facade.query.counterendcheck.CounterEndCheckFacade"  registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

    <!-- 查询高端订单信息 -->	
	<dubbo:reference id="queryHighFundDealOrderDtlFacade" interface="com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlFacade" registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 查询私募批处理流程节点信息接口 -->
	<dubbo:reference id="sm.querySimuBatchFlowInfoFacade"
					 interface="com.howbuy.tms.high.batch.facade.query.querybatchflowinfo.QueryHighBatchFlowInfoFacade"
					 registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 查询高端TA批处理流程节点信息接口 -->
	<dubbo:reference id="queryHighTaBatchFlowInfoFacade"
					 interface="com.howbuy.tms.high.batch.facade.query.querytabusinessbatchflow.QueryHighTaBatchFlowInfoFacade"
					 registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!--查询批处理状态 -->
	<dubbo:reference id="high.queryBatchFlowStatFacade"
					 interface="com.howbuy.tms.high.batch.facade.query.querybatchflowstat.QueryBatchFlowStatFacade"
					 registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 查询高端系统工作日-->
	<dubbo:reference id="querySimuWorkdayFacade"
					 interface="com.howbuy.tms.high.batch.facade.query.queryworkday.QueryHighWorkdayFacade"
					 registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 查询高端分TA列表 -->
	<dubbo:reference id="queryHighTaBusinessListFacade"
					 interface="com.howbuy.tms.high.batch.facade.query.querytabusinesslist.QueryHighTaBusinessListFacade"
					 registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- TA汇总统计 -->
	<dubbo:reference id="queryTaBusinessBatchCountFacade" interface="com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountFacade" registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- TA不收市 -->
	<dubbo:reference id="queryTaCounterNotEndFacade" interface="com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCounterNotEndFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- TA不收市维护 -->
	<dubbo:reference id="highCounterSaveOrDelNotEndTaFacade" interface="com.howbuy.tms.high.batch.facade.trade.tacounternotend.CounterSaveOrDelNotEndTaFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!--查询滚动赎回参数-->
	<dubbo:reference id="queryExpiredRedeemParamsFacade"  interface="com.howbuy.tms.high.batch.facade.query.queryexpiredredeemparams.QueryExpiredRedeemParamsFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!--查询滚动赎回参数-->
	<dubbo:reference id="queryFixedRedeemReportFacade" interface="com.howbuy.tms.high.batch.facade.query.queryfixedredeemreport.QueryFixedRedeemReportFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	
	<!-- 查询高端交易订单明细  换卡 -->
	<dubbo:reference id="queryHighFundDealOrderDtlChangeCardFacade" interface="com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtlchangecard.QueryHighFundDealOrderDtlChangeCardFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	

	<!--查询未完成复购协议-->
	<dubbo:reference id="tmscounter.queryUnFinishRepurchaseProtocolFacade" interface="com.howbuy.tms.high.batch.facade.query.querycounterendcheck.QueryUnFinishRepurchaseProtocolFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 修改复购协议-->
	<dubbo:reference id="tmscounter.counterModifyRepurchaseProctolFacade" interface="com.howbuy.tms.high.batch.facade.trade.countermodifyrepurchaseproctol.CounterModifyRepurchaseProctolFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 修改复购协议-->
	<dubbo:reference id="counterNoTradeOverAccountFacade" interface="com.howbuy.tms.high.batch.facade.trade.counternotradeoveraccount.CounterNoTradeOverAccountFacade" check="false" registry="high-batch-center-remote">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 高端柜台回款方向 -->
	<dubbo:reference id="counterModifyRefundDirectionFacade" interface="com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 查询股权份额转让订单的接口 -->
	<dubbo:reference id="queryOwnershipRightTransferFacade" interface="com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.QueryOwnershipRightTransferFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 查询股权份额转让订单详情的接口 -->
	<dubbo:reference id="queryOwnershipRightTransferDtlFacade" interface="com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.QueryOwnershipRightTransferDtlFacade" registry="high-batch-center-remote" check="false" timeout="60000" retries="0">
		<dubbo:parameter key="serialization" value="hessian2" />
		<dubbo:parameter key="prefer.serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 股权份额修改申请 -->
	<dubbo:reference id="counterOwnershipRightTransferFacade" interface="com.howbuy.tms.high.batch.facade.trade.counterownershiprighttransferfacade.CounterOwnershipRightTransferFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 修改股权份额转让信息 -->
	<dubbo:reference id="modifyOwnershipRightTransferFace" interface="com.howbuy.tms.high.batch.facade.trade.modifyownershiprighttransferface.ModifyOwnershipRightTransferFace"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 修改股权份额转让信息 -->
	<dubbo:reference id="noTradeUpdateSubscribeAmtInfoFacade" interface="com.howbuy.tms.high.batch.facade.trade.noTradeUpdateSubscribeAmtInfo.NoTradeUpdateSubscribeAmtInfoFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 修改认缴金额申请 -->
	<dubbo:reference id="customerSubsAmtUpdateApplyFacade" interface="com.howbuy.tms.high.batch.facade.trade.customerSubsAmtApply.CustomerSubsAmtUpdateApplyFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>

	<!-- 查询认缴金额变更信息 -->
	<dubbo:reference id="querySubsAmtChangeDetailFacade" interface="com.howbuy.tms.high.batch.facade.query.querySubsAmtChangeDetail.QuerySubsAmtChangeDetailFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<!-- 认缴金额变更信息审核 -->
	<dubbo:reference id="subsAmtChangeCheckFacade" interface="com.howbuy.tms.high.batch.facade.trade.subsAmtChangeCheck.SubsAmtChangeCheckFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>
	<dubbo:reference id="subsAmtApplyUpdateFacade" interface="com.howbuy.tms.high.batch.facade.trade.subsAmtApplyUpdate.SubsAmtApplyUpdateFacade"   registry="high-batch-center-remote" check="false">
		<dubbo:parameter key="serialization" value="hessian2" />
	</dubbo:reference>


</beans>