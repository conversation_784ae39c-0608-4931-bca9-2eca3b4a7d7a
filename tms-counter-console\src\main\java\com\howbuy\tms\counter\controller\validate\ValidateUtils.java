/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.validate;

import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * @className ValidateUtils
 * @description
 * <AUTHOR>
 * @date 2019/6/10 15:21
 */
@Slf4j
public class ValidateUtils {
    public static void validateAppDtm(Date appDtm) {
        if (compareAppDtWithCurrDt(DateUtils.formatToString(appDtm, DateUtils.YYYYMMDD))) {
            throw new TmsCounterException("0003", "申请日期不能小于当前日期");
        }
    }

    /**
     *
     * compareAppDtWithCurrDt:(申请日期与当前日期比较) true:申请日期小于当前日期;false:申请日期大于等于当前日期
     *
     * @param appDt
     * @return
     * <AUTHOR>
     * @date 2017年5月3日 下午2:49:14
     */
    private static boolean compareAppDtWithCurrDt(String appDt) {
        Date currDate = new Date();
        String currDt = DateUtils.formatToString(currDate, DateUtils.YYYYMMDD);
        if (DateUtils.formatToDate(appDt, DateUtils.YYYYMMDD).compareTo(DateUtils.formatToDate(currDt, DateUtils.YYYYMMDD)) < 0) {
            log.info("ValidateUtils-compareAppDtWithCurrDt,appDt={},currDt={}",appDt,currDt);
            return true;
        }
        return false;
    }
}
