/**
 *公共工具类
 *<AUTHOR>
 *@date 2017-03-28 10:47
 */
$(function () {
    CommonUtil.currDate = '';
});

var CommonUtil = {
    getParam: function (name) {
        var search = document.location.search;
        var pattern = new RegExp("[?&]" + name + "\=([^&]+)", "g");
        var matcher = pattern.exec(search);
        var items = null;
        if (null != matcher) {
            try {
                items = decodeURIComponent(decodeURIComponent(matcher[1]));
            } catch (e) {
                try {
                    items = decodeURIComponent(matcher[1]);
                } catch (e) {
                    items = matcher[1];
                }
            }
        }
        if ("null" == items) {
            return null;
        }
        return items;
    },

    getParamJson: function () {
        var uri = document.location.search;
        var querys = uri
            .substring(uri.indexOf('?') + 1)
            .split('&');
        var result = {};
        for (var i = 0; i < querys.length; i++) {
            var temp = querys[i].split('=');
            if (temp.length < 2) {
                result[temp[0]] = '';
            } else {
                result[temp[0]] = temp[1];
            }
        }

        return result;
    },

    clearForm: function (formId) {
        var inputs = $("#" + formId).find('input');
        var selects = $("#" + formId).find('select');
        $(inputs).each(function (index, element) {
            $(element).val('');
        });
        $(selects).each(function (index, element) {
            $(element).val('');
        });

        $(selects).each(function (index, element) {
            if ($(element).attr("multiple") == "multiple") {
                $(element).multiselect("uncheckAll")
            } else {

                $(element).val('');
            }


        });
    },
    /**
     * 金额转大写
     **/
    amtUpper: function (str) {
        if ("--" === str) {
            return "--";
        }
        if ("" === str) {
            return "--";
        }
        // 如果输入不是字符串，将其转换为字符串
        if (typeof str !== 'string') {
            str = String(str); // 将输入转换为字符串
        }
        str = str.replace(/\,/g, '');
        var re = /([0-9]+\.[0-9]{2})[0-9]*/;
        str = str.replace(re, "$1");
        return CommonUtil.digit_uppercase(str);
        ;
    },
    /**
     * 对象为空显示默认值
     * @param obj
     * @param defaultValue
     * @returns
     */
    formatData: function (str, defaultValue) {
        if (CommonUtil.isEmpty(defaultValue)) {
            defaultValue = "";
        }
        if (CommonUtil.isEmpty(str)) {
            return defaultValue;
        } else {
            return str;
        }
    },

    /**
     *
     *字符串为空校验
     */

    isEmpty: function (str) {
        return (str == null || str === "" || str == "undefined" || str == "null");
    },

    /**
     * 获取Map 值
     * @param map
     * @param defValve  默认返回值
     */
    getMapValue: function (map, nameStr, defValve) {

        if (CommonUtil.isEmpty(defValve)) {
            defValve = '';
        }
        var returnValue = defValve;
        $.each(map, function (name, value) {
            if (nameStr == name) {
                returnValue = value;
                return false;
            }
        });

        return returnValue;
    },

    /**
     * 动态生成select options html
     * @param maps  options 集合
     * @defaultSelect 默认选中值
     * @param id   selectId
     */
    selectOptionsHtml: function (maps, defaultSelect, defvalue, defText, notNeedDefaultSelect) {

        var appendHtml = '';
        if (notNeedDefaultSelect) {
            appendHtml = '';
        } else {
            if (isEmpty(defvalue) && isEmpty(defText)) {
                appendHtml = '<option value="">请选择</option>';
            } else {
                appendHtml = '<option value=\"' + defvalue + '\">' + defText + '</option>';
            }
        }
        $.each(maps, function (key, value) {
            if (!isEmpty(key) && !isEmpty(value)) {
                if (!isEmpty(defaultSelect) && defaultSelect == key) {
                    appendHtml += '<option value=\"' + key + '\"  selected>' + value + '</option>';
                } else {
                    appendHtml += '<option value=\"' + key + '\" >' + value + '</option>';
                }

            }

        });

        return appendHtml;
    },

    /**
     * 动态生成select options html
     * @param maps  options 集合
     * @defaultSelect 默认选中值
     * @param id   selectId
     */
    multiSelectDisCodeOptionsHtml: function (maps, defaultSelect, defvalue, defText, notNeedDefaultSelect) {

        var appendHtml = '';
        if (notNeedDefaultSelect) {
            appendHtml = '';
        } else {
            if (isEmpty(defvalue) && isEmpty(defText)) {
                appendHtml = '<option value="">请选择</option>';
            } else {
                appendHtml = '<option value=\"' + defvalue + '\">' + defText + '</option>';
            }
        }
        $.each(maps, function (key, value) {
            if (!isEmpty(key) && !isEmpty(value)) {
                if (!isEmpty(defaultSelect) && defaultSelect == key) {
                    appendHtml += '<option value=\"' + key + '\"  selected>' + value + '</option>';
                } else {
                    appendHtml += '<option value=\"' + key + '\" >' + value + '</option>';
                }

            }

        });

        return appendHtml;
    },
    /**
     * 只显示后面指定位数
     */
    encodeLastNum: function (str, len, enCodeLen, defaultEcode) {
        if (CommonUtil.isEmpty(defaultEcode)) {
            defaultEcode = "*";
        }
        var start = (str.length - len) < 0 ? 0 : (str.length - len);
        var lastStr = CommonUtil.formatSubString(str, start, str.length);
        return "****" + lastStr;
    },
    /**
     * 替换指定位置
     * str  目标字符串
     * start 开始位置
     * enCodeLen 替换字符串长度
     * replaceCode 用来替换的字符
     */
    replaceStrPosition: function (str, start, enCodeLen, replaceCode) {
        if (CommonUtil.isEmpty(str)) {
            return '';
        }
        if (CommonUtil.isEmpty(replaceCode)) {
            replaceCode = "*";
        }
        if (enCodeLen > str.length) {
            enCodeLen = str.length;
        }
        var startStr = CommonUtil.formatSubString(str, 0, start);
        var endStr = CommonUtil.formatSubString(str, start + enCodeLen, str.length);
        var encodeStr = '';
        for (var i = 0; i < enCodeLen; i++) {
            encodeStr += replaceCode;
        }
        return startStr + encodeStr + endStr;
    },

    /**
     * 截取字符串
     * @param str
     * @param start
     * @param ended
     * @returns
     */
    formatSubString: function (str, start, ended) {

        if (CommonUtil.isEmpty(str)) {
            return "";
        }

        var length = str.length;
        if (start > (length)) {
            start = length;
        }

        if (ended > (length)) {
            ended = length;
        }

        return str.substring(start, ended);

    },

    /**
     * 格式化日期
     * @param date
     * @param fmt
     * @returns {String}
     */
    formatDateToStr: function (dateStr, fmt) {
        if (CommonUtil.isEmpty(dateStr)) {
            return "";
        }
        var fmtDate = new Date(dateStr);
        if (fmtDate instanceof Date) {
            return fmtDate.Format(fmt);
        } else {
            return dateStr;
        }

    },
    getNettyAmount: function (amount) {
        if (CommonUtil.isEmpty(amount)) {
            return "";
        }
        if ("--" === amount) {
            return "";
        }
        return amount;
    },
    /**
     * 金额格式转换 111000.00  --> 111,000.00
     * @param obj
     * @param defaultValue
     * @returns {String}
     */
    formatAmount: function (s, defaultVal) {
        if (CommonUtil.isEmpty(defaultVal)) {
            defaultVal = "--";
        }
        if (CommonUtil.isEmpty(s)) {
            return defaultVal;
        }

        if (!isNaN(s)) {
            s = s.toString();
        }

        if (s.indexOf('--') >= 0) {
            return defaultVal;
        }

        if (/[^0-9\.-]/.test(s)) {
            return s;
        }

        s = s.replace(/^((-)?\d*)$/, "$1.");
        s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
        s = s.replace(".", ",");
        var re = /(\d)(\d{3},)/;
        while (re.test(s))
            s = s.replace(re, "$1,$2");
        s = s.replace(/,(\d\d)$/, ".$1");
        return s.replace(/^\./, "0.");
    },

    /**
     * 返原格式化金额 111000.00  <-- 111,000.00
     * @param amount 金额
     * @returns
     */
    unFormatAmount: function (amount) {
        if (CommonUtil.isEmpty(amount)) {
            return '';
        }
        // 如果输入不是字符串，将其转换为字符串
        if (typeof amount !== 'string') {
            amount = String(amount); // 将输入转换为字符串
        }
        return amount.replace(/\,/g, '');
    },

    /**
     * 将金额字符串转换为数值类型
     * @param amount 金额字符串
     * @returns {number} 转换后的数值,如果转换失败返回0
     */
    convertAmountToNumber: function(amount) {
        if (CommonUtil.isEmpty(amount)) {
            return 0;
        }
        // 先去除格式化
        var unformattedAmount = CommonUtil.unFormatAmount(amount);
        // 转换为数值
        var num = parseFloat(unformattedAmount);
        return isNaN(num) ? 0 : num;
    },

    /**
     * 加法
     * 计算方式：1.先将所有的小数乘为整数；
     2.待加减运算执行完之后再除去对应的 m 的值，将其变为小数输出;
     * */
    accAdd: function (arg1, arg2) {
        var r1, r2, m;
        //获取arg1,arg2的小数点后的长度;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2));
        /**需要引入其他的方法
         * return m.div(parseFloat(arg1).mul(m)+parseFloat(arg2).mul(m))
         **/
        return (arg1 * m + arg2 * m) / m;
    },
    /**
     * format 净值
     */
    formatNav: function (s, defaultVal) {
        if (CommonUtil.isEmpty(defaultVal)) {
            defaultVal = "--";
        }
        if (CommonUtil.isEmpty(s)) {
            return defaultVal;
        }
        if (/[^0-9\.-]/.test(s)) {
            return s;
        }

        if (!isNaN(s)) {
            s = s.toString();
        }

        s = s.replace(/^(\d*)$/, "$1.");
        s = (s + "0000").replace(/(\d*\.\d\d\d\d)\d*/, "$1");
        s = s.replace(".", ",");
        var re = /(\d)(\d{5},)/;
        while (re.test(s))
            s = s.replace(re, "$1,$2");
        s = s.replace(/,(\d\d\d\d)$/, ".$1");
        return s.replace(/^\./, "0.");
    },
    /**
     * 小数转百分比
     * @param s
     */
    formatPercent: function (s, defaultVal, precision) {
        if (CommonUtil.isEmpty(defaultVal)) {
            defaultVal = "";
        }
        if (CommonUtil.isEmpty(s)) {
            return defaultVal;
        }
        if (/[^0-9\.-]/.test(s)) {
            return s;
        }

        if (isNaN(s)) {
            return s.toString();
        } else {
            if (!isNaN(precision)) {
                s = (s * 100).toFixed(precision);
            } else {
                s = s * 100;
            }
            return s + "%";
        }
    },

    /**
     * 小数转百分比(无百分号)
     * @param s
     */
    formatPercentNo: function (s, defaultVal, precision) {
        if (CommonUtil.isEmpty(defaultVal)) {
            defaultVal = "";
        }
        if (CommonUtil.isEmpty(s)) {
            return defaultVal;
        }
        if (/[^0-9\.-]/.test(s)) {
            return s;
        }

        if (isNaN(s)) {
            return s.toString();
        } else {
            if (!isNaN(precision)) {
                s = (s * 100).toFixed(precision);
            } else {
                s = s * 100;
            }
            return s;
        }
    },

    multiply: function (s, v, precision) {
        if (/[^0-9\.-]/.test(s)) {
            return s;
        }

        if (isNaN(s) || isNaN(v)) {
            return s.toString();
        } else {
            if (!isNaN(precision)) {
                s = (s * v).toFixed(precision);
            } else {
                s = s * v;
            }
            return s;
        }
    },
    /**
     *金额大小写转换
     * @param n
     */
    digit_uppercase: function (num) {
        part = String(num).split(".");
        var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
        var unit = [['元', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'], ['角', '分']];

        //整数部分
        var intNum = part[0].toString().split("");
        var result = "";
        for (var i = 0; i < intNum.length; i++) {
            var des_i = intNum.length - 1 - i;//倒序排列设值
            result = unit[0][i] + result;
            var digit_index = intNum[des_i];
            result = digit[digit_index] + result;
        }
        //将【零千、零百】换成【零】 【十零】换成【十】
        result = result.replace(/零(仟|佰|拾)/g, '零').replace(/拾零/g, '拾');
        //合并中间多个零为一个零
        result = result.replace(/零+/g, '零');
        //将【零亿】换成【亿】【零万】换成【万】
        result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
        //将【亿万】换成【亿】
        result = result.replace(/亿万/g, '亿');
        //移除末尾的零
        result = result.replace(/零+元$/, '元');
        //将【零一十】换成【零十】
        //result = result.replace(/零一拾/g, '零拾');//貌似正规读法是零一十
        //将【一十】换成【十】
        result = result.replace(/^壹拾/g, '拾');
        //将【0】换成【零元】
        result = result.replace(/^元$/, '零元');

        var newchar = '';
        //小数点之后进行转化,只保留2位小数
        if (String(num).indexOf(".") != -1) {
            if (part[1].length > 2) {
                part[1] = part[1].substr(0, 2);
            }
            for (i = 0; i < part[1].length; i++) {

                var perchar = part[1].charAt(i);
                newchar = newchar + digit[perchar] + unit[1][i];
            }
        }

        return result + newchar;
    },

    /**
     *份额数字大小写转换
     * @param n
     */
    digit_vol_uppercase: function (num, volUnit) {
        volUnit = volUnit || '份';
        part = String(num).split(".");
        var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
        var unit = [['', '万', '亿'], ['', '拾', '佰', '仟']];
        var s = '';
        var n = part[0];
        for (var i = 0; i < unit[0].length && n > 0; i++) {
            var p = '';
            for (var j = 0; j < unit[1].length && n > 0; j++) {
                var divModulo = n % 10;
                if (divModulo != 0) {
                    p = digit[divModulo] + unit[1][j] + p;
                }
                n = Math.floor(n / 10);
            }
            s = p + unit[0][i] + s;
        }
        var newchar = '';
        //小数点之后进行转化,只保留2位小数
        if (String(num).indexOf(".") != -1) {
            newchar = "点";
            if (part[1].length > 2) {
                part[1] = part[1].substr(0, 2);
            }
            for (i = 0; i < part[1].length; i++) {

                var perchar = part[1].charAt(i);
                newchar = newchar + digit[perchar];
            }
        }

        if (CommonUtil.isEmpty(s + newchar)) {
            return '';
        } else {
            return s + newchar + volUnit;
        }

    },
    /**
     * 金额转大写
     **/
    amtUpper: function (str) {
        if ("--" === str) {
            return "--";
        }
        if ("" === str) {
            return "--";
        }
        // 如果输入不是字符串，将其转换为字符串
        if (typeof str !== 'string') {
            str = String(str); // 将输入转换为字符串
        }
        str = str.replace(/\,/g, '');
        var re = /([0-9]+\.[0-9]{2})[0-9]*/;
        str = str.replace(re, "$1");
        return CommonUtil.digit_uppercase(str);
        ;
    },

    /**
     * 提示框
     * @param msg
     * @param timeOut
     * @param iconIndex  图标
     */
    layer_tip: function (msg, timeOut, iconIndex) {
        if (CommonUtil.isEmpty(timeOut)) {
            timeOut = 2000;
        }
        if (CommonUtil.isEmpty(iconIndex)) {
            iconIndex = 6;
        }
        layer.msg(msg, {
            time: timeOut,
            icon: iconIndex
        });
    },

    layer_tip_four: function (msg, timeOut, iconIndex) {
        if (CommonUtil.isEmpty(timeOut)) {
            timeOut = 6000;
        }
        if (CommonUtil.isEmpty(iconIndex)) {
            iconIndex = 6;
        }
        layer.msg(msg, {
            time: timeOut,
            icon: iconIndex
        });
    },

    layer_open: function (title, contentId, yesCallBack) {
        layer.open({
            type: 1 //Page层类型
            , area: ['750px', '580px']
            , title: title
            , shade: 0.6 //遮罩透明度
            , maxmin: true //允许全屏最小化
            , anim: 1 //0-6的动画形式，-1不开启
            , content: $("#" + contentId)
            , btn: ['确定']
            , zIndex: 19891015
            , yes: function (index, layero) {
                //调用新增的方法
                var result = yesCallBack();
                if (result) {
                    layer.close(index);
                }
            }
        });
    },

    layer_open_content: function (title, content) {
        layer.open({
            type: 1 //Page层类型
            , area: ['750px', '580px']
            , title: title
            , shade: 0.6 //遮罩透明度
            , maxmin: true //允许全屏最小化
            , anim: 1 //0-6的动画形式，-1不开启
            , content: content
            , btn: ['确定']
            , zIndex: 19891015
            , yes: function (index, layero) {
                //调用新增的方法
                layer.close(index);
            }
        });
    },
    /**
     * 全选或反选
     * @param allId
     */
    checkedOrUncheckedAll: function (allId) {
        var checked = $("#" + allId).is(':checked');
        if (checked) {
            $("input[type='checkbox']").each(function (index, element) {
                if ($(element).attr("id") != allId) {
                    $(element).prop("checked", true);
                }

            });
        } else {
            $("input[type='checkbox']").each(function (index, element) {
                if ($(element).attr("id") != allId) {
                    $(element).prop("checked", false);
                }
            });
        }
    },
    //初始化产品筛选
    mutiSelect: function (selectId, reqUrl) {
        var paramters = {};
        paramters.uri = reqUrl;
        paramters.isAsync = true;

        ajaxAndCallBack(paramters, function (data) {
            var valList = data.list || [];
            var optionList = [];
            if (valList.length > 0) {
                $(valList).each(function (index, element) {
                    var optionValue = element.code || '';
                    var optionDesc = optionValue + '-' + element.name || '';
                    optionList.push('<option value="' + optionValue + '">' + optionDesc + '</option>');
                });
            }

            var optionHtml = optionList.join('');
            $("#" + selectId).html(optionHtml);

            // 初始化多选框插件
            $("#" + selectId).multiselect({
                checkAllText: '全选',
                uncheckAllText: '取消',
                noneSelectedText: "请选择"
            }).multiselectfilter({
                label: "过滤",
                placeholder: "代码或简称"
            });
        });
    },

    //初始化产品筛选
    singleSelect: function (selectId, reqUrl, productChannel) {
        var paramters = {};
        paramters.uri = reqUrl;
        paramters.isAsync = true;
        var reqparamters = {"productChannel": productChannel};
        paramters.reqparamters = reqparamters;

        ajaxAndCallBack(paramters, function (data) {
            var valList = data.list || [];
            var optionList = [];
            if (valList.length > 0) {
                $(valList).each(function (index, element) {
                    var optionValue = element.code || '';
                    var optionDesc = optionValue + '-' + element.name || '';
                    optionList.push('<option value="' + optionValue + '">' + optionDesc + '</option>');
                });
            }

            var optionHtml = optionList.join('');
            $("#" + selectId).html(optionHtml);

            // 初始化多选框插件
            $("#" + selectId).multiselect({
                checkAllText: '全选',
                uncheckAllText: '取消',
                noneSelectedText: "请选择",
                showCheckAll: false,
                showUncheckAll: false,
                multiple: false
            }).multiselectfilter({
                label: "过滤",
                placeholder: "代码或简称"
            });
        });
    },

    //初始化产品筛选,值包含基金简称
    singleSelectForRefund: function (selectId, reqUrl, productChannel) {
        var paramters = {};
        paramters.uri = reqUrl;
        paramters.isAsync = true;
        var reqparamters = {"productChannel": productChannel};
        paramters.reqparamters = reqparamters;

        ajaxAndCallBack(paramters, function (data) {
            var valList = data.list || [];
            var optionList = [];
            if (valList.length > 0) {
                $(valList).each(function (index, element) {
                    var optionValue = element.code || '';
                    var optionDesc = optionValue + '-' + element.name || '';
                    optionList.push('<option value="' + optionDesc + '">' + optionDesc + '</option>');
                });
            }

            var optionHtml = optionList.join('');
            $("#" + selectId).html(optionHtml);

            // 初始化多选框插件
            $("#" + selectId).multiselect({
                checkAllText: '全选',
                uncheckAllText: '取消',
                noneSelectedText: "请选择",
                showCheckAll: false,
                showUncheckAll: false,
                multiple: false
            }).multiselectfilter({
                label: "过滤",
                placeholder: "代码或简称"
            });
        });
    },

    arrToStr: function (paramList) {
        if (isEmptyList(paramList)) {
            return '';
        }
        return paramList.join(',');
    },
    /**
     * 获取当前服务器端日期
     */
    getWorkDay: function () {
        var currDate = '';
        var uri = TmsCounterConfig.QUERY_CURR_WORK_DAY_URL;
        var paramters = CommonUtil.buildReqParams(uri);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                currDate = body.currDate || '';
                $("#appDt").val(currDate);
            }
        });
        return currDate;
    },

    /**
     * 获取当前服务器端日期
     */
    getHighWorkDay: function () {
        var currDate = '';
        var uri = TmsCounterConfig.QUERY_HIGH_CURR_WORK_DAY_URL;
        var paramters = CommonUtil.buildReqParams(uri);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                currDate = body.workDay || '';
                $("#appDt").val(currDate);
            }
        });
        return currDate;
    },

    /**

     /**
     * alert提示框
     * @param msg
     * @param skin
     */
    layer_alert: function (msg, skin) {
        if (CommonUtil.isEmpty(skin)) {
            skin = 'layui-layer-lan';
        }
        layer.alert(msg,
            {
                skin: skin,
                closeBtn: 0
            }
        );
    },

    layerConfrim: function (title, context, area, yesCallBack, noCallBack, cancelCallBack) {
        var area = area || ['200px', '200px'];
        layer.open({
            type: 1,
            title: title,
            content: context,
            area: area,
            btn: ["确定", "取消"],
            yes: function (index, layero) {
                // yes callBack
                if ($.isFunction(yesCallBack)) {
                    yesCallBack();
                }

                layer.close(index);
            },
            btn2: function (index, layero) {
                // no callBack
                if ($.isFunction(noCallBack)) {
                    noCallBack();
                }

                layer.close(index);

            },
            cancel: function (index, layero) {
                // close callBack
                if ($.isFunction(cancelCallBack)) {
                    cancelCallBack();
                }
                layer.close(index);
            }
        });
    },


    /**
     *
     * 禁用按钮
     * @param btnId
     */
    disabledBtn: function (btnId) {
        $("#" + btnId).addClass("disabled");
        $("#" + btnId).attr("disabled", true);

    },

    /**
     * 执行成功按钮
     * @param btn obj
     */
    succBtn: function (btn) {
        btn.addClass("disabled").addClass("btn-danger");
        btn.attr("disabled", true);
    },

    /**
     *
     * 禁用按钮
     * @param btnId
     */
    disabledBtnWithClass: function (btnClass) {
        $("." + btnClass).addClass("disabled");
        $("." + btnClass).attr("disabled", true);

    },

    /**
     * 启用按钮
     */
    enabledBtn: function (btnId) {
        $("#" + btnId).removeClass("disabled");
        $("#" + btnId).attr("disabled", false);

    },

    /**
     * 禁用所有input
     */
    disableAllInput: function () {

        $(':input').each(function (index, element) {
            CommonUtil.disableddObj(element);
        });

    },

    /**
     * 启用form表单下的所有input
     */
    enableInputsOfForm: function (formId) {

        $("#" + formId).find("input").each(function (index, element) {
            CommonUtil.enabledObj(element);
        });


    },

    /**
     * 禁用obj
     * @param obj
     */
    disableddObj: function (obj) {
        $(obj).attr('disabled', true);
    },

    /**
     * 启用obj
     * @param obj
     */
    enabledObj: function (obj) {
        $(obj).attr('disabled', false);
    },
    /**
     * 启用列表
     * @param objList
     */
    enabledList: function (objList) {
        objList = objList || [];

        if (objList.length > 0) {
            $(objList).each(function (index, element) {
                CommonUtil.enabledObj(element);
            });
        }
    },

    // "0":"全部赎回";"1": "部分赎回";"2", "全部复购"
    getRepurchaseType: function (repurchaseVol, balanceVol) {
        if (CommonUtil.isEmpty(repurchaseVol) || repurchaseVol === 0) {
            return "0";
        } else if (repurchaseVol == balanceVol) {
            return "2";
        } else {
            return "1"
        }
    },
    /**
     *
     * @param formId  表单Id
     * @param excludeNameList 排除的列表
     */
    removerReadOnly: function (formId, excludeInputs) {
        excludeInputs = excludeInputs || [];
        $("#" + formId).find("input").each(function (index, element) {
            var elementName = $(element).attr('name');
            if (excludeInputs.length > 0) {
                for (var i = 0; i < excludeInputs.length; i++) {
                    if (elementName != excludeInputs[i]) {
                        $(element).removeAttr("readonly");
                    }
                }
            }

        });

        $("#" + formId).find("select").each(function (index, element) {
            var elementName = $(element).attr('name');
            if (excludeInputs.length > 0) {
                for (var i = 0; i < excludeInputs.length; i++) {
                    if (elementName != excludeInputs[i]) {
                        $(element).removeAttr("readonly");
                    }
                }
            }
        });
    },

    /**
     * 输入框只读
     */
    addReadOnlyAllInput: function (formId) {
        $("#" + formId).find("input").each(function (index, element) {
            $(element).attr("readonly", "readonly");
        });

    },

    /**
     * 隐藏div下的所有btn
     * @param divId
     */
    higdeAllBtnOfDiv: function (divId) {
        $("#" + divId).find(".btn").hide();
    },
    /**
     * 构建请求参数
     */
    buildReqParams: function (uri, reqparamters, isAsync, method, timeout) {
        var req = {};
        req.uri = uri || '';
        req.reqparamters = reqparamters;
        req.isAsync = isAsync || false;
        req.method = method || 'get';
        req.timeout = timeout || 10 * 60 * 1000;
        return req;
    },
    /**
     * 构建请求url
     * @param baseUrl
     * @param subUrl
     * @returns
     */
    buildReqUri: function (baseUrl, subUrl) {
        return baseUrl + subUrl;
    },
    /**
     * 间隔时间刷新url
     */
    reloadUrl: function (timeout) {

        if (CommonUtil.isEmpty(timeout)) {
            timeout = 3000;
        }

        setTimeout(function () {
            window.location.replace(location.href);
        }, timeout);

    },

    /**
     * 刷新父页面
     */
    reloadParentUrl: function () {
        self.window.opener.locaction.reload();
        window.location.href = window.location.href;
        window.opener.location = window.opener.location;
    },
    /**
     *关闭当前页面
     */
    closeCurrentUrl: function () {
        window.opener = null;
        window.open('', '_self');
        window.close();
    },

    /**
     * 重置form
     * @param formId 表单ID
     * @param excludeInputs 不清空的输入框集合
     */
    cleanForm: function (formId, excludeInputs) {
        excludeInputs = excludeInputs || [];
        $("#" + formId).find("input").each(function (index, element) {
            var elementName = $(element).attr('name');
            if (excludeInputs.length > 0 && excludeInputs.includes(elementName)) {
                // 不清空
            } else {
                $(element).val("");
            }

        });

        $("#" + formId).find("select").each(function (index, element) {
            $(element).val("");
        });
    },

    /**
     * 初始化只读文本
     */
    initReadeText: function (className) {
        $("." + className).html('');
    },
    /**
     * 小数校验
     * @param num
     * @returns
     */
    validFloat: function (num) {
        if (CommonUtil.isEmpty(num)) {
            return true;
        }
        var reg = new RegExp("^(([0-9]{1,12}\\.[0-9]{0,2})|([0-9]{1,12}))$");
        return reg.test(num);
    },
    /**
     * 时间格式 hhmmss 校验
     */
    timeValid: function (timsStr) {
        if (CommonUtil.isEmpty(timsStr)) {
            return false;
        }
        // 时间格式正则
        var checkReg = new RegExp('^(([2]{1}[0-3])|([0-1][0-9]))([0-5][0-9])([0-5][0-9])$');
        return checkReg.test(timsStr);

    },

    /**
     * 异步请求和处理
     * paramters 请求参数
     * processData 处理函数
     */
    ajaxAndCallBack: function (paramters, callBack, completeFun) {
        var uri = paramters.uri;
        var method = paramters.method || "";
        var isAsync = paramters.isAsync || false;
        var timeout = paramters.timeout || '';
        if (CommonUtil.isEmpty(timeout)) {
            timeout = 1000 * 60 * 10;
        }
        var reqparamters = paramters.reqparamters || {};
        if (CommonUtil.isEmpty(method)) {
            method = "post";
        }
        var needLoad = paramters.needLoad || true;
        if (needLoad) {
            index = layer.load(0, {shade: [0.1, '#fff']});
        }
        $.ajax({
            url: uri,
            type: method,
            async: isAsync,
            cache: false,
            dataType: 'jsonp',
            timeout: timeout,
            data: reqparamters,
            jsonp: 'callbackfun',
            success: function (data) {
                if (needLoad) {
                    layer.close(index);
                }
                callBack(data);

            },
            error: function (XMLHttpRequest, status, error) {
                if (needLoad) {
                    layer.close(index);
                }
                if (status == 'timeout') {
                    CommonUtil.layer_tip("请求超时。处理超过" + (timeout / (1000 * 60)) + '分钟');
                } else {
                    CommonUtil.layer_tip('系统异常');
                }

            },
            complete: function () {
                if (needLoad) {
                    layer.close(index);
                }
                if ($.isFunction(completeFun)) {
                    completeFun();
                }

            }
        });
    },
    isSucc: function (respCode) {
        if (respCode == '0000' || respCode == '0000000' || respCode == '********') {
            return true;
        } else {
            return false;
        }
    },


    /**
     * 睡眠等待
     * @param ms
     */
    sleep: function (ms) {
        let start = Date.now()
        let end = start + ms
        while (true) {
            if (Date.now() > end) {
                return
            }
        }
    },


    /**
     * 异步请求和处理
     * paramters 请求参数
     * processData 处理函数
     */
    getCurrentFundInfoData: function (paramters) {
        var uri = paramters.uri;
        var method = paramters.method || "";
        var isAsync = paramters.isAsync || false;
        var timeout = paramters.timeout || '';
        if (CommonUtil.isEmpty(timeout)) {
            timeout = 1000 * 60 * 10;
        }
        var reqparamters = paramters.reqparamters || {};
        if (CommonUtil.isEmpty(method)) {
            method = "post";
        }
        var needLoad = paramters.needLoad || true;
        if (needLoad) {
            index = layer.load(0, {shade: [0.1, '#fff']});
        }
        var _data
        $.ajax({
            url: uri,
            type: method,
            async: isAsync,
            cache: false,
            dataType: 'jsonp',
            timeout: timeout,
            data: reqparamters,
            jsonp: 'callbackfun',
            success: function (data) {
                if (needLoad) {
                    layer.close(index);
                }
                _data = data;

            },
            error: function (XMLHttpRequest, status, error) {
                if (needLoad) {
                    layer.close(index);
                }
                if (status == 'timeout') {
                    CommonUtil.layer_tip("请求超时。处理超过" + (timeout / (1000 * 60)) + '分钟');
                } else {
                    CommonUtil.layer_tip('系统异常');
                }

            },
        });
        return _data;
    },


    /**
     * 判断一个对象是否是数组，参数不是对象或者不是数组，返回false
     *
     *  如果浏览器支持可直接用Array.isArray(arg)
     * @param {Object} arg 需要测试是否为数组的对象
     * @return {Boolean} 传入参数是数组返回true，否则返回false
     */
    isArray: function (arg) {
        if (typeof arg === 'object') {
            return Object.prototype.toString.call(arg) === '[object Array]';
        }
        return false;
    },

    /**
     * ajax分页
     * @param uri  查询uri
     * @param curr 当前页
     * @@param processView 分页数据渲染
     */
    ajaxPaging: function (uri, reqdata, processView, pageView, completeFun, layout) {
        //加载中
        var index = layer.load();
        var reqdata = reqdata || {};
        reqdata.ajax = "true";
        if (CommonUtil.isEmpty(pageView)) {
            pageView = "pageView";
        }
        if (isEmptyList(layout)) {
            layout = ['prev', 'page', 'next', 'skip'];
        }
        var limits = [10, 20, 30, 40, 50];
        CommonUtil.ajaxAndCallBack(reqdata, function (data) {
            var code = data.code || "";
            var desc = data.desc || "";
            var body = data.body || {};
            if (CommonUtil.isSucc(code)) {
                // 显示分页
                laypage({
                    cont: pageView, // 容器。值支持id名、原生dom对象，jquery对象。【如该容器为】：<div id="page1"></div>
                    pages: body.totalPage, // 通过后台拿到的总页数
                    curr: body.pageNum || 1, // 当前页
                    skin: 'molv',
                    first: 1, //将首页显示为数字1,。若不显示，设置false即可
                    last: body.totalPage, //将尾页显示为总页数。若不显示，设置false即可
                    skip: true, //是否开启跳页
                    limits: limits,//每页条数的选择项
                    layout: layout,//自定义排版
                    limit: body.pageSize,//每页显示的条数
                    jump: function (obj, first) { // 触发分页后的回调
                        if (!first) { // 点击跳页触发函数自身，并传递当前页：obj.curr
                            var reqparamters = reqdata.reqparamters || {};
                            reqparamters.page = obj.curr;
                            reqdata.reqparamters = reqparamters;
                            CommonUtil.ajaxPaging(uri, reqdata, processView);
                        }
                        obj.pages = obj.last = body.totalPage; //重新获取总页数，一般不用写
                        processView(body);
                    }
                });
            } else {
                CommonUtil.layer_tip(desc + '(' + code + ')');
            }
        });
    },

    numAdd: function (num1, num2) {
        var baseNum, baseNum1, baseNum2;
        try {
            baseNum1 = num1.toString().split(".")[1].length;
        } catch (e) {
            baseNum1 = 0;
        }
        try {
            baseNum2 = num2.toString().split(".")[1].length;
        } catch (e) {
            baseNum2 = 0;
        }
        baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
        return (num1 * baseNum + num2 * baseNum) / baseNum;
    },
    buildRedeemExpireHtml: function () {
        return ' <span class="select-box inline">\n' +
            '            <select name="isRedeemExpire" class="select selectIsRedeemExpire">\n' +
            '            <option value="1">是</option>\n' +
            '            <option value="0">否</option>\n' +
            '            </select>\n' +
            '            </span>'

    }


};

var cookie = {
    set: function (key, val, time) {//设置cookie方法
        var date = new Date(); //获取当前时间
        var expiresDays = time;  //将date设置为n天以后的时间
        date.setTime(date.getTime() + expiresDays * 24 * 3600 * 1000); //格式化为cookie识别的时间
        document.cookie = key + "=" + val + ";expires=" + date.toGMTString();  //设置cookie
    },
    get: function (key) {//获取cookie方法
        /*获取cookie参数*/
        var getCookie = document.cookie.replace(/[ ]/g, "");  //获取cookie，并且将获得的cookie格式化，去掉空格字符
        var arrCookie = getCookie.split(";")  //将获得的cookie以"分号"为标识 将cookie保存到arrCookie的数组中
        var tips;  //声明变量tips
        for (var i = 0; i < arrCookie.length; i++) {   //使用for循环查找cookie中的tips变量
            var arr = arrCookie[i].split("=");   //将单条cookie用"等号"为标识，将单条cookie保存为arr数组
            if (key == arr[0]) {  //匹配变量名称，其中arr[0]是指的cookie名称，如果该条变量为tips则执行判断语句中的赋值操作
                tips = arr[1];   //将cookie的值赋给变量tips
                break;   //终止for循环遍历
            }
            return tips;
        }
    },

    deleteCookie: function (key) { //删除cookie方法
        var date = new Date(); //获取当前时间
        date.setTime(date.getTime() - 10000); //将date设置为过去的时间
        document.cookie = key + "=v; expires =" + date.toGMTString();//设置cookie
    }
}


Date.prototype.Format = function (fmt) {
    var o = {
        "y+": this.getFullYear(),
        "M+": this.getMonth() + 1,                 //月份
        "d+": this.getDate(),                    //日
        "h+": this.getHours(),                   //小时
        "m+": this.getMinutes(),                 //分
        "s+": this.getSeconds(),                 //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S+": this.getMilliseconds()             //毫秒
    };
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            if (k == "y+") {
                fmt = fmt.replace(RegExp.$1, ("" + o[k]).substr(4 - RegExp.$1.length));
            } else if (k == "S+") {
                var lens = RegExp.$1.length;
                lens = lens == 1 ? 3 : lens;
                fmt = fmt.replace(RegExp.$1, ("00" + o[k]).substr(("" + o[k]).length - 1, lens));
            } else {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
    }
    return fmt;
};
