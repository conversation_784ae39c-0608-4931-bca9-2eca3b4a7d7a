/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;
/**
 * @description:(无纸化下单dto) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年11月30日 下午2:47:33
 * @since JDK 1.6
 */
public class SubscriptOrderDto {
    /**
     * 风险确认标识
     */
    private  String riskFlag;
    /**
     * 是否默认回储蓄罐
     */
    private  String isDefautCxg;
    
    public String getRiskFlag() {
        return riskFlag;
    }
    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }
    public String getIsDefautCxg() {
        return isDefautCxg;
    }
    public void setIsDefautCxg(String isDefautCxg) {
        this.isDefautCxg = isDefautCxg;
    }
    
    
}

