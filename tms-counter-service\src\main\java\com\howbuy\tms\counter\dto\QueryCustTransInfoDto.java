/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(查询客户经办人信息) 
 * @reason:
 * <AUTHOR>
 * @date 2018年4月12日 下午7:56:25
 * @since JDK 1.6
 */
public class QueryCustTransInfoDto implements Serializable{
   /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;

    /**
     * 客户号
     */
    private String custNo = "";

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 投资者类型
     */
    private String invstType;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 客户名
     */
    private String custName;

    /**
     * 客户状态
     */
    private String custStat;

    /**
     * 证件有效起始日
     */
    private String idValidityStart;

    /**
     * 证件有效结束日
     */
    private String idValidityEnd;

    /**
     * 长期有效标志
     */
    private String alwaysFlag;

    /**
     * 经办人
     */
    private String linkMan;

    /**
     * 经办人证件类型
     */
    private String linkIdType;

    /**
     * 经办人证件号码
     */
    private String linkIdNo;

    /**
     * 经办人证件有效结束日
     */
    private String linkIdValidityEnd;

    /**
     * 经办人长期有效标志
     */
    private String linkIdAlwaysFlag;

    /**
     * 经办人识别方式
     */
    private String linkMethod;

    /**
     * 经办人电话
     */
    private String linkTel;

    /**
     * 法人代表证件号码
     */
    private String corpIdNo;

    /**
     * 法人代表证件类型
     */
    private String corpIdType;

    /**
     * 法人代表证件有效结束日
     */
    private String corpIdValidityEnd;

    /**
     * 法人代表长期有效标志
     */
    private String corpIdAlwaysFlag;

    /**
     * 法人代表名称
     */
    private String corporation;

    /**
     * 登记日期
     */
    private String regDt;

    /**
     * 开户网点代码
     */
    private String regOutletCode;

    /**
     * 开户交易渠道
     */
    private String regTradeChan;

    /**
     * 开户分销机构代码
     */
    private String regDisCode;
    
    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 一账通账号：当账通账号状态为关闭时，返回空
     */
    private String hboneNo;

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustStat() {
        return custStat;
    }

    public void setCustStat(String custStat) {
        this.custStat = custStat;
    }

    public String getIdValidityStart() {
        return idValidityStart;
    }

    public void setIdValidityStart(String idValidityStart) {
        this.idValidityStart = idValidityStart;
    }

    public String getIdValidityEnd() {
        return idValidityEnd;
    }

    public void setIdValidityEnd(String idValidityEnd) {
        this.idValidityEnd = idValidityEnd;
    }

    public String getAlwaysFlag() {
        return alwaysFlag;
    }

    public void setAlwaysFlag(String alwaysFlag) {
        this.alwaysFlag = alwaysFlag;
    }

    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    public String getLinkIdType() {
        return linkIdType;
    }

    public void setLinkIdType(String linkIdType) {
        this.linkIdType = linkIdType;
    }

    public String getLinkIdNo() {
        return linkIdNo;
    }

    public void setLinkIdNo(String linkIdNo) {
        this.linkIdNo = linkIdNo;
    }

    public String getLinkIdValidityEnd() {
        return linkIdValidityEnd;
    }

    public void setLinkIdValidityEnd(String linkIdValidityEnd) {
        this.linkIdValidityEnd = linkIdValidityEnd;
    }

    public String getLinkIdAlwaysFlag() {
        return linkIdAlwaysFlag;
    }

    public void setLinkIdAlwaysFlag(String linkIdAlwaysFlag) {
        this.linkIdAlwaysFlag = linkIdAlwaysFlag;
    }

    public String getLinkMethod() {
        return linkMethod;
    }

    public void setLinkMethod(String linkMethod) {
        this.linkMethod = linkMethod;
    }

    public String getLinkTel() {
        return linkTel;
    }

    public void setLinkTel(String linkTel) {
        this.linkTel = linkTel;
    }

    public String getCorpIdNo() {
        return corpIdNo;
    }

    public void setCorpIdNo(String corpIdNo) {
        this.corpIdNo = corpIdNo;
    }

    public String getCorpIdType() {
        return corpIdType;
    }

    public void setCorpIdType(String corpIdType) {
        this.corpIdType = corpIdType;
    }

    public String getCorpIdValidityEnd() {
        return corpIdValidityEnd;
    }

    public void setCorpIdValidityEnd(String corpIdValidityEnd) {
        this.corpIdValidityEnd = corpIdValidityEnd;
    }

    public String getCorpIdAlwaysFlag() {
        return corpIdAlwaysFlag;
    }

    public void setCorpIdAlwaysFlag(String corpIdAlwaysFlag) {
        this.corpIdAlwaysFlag = corpIdAlwaysFlag;
    }

    public String getCorporation() {
        return corporation;
    }

    public void setCorporation(String corporation) {
        this.corporation = corporation;
    }

    public String getRegDt() {
        return regDt;
    }

    public void setRegDt(String regDt) {
        this.regDt = regDt;
    }

    public String getRegOutletCode() {
        return regOutletCode;
    }

    public void setRegOutletCode(String regOutletCode) {
        this.regOutletCode = regOutletCode;
    }

    public String getRegTradeChan() {
        return regTradeChan;
    }

    public void setRegTradeChan(String regTradeChan) {
        this.regTradeChan = regTradeChan;
    }

    public String getRegDisCode() {
        return regDisCode;
    }

    public void setRegDisCode(String regDisCode) {
        this.regDisCode = regDisCode;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }
    
}

