/***
*定期产品信息
*<AUTHOR>
*@date 2018-06-21 10:16
 */

$(function(){
	QueryProductInfo.productInfo ={};
});

var QueryProductInfo ={
		
		/**
		 * 查询定期产品信息
		 * 
		 * productId 产品代码 需要传
		 */
		queryProductInfo:function(productId){

			if(!productId){
                CommonUtil.layer_tip("产品代码不能为空");
			} else {
                var uri= TmsCounterConfig.QUERY_REGULAR_PRODUCT_INFO_URL ||  {};
                var reqparamters = {"productId":productId};
                var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
                CommonUtil.ajaxAndCallBack(paramters, QueryProductInfo.queryProductInfoCallBack);
			}
		},
		
		/**
		 * 处理定期产品信息
		 */
		queryProductInfoCallBack:function(data){

			var bodyData = data.body || {};
			var productInfo = bodyData.productInfo || {};
			QueryProductInfo.productInfo = productInfo;

			var isCommonFund = QueryProductInfo.checkProductInfo(productInfo);
			
			if(!isCommonFund){
				return false;
			}
			
			if($("#productName").length > 0){
				$("#productName").html(productInfo.productAbbrName || '');
			}

			if($("#productRiskLevel").length > 0){
				$("#productRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}
			
			if($("#productStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, productInfo.issueState));
			}
		},
		checkProductInfo:function(productInfo){
			if(CommonUtil.isEmpty(productInfo.productId)){
				CommonUtil.layer_tip("没有查询到此产品");
				return false;
			}
			
			if('0' != productInfo.issueState && '1' != productInfo.issueState){
				CommonUtil.layer_tip("此产品不开放认申购");
			}

			return true;
		}


}