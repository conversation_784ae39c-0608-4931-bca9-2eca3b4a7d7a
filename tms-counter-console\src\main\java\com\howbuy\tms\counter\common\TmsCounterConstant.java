/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;


import java.util.*;

/**
 * @description:(柜台自定义常量) 
 * <AUTHOR>
 * @date 2017年4月6日 下午5:33:29
 * @since JDK 1.6
 */
@SuppressWarnings("unchecked")
public class TmsCounterConstant {
    /**
     * 操作员信息缓存
     */
    public static final String SESSION_OPERATORINFO = "session_operatorinfo";
    
    /**
     * 普通公募版本
     */
    public static final String VERSION_TYPE_GONGMU = "0";
    /** 
     * 专户及私募版本
     */
    public static final String VERSION_TYPE_HIGH = "1";
    /**
     * 好买分销机构号
     */
    public static final String HOWBUY_DISCODE= "HB000A001";
    /**
     * 高端系统工作日
     */
    public static final String HIGH_SYS_CODE= "90";
    
    /**
     * 产品通道和批处理系统码对应
     */
    public static final Map<String, String> PRODUCT_CHANNEL_AND_SYS_CODE_MAP;

    static{
        Map<String,String> map= new HashMap<>();
        /**
         * 高端私募
         */
        map.put("3", "90");
        /**
         * 普通公募
         */
        map.put("5", "91");
        /**
         * 高端公募
         */
        map.put("6", "90");
        /**
         * TP私募
         */
        map.put("7", "90");
        PRODUCT_CHANNEL_AND_SYS_CODE_MAP = Collections.unmodifiableMap(map);
    }

    /**
     * 购买
     */
    public static final String CRM_TRADE_TYPE_BUY = "0";
    /**
     * 赎回
     */
    public static final String CRM_TRADE_TYPE_SELL = "1";
    /**
     * 修改分红方式
     */
    public static final String CRM_TRADE_TYPE_MODIFYDIV = "2";
    /**
     * 撤单
     */
    public static final String CRM_TRADE_TYPE_CANCEL = "3";

    public static final Map<String, String> TXCODEANDCRMTRADETYPE;
    static {
        Map<String,String> map = new HashMap<>();
        map.put("Z900011", CRM_TRADE_TYPE_BUY);
        map.put("Z900012", CRM_TRADE_TYPE_SELL);
        map.put("Z900013", CRM_TRADE_TYPE_MODIFYDIV);
        map.put("Z900014", CRM_TRADE_TYPE_CANCEL);
        map.put("Z900015", CRM_TRADE_TYPE_CANCEL);
        TXCODEANDCRMTRADETYPE = Collections.unmodifiableMap(map);

    }

    public static final List<String> ONLINE_CHANGE_CARD_FILE_TYPE;
    static {
        List<String> list = new ArrayList<String>(4);
        list.add("jpeg");
        list.add("png");
        list.add("jpg");
        list.add("gif");
        ONLINE_CHANGE_CARD_FILE_TYPE = Collections.unmodifiableList(list);
    }

    public static final List<String> ONLINE_CHANGE_CARD_FILE_VIDEO_TYPE;
    static {
        List<String> list = new ArrayList<String>(4);
        list.add("avi");
        list.add("mp4");
        list.add("wmv");
        list.add("mov");
        list.add("flv");
        list.add("mpeg");
        list.add("webm");
        ONLINE_CHANGE_CARD_FILE_VIDEO_TYPE = Collections.unmodifiableList(list);
    }
}

