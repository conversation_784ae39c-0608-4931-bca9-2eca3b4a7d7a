/**
*零售柜台---交易申请查询
*<AUTHOR>
*@date 2017-09-18 15:23
*/
$(function(){
	Init.init();
	QueryTradeApply.init();	
	QueryTradeApply.custInfo = {};
	QueryTradeApply.checkOrders = [];
	//QueryTradeApply.checkedOrder = {};

});
 var  QueryTradeApply = {
	init:function(){
		$("#queryBtn").on('click',function(){
			QueryTradeApply.queryOrderInfo();
		});
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
	},
	
	/**
	 * 查询待审核订单列表
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_FUND_CHECK_ORDER_URL  ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, QueryTradeApply.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		QueryTradeApply.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];
		
		var staticData = bodyData.counterQueryOrderRespDto || {};
		$("#staticId").html("当页小计：申请笔数【"+QueryTradeApply.checkOrders.length+"】申请金额【"+CommonUtil.formatAmount(staticData.pageAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.pageAppVol)+"】 合计：申请笔数【"+staticData.totalCount+"】申请金额【"+CommonUtil.formatAmount(staticData.totalAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.totalAppVol)+"】");

		$("#rsList").empty();
		if(QueryTradeApply.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
			$("#rsList").append(trHtml);
		} else{
			// 交易申请
			$(QueryTradeApply.checkOrders).each(function(index,element){
				var trList = [];
				trList.push((index+1));
				trList.push(CommonUtil.formatData(element.dealAppNo));
				trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
				trList.push(CommonUtil.formatData(element.custNameEncrypt));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, element.txCode, ''));
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.fundName));
				if(element.appRatio > 0){
					trList.push(CommonUtil.formatData(CommonUtil.formatPercent(element.appRatio)));
				}else {
					trList.push('--');
				}
				if(element.appAmt > 0){
					trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
				}else {
					trList.push('--');
				}
				if(element.appVol > 0){
					trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
				}else {
					trList.push('--');
				}
				trList.push(CommonUtil.formatData(element.appDt));
				trList.push(CommonUtil.formatData(element.appTm));
				trList.push('柜台');
				trList.push(CommonUtil.formatData(element.creator,''));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, element.checkFlag, ''));
				trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = '+index+'>详情</a>');
				var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
				$("#rsList").append(trHtml);
			});
		}
		
		// 申请详情
		$(".reCheck").off();
		$(".reCheck").on('click',function(){
			
			var indexValue = $(this).attr("indexvalue");
			var checkOrder = QueryTradeApply.checkOrders[indexValue] || {};
			
			//QueryTradeApply.checkedOrder = checkOrder;
			var dealAppNo = checkOrder.dealAppNo;
			if(!CommonUtil.isEmpty(dealAppNo)){
				
				if(checkOrder.txCode == 'Z910075'){// 转托管转出
					QueryCheckOrder.queryCheckOrderById(dealAppNo, BodyView.queryShowCounterTransferTubeOrderInfo);
					
				} else{// 认申购，赎回，修改分红方式，撤单，基金转换
					QueryTradeApply.viewDtl(dealAppNo);
				}
			}else {
				CommonUtil.layer_tip("无预申请单号无法显示订单详情！");
			}
			
		});
	},

	 /**
	  * 查询数据明细
	  */
	 viewDtl:function(dealAppNo){
		 var  uri= TmsCounterConfig.QUERY_FUND_CHECK_ORDER_URL ||  {};
		 var data ={};
		 data.queryConditonForm = JSON.stringify({"dealAppNo":dealAppNo});
		 data.queryType = "1";
		 var paramters = CommonUtil.buildReqParams(uri, data,true,null,null);
		 CommonUtil.ajaxAndCallBack(paramters, function(data){
			 QueryTradeApply.processViewDtl(data);
		 });
	 },

	 processViewDtl:function (data) {
		 var checkOrderList = data.body.counterQueryOrderRespDto.counterOrderList;
		 var checkOrder = checkOrderList[0];
		 BodyView.showCounterOrderInfo(checkOrder);
	 }
};
