package com.howbuy.tms.counter.fundservice.trade;

import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoFacade;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoRequest;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.QueryBankAcctSensitiveInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.bankacctinfo.bean.BankAcctSensitiveInfo;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.interlayer.product.model.portfolio.PortfolioProductFullModel;
import com.howbuy.interlayer.product.service.AdviserProdInfoService;
import com.howbuy.interlayer.product.service.PortfolioProductService;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.AdviserFundBalanceModel;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceFacade;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceRequest;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceResponse;
import com.howbuy.tms.robot.orders.facade.query.querybalance.QueryUserBalanceFacade;
import com.howbuy.tms.robot.orders.facade.query.querybalance.QueryUserBalanceRequest;
import com.howbuy.tms.robot.orders.facade.query.querybalance.QueryUserBalanceResponse;
import com.howbuy.tms.robot.orders.facade.query.querybalancevol.QueryBalanceVolFacade;
import com.howbuy.tms.robot.orders.facade.query.querybalancevol.QueryBalanceVolRequest;
import com.howbuy.tms.robot.orders.facade.query.querybalancevol.QueryBalanceVolResponse;
import com.howbuy.tms.robot.orders.facade.query.querycustomorder.bean.CustomFundInfoRequest;
import com.howbuy.tms.robot.orders.facade.query.querycustomorder.bean.CustomFundInfoResponse;
import com.howbuy.tms.robot.orders.facade.query.querycustomorder.customsell.QueryCustomSellRatioFacade;
import com.howbuy.tms.robot.orders.facade.query.querycustomorder.customsell.QueryCustomSellRatioRequest;
import com.howbuy.tms.robot.orders.facade.query.querycustomorder.customsell.QueryCustomSellRatioResponse;
import com.howbuy.tms.robot.orders.facade.query.queryredmproductlist.QueryRedmProductListFacade;
import com.howbuy.tms.robot.orders.facade.query.queryredmproductlist.QueryRedmProductListRequest;
import com.howbuy.tms.robot.orders.facade.query.queryredmproductlist.QueryRedmProductListResponse;
import com.howbuy.tms.robot.orders.facade.query.queryredmproductlist.bean.RedeemProductModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 投顾逻辑类
 * <AUTHOR>
 * @description:
 * @date 2024/2/6 16:50
 * @since JDK 1.8
 */
@Service
public class AdviserService {
    @Resource
    private QueryCustAdviserBalanceFacade queryCustAdviserBalanceFacade;

    @Resource
    private AdviserProdInfoService adviserProdInfoService;

    @Resource
    private PortfolioProductService portfolioProductService;

    @Resource
    protected TmsCounterOutService tmsCounterOutService;

    @Resource
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    @Resource
    private QueryRedmProductListFacade queryRedmProductListFacade;

    @Autowired
    @Qualifier("tmscounter.queryBankAcctSensitiveInfoFacade")
    private QueryBankAcctSensitiveInfoFacade queryBankAcctSensitiveInfoFacade;

    @Autowired
    @Qualifier("tmscounter.queryBankCardInfoFacade")
    private QueryBankCardInfoFacade queryBankCardInfoFacade;

    @Resource
    private QueryCustomSellRatioFacade queryCustomSellRatioFacade;

    @Resource
    private QueryBalanceVolFacade queryBalanceVolFacade;

    public CustomSellRatioResDto queryCustomSellRatio(QueryCustomSellRatioResDto dto){
        QueryCustomSellRatioRequest request = new QueryCustomSellRatioRequest();
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setDisCode(dto.getDisCode());
        request.setProtocolNo(dto.getProtocolNo());
        request.setCustomOrRatio(dto.getCustomOrRatio());
        request.setCpAcctNo(dto.getCpAcctNo());

        List<CustomFundInfoRequest> customFundInfos = new ArrayList<>();
        List<RedeemTrailResDto> redeemTrailResDtos = dto.getRedeemTrailResDtos();
        if(!CollectionUtils.isEmpty(redeemTrailResDtos)){
            CustomFundInfoRequest infoRequest = null;
            for(RedeemTrailResDto trailDto : redeemTrailResDtos){
                infoRequest = new CustomFundInfoRequest();
                infoRequest.setFundCode(trailDto.getFundCode());
                infoRequest.setAppVol(trailDto.getAppVol());
                customFundInfos.add(infoRequest);
            }
        }
        request.setCustomFundInfos(customFundInfos);
        Date nowDate = new Date();
        request.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        request.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        QueryCustomSellRatioResponse response = queryCustomSellRatioFacade.execute(request);

        CustomSellRatioResDto resDto = new CustomSellRatioResDto();
        if(response == null){
            return resDto;
        }
        resDto.setReturnCode(response.getReturnCode());
        resDto.setDescription(response.getDescription());
        resDto.setTotalSellRatio(response.getTotalSellRatio());

        List<CustomFundInfoResponse> retCustomFundInfos = response.getCustomFundInfos();
        List<CustomFundInfoResDto> customFundInfoResDtos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(retCustomFundInfos)){
            CustomFundInfoResDto customFundInfoResDto = null;
            for(CustomFundInfoResponse customFundInfoResponse : retCustomFundInfos){
                customFundInfoResDto = new CustomFundInfoResDto();
                BeanUtils.copyProperties(customFundInfoResponse, customFundInfoResDto);
                customFundInfoResDtos.add(customFundInfoResDto);
            }
        }
        resDto.setCustomFundInfoResDtos(customFundInfoResDtos);
        return resDto;
    }


    public QueryRedmProductListResDto queryRedmProductList(QueryRedmProductListReqDto dto) {
        QueryRedmProductListResDto resDto = new QueryRedmProductListResDto();

        QueryBalanceVolResponse res = queryUserBalanceVolAndOpenFlag(dto);
        List<CustBalanceResDto> custBalanceResDtos = getBalanceVol(res);
        List<CustProtocolOpenResDto> custProtocolOpenResDtos = getCustProtocolOpen(res);
        resDto.setCustProtocolOpenResDtos(custProtocolOpenResDtos);

        //第一次无银行卡
        if(StringUtils.isEmpty(dto.getCpAcctNo())){
            List<String> cpAcctNos = getCpAcctNos(custBalanceResDtos);
            List<BankAcctSensitiveResModel> bankAcctSensitiveResModels = queryBankAcctSensitiveInfo(dto.getTxAcctNo(), dto.getDisCode(), cpAcctNos);
            if(CollectionUtils.isEmpty(bankAcctSensitiveResModels)){
                return resDto;
            }
            resDto.setBankAcctSensitiveResModels(bankAcctSensitiveResModels);
            dto.setCpAcctNo(bankAcctSensitiveResModels.get(0).getCpAcctNo());
        }
        List<CustBalanceResDto> retCustBalanceResDtos = getCustBalanceResDtos(dto.getCpAcctNo(), custBalanceResDtos);
        resDto.setCustBalanceResDtos(retCustBalanceResDtos);

        QueryRedmProductListRequest request = new QueryRedmProductListRequest();
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setDisCode(dto.getDisCode());
        request.setProtocolNo(dto.getProtocolNo());
        request.setProductCode(dto.getProductCode());
        request.setCpAcctNo(dto.getCpAcctNo());
        request.setForceRedeemFlag("0");
        request.setShowFundWhenFundNoVol("0");
        request.setAppRatio(dto.getAppRatio());
        Date nowDate = new Date();
        request.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        request.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        QueryRedmProductListResponse response = queryRedmProductListFacade.execute(request);
        if(response == null){
            return resDto;
        }

        resDto.setAdviceDay(response.getAdviceDay());
        resDto.setCanRedeem(response.getCanRedeem());
        resDto.setRedeemReason(response.getDescription());
        resDto.setReason(response.getReason());
        resDto.setLatestPurFlag(response.getLatestPurFlag());
        resDto.setAdviceDay(response.getAdviceDay());
        resDto.setAppRatio(response.getAppRatio());
        resDto.setCheckIn(response.getCheckIn());
        resDto.setT0Flag(response.getT0Flag());

        List<RedeemProductModel> redeemProductModelList = response.getRedeemProductModelList();
        if(CollectionUtils.isEmpty(redeemProductModelList)){
            return resDto;
        }
        RedeemProductResDto redeemProductResDto = null;
        List<RedeemProductResDto> redeemProductResDtos = new ArrayList<>();
        for(RedeemProductModel model : redeemProductModelList){
            redeemProductResDto = new RedeemProductResDto();
            BeanUtils.copyProperties(model, redeemProductResDto);
            if(redeemProductResDto.getHoldRatio() == null){
                redeemProductResDto.setHoldRatio("0");
            }
            if(redeemProductResDto.getSellRatio() == null){
                redeemProductResDto.setSellRatio("0");
            }
            if(StringUtils.isEmpty(redeemProductResDto.getFundReason())){
                redeemProductResDto.setFundReason("无");
            }
            redeemProductResDtos.add(redeemProductResDto);
        }
        resDto.setRedeemProductResDtos(redeemProductResDtos);
        return resDto;
    }



    private List<String> getCpAcctNos(List<CustBalanceResDto> custBalanceResDtos) {
        List<String> cpAcctNos = new ArrayList<>();
        if(CollectionUtils.isEmpty(custBalanceResDtos)){
            return cpAcctNos;
        }
        for(CustBalanceResDto dto : custBalanceResDtos){
            if(!StringUtils.isEmpty(dto.getCpAcctNo()) && !cpAcctNos.contains(dto.getCpAcctNo())){
                cpAcctNos.add(dto.getCpAcctNo());
            }
        }
        return cpAcctNos;
    }

    private List<CustBalanceResDto> getCustBalanceResDtos(String cpAcctNo, List<CustBalanceResDto> custBalanceResDtos) {
        List<CustBalanceResDto> retCustBalanceResDtos = new ArrayList<>();
        if(StringUtils.isEmpty(cpAcctNo) || CollectionUtils.isEmpty(custBalanceResDtos)){
            return retCustBalanceResDtos;
        }
        for(CustBalanceResDto custBalanceResDto : custBalanceResDtos){
            if(cpAcctNo.equals(custBalanceResDto.getCpAcctNo())){
                retCustBalanceResDtos.add(custBalanceResDto);
            }
        }
        return retCustBalanceResDtos;
    }


    public QueryBalanceVolResponse queryUserBalanceVolAndOpenFlag(QueryRedmProductListReqDto dto){
        QueryBalanceVolRequest request = new QueryBalanceVolRequest();
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setDisCode(dto.getDisCode());
        request.setProtocolNo(dto.getProtocolNo());
        Date nowDate = new Date();
        request.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        request.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        QueryBalanceVolResponse res = queryBalanceVolFacade.execute(request);
        return res;
    }
    private List<CustBalanceResDto> getBalanceVol(QueryBalanceVolResponse res) {
        List<CustBalanceResDto> custBalanceResDtos = new ArrayList<>();
        if(res == null || CollectionUtils.isEmpty(res.getQueryBalanceVolBeans())){
            return custBalanceResDtos;
        }

        CustBalanceResDto custBalanceResDto = null;
        List<QueryBalanceVolResponse.QueryBalanceVolBean> queryBalanceVolBeans = res.getQueryBalanceVolBeans();
        for(QueryBalanceVolResponse.QueryBalanceVolBean bean : queryBalanceVolBeans){
            custBalanceResDto = new CustBalanceResDto();
            custBalanceResDto.setFundCode(bean.getFundCode());
            custBalanceResDto.setBalanceVol(bean.getBalanceVol());
            custBalanceResDto.setAvailVol(bean.getAvailVol());
            custBalanceResDto.setCpAcctNo(bean.getCpAcctNo());
            custBalanceResDtos.add(custBalanceResDto);
        }
        return custBalanceResDtos;
    }

    private List<CustProtocolOpenResDto> getCustProtocolOpen(QueryBalanceVolResponse res) {
        List<CustProtocolOpenResDto> custProtocolOpenResDtos = new ArrayList<>();
        if(res == null || CollectionUtils.isEmpty(res.getCustProtocolOpenBeans())){
            return custProtocolOpenResDtos;
        }

        CustProtocolOpenResDto resDto = null;
        List<QueryBalanceVolResponse.CustProtocolOpenBean> custProtocolOpenBeans = res.getCustProtocolOpenBeans();
        for(QueryBalanceVolResponse.CustProtocolOpenBean bean : custProtocolOpenBeans){
            resDto = new CustProtocolOpenResDto();
            resDto.setTxCode(bean.getTxCode());
            resDto.setOpenFlag(bean.getOpenFlag());
            resDto.setProtocolNo(bean.getProtocolNo());
            resDto.setCustProtocolOpenName(bean.getCustProtocolOpenName());
            resDto.setNextOpenDt(bean.getNextOpenDt());
            custProtocolOpenResDtos.add(resDto);
        }
        return custProtocolOpenResDtos;
    }

    public List<BankAcctSensitiveResModel> queryBankAcctSensitiveInfo(String txAcctNo, String disCode, List<String> cpAcctNos){
        List<BankAcctSensitiveResModel> bankAcctSensitiveResModels = new ArrayList<>();
        if(StringUtils.isEmpty(txAcctNo) || StringUtils.isEmpty(disCode) || CollectionUtils.isEmpty(cpAcctNos)){
            return bankAcctSensitiveResModels;
        }

        for(String cpAcctNo : cpAcctNos){
            BankAcctSensitiveResModel model = queryBankAcctSensitiveInfo(txAcctNo, disCode, cpAcctNo);
            if(StringUtils.isEmpty(model.getBankAcct())){
                continue;
            }
            bankAcctSensitiveResModels.add(model);
        }
        return bankAcctSensitiveResModels;
    }

    private BankAcctSensitiveResModel queryBankAcctSensitiveInfo(String txAcctNo, String disCode, String cpAcctNo) {
        Date nowDate = new Date();

        BankAcctSensitiveResModel model = new BankAcctSensitiveResModel();
        QueryBankAcctSensitiveInfoRequest request = new QueryBankAcctSensitiveInfoRequest();
        request.setCustNo(txAcctNo);
        request.setDisCode(disCode);
        request.setCpAcctNo(cpAcctNo);
        request.setCpAcctSwitch(true);
        request.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        request.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        QueryBankAcctSensitiveInfoResponse response = queryBankAcctSensitiveInfoFacade.execute(request);
        if(response == null || response.getBankAcctSensitiveInfo() == null){
            return model;
        }
        BankAcctSensitiveInfo bankAcctSensitiveInfo = response.getBankAcctSensitiveInfo();
        model.setBankAcct(bankAcctSensitiveInfo.getBankAcct());
        model.setCpAcctNo(bankAcctSensitiveInfo.getCpAcctNo());

        QueryBankCardInfoRequest req = new QueryBankCardInfoRequest();
        req.setTxAcctNo(txAcctNo);
        req.setDisCode(disCode);
        req.setCpAcctNo(cpAcctNo);
        req.setCpAcctSwitch(true);
        req.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        req.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        QueryBankCardInfoResponse res = queryBankCardInfoFacade.execute(req);
        if(res == null){
            return model;
        }
        model.setBankAcctDigest(res.getBankAcctDigest());
        model.setBankAcctMask(res.getBankAcctMask());
        model.setBankCode(res.getBankCode());
        model.setBankRegionCode(res.getBankRegionCode());
        model.setBankRegionName(res.getBankRegionName());
        model.setBankAcctName(res.getBankAcctName());
        model.setBankAcctStatus(res.getBankAcctStatus());
        model.setBankAcctVrfyStat(res.getBankAcctVrfyStat());
        model.setBankName(res.getBankName());
        return model;
    }

    public QueryAcctBalanceDtlRespDto queryAdviserBalance(QueryCustAdviserBalanceRequest request) {
        String productCode = request.getProductCode();
        String disCode = request.getDisCode();
        String txAcctNo = request.getTxAcctNo();


        PortfolioProductFullModel portfolioProdInfoDto = adviserProdInfoService.queryAdviserProdInfoByProductCodeV2(productCode);
        String tradeDt = tmsCounterOutService.getCurrTaTradeDt();
        // 可赎回日期
        String redeemDt = getRedeemDt(portfolioProdInfoDto, tradeDt);

        QueryCustAdviserBalanceResponse response = queryCustAdviserBalanceFacade.execute(request);

        List<QueryAcctBalanceDtlRespDto.DtlBean> balanceDtlList = new ArrayList<QueryAcctBalanceDtlRespDto.DtlBean>();

        if (response != null && CollectionUtils.isNotEmpty(response.getAdviserBalList())) {
            Map<String, QueryCustBankCardResult> bankMap = getBankMap(txAcctNo, disCode, response.getAdviserBalList());
            Map<String, List<AdviserFundBalanceModel>> groupMap = response.getAdviserBalList().stream()
                    .filter(dtl -> dtl.getAvailVol().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.groupingBy(dtl -> String.join("_", dtl.getProtocolNo(), dtl.getCpAcctNo())));

            groupMap.forEach((k, v) -> {
                BigDecimal availVol = v.stream().map(dtl -> dtl.getAvailVol()).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal unConfirmedVol = v.stream().map(dtl -> dtl.getUnConfirmedVol()).reduce(BigDecimal.ZERO, BigDecimal::add);

                AdviserFundBalanceModel balanceModel = v.get(0);

                QueryAcctBalanceDtlRespDto.DtlBean dtlBean = new QueryAcctBalanceDtlRespDto.DtlBean();
                dtlBean.setProductCode(productCode);
                dtlBean.setProductName(portfolioProdInfoDto.getProductName());
                dtlBean.setTxAcctNo(txAcctNo);
                dtlBean.setDisCode(disCode);
                dtlBean.setOpenRedeDt(redeemDt);
                // 1可赎回
                dtlBean.setRedeemStatus(YesOrNoEnum.YES.getCode().equals(portfolioProdInfoDto.getIsSoldOpen()) && response.isFundCanRedeem() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                dtlBean.setAvailVol(availVol);
                dtlBean.setUnconfirmedVol(unConfirmedVol);
                dtlBean.setProtocolNo(balanceModel.getProtocolNo());
                dtlBean.setProtocolType(response.getProtocolType());
                dtlBean.setCpAcctNo(balanceModel.getCpAcctNo());

                // 查询银行卡信息
                QueryCustBankCardResult bankCardResult = bankMap.get(dtlBean.getCpAcctNo());
                if (bankCardResult != null) {
                    dtlBean.setBankAcctNo(bankCardResult.getBankAcct());
                    dtlBean.setBankName(bankCardResult.getBankName());
                    dtlBean.setBankCode(bankCardResult.getBankCode());
                }

                balanceDtlList.add(dtlBean);
            });
        }

        QueryAcctBalanceDtlRespDto returnDto = new QueryAcctBalanceDtlRespDto();
        returnDto.setReturnCode(Optional.ofNullable(response).map(QueryCustAdviserBalanceResponse::getReturnCode).orElseGet(null));
        returnDto.setDescription(Optional.ofNullable(response).map(QueryCustAdviserBalanceResponse::getDescription).orElseGet(null));
        returnDto.setBalanceDtlList(balanceDtlList);
        returnDto.setTaTradeDt(tradeDt);
        returnDto.setRedeemDirectionIsSupCardFlag("1");
        returnDto.setRedeemDirectionIsSupCxgFlag("1");
        return returnDto;
    }

    private String getRedeemDt(PortfolioProductFullModel portfolioProdInfoDto, String currentWorkDay) {
        String redeemDt = currentWorkDay;
        if (!YesOrNoEnum.YES.getCode().equals(portfolioProdInfoDto.getIsSoldOpen())) {
            String endDay = getIntervalDay(currentWorkDay, 1);
            redeemDt = portfolioProductService.queryPortfolioProdCanRedeemTradeDay(currentWorkDay, endDay, portfolioProdInfoDto.getProductCode(), portfolioProdInfoDto.getChangeVersion());
        }
        return redeemDt;
    }


    private String getIntervalDay(String startDay, int intervalMonth) {
        Date startDate = DateUtils.formatToDate(startDay, DateUtil.SHORT_DATEPATTERN);
        Calendar cale = Calendar.getInstance();
        cale.setTime(startDate);
        cale.add(Calendar.MONTH, intervalMonth);
        cale.set(Calendar.DAY_OF_MONTH, 0);
        cale.set(Calendar.HOUR, 0);
        return DateUtil.date2String(cale.getTime(), DateUtil.SHORT_DATEPATTERN);
    }


    private Map<String, QueryCustBankCardResult> getBankMap(String txAcctNo, String disCode, List<AdviserFundBalanceModel> balanceList) {
        List<String> cpAcctNos = balanceList.stream().map(AdviserFundBalanceModel::getCpAcctNo).distinct().collect(Collectors.toList());

        Map<String, QueryCustBankCardResult> bankMap = cpAcctNos.stream()
                .map(x -> getBankInfo(txAcctNo, disCode, x))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(QueryCustBankCardResult::getCpAcctNo, Function.identity(), (k1, k2) -> k1));
        return bankMap;
    }

    private QueryCustBankCardResult getBankInfo(String txAcctNo, String disCode, String cpAcctNo) {
        QueryCustBankCardContext ctx = new QueryCustBankCardContext();
        ctx.setTxAcctNo(txAcctNo);
        ctx.setDisCode(disCode);
        ctx.setCpAcctNo(cpAcctNo);
        QueryCustBankCardResult result = queryCustBankCardOuterService.queryCudtBankCard(ctx);

        return result;
    }
}
