package com.howbuy.tms.counter.common;

public class OperationCodes {
	/**
	 * 开户
	 */
	public final static String SUBSCRIPTION_OPEN_ACCOUNT_AND_BINGING_CARD = "SUBSCRIPTION_OPEN_ACCOUNT_AND_BINGING_CARD";
	/**
	 * 绑卡
	 */
	public final static String SUBSCRIPTION_BINGING_CARD = "SUBSCRIPTION_BINGING_CARD";
	/**
	 * 下单
	 */
	public final static String SUBSCRIPTION_ORDER = "SUBSCRIPTION_ORDER";
	/**
	 * 预约单取消
	 */
	public final static String SUBSCRIPTION_CANCEL = "SUBSCRIPTION_CANCEL";
	/**
	 * 预约单驳回
	 */
	public final static String SUBSCRIPTION_REJECT = "SUBSCRIPTION_REJECT";
	/**
	 * 预约单确认
	 */
	public final static String SUBSCRIPTION_CONFIRM = "SUBSCRIPTION_CONFIRM";
	/**
	 * 预约单过期检查
	 */
	public final static String SUBSCRIPTION_CHECK_OUT_OF_DATE = "SUBSCRIPTION_CHECK_OUT_OF_DATE";
	/**
	 * 同步风险评级
	 */
	public final static String SUBSCRIPTION_SYNC_RISK_LEVEL = "SUBSCRIPTION_SYNC_RISK_LEVEL";

}
