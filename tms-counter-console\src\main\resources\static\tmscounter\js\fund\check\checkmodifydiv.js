$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckModifydiv.checkOrder = {};	 
	CheckModifydiv.init(checkId,custNo,disCode,idNo);
});

var CheckModifydiv = {	
		init:function(checkId, custNo, disCode,idNo){
			QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
			QueryCheckOrder.queryCheckOrderById(checkId,CheckModifydiv.queryCheckOrderByIdBack);
			
			$("#returnBtn").on('click',function(){
				CheckModifydiv.confirm(CounterCheck.Faild);
			});
			
			$("#succBtn").on('click',function(){
				CheckModifydiv.confirm(CounterCheck.Succ);
			});
		},
		
		/***
		 * 审核确认
		 */	
		confirm : function(checkStatus){
			if(window.checkedClick == '1'){
				return false;
			}
			//防止重复点击
			window.checkedClick = '1';
			
			var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
			
			if(CounterCheck.Faild == checkStatus){
				if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
					window.checkedClick = '0';
					CommonUtil.layer_tip("请输入失败原因");
					return false;
				}
				CheckModifydiv.checkFaildDesc = $("#checkFaildDesc").val();
			}
			
			var reqparamters ={"checkFaildDesc":CheckModifydiv.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckModifydiv.checkOrder)};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, CheckModifydiv.callBack);
			return true;
		},
		callBack:function(data){
			window.checkedClick = '0';
			var respCode = data.code || '';
			var respDesc = data.desc || '';
			
			if(CommonUtil.isSucc(respCode)){
				CommonUtil.layer_tip("成功");
				CommonUtil.disabledBtn("returnBtn");
				CommonUtil.disabledBtn("succBtn");
			}else{
				CommonUtil.layer_tip(respDesc);
			}
		},
		
		queryCheckOrderByIdBack:function(data){
			var bodyData = data.body || {};
			CheckModifydiv.checkOrder = bodyData.checkOrder || {};
					
			if(CommonUtil.isEmpty(CheckModifydiv.checkOrder.dealAppNo)){
				CommonUtil.layer_tip("无此订单");
				return false;
			}
			
			if(CheckModifydiv.checkOrder.checkFlag != 0){
				CommonUtil.layer_tip("该订单已审核完成");
				return false;
			}
			
			QueryFundDivInfo.queryFundDivInfo(QueryCustInfo.custInfo.disCode,QueryCustInfo.custInfo.custNo,CheckModifydiv.checkOrder.fundCode);
			
			if($("#fundCode").length > 0){
				$("#fundCode").html(CheckModifydiv.checkOrder.fundCode);
			}
			
			if($("#targetDivMode").length > 0){
				$("#targetDivMode").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,CheckModifydiv.checkOrder.fundDivMode, '--'));	
			}
			
			/**other*/
			if($("#appDt").length > 0){
				$("#appDt").html(CheckModifydiv.checkOrder.appDt);
			}
			
			if($("#appTm").length > 0){
				$("#appTm").html(CheckModifydiv.checkOrder.appTm);
			}
			
			if($("#consCode").length > 0){
				$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckModifydiv.checkOrder.consCode, ''));
			}
			
			if($("#transactorName").length > 0){
				$("#transactorName").html(CheckModifydiv.checkOrder.transactorName);
			}

			if($("#transactorIdType").length > 0){
				$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckModifydiv.checkOrder.transactorIdType, ''));
			}
			
			if($("#transactorIdNo").length > 0){
				$("#transactorIdNo").html(CheckModifydiv.checkOrder.transactorIdNo);
			}
			
			if($("#checkFaildDesc").length > 0){
				$("#checkFaildDesc").val(CheckModifydiv.checkOrder.memo);
			}
		},
	}
