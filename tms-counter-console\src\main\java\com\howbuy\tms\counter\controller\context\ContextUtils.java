/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;

import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.dto.CounterOrderDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.CustomerAppointmentInfoDto;

/**
 * @className ContextUtils
 * @description
 * <AUTHOR>
 * @date 2019/6/19 14:01
 */
public class ContextUtils {

    public static QueryOrderFileContext buildQueryCotext(TradeCommonContext context, String crmTradeType){
        QueryOrderFileContext queryOrderFileContext = new QueryOrderFileContext();
        CustInfoDto custInfoDto = context.getCustInfoDto();
        queryOrderFileContext.setHboneno(custInfoDto.getHboneNo());

        HighProductBaseInfoBean highProductBaseInfoBean = context.getHighProduct();
        queryOrderFileContext.setPcode(highProductBaseInfoBean.getFundCode());

        CustomerAppointmentInfoDto customerAppointmentInfoDto = context.getCustomerAppointmentInfoDto();
        if(customerAppointmentInfoDto != null){
            queryOrderFileContext.setPreid(customerAppointmentInfoDto.getAppointId());
        }


        AuditingOrderFileCmd auditingOrderFileCmd = context.getAuditingOrderFileCmd();
        if(auditingOrderFileCmd != null){
            queryOrderFileContext.setOrderid(auditingOrderFileCmd.getOrderid());
        }

        queryOrderFileContext.setBusiid(crmTradeType);

        return queryOrderFileContext;
    }

    public static CounterOrderDto getValidateReplyCondition(TradeCommonContext context, String txCode) {
        CounterOrderDto condition = new CounterOrderDto();
        condition.setTxCode(txCode);
        condition.setTxAcctNo(context.getCustInfoDto().getCustNo());
        condition.setFundCode(context.getHighProduct().getFundCode());
        if(context.getCustomerAppointmentInfoDto() != null){
            condition.setAppointmentDealNo(context.getCustomerAppointmentInfoDto().getAppointId());
        }

        if(context.getAuditingOrderFileCmd() != null){
            condition.setMaterialId(context.getAuditingOrderFileCmd().getOrderid());
        }
        return condition;
    }

}
