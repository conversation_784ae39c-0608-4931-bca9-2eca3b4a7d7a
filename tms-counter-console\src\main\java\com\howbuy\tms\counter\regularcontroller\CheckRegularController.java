/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:(柜台审核控制)
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class CheckRegularController extends AbstractController {
    private static Logger logger = LogManager.getLogger(CheckRegularController.class);

    /**
     * 
     * checkConfirm:(审核确认)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:16:20
     */
    @RequestMapping("/tmscounter/regular/checkconfirm.htm")
    public ModelAndView checkConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String checkFaildDesc = request.getParameter("checkFaildDesc");
        String checkStatus = request.getParameter("checkStatus");
        String checkedOrderForm = request.getParameter("checkedOrderForm");
        String operation = request.getParameter("operation");
        RegularCounterOrderDto counterOrderDto = JSON.parseObject(checkedOrderForm, RegularCounterOrderDto.class);
//        counterOrderDto.setAppAmt(counterOrderDto.getAppAmt());
        String creator = counterOrderDto.getCreator();

        if (creator == null) {
            creator = "";
        }

        if (!Constants.OPERATION_ABOLISH.equals(operation)) {
            if (creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_REPLY);
            }
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterOrderDto);
        RegularSubmitUncheckOrderDto submitUncheckOrderDto = new RegularSubmitUncheckOrderDto();
        BeanUtils.copyProperties(counterOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setCheckFlag(checkStatus);
        submitUncheckOrderDto.setMemo(checkFaildDesc);
        submitUncheckOrderDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        tmsRegularCounterService.checkOrder(submitUncheckOrderDto, disInfoDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 
     * queryCheckOrder:(查询待审核订单)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:16:03
     */
    @RequestMapping("/tmscounter/regular/querycheckorder.htm")
    public ModelAndView queryCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String queryOrderCmd = request.getParameter("queryConditonForm");
        String pageNum = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        String owner = request.getParameter("owner");
        String checkFlag = request.getParameter("checkFlag");
        String tradeDt = request.getParameter("tradeDt");

        logger.info("queryOrderCmd:{}", queryOrderCmd);
        RegularCounterQueryOrderReqDto counterQueryOrderReqDto = JSON.parseObject(queryOrderCmd, RegularCounterQueryOrderReqDto.class);
        logger.info("counterQueryOrderReqDto:{}", JSON.toJSONString(counterQueryOrderReqDto));

        // 我的交易申请查询需要绑定操作员号
        if (Constants.ROLE_OWNER.equals(owner)) {
            OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
            counterQueryOrderReqDto.setCreator(operatorInfoCmd.getOperatorNo());

            // 我的交易申请查询默认查询当前工作日
            if (Constants.ORDER_CMD_BRACKETS.equals(queryOrderCmd)) {
                counterQueryOrderReqDto.setTradeDt(tradeDt);
            }
        }

        logger.info("pageNum:{},pageSize:{}", pageNum, pageSize);
        if (StringUtils.isEmpty(pageNum)) {
            pageNum = "1";
        }
        if (StringUtils.isEmpty(pageSize)) {
            pageSize = "20";
        }
        counterQueryOrderReqDto.setPageNo(Integer.parseInt(pageNum));
        counterQueryOrderReqDto.setPageSize(Integer.parseInt(pageSize));
        
        if (!StringUtils.isEmpty(checkFlag)) {
            counterQueryOrderReqDto.setCheckFlag(checkFlag);
        }

        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        if (StringUtils.isEmpty(counterQueryOrderReqDto.getTxAcctNo())) {
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getIdNo())) {
                queryCustBaseInfoReqDto.setIdNo(counterQueryOrderReqDto.getIdNo());
                QueryCustBaseInfoRespDto qeryCustBaseInfoRespDto = tmsCounterService.queryCustBaseInfo(queryCustBaseInfoReqDto, null);
                counterQueryOrderReqDto.setTxAcctNo(qeryCustBaseInfoRespDto.getTxAcctNo());
            }
        }

        RegularCounterQueryOrderRespDto counterQueryOrderRespDto = tmsRegularCounterService.counterQueryOrder(counterQueryOrderReqDto, null);
        List<RegularCounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();
        if (!CollectionUtils.isEmpty(counterOrderList)) {
            for (RegularCounterOrderDto dto : counterOrderList) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("counterOrderList", counterOrderList);
        body.put("totalPage", counterQueryOrderRespDto.getTotalPage());
        body.put("pageNum", counterQueryOrderRespDto.getPageNo());
        body.put("counterQueryOrderRespDto", counterQueryOrderRespDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 
     * queryCheckOrderById:(查询单条未审核订单)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:10:22
     */
    @RequestMapping("/tmscounter/regular/querycheckorderbyid.htm")
    public ModelAndView queryCheckOrderById(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String pageNum = request.getParameter("pageNum");
        String pageSize = request.getParameter("pageSize");

        RegularCounterQueryOrderReqDto queryReqDto = new RegularCounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        RegularCounterOrderDto counterOrderDto = tmsRegularCounterService.counterQueryOrderById(queryReqDto, null);
//        counterOrderDto.setAppAmt(counterOrderDto.getAppAmt());
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("checkOrder", counterOrderDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

}
