/**
*是否经办-模块
*<AUTHOR>
*@date 2018-01-26 10:26
*/
$(function(){
	Agent.init();
});


var Agent = {
		
	init:function(){
		 $(".selectAgened").change(function () {
		   if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
				CommonUtil.layer_tip("请先选择用户");
				return ;
			}
	        if ($(this).children('option:selected').val() == '1') {//选择是
	        	Agent.selectAgenedOption("1");
	        	Agent.agentDivShow();
	        } else{
	        	Agent.selectAgenedOption("0");
	        	Agent.agentDivHide();
	        }
		});
	},
	
	agentDivShow : function(){
		$("#agentInfoDiv").css("display","block");
	},
	
	agentDivHide : function(selAgenedFlag){
		$("#agentInfoDiv").css("display","none");
	},
	
	/**
	 * 设置是否经办选中
	 */
	selectAgenedOption: function(optionValue){
		if(optionValue == '1'){
			 $(".selectAgened").find("option[value='0']").attr("selected",false);
			 $(".selectAgened").find("option[value='1']").attr("selected",true);
		} else{
			$(".selectAgened").find("option[value='0']").attr("selected",true);
			$(".selectAgened").find("option[value='1']").attr("selected",false);
		}
	},
	
	/**
	 * 判断选择的是个人客户还是机构客户，个人时"是否经办"默认选中否, 机构默认选中是
	 */
	setAgentDivByInvstType :  function(invstType){
		if(invstType == '0' || invstType == '2'){//机构
			Agent.selectAgenedOption("1");
			Agent.agentDivShow();
		} else{
			Agent.selectAgenedOption("0");
			Agent.agentDivHide();
		}
	}
} 