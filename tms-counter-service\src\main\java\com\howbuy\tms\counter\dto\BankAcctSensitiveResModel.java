package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/31 09:28
 * @since JDK 1.8
 */
public class BankAcctSensitiveResModel implements Serializable {

    private static final long serialVersionUID = -2189579695587571631L;
    private String cpAcctNo;
    private String bankAcct;
    private String bankAcctDigest;
    private String bankAcctMask;
    private String bankCode;
    private String bankRegionCode;
    private String bankRegionName;
    private String bankAcctName;
    private String bankAcctStatus;
    private String bankAcctVrfyStat;
    private String bankName;

    public String getBankAcctDigest() {
        return bankAcctDigest;
    }

    public void setBankAcctDigest(String bankAcctDigest) {
        this.bankAcctDigest = bankAcctDigest;
    }

    public String getBankAcctMask() {
        return bankAcctMask;
    }

    public void setBankAcctMask(String bankAcctMask) {
        this.bankAcctMask = bankAcctMask;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankRegionCode() {
        return bankRegionCode;
    }

    public void setBankRegionCode(String bankRegionCode) {
        this.bankRegionCode = bankRegionCode;
    }

    public String getBankRegionName() {
        return bankRegionName;
    }

    public void setBankRegionName(String bankRegionName) {
        this.bankRegionName = bankRegionName;
    }

    public String getBankAcctName() {
        return bankAcctName;
    }

    public void setBankAcctName(String bankAcctName) {
        this.bankAcctName = bankAcctName;
    }

    public String getBankAcctStatus() {
        return bankAcctStatus;
    }

    public void setBankAcctStatus(String bankAcctStatus) {
        this.bankAcctStatus = bankAcctStatus;
    }

    public String getBankAcctVrfyStat() {
        return bankAcctVrfyStat;
    }

    public void setBankAcctVrfyStat(String bankAcctVrfyStat) {
        this.bankAcctVrfyStat = bankAcctVrfyStat;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

}