/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.math.BigDecimal;
import java.io.Serializable;

/**
 * @description:(高端产品信息) 
 * @reason:
 * <AUTHOR>
 * @date 2018年2月6日 下午2:50:12
 * @since JDK 1.6
 */
public class HighProductDto implements Serializable{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 6061275147216435446L;


    /**
     * 是否支持电子合同 1支持 0 是不支持
     */
    private String eContract;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金名称
     */
    private String fundName;
    /**
     * 基金类型
     */
    private String fundType;
    /**
     * 基金二级类型
     */
    private String fundSubType;
    /**
     * TA代码
     */
    private String taCode;
    /**
     * 基金默认分红方式
     */
    private String dfltDivMode;
    /**
     * 追加申购判断规则
     */
    private String suppleSubsRule;
    /**
     * 最低持有份额
     */
    private BigDecimal minAcctVol;
    /**
     * 基金风险等级
     */
    private String fundRiskLevel;
    /**
     * 基金简称拼音
     */
    private String fundAttrPinyin;
    /**
     * 主基金代码
     */
    private String mainFundCode;
    /**
     * 可销售规模
     */
    private BigDecimal distributeSize;
    /**
     * 交易截止时间
     */
    private String endTm;
    /**
     * 募集开始日期
     */
    private String ipoStartDt;
    /**
     * 募集结束日期
     */
    private String ipoEndDt;
    /**
     * 基金简称
     */
    private String fundAttr;
    /**
     * 产品开放周期
     */
    private String redeOpenTerm;
    /**
     * 产品开放类型
     */
    private String fundOpenMode;
    /**
     * 开通日期
     */
    private String openDt;
    /**
     * 开通标志
     */
    private String openFlag;
    /**
     * 基金类别
     */
    private String fundClass;
    /**
     * 滚动产品期限
     */
    private Integer prodRollRange;
    /**
     * 滚动产品认申购开放天数
     */
    private Integer prodSubDays;
    /**
     * 滚动产品赎回开放天数
     */
    private Integer prodRedmDays;
    /**
     * 遇节假日策略
     */
    private String holidayStrategy;
    /**
     * 产品极差
     */
    private BigDecimal prodDiffer;
    /**
     * 份额类型
     */
    private String shareClass;
    /**
     * TA名称
     */
    private String taName;
    /**
     * 首次最低申请金额（净购买金额）
     */
    private BigDecimal netMinAppAmt;
    /**
     * 最低追加申请金额（净追加金额）
     */
    private BigDecimal netMinSuppleAmt;
    /**
     * 确认天数
     */
    private Integer confirmDays;
    /***
     * 定向客户开放标识（预约码交易）
     */
    private String directOpenFlag;
    /***
     * 是否支持预约标识
     */
    private String supportAdvanceFlag;
    /***
     * 手续费计算类型
     */
    private String feeCalMode;
    /***
     * 是否支持多卡
     */
    private String multiCardFlag;
    /***
     * 赎回备注
     */
    private String redeemMemo;
    /***
     * 产品风险揭示书访问路径
     */
    private String riskNoticeUrl;
    /***
     * 产品电子合同访问路径
     */
    private String elecContractUrl;
    /***
     * 基金产品网页链接
     */
    private String fundWebUrl;
    /***
     * 冷静期
     */
    private Integer calmTime;
    /***
     * 销售协议书URL
     */
    private String saleAgreementUrl;
    /**
     * 销售补充协议URL1
     */
    private String supplyAgreementUrl1;
    /**
     * 销售补充协议URL2
     */
    private String supplyAgreementUrl2;
    /***
     * 销售补充协议URL3
     */
    private String supplyAgreementUrl3;
    /***
     * 产品类别
     */
    private String productClass;
    /**
     * 支付方式：是否支持自划款 0-不支持 1-支持
     */
    private String isSupSelfDrawing;
    /***
     * 支付方式：是否支持代划款 0-不支持 1-支持
     */
    private String isSupAgentDrawing;
    /***
     * 支付方式：是否支持储蓄罐支付 0-不支持 1-支持
     */
    private String isSupPiggy;
    /***
     * 赎回去向是否支持银行卡 0-不支持 1-支持
     */
    private String redeemDirectionIsSupCardFlag;
    /***
     * 赎回去向是否支持储蓄罐 0-不支持 1-支持
     */
    private String redeemDirectionIsSupCxgFlag;
    /**
     * 代销关系标识 是否支持好买柜台 0-不支持 1-支持
     */
    private String isSupCounter;
    /***
     * 代销关系标识 是否支持好买网站 0-不支持 1-支持
     */
    private String isSupWeb;
    /***
     * 风险测评强制匹配开关 0-能 1-不能
     */
    private String testForceMatchFlag;
    /***
     * 份额控制日期
     */
    private String shareCtrlDate;
    /***
     * 产品交易通道
     */
    private String productChannel;
    /***
     * 是否允许修改分红方式
     */
    private String divMethodFlag;
    /***
     * 总人数
     */
    private Long totalPlaces;
    /***
     * 年龄控制标识 0-否 1-是
     */
    private String ageControlFlag;
    /***
     * 最大年龄
     */
    private Integer maxAge;
    /***
     * 最小年龄
     */
    private Integer minAge;
    /***
     * 申购确认T+N
     */
    private Integer purConfirmDays;
    /***
     * 赎回确认T+N
     */
    private Integer redeConfirmDays;
    /***
     * 赎回到账天数
     */
    private Integer redePaymentDays;

    private BigDecimal totalBalance;

    /**
     * 结构型产品标识，0-非结构型；1-结构型
     */
    private String structureFlag;
    

    /**
    *预约开始日期
    */
   private String appointStartDt;
   /**
    *预约开始时间
    */
   private String appointStartTm;
   /**
    *预约结束日期
    */
   private String apponitEndDt;
   /**
    *预约结束时间
    */
   private String apponitEndTm;
   /**
    *开放开始日期
    */
   private String openStartDt;
   /**
    *开放开始时间
    */
   private String openStartTm;
   /**
    *开放结束日期
    */
   private String openEndDt;
   /**
    *开放结束时间
    */
   private String openEndTm;

   /**
    * ta交易日期
    */
   private String taTradeDt;
   
   /**
    * 基金状态
    */
   private String fundStat;
   
   /**
    * 购买业务类型 0-认购 1-申购
    */
   private String buyBusiType;
   
   /**
    * 赎回去向 11每位分别代表储蓄罐银行卡
    */
   private String redeemDirectionList;
   
   /**
    * 最高申请金额
    */
   private BigDecimal maxAppAmt;

    /**
     * 底层固定收益标识0-否 1-是
     */
   private String fixedIncomeFlag;

    /**
     * 是否分次CALL款股权产品 0-否 1是
     */
    private String peDivideCallFlag;
   
    public String getRedeemDirectionList() {
    return redeemDirectionList;
}

public void setRedeemDirectionList(String redeemDirectionList) {
    this.redeemDirectionList = redeemDirectionList;
}

    public String geteContract() {
        return eContract;
    }

    public void seteContract(String eContract) {
        this.eContract = eContract;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundSubType() {
        return fundSubType;
    }

    public void setFundSubType(String fundSubType) {
        this.fundSubType = fundSubType;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getDfltDivMode() {
        return dfltDivMode;
    }

    public void setDfltDivMode(String dfltDivMode) {
        this.dfltDivMode = dfltDivMode;
    }

    public String getSuppleSubsRule() {
        return suppleSubsRule;
    }

    public void setSuppleSubsRule(String suppleSubsRule) {
        this.suppleSubsRule = suppleSubsRule;
    }

    public BigDecimal getMinAcctVol() {
        return minAcctVol;
    }

    public void setMinAcctVol(BigDecimal minAcctVol) {
        this.minAcctVol = minAcctVol;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getFundAttrPinyin() {
        return fundAttrPinyin;
    }

    public void setFundAttrPinyin(String fundAttrPinyin) {
        this.fundAttrPinyin = fundAttrPinyin;
    }

    public String getMainFundCode() {
        return mainFundCode;
    }

    public void setMainFundCode(String mainFundCode) {
        this.mainFundCode = mainFundCode;
    }

    public BigDecimal getDistributeSize() {
        return distributeSize;
    }

    public void setDistributeSize(BigDecimal distributeSize) {
        this.distributeSize = distributeSize;
    }

    public String getEndTm() {
        return endTm;
    }

    public void setEndTm(String endTm) {
        this.endTm = endTm;
    }

    public String getIpoStartDt() {
        return ipoStartDt;
    }

    public void setIpoStartDt(String ipoStartDt) {
        this.ipoStartDt = ipoStartDt;
    }

    public String getIpoEndDt() {
        return ipoEndDt;
    }

    public void setIpoEndDt(String ipoEndDt) {
        this.ipoEndDt = ipoEndDt;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getRedeOpenTerm() {
        return redeOpenTerm;
    }

    public void setRedeOpenTerm(String redeOpenTerm) {
        this.redeOpenTerm = redeOpenTerm;
    }

    public String getFundOpenMode() {
        return fundOpenMode;
    }

    public void setFundOpenMode(String fundOpenMode) {
        this.fundOpenMode = fundOpenMode;
    }

    public String getOpenDt() {
        return openDt;
    }

    public void setOpenDt(String openDt) {
        this.openDt = openDt;
    }

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag;
    }

    public String getFundClass() {
        return fundClass;
    }

    public void setFundClass(String fundClass) {
        this.fundClass = fundClass;
    }

    public Integer getProdRollRange() {
        return prodRollRange;
    }

    public void setProdRollRange(Integer prodRollRange) {
        this.prodRollRange = prodRollRange;
    }

    public Integer getProdSubDays() {
        return prodSubDays;
    }

    public void setProdSubDays(Integer prodSubDays) {
        this.prodSubDays = prodSubDays;
    }

    public Integer getProdRedmDays() {
        return prodRedmDays;
    }

    public void setProdRedmDays(Integer prodRedmDays) {
        this.prodRedmDays = prodRedmDays;
    }

    public String getHolidayStrategy() {
        return holidayStrategy;
    }

    public void setHolidayStrategy(String holidayStrategy) {
        this.holidayStrategy = holidayStrategy;
    }

    public BigDecimal getProdDiffer() {
        return prodDiffer;
    }

    public void setProdDiffer(BigDecimal prodDiffer) {
        this.prodDiffer = prodDiffer;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }

    public BigDecimal getNetMinAppAmt() {
        return netMinAppAmt;
    }

    public void setNetMinAppAmt(BigDecimal netMinAppAmt) {
        this.netMinAppAmt = netMinAppAmt;
    }

    public BigDecimal getNetMinSuppleAmt() {
        return netMinSuppleAmt;
    }

    public void setNetMinSuppleAmt(BigDecimal netMinSuppleAmt) {
        this.netMinSuppleAmt = netMinSuppleAmt;
    }

    public Integer getConfirmDays() {
        return confirmDays;
    }

    public void setConfirmDays(Integer confirmDays) {
        this.confirmDays = confirmDays;
    }

    public String getDirectOpenFlag() {
        return directOpenFlag;
    }

    public void setDirectOpenFlag(String directOpenFlag) {
        this.directOpenFlag = directOpenFlag;
    }

    public String getSupportAdvanceFlag() {
        return supportAdvanceFlag;
    }

    public void setSupportAdvanceFlag(String supportAdvanceFlag) {
        this.supportAdvanceFlag = supportAdvanceFlag;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public String getMultiCardFlag() {
        return multiCardFlag;
    }

    public void setMultiCardFlag(String multiCardFlag) {
        this.multiCardFlag = multiCardFlag;
    }

    public String getRedeemMemo() {
        return redeemMemo;
    }

    public void setRedeemMemo(String redeemMemo) {
        this.redeemMemo = redeemMemo;
    }

    public String getRiskNoticeUrl() {
        return riskNoticeUrl;
    }

    public void setRiskNoticeUrl(String riskNoticeUrl) {
        this.riskNoticeUrl = riskNoticeUrl;
    }

    public String getElecContractUrl() {
        return elecContractUrl;
    }

    public void setElecContractUrl(String elecContractUrl) {
        this.elecContractUrl = elecContractUrl;
    }

    public String getFundWebUrl() {
        return fundWebUrl;
    }

    public void setFundWebUrl(String fundWebUrl) {
        this.fundWebUrl = fundWebUrl;
    }

    public Integer getCalmTime() {
        return calmTime;
    }

    public void setCalmTime(Integer calmTime) {
        this.calmTime = calmTime;
    }

    public String getSaleAgreementUrl() {
        return saleAgreementUrl;
    }

    public void setSaleAgreementUrl(String saleAgreementUrl) {
        this.saleAgreementUrl = saleAgreementUrl;
    }

    public String getSupplyAgreementUrl1() {
        return supplyAgreementUrl1;
    }

    public void setSupplyAgreementUrl1(String supplyAgreementUrl1) {
        this.supplyAgreementUrl1 = supplyAgreementUrl1;
    }

    public String getSupplyAgreementUrl2() {
        return supplyAgreementUrl2;
    }

    public void setSupplyAgreementUrl2(String supplyAgreementUrl2) {
        this.supplyAgreementUrl2 = supplyAgreementUrl2;
    }

    public String getSupplyAgreementUrl3() {
        return supplyAgreementUrl3;
    }

    public void setSupplyAgreementUrl3(String supplyAgreementUrl3) {
        this.supplyAgreementUrl3 = supplyAgreementUrl3;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public String getIsSupSelfDrawing() {
        return isSupSelfDrawing;
    }

    public void setIsSupSelfDrawing(String isSupSelfDrawing) {
        this.isSupSelfDrawing = isSupSelfDrawing;
    }

    public String getIsSupAgentDrawing() {
        return isSupAgentDrawing;
    }

    public void setIsSupAgentDrawing(String isSupAgentDrawing) {
        this.isSupAgentDrawing = isSupAgentDrawing;
    }

    public String getIsSupPiggy() {
        return isSupPiggy;
    }

    public void setIsSupPiggy(String isSupPiggy) {
        this.isSupPiggy = isSupPiggy;
    }

    public String getRedeemDirectionIsSupCardFlag() {
        return redeemDirectionIsSupCardFlag;
    }

    public void setRedeemDirectionIsSupCardFlag(String redeemDirectionIsSupCardFlag) {
        this.redeemDirectionIsSupCardFlag = redeemDirectionIsSupCardFlag;
    }

    public String getRedeemDirectionIsSupCxgFlag() {
        return redeemDirectionIsSupCxgFlag;
    }

    public void setRedeemDirectionIsSupCxgFlag(String redeemDirectionIsSupCxgFlag) {
        this.redeemDirectionIsSupCxgFlag = redeemDirectionIsSupCxgFlag;
    }

    public String getIsSupCounter() {
        return isSupCounter;
    }

    public void setIsSupCounter(String isSupCounter) {
        this.isSupCounter = isSupCounter;
    }

    public String getIsSupWeb() {
        return isSupWeb;
    }

    public void setIsSupWeb(String isSupWeb) {
        this.isSupWeb = isSupWeb;
    }

    public String getTestForceMatchFlag() {
        return testForceMatchFlag;
    }

    public void setTestForceMatchFlag(String testForceMatchFlag) {
        this.testForceMatchFlag = testForceMatchFlag;
    }

    public String getShareCtrlDate() {
        return shareCtrlDate;
    }

    public void setShareCtrlDate(String shareCtrlDate) {
        this.shareCtrlDate = shareCtrlDate;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getDivMethodFlag() {
        return divMethodFlag;
    }

    public void setDivMethodFlag(String divMethodFlag) {
        this.divMethodFlag = divMethodFlag;
    }

    public Long getTotalPlaces() {
        return totalPlaces;
    }

    public void setTotalPlaces(Long totalPlaces) {
        this.totalPlaces = totalPlaces;
    }

    public String getAgeControlFlag() {
        return ageControlFlag;
    }

    public void setAgeControlFlag(String ageControlFlag) {
        this.ageControlFlag = ageControlFlag;
    }

    public Integer getMaxAge() {
        return maxAge;
    }

    public void setMaxAge(Integer maxAge) {
        this.maxAge = maxAge;
    }

    public Integer getMinAge() {
        return minAge;
    }

    public void setMinAge(Integer minAge) {
        this.minAge = minAge;
    }

    public Integer getPurConfirmDays() {
        return purConfirmDays;
    }

    public void setPurConfirmDays(Integer purConfirmDays) {
        this.purConfirmDays = purConfirmDays;
    }

    public Integer getRedeConfirmDays() {
        return redeConfirmDays;
    }

    public void setRedeConfirmDays(Integer redeConfirmDays) {
        this.redeConfirmDays = redeConfirmDays;
    }

    public Integer getRedePaymentDays() {
        return redePaymentDays;
    }

    public void setRedePaymentDays(Integer redePaymentDays) {
        this.redePaymentDays = redePaymentDays;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public String getStructureFlag() {
        return structureFlag;
    }

    public void setStructureFlag(String structureFlag) {
        this.structureFlag = structureFlag;
    }

    public String getAppointStartDt() {
        return appointStartDt;
    }

    public void setAppointStartDt(String appointStartDt) {
        this.appointStartDt = appointStartDt;
    }

    public String getAppointStartTm() {
        return appointStartTm;
    }

    public void setAppointStartTm(String appointStartTm) {
        this.appointStartTm = appointStartTm;
    }

    public String getApponitEndDt() {
        return apponitEndDt;
    }

    public void setApponitEndDt(String apponitEndDt) {
        this.apponitEndDt = apponitEndDt;
    }

    public String getApponitEndTm() {
        return apponitEndTm;
    }

    public void setApponitEndTm(String apponitEndTm) {
        this.apponitEndTm = apponitEndTm;
    }

    public String getOpenStartDt() {
        return openStartDt;
    }

    public void setOpenStartDt(String openStartDt) {
        this.openStartDt = openStartDt;
    }

    public String getOpenStartTm() {
        return openStartTm;
    }

    public void setOpenStartTm(String openStartTm) {
        this.openStartTm = openStartTm;
    }

    public String getOpenEndDt() {
        return openEndDt;
    }

    public void setOpenEndDt(String openEndDt) {
        this.openEndDt = openEndDt;
    }

    public String getOpenEndTm() {
        return openEndTm;
    }

    public void setOpenEndTm(String openEndTm) {
        this.openEndTm = openEndTm;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public String getBuyBusiType() {
        return buyBusiType;
    }

    public void setBuyBusiType(String buyBusiType) {
        this.buyBusiType = buyBusiType;
    }

    public BigDecimal getMaxAppAmt() {
        return maxAppAmt;
    }

    public void setMaxAppAmt(BigDecimal maxAppAmt) {
        this.maxAppAmt = maxAppAmt;
    }

    public String getFixedIncomeFlag() {
        return fixedIncomeFlag;
    }

    public void setFixedIncomeFlag(String fixedIncomeFlag) {
        this.fixedIncomeFlag = fixedIncomeFlag;
    }

    public String getPeDivideCallFlag() {
        return peDivideCallFlag;
    }

    public void setPeDivideCallFlag(String peDivideCallFlag) {
        this.peDivideCallFlag = peDivideCallFlag;
    }
}

