/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(柜台交易统计) 
 * <AUTHOR>
 * @date 2017年4月11日 上午11:24:48
 * @since JDK 1.6
 */
public class CounterTradeDto implements  Serializable {
    

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 6702921775550546696L;
   /**
    * 柜台交易码
    */
    private String txCode;
    /**
     * 总申请笔数
     */
    private Long totalAppNum;
    
    /**
     * 未审核笔数
     */
    private Long notCheckedNum;
    
    /**
     * 已审核笔数
     */
    private long checkedNum;
    
    /**
     * 审核通过笔数
     */
    private long checkedSuccNum;
    
    /**
     * 审核失败笔数
     */
    private long checkedFaildNum;
    
    /**
     * 申请成功笔数
     */
    private long appSuccNum;
    
    /**
     * 申请失败笔数
     */
    private long appFaildNum;
    
    /**
     * 等待申请笔数
     */
    private long appWaitedNum;
    
    

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public Long getTotalAppNum() {
        return totalAppNum;
    }

    public void setTotalAppNum(Long totalAppNum) {
        this.totalAppNum = totalAppNum;
    }

    public Long getNotCheckedNum() {
        return notCheckedNum;
    }

    public void setNotCheckedNum(Long notCheckedNum) {
        this.notCheckedNum = notCheckedNum;
    }

    public long getCheckedNum() {
        return checkedNum;
    }

    public void setCheckedNum(long checkedNum) {
        this.checkedNum = checkedNum;
    }

    public long getCheckedSuccNum() {
        return checkedSuccNum;
    }

    public void setCheckedSuccNum(long checkedSuccNum) {
        this.checkedSuccNum = checkedSuccNum;
    }

    public long getCheckedFaildNum() {
        return checkedFaildNum;
    }

    public void setCheckedFaildNum(long checkedFaildNum) {
        this.checkedFaildNum = checkedFaildNum;
    }

    public long getAppSuccNum() {
        return appSuccNum;
    }

    public void setAppSuccNum(long appSuccNum) {
        this.appSuccNum = appSuccNum;
    }

    public long getAppFaildNum() {
        return appFaildNum;
    }

    public void setAppFaildNum(long appFaildNum) {
        this.appFaildNum = appFaildNum;
    }

    public long getAppWaitedNum() {
        return appWaitedNum;
    }

    public void setAppWaitedNum(long appWaitedNum) {
        this.appWaitedNum = appWaitedNum;
    }
    
}

