/**
 *Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;

import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.QueryCustTransInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(查询客户经办人信息) 
 * @reason:
 * <AUTHOR>
 * @date 2018年4月12日 下午8:02:01
 * @since JDK 1.6
 */
@Controller
public class QueryCustTransInfoController{
    private static Logger logger = LogManager.getLogger(QueryCustTransInfoController.class);
    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    
    @RequestMapping("/tmscounter/querycusttransinfo.htm")
    public ModelAndView queryCustTransInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String  txAcctNo = request.getParameter("txAcctNo");
        String disCode = request.getParameter("disCode");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        QueryCustTransInfoDto  queryCustTransInfoDto = tmsCounterOutService.queryCustTransInfo(txAcctNo, disInfoDto);
        logger.info("queryCustTransInfo|queryCustTransInfoDto:{}", JSON.toJSONString(queryCustTransInfoDto));
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(queryCustTransInfoDto);
        WebUtil.write(response, tmsCounterResult);
        return null;
    }
}

