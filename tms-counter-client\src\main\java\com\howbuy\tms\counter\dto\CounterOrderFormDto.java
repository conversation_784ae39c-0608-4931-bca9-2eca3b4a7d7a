package com.howbuy.tms.counter.dto;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 
 * @description:(柜台订单表单json)
 * <AUTHOR>
 * @date 2018年3月12日 下午5:06:27
 * @since JDK 1.6
 */
public class CounterOrderFormDto implements Serializable{
    
    
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预约开放日历信息
     */
    private CounterProductAppointmentInfoBean counterProductAppointmentInfoBean;
    
    /**
     * 客户持有份额明细
     */
    private List<CounterCustBalanceVolDtlBean> dtlBeanList;
    
    /**
     * 产品基本信息
     */
    private CounterHighProductBaseBean  counterHighProductBaseBean;

    /**
     * 产品复购信息
     */
    private CounterRepurChaseInfoBean counterRepurChaseInfoBean;
    
    /**
     * 原订单
     */
    private SourceDealOrderBean sourceDealOrderBean;

    private RefundBean refundBean;
    
    public SourceDealOrderBean getSourceDealOrderBean() {
        return sourceDealOrderBean;
    }


    public void setSourceDealOrderBean(SourceDealOrderBean sourceDealOrderBean) {
        this.sourceDealOrderBean = sourceDealOrderBean;
    }

    public CounterRepurChaseInfoBean getCounterRepurChaseInfoBean() {
        return counterRepurChaseInfoBean;
    }

    public void setCounterRepurChaseInfoBean(CounterRepurChaseInfoBean counterRepurChaseInfoBean) {
        this.counterRepurChaseInfoBean = counterRepurChaseInfoBean;
    }

    public RefundBean getRefundBean() {
        return refundBean;
    }

    public void setRefundBean(RefundBean refundBean) {
        this.refundBean = refundBean;
    }

    public static class SourceDealOrderBean implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = 1L;

        /**
         * 客户订单号
         */
        private String dealNo;
        /**
         * 交易账号
         */
        private String txAcctNo;

        /**
         * 银行账号
         */
        private String bankAcct;

        /**
         * 银行代码
         */
        private String bankCode;

        /**
         * 支付方式 01-自划款 04-代扣款 06-储蓄罐
         */
        private String paymentType;

        /**
         * 申请金额
         */
        private BigDecimal appAmt;

        /**
         * 申请份额
         */
        private BigDecimal appVol;

        /**
         * 申请日期
         */
        private String appDate;

        /**
         * 申请时间
         */
        private String appTime;

        /**
         * 申请日期时间
         */
        private Date appDtm;


        /**
         * 付款状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败
         */
        private String payStatus;

        /**
         * 订单状态 1-申请成功 2-部分确认 3-确认成功 4-确认失败 5-自行撤销 6-强制取消
         */
        private String orderStatus;


        public String getSubmitTaDt() {
            return submitTaDt;
        }
        public void setSubmitTaDt(String submitTaDt) {
            this.submitTaDt = submitTaDt;
        }
        /**
         * 手续费
         */
        private BigDecimal feeRate;
        /**
         * 中台业务码
         */
        private String mBusiCode;
        /**
         * 上报TA日期
         */
        private String submitTaDt;
        /**
         * 购买净金额下限
         */
        private BigDecimal minPurchaseAmt;
        /**
         * 追加购买净金额下限
         */
        private BigDecimal minAddPurchaseAmt;
        
        public String getDealNo() {
            return dealNo;
        }
        public void setDealNo(String dealNo) {
            this.dealNo = dealNo;
        }
        public String getTxAcctNo() {
            return txAcctNo;
        }
        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }
        public String getBankAcct() {
            return bankAcct;
        }
        public void setBankAcct(String bankAcct) {
            this.bankAcct = bankAcct;
        }
        public String getBankCode() {
            return bankCode;
        }
        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }
        public String getPaymentType() {
            return paymentType;
        }
        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }
        public BigDecimal getAppAmt() {
            return appAmt;
        }
        public void setAppAmt(BigDecimal appAmt) {
            this.appAmt = appAmt;
        }
        public BigDecimal getAppVol() {
            return appVol;
        }
        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }
        public String getAppDate() {
            return appDate;
        }
        public void setAppDate(String appDate) {
            this.appDate = appDate;
        }
        public String getAppTime() {
            return appTime;
        }
        public void setAppTime(String appTime) {
            this.appTime = appTime;
        }
        public Date getAppDtm() {
            return appDtm;
        }
        public void setAppDtm(Date appDtm) {
            this.appDtm = appDtm;
        }
        public String getPayStatus() {
            return payStatus;
        }
        public void setPayStatus(String payStatus) {
            this.payStatus = payStatus;
        }
        public String getOrderStatus() {
            return orderStatus;
        }
        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }
        public BigDecimal getFeeRate() {
            return feeRate;
        }
        public void setFeeRate(BigDecimal feeRate) {
            this.feeRate = feeRate;
        }
        public String getmBusiCode() {
            return mBusiCode;
        }
        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }
        public BigDecimal getMinPurchaseAmt() {
            return minPurchaseAmt;
        }
        public void setMinPurchaseAmt(BigDecimal minPurchaseAmt) {
            this.minPurchaseAmt = minPurchaseAmt;
        }
        public BigDecimal getMinAddPurchaseAmt() {
            return minAddPurchaseAmt;
        }
        public void setMinAddPurchaseAmt(BigDecimal minAddPurchaseAmt) {
            this.minAddPurchaseAmt = minAddPurchaseAmt;
        }

    }


    public CounterProductAppointmentInfoBean getCounterProductAppointmentInfoBean() {
        return counterProductAppointmentInfoBean;
    }


    public void setCounterProductAppointmentInfoBean(CounterProductAppointmentInfoBean counterProductAppointmentInfoBean) {
        this.counterProductAppointmentInfoBean = counterProductAppointmentInfoBean;
    }

    public List<CounterCustBalanceVolDtlBean> getDtlBeanList() {
        return dtlBeanList;
    }

    public void setDtlBeanList(List<CounterCustBalanceVolDtlBean> dtlBeanList) {
        this.dtlBeanList = dtlBeanList;
    }

    public CounterHighProductBaseBean getCounterHighProductBaseBean() {
        return counterHighProductBaseBean;
    }


    public void setCounterHighProductBaseBean(CounterHighProductBaseBean counterHighProductBaseBean) {
        this.counterHighProductBaseBean = counterHighProductBaseBean;
    }


    public static class CounterOrderFormBean implements Serializable{

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */
        
        private static final long serialVersionUID = 1L;
        
        private String dealAppNo;

        private String txAcctNo;

        private String subTxAcctNo;

        private String disCode;

        private String cpAcctNo;

        private String fundCode;

        private String fundShareClass;

        private String tFundCode;

        private String tFundShareClass;

        private BigDecimal appAmt;

        private BigDecimal appVol;

        private String appDt;

        private String appTm;

        private String txCode;

        private String paymentType;

        private BigDecimal discountRate;

        private String protocolType;

        private String protocolNo;

        private String checkFlag;

        private String fundDivMode;

        private String dealNo;

        private String transactorIdNo;

        private String transactorIdType;

        private String transactorName;

        private String operatorNo;

        private String consCode;

        private String returnCode;

        private String description;

        private String memo;

        private String creator;

        private String checker;

        private String modifier;

        private String appFlag;

        private String custName;

        private String fundName;

        private String largeRedmFlag;

        private String agentFlag;

        private Date createDtm;

        private Date checkDtm;

        private Date updateDtm;

        private String idNo;

        private String bankAcct;

        private String bankCode;

        private String taTradeDt;

        private BigDecimal nav;

        private String unusualTransType;

        private String riskFlag; // 风险确认标记：1-确认，0-未确认

        private BigDecimal esitmateFee;

        private BigDecimal appointmentDiscount;

        /**
         * 预约金额
         */
        private BigDecimal advanceAmt;

        /**
         * 预约订单号
         */
       private String appointmentDealNo;
        
       private String outletCode;
        
        /**
         * 赎回去向
         */
        private String redeemCapitalFlag;

        private String productClass;

        private BigDecimal feeRate;

        private String tFundName;
       
        private String idType;

        private String orderFormType;
       
        /**
        * 中台业务码
        */
        private String mBusiCode;
       
        /**
        * 强制撤单标识(柜台校验必传): 0-强制; 1-非强制
        */
       private String forceCancelFlag;
       
       /**
        * 撤单原因
        */
       private String cancelMemo;

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getSubTxAcctNo() {
        return subTxAcctNo;
    }

    public void setSubTxAcctNo(String subTxAcctNo) {
        this.subTxAcctNo = subTxAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String gettFundCode() {
        return tFundCode;
    }

    public void settFundCode(String tFundCode) {
        this.tFundCode = tFundCode;
    }

    public String gettFundShareClass() {
        return tFundShareClass;
    }

    public void settFundShareClass(String tFundShareClass) {
        this.tFundShareClass = tFundShareClass;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag;
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getCheckDtm() {
        return checkDtm;
    }

    public void setCheckDtm(Date checkDtm) {
        this.checkDtm = checkDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getUnusualTransType() {
        return unusualTransType;
    }

    public void setUnusualTransType(String unusualTransType) {
        this.unusualTransType = unusualTransType;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public BigDecimal getEsitmateFee() {
        return esitmateFee;
    }

    public void setEsitmateFee(BigDecimal esitmateFee) {
        this.esitmateFee = esitmateFee;
    }

    public BigDecimal getAppointmentDiscount() {
        return appointmentDiscount;
    }

    public void setAppointmentDiscount(BigDecimal appointmentDiscount) {
        this.appointmentDiscount = appointmentDiscount;
    }

    public BigDecimal getAdvanceAmt() {
        return advanceAmt;
    }

    public void setAdvanceAmt(BigDecimal advanceAmt) {
        this.advanceAmt = advanceAmt;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getRedeemCapitalFlag() {
        return redeemCapitalFlag;
    }

    public void setRedeemCapitalFlag(String redeemCapitalFlag) {
        this.redeemCapitalFlag = redeemCapitalFlag;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String gettFundName() {
        return tFundName;
    }

    public void settFundName(String tFundName) {
        this.tFundName = tFundName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getOrderFormType() {
        return orderFormType;
    }

    public void setOrderFormType(String orderFormType) {
        this.orderFormType = orderFormType;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getForceCancelFlag() {
        return forceCancelFlag;
    }

    public void setForceCancelFlag(String forceCancelFlag) {
        this.forceCancelFlag = forceCancelFlag;
    }

    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }
       
        
    }
    
    
     public static class CounterProductAppointmentInfoBean implements Serializable {

       private static final long serialVersionUID = 1L;

       private String appointId;

       private String strategyId;

       private String midProductId;

       private String shareClass;

       private String productId;

       private String mBusiCode;

       private String appointStartDt;

       private String appointStartTm;

       private String apponitEndDt;

       private String apponitEndTm;

       private String openStartDt;

       private String openStartTm;

       private String openEndDt;

       private String openEndTm;

       private String payDeadlineDtm;

       private BigDecimal payRatio;

         public BigDecimal getPayRatio() {
             return payRatio;
         }

         public void setPayRatio(BigDecimal payRatio) {
             this.payRatio = payRatio;
         }

         public String getAppointId() {
        return appointId;
    }

    public void setAppointId(String appointId) {
        this.appointId = appointId;
    }

    public String getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public String getMidProductId() {
        return midProductId;
    }

    public void setMidProductId(String midProductId) {
        this.midProductId = midProductId;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getAppointStartDt() {
        return appointStartDt;
    }

    public void setAppointStartDt(String appointStartDt) {
        this.appointStartDt = appointStartDt;
    }

    public String getAppointStartTm() {
        return appointStartTm;
    }

    public void setAppointStartTm(String appointStartTm) {
        this.appointStartTm = appointStartTm;
    }

    public String getApponitEndDt() {
        return apponitEndDt;
    }

    public void setApponitEndDt(String apponitEndDt) {
        this.apponitEndDt = apponitEndDt;
    }

    public String getApponitEndTm() {
        return apponitEndTm;
    }

    public void setApponitEndTm(String apponitEndTm) {
        this.apponitEndTm = apponitEndTm;
    }

    public String getOpenStartDt() {
        return openStartDt;
    }

    public void setOpenStartDt(String openStartDt) {
        this.openStartDt = openStartDt;
    }

    public String getOpenStartTm() {
        return openStartTm;
    }

    public void setOpenStartTm(String openStartTm) {
        this.openStartTm = openStartTm;
    }

    public String getOpenEndDt() {
        return openEndDt;
    }

    public void setOpenEndDt(String openEndDt) {
        this.openEndDt = openEndDt;
    }

    public String getOpenEndTm() {
        return openEndTm;
    }

    public void setOpenEndTm(String openEndTm) {
        this.openEndTm = openEndTm;
    }

    public String getPayDeadlineDtm() {
        return payDeadlineDtm;
    }

    public void setPayDeadlineDtm(String payDeadlineDtm) {
        this.payDeadlineDtm = payDeadlineDtm;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

   }
    

   public static class CounterCustBalanceVolDtlBean implements Serializable{
       
       /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;
       /**
        * 总份额
        */
       private BigDecimal balanceVol;
       /**
        * 可用份额
        */
       private BigDecimal availVol;
       /**
        * 待确认份额
        */
       private BigDecimal unconfirmedVol;
       /**
        * 市值
        */
       private BigDecimal marketValue;
       /**
        * 开放赎回日期
        */
       private String openRedeDt;
       private String cpAcctNo;
       private String bankAcctNo;
       private BigDecimal appVol;

       /**
        * 回款方向
        */
       private String redeemCapitalFlag;
       /**
        * 回款到可用金额
        */
       private BigDecimal refundFinaAvailAmt;
       /**
        * 回款备注
        */
       private String refundFinaAvailMemo;

        public BigDecimal getBalanceVol() {
            return balanceVol;
        }
        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }
        public BigDecimal getAvailVol() {
            return availVol;
        }
        public void setAvailVol(BigDecimal availVol) {
            this.availVol = availVol;
        }
        public BigDecimal getUnconfirmedVol() {
            return unconfirmedVol;
        }
        public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
            this.unconfirmedVol = unconfirmedVol;
        }
        public BigDecimal getMarketValue() {
            return marketValue;
        }
        public void setMarketValue(BigDecimal marketValue) {
            this.marketValue = marketValue;
        }
        public String getOpenRedeDt() {
            return openRedeDt;
        }
        public void setOpenRedeDt(String openRedeDt) {
        this.openRedeDt = openRedeDt;
    }

       public String getCpAcctNo() {
           return cpAcctNo;
       }

       public void setCpAcctNo(String cpAcctNo) {
           this.cpAcctNo = cpAcctNo;
       }

       public String getBankAcctNo() {
           return bankAcctNo;
       }

       public void setBankAcctNo(String bankAcctNo) {
           this.bankAcctNo = bankAcctNo;
       }

       public BigDecimal getAppVol() {
           return appVol;
       }

       public void setAppVol(BigDecimal appVol) {
           this.appVol = appVol;
       }

       public String getRedeemCapitalFlag() {
           return redeemCapitalFlag;
       }

       public void setRedeemCapitalFlag(String redeemCapitalFlag) {
           this.redeemCapitalFlag = redeemCapitalFlag;
       }

       public BigDecimal getRefundFinaAvailAmt() {
           return refundFinaAvailAmt;
       }

       public void setRefundFinaAvailAmt(BigDecimal refundFinaAvailAmt) {
           this.refundFinaAvailAmt = refundFinaAvailAmt;
       }

       public String getRefundFinaAvailMemo() {
           return refundFinaAvailMemo;
       }

       public void setRefundFinaAvailMemo(String refundFinaAvailMemo) {
           this.refundFinaAvailMemo = refundFinaAvailMemo;
       }
   }
   

   /**
    * @description:(高端产品基金信息)
    * @reason:
    * <AUTHOR>
    * @date 2017年4月12日 下午3:24:44
    * @since JDK 1.6
    */
   public static class CounterHighProductBaseBean implements Serializable{
       
       /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;
    /**
        * 是否支持电子合同 1支持 0 是不支持
        */
       private String eContract;
       /**
        * 基金代码
        */
       private String fundCode;
       /**
        * 基金名称
        */
       private String fundName;
       /**
        * 基金类型
        */
       private String fundType;
       /**
        * 基金二级类型
        */
       private String fundSubType;
       /**
        * TA代码
        */
       private String taCode;
       /**
        * 基金默认分红方式
        */
       private String dfltDivMode;
       /**
        * 追加申购判断规则
        */
       private String suppleSubsRule;
       /**
        * 最低持有份额
        */
       private BigDecimal minAcctVol;
       /**
        * 基金风险等级
        */
       private String fundRiskLevel;
       /**
        * 基金简称拼音
        */
       private String fundAttrPinyin;
       /**
        * 主基金代码
        */
       private String mainFundCode;
       /**
        * 交易截止时间
        */
       private String endTm;
       /**
        * 募集开始日期
        */
       private String ipoStartDt;
       /**
        * 募集结束日期
        */
       private String ipoEndDt;
       /**
        * 基金简称
        */
       private String fundAttr;
       /**
        * 开通日期
        */
       private String openDt;
       /**
        * 基金类别
        */
       private String fundClass;
       /**
        * 份额类型
        */
       private String shareClass;
       /**
        * TA名称
        */
       private String taName;
       /**
        * 首次最低申请金额（净购买金额）
        */
       private BigDecimal netMinAppAmt;
       /**
        * 最低追加申请金额（净追加金额）
        */
       private BigDecimal netMinSuppleAmt;
       /***
        * 手续费计算类型
        */
       private String feeCalMode;
       /***
        * 产品交易通道
        */
       private String productChannel;
       
       /**
        * 基金状态
        */
       private String fundStat;
       
       /**
        * 购买业务类型 0-认购 1-申购
        */
       private String buyBusiType;

     

    public String geteContract() {
        return eContract;
    }

    public void seteContract(String eContract) {
        this.eContract = eContract;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundSubType() {
        return fundSubType;
    }

    public void setFundSubType(String fundSubType) {
        this.fundSubType = fundSubType;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getDfltDivMode() {
        return dfltDivMode;
    }

    public void setDfltDivMode(String dfltDivMode) {
        this.dfltDivMode = dfltDivMode;
    }

    public String getSuppleSubsRule() {
        return suppleSubsRule;
    }

    public void setSuppleSubsRule(String suppleSubsRule) {
        this.suppleSubsRule = suppleSubsRule;
    }

    public BigDecimal getMinAcctVol() {
        return minAcctVol;
    }

    public void setMinAcctVol(BigDecimal minAcctVol) {
        this.minAcctVol = minAcctVol;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getFundAttrPinyin() {
        return fundAttrPinyin;
    }

    public void setFundAttrPinyin(String fundAttrPinyin) {
        this.fundAttrPinyin = fundAttrPinyin;
    }

    public String getMainFundCode() {
        return mainFundCode;
    }

    public void setMainFundCode(String mainFundCode) {
        this.mainFundCode = mainFundCode;
    }

    public String getEndTm() {
        return endTm;
    }

    public void setEndTm(String endTm) {
        this.endTm = endTm;
    }

    public String getIpoStartDt() {
        return ipoStartDt;
    }

    public void setIpoStartDt(String ipoStartDt) {
        this.ipoStartDt = ipoStartDt;
    }

    public String getIpoEndDt() {
        return ipoEndDt;
    }

    public void setIpoEndDt(String ipoEndDt) {
        this.ipoEndDt = ipoEndDt;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getOpenDt() {
        return openDt;
    }

    public void setOpenDt(String openDt) {
        this.openDt = openDt;
    }

    public String getFundClass() {
        return fundClass;
    }

    public void setFundClass(String fundClass) {
        this.fundClass = fundClass;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public String getTaName() {
        return taName;
    }

    public void setTaName(String taName) {
        this.taName = taName;
    }

    public BigDecimal getNetMinAppAmt() {
        return netMinAppAmt;
    }

    public void setNetMinAppAmt(BigDecimal netMinAppAmt) {
        this.netMinAppAmt = netMinAppAmt;
    }

    public BigDecimal getNetMinSuppleAmt() {
        return netMinSuppleAmt;
    }

    public void setNetMinSuppleAmt(BigDecimal netMinSuppleAmt) {
        this.netMinSuppleAmt = netMinSuppleAmt;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public String getBuyBusiType() {
        return buyBusiType;
    }

    public void setBuyBusiType(String buyBusiType) {
        this.buyBusiType = buyBusiType;
    }
    
    

   }

   public static class FundLimitBean implements Serializable {
       private static final long serialVersionUID = 1L;
       /**
        * 首次最低申请金额（净购买金额）
        */
       private BigDecimal netMinAppAmt;
       /**
        * 最低追加申请金额（净追加金额）
        */
       private BigDecimal netMinSuppleAmt;
       
        public BigDecimal getNetMinAppAmt() {
            return netMinAppAmt;
        }
        public void setNetMinAppAmt(BigDecimal netMinAppAmt) {
            this.netMinAppAmt = netMinAppAmt;
        }
        public BigDecimal getNetMinSuppleAmt() {
            return netMinSuppleAmt;
        }
        public void setNetMinSuppleAmt(BigDecimal netMinSuppleAmt) {
            this.netMinSuppleAmt = netMinSuppleAmt;
        }
   }

   public static class CounterRepurChaseInfoBean implements Serializable{
       private static final long serialVersionUID = 1L;
       /**
        *持有份额(修改复购协议特有)
        */
       private BigDecimal balanceVol;
       /**
        *赎回份额(修改复购协议特有)
        */
       private BigDecimal redeemVol;
       /**
        *冻结份额(修改复购协议特有)
        */
       private BigDecimal frznVol;
       /**
        *冻结份额(预计到期日期)
        */
       private List<String> expectedDueDt;

       /**
        *复购协议号
        */
       private String repurchaseProtocolNo;

       public BigDecimal getRedeemVol() {
           return redeemVol;
       }

       public void setRedeemVol(BigDecimal redeemVol) {
           this.redeemVol = redeemVol;
       }

       public BigDecimal getFrznVol() {
           return frznVol;
       }

       public void setFrznVol(BigDecimal frznVol) {
           this.frznVol = frznVol;
       }

       public List<String> getExpectedDueDt() {
           return expectedDueDt;
       }

       public void setExpectedDueDt(List<String> expectedDueDt) {
           this.expectedDueDt = expectedDueDt;
       }

       public String getRepurchaseProtocolNo() {
           return repurchaseProtocolNo;
       }

       public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
           this.repurchaseProtocolNo = repurchaseProtocolNo;
       }

       public BigDecimal getBalanceVol() {
           return balanceVol;
       }

       public void setBalanceVol(BigDecimal balanceVol) {
           this.balanceVol = balanceVol;
       }
   }

    public static class RefundBean implements Serializable {

        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 回款方向
         */
        private String refundDirection;
        /**
         * 回款到可用金额
         */
        private BigDecimal refundFinaAvailAmt;
        /**
         * 回款备注
         */
        private String refundFinaAvailMemo;

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getRefundDirection() {
            return refundDirection;
        }

        public void setRefundDirection(String refundDirection) {
            this.refundDirection = refundDirection;
        }

        public BigDecimal getRefundFinaAvailAmt() {
            return refundFinaAvailAmt;
        }

        public void setRefundFinaAvailAmt(BigDecimal refundFinaAvailAmt) {
            this.refundFinaAvailAmt = refundFinaAvailAmt;
        }

        public String getRefundFinaAvailMemo() {
            return refundFinaAvailMemo;
        }

        public void setRefundFinaAvailMemo(String refundFinaAvailMemo) {
            this.refundFinaAvailMemo = refundFinaAvailMemo;
        }
    }

}