/***
 *基金信息
 *<AUTHOR>
 *@date 2017-07-03 10:39
 */

$(function(){
	QueryFundInfo.fundInfo ={};
	QueryFundInfo.tfundInfo = {};
});

var QueryFundInfo ={

	/**
	 * 查询基金信息
	 *
	 * fundCode 基金代码 可传可不传
	 */
	queryFundInfo:function(fundCode){
		if(!fundCode){
			fundCode = $("#fundCode").val();
		}

		var uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":fundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryFundInfo.queryFundInfoCallBack);
	},

	/**
	 * 处理基金信息
	 */
	queryFundInfoCallBack:function(data){

		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		QueryFundInfo.fundInfo = fundInfo;

		var isCommonFund = QueryFundInfo.checkFundInfo(fundInfo);

		if(!isCommonFund){
			return false;
		}

		if($("#fundName").length > 0){
			$("#fundName").html(fundInfo.fundAttr || '');
		}

		if($("#fundRiskLevel").length > 0){
			$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, fundInfo.fundRiskLevel, ''));
		}

		if($("#fundStatus").length > 0){
			$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundInfo.fundStat));
		}
	},

	queryTFundInfo:function(tFundCode){
		if(!tFundCode){
			tFundCode = $("#tFundCode").val();
		}
		var  uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":tFundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryFundInfo.queryTFundInfoCallBack);
	},

	queryTFundInfoCallBack:function(data){

		var bodyData = data.body || {};
		QueryFundInfo.tfundInfo = bodyData.fundInfo || {};
		QueryFundInfo.checkFundInfo(QueryFundInfo.tfundInfo);

		if($("#tFundName").length > 0){
			$("#tFundName").html(QueryFundInfo.tfundInfo.fundAttr || '');
		}

		if($("#tFundRiskLevel").length > 0){
			$("#tFundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, QueryFundInfo.tfundInfo.fundRiskLevel, ''));
		}

		if($("#tFundStatus").length > 0){
			$("#tFundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, QueryFundInfo.tfundInfo.fundStat));
		}
	},

	checkFundInfo:function(fundInfo){
		if(CommonUtil.isEmpty(fundInfo.fundCode)){
			CommonUtil.layer_tip("没有查询到此产品");
			return false;
		}

		if('5' == fundInfo.fundStat || '4' == fundInfo.fundStat){
//				CommonUtil.layer_tip("此产品不开放认申购");
		}

		if("7" == fundInfo.fundType || '9' == fundInfo.fundType){
			CommonUtil.layer_tip("该基金不是普通公募基金");
			return false;
		}
		// 去除理财型基金限制
		// if("2" == fundInfo.fundType &&  '21'== fundInfo.fundSubType){
		// 	CommonUtil.layer_tip("该基金不是普通公募基金");
		// 	return false;
		// }

		return true;
	},


	/**
	 * 查询客户持仓
	 */
	queryCustHodlInfo:function(order){
		var uri= TmsCounterConfig.FUND_QUERY_CUST_HODL_INFO_URL ||  {};
		var custNo = QueryCustInfo.custInfo.custNo || '';
		var disCode = QueryCustInfo.custInfo.disCode || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		var fundCode ='';
		var appDt ='';
		var appTm ='';
		var protocolType ='';
		if(!order){
			fundCode = $("#fundCode").val();
			appDt = $("#appDt").val();
			appTm = $("#appTm").val();
		}else {
			appDt = order.appDt;
			appTm = order.appTm;
			fundCode = order.fundCode;
			protocolType = order.protocolType;
		}
		var reqparamters = {'protocolType':protocolType,'appDt':appDt,'appTm':appTm,"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryFundInfo.queryCustHoldFundInfoCallBack);
	},

	/**
	 * 处理基金持仓信息
	 */
	queryCustHoldFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		var dtlResp = bodyData || {};
		QueryFundInfo.fundHold = dtlResp || {};
		QueryFundInfo.dtlList = dtlResp.balanceDtlList || [];
		//console.log(QueryFundInfo.dtlList);

		if(QueryFundInfo.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
		}

		var selectHtml ='';
		$(QueryFundInfo.dtlList).each(function(index,element){
			selectHtml +='<option indexnum ="'+index+'" value="'+element.cpAcctNo+'">'+element.bankName+''+element.bankAcctNo+' </option>';
		});

		$("#selectBank").html(selectHtml);
		$("#selectBank").change(function(){
			var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
			var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
			if($("#bankCode").length > 0){
				$("#bankCode").val(selectDtl.bankCode);
			}
		});

		$("#selectBank").change();

		if(QueryFundInfo.dtlList.length >0){
			var selectDtl = QueryFundInfo.dtlList[0] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
		}
	},

	queryDiscount:function(){
		var uri= TmsCounterConfig.FUND_CAL_DISCOUNT_RATE_URL ||  {};

		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		if(CommonUtil.isEmpty(custInfoForm)){
			showMsg("请先选择客户信息，以计算费率");
			return false;
		}

		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请先选择基金");
			return false;
		}
		var bankCode = $('#selectBank').find('option:selected').attr('bankCode');
		if(CommonUtil.isEmpty(bankCode) && QueryCustInfo.custBanks.length > 0){
			bankCode = QueryCustInfo.custBanks[0].bankCode;
		}

		var reqparamters = {};
		reqparamters.custInfoForm = custInfoForm;
		reqparamters.fundCode = fundCode;
		reqparamters.bankCode = bankCode;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, QueryFundInfo.queryDiscountCallBack);
	},

	queryDiscountCallBack:function(data){
		var bodyData = data.body || {};
		var respData = bodyData.respData || [];
		$("#discountRate").val(respData.discountRate);
	},

	/**
	 * 查询产品信息
	 * 目前只返回2种  投顾 或者 单基金
	 */
	queryAdviserOrFundProduct:function(productCode, callback){
		var uri= TmsCounterConfig.QUERY_PRODUCT_INFO_URL ||  {};
		var reqparamters = {"productCode":productCode, "onlySearchProduct": "1"};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var productInfo = data.body || {};

			if(CommonUtil.isEmpty(productInfo.productCode)){
				CommonUtil.layer_tip("没有查询到此产品");
				return false;
			}

			callback(productInfo);
		});
	},


	/**
	 * 查询产品信息
	 * 目前查询自己按组合
	 */
	querySelfFundProduct:function(productCode, callback){
		var uri= TmsCounterConfig.QUERY_SELF_PRODUCT_INFO_URL ||  {};
		var reqparamters = {"productCode":productCode, "custInfoForm": JSON.stringify(QueryCustInfo.custInfo), "onlySearchProduct": "1"};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var productInfo = data.body || {};

			if(CommonUtil.isEmpty(productInfo.productCode)){
				CommonUtil.layer_tip("没有查询到此产品");
				return false;
			}

			callback(productInfo);
		});
	},
}