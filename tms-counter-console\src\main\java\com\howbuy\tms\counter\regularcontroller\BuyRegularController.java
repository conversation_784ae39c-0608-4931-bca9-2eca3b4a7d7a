/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.validator.account.CustRiskValidator;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/***
 * 
 * @description:(认申购)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年9月15日 上午9:34:42
 * @since JDK 1.6
 */
@Controller
public class BuyRegularController extends AbstractController {
    private static Logger logger = LogManager.getLogger(BuyRegularController.class);

    /***
     * 
     * buyConfirm:(确认购买)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年6月22日 上午10:38:02
     */
    @RequestMapping("/tmscounter/regular/buyconfirm.htm")
    public ModelAndView buyConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String buyConfirmCmd = request.getParameter("buyConfirmForm");
        String custInfoForm = request.getParameter("custInfoForm");
        String productInfoForm = request.getParameter("productInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.info("BuyController|buyConfirm|buyConfirmCmd:{},custInfoForm:{},fundInfoForm:{},transactorInfoForm:{}", buyConfirmCmd, custInfoForm,
                productInfoForm, transactorInfoForm);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        RegularCounterPurchaseReqDto counterPurchaseReqDto = JSON.parseObject(buyConfirmCmd, RegularCounterPurchaseReqDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        QueryRegularProductDto productInfo = JSON.parseObject(productInfoForm, QueryRegularProductDto.class);

        counterPurchaseReqDto.setDealAppNo(dealAppNo);
//        counterPurchaseReqDto.setBankCode(counterPurchaseReqDto.getBankCode());
//        counterPurchaseReqDto.setCpAcctNo(counterPurchaseReqDto.getCpAcctNo());
//        counterPurchaseReqDto.setAppAmt(counterPurchaseReqDto.getAppAmt());
//        counterPurchaseReqDto.setAppDt(counterPurchaseReqDto.getAppDt());
//        counterPurchaseReqDto.setAppTm(counterPurchaseReqDto.getAppTm());
//        counterPurchaseReqDto.setBankAcct(counterPurchaseReqDto.getBankAcct());
//        counterPurchaseReqDto.setRiskFlag(counterPurchaseReqDto.getRiskFlag());

        counterPurchaseReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterPurchaseReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        counterPurchaseReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterPurchaseReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterPurchaseReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterPurchaseReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterPurchaseReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

        counterPurchaseReqDto.setCustName(custInfoDto.getCustName());
        counterPurchaseReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterPurchaseReqDto.setDisCode(custInfoDto.getDisCode());
        counterPurchaseReqDto.setIdNo(custInfoDto.getIdNo());
        counterPurchaseReqDto.setIdType(custInfoDto.getIdType());
        counterPurchaseReqDto.setInvstType(custInfoDto.getInvstType());

        counterPurchaseReqDto.setProductName(productInfo.getProductAbbrName());
        counterPurchaseReqDto.setProductCode(productInfo.getProductId());

        counterPurchaseReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        // 定期协议
        counterPurchaseReqDto.setProtocolType(ProtocolTypeEnum.REGULAR.getCode());
        counterPurchaseReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        // 普通公募产品
        counterPurchaseReqDto.setProductClass(ProductClassEnum.FIX.getCode());
        // 自划款
        counterPurchaseReqDto.setPaymentType(PaymentTypeEnum.SELF_DRAWING.getCode());
        counterPurchaseReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        counterPurchaseReqDto.setTaTradeDt(counterPurchaseReqDto.getAppDt());
        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterPurchaseReqDto);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        CounterPurchaseRespDto responseDto = tmsRegularCounterService.counterPurchase(counterPurchaseReqDto, disInfoDto);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/regular/validatorretailrisklevel.htm")
    public ModelAndView validatorRetailRiskLevel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String custInfoForm = request.getParameter("custInfoForm");
        String productInfoForm = request.getParameter("productInfoForm");

        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        QueryRegularProductDto productInfo = JSON.parseObject(productInfoForm, QueryRegularProductDto.class);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
//        DisInfoDto disInfoDto = new DisInfoDto();
//        disInfoDto.setDisCode(custInfoDto.getDisCode());

        try {
            CustRiskValidator.validatorRetailRiskLevel(custInfoDto.getCustNo(), custInfoDto.getInvstType(), custInfoDto.getInvestorType(), null,
                    productInfo.getRiskLevel(),custInfoDto.getDisCode());
        } catch (ValidateException e) {
            logger.error("", e);
            throw new TmsCounterException(e.getErrorCode(), e.getErrorDesc(),e);
        }

        ValidatorRetailRiskLevelDto responseDto = new ValidatorRetailRiskLevelDto();
        responseDto.setReturnCode("Z0000000");
        responseDto.setDescription("验证成功");
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

}
