<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - limitations 
	under the License. - See the License for the specific language governing 
	permissions and -->

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	
       <!-- 柜台认申购 -->
       <dubbo:reference registry="highorder"  id="subsOrPurCounterFacade" interface="com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterFacade" check="false"/>
       <!-- 柜台赎回 -->
       <dubbo:reference registry="highorder"  id="redeemCounterFacade" interface="com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterFacade" check="false"/>
       <!-- 柜台修改分红方式 -->
       <dubbo:reference registry="highorder"  id="modifyDivCounterFacade" interface="com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcounter.ModifyDivCounterFacade" check="false"/>
       <!-- 柜台强制撤单 -->
       <dubbo:reference registry="highorder"  id="forcedCancelOrderFacade" interface="com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelorder.ForcedCancelOrderFacade" check="false"/>
	   <!-- 撤单校验接口 -->
	   <dubbo:reference id="forcedCancelOrderValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelordervalidate.ForcedCancelOrderValidateFacade" registry="highorder" check="false" />
	    <!-- 赎回校验接口 -->
	   <dubbo:reference id="redeemCounterValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.redeem.redeemcountervalidate.RedeemCounterValidateFacade" registry="highorder"  check="false" />
        <!-- 认申购校验接口 -->
	   <dubbo:reference id="subsOrPurValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcountervalidate.SubsOrPurCounterValidateFacade" registry="highorder" check="false" />
        <!-- 认申购校验接口 -->
	   <dubbo:reference id="subsOrPurPreValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounterprevalidate.SubsOrPurCounterPreValidateFacade" registry="highorder" check="false" />
		<!-- 修改分红方式校验接口 -->
	  	<dubbo:reference id="modifyDivCounterValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcountervalidate.ModifyDivCounterValidateFacade" registry="highorder" check="false"/>
	  
        <!-- 柜台份额合并提交校验-->
        <dubbo:reference registry="highorder"  id="highShareMergeValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergevalidate.HighShareMergeValidateFacade" check="false"/>
       
        <!-- 柜台份额迁移提交校验-->
        <dubbo:reference registry="highorder"  id="highShareTransferValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfervalidate.HighShareTransferValidateFacade" check="false"/>
       
        <!-- 柜台份额合并审核落单-->
        <dubbo:reference registry="highorder"  id="highShareMergeCounterFacade" interface="com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergecounter.HighShareMergeCounterFacade" check="false"/>
        
        <!-- 柜台份额迁移审核落单-->
        <dubbo:reference registry="highorder"  id="highShareTransferCounterFacade" interface="com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfercounter.HighShareTransferCounterFacade" check="false"/>

         <!-- 修改客户复购协议-->
         <dubbo:reference registry="highorder"  id="modifyRepurchaseProtocolFacade" interface=" com.howbuy.tms.high.orders.facade.trade.modifyrepurchaseprotocol.ModifyRepurchaseProtocolFacade" check="false"/>

         <!-- 柜台非交易过户-->
         <dubbo:reference registry="highorder"  id="noTradeOverAccountCounterValidateFacade" interface="com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcountervalidate.NoTradeOverAccountCounterValidateFacade" check="false"/>
         <dubbo:reference registry="highorder"  id="noTradeOverAccountCounterFacade" interface="com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcounter.NoTradeOverAccountCounterFacade" check="false"/>
         <!-- 修改客户回款-->
         <dubbo:reference registry="highorder"  id="modifyRefundFacade" interface=" com.howbuy.tms.high.orders.facade.trade.modifyrefund.ModifyRefundFacade" check="false"/>
</beans>