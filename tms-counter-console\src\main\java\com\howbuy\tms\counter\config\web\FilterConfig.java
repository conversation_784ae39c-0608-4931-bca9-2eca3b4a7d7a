package com.howbuy.tms.counter.config.web;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.web.http.SessionRepositoryFilter;
import org.springframework.web.filter.CharacterEncodingFilter;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xin.jiang.cn
 * @date: 2023年09月13日 17:33:16
 * @description:
 */
@Configuration
public class FilterConfig {
    @Bean
    public FilterRegistrationBean<CharacterEncodingFilter> characterEncodingFilter() {
        FilterRegistrationBean<CharacterEncodingFilter> registrationBean = new FilterRegistrationBean<>();
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        filter.setEncoding("UTF-8");
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<SessionRepositoryFilter> sessionRepositoryFilter() {
        FilterRegistrationBean<SessionRepositoryFilter> registrationBean = new FilterRegistrationBean<>();
        SessionRepositoryFilter filter = new SessionRepositoryFilter<>();

        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("howbuySessionPrefix", "ehowbuy:session");
        initParameters.put("howbuyCookiePrefix", "TEN");

        registrationBean.setInitParameters(initParameters);
        registrationBean.setFilter(filter);
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(-1);

        return registrationBean;
    }
}
