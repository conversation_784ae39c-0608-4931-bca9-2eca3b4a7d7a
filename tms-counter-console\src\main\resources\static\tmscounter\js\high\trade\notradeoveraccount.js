/**
 * 非交易过户
 */
$(function () {
    NoTradeOverAccount.init();
});
var NoTradeOverAccount = {
    /**
     * 弹窗查询转入用户信息后的回调
     */
    queryInCustInfoCallback: function (data) {
        NoTradeOverAccount.inCustInfo = data;
        $("#inCustNo").val(data.txAcctNo);

        // 根据客户号查询身份证号的脱敏信息
        NoTradeOverAccount.queryInCustInfo(data.txAcctNo);
        NoTradeOverAccount.inCustInfo.inIdNo = data.idNo;

        // $("#inIdNo").val(data.idNo);
        $("#inIdTypeName").val(NoTradeOverAccount.getIdTypeName(data.idType, data.invstType));//客户证件类型
        $("#inIdType").val(data.idType);//客户证件类型
        $("#inCustName").val(data.custName);

        // 查询银行卡
        NoTradeOverAccount.queryCustBankInfo(data.txAcctNo, $("#selectDisCode").val());
    },
    init: function () {
        //初始化数据
        NoTradeOverAccount.initData();
        // 初始绑定
        NoTradeOverAccount.initBind();
    },

    initAppTm: function () {
        var now = new Date();
        var h = now.getHours() > 9 ? now.getHours().toString() : '0' + now.getHours();
        var m = now.getMinutes() > 9 ? now.getMinutes().toString() : '0' + now.getMinutes();
        var s = now.getSeconds() > 9 ? now.getSeconds().toString() : '0' + now.getSeconds();
        var tm = h + "" + m + "" + s;
        var maxTm = "145900";
        if (tm > maxTm) {
            tm = maxTm;
        }
        $("#appTm").val(tm);
    },

    initBind: function () {
        $("#confimBtn").on('click', function () {
            NoTradeOverAccount.transferConfim();
        });

        $("#queryCustHoldBtn").on('click', function () {
            NoTradeOverAccount.queryCustInfoAndBalDtl();
        });

        $("#custNo").on('dblclick', function () {
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        $("#inCustNo").on('dblclick', function () {
            QueryCustInfoSubPage.selectCustNo(null, 'NoTradeOverAccount.queryInCustInfoCallback');
        });

        // 过户份额格式化
        $("#appVol").on("blur", function () {
            // 还原格式化金额
            var appVol = CommonUtil.unFormatAmount($(this).val());
            // 格式化
            $("#appVol").val(CommonUtil.formatAmount(appVol));
            // 大写
            var upperAppVol = CommonUtil.digit_vol_uppercase(appVol);
            $("#appVolUpper").html(upperAppVol);
        });
        // 过户份额对应的认缴金额格式化
        $("#subsAmt").on("blur", function () {
            // 还原格式化金额
            var subsAmt = CommonUtil.unFormatAmount($(this).val());
            // 格式化
            $("#subsAmt").val(subsAmt);
            // 大写
            if(subsAmt){
                var subsAmtUpper = CommonUtil.digit_uppercase(subsAmt);
                $("#subsAmtUpper").html(subsAmtUpper);
            }


        });
        // 转让价格大写
        $("#transferPrice").on("blur", function () {
            // 还原格式化金额
            var transferPrice = CommonUtil.unFormatAmount($(this).val());
            // 格式化
            $("#transferPrice").val(CommonUtil.formatAmount(transferPrice));
            // 大写
            var transferPriceUpper = CommonUtil.digit_uppercase(transferPrice);
            $("#transferPriceUpper").html(transferPriceUpper);
        });

        // 过户的总认缴金额格式化
        $("#totalSubsAmt").on("blur", function () {
            var totalSubsAmt = CommonUtil.unFormatAmount($(this).val());
            // 格式化
            $("#totalSubsAmt").val(totalSubsAmt);
            // 大写
            if(totalSubsAmt){
                var totalSubsAmtUpper = CommonUtil.digit_uppercase(totalSubsAmt);
                $("#totalSubsAmtUpper").html(totalSubsAmtUpper);
            }

        });


    },

    /**
     * 初始化数据
     */
    initData: function () {
        NoTradeOverAccount.initAppTm();
        NoTradeOverAccount.dtlList = [];//客户持仓列表
        NoTradeOverAccount.transferBal = {};//客户过户持仓信息
        NoTradeOverAccount.outCustInfo = {};//转出客户信息
        NoTradeOverAccount.inCustInfo = {};//转入客户信息
        NoTradeOverAccount.fundInfo = {};//选中的产品信息（含打标信息）
    },

    /**
     * 获取证件类型
     * @param idType
     * @param invstType
     * @returns {*}
     */
    getIdTypeName: function (idType, invstType) {
        if ('0' === invstType) {
            // 机构证件类型
            return CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, idType, '');
        } else if ('1' === invstType) {
            // 个人证件类型
            return CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, idType, '');
        } else {
            // 产品证件类型
            return CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, idType, '');
        }
    },

    /**
     * 查询客户持仓明细
     */
    queryBalDtl: function (custNo, disCode) {
        var uri = TmsCounterConfig.HIGH_QUERY_CUST_BAL_DTL || "";

        if (CommonUtil.isEmpty(custNo)) {
            CommonUtil.layer_tip("请先选择用户");
            return false;
        }

        var reqparamters = {"custNo": custNo, "disCode": disCode};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, NoTradeOverAccount.queryCustDtlCallBack);
    },

    /**
     * 处理客户份额明细信息
     */
    queryCustDtlCallBack: function (data) {
        var bodyData = data.body || {};
        var dtlResp = bodyData || {};

        var dtlList = dtlResp.dtlList || [];
        NoTradeOverAccount.dtlList = dtlList;

        if (NoTradeOverAccount.dtlList.length <= 0) {
            CommonUtil.layer_tip("没有查询到持仓信息");
            return false;
        }

        var appendHtml = "";
        $(NoTradeOverAccount.dtlList).each(function (index, element) {
            var trList = [];
            trList.push('<input class="selectcustbal" name="checkBalDtl" type="radio" index="' + index + '">');
            trList.push(CommonUtil.formatData(element.productCode, '--'));// 产品代码
            trList.push(CommonUtil.formatData(element.productName, '--'));// 产品名称
            trList.push(CommonUtil.formatData(element.bankName, '--'));// 银行名称
            trList.push(CommonUtil.formatData(element.bankAcctNo, '--'));// 银行账号
            trList.push(CommonUtil.formatData(element.balanceVol, '--'));// 总份额
            trList.push(CommonUtil.formatAmount(element.availVol, '--'));// 可用份额
            trList.push(CommonUtil.formatAmount(element.unconfirmedVol, '--'));// 冻结份额
            trList.push(CommonUtil.formatAmount(element.subscribeAmt, '--'));// 认缴金额
            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            appendHtml += trHtml;
        });

        $("#dtlList").html(appendHtml);

        //绑定客户份额选择事件
        $(".selectcustbal").off();
        $(".selectcustbal").click(function () {

            $(this).attr('checked', 'checked').siblings().removeAttr('checked');
            var selectIndex = $(this).attr("index");
            // 选择用户份额
            NoTradeOverAccount.selectCustVol(selectIndex);

        });
    },
    /**
     * 选择用户份额
     */
    selectCustVol: function (selectIndex) {
        // 初始化订单信息
        CommonUtil.cleanForm('transferConfirmForm', ['appDt', 'appTm']);
        CommonUtil.initReadeText('readText');

        NoTradeOverAccount.transferBal = NoTradeOverAccount.dtlList[selectIndex] || {};

        NoTradeOverAccount.buildTransferForm(NoTradeOverAccount.transferBal);
    },

    /**
     * 构建份额明细
     */
    buildTransferForm: function (transferVol) {
        $("#outCustNo").val(NoTradeOverAccount.outCustInfo.custNo);
        $("#outIdNo").val(NoTradeOverAccount.outCustInfo.idNo);
        $("#outIdTypeName").val(NoTradeOverAccount.getIdTypeName(NoTradeOverAccount.outCustInfo.idType, NoTradeOverAccount.outCustInfo.invstType));//客户证件类型
        $("#outIdType").val(NoTradeOverAccount.outCustInfo.idType);//客户证件类型
        $("#outCustName").val(NoTradeOverAccount.outCustInfo.custName);
        $("#outBankAcctNo").val(transferVol.bankAcctNo);//银行卡号
        $("#outCpAcctNo").val(transferVol.cpAcctNo);//资金账号
        $("#fundCode").val(transferVol.productCode);//产品代码
        $("#totalSubsAmt").val(transferVol.subscribeAmt)

        // 查询产品（打标）信息
        QueryHighProduct.queryFundInfo(NoTradeOverAccount.transferBal.productCode);
        NoTradeOverAccount.fundInfo = QueryHighProduct.fundInfo;
        // 处理股权分次call产品
        if ("1" === NoTradeOverAccount.fundInfo.peDivideCallFlag) {
            $("#subsAmt").attr("isnull", "false");
        } else {
            $("#subsAmt").removeAttr("isnull");
        }
        // 非股权产品,两字段都默认为空,不允许修改
        if ("C" !== NoTradeOverAccount.fundInfo.fundSubType) {
            $('#subsAmt').attr("readonly", "readonly");
            $('#totalSubsAmt').attr("readonly", "readonly");
        }
        if(transferVol.subscribeAmt){
            var totalSubsAmtUpper = CommonUtil.digit_uppercase(transferVol.subscribeAmt);
            $("#totalSubsAmtUpper").html(totalSubsAmtUpper);
        }

    },

    /**
     * 查询转入客户脱敏信息
     */
    queryInCustInfo: function (txAcctNo) {
        var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};
        console.log(">>>>>>>>>>>>>>>>" + txAcctNo)

        if (CommonUtil.isEmpty(txAcctNo)) {
            CommonUtil.layer_tip("客户号必须输入");
            return false;
        }

        var reqparamters = {};

        reqparamters.custNo = txAcctNo;

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
        CommonUtil.ajaxAndCallBack(paramters, NoTradeOverAccount.queryInCustInfocallBack);
    },

    /**
     * 客户信息查询结果处理
     * @param data
     * @returns {Boolean}
     */
    queryInCustInfocallBack: function (data) {
        var bodyData = data.body || {};
        var custInfoDtoList = bodyData.custInfoList || [];
        if (custInfoDtoList.length === 0) {
            CommonUtil.layer_tip("没有查询到此用户");
            return false;
        }

        console.log(">>>>>>>>>>>>>>>>" + custInfoDtoList[0].idNo)
        $("#inIdNo").val(custInfoDtoList[0].idNo);
    },

    /**
     * 查询客户信息
     */
    queryCustInfoAndBalDtl: function () {
        $("#dtlList").empty();
        var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};
        var custNo = $("#custNo").val();
        var hboneNo = $("#hboneNo").val();
        console.log(">>>>>>>>>>>>>>>>" + hboneNo)
        var idNo = $("#idNo").val();
        var disCode = $("#selectDisCode").val();

        if (CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo) && CommonUtil.isEmpty(hboneNo)) {
            CommonUtil.layer_tip("客户号、证件号、一账通号必须输入一项");
            return false;
        }

        var reqparamters = {};

        if (!CommonUtil.isEmpty(idNo)) {
            reqparamters.idNo = idNo;
        }
        if (!CommonUtil.isEmpty(custNo)) {
            reqparamters.custNo = custNo;
        }
        if (!CommonUtil.isEmpty(hboneNo)) {
            reqparamters.hboneNo = hboneNo;
        }
        if (!CommonUtil.isEmpty(disCode)) {
            reqparamters.disCode = disCode;
        }

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
        CommonUtil.ajaxAndCallBack(paramters, NoTradeOverAccount.queryCustInfoAndBalDtlcallBack);
    },

    /**
     * 客户信息查询结果处理
     * @param data
     * @returns {Boolean}
     */
    queryCustInfoAndBalDtlcallBack: function (data) {
        var bodyData = data.body || {};
        var custInfoDtoList = bodyData.custInfoList || [];

        if (custInfoDtoList.length === 0) {
            CommonUtil.layer_tip("没有查询到此用户");
            return false;
        }

        NoTradeOverAccount.outCustInfo = custInfoDtoList[0];
        var disCode = $("#selectDisCode").val();

        NoTradeOverAccount.queryBalDtl(NoTradeOverAccount.outCustInfo.custNo, disCode);
    },

    /**
     * 查询客户银行卡信息
     */
    queryCustBankInfo: function (custNo, disCode) {
        var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
        var reqparamters = {"custNo": custNo, "disCode": disCode};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                NoTradeOverAccount.custBanks = body.custBanks || [];
                var selectBankHtml = '';
                $(NoTradeOverAccount.custBanks).each(function (index, element) {
                    selectBankHtml += '<option bankacct= "' + element.bankAcct + '" value="' + element.cpAcctNo + '" bankCode="' + element.bankCode + '">' + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                });
                $("#selectBank").empty();
                $("#selectBank").html(selectBankHtml);

                // 校验是否绑卡
                if (NoTradeOverAccount.custBanks.length <= 0) {
                    CommonUtil.layer_tip("客户银行卡没绑卡");
                    return false;
                }
            }
        });
    },

    /**
     * 过户确认
     */
    transferConfim: function () {
        var btn = "confimBtn";
        CommonUtil.disabledBtn(btn);

        var confirmForm = $("#transferConfirmForm").serializeObject();
        // 表单参数校验
        var validRst = Valid.valiadateFrom($("#transferConfirmForm"));
        if (!validRst.status) {
            CommonUtil.layer_tip(validRst.msg);
            CommonUtil.enabledBtn(btn);
            return false;
        }

        // 去除格式
        confirmForm.appVol = CommonUtil.unFormatAmount(confirmForm.appVol);
        confirmForm.subsAmt = CommonUtil.unFormatAmount(confirmForm.subsAmt);
        // 转让价格
        confirmForm.transferPrice = CommonUtil.unFormatAmount(confirmForm.transferPrice);
        if ("--" === confirmForm.transferPrice) {
            confirmForm.transferPrice = null;
        }

        // 过户份额校验
        var appVol = confirmForm.appVol;
        var balanceVol = NoTradeOverAccount.transferBal.balanceVol;

        // 非分次call不允许为0
        if ("1" !== NoTradeOverAccount.fundInfo.peDivideCallFlag) {
            if (appVol <= 0) {
                CommonUtil.layer_tip("过户份额[" + appVol + "]小于等于0");
                CommonUtil.enabledBtn(btn);
                return false;
            }
        } else {
            // 分次call可以为0,但是不可以为空
            if (appVol === null) {
                CommonUtil.layer_tip("过户份额不能为空");
                CommonUtil.enabledBtn(btn);
                return false;
            }
            if (appVol < 0) {
                CommonUtil.layer_tip("过户份额不能小于0");
                CommonUtil.enabledBtn(btn);
                return false;
            }
        }


        if (appVol > balanceVol) {
            CommonUtil.layer_tip("过户份额[" + appVol + "]大于总份额[" + balanceVol + "]");
            CommonUtil.enabledBtn(btn);
            return false;
        }

        var subsAmt = confirmForm.subsAmt;
        var totalSubsAmt = confirmForm.totalSubsAmt;
        // 股权产品需要校验
        if ("C" === NoTradeOverAccount.fundInfo.fundSubType) {
            // 过户份额对应的认缴金额校验
            if ("1" !== NoTradeOverAccount.fundInfo.peDivideCallFlag) {
                // 非分次call股权产品下单时，不允许为0。
                if (subsAmt <= 0) {
                    CommonUtil.layer_tip("过户份额对应的认缴金额[" + subsAmt + "]小于等于0");
                    CommonUtil.enabledBtn(btn);
                    return false;
                }
            } else {
                //分次call产品下单时，允许为0
                if (subsAmt < 0) {
                    CommonUtil.layer_tip("过户份额对应的认缴金额[" + subsAmt + "]小于0");
                    CommonUtil.enabledBtn(btn);
                    return false;
                }
            }
            if("HZ000N001"===NoTradeOverAccount.outCustInfo.disCode){
                if (totalSubsAmt == null) {
                    CommonUtil.layer_tip("过户的总认缴金额不能为空");
                    CommonUtil.enabledBtn(btn);
                    return false;
                }
            }
        }

        if (confirmForm.cpAcctNo === confirmForm.inCpAcctNo) {
            CommonUtil.layer_tip("不能过户给自己");
            CommonUtil.enabledBtn(btn);
            return false;
        }

        confirmForm.disCode = NoTradeOverAccount.outCustInfo.disCode;
        confirmForm.invstType = NoTradeOverAccount.outCustInfo.invstType;
        confirmForm.inDisCode = NoTradeOverAccount.inCustInfo.disCode;
        confirmForm.inInvstType = NoTradeOverAccount.inCustInfo.invstType;
        confirmForm.inBankAcct = $("#selectBank option:selected").attr("bankacct");
        confirmForm.inIdNo = NoTradeOverAccount.inCustInfo.inIdNo;
        // 提交
        var uri = TmsCounterConfig.NO_TRADE_OVER_ACCOUNT_URL || {};
        var reqparamters = {
            "confirmForm": JSON.stringify(confirmForm),
            "balInfo": JSON.stringify(NoTradeOverAccount.transferBal)
        };
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, NoTradeOverAccount.transferConfimCallback);
        CommonUtil.enabledBtn(btn);
    },

    transferConfimCallback: function (data) {
        var respCode = data.code || '';
        var respDesc = data.desc || '';

        if (CommonUtil.isSucc(respCode)) {
            layer.confirm('非交易过户提交成功', {
                btn: ['确定'] //按钮
            }, function () {
                layer.closeAll();
                // 刷新页面
                CommonUtil.reloadUrl();
            });
        } else {
            CommonUtil.layer_tip("提交失败," + respDesc + "(" + respCode + ")");
        }
        CommonUtil.enabledBtn("confimBtn");
    }

};

