package com.howbuy.tms.counter.aspect;

import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;

import java.util.Date;
import org.springframework.stereotype.Service;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;

/**
 * @className BusinessAspect
 * @description 处理公共参数处理切面
 * <AUTHOR>
 * @date 2015-3-23 下午5:24:31
 * 
 */
@Service("businessAspect")
public class BusinessAspect {

    /**
     * 设置dubbo调用的公共参数
     * 
     * @param disCode
     * @param outletCode
     */
    public static void setTradeCommomParams(String disCode, String outletCode) {
        if(DefaultParamsConstant.DEFULT_DIS_CODE.equals(disCode)) {
            disCode = DisCodeEnum.HM.getCode();
        }
        // 日期/时间
         Date currDate = new Date();
         String appDt = DateUtils.formatToString(currDate, DateUtils.YYYYMMDD);
         String appTm = DateUtils.formatToString(currDate, DateUtils.HHMMSS);
         TradeParamLocalUtils.setAppDt(appDt);
         TradeParamLocalUtils.setAppTm(appTm);
         if(StringUtil.isEmpty(disCode)){
             TradeParamLocalUtils.setDisCode("HB000A001");
         }else{
             TradeParamLocalUtils.setDisCode(disCode); 
         }
         
         if(StringUtil.isEmpty(outletCode)){
             TradeParamLocalUtils.setOutletCode("H20131104");
         }else{
             TradeParamLocalUtils.setOutletCode(outletCode); 
         }
        
         TradeParamLocalUtils.setTxChannel(TxChannelEnum.COUNTER.getCode());
    }


}