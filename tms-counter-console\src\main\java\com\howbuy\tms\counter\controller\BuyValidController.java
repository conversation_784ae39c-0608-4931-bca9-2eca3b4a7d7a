/**
 * Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.common.enums.ExamTypeEnum;
import com.howbuy.crm.prosale.request.QueryDetailPreInfoRequest;
import com.howbuy.crm.prosale.response.QueryDetailPreInfoResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.model.HighProductControlModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.TxChannelEnum;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryaqsetmanagementcertificatestatus.QueryAssetManagementCertificateStatusOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryaqsetmanagementcertificatestatus.QueryAssetManagementCertificateStatusResult;
import com.howbuy.tms.common.outerservice.acccenter.queryassetcertificatestatus.QueryCustAssetCertificateStatusOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.cc.center.queryassetcertificate.QueryCurrentAssetCertificateStatusResult;
import com.howbuy.tms.common.outerservice.crm.td.queryDoubleTrade.QueryDoubleTradeService;
import com.howbuy.tms.common.outerservice.crm.td.queryDoubleTrade.bean.CheckNeedDoubleTradeRequest;
import com.howbuy.tms.common.outerservice.crm.td.queryDoubleTrade.bean.DoubleTradeResultDto;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import com.howbuy.tms.common.validator.account.InvestorCommitmentValidator;
import com.howbuy.tms.common.validator.highproductinfo.ProductInfoValidator;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CounterOrderDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderReqDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderRespDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.fundservice.trade.HighProductAppointService;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlFacade;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlRequest;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.QueryHighFundDealOrderDtlResponse;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.bean.HighFundDealOrderDtlBean;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.bean.QueryHighFundDealOrderDtlCondition;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse.BuyFundStatusBean;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaRequest;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse.QuotaBean;
import com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusResponse;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounterprevalidate.SubsOrPurCounterPreValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounterprevalidate.SubsOrPurCounterPreValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounterprevalidate.SubsOrPurCounterPreValidateResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(购买校验)
 * @reason:
 * @date 2018年2月6日 下午6:41:54
 * @since JDK 1.6
 */
@Controller
public class BuyValidController {

    private final static Logger logger = LogManager.getLogger(BuyValidController.class);

    @Autowired
    private HighProductService highProductService;

    @Autowired
    private QuerySuppleStatusFacade querySuppleStatusFacade;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private QueryBuyFundStatusFacade queryBuyFundStatusFacade;

    @Autowired
    private QueryProductQuotaFacade queryProductQuotaFacade;

    @Autowired
    private QueryHighFundDealOrderDtlFacade queryHighFundDealOrderDtlFacade;
    @Autowired
    private QueryAssetManagementCertificateStatusOuterService queryAssetManagementCertificateStatusOuterService;

    @Autowired
    private QueryCustAssetCertificateStatusOuterService queryCustAssetCertificateStatusOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryAccKycInfoOuterService queryAccKycInfoOuterService;

    @Autowired
    private SubsOrPurCounterPreValidateFacade subsOrPurCounterPreValidateFacade;

    @Autowired
    private HighProductAppointService highProductAppointService;
    @Autowired
    private QueryDoubleTradeService queryDoubleTradeService;

    private static final String DOUBLE_DEAL_UN_GENERATE = "9";

    @Autowired
    @Qualifier("tmscounter.preBookService")
    private PreBookService preBookService;


    @RequestMapping("tmscounter/high/buyprevalid.htm")
    public void buyPreValid(HttpServletRequest request, HttpServletResponse response) throws Exception {

        //请求参数
        String txAcctNo = request.getParameter("txAcctNo");
        String productCode = request.getParameter("productCode");
        String disCode = request.getParameter("disCode");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        String cpAcctNo = request.getParameter("cpAcctNo");
        String appAmtStr = request.getParameter("appAmt");
        // 0-普通 1-专业
        String investorType = request.getParameter("investorType");
        // 0-机构1-个人
        String invstType = request.getParameter("invstType");
        // 投顾预约信息
        String appointId = request.getParameter("appointId");
        // 风险确认标志
        String riskFlag = request.getParameter("riskFlag");
        QueryDetailPreInfoResponse queryDetailPreInfoResponse = null;
        if (StringUtils.isNotEmpty(appointId)) {
            QueryDetailPreInfoRequest queryDetailPreInfoRequest = new QueryDetailPreInfoRequest();
            queryDetailPreInfoRequest.setPreId(appointId);
            queryDetailPreInfoResponse = preBookService.queryDetailPreInfo(queryDetailPreInfoRequest);
        }
        String busiType = BusiTypeEnum.BUY.getCode();
        BigDecimal appAmt = new BigDecimal(appAmtStr);
        HighProductBaseInfoModel highProductBaseModel = highProductService.getHighProductBaseInfo(productCode);
        if (highProductBaseModel == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }

        // 获取上报日期
        String submitTaDt = highProductAppointService.getBuySubmitTaDt(highProductBaseModel, busiType, appDt, appTm, disCode);

        // 双录校验
        String dubboStatusMatchFlag = validDubboStatus(queryDetailPreInfoResponse, appAmt, submitTaDt);

        // 资产证明状态校验
        String matchCertificateFlag = validCurrentAssetCertificateStatus(txAcctNo, highProductBaseModel.getManagerAttribute());

        // 购买限额校验
        String matchBuyQuotoFlag = validBuyLimit(highProductBaseModel, txAcctNo, productCode, disCode, appDt, appTm);

        // 金额重复订单检验
        String matchReplyDealFlag = validReplyTrade(txAcctNo, cpAcctNo, productCode, appAmt, appDt, submitTaDt, disCode);

        // 合格投资者校验
        boolean kycResult = validKycInfo(disCode, txAcctNo, invstType, investorType, highProductBaseModel.getFundType());

        if (isValidateRisk(txAcctNo, productCode, disCode)) {
            // 风险信息校验
            validRiskInfo(invstType, investorType, highProductBaseModel.getFundCode(), disCode, txAcctNo, riskFlag);
        }

        // PE/VC产品最大年龄校验
        String peProductMaxAgeFlag = validatePEProductAgeLimit(txAcctNo, disCode, highProductBaseModel.getFundSubType());

        // 分销代码校验
        validateDisCode(productCode, disCode);

        //返回结果
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyMap = new HashMap<String, Object>(16);
        bodyMap.put("matchCertificateFlag", matchCertificateFlag);
        bodyMap.put("matchBuyQuotoFlag", matchBuyQuotoFlag);
        bodyMap.put("matchReplyDealFlag", matchReplyDealFlag);
        bodyMap.put("kycFlag", kycResult ? "1" : "0");
        bodyMap.put("dubboStatusMatchFlag", dubboStatusMatchFlag);
        bodyMap.put("peProductMaxAgeFlag", peProductMaxAgeFlag);
        tmsCounterResult.setBody(bodyMap);

        WebUtil.write(response, tmsCounterResult);
    }

    private boolean isValidateRisk(String txAcctNo, String productCode, String disCode) {
        SubsOrPurCounterPreValidateRequest validateRequest = new SubsOrPurCounterPreValidateRequest();
        validateRequest.setFundCode(productCode);
        validateRequest.setTxAcctNo(txAcctNo);
        validateRequest.setDisCode(disCode);

        SubsOrPurCounterPreValidateResponse preValidateResponse = subsOrPurCounterPreValidateFacade.execute(validateRequest);
        return preValidateResponse.isValidateRisk();
    }

    private void validRiskInfo(String invstType, String investorType, String fundCode, String disCode, String txAcctNo, String riskFlag) {
        if (QualificaitionTypeEnum.PRO.getCode().equals(investorType)) {
            riskFlag = "1";
        }
        // 产品用户不校验风险等级
        if (InvstTypeEnum.PRODUCT.getCode().equals(invstType)) {
            return;
        }

        // 获取高端产品信息
        HighProductInfoBean highProductInfoBean = queryHighProductOuterService.getHighProductInfo(fundCode);
        QueryAccKycInfoResult queryAccKycInfoResult = queryAccKycInfoOuterService.queryAccKycInfoByTxAcctNo(txAcctNo, disCode);
        if (InvstTypeEnum.INST.getCode().equals(invstType)) {
            // 如果风险测评类型是空,或则机构,可以校验通过,其他都不行
            if (!(StringUtils.isBlank(queryAccKycInfoResult.getRiskToleranceExamType()) || ExamTypeEnum.INSTITUTION.getValue().equals(queryAccKycInfoResult.getRiskToleranceExamType()))) {
                throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "机构高端风险测评未做");
            }
        } else {
            if (!ExamTypeEnum.HIGH_END.getValue().equals(queryAccKycInfoResult.getRiskToleranceExamType()) && !ExamTypeEnum.INSTITUTION.getValue().equals(queryAccKycInfoResult.getRiskToleranceExamType())) {
                throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "高端风险测评未做");
            }
        }
        String forceRiskMatch = getForceRiskMatch(highProductInfoBean);
        // 强制校验风险等级
        if (YesOrNoEnum.YES.getCode().equals(forceRiskMatch)) {
            riskFlag = null;
        }
        validateRiskLevelNew(riskFlag, highProductInfoBean.getFundRiskLevel(), queryAccKycInfoResult);
    }


    private static void validateRiskLevelNew(String riskFlag, String productRiskLevel, QueryAccKycInfoResult queryAccKycInfoResult) {
        // 查询客户风险评测信息
        if (queryAccKycInfoResult == null) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "客户被要求的风险评测未完成.");
        }

        // 校验用户风险评测是否过期
        validateRiskExpire(queryAccKycInfoResult.getRiskToleranceTerm());

        // 客户风险承受能力等级
        String custRiskLevel = queryAccKycInfoResult.getRiskToleranceLevel();

        if (MDataDic.VERY_LOW_CUST_RISK_LEVEL.equals(custRiskLevel)) {
            //极低的普通投资者只能购买低风险等级的产品
            if (!ProductRiskLevelEnum.LOW.getCode().equals(productRiskLevel)) {
                throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "极低的普通投资者只能购买低风险等级的产品");
            }
        } else {
            // 客户风险等级小于产品等级, 且未二次风险确认
            if (productRiskLevel.compareTo(custRiskLevel) > 0 && (com.howbuy.tms.common.utils.StringUtils.isEmpty(riskFlag) || !MDataDic.FUND_RISK_FLAG_SUCCESS.equals(riskFlag))) {
                throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "产品风险高于客户承受风险能力.");
            }
        }
    }

    private static void validateRiskExpire(String riskExpireDate) {
        if (riskExpireDate == null) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(),
                    "客户风险承受能力评测已过期.");
        }

        String nowDate = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);

        if (nowDate.compareTo(riskExpireDate) > 0) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(),
                    "客户风险承受能力评测已过期.");
        }
    }

    private String getForceRiskMatch(HighProductInfoBean highProductInfoBean) {
        if (com.howbuy.tms.common.utils.StringUtils.isEmpty(highProductInfoBean.getForceMatchRiskFlag())) {
            if (ProductTypeEnum.ZHUANHU.getCode().equals(highProductInfoBean.getFundType())) {
                return YesOrNoEnum.YES.getCode();
            } else {
                return YesOrNoEnum.NO.getCode();
            }
        } else {
            return highProductInfoBean.getForceMatchRiskFlag();
        }
    }

    private void validateDisCode(String productCode, String disCode) {
        HighProductControlModel highProductControlModel = highProductService.getHighProductControlInfo(productCode);
        if (highProductControlModel == null) {
            throw new TmsCounterException(TmsCounterResultEnum.DISCODE_ERROR.getCode(), "查不到分销信息,分销校验失败");
        }
        if (!DisCodeEnum.OTC_MIDDLE.getCode().equals(disCode)) {
            if (highProductControlModel.getDisCode() == null || !highProductControlModel.getDisCode().contains(disCode)) {
                if (!DisCodeEnum.HM.getCode().equals(disCode) || !"*********".equals(highProductControlModel.getDisCode())) {
                    throw new TmsCounterException(TmsCounterResultEnum.DISCODE_ERROR.getCode(), "分销校验失败");
                }
            }
        } else {
            // 机构分销，只能购买好买，不能购买好臻
            List<String> canBuyDisCodeList = new ArrayList<>();
            canBuyDisCodeList.add(DisCodeEnum.HM.getCode());
            canBuyDisCodeList.add(DisCodeEnum.HZ.getCode());
            canBuyDisCodeList.add(DisCodeEnum.OTC_MIDDLE.getCode());
            canBuyDisCodeList.add("*********");
            for (String ds : canBuyDisCodeList) {
                if (highProductControlModel.getDisCode().contains(ds)) {
                    return;
                }
                throw new TmsCounterException(TmsCounterResultEnum.DISCODE_ERROR.getCode(), "分销校验失败");
            }
        }
    }

    /**
     * @param queryDetailPreInfoResponse
     * @return String 0-不匹配 1-匹配
     * @Description 判断是否需要双录
     * <AUTHOR>
     * @Date 2019/1/24 15:03
     **/
    private String validDubboStatus(QueryDetailPreInfoResponse queryDetailPreInfoResponse, BigDecimal
            appAmt, String submitTaDt) {
        String doubleTradeStatus = buildDoubleTradeStatus(queryDetailPreInfoResponse, appAmt, submitTaDt);
        if (DualentryStatusEnum.UN_DONE.getCode().equals(doubleTradeStatus)) {
            return YesOrNoEnum.NO.getCode();
        } else {
            return YesOrNoEnum.YES.getCode();
        }
    }

    /**
     * 设置双录信息
     */
    private String buildDoubleTradeStatus(QueryDetailPreInfoResponse queryDetailPreInfoResponse, BigDecimal
            appAmt, String submitTaDt) {
        // 1.没有预约单,不需要双录
        if (queryDetailPreInfoResponse == null || StringUtils.isBlank(queryDetailPreInfoResponse.getPreId())) {
            logger.info("没有预约单,不需要双录");
            return DualentryStatusEnum.NOT_NEED.getCode();
        }
        // 2.查询是否需要双录
        CheckNeedDoubleTradeRequest checkNeedDoubleTradeRequest = new CheckNeedDoubleTradeRequest();
        checkNeedDoubleTradeRequest.setTradeType(queryDetailPreInfoResponse.getTradeType());
        checkNeedDoubleTradeRequest.setAppAmt(appAmt);
        checkNeedDoubleTradeRequest.setPreId(queryDetailPreInfoResponse.getPreId());
        checkNeedDoubleTradeRequest.setPreType(queryDetailPreInfoResponse.getPreType());
        checkNeedDoubleTradeRequest.setHbOneNo(queryDetailPreInfoResponse.getHboneNo());
        checkNeedDoubleTradeRequest.setTxChannel(TxChannelEnum.COUNTER.getKey());
        checkNeedDoubleTradeRequest.setFundCode(queryDetailPreInfoResponse.getFundCode());
        checkNeedDoubleTradeRequest.setSubmitTaDt(submitTaDt);
        String needDoubleTrade = queryDoubleTradeService.checkNeedDoubleTrade(checkNeedDoubleTradeRequest);
        if (StringUtils.isNotBlank(needDoubleTrade) && YesOrNoEnum.NO.getCode().equals(needDoubleTrade)) {
            logger.info("查询双录结果:不需要双录,request={},needDoubleTrade={}", JSON.toJSON(checkNeedDoubleTradeRequest), needDoubleTrade);
            return DualentryStatusEnum.NOT_NEED.getCode();
        }
        // 3.查询双录状态
        DoubleTradeResultDto doubleTradeResultDto = queryDoubleTradeService.queryDoubleResult(queryDetailPreInfoResponse.getPreId(), queryDetailPreInfoResponse.getHboneNo());
        if (doubleTradeResultDto != null && !org.springframework.util.StringUtils.isEmpty(doubleTradeResultDto.getHandleFlag())) {
            logger.info("双录查询结果,preId={},hbOneNo={},doubleTradeResultDto={}", queryDetailPreInfoResponse.getPreId(), queryDetailPreInfoResponse.getHboneNo(), JSON.toJSONString(doubleTradeResultDto));
            return getCrmDualentryStatus(doubleTradeResultDto.getHandleFlag());

        } else {
            logger.info("需要双录,查不到双录状态,默认未双录,preId={},hbOneNo={}", queryDetailPreInfoResponse.getPreId(), queryDetailPreInfoResponse.getHboneNo());
            return DualentryStatusEnum.UN_DONE.getCode();

        }
    }

    /**
     * CRM 双录标识转换
     */
    private String getCrmDualentryStatus(String crmStatus) {
        String status = DualentryStatusEnum.UN_DONE.getCode();
        if (DualentryStatusEnum.DONE.getCode().equals(crmStatus)) {
            status = DualentryStatusEnum.DONE.getCode();
        } else if (DualentryStatusEnum.NOT_NEED.getCode().equals(crmStatus)) {
            status = DualentryStatusEnum.NOT_NEED.getCode();
        } else {
            logger.info("双录状态不是已双录与不需要双录,默认未双录:{}", crmStatus);
        }
        return status;
    }


    private void validateDualentryParam(QueryDetailPreInfoResponse queryDetailPreInfoResponse) {
        if (queryDetailPreInfoResponse != null) {
            if (DOUBLE_DEAL_UN_GENERATE.equals(queryDetailPreInfoResponse.getDoubleNeedFlag())) {
                throw new TmsCounterException(TmsCounterResultEnum.CRM_DOUBLE_DEAL_UN_GENERATE_ERROR);
            }

            if (StringUtils.isEmpty(queryDetailPreInfoResponse.getDoubleNeedFlag())
                    || StringUtils.isEmpty(queryDetailPreInfoResponse.getDoubleHandleFlag())) {
                throw new TmsCounterException(TmsCounterResultEnum.CRM_PARAMS_ERROR);
            }

        }

    }

    private boolean validKycInfo(String disCode, String txAcctNo, String invstType, String
            qualificaitionType, String productType) {
        QueryCustInfoResult result = tmsCounterOutService.queryCustInfo(disCode, txAcctNo);
        if (result == null) {
            return false;
        }

        try {
            InvestorCommitmentValidator.validateInvestorCommitment(invstType, qualificaitionType, productType,
                    result.getSignFlag(), result.getFundFlag(), null);
        } catch (Exception e) {
            logger.error("BuyValidController|validKycInfo|fail error:{}", e.getMessage(), e);
            return false;
        }

        return true;
    }

    /**
     * getSubmitTaDt:(获取上报日)
     *
     * @param highProductBaseModel
     * @param busiType
     * @param appDt
     * @param appTm
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2018年4月17日 下午7:29:38
     */
    private String getSubmitTaDt(HighProductBaseInfoModel highProductBaseModel, String busiType, String
            appDt, String appTm, String disCode) {
        logger.info("getSubmitTaDt|highProductBaseModel:{}, appDt:{}, appTm:{}", JSON.toJSONString(highProductBaseModel), appDt, appTm);

        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade()) ||
                SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade())) {
            // 支持提前购买
            String appDtmStr = appDt + appTm;
            Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
            HighProductAppointmentInfoModel productAppointmentInfoModel = highProductService.getAppointmentInfoByAppointDate(highProductBaseModel.getFundCode(), busiType, highProductBaseModel.getShareClass(), disCode, appDtm);
            if (productAppointmentInfoModel != null) {
                if (appDt.compareTo(productAppointmentInfoModel.getOpenStartDt()) < 0) {
                    return productAppointmentInfoModel.getOpenStartDt();
                }
            }
        }

        return appDt;
    }

    /**
     * validCurrentAssetCertificateStatus:(资产证明状态校验)
     *
     * @param txAcctNo
     * @param managerAttribute
     * <AUTHOR>
     * @date 2018年2月6日 下午7:14:29
     */
    private String validCurrentAssetCertificateStatus(String txAcctNo, String managerAttribute) {
        String hbOneNo = tmsCounterOutService.queryHboneNoByTxAccountNo(txAcctNo);

        TradeParamLocalUtils.setDisCode(DisCodeEnum.HM.getCode());
        TradeParamLocalUtils.setOutletCode(TradeConstant.TRADE_ACC_OUTLET_CODE);
        TradeParamLocalUtils.setTxChannel(TxChannelEnum.COUNTER.getKey());
        //默认资产证明状态不匹配
        String matchCertificateStatus = "0";
        if (ManagerAttributeEnum.SIMU.getCode().equals(managerAttribute)) {
            QueryCurrentAssetCertificateStatusResult queryRst = queryCustAssetCertificateStatusOuterService.queryCurrentAssetCertificateStatus(hbOneNo);
            if (AssetcertificateStatusEnum.VALID.getCode().equals(queryRst.getStatus())) {
                // 匹配
                matchCertificateStatus = "1";
            } else {
                // 私募资产证明无效
                matchCertificateStatus = "2";
            }
        } else if (ManagerAttributeEnum.ASSERT_MANAGER.getCode().equals(managerAttribute)) {
            QueryAssetManagementCertificateStatusResult queryRst = queryAssetManagementCertificateStatusOuterService.queryCurrentAssetCertificateStatus(hbOneNo);
            if (AssetcertificateStatusEnum.VALID.getCode().equals(queryRst.getStatus())) {
                matchCertificateStatus = "1";
            } else {
                // 资管资产证明无效
                matchCertificateStatus = "3";
            }
        }

        TradeParamLocalUtils.removeTradeParam();
        return matchCertificateStatus;
    }

    /**
     * voidBuyLimit:(购买额度校验)
     *
     * @param txAcctNo
     * @param productCode
     * @return 购买额度是否满足 0-不符合 1-符合
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月6日 下午7:18:39
     */
    private String validBuyLimit(HighProductBaseInfoModel highProductBaseModel, String txAcctNo, String
            productCode, String disCode, String appDt, String appTm) throws Exception {
        logger.info("BuyValidController|validBuyLimit|txAcctNo:{}, productCode:{}", txAcctNo, productCode);

        //购买状态查询
        QueryBuyFundStatusRequest queryBuyFundStatusRequest = new QueryBuyFundStatusRequest();
        queryBuyFundStatusRequest.setTxAcctNo(txAcctNo);
        List<String> productCodeList = new ArrayList<String>();
        productCodeList.add(productCode);
        queryBuyFundStatusRequest.setProductCodeList(productCodeList);
        queryBuyFundStatusRequest.setDisCode(disCode);
        queryBuyFundStatusRequest.setAppDt(appDt);
        queryBuyFundStatusRequest.setAppTm(appTm);

        TmsFacadeUtil.doFillBaseRequest(queryBuyFundStatusRequest, null);
        QueryBuyFundStatusResponse queryBuyFundStatusResponse = queryBuyFundStatusFacade.execute(queryBuyFundStatusRequest);
        if (queryBuyFundStatusResponse != null && TradeConstant.TMS_TRADE_SUCC_CODE.equals(queryBuyFundStatusResponse.getReturnCode())) {
            if (!CollectionUtils.isEmpty(queryBuyFundStatusResponse.getBuyFundStatusList())) {
                BuyFundStatusBean buyFundStatusBean = queryBuyFundStatusResponse.getBuyFundStatusList().get(0);
                if (BuyStatusEnum.NOT_ALLOW.getCode().equals(buyFundStatusBean.getBuyStatus())) {
                    //不可购买
                    String desc = convertCanNotBuyDesc(buyFundStatusBean.getBuyStatusType());
                    throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_CAN_NOT_BUY.getCode(), desc);
                }
            }
        }

        //默认购买额度匹配
        String matchBuyQuotoStaus = "1";

        //查询追加状态
        QuerySuppleStatusRequest querySuppleStatusRequest = new QuerySuppleStatusRequest();
        querySuppleStatusRequest.setTxAcctNo(txAcctNo);
        querySuppleStatusRequest.setFundCode(productCode);
        querySuppleStatusRequest.setFundShareClass(highProductBaseModel.getShareClass());
        querySuppleStatusRequest.setDisCode(disCode);
        querySuppleStatusRequest.setAppDt(appDt);
        querySuppleStatusRequest.setAppTm(appTm);
        TmsFacadeUtil.doFillBaseRequest(querySuppleStatusRequest, null);
        QuerySuppleStatusResponse querySuppleStatusResponse = querySuppleStatusFacade.execute(querySuppleStatusRequest);
        if (querySuppleStatusResponse != null && TmsCounterResultEnum.TMSSUCC.getCode().equals(querySuppleStatusResponse.getReturnCode())) {
            if ("1".equals(querySuppleStatusResponse.getSuppleSubsStatus())) {

                QuotaBean quotaBean = getQuotaBean(txAcctNo, productCode, disCode, appDt, appTm);

                // 待审核的柜台购买订单总数
                long unCheckBuyDeals = getUnCheckCouterBuyDeals(txAcctNo, productCode, null, appDt, disCode);

                // 申请成功未付款的订单订单总数
                long unPayBuyDeals = getUnPayAppSuccBuyDeals(txAcctNo, productCode, disCode, appDt, appTm);

                if (quotaBean != null) {
                    logger.info("BuyValidController|voidBuyLimit|totalPlaces:{}, costTotalPlaces:{}, unCheckBuyDeals:{}, unPayBuyDeals:{}", highProductBaseModel.getIpoPeopleLimit(), quotaBean.getCostTotalPlaces(), unCheckBuyDeals, unPayBuyDeals);
                    long costPalaces = quotaBean.getCostTotalPlaces() == null ? 0 : quotaBean.getCostTotalPlaces() + unCheckBuyDeals + unPayBuyDeals;
                    if (costPalaces > highProductBaseModel.getIpoPeopleLimit() + (quotaBean.getLockExitCount() * -1)) {
                        matchBuyQuotoStaus = "0";
                    }
                }
            }
        }

        return matchBuyQuotoStaus;

    }

    /**
     * getCostPalace:(查询已购买总数)
     *
     * @param txAcctNo
     * @param productCode
     * @param disCode
     * <AUTHOR>
     * @date 2018年2月6日 下午7:58:15
     */
    private QuotaBean getQuotaBean(String txAcctNo, String productCode, String disCode, String appDt, String appTm) {
        QueryProductQuotaRequest queryProductQuotaRequest = new QueryProductQuotaRequest();
        queryProductQuotaRequest.setTxAcctNo(txAcctNo);
        queryProductQuotaRequest.setProductCodeArr(new String[]{productCode});
        queryProductQuotaRequest.setDisCode(disCode);
        queryProductQuotaRequest.setAppDt(appDt);
        queryProductQuotaRequest.setAppTm(appTm);
        TmsFacadeUtil.doFillBaseRequest(queryProductQuotaRequest, null);
        QueryProductQuotaResponse queryProductQuotaResponse = queryProductQuotaFacade.execute(queryProductQuotaRequest);
        if (queryProductQuotaResponse != null && TradeConstant.TMS_TRADE_SUCC_CODE.equals(queryProductQuotaResponse.getReturnCode())) {
            if (!CollectionUtils.isEmpty(queryProductQuotaResponse.getQuotaBeanList())) {
                QuotaBean quotaBean = queryProductQuotaResponse.getQuotaBeanList().get(0);
                return quotaBean;
            }
        }

        return null;
    }

    /**
     * getUnCheckCouterBuyDeals:(获取未审核的柜台购买订单数)
     *
     * @param txAcctNo
     * @param productCode
     * @param disCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月6日 下午8:54:47
     */
    private long getUnCheckCouterBuyDeals(String txAcctNo, String productCode, String cpAcctNo, String
            appDt, String disCode) throws Exception {
        List<CounterOrderDto> counterOrderList = getCounterOrderList(txAcctNo, null, productCode, appDt, disCode, CheckFlagEnum.NOT_CHECK.getCode());
        if (CollectionUtils.isEmpty(counterOrderList)) {
            return 0L;
        }
        Set<String> unCheckPalacesSet = new HashSet<String>(counterOrderList.size());
        for (CounterOrderDto counterOrderDto : counterOrderList) {
            unCheckPalacesSet.add(counterOrderDto.getTxAcctNo());
        }

        return unCheckPalacesSet.size();

    }

    /**
     * getCounterOrderList:(查询柜台订单)
     *
     * @param txAcctNo
     * @param cpAcctNo
     * @param productCode
     * @param appDt
     * @param disCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月18日 上午11:17:03
     */
    private List<CounterOrderDto> getCounterOrderList(String txAcctNo, String cpAcctNo, String productCode, String
            appDt, String disCode, String checkFlag) throws Exception {

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);

        CounterQueryOrderReqDto counterQueryOrderReqDto = new CounterQueryOrderReqDto();
        counterQueryOrderReqDto.setTxAcctNo(txAcctNo);
        counterQueryOrderReqDto.setFundCode(productCode);
        counterQueryOrderReqDto.setCpAcctNo(cpAcctNo);
        counterQueryOrderReqDto.setAppDt(appDt);
        counterQueryOrderReqDto.setCheckFlag(checkFlag);
        counterQueryOrderReqDto.setTxCode(TxCodes.HIGH_COUNTER_PURCHASE);
        CounterQueryOrderRespDto counterQueryOrder = tmsCounterService.counterQueryOrder(counterQueryOrderReqDto, disInfoDto);
        if (counterQueryOrder == null) {
            return null;
        }

        return counterQueryOrder.getCounterOrderList();
    }

    /**
     * getUnPayAppSuccBuyDeals:(申请成功，未付款的购买订单数)
     *
     * @param txAcctNo
     * @param productCode
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2018年2月6日 下午7:42:22
     */
    private long getUnPayAppSuccBuyDeals(String txAcctNo, String productCode, String disCode, String appDt, String
            appTm) {
        //申请成功未支付的订单数
        long totalUnPayAppSucc = 0L;
        // 当前页
        int currPage = 1;
        // 每页记录数
        int pageSize = 100;
        // 首页记录
        QueryHighFundDealOrderDtlResponse queryHighFundDealOrderDtlResponse = queryHighFundDealOrder(txAcctNo, productCode, null, currPage, pageSize);
        if (queryHighFundDealOrderDtlResponse != null && ReturnCodeEnum.SUCC_TMS.getCode().equals(queryHighFundDealOrderDtlResponse.getReturnCode())) {

            if (CollectionUtils.isEmpty(queryHighFundDealOrderDtlResponse.getFundDealOrderDtlBeanList())) {
                return 0L;
            }
            // 总页数
            long totalPages = queryHighFundDealOrderDtlResponse.getTotalPage();
            // 每页下单成功未付款的订单
            long unPayAppSuccs = getUnPayAppSuccBuyDeals(queryHighFundDealOrderDtlResponse.getFundDealOrderDtlBeanList());
            // 总的申请成功未付款订单数
            totalUnPayAppSucc = totalUnPayAppSucc + unPayAppSuccs;

            while (currPage < totalPages) {
                currPage = currPage + 1;
                unPayAppSuccs = getUnPayAppSuccBuyDeals(queryHighFundDealOrderDtlResponse.getFundDealOrderDtlBeanList());
                // 总的申请成功未付款订单数
                totalUnPayAppSucc = totalUnPayAppSucc + unPayAppSuccs;
            }

        }
        return totalUnPayAppSucc;
    }

    /**
     * getUnPayAppSuccBuyDeals:(每页申请成功，未付款订单数)
     *
     * @param highFundDealOrderDtlBeanList
     * @return
     * <AUTHOR>
     * @date 2018年4月17日 下午7:17:27
     */
    private long getUnPayAppSuccBuyDeals(List<HighFundDealOrderDtlBean> highFundDealOrderDtlBeanList) {

        if (CollectionUtils.isEmpty(highFundDealOrderDtlBeanList)) {
            return 0L;
        }
        Set<String> unPayAppSuccBuyDealsSet = new HashSet<String>();
        for (HighFundDealOrderDtlBean highFundDealOrderDtlBean : highFundDealOrderDtlBeanList) {
            if (PayStatusEnum.UN_PAY.getCode().equals(highFundDealOrderDtlBean.getPayStatus()) || PayStatusEnum.PAYING.getCode().equals(highFundDealOrderDtlBean.getPayStatus())) {
                unPayAppSuccBuyDealsSet.add(highFundDealOrderDtlBean.getTxAcctNo());
            }
        }

        return unPayAppSuccBuyDealsSet.size();
    }

    /**
     * queryHighFundDealOrder:(查询高端申请成功的订单)
     *
     * @param txAcctNo
     * @param productCode
     * @param pageNum
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2018年4月11日 下午3:33:34
     */
    private QueryHighFundDealOrderDtlResponse queryHighFundDealOrder(String txAcctNo, String productCode, String
            submitTaDt, int pageNum, int pageSize) {

        QueryHighFundDealOrderDtlRequest queryHighFundDealOrderDtlRequest = new QueryHighFundDealOrderDtlRequest();
        QueryHighFundDealOrderDtlCondition queryHighFundDealOrderDtlCondition = new QueryHighFundDealOrderDtlCondition();
        queryHighFundDealOrderDtlCondition.setTxAcctNo(txAcctNo);
        queryHighFundDealOrderDtlCondition.setFundCode(productCode);
        queryHighFundDealOrderDtlCondition.setzBusiCode(ZBusiCodeEnum.BUY.getCode());
        queryHighFundDealOrderDtlCondition.setSubmitTaDtEnd(submitTaDt);
        queryHighFundDealOrderDtlCondition.setSubmitTaDtStart(submitTaDt);
        queryHighFundDealOrderDtlCondition.setTxAppFlag(TxAppFlagEnum.APP_SUCCESS.getCode());
        queryHighFundDealOrderDtlRequest.setPageNo(pageNum);
        queryHighFundDealOrderDtlRequest.setPageSize(pageSize);
        queryHighFundDealOrderDtlRequest.setQueryCondition(queryHighFundDealOrderDtlCondition);
        TmsFacadeUtil.doFillBaseRequest(queryHighFundDealOrderDtlRequest, null);
        QueryHighFundDealOrderDtlResponse queryHighFundDealOrderDtlResponse = queryHighFundDealOrderDtlFacade.execute(queryHighFundDealOrderDtlRequest);

        return queryHighFundDealOrderDtlResponse;
    }

    private String validReplyTrade(String txAcctNo, String cpAcctNo, String productCode, BigDecimal appAmt, String
            appDt, String submitTaDt, String disCode) throws Exception {
        // 默认校验通过
        String validStatus = "1";
        // 总的重复金额申请订单
        int totalReplyAppNums = getTotalReplyAppSuccNum(txAcctNo, cpAcctNo, productCode, appAmt, submitTaDt, disCode);

        // 柜台申请金额重复订单数
        int totalCounterAppNums = getTotalCounterAppNums(txAcctNo, cpAcctNo, productCode, appAmt, appDt, disCode);
        logger.info("BuyValidController|validReplyTrade|totalReplyAppNums:{}, totalCounterAppNums:{}", totalReplyAppNums, totalCounterAppNums);
        if (totalReplyAppNums != 0 || totalCounterAppNums != 0) {
            return "0";
        }
        return validStatus;
    }

    /**
     * getTotalCounterAppNums:(柜台未审核，申请金额重复的订单)
     *
     * @param txAcctNo
     * @param cpAcctNo
     * @param productCode
     * @param appAmt
     * @param disCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月17日 下午7:18:16
     */
    private int getTotalCounterAppNums(String txAcctNo, String cpAcctNo, String productCode, BigDecimal
            appAmt, String appDt, String disCode) throws Exception {
        // 柜台申请金额重复订单数
        int totalCounterAppNums = 0;
        List<CounterOrderDto> counterOrderList = getCounterOrderList(txAcctNo, cpAcctNo, productCode, appDt, disCode, CheckFlagEnum.NOT_CHECK.getCode());
        if (CollectionUtils.isEmpty(counterOrderList)) {
            return totalCounterAppNums;
        }

        for (CounterOrderDto counterOrderDto : counterOrderList) {
            logger.info("BuyValidController|getTotalCounterAppNums|orderAppAmt:{}, appAmt:{}", counterOrderDto.getAppAmt(), appAmt);
            if (counterOrderDto.getAppAmt().compareTo(appAmt) == 0) {
                totalCounterAppNums = totalCounterAppNums + 1;
            }
        }
        return totalCounterAppNums;
    }

    private int getTotalReplyAppSuccNum(String txAcctNo, String cpAcctNo, String productCode, BigDecimal
            appAmt, String submitTaDt, String disCode) {
        logger.info("BuyValidController|getTotalReplyAppSuccNum|txAcctNo:{}, cpAcctNo:{}, productCode:{}, appAmt:{}, submitTaDt:{}, disCode:{}",
                txAcctNo, cpAcctNo, productCode, appAmt, submitTaDt, disCode);
        //申请成功金额重复订单数
        int totalReplyApp = 0;
        // 当前页
        int currPage = 1;
        // 每页记录数
        int pageSize = 100;
        // 总页数
        long totalPages = 0L;
        // 首页记录
        QueryHighFundDealOrderDtlResponse queryHighFundDealOrderDtlResponse = queryHighFundDealOrder(txAcctNo, productCode, submitTaDt, currPage, pageSize);

        if (queryHighFundDealOrderDtlResponse != null && ReturnCodeEnum.SUCC_TMS.getCode().equals(queryHighFundDealOrderDtlResponse.getReturnCode())) {

            if (CollectionUtils.isEmpty(queryHighFundDealOrderDtlResponse.getFundDealOrderDtlBeanList())) {
                logger.info("BuyValidController|getTotalReplyAppSuccNum|FundDealOrderDtlBeanList is empty");
                return 0;
            }
            // 总页数
            totalPages = queryHighFundDealOrderDtlResponse.getTotalPage();
            // 每页下单成功未付款的订单
            int replyApp = getAppSuccReply(appAmt, cpAcctNo, queryHighFundDealOrderDtlResponse.getFundDealOrderDtlBeanList());
            totalReplyApp = totalReplyApp + replyApp;

        }

        while (currPage < totalPages) {
            currPage = currPage + 1;
            int replyApp = getAppSuccReply(appAmt, cpAcctNo, queryHighFundDealOrderDtlResponse.getFundDealOrderDtlBeanList());
            // 总的申请成功未付款订单数
            totalReplyApp = totalReplyApp + replyApp;
        }

        return totalReplyApp;
    }

    /**
     * getAppSuccReply:(每页重复订单数)
     *
     * @param appAmt
     * @param highFundDealOrderDtlBeanList
     * @return
     * <AUTHOR>
     * @date 2018年4月17日 下午6:53:38
     */
    private int getAppSuccReply(BigDecimal appAmt, String
            cpAcctNo, List<HighFundDealOrderDtlBean> highFundDealOrderDtlBeanList) {
        logger.info("BuyValidController|getAppSuccReply|appAmt:{}, cpAcctNo:{}", appAmt, cpAcctNo);
        if (CollectionUtils.isEmpty(highFundDealOrderDtlBeanList)) {
            logger.info("BuyValidController|getAppSuccReply|highFundDealOrderDtlBeanList is empty");
            return 0;
        }

        // 金额相同的重复的订单
        int appSuccReplyNums = 0;
        for (HighFundDealOrderDtlBean highFundDealOrderDtlBean : highFundDealOrderDtlBeanList) {
            logger.info("BuyValidController|getAppSuccReply|orderAppAmt:{}, appAmt:{}", highFundDealOrderDtlBean.getAppAmt(), appAmt);
            if (highFundDealOrderDtlBean.getAppAmt().compareTo(appAmt) == 0) {
                appSuccReplyNums = appSuccReplyNums + 1;
            }
        }
        logger.info("BuyValidController|getAppSuccReply|appSuccReplyNums:{}", appSuccReplyNums);
        return appSuccReplyNums;
    }

    /**
     * convertCanNotBuyDesc:(转换不可购买描述)
     *
     * @param buyStatusType
     * @return
     * <AUTHOR>
     * @date 2018年5月10日 下午2:18:39
     */
    private String convertCanNotBuyDesc(String buyStatusType) {
        String desc = "不可购买";
        if (StringUtils.isEmpty(buyStatusType)) {
            return desc;
        }

        if (BuyStatusTypeEnum.SOLD_OUT.getCode().equals(buyStatusType)) {
            desc = "产品已售罄,不可购买";
        } else if (BuyStatusTypeEnum.NOT_SUP_DIS.getCode().equals(buyStatusType)) {
            desc = "代销关系不支持，不可购买";
        } else if (BuyStatusTypeEnum.IN_BLANKLIST.getCode().equals(buyStatusType)) {
            desc = "在直销黑名单中，不可购买";
        } else if (BuyStatusTypeEnum.LIMIT_AGE.getCode().equals(buyStatusType)) {
            desc = "年龄不满足，不可购买";
        } else if (BuyStatusTypeEnum.NOT_BUY_STATUS.getCode().equals(buyStatusType)) {
            desc = "产品状态不可购买，或不在预约期，不可购买";
        } else if (BuyStatusTypeEnum.ERROR_CONFIG.getCode().equals(buyStatusType)) {
            desc = "参数配置错误，不可购买";
        } else if (BuyStatusTypeEnum.ONLY_SUPPLY_BUY.getCode().equals(buyStatusType)) {
            desc = "只支持追加购买";
        } else if (BuyStatusTypeEnum.OTHERS.getCode().equals(buyStatusType)) {
            desc = "不可购买";
        }

        return desc;
    }

    private static Integer PE_VC_MAX_AGE = 70;

    public String validatePEProductAgeLimit(String txAcctNo, String disCode, String productSubType) throws
            Exception {
        if (ProductTypeEnum.PE_VC.getCode().equals(productSubType)) {
            QueryCustInfoAndTxAcctForCounterResult queryCustInfoResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);
            if (InvstTypeEnum.INDI.getCode().equals(queryCustInfoResult.getInvstType())
                    && IdTypeEnum.IDCARD.getValue().equals(queryCustInfoResult.getIdType())) {
                String idNo = queryCustInfoResult.getIdNo();
                if (!ProductInfoValidator.validateCustAge(idNo, 0, PE_VC_MAX_AGE)) {
                    return YesOrNoEnum.NO.getCode();
                }
            }
        }
        return YesOrNoEnum.YES.getCode();
    }
}

