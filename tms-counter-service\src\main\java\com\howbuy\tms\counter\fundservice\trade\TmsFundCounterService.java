/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundservice.trade;

import com.howbuy.tms.common.outerservice.finonline.querylatestpaycust.QueryLatestPayCustResult;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;

import java.io.InputStream;
import java.util.List;

/**
 * @description:(中台柜台控制台服务)
 * <AUTHOR>
 * @date 2017年3月28日 下午8:49:54
 * @since JDK 1.6
 */
public interface TmsFundCounterService {

    /**
     * 
     * CounterPurchase:(零售柜台购买)
     * 
     * @param counterPurchaseReqDto
     * @return
     * <AUTHOR>
     * @date 2017年3月31日 上午9:21:51
     */
    public CounterPurchaseRespDto counterPurchase(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception;

    public CounterPurchaseRespDto counterAdviserPurchase(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryAcctBalanceDtl:(查询基金持仓明细)<br>协议类型：1-普通公募
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午4:46:25
     */
    public QueryAcctBalanceDtlRespDto queryAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * queryAcctBalDtlByMultiProtocol:(查询客户基金多个协议持仓明细)<br>协议类型：1-普通公募 + 7-普通公募定投...
     * @param reqDto
     * @param disInfoDto
     * @param protocolTypeList
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年1月23日 下午8:16:14
     */
    public QueryAcctBalanceDtlRespDto queryAcctBalDtlByMultiProtocol(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception;


    /**
     *
     * queryAcctBalDtlByMultiProtocol:(查询客户基金多个协议持仓明细)<br>协议类型：1-普通公募 + 7-普通公募定投...
     * @param reqDto
     * @param disInfoDto
     * @param protocolTypeList
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年1月23日 下午8:16:14
     */
    QueryAcctBalanceDtlRespDto queryAcctBalDtlByMultiProtocolType(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception;

    /**
     * 
     * counterRedeem:(柜台赎回)
     * 
     * @param counterRedeemReqDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年3月31日 上午9:52:58
     */
    public CounterRedeemRespDto counterRedeem(CounterRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 赎回订单
     * @param counterRedeemReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    CounterRedeemRespDto counterRedeemAdviser(CounterRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception;


    /**
     * 赎回订单
     * @param counterRedeemReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    CounterRedeemRespDto counterRedeempPortfolio(CounterPortfolioRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * counterExchange:(柜台基金转换)
     * 
     * @param counterExchangeReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午6:34:44
     */
    public CounterExchangeRespDto counterExchange(CounterExchangeReqDto counterExchangeReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterCancel:(零售柜台撤单)
     * 
     * @param counterCancelReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月16日 下午7:18:08
     */
    public CounterCancelRespDto counterCancel(CounterCancelReqDto counterCancelReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterModifyDiv:(零售柜台修改分红方式)
     * 
     * @param counterModifyDivReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月17日 上午11:04:53
     */
    public CounterModifyDivRespDto counterModifyDiv(CounterModifyDivReqDto counterModifyDivReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryCanCancelOrder:(查询可撤销订单)
     * 
     * @param custNo
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月17日 上午11:16:47
     */
    public List<CancelDealDto> queryCanCancelOrder(String dealNo, String custNo, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterQueryOrderById:(查询未审核订单)
     * 
     * @param counterQueryOrderReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:04:49
     */
    public CounterOrderDto counterQueryOrderById(CounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterQueryOrder:(查询未审核订单)
     * 
     * @param counterQueryOrderReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:12:13
     */
    public CounterQueryOrderRespDto counterQueryOrder(CounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * checkOrder:(下审核订单)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:20:34
     */
    public boolean checkOrder(SubmitUncheckOrderDto submitUncheckOrderDto, CounterPortfolioProductDto productDto, List<SubmitUncheckOrderDtlDto> dtlOrderDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * counterEnd:(柜台收市)
     * 
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月20日 下午2:19:16
     */
    public boolean counterEnd(DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * calDiscountRate:(计算折扣)
     * 
     * @param custInfoDto
     * @param fundCode
     * @param bankCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年11月24日 下午3:11:13
     */
    public FeeDto calDiscountRate(CustInfoDto custInfoDto, String fundCode, String bankCode) throws Exception;
    
    /**
     * 
     * allRedeemValidate:(零售客户持仓基金是否可以全赎)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月13日 上午10:20:30
     */
    public String queryIsOrNotAllRedeem(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * queryCustBankBalVol:(查询客户基金银行卡持仓份额)
     *                <br>份额合并或份额迁移
     * @param balVolDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月4日 上午11:18:31
     */
    public QueryCustBankBalVolRespDto queryCustBankBalVol(QueryCustBankBalVolReqDto balVolDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * counterFundShareMergeVol:(柜台份额合并或迁移申请提交)
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月8日 上午10:43:27
     */
    public CounterShareMergeVolRespDto counterFundShareMergeVol(CounterShareMergeVolReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * counterFundShareTransferVol:份额迁移申请提交
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2019年5月21日 下午4:25:22
     */
    public CounterShareMergeVolRespDto counterFundShareTransferVol(CounterShareTransferVolReqDto reqDto, String searchDisCode, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * querySubmitUnCheckDtlOrder:(查询柜台提交的待审核明细订单)
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:04:49
     */
    public List<SubmitUncheckOrderDtlDto> querySubmitUnCheckDtlOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    
    /**
     * 
     * querySubmitUnCheckDtlOrderPage:(查询柜台提交的待审核明细订单)
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:04:49
     */
    public SubmitUncheckOrderDtlAllDto querySubmitUnCheckDtlOrderPage(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;


    /**
     * 
     * queryAppRateChange:(查询修改费率接口)
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2021年2月23日 下午5:54:34
     */
    public QueryAppRateChangeResDto queryAppRateChange(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    
    /**
     * 
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    public boolean counterModifyDicount(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    
    /**
     * 
     * queryShareMergeTradeOrder:(查询份额合并或迁移交易订单信息)
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月14日 下午2:44:54
     */
    public List<CounterShareMergeTradeOrderDto> queryShareMergeTradeOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * counterTransferTube:(零售转托管转入或转出)
     * @param counterTransferTubeReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月9日 下午3:53:07
     */
    public TmsCounterResult counterTransferTube(CounterTransferTubeReqDto counterTransferTubeReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * queryCounterEndTaInfo:(查询柜台收市TA信息)
     * @param sysCode
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月14日 下午3:33:18
     */
    public CounterEndTaRespDto queryCounterEndTaInfo(String sysCode, DisInfoDto disInfoDto) throws Exception;
    
   /**
    * 
    * counterSaveOrDelNotEndTa:(保存或删除柜台不收市TA信息)
    * @param actionType
    *           业务操作类型1-ADD;3-DELETE;
    * @param disInfoDto
    * @throws Exception
    * <AUTHOR>
    * @date 2018年12月14日 下午3:54:09
    */
    public CounterRespDto counterSaveOrDelNotEndTa(String actionType, String sysCode, List<FundTaInfoDto> taDtoList, DisInfoDto disInfoDto) throws Exception;

    public CounterTransferTubeRespDto counterLctTransferTube(CounterTransferTubeReqDto counterTransferTubeReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 查询客户资料明细
     * @param queryReqDto
     * @return
     */
    List<ExchangeCardMaterialDtlDto> queryMaterialDtl(CounterQueryOrderReqDto queryReqDto, DisInfoDto disInfoDto)throws Exception;
    /**
     * @description:上传双录文件
     * @param inputStream
     * @param fileName
     * @param appDt
     * @return java.lang.String
     * @author: xingxing.wang
     * @date: 2020/8/18 14:34
     * @since JDK 1.8
     */
    public String uploadDoubleRecordFile(InputStream inputStream, String fileName, String appDt) throws Exception;

    /**
     * @description:查询最近一次打款记录
     * @param txAcctNo
     * @return com.howbuy.tms.common.outerservice.finonline.querylatestpaycust.QueryLatestPayCustResult
     * @author: dejun.gu
     * @date: 2020/11/12 13:15
     * @since JDK 1.8
     */
    QueryLatestPayCustResult queryLatestPayRecord(String txAcctNo, String disCode, String cpAcctNo, String bankAcctDigest);


    /**
     *
     * counterTransferTube:(零售转托管转入)
     * @param reqDtoList
     * @param disInfoDto
     * @return CounterTransferTubeRespDto
     * @throws Exception
     * <AUTHOR>
     * @date 2023年5月25日 下午3:53:07
     */
    TmsCounterResult counterTransferInTube(List<CounterTransferTubeReqDto> reqDtoList, DisInfoDto disInfoDto) throws Exception;
}
