/**
* 转托管转入
* <AUTHOR>
* @date 2018-10-09 14:02
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();
	TransfertubeIn.init();
	TransfertubeIn.currDate = '';
});

var TransfertubeIn = {
	init:function(){

		/**
		 * 确认转托管转入
		 */
		$("#confimTransInBtn").on('click',function(){
			TransfertubeIn.confirm("add", null);
		});

		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});

		/**
		 * 查询客户基本信息
		 */
		$("#queryCustInfoBtn").on('click',function(){
			QueryCurrentCustInfo.QueryCurrentCustInfo();
		});

		/**
		 * 查询基金基本信息
		 */
		$(".searchIcon").on('click blur',function(){
			QueryCurrentFundInfo.queryFundInfo();
		});

		/**
		 * 业务类型与对方销售人代码联动
		 */
		Init.selectBoxTransferTubeBusiType();

		// 全选
		$("#selectAll").on('change', function () {
			QueryCurrentFundInfo.selectAll();
		});

		// 新增转托管
		$("#addItem").on('click', function () {
			QueryCurrentFundInfo.addItem();
		});
		// 删除转托管
		$("#removeItem").on('click', function () {
			QueryCurrentFundInfo.removeItem();
		});
	},


	/***
	 * 确认转托管转入
	 */
	confirm : function(action, dealAppNo){

		if (CommonUtil.isEmpty(QueryCurrentCustInfo.custInfo.custNo)) {
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		// 转入录入信息
		var transfertubeInForm = $("#transfertubeInForm").serializeObject();
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		//var transInVolCmd = {"appVol": CommonUtil.unFormatAmount(inAppVol), "cpAcctNo":cpAcctNo, "bankAcct":bankAcct, "bankCode":bankCode};

		if (!Validate.validateTransactorInfo(transactorInfoForm, QueryCurrentCustInfo.custInfo)) {
			return false;
		}

		 var appDtm = $("#appDt").val() + '' + $("#appTm").val();
		if (CommonUtil.isEmpty($("#appDt").val())) {
			CommonUtil.layer_tip("请输入下单时间");
			return false;
		}
		if (!Valid.valiadTradeTime($("#appTm").val())) {
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			return false;
		}

		var transferTubeBusiType = $("#transferTubeBusiType").val();
		var tSellerCode = $("#tSellerCode").val();
		if (transferTubeBusiType == '2' && tSellerCode == '304') {
			CommonUtil.layer_tip("场外跨销售机构, 对方销售人代码不能输入好买304！");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#transfertubeInForm"));
		if (!validRst.status) {
			CommonUtil.layer_tip(validRst.msg);
			return false;
		}

		var transfertubeInListForm = [];

		if ($("input[class='selectBox']:checked").length == 0) {
			CommonUtil.layer_tip("至少录入一条转托管数据后再提交");
			return false;
		}

		 var validateFlag = true;
		 var taCodeList =[];

		$("#dtlForms").find(".rowData").each(function (index, element) {
			// 被选中的
			if ($(element).find('input:checkbox').is(':checked')) {
				var fundCode = $(element).find(".fundCode").val();
				if (CommonUtil.isEmpty(fundCode)) {
					$("#inAppVol").val();
					CommonUtil.layer_tip("请输入转入基金代码");
					validateFlag = false;
					return false;
				}

				/**
				 * 转托管方式,1-一次转托管；2-两次转托管
				 */
				var chgTrusteeMode = QueryCurrentFundInfo.fundInfo.chgTrusteeMode;
				if (chgTrusteeMode == "1") {
					var fundName = QueryCurrentFundInfo.fundInfo.fundAttr;
					CommonUtil.layer_tip("转入基金" + fundName + "为一次转托管，不可转托管转入！");
					validateFlag = false;
					return false;
				}

				var inAppVol = $(element).find("#inAppVol").val();
				if (CommonUtil.isEmpty(inAppVol)) {
					CommonUtil.layer_tip("请填写转托管转入份额");
					validateFlag = false;
					return false;
				}

				var cpAcctNo = $(element).find("#selectBank").val();
				var originalAppDealNo = $(element).find("#originalAppDealNo").val();
				var bankAcct = $(element).find("#selectBank").find('option:selected').attr("bankacct");
				var bankCode = $(element).find("#selectBank").find('option:selected').attr("bankcode");
				if (CommonUtil.isEmpty(cpAcctNo)) {
					CommonUtil.layer_tip("请选择银行卡");
					validateFlag = false;
					return false;
				}
				var transInVolCmd = {
					"fundCode": fundCode,
					"appVol": CommonUtil.unFormatAmount(inAppVol),
					"cpAcctNo": cpAcctNo,
					"bankAcct": bankAcct,
					"bankCode": bankCode,
					"originalAppDealNo": originalAppDealNo
				};
				transfertubeInListForm.push(transInVolCmd);

				var fundTxAcctNo = $(element).find("#fundTxAcctNo").html();
				var taCode = $(element).find("#taCode").html();
				// 确认框
				if (CommonUtil.isEmpty(cpAcctNo) || fundTxAcctNo === "--") {
					var split = " ";
					taCodeList.push(taCode + split);
				}
			}

		});

		if(!validateFlag){
			return false;
		}

		var uri = TmsCounterConfig.TRANSFERTUBE_IN_FUND_CONFIRM_URL ||  {};
		var reqparamters = {"dealAppNo": CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
			"fundInfoListForm": JSON.stringify(QueryCurrentFundInfo.fundInfoList),
			"custInfoForm": JSON.stringify(QueryCurrentCustInfo.custInfo),
			"transfertubeInForm": JSON.stringify(transfertubeInForm),
			"transfertubeInListForm": JSON.stringify(transfertubeInListForm),
			"transactorInfoForm": JSON.stringify(transactorInfoForm)
		};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		paramters.method = "";

		if(taCodeList.length) {
			// 询问框
			layer.confirm(`<div style='margin: 6px 20px 0 20px;'>TA[${taCodeList.toString()}]未开通基金账号，是否继续进行转托管</div>`, {
				area: ['200px', '200px'],
				title: "确认",
				btn: ['确认','取消']
			},function(){
				layer.close(layer.index);
				// 提交
				TransfertubeIn.gotoAjaxAndCallBack(paramters);
				//CommonUtil.ajaxAndCallBack(paramters, TransfertubeIn.callBack);
			},function(){
				layer.close(layer.index);
				return false;
			})
		}else{
			// 提交
			TransfertubeIn.gotoAjaxAndCallBack(paramters);
			//CommonUtil.ajaxAndCallBack(paramters, TransfertubeIn.callBack);
		}
	},

	gotoAjaxAndCallBack: function (paramters) {
		var originalAppDealNoFlag = false;
		$("#dtlForms").find(".rowData").each(function (index, element) {
			// 被选中的
			if ($(element).find('input:checkbox').is(':checked')) {
				var originalAppDealNo = $("#originalAppDealNo").val();
				if (CommonUtil.isEmpty(originalAppDealNo)) {
					originalAppDealNoFlag = true;
				}
			}
		});

		if(originalAppDealNoFlag){
			layer.confirm('请确认该TA是否需要填写原申请单号！', {
				btn: ['继续提交', '去填写']
			}, function (index) {
				layerall_close();
				// 提交
				CommonUtil.ajaxAndCallBack(paramters, TransfertubeIn.callBack);
			}, function () {
				layerall_close();
			});
		} else {
			// 提交
			CommonUtil.ajaxAndCallBack(paramters, TransfertubeIn.callBack);
		}
	},

	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confimTransInBtn");
			CommonUtil.layer_tip("提交成功" + respDesc);
		}else{
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	},
};



