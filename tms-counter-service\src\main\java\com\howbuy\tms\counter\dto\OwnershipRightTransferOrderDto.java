package com.howbuy.tms.counter.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询股权份额转让订单信息实体
 */
@Data
public class OwnershipRightTransferOrderDto extends BaseDto {
    /**
     * 订单号
     */
    private String dealDtlNo;

    /**
     * 审核状态,0:待维护,1:待审核,2:已审核
     */
    private String checkFlag;
    /**
     * 客户号
     */
    private String custNo;

    /**
     * 客户名
     */
    private String custName;

    /**
     * 业务名称
     */
    private String mBusinessCode;

    /**
     * 基金产品代码
     */
    private String fundCode;

    /**
     * 基金产品名称
     */
    private String fundName;

    /**
     * 基金类型
     */
    private String fundType;

    /**
     * 基金二级类型
     */
    private String fundSubType;

    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 确认时间
     */
    private String ackDtm;

    /**
     * 是否转译后非交易过户
     */
    private String isNoTradeTransfer;

    /**
     * 更新时间
     */
    private Date updateDtm;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyDtm;

    /**
     * 审核人
     */
    private String checker;


}
