package com.howbuy.tms.counter.service.orderplan;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.utils.HttpConnectionPoolUtil;
import com.howbuy.tms.counter.service.orderplan.api.query.QuerySchePlanCounterRequest;
import com.howbuy.tms.counter.service.orderplan.api.query.QuerySchePlanCounterResponse;
import com.howbuy.tms.counter.service.orderplan.api.trade.RelieveSchePlanRequest;
import com.howbuy.tms.counter.service.orderplan.api.trade.RelieveSchePlanResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * @description:order-plan调用
 * <AUTHOR>
 * @date 2020年5月18日 下午1:53:15
 * @since JDK 1.6
 */
@Service
public class OrderPlanService {
    private static final Logger logger = LoggerFactory.getLogger(OrderPlanService.class);

    @Autowired
    private Config config;

    /**
     * 查询定投计划for console
     * 
     * @param request
     * @return
     */
    public QuerySchePlanCounterResponse querySchePlanCounter(QuerySchePlanCounterRequest request) {
        QuerySchePlanCounterResponse response = null;
        try {
            request.setPageNum(1);
            request.setPageSize(1000);
            String resStr = HttpConnectionPoolUtil.post(config.getQuerySchePlanForConsoleUrl(), request);
            response = JSON.parseObject(resStr, QuerySchePlanCounterResponse.class);
        } catch (Exception e) {
            logger.error("call order-plan add plan failed.", e);
        }
        return response;
    }
    
    public RelieveSchePlanResponse relieve(RelieveSchePlanRequest request){
        RelieveSchePlanResponse response = null;
        try{
            String resStr = HttpConnectionPoolUtil.post(config.getRelievePlanUrl(), request);
            response = JSON.parseObject(resStr, RelieveSchePlanResponse.class);
        }catch (Exception e){
            logger.error("call order-plan relieve plan failed.", e);
        }
        return response;
    }

}
