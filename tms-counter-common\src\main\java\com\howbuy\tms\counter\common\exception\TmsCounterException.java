/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common.exception;

import com.howbuy.tms.counter.common.TmsCounterResultEnum;

/**
 * @description:(自定义中台柜台异常类) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月29日 下午1:14:17
 * @since JDK 1.6
 */
public class TmsCounterException extends RuntimeException{
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 4155949383676489953L;

    /**
     * 异常代码
     */
    private String code;
    
    /**
     * 异常描述
     */
    private String desc;
    
    public TmsCounterException(String code,String desc){
        this.code =code;
        this.desc = desc;
    }
    public TmsCounterException(String code,String desc,Throwable cause){
        super(desc,cause);
        this.code =code;
        this.desc = desc;
    }

    public TmsCounterException(TmsCounterResultEnum tmsCounterResultEnum){
        this.code =tmsCounterResultEnum.getCode();
        this.desc = tmsCounterResultEnum.getDesc();
    }
    public TmsCounterException(TmsCounterResultEnum tmsCounterResultEnum,Throwable cause){
        super(tmsCounterResultEnum.getDesc(),cause);
        this.code =tmsCounterResultEnum.getCode();
        this.desc = tmsCounterResultEnum.getDesc();
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    
    
    
    
}

