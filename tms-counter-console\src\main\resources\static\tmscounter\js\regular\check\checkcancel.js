$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
    RegularCheckCancel.checkOrder = {};
    RegularCheckCancel.init(checkId,custNo,disCode,idNo);
});

var RegularCheckCancel = {
	init:function(checkId, custNo, disCode,idNo){
        RegularQueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		RegularQueryCheckOrder.queryCheckOrderById(checkId,RegularCheckCancel.queryCheckOrderByIdBack);
		
		$("#returnBtn").on('click',function(){
            RegularCheckCancel.confirm(RegularCounterCheck.Faild);
		});
		
		$("#succBtn").on('click',function(){
            RegularCheckCancel.confirm(RegularCounterCheck.Succ);
		});
	},
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';
		
		var uri= TmsCounterConfig.CHECK_REGULAR_CONFIRM_URL ||  {};
		
		if(RegularCounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			RegularCheckCancel.checkFaildDesc = $("#checkFaildDesc").val();
		}
		
		var reqparamters ={"checkFaildDesc":RegularCheckCancel.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(RegularCheckCancel.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);;
		CommonUtil.ajaxAndCallBack(paramters, RegularCheckCancel.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		RegularCheckCancel.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(RegularCheckCancel.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(RegularCheckCancel.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		
		RegularQueryCanCancel.queryCanCancel(RegularQueryCustInfo.custInfo.custNo, RegularCheckCancel.checkOrder.dealNo,RegularCheckCancel.checkOrder.operatorNo);
		
		$("#cancelType").html(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, RegularCheckCancel.checkOrder.txCode, ''));
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(RegularCheckCancel.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").html(RegularCheckCancel.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, RegularCheckCancel.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(RegularCheckCancel.checkOrder.transactorName);
		}
				
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, RegularCheckCancel.checkOrder.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(RegularCheckCancel.checkOrder.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(RegularCheckCancel.checkOrder.memo);
		}
	},
}
