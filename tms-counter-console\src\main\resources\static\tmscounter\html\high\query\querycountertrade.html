<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>柜台交易查询</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 业务查询 <span class="c-gray en">&gt;</span> 柜台交易查询 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
        <form id="queryConditonForm">
            <div class="container_box">
                <p class="mainTitle mt10">柜台交易查询</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="txAcctNo" placeholder="请输入">
                    <span class="normal_span ml30">证件号：</span>
                    <input type="text" name="idNo" placeholder="请输入">
                    <span class="normal_span ml30">业务类型：</span>
                    <span class="select-box inline">
                       <select name="txCode" class="select" id="selectTxCode">
                       </select>
                    </span>
                </div>
                <div class="cp_top">
                    <span class="normal_span">开始日期：</span>
                    <input name="beginDtm" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYY-MM-DD'})">
                    <span class="normal_span ml30">结束日期：</span>
                    <input name="endDtm" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYY-MM-DD'})">
                    <span class="normal_span ml30">审核状态：</span>
                    <span class="select-box inline">
                       <select name="checkFlag" class="select" id="selectCheckFlag">
                          <option value="">全部</option>
                          <option value="0">待审核</option>
                          <option value="1">审核成功</option>
                          <option value="2">审核失败</option>
                       </select>
                    </span>
                    <!--<a href="javascript:void(0)" class="btn radius btn-secondary ml30">查询</a>-->
                </div>
                <div class="cp_top">
                    <span class="normal_span">申请状态：</span>
                    <span class="select-box inline">
                       <select name="appFlag" class="select" id="selectAppFlag">
                          <option value="">全部</option>
                          <option value="0">待申请</option>
                          <option value="1">申请成功</option>
                          <option value="2">申请失败</option>
                       </select>
                    </span>
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryTradeBtn">查询</a>
                    <a href="javascript:void(0)" class="btn radius btn-success ml30" id="downLoadBtn">下载</a>
                </div>
            </div>
        </form>
        </div>
    </div>
    <div class="page-container">
        <p class="main_title">查询结果</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>基金代码</th>
                        <th>基金简称</th>
                        <th>业务类型</th>
                        <th>柜台订单号</th>
                        <th>中台订单号</th>
                        <th>审核状态</th>
                        <th>申请状态</th>
                        <th>申请时间</th>
                        <th>操作员</th>
                        <th>操作</th>
                    </tr>
               </thead>
                <tbody id="rsList">
                </tbody>
            </table>
        </div>
        <div class="clear page_all">
            <div class="fy_part fr mt20" id="pageView"></div>
        </div>
    </div>

    <div class="detailInfo hide">
        <table class="tabPop">
            <tr>
                <td class="type" width="15%">基金代码</td>
                <td width="35%"></td>
                <td class="type" width="15%">业务类型</td>
                <td width="35%"></td>
            </tr>
            <tr>
                <td class="type">申请金额/份额</td>
                <td></td>
                <td class="type">申请折扣率</td>
                <td></td>
            </tr>
            <tr>
                <td class="type">银行卡</td>
                <td></td>
                <td class="type">支付方式</td>
                <td>自划款</td>
            </tr>
            <tr>
                <td class="type">申请时间</td>
                <td></td>
                <td class="type">目标分红方式</td>
                <td></td>
            </tr>
            <tr>
                <td class="type">巨额赎回顺延</td>
                <td></td>
                <td class="type">异常赎回标记</td>
                <td></td>
            </tr>
            <tr>
                <td class="type">申请状态</td>
                <td></td>
                <td class="type">审核状态</td>
                <td></td>
            </tr>
            <tr>
                <td class="type">柜台订单号</td>
                <td></td>
                <td class="type">中台订单号</td>
                <td></td>
            </tr>
            <tr>
                <td class="type">失败原因</td>
                <td></td>
                <td class="type"></td>
                <td></td>
            </tr>
        </table>
    </div>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
     <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/high/query/querycountertrade.js?v=20200301002"></script>
</body>

</html>