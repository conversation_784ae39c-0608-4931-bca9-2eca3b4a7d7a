/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.enums;

/**
 * 
 * @description:(审核操作类型)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年3月8日 下午5:16:09
 * @since JDK 1.6
 */
public enum CheckActionTypeEnum {
    
    /**
     * 审核
     */
    ACTION_CHECK("0", "审核"),
    
    /**
     * 修改
     */
    ACTION_MODIFY("1", "修改");
    
    


    private String code;
    private String name;

    private CheckActionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
