package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.util.List;

public class QueryAppRateChangeResDto implements Serializable{

	private static final long serialVersionUID = 2208452895987311269L;
	
	/**
     * 总记录数
     */
    private long totalCount;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 当前页
     */
    private long pageNo;
	
    /**
     * 列表
     */
	private List<QueryAppRateChangeDto> queryAppRateChangeList;

	public long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}

	public long getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}

	public long getPageNo() {
		return pageNo;
	}

	public void setPageNo(long pageNo) {
		this.pageNo = pageNo;
	}

	public List<QueryAppRateChangeDto> getQueryAppRateChangeList() {
		return queryAppRateChangeList;
	}

	public void setQueryAppRateChangeList(List<QueryAppRateChangeDto> queryAppRateChangeList) {
		this.queryAppRateChangeList = queryAppRateChangeList;
	}
	
}
