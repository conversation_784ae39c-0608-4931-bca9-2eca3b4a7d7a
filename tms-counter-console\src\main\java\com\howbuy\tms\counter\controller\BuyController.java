/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.controller;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.prosale.request.QueryCurrentPreInfoRequest;
import com.howbuy.crm.prosale.response.GetCurrentPreInfoResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.interlayer.product.model.appointment.ProductAppointmentInfoModel;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.context.BuyContext;
import com.howbuy.tms.counter.controller.context.ContextUtils;
import com.howbuy.tms.counter.controller.validate.ValidateUtils;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.FundLimitBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.OtherInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.service.validate.ValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.counter.util.CounterOrderFormUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(购买控制器首页)
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class BuyController {
    private static Logger logger = LogManager.getLogger(BuyController.class);
    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    
    @Autowired
    private ValidateService validateService;
    
    @Autowired
    @Qualifier("tmscounter.preBookService")
    private PreBookService preBookService;
    
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    /**
     * 
     * buyIndex:(确认购买)
     * 
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/buyconfirm.htm")
    public ModelAndView buyConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建下单上下文信息
        BuyContext buyContext = buildBuyContext(request);

        String fundCode = buyContext.getFundInfo().getFundCode();
        
        //校验产品信息
        validateService.validateProductInfo(fundCode);

        //查询产品信息
        HighProductBaseInfoBean highProduct =  queryHighProductOuterService.getHighProductBaseInfo(fundCode);
        buyContext.setHighProduct(highProduct);

        // 预约信息
        if (!crmAppointExist(buyContext.getCustomerAppointmentInfoDto())) {
            buyContext.setCustomerAppointmentInfoDto(getCustomerAppointmentInfoDto(buyContext.getCustInfoDto(), fundCode));
        }

        // 申请日期
        Date appDtm = DateUtil.string2Date(buyContext.getCounterPurchaseReqDto().getAppDtm(), DateUtils.YYYYMMDDHHMMSS);

        // 校验申请日期
        ValidateUtils.validateAppDtm(appDtm);

        // 校验线上资料
        if(buyContext.getAuditingOrderFileCmd() != null
                && !StringUtils.isEmpty(buyContext.getAuditingOrderFileCmd().getOrderid())) {
            tmsCounterOutService.validateOrderFileStatus(buyContext.getAuditingOrderFileCmd());
        }else{
            tmsCounterOutService.validateOrderFileExist(ContextUtils.buildQueryCotext(buyContext, TmsCounterConstant.CRM_TRADE_TYPE_BUY), OpCheckNode.PRE_CHECK.getCode());
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(buyContext.getCustInfoDto().getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());

        CounterOrderDto condition = ContextUtils.getValidateReplyCondition(buyContext, TxCodes.HIGH_COUNTER_PURCHASE);
        if(buyContext.getAuditingOrderFileCmd()!=null){
            condition.setDealAppNo(buyContext.getAuditingOrderFileCmd().getForderid());
        }
        tmsCounterService.validateReplyOrder(condition, disInfoDto);


        buyContext.setAppDtm(appDtm);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        // 设置下单信息
        fillOrderInfo(buyContext);
        CounterPurchaseRespDto responseDto = tmsCounterService.counterPurchase(buyContext.getCounterPurchaseReqDto(), disInfoDto);

        // 修改客户默认回款到银行卡
        changeFinaDirectToBank(buyContext.getCustInfoDto());

        // 线上资料初审通过
        tmsCounterOutService.auditingFile(buyContext.getOperatorInfoCmd(), buyContext.getAuditingOrderFileCmd(), responseDto.getDealAppNo());

        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    private BuyContext buildBuyContext(HttpServletRequest request){
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        //购买确认信息
        String buyConfirmCmd = request.getParameter("buyConfirmForm");
        //客户信息
        String custInfoForm = request.getParameter("custInfoForm");
        //基金信息
        String fundInfoForm = request.getParameter("fundInfoForm");
        //其他信息
        String othetInfoForm = request.getParameter("othetInfoForm");
        //投顾预约信息
        String appointmentForm = request.getParameter("appointmentForm");
        //预约开放日历
        String appointmentInfoForm= request.getParameter("appointmentInfoForm");
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");

        OtherInfoDto otherInfoDto = JSON.parseObject(othetInfoForm, OtherInfoDto.class);
        CounterPurchaseReqDto counterPurchaseReqDto = JSON.parseObject(buyConfirmCmd, CounterPurchaseReqDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        //投顾预约信息
        CustomerAppointmentInfoDto customerAppointmentInfoDto = JSON.parseObject(appointmentForm, CustomerAppointmentInfoDto.class);
        //预约开放日信息
        ProductAppointmentInfoModel productAppointmentInfoModel = JSON.parseObject(appointmentInfoForm, ProductAppointmentInfoModel.class);

        String transactorInfoForm = request.getParameter("transactorInfoForm");
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);

        FundInfoAndNavDto fundInfo = JSON.parseObject(fundInfoForm, FundInfoAndNavDto.class);

        AuditingOrderFileCmd auditingOrderFileCmd =  null;
        if(!StringUtils.isEmpty(materialinfoForm)){
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
        }

        BuyContext buyContext = new BuyContext();
        buyContext.setCounterPurchaseReqDto(counterPurchaseReqDto);
        buyContext.setAuditingOrderFileCmd(auditingOrderFileCmd);
        buyContext.setCustInfoDto(custInfoDto);
        buyContext.setCustomerAppointmentInfoDto(customerAppointmentInfoDto);
        buyContext.setFundInfo(fundInfo);
        buyContext.setOtherInfoDto(otherInfoDto);
        buyContext.setTransactorInfoDto(transactorInfoDto);
        buyContext.setProductAppointmentInfoModel(productAppointmentInfoModel);
        buyContext.setOperatorInfoCmd(operatorInfoCmd);

        return buyContext;
    }

    private void fillOrderInfo(BuyContext buyContext) {
        // 预约信息
        fillAppoinInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getCustomerAppointmentInfoDto());
        // 交易信息
        fillTradeInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getAppDtm());
        // 产品信息
        fillProductInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getHighProduct());
        // 客户信息
        fillCustInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getCustInfoDto());
        // 其他信息
        fillOtherInfo(buyContext.getOtherInfoDto(), buyContext.getCounterPurchaseReqDto());
        // 经办人信息
        fillTransactorInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getTransactorInfoDto());
        //操作员信息
        CommonUtil.setCommonOperInfo(buyContext.getOperatorInfoCmd(), buyContext.getCounterPurchaseReqDto());
        // 线上资料信息
        fillMaterialInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getAuditingOrderFileCmd());

        // 设置下单快照信息
        fillSnapshotInfo(buyContext.getCounterPurchaseReqDto(), buyContext.getProductAppointmentInfoModel(),
                buyContext.getFundInfo(),buyContext.getHighProduct());
    }

    private void fillMaterialInfo(CounterPurchaseReqDto counterPurchaseReqDto, AuditingOrderFileCmd auditingOrderFileCmd) {
        if(auditingOrderFileCmd != null){
            // CRM线上资料ID
            counterPurchaseReqDto.setMaterialId(auditingOrderFileCmd.getOrderid());
        }
    }

    private void fillSnapshotInfo(CounterPurchaseReqDto counterPurchaseReqDto, ProductAppointmentInfoModel productAppointmentInfoModel, FundInfoAndNavDto fundInfo, HighProductBaseInfoBean highProduct) {
        String openStartDt = null;
        if(productAppointmentInfoModel != null){
            openStartDt = productAppointmentInfoModel.getOpenStartDt();
        }
        String submitTaDt = CounterOrderFormUtil.getSubmitTradeDt(counterPurchaseReqDto.getAppDt(), openStartDt );
        HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProduct.getFundCode(), submitTaDt);
        FundLimitBean fundLimitBean = new FundLimitBean();
        fundLimitBean.setNetMinAppAmt(fundInfo.getNetMinAppAmt());
        fundLimitBean.setNetMinSuppleAmt(fundInfo.getNetMinSuppleAmt());

        String  counterOrderFormMemo =  CounterOrderFormUtil.createCounterOrderFormNew( highProduct,
                highProductStatInfoBean,productAppointmentInfoModel, fundLimitBean);

        counterPurchaseReqDto.setOrderFormMemo(counterOrderFormMemo);
    }

    private void fillAppoinInfo(CounterPurchaseReqDto counterPurchaseReqDto, CustomerAppointmentInfoDto customerAppointmentInfoDto) {
        counterPurchaseReqDto.setAppointmentAmt(customerAppointmentInfoDto.getAppAmt());
        counterPurchaseReqDto.setAppointmentDealNo(customerAppointmentInfoDto.getAppointId());
        counterPurchaseReqDto.setAppointmentDiscount(customerAppointmentInfoDto.getDiscountRate());
    }

    private void fillTradeInfo(CounterPurchaseReqDto counterPurchaseReqDto, Date appDtm) {
        counterPurchaseReqDto.setBankAcct(PrivacyUtil.encryptBankAcct(counterPurchaseReqDto.getBankAcct()));
        counterPurchaseReqDto.setAppDt(DateUtils.formatToString(appDtm, DateUtils.YYYYMMDD));
        counterPurchaseReqDto.setAppTm(DateUtils.formatToString(appDtm, DateUtils.HHMMSS));
        //支付方式默认自划款
        counterPurchaseReqDto.setPaymentType(PaymentTypeEnum.SELF_DRAWING.getCode());
        counterPurchaseReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
    }

    private DisInfoDto changeFinaDirectToBank(CustInfoDto custInfoDto) {
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        if(StringUtils.isEmpty(custInfoDto.getCollectProtocolMethod()) || CollectProtocolMethodEnum.DEFAULT_CXG.getCode().equals(custInfoDto.getCollectProtocolMethod()) ){
            //1-系统默认回银行卡
            tmsCounterOutService.changeFinaDirect(custInfoDto.getCustNo(), "1" , disInfoDto);
        }
        return disInfoDto;
    }

    private void fillTransactorInfo(CounterPurchaseReqDto counterPurchaseReqDto, TransactorInfoDto transactorInfoDto) {
        //经办人信息
        counterPurchaseReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        //经办人证件类型
        counterPurchaseReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        //经办人名称
        counterPurchaseReqDto.setTransactorName(transactorInfoDto.getTransactorName());
    }

    private void fillOtherInfo(OtherInfoDto otherInfoDto, CounterPurchaseReqDto counterPurchaseReqDto) {
        counterPurchaseReqDto.setAgentFlag(otherInfoDto.getAgentFlag());
        counterPurchaseReqDto.setConsCode(otherInfoDto.getConsCode());
    }

    private void fillCustInfo(CounterPurchaseReqDto counterPurchaseReqDto, CustInfoDto custInfoDto) {
        counterPurchaseReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterPurchaseReqDto.setCustName(custInfoDto.getCustName());
        counterPurchaseReqDto.setIdNo(PrivacyUtil.encryptIdCard(custInfoDto.getIdNo()));
        counterPurchaseReqDto.setDisCode(custInfoDto.getDisCode());
    }

    private void fillProductInfo(CounterPurchaseReqDto counterPurchaseReqDto, HighProductBaseInfoBean highProduct) {
        counterPurchaseReqDto.setFundShareClass(highProduct.getShareClass());
        counterPurchaseReqDto.setFundName(highProduct.getFundAttr());
        counterPurchaseReqDto.setFundCode(highProduct.getFundCode());
        // 产品通道3-群济私募 5-好买公募 6-高端公募
        counterPurchaseReqDto.setProductChannel(highProduct.getProductChannel());
        counterPurchaseReqDto.setTaCode(highProduct.getTaCode());
        if (StringUtils.isEmpty(counterPurchaseReqDto.getFundShareClass())) {
            counterPurchaseReqDto.setFundShareClass(highProduct.getShareClass());
        }
    }

    private CustomerAppointmentInfoDto getCustomerAppointmentInfoDto(CustInfoDto custInfoDto, String fundCode) {
        CustomerAppointmentInfoDto customerAppointmentInfoDto;
        GetCurrentPreInfoResponse currentPreInfoResponse = getGetCurrentPreInfo(custInfoDto, fundCode);
        if(currentPreInfoResponse != null && !StringUtils.isEmpty(currentPreInfoResponse.getPreId())){
            customerAppointmentInfoDto = new CustomerAppointmentInfoDto();
            customerAppointmentInfoDto.setAppointId(currentPreInfoResponse.getPreId());
        }else{
            throw new TmsCounterException(TmsCounterResultEnum.PREID_NOT_EXIT);
        }
        return customerAppointmentInfoDto;
    }

    private boolean crmAppointExist(CustomerAppointmentInfoDto customerAppointmentInfoDto){
        return (null != customerAppointmentInfoDto && !StringUtils.isEmpty(customerAppointmentInfoDto.getAppointId()));
    }

    private GetCurrentPreInfoResponse getGetCurrentPreInfo(CustInfoDto custInfoDto, String fundCode) {
        QueryCurrentPreInfoRequest queryCurrentPreInfoRequest = new QueryCurrentPreInfoRequest();
        List<String> prebookStateList = new ArrayList<>();
        prebookStateList.add(PreBookStateEnum.CONFIRM.getCode());
        queryCurrentPreInfoRequest.setPrebookState(prebookStateList);
        queryCurrentPreInfoRequest.setFundCode(fundCode);
        List<String> preTypeList = new ArrayList<>();
        preTypeList.add(OrderFormTypeEnum.PAPER.getCode());
        queryCurrentPreInfoRequest.setPreType(preTypeList);
        List<String> tradeTypeList = new ArrayList<>();
        tradeTypeList.add(PreTradeTypeEnum.BUY.getCode());
        tradeTypeList.add(PreTradeTypeEnum.SUB_BUY.getCode());
        queryCurrentPreInfoRequest.setTradeType(tradeTypeList);
        String hbOneNo = tmsCounterOutService.queryHboneNoByTxAccountNo(custInfoDto.getCustNo());
        queryCurrentPreInfoRequest.setHboneNo(hbOneNo);
        queryCurrentPreInfoRequest.setUseFlag(PreBookUseFlagEnum.NOT_USED.getCode());
        return preBookService.getCurrentPreInfo(queryCurrentPreInfoRequest);
    }

}
