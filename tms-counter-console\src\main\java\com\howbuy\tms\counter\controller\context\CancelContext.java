/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;
import com.howbuy.tms.counter.dto.OrderDto;
import com.howbuy.tms.counter.dto.RefundDto;


/**
 * @className CancelContext
 * @description
 * <AUTHOR>
 * @date 2019/6/19 11:28
 */
public class CancelContext extends TradeCommonContext {
    /**
     *  1-用户发起；0-非用户发起
     */
   private String userCancelFlag;
    /**
     * 撤单原因
     */
   private String cancelMemo;
   private  OrderDto orderDto;
   private RefundDto refundDto;

    public String getUserCancelFlag() {
        return userCancelFlag;
    }

    public void setUserCancelFlag(String userCancelFlag) {
        this.userCancelFlag = userCancelFlag;
    }

    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }

    public OrderDto getOrderDto() {
        return orderDto;
    }

    public void setOrderDto(OrderDto orderDto) {
        this.orderDto = orderDto;
    }

    public RefundDto getRefundDto() {
        return refundDto;
    }

    public void setRefundDto(RefundDto refundDto) {
        this.refundDto = refundDto;
    }
}
