package com.howbuy.tms.counter.config.web;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

/**
 * @author: xin.jiang.cn
 * @date: 2023年12月20日 15:29:17
 * @description:
 */
@Configuration
public class ServletConfig {

    @Bean
    public FilterRegistrationBean<DelegatingFilterProxy> shiroFilterRegistration() {
        FilterRegistrationBean<DelegatingFilterProxy> registrationBean = new FilterRegistrationBean<>();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);

        registrationBean.setFilter(proxy);
        registrationBean.addUrlPatterns("*.htm");
        registrationBean.setName("shiroFilter"); // Matches the filter-name in your web.xml
        registrationBean.setOrder(1);

        return registrationBean;
    }
}
