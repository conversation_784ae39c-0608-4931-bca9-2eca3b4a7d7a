/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(柜台强制撤单落单请求) 
 * <AUTHOR>
 * @date 2017年3月29日 下午8:25:00
 * @since JDK 1.6
 */
public class CounterForceCancelReqDto extends OperInfoBaseDto{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -4973278455762702799L;

    /**
     * 审核申请流水号
     */
    private String dealAppNo;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 子交易账号
     */
    private String subTxAcctNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金份额类型
     */
    private String fundShareClass;
    
    /**
     * 目标基金代码
     */
    private String tFundCode;

    /**
     * 目标基金份额类型
     */
    private String tFundShareClass;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 交易码
     */
    private String txCode;

    /**
     * 支付方式
     *01-自划款
     *04-代扣款
     *06-储蓄罐
     */
    private String paymentType;

    /**
     * 手续费折扣
     */
    private BigDecimal discountRate;

    /**
     * 协议类型
     *1-普通公募基金投资
     *2-组合公募基金投资
     *3-暴力定投
     */
    private String protocolType;

    /**
     * 协议号
     */
    private String protocolNo;
    
    /**
     * 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;

    /**
     * 基金分红方式
     *0-红利再投；
     *1-现金红利
     */
    private String fundDivMode;

    /**
     * 客户订单号
     */
    private String dealNo;

    /**
     * 经办人证件号
     */
    private String transactorIdNo;

    /**
     * 经办人证件类型
     */
    private String transactorIdType;

    /**
     * 经办人姓名
     */
    private String transactorName;

    /**
     * 投资顾问代码
     */
    private String consCode;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回描述
     */
    private String description;

    /**
     * 备注
     */
    private String memo;
    
    /**
     *审核人
     */
    private String checker;

    /**
     *审核日期时间
     */
    private Date checkDtm;
    
    
    /**
     * 申请标识
     */
    private String appFlag;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 巨额赎回顺延
     */
    private String largeRedmFlag;

    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是)
     */
    private String agentFlag;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 银行卡号
     */
    private String bankAcct;
    
    /**
     * ta日期
     */
    private String taTradeDt;
    
    /**
     * 当前净值
     */
    private BigDecimal nav;
    
    /**
     * 强制撤单标识
     */
    private String forceCancelFlag;
    
    /**
     * 订单信息
     */
    private String orderFormMemo;
    
    /**
     * 
     */
    private String cancelMemo;
    
    /**
     * 产品通道 3-群济私募 5-好买公募  6-高端公募
     */
    private String productChannel;
    /**
     * TACODE
     */
    private String taCode;
    /**回款方向
     */
    private String withdrawDirection;

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getSubTxAcctNo() {
        return subTxAcctNo;
    }

    public void setSubTxAcctNo(String subTxAcctNo) {
        this.subTxAcctNo = subTxAcctNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String gettFundCode() {
        return tFundCode;
    }

    public void settFundCode(String tFundCode) {
        this.tFundCode = tFundCode;
    }

    public String gettFundShareClass() {
        return tFundShareClass;
    }

    public void settFundShareClass(String tFundShareClass) {
        this.tFundShareClass = tFundShareClass;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public Date getCheckDtm() {
        return checkDtm;
    }

    public void setCheckDtm(Date checkDtm) {
        this.checkDtm = checkDtm;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag;
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }


    public String getForceCancelFlag() {
        return forceCancelFlag;
    }

    public void setForceCancelFlag(String forceCancelFlag) {
        this.forceCancelFlag = forceCancelFlag;
    }

    public String getOrderFormMemo() {
        return orderFormMemo;
    }

    public void setOrderFormMemo(String orderFormMemo) {
        this.orderFormMemo = orderFormMemo;
    }

    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getWithdrawDirection() {
        return withdrawDirection;
    }

    public void setWithdrawDirection(String withdrawDirection) {
        this.withdrawDirection = withdrawDirection;
    }
}

