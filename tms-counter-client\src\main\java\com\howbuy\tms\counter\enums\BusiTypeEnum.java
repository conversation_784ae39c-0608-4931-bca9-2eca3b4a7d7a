/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.enums;

/**
 * 
 * @description:(提前下单业务类型)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年3月9日 下午4:02:12
 * @since JDK 1.6
 */
public enum BusiTypeEnum {
    /**
     * 购买
     */
    BUY("0", "未审核"),
    /**
     * 赎回
     */
    SELL("1", "审核通过");
    
    private String code;
    private String name;

    private BusiTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
