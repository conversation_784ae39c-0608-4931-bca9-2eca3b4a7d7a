$(function () {
   

//    laypage({
//        cont: $('.fy_part'), //容器。值支持id名、原生dom对象，jquery对象,
//        pages: 10, //总页数
//        skip: true, //是否开启跳页
//        skin: '#2DB7F5',
//        groups: 3 //连续显示分页数
//    });

    

    //关闭预览
    $('.close').on('click', function () {
        $('.seeInfo').addClass('hide');
    });

    /*转换金额*/
//    function digit_uppercase(n) {
//    	var part = String(n).split(".");
//    	var num = n;
//        var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
//        var unit = [['元', '万', '亿'],['', '拾', '佰', '仟'],['角','分']];
//       
//        part = String(n).split(".");
//        var s = '';
//        n = part[0];
//        for (var i = 0; i < unit[0].length && n > 0; i++) {
//            var p = '';
//            for (var j = 0; j < unit[1].length && n > 0; j++) {
//                p = digit[n % 10] + unit[1][j] + p;
//                n = Math.floor(n / 10);
//            }
//            s = p.replace(/(零.)*零$/, '')
//                .replace(/^$/, '零') + unit[0][i] + s;
//            document.getElementById('convertInfo').innerHTML=s;
//        }
//        
//      //小数点之后进行转化,只保留2位小数
//        if(num.indexOf(".") !=-1)
//        {
//        	var newchar = '';
//        	if(part[1].length > 2){
//        		part[1] = part[1].substr(0,2);
//        	}
//        	for(i=0;i<part[1].length;i++){
//        		tmpnewchar = "";
//        		var perchar = part[1].charAt(i);
//        		newchar = newchar + digit[perchar]+unit[2][i];
//        		document.getElementById('convertInfo').innerHTML=s+newchar;
//        	}
//        }
//        
//        /*return s.replace(/(零.)*零元/, '元')
//            .replace(/(零.)+/g, '零')
//            .replace(/^$/, '零元') + '整';*/
//    }
//    
//    $('.applyAmount').on('keyup focus',function(){    	
//        var str = $(this).val() || '';
//        str = str.replace(/\,/g,'');
//        var re = /([0-9]+\.[0-9]{2})[0-9]*/;
//		 str = str.replace(re,"$1");
//		 $(this).val(str);
//        digit_uppercase(str);
//    }).blur(function(){
//        $('.convertInfo').text('');
//    })
})