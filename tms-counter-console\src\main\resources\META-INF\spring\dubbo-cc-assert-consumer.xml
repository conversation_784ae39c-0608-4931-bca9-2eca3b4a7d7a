<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!--查询当前持有好买资产(缓存)-->

    <dubbo:reference id="queryCurrentBalanceWithCacheService" interface="com.howbuy.cc.center.assetservice.centralization.service.QueryCurrentBalanceWithCacheService" registry="asset-center-remote" check="false" />



</beans>