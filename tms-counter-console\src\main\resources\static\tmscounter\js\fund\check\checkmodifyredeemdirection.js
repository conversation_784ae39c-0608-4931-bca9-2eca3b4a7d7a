/**
 * 赎回方向审核
 */
$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();
	$("#returnBtn").on('click',function(){
		CheckModifyDirection.confirm(CounterCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckModifyDirection.confirm(CounterCheck.Succ);
	});

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	var dealNo = CommonUtil.getParam("dealNo");
	CheckModifyDirection.checkOrder = {};
	CheckModifyDirection.init(checkId,custNo,disCode,idNo,dealNo);
});

var CheckModifyDirection = {	
	init:function(checkId, custNo, disCode,idNo, dealNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		var directionList = {};
		// 个人用户
		if (QueryCustInfo.custInfo.invstType == '1') {
			directionList = CONSTANTS.PERSON_DIRECTION_TYPE_MAP;
		} else {
			directionList = CONSTANTS.JIGOU_DIRECTION_TYPE_MAP;
		}
		var selectModify = CommonUtil.selectOptionsHtml(directionList);
		$("#afterModify").html(selectModify);

		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplySell.checkOrder);
		});

		// 设置申请订单信息
		QueryCheckOrder.queryCheckOrderById(checkId,CheckModifyDirection.queryCheckOrderByIdBack);

	},


	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
		}

		var afterModify = $("#afterModify").val();
		if(CommonUtil.isEmpty(afterModify) && CounterCheck.Faild != checkStatus){
			CommonUtil.layer_tip("请选择修改后回款方向");
			return false;
		}

		if(afterModify != CheckModifyDirection.checkOrder.withdrawDirection && CounterCheck.Faild != checkStatus){
			CommonUtil.layer_tip("回款方向不匹配，请重新确认");
			return false;
		}
		CheckModifyDirection.checkFaildDesc = $("#checkFaildDesc").val();

		var reqparamters ={"checkFaildDesc":CheckModifyDirection.checkFaildDesc || '',
			"checkStatus":checkStatus,
			"redeemDirection":$("#afterModify").val(),
			"beforeModifyDirection":$("#beforeModifyDirection").val(),
			"checkedOrderForm":JSON.stringify(CheckModifyDirection.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckModifyDirection.callBack);
		return true;
	},

	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},


	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckModifyDirection.checkOrder = bodyData.checkOrder || {};
		CheckModifyDirection.dealOrderDtlBean = bodyData.dealOrderDtlBean || {};

		if(CommonUtil.isEmpty(CheckModifyDirection.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckModifyDirection.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}

		$("#dealInfoId").empty();
		var element = CheckModifyDirection.dealOrderDtlBean || {};
		var trList = [];
		trList.push(CommonUtil.formatData(element.dealNo, '--'));
		trList.push(CommonUtil.formatData(CommonUtil.getMapValue(CONSTANTS.BUSI_CODE_MAP, element.mBusiCode, ''), '--'));
		trList.push(CommonUtil.formatData(element.fundName, '--'));
		trList.push(CommonUtil.formatData(element.fundCode, '--'));
		trList.push(CommonUtil.formatData(element.appDate, '--'));
		trList.push(CommonUtil.formatData(element.appAmt, '--'));
		trList.push(CommonUtil.formatData(element.appVol, '--'));
		trList.push(CommonUtil.formatData(element.tFundCode, '--'));
		trList.push(CommonUtil.formatData(element.tFundName, '--'));
		trList.push(element.mBusiCode = '1136' ? CommonUtil.formatData(element.transferAmt,'--') : '--');
		trList.push(element.mBusiCode = '1136' ? CommonUtil.formatData(element.transferVol,'--') :'--');
		trList.push(CommonUtil.getMapValue(CONSTANTS.TX_APP_FLAG_MAP, element.txAppFlag, '--'));
		trList.push(CommonUtil.formatData(element.appDate, '--'));
		trList.push(CommonUtil.formatData(element.appTime, '--'));
		var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
		$("#dealInfoId").append(trHtml);

		$.each(CONSTANTS.JIGOU_DIRECTION_TYPE_SHOW_MAP, function (key, value) {
			if (key == element.redeemDirection) {
				$("#beforeModify").html(value);
				$("#beforeModifyDirection").val(key);
			}
		});
	}

}
