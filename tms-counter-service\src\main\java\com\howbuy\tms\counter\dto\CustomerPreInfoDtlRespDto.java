/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description:(查询预约详情)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 上午9:22:49
 * @since JDK 1.6
 */
public class CustomerPreInfoDtlRespDto implements Serializable {

    private static final long serialVersionUID = 7599019413415485002L;

    private String type;

    private Integer id;

    private String serialNo;

    private String investType;

    private String hboneNo;

    private String custNo;

    private String name;

    private String idType;

    private String idNo;

    private String mobile;

    private String validOfTerm;

    private Date validDate;

    private String sex;

    private Date birth;

    private String custBankId;

    private String bankProv;

    private String bankCity;

    private String bankCode;

    private String bankNo;

    private String branchBankName;

    private String accountHolder;

    private String bankType;

    private String currency;

    private String defaultAccount;

    private String fundType;

    private String jjdm;

    private String jjjc;

    private String bookType;

    private Date dueDate;

    private BigDecimal bookAmount;

    private BigDecimal bookRatio;

    private String orderNo;

    private String province;

    private String city;

    private String district;

    private String address;

    private String zipCode;

    private String tel;

    private String email;

    private Boolean isSignFlag;

    private String freSendBill;

    private String prosession;

    private String education;

    private String incomeOfFamily;

    private String status;

    private String confirmStatus;

    private String currentStatus;

    private String targetStatus;

    private String memo;

    private Date createTime;

    private Date modTime;

    private BigDecimal baseFeeRate;

    private String buyStatus;

    private PersonInfoVO contract;
    private PersonInfoVO legal;
    private PersonInfoVO proxy;
    private String cnapsNo;
    private BigDecimal tradeFee;

    /**
     * 预约时间
     */
    private String prebookDt;  
    /**
     * 交易类型
     */
    private String tradeType;  
    /**
     * 预约类型
     */
    private String preType;  
    /**
     * 机构客户性质
     */
    private String orgProperty;  
    /**
     * 机构资质
     */
    private String aptitude;  
    /**
     * 实际控制人
     */
    private String actualController;  
    /**
     * 经营范围
     */
    private String businessScope; 
    /**
     * 赎回份额
     */
    private BigDecimal sellVol; 
    /**
     * 无纸化确认状态
     */
    private String nopaperState;
    /**
     * 使用状态
     */
    private String useFlag;
    /**
     * 过期状态
     */
    private String dueFlag;

    /**
     * 需双录标识：0-无需双录；1-需双录;
     */
    private String doubleNeedFlag;
    /**
     * 处理标识：0-无需处理；1-未处理；2-已处理
     */
    private String doubleHandleFlag;
    /**
     * 双录处理时间，带时间yyyyMMddHH24miss
     */
    private Date doubleHandleDt;
    /**
     * 是否首次实缴预约 0-否 1-是
     */
    private String firstPreId;


    public String getNopaperState() {
        return nopaperState;
    }

    public void setNopaperState(String nopaperState) {
        this.nopaperState = nopaperState;
    }

    public String getUseFlag() {
        return useFlag;
    }

    public void setUseFlag(String useFlag) {
        this.useFlag = useFlag;
    }

    public String getDueFlag() {
        return dueFlag;
    }

    public void setDueFlag(String dueFlag) {
        this.dueFlag = dueFlag;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo == null ? null : serialNo.trim();
    }

    public String getInvestType() {
        return investType;
    }

    public void setInvestType(String investType) {
        this.investType = investType == null ? null : investType.trim();
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo == null ? null : hboneNo.trim();
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo == null ? null : custNo.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType == null ? null : idType.trim();
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo == null ? null : idNo.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getValidOfTerm() {
        return validOfTerm;
    }

    public void setValidOfTerm(String validOfTerm) {
        this.validOfTerm = validOfTerm == null ? null : validOfTerm.trim();
    }

    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public Date getBirth() {
        return birth;
    }

    public void setBirth(Date birth) {
        this.birth = birth;
    }

    public String getCustBankId() {
        return custBankId;
    }

    public void setCustBankId(String custBankId) {
        this.custBankId = custBankId == null ? null : custBankId.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo == null ? null : bankNo.trim();
    }

    public String getBranchBankName() {
        return branchBankName;
    }

    public void setBranchBankName(String branchBankName) {
        this.branchBankName = branchBankName == null ? null : branchBankName.trim();
    }

    public String getAccountHolder() {
        return accountHolder;
    }

    public void setAccountHolder(String accountHolder) {
        this.accountHolder = accountHolder == null ? null : accountHolder.trim();
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType == null ? null : bankType.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getDefaultAccount() {
        return defaultAccount;
    }

    public void setDefaultAccount(String defaultAccount) {
        this.defaultAccount = defaultAccount == null ? null : defaultAccount.trim();
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType == null ? null : fundType.trim();
    }

    public String getJjdm() {
        return jjdm;
    }

    public void setJjdm(String jjdm) {
        this.jjdm = jjdm == null ? null : jjdm.trim();
    }

    public String getJjjc() {
        return jjjc;
    }

    public void setJjjc(String jjjc) {
        this.jjjc = jjjc == null ? null : jjjc.trim();
    }

    public String getBookType() {
        return bookType;
    }

    public void setBookType(String bookType) {
        this.bookType = bookType == null ? null : bookType.trim();
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getBookAmount() {
        return bookAmount;
    }

    public void setBookAmount(BigDecimal bookAmount) {
        this.bookAmount = bookAmount;
    }

    public BigDecimal getBookRatio() {
        return bookRatio;
    }

    public void setBookRatio(BigDecimal bookRatio) {
        this.bookRatio = bookRatio;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(String confirmStatus) {
        this.confirmStatus = confirmStatus == null ? null : confirmStatus.trim();
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus == null ? null : currentStatus.trim();
    }

    public String getTargetStatus() {
        return targetStatus;
    }

    public void setTargetStatus(String targetStatus) {
        this.targetStatus = targetStatus == null ? null : targetStatus.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Boolean getIsSignFlag() {
        return isSignFlag;
    }

    public void setIsSignFlag(Boolean isSignFlag) {
        this.isSignFlag = isSignFlag;
    }

    public String getFreSendBill() {
        return freSendBill;
    }

    public void setFreSendBill(String freSendBill) {
        this.freSendBill = freSendBill;
    }

    public String getProsession() {
        return prosession;
    }

    public void setProsession(String prosession) {
        this.prosession = prosession;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getIncomeOfFamily() {
        return incomeOfFamily;
    }

    public void setIncomeOfFamily(String incomeOfFamily) {
        this.incomeOfFamily = incomeOfFamily;
    }

    public String getBankProv() {
        return bankProv;
    }

    public void setBankProv(String bankProv) {
        this.bankProv = bankProv;
    }

    public String getBankCity() {
        return bankCity;
    }

    public void setBankCity(String bankCity) {
        this.bankCity = bankCity;
    }

    public PersonInfoVO getContract() {
        return contract;
    }

    public void setContract(PersonInfoVO contract) {
        this.contract = contract;
    }

    public PersonInfoVO getLegal() {
        return legal;
    }

    public void setLegal(PersonInfoVO legal) {
        this.legal = legal;
    }

    public PersonInfoVO getProxy() {
        return proxy;
    }

    public void setProxy(PersonInfoVO proxy) {
        this.proxy = proxy;
    }

    public BigDecimal getBaseFeeRate() {
        return baseFeeRate;
    }

    public void setBaseFeeRate(BigDecimal baseFeeRate) {
        this.baseFeeRate = baseFeeRate;
    }

    public String getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(String buyStatus) {
        this.buyStatus = buyStatus;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCnapsNo() {
        return cnapsNo;
    }

    public void setCnapsNo(String cnapsNo) {
        this.cnapsNo = cnapsNo;
    }

    public BigDecimal getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(BigDecimal tradeFee) {
        this.tradeFee = tradeFee;
    }

    public String getPrebookDt() {
        return prebookDt;
    }

    public void setPrebookDt(String prebookDt) {
        this.prebookDt = prebookDt;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }

    public String getOrgProperty() {
        return orgProperty;
    }

    public void setOrgProperty(String orgProperty) {
        this.orgProperty = orgProperty;
    }

    public String getAptitude() {
        return aptitude;
    }

    public void setAptitude(String aptitude) {
        this.aptitude = aptitude;
    }

    public String getActualController() {
        return actualController;
    }

    public void setActualController(String actualController) {
        this.actualController = actualController;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public BigDecimal getSellVol() {
        return sellVol;
    }

    public void setSellVol(BigDecimal sellVol) {
        this.sellVol = sellVol;
    }


    public String getDoubleNeedFlag() {
        return doubleNeedFlag;
    }

    public void setDoubleNeedFlag(String doubleNeedFlag) {
        this.doubleNeedFlag = doubleNeedFlag;
    }

    public String getDoubleHandleFlag() {
        return doubleHandleFlag;
    }

    public void setDoubleHandleFlag(String doubleHandleFlag) {
        this.doubleHandleFlag = doubleHandleFlag;
    }

    public Date getDoubleHandleDt() {
        return doubleHandleDt;
    }

    public void setDoubleHandleDt(Date doubleHandleDt) {
        this.doubleHandleDt = doubleHandleDt;
    }

    public String getFirstPreId() {
        return firstPreId;
    }

    public void setFirstPreId(String firstPreId) {
        this.firstPreId = firstPreId;
    }

    public static class PersonInfoVO implements Serializable {
        private static final long serialVersionUID = -4725222090493211381L;
        private String name;
        private String idType;
        private String idNo;
        private String mobile;
        private String validOfTerm;
        private Date validDate;
        private String sex;
        private Date birth;

        public String getName() {
            return this.name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIdType() {
            return this.idType;
        }

        public void setIdType(String idType) {
            this.idType = idType;
        }

        public String getIdNo() {
            return this.idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getMobile() {
            return this.mobile;
        }

        public void setMobile(String mobile) {
            this.mobile = mobile;
        }

        public String getValidOfTerm() {
            return this.validOfTerm;
        }

        public void setValidOfTerm(String validOfTerm) {
            this.validOfTerm = validOfTerm;
        }

        public Date getValidDate() {
            return this.validDate;
        }

        public void setValidDate(Date validDate) {
            this.validDate = validDate;
        }

        public String getSex() {
            return this.sex;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public Date getBirth() {
            return this.birth;
        }

        public void setBirth(Date birth) {
            this.birth = birth;
        }
    }
}
