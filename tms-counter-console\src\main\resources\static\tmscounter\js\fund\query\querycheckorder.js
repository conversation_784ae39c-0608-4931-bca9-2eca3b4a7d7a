/**
*查询柜台复核订单
*<AUTHOR>
*@date 2017-04-11 16:17
*/

var QueryCheckOrder ={
		queryCheckOrderById:function(dealAppNo, callBack){
			var  uri= TmsCounterConfig.QUERY_CHECK_ORDER_BY_ID_URL  ||  {};
			var reqparamters = {};
			reqparamters.dealAppNo = dealAppNo;
			reqparamters.pageNum = 1;
			reqparamters.pageSize = 100;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
			CommonUtil.ajaxAndCallBack(paramters, callBack);
		},	
		queryCheckOrderList:function(custNo){
			
		},
		queryCheckOrderListBack:function(data){
			
		},
		
		queryMergeTransCheckOrderById:function(dealAppNo, callBack, checkNode,txCode){
			var  uri= TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_BY_ID_URL  ||  {};
			var reqparamters = {};
			reqparamters.dealAppNo = dealAppNo;
			reqparamters.pageNum = 1;
			reqparamters.pageSize = 100;
			reqparamters.checkNode = checkNode;
			reqparamters.txCode = txCode;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
			CommonUtil.ajaxAndCallBack(paramters, callBack);
		},	
		
		queryMergeTransTradeOrderById:function(dealNo, dealAppNo, protocolType, disCode, callBack, checkNode){
			var  uri= TmsCounterConfig.QUERY_MERGE_TRANS_TRADE_ORDER_BY_ID_URL  ||  {};
			var reqparamters = {};
			reqparamters.dealNo = dealNo;
			reqparamters.dealAppNo = dealAppNo;
			reqparamters.protocolType = protocolType;
			reqparamters.disCode = disCode;
			reqparamters.pageNum = 1;
			reqparamters.pageSize = 100;
			reqparamters.checkNode = checkNode;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
			CommonUtil.ajaxAndCallBack(paramters, callBack);
		},
		
		
		querySubmitAppOrderById:function(dealAppNo, callBack){
			var  uri= TmsCounterConfig.QUERY_SUBMIT_APP_ORDER_TRADE_BY_ID_URL || {};
			var reqparamters = {};
			reqparamters.dealAppNo = dealAppNo;
			reqparamters.page = 1;
			reqparamters.pageSize = 3;

			var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
			CommonUtil.ajaxPaging(uri, paramters, callBack, "pageView1");
		}
};
