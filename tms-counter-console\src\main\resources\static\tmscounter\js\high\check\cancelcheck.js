/**
*撤单审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
    if('crm' == CommonUtil.getParam("source")){
        viewType = '1';
    }
	
	//初始化按钮
	CounterCheck.initBtn(viewType, CancelCheck.checkOrder);
	
	var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
	$(".selectconsCode").html(selectConsCodesHtml);

    // 查询订单
    CounterCheck.queryCounterDealOrder(viewType, CancelCheck.queryCounterDealOrderCallBack, null);
});

var CancelCheck = {
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		var counterOrder = bodyData.counterOrder || {};
		var sourceDealOrder = counterOrder.sourceDealOrderBean || {};//原订单
		CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};//柜台订单
		var orderFile = bodyData.orderFile || {};// CRM线上资料
		
		var checkOrder =CounterCheck.counterOrderDto || {};//柜台订单信息
		var custInfofiList = bodyData.custInfofiList || [];//客户信息
		CancelCheck.checkOrder = checkOrder;
		
		CancelCheck.buildDealInfo(checkOrder, sourceDealOrder);//订单信息
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		OnLineOrderFile.buildOrderFileHtml(orderFile);

		var paymentTypeName = CommonUtil.getMapValue(CONSTANTS.PAYMENT_TYPE_MAP,sourceDealOrder.paymentType);
		$("#paymentTypeId").html(paymentTypeName);

		var refundInfo = counterOrder.refundBean;
		// 显示回可用配置项
		CancelCheck.refundDirectionOnChange(sourceDealOrder, refundInfo);
	},
	
	/**
     * 订单信息
     * @param checkOrder 柜台审核订单
     * @param sourceDealOrder 原订单
     */
    buildDealInfo:function(checkOrder,sourceDealOrder){
		var trList = [];
		var selectUserCancelFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.USER_CANCEL_FLAG_MAP, checkOrder.forceCancelFlag);
		var selectCancelHtml = '<span class="select-box">'+
									'<select name="userCancelFlag" class="select selectUserCancelFlag" readonly="readonly">'+
										selectUserCancelFlagHtml+
									  '</select>'+
							     '</span>';
		//撤单信息
		trList.push(selectCancelHtml);//撤单方式
		trList.push(CommonUtil.formatData(checkOrder.dealNo));//原订单号
		trList.push(CommonUtil.formatData(checkOrder.fundCode));//基金代码
		trList.push(CommonUtil.formatData(checkOrder.fundName));//基金名称
		trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME_MAP,sourceDealOrder.mBusiCode,'--'));//中台业务码
		//原订单信息
		trList.push(CommonUtil.formatData(sourceDealOrder.appAmt,''));//申请金额
		trList.push(CommonUtil.formatData(sourceDealOrder.appVol,''));//申请份额
		trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP,sourceDealOrder.orderStatus));//订单状态
		trList.push(CommonUtil.getMapValue(CONSTANTS.PAY_STATUS_MAP,sourceDealOrder.payStatus));//支付状态
		trList.push(CommonUtil.formatDateToStr(sourceDealOrder.appDtm, 'yyyy-MM-dd hh:mm:ss'));//订单申请日期
		trList.push(CommonUtil.formatData(sourceDealOrder.submitTaDt));// 上报TA日期
		var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>')+'</td></tr>';
		$("#rsList").append(trHtml);
		
		// 撤单原因
		$("#cancelMemo").val(checkOrder.cancelMemo);

    },

	/**
	 * 回可用备注选择
	 * @param redeemDirection
	 */
	refundDirectionOnChange: function(sourceDealOrder,refundInfo) {
		var payType = sourceDealOrder.paymentType || '';
		if ("01" == payType) {
			$("#refundTableId").html(CancelCheck.generateCancelFormTable("refundFormId"));
			//回款方向
			var withdrawDirectionHtml = CommonUtil.getMapValue(CONSTANTS.WITHDRAW_DIR_ALL_MAP, CancelCheck.checkOrder.withdrawDirection);
			$("#withdrawDirection").html(withdrawDirectionHtml);
			if(CancelCheck.checkOrder.withdrawDirection == 5 || CancelCheck.checkOrder.withdrawDirection == 6 ||CancelCheck.checkOrder.withdrawDirection == 7){
				$("#refundTableId").after(CancelCheck.finaAvailInfo("finaAvailId"))
				// 回可用备注
				$("#refundMemo").html(refundInfo.refundFinaAvailMemo);
				$("#refundFinaAvailAmt").html(refundInfo.refundFinaAvailAmt);
			}

		}else {
			$("#refundTableId").html("");
		}
	},

	generateCancelFormTable:function(sellFormTableId) {
		var table = '<tbody id="'+sellFormTableId+'">' +
			'				<tr class="text-c">' +
			'                <td>回款方向</td>' +
			'                <td>' +
			'					<span class="select-box inline" id="withdrawDirection">' +
			'					</span>'+
			'				 </td>'+
			'        </tbody>';
		return table;
	},

	finaAvailInfo:function(finaAvailId) {
		var table = '<tbody id="'+finaAvailId+'">' +
			'              <tr class="text-c">' +
			'                <td>回可用余额备注</td>' +
			'                <td class="readText availVol"><span class="select-box inline" id="refundMemo"></span></td>' +
			'			   </tr>' +
			'              <tr class="text-c">' +
			'                <td>回可用余额金额</td>' +
			'                <td>' +
			'					<span class="select-box inline" id="refundFinaAvailAmt"></span>'+
			'				 </td>' +
			'              </tr>' +
			'        </tbody>';
		return table;
	},
    
};
