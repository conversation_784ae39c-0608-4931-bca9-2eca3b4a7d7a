/**
 *赎回
 *<AUTHOR>
 *@date 2017-04-01 15:12
 */
$(function () {
    Init.init();
    SellFund.init();
    CommonUtil.getWorkDay();
    SellFund.custHold = {};
    SellFund.isAdviser = false;
    SellFund.jointOpenDayRegion = "";
    SellFund.endTm = "";

});

var SellFund = {
    init: function () {
        // 初始录入订单信息
        SellFund.initRedeemOrderInfoTable(SellFund.isAdviser);

        $("#confimSellBtn").on('click', function () {
            if (SellFund.validateFund(SellFund.jointOpenDayRegion)){
                SellFund.confirm();
            }
        });

        // 查询客户信息
        $("#queryCustInfoBtn").on('click', function () {
            // SellFund.initRedeemOrderInfoTable(SellFund.isAdviser);
            QueryCustInfo.queryCustInfo();
        });

        $("#custNo").on('dblclick', function () {
            QueryCustInfoSubPage.selectCustNo($(this));
        });

    },


    /***
     * 确认赎回
     */
    confirm: function (dealAppNo) {

        var selectedCheckboxs = $("#sellConfirmForm").find("input[type='checkbox'][name='checkRedeemFund']:checked");

        CommonUtil.disabledBtn("confimSellBtn");

        if (selectedCheckboxs.length <= 0) {
            CommonUtil.layer_tip("请选择要赎回的订单");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }

        // 选择的赎回基金信息list
        var sellRedeemFunds = SellFund.selectRedeemFundInfos(selectedCheckboxs);

        if (SellFund.isAdviser) {
            $(sellRedeemFunds).each(function (index, obj) {
                var redeemRatio = obj.redeemRatio;
                var selIndex = obj.selIndex;
                var redeemStatus = obj.redeemStatus;
                // 校验提交的赎回申请份额是否为空
                if (isEmpty(redeemRatio)) {
                    CommonUtil.layer_tip("赎回比例不能为空");
                    $("#redeemRatio_" + selIndex).css("border-color", "red");
                    CommonUtil.enabledBtn("confimSellBtn");
                    return false;
                }
                //校验开放赎回日期是否大于当前日期
                if (redeemStatus != '1') {
                    CommonUtil.layer_tip("该投顾不可赎回，请重新选择");
                    CommonUtil.enabledBtn("confimSellBtn");
                    return false;
                }
            });
        }else {
            $(".appVolClass").css("border-color", "");
            $(sellRedeemFunds).each(function (index, obj) {
                var appVol = obj.appVol;
                var openRedeDt = obj.openRedeDt;
                var selIndex = obj.selIndex;
                // 校验提交的赎回申请份额是否为空
                if (isEmpty(appVol)) {
                    CommonUtil.layer_tip("申请份额不能为空");
                    $("#appVol_" + selIndex).css("border-color", "red");
                    CommonUtil.enabledBtn("confimSellBtn");
                    return false;
                }
                //校验开放赎回日期是否大于当前日期
                if ((openRedeDt != '--' || openRedeDt != '') && openRedeDt > $("#appDt").val()) {
                    CommonUtil.layer_tip("该基金份额未到期，不可赎回，请重新选择");
                    CommonUtil.enabledBtn("confimSellBtn");
                    return false;
                }
            });
        }


        // 校验其他录入信息
        var transactorInfoForm = $("#transactorInfoForm").serializeObject();
        transactorInfoForm.appDtm = transactorInfoForm.appDt + '' + transactorInfoForm.appTm;
        if (CommonUtil.isEmpty(transactorInfoForm.appTm)) {
            CommonUtil.layer_tip("请输入赎回下单时间");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }
        if (!Valid.valiadTradeTime(transactorInfoForm.appTm)) {
            CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }

        if (!Validate.validateTransactorInfo(transactorInfoForm, QueryCustInfo.custInfo)) {
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        }

        var dealAppNo = "";
        if (!(typeof ApplySell == "undefined")) {
            dealAppNo = ApplySell.checkOrder.dealAppNo;
        }

        layer.confirm('确定提交吗？', {
            btn: ['确定', '取消']
        }, function (index) {

            if (SellFund.isAdviser) {
                layerall_close();
                SellFund.confirmAdviserSubmit(dealAppNo, sellRedeemFunds, transactorInfoForm);

            }else {
                // 校验当前TA交易日是否有确认份额是否继续赎回
                var uri = TmsCounterConfig.SELL_FUND_TADTACKVOL_VALIDATE_URL || {};
                var reqparamters = {
                    "sellRedeemFunds": JSON.stringify(sellRedeemFunds),
                    "custInfoForm": JSON.stringify(QueryCustInfo.custInfo)
                };

                //console.log(reqparamters);
                var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
                CommonUtil.ajaxAndCallBack(paramters, function (data) {
                    layerall_close();

                    if (CommonUtil.isArray(data) && data.length > 0) {

                        var retMsg = "";
                        $(data).each(function (index, element) {

                            var ackVol = element.desc || '0';
                            var bodyData = element.body || {};
                            var reqVdlDto = bodyData.reqVdlDto || {};

                            retMsg += "基金: " + reqVdlDto.fundCode + ", 申请赎回: " + reqVdlDto.appVol + "份, 当前TA交易日已经存在确认份额：" + ackVol + "份。<br>";

                        });
                        retMsg = retMsg + "<br>是否继续赎回？";
                        // 否继续赎回
                        layer.confirm(retMsg, {
                            btn: ['是', '否']
                        }, function (index) {
                            layerall_close();
                            SellFund.confirmSubmit(dealAppNo, sellRedeemFunds, transactorInfoForm);

                        }, function () {
                            layerall_close();
                        });

                    } else {
                        SellFund.confirmSubmit(dealAppNo, sellRedeemFunds, transactorInfoForm);
                    }
                });
            }



        }, function () {
            layerall_close();
        });
    },

    /**
     * 确认提交
     * @param dealAppNo
     * @param sellRedeemFunds
     * @param transactorInfoForm
     */
    confirmSubmit: function (dealAppNo, sellRedeemFunds, transactorInfoForm) {

        var uri = TmsCounterConfig.SELL_FUND_CONFIRM_URL || {};
        var reqparamters = {
            "dealAppNo": dealAppNo,
            "sellRedeemFunds": JSON.stringify(sellRedeemFunds),
            "custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
            "transactorInfoForm": JSON.stringify(transactorInfoForm)
        };
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.callBack);
    },

    callBack: function (data) {
        var retMsg = "";
        if (data == null) {
            retMsg = "提交失败。";

        } else {
            if (!CommonUtil.isSucc(data.code) && !CommonUtil.isArray(data)) {
                retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
            }
            if (CommonUtil.isArray(data) && data.length > 0) {
                retMsg = "";
                $(data).each(function (index, element) {
                    //console.log(element);
                    var respCode = element.code || '';
                    var respDesc = element.desc || '';
                    var bodyData = element.body || {};
                    var responseDto = bodyData.responseDto || {};
                    var requestDto = bodyData.requestDto || {};

                    retMsg += "基金: " + requestDto.fundCode + ", 申请赎回份额: " + requestDto.appVol + "份, 赎回提交" + respDesc + "。<br>";
                });
            }
        }

        //CommonUtil.layer_tip(retMsg, 3000);
        layer.open({
            type: 1,
            skin: 'layui-layer-rim', //加上边框
            area: ['400px', '150px'], //宽高
            content: '<div style="text-align: center;margin: 10px 10px;">' + retMsg + '</div>'
        });

        if ($(".confimBtn").length > 0) {
            CommonUtil.disabledBtnWithClass("confimBtn");
            CommonUtil.disabledBtn("abolishBtn");
        }

    },



    /**
     * 确认提交
     * @param dealAppNo
     * @param sellRedeemFunds
     * @param transactorInfoForm
     */
    confirmAdviserSubmit: function (dealAppNo, sellRedeemFunds, transactorInfoForm) {

        var uri = TmsCounterConfig.SELL_ADVISER_CONFIRM_URL || {};
        var reqparamters = {
            "dealAppNo": dealAppNo,
            "sellRedeemFunds": JSON.stringify(sellRedeemFunds),
            "custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
            "transactorInfoForm": JSON.stringify(transactorInfoForm)
        };
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.adviserCallBack);
    },

    adviserCallBack: function (data) {
        var retMsg = "";
        if (data == null) {
            retMsg = "提交失败。";

        } else {
            if (!CommonUtil.isSucc(data.code) && !CommonUtil.isArray(data)) {
                retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
            }
            if (CommonUtil.isArray(data) && data.length > 0) {
                retMsg = "";
                $(data).each(function (index, element) {
                    //console.log(element);
                    var respCode = element.code || '';
                    var respDesc = element.desc || '';
                    var bodyData = element.body || {};
                    var responseDto = bodyData.responseDto || {};
                    var requestDto = bodyData.requestDto || {};

                    retMsg += "产品: " + requestDto.fundCode + ", 申请赎回比例: " + requestDto.redeemRatio + "%, 赎回提交" + respDesc + "。<br>";
                });
            }
        }

        //CommonUtil.layer_tip(retMsg, 3000);
        layer.open({
            type: 1,
            skin: 'layui-layer-rim', //加上边框
            area: ['400px', '150px'], //宽高
            content: '<div style="text-align: center;margin: 10px 10px;">' + retMsg + '</div>'
        });

        if ($(".confimBtn").length > 0) {
            CommonUtil.disabledBtnWithClass("confimBtn");
            CommonUtil.disabledBtn("abolishBtn");
        }

    },

    /**
     * 零售查询客户持仓
     */
    queryCustHodlInfo: function (fundCode) {

        var custNo = QueryCustInfo.custInfo.custNo || '';
        var disCode = QueryCustInfo.custInfo.disCode || '';
        console.log("select custNo: "+ custNo + " disCode: " + disCode + "fundCode: "+fundCode + " isAdviser:" + SellFund.isAdviser);

        // if (isEmpty(custNo)) {
        //     CommonUtil.layer_tip("请先选择用户");
        //     return false;
        // }
        // if (isEmpty(fundCode)) {
        //     CommonUtil.layer_tip("请输入赎回的基金代码");
        //     return false;
        // }
        var uri;
        if (SellFund.isAdviser) {
            uri = TmsCounterConfig.QUERY_ADVISER_REDEEM_INFO_URL || {};
        }else {
            uri = TmsCounterConfig.FUND_QUERY_FUND_REDEEM_INFO_URL || {};
        }

        var reqparamters = {"fundCode": fundCode, "custNo": custNo, "disCode": disCode};
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
        CommonUtil.ajaxAndCallBack(paramters, SellFund.queryCustHoldFundInfoCallBack);
    },

    redeemCapitalChange : function (val,index){
        if(val === '1' ){
            $("#redeemCapitalFlag_"+index).attr("disabled","disabled");
        }else{
            $("#redeemCapitalFlag_"+index).removeAttr("disabled");
        }
    },
    /**
     * 处理基金持仓信息
     */
    queryCustHoldFundInfoCallBack: function (data) {
        console.log("SellFund.queryCustHoldFundInfoCallBack-------" + JSON.stringify(data));
        var bodyData = data.body || {};
        SellFund.dtlList = bodyData.balanceDtlList || [];


        if (SellFund.dtlList == null || SellFund.dtlList.length <= 0) {
            CommonUtil.layer_tip("没有查询到持仓信息");

        } else {
            // 组装客户购买基金持仓列表
            var redeemFundInfoList = [];
            $(SellFund.dtlList).each(function (index, element) {
                var holdInfo = {};
                holdInfo.fundCode = element.productCode;
                holdInfo.fundStatus = element.productStatus;
                holdInfo.fundName = element.productName;
                holdInfo.redeemStatus = element.redeemStatus;

                holdInfo.cpAcctNo = element.cpAcctNo;
                holdInfo.bankCode = element.bankCode;//银行代码
                holdInfo.bankName = element.bankName;//银行名称
                holdInfo.bankAcctNo = element.bankAcctNo;//银行卡号
                holdInfo.unconfirmedVol = element.unconfirmedVol;//冻结份额(待确认份额+申请份额)
                holdInfo.availVol = element.availVol;//可用份额
                holdInfo.protocolNo = element.protocolNo;//协议号
                holdInfo.protocolType = element.protocolType;//协议类型

                holdInfo.allRedeemFlag = element.allRedeemFlag;//是否可以全赎：1:是(可以);0:否(不可以);

                holdInfo.openRedeDt = element.openRedeDt;//开放赎回日期

                redeemFundInfoList.push(holdInfo);
            });

            $("#redeemOrderInfoId").empty();

            // 重新渲染列表
            $(redeemFundInfoList).each(function (index, element) {
                if (SellFund.isAdviser) {
                    SellFund.showAdviserRedeemTable(index, element, bodyData);
                }else {
                    SellFund.showFundRedeemTable(index, element, bodyData);
                }

            });
            // 绑定icon点击事件
            $(".redeemCapital").on('change', function () {
                var val = $(this).val();
                var idstr = $(this).attr("id") ;
                var index = idstr.substring(idstr.lastIndexOf("_")+1,idstr.length);
                SellFund.redeemCapitalChange(val,index);
            });
        }
    },

    showFundRedeemTable:function (index, element, bodyData) {
        var redeemDirectionIsSupCardFlag = bodyData.redeemDirectionIsSupCardFlag;
        var redeemDirectionIsSupCxgFlag = bodyData.redeemDirectionIsSupCxgFlag;


        var hiddenHTML = '';
        hiddenHTML += '<input type="hidden" id="cpAcctNo_' + index + '" name="cpAcctNo_' + index + '" value="' + element.cpAcctNo + '">';
        hiddenHTML += '<input type="hidden" id="bankAcct_' + index + '" name="bankAcct_' + index + '" value="' + element.bankAcctNo + '">';
        hiddenHTML += '<input type="hidden" id="bankCode_' + index + '" name="bankCode_' + index + '" value="' + element.bankCode + '">';
        hiddenHTML += '<input type="hidden" id="protocolNo_' + index + '" name="protocolNo_' + index + '" value="' + element.protocolNo + '">';
        hiddenHTML += '<input type="hidden" id="protocolType_' + index + '" name="protocolType_' + index + '" value="' + element.protocolType + '">';

        hiddenHTML += '<input type="hidden" id="openRedeDt_' + index + '" name="openRedeDt_' + index + '" value="' + CommonUtil.formatData(element.openRedeDt) + '">';

        var tdList = [];
        tdList.push('<td><input class="selectRedeemFund" name="checkRedeemFund" type="checkbox" data-index="' + index + '"></input>' + hiddenHTML + '</td>');
        tdList.push('<td><input id="fundCode_' + index + '" name="fundCode_' + index + '" type="text" value="' + element.fundCode + '" readonly="true"></td>');
        tdList.push('<td>' + element.fundName + '</td>');
        tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_STATE, element.fundStatus) + '</td>');
        tdList.push('<td><input type="text" value="' + element.bankName + ' ' + element.bankAcctNo + '" readonly="true"></td>');
        tdList.push('<td>' + CommonUtil.formatAmount(element.unconfirmedVol) + '</td>');
        tdList.push('<td>' + CommonUtil.formatAmount(element.availVol) + '</td>');
        // 可赎回日期可编辑
        var openRedeDt = '--';
        if (!CommonUtil.isEmpty(element.openRedeDt) && element.openRedeDt > $("#appDt").val()) {
            openRedeDt = element.openRedeDt;
        }
        tdList.push('<td>' + openRedeDt + '</td>');
        if (element.allRedeemFlag == CONSTANTS.IS_ALL_REDEEM) {// 可以全赎

            // onmouseover or onclick
            var toolTipIcon = '';
            toolTipIcon += '<div style="width: 16px;height:16px;border-radius:10px;border:1px solid #5a98de;margin-right:10px;display: inline-block;" onclick="layer.tips(' + "'" + "免责申明：选择全赎时，当日若有分红收益下发，好买将尝试帮您一并赎回，但不承诺收益部分一定赎回成功。" + "'" + ', this, {tips: [1, ' + "'" + "#5a98de" + "'" + '], time:3000});">';
            toolTipIcon += '<span style="height: 16px;line-height: 16px;display:block;color: #5a98de;font-size: 12px;text-align:center;">?</span>';
            toolTipIcon += '</div>';

            var allRedeemCheckBox = '<input class="selectAllRedeem" name="allRedeemFlag" id="allRedeemFlag_' + index + '" type="checkbox" style="margin-top: 0px;margin-left: 5px;" onclick="SellFund.selectedAllRedeem(' + index + ',' + element.availVol + ', this);"><span style="color: #5a98de;">&nbsp;全赎</span></input>';

            tdList.push('<td>' + toolTipIcon + '<input type="text" class="appVolClass" name="appVol_' + index + '" id="appVol_' + index + '" isnull="false" datatype="s" errormsg="申请份额" placeholder="请输入" onkeyup="SellFund.validatorAppVol(' + index + ',' + element.availVol + ', this);">' + allRedeemCheckBox + '</td>');

        } else {
            tdList.push('<td><input type="text" class="appVolClass" name="appVol_' + index + '" id="appVol_' + index + '" isnull="false" datatype="s" errormsg="申请份额" placeholder="请输入" onkeyup="SellFund.validatorAppVol(' + index + ',' + element.availVol + ', this);"></td>');
        }
        tdList.push('<td><input type="text" name="appVolCapitalForSell_' + index + '" id="appVolCapitalForSell_' + index + '" isnull="false" datatype="s" errormsg="申请份额" readonly="true"></td>');
        tdList.push('<td>' + element.protocolNo + '</td>');
        tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType) + '</td>');
        // 巨额赎回顺延标记
        tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag_' + index + '" id="largeRedmFlag_' + index + '" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option><option selected="selected" value="1">顺延</option></select></span></td>');
        // 赎回不出款标记
        tdList.push('<td><span class="select-box inline"><select   name="unusualTransType_' + index + '" id="unusualTransType_' + index + '" class="select redeemCapital" isnull="false" datatype="s" errormsg="异常交易标识"><option value="0" selected="selected">否</option><option value="1">是</option></select></span></td>');

        // 交易回款方式
        var redeemCapitalOptionsHtml = Redemption.buildSelectRedeemHtml(QueryCustInfo.custInfo.collectProtocolMethod, QueryCustInfo.custInfo.invstType, redeemDirectionIsSupCardFlag, redeemDirectionIsSupCxgFlag);
        tdList.push('<td><span class="select-box inline"><select    name="redeemCapitalFlag_' + index + '" id="redeemCapitalFlag_' + index + '" class="select" isnull="false" datatype="s" errormsg="交易回款方式">' + redeemCapitalOptionsHtml + '</select></span>');

        var trAppendHtml = '<tr class="text-c" id="redeemInfo_tr_' + index + '">' + tdList.join() + '</tr>';
        $("#redeemOrderInfoId").append(trAppendHtml);
    },

    showAdviserRedeemTable:function (index, element, bodyData) {
        var redeemDirectionIsSupCardFlag = bodyData.redeemDirectionIsSupCardFlag;
        var redeemDirectionIsSupCxgFlag = bodyData.redeemDirectionIsSupCxgFlag;

        var hiddenHTML = '';
        hiddenHTML += '<input type="hidden" id="cpAcctNo_' + index + '" name="cpAcctNo_' + index + '" value="' + element.cpAcctNo + '">';
        hiddenHTML += '<input type="hidden" id="bankAcct_' + index + '" name="bankAcct_' + index + '" value="' + element.bankAcctNo + '">';
        hiddenHTML += '<input type="hidden" id="bankCode_' + index + '" name="bankCode_' + index + '" value="' + element.bankCode + '">';
        hiddenHTML += '<input type="hidden" id="protocolNo_' + index + '" name="protocolNo_' + index + '" value="' + element.protocolNo + '">';
        hiddenHTML += '<input type="hidden" id="protocolType_' + index + '" name="protocolType_' + index + '" value="' + element.protocolType + '">';

        hiddenHTML += '<input type="hidden" id="openRedeDt_' + index + '" name="openRedeDt_' + index + '" value="' + CommonUtil.formatData(element.openRedeDt) + '">';
        hiddenHTML += '<input type="hidden" id="redeemStatus_' + index + '" name="redeemStatus_' + index + '" value="' + element.redeemStatus + '">';

        var tdList = [];
        tdList.push('<td><input class="selectRedeemFund" name="checkRedeemFund" type="checkbox" data-index="' + index + '"></input>' + hiddenHTML + '</td>');
        tdList.push('<td><input id="fundCode_' + index + '" name="fundCode_' + index + '" type="text" value="' + element.fundCode + '" readonly="true"></td>');
        tdList.push('<td>' + element.fundName + '</td>');
        tdList.push('<td>' + (element.redeemStatus == "1" ? '可赎回' : '暂停赎回') + '</td>');
        tdList.push('<td><input type="text" value="' + element.bankName + ' ' + element.bankAcctNo + '" readonly="true"></td>');
        tdList.push('<td>' + CommonUtil.formatAmount(element.unconfirmedVol) + '</td>');
        tdList.push('<td>' + CommonUtil.formatAmount(element.availVol) + '</td>');
        // 可赎回日期可编辑
        var openRedeDt = '--';
        if (!CommonUtil.isEmpty(element.openRedeDt) && element.openRedeDt > $("#appDt").val()) {
            openRedeDt = element.openRedeDt;
        }
        tdList.push('<td>' + openRedeDt + '</td>');
        tdList.push('<td><input type="text" class="redeemRatioClass" name="redeemRatio_' + index + '" id="redeemRatio_' + index + '" isnull="false" datatype="s" errormsg="赎回比例" placeholder="请输入" onkeyup="SellFund.validatorRedeemRatio(this);">%</td>');
        tdList.push('<td>' + element.protocolNo + '</td>');
        tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType) + '</td>');
        // 巨额赎回顺延标记
        tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag_' + index + '" id="largeRedmFlag_' + index + '" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option></select></span></td>');
        // 赎回不出款标记
        tdList.push('<td><span class="select-box inline"><select   name="unusualTransType_' + index + '" id="unusualTransType_' + index + '" class="select redeemCapital" isnull="false" datatype="s" errormsg="异常交易标识"><option value="0" selected="selected">否</option><option value="1">是</option></select></span></td>');

        // 交易回款方式
        var redeemCapitalOptionsHtml = Redemption.buildSelectRedeemHtml(QueryCustInfo.custInfo.collectProtocolMethod, QueryCustInfo.custInfo.invstType, redeemDirectionIsSupCardFlag, redeemDirectionIsSupCxgFlag);
        tdList.push('<td><span class="select-box inline"><select    name="redeemCapitalFlag_' + index + '" id="redeemCapitalFlag_' + index + '" class="select" isnull="false" datatype="s" errormsg="交易回款方式">' + redeemCapitalOptionsHtml + '</select></span>');

        var trAppendHtml = '<tr class="text-c" id="redeemInfo_tr_' + index + '">' + tdList.join() + '</tr>';
        $("#redeemOrderInfoId").append(trAppendHtml);
    },
    /**
     * 获取选择的TR data
     */
    selectRedeemFundInfos: function (selectedCheckboxs) {
        var redeFundList = [];
        $(selectedCheckboxs).each(function (index, obj) {
            var selIndex = $(obj).attr('data-index');
            //console.log(selIndex);

            // 注：属性与CounterRedeemReqDto对应
            var redeFund = {};
            redeFund.selIndex = selIndex;
            redeFund.fundCode = $('#fundCode_' + selIndex).val();
            redeFund.appVol = $('#appVol_' + selIndex).val();
            redeFund.largeRedmFlag = $('#largeRedmFlag_' + selIndex).val();
            redeFund.unusualTransType = $('#unusualTransType_' + selIndex).val();

            redeFund.cpAcctNo = $('#cpAcctNo_' + selIndex).val();
            redeFund.bankAcct = $('#bankAcct_' + selIndex).val();
            redeFund.bankCode = $('#bankCode_' + selIndex).val();

            redeFund.protocolNo = $('#protocolNo_' + selIndex).val();
            redeFund.protocolType = $('#protocolType_' + selIndex).val();

            // 赎回去向 0-回款至银行卡（协议默认），1-回款至储蓄罐（协议默认），2-回款至银行卡（人工选择）,3-回款至储蓄罐（人工选择）
            redeFund.redeemCapitalFlag = $('#redeemCapitalFlag_' + selIndex).val();
            // 开放赎回日期
            redeFund.openRedeDt = CommonUtil.formatData($('#openRedeDt_' + selIndex).val());

            // 全赎购选
            var checkAllRedeemFlag = $("#allRedeemFlag_" + selIndex).is(':checked');
            if (checkAllRedeemFlag) {
                redeFund.allRedeemFlag = CONSTANTS.IS_ALL_REDEEM;
            } else {
                redeFund.allRedeemFlag = CONSTANTS.NOT_ALL_REDEEM;
            }

            // 投顾特有
            redeFund.redeemRatio = $('#redeemRatio_' + selIndex).val();
            redeFund.redeemStatus = $('#redeemStatus_' + selIndex).val();

            redeFundList.push(redeFund);
        });
        //console.log(redeFundList);
        return redeFundList;
    },

    /**
     * 校验输入份额(事件:onkeyup)
     */
    validatorAppVol: function (index, availVol, thisObj) {
        // console.log(availVol + " "+ thisObj.value);
        var appVol = thisObj.value;
        if (!/^[0-9]+\.?[0-9]{0,2}$/.test(appVol)) {
            CommonUtil.layer_tip('只能输入数字且小数点后两位');
            thisObj.value = '';
        }

        var cnAppVol = CommonUtil.digit_uppercase(appVol);
        $("#appVolCapitalForSell_" + index).val(cnAppVol.replace('元', '份'));

        if (appVol > availVol) {
            CommonUtil.layer_tip("申请份额不能大于可用份额");
            $(thisObj).css("border-color", "red");
            CommonUtil.enabledBtn("confimSellBtn");
            return false;
        } else {
            $(thisObj).css("border-color", "");
        }
    },

    /**
     * 校验赎回比例(事件:onkeyup)
     */
    validatorRedeemRatio: function (thisObj) {
        var redeemRatio = thisObj.value;
        //if (!/^[0-9]+$/.test(redeemRatio)) {
        if (!/^[0-9]+\.?[0-9]{0,2}$/.test(redeemRatio)) {
            CommonUtil.layer_tip('只能输入数字且小数点后两位');
            thisObj.value = '';
        }

        if (!(redeemRatio <= 100 && redeemRatio >= 0)) {
            CommonUtil.layer_tip('请输入赎回比例1~100');
            thisObj.value = '';
        }
    },

    /**
     * 选择了全赎触发
     */
    selectedAllRedeem: function (index, availVol, thisObj) {
        var checkFlag = $(thisObj).is(':checked');
        if (checkFlag) {
            $("#sellConfirmForm #appVol_" + index).val(availVol);
            $("#sellConfirmForm #appVol_" + index).attr('readonly', true);
            var cnAppVol = CommonUtil.digit_uppercase(availVol);
            $("#sellConfirmForm #appVolCapitalForSell_" + index).val(cnAppVol.replace('元', '份'));
        } else {
            $("#sellConfirmForm #appVol_" + index).val('');
            $("#sellConfirmForm #appVol_" + index).removeAttr('readonly');
            $("#sellConfirmForm #appVolCapitalForSell_" + index).val('');

        }
    },

    initSearchIcon: function () {
        // 绑定icon点击事件
        $(".searchIcon").on('click', function () {
            var custNo = QueryCustInfo.custInfo.custNo || '';

            if (isEmpty(custNo)) {
                CommonUtil.layer_tip("请先选择用户");
                return false;
            }
            var fundCode = $("#fundCode").val();
            if (isEmpty(fundCode)) {
                CommonUtil.layer_tip("请输入赎回的基金/产品代码");
                return false;
            }

            QueryFundInfo.queryAdviserOrFundProduct(fundCode,function (productInfo){
                var isAdviser = productInfo.adviserFlag || false;
                SellFund.isAdviser = isAdviser;
                SellFund.jointOpenDayRegion = productInfo.jointOpenDayRegion;
                SellFund.endTm = productInfo.endTm;

                console.info("====>" + fundCode + " isAdviser " + SellFund.isAdviser);

                SellFund.initRedeemOrderInfoTable(SellFund.isAdviser);

                SellFund.queryCustHodlInfo(fundCode);

            });
        });
    },
    /**
     * 初始化赎回订单信息Table
     */
    initRedeemOrderInfoTable: function (isAdviser) {
        if (isAdviser) {
            SellFund.initRedeemAdviserTable();
        }else {
            SellFund.initRedeemFundTable();
        }
        SellFund.initSearchIcon();
    },

    initRedeemFundTable: function () {
        var trAppendTableHtml =
            '<tr className="text-c">' +
            '<th>选择</th>            '+
            '<th>基金/产品代码</th>'+
            '<th>基金/产品简称</th>'+
            '<th>基金/产品状态</th>'+
            '<th>银行账户</th>'+
            '<th>冻结份额</th>'+
            '<th>当前可用份额</th>'+
            '<th>可赎回日期</th>'+
            '<th>申请份额（份）</th>'+
            '<th>申请份额（大写）</th>'+
            '<th>协议号</th>'+
            '<th>协议类型</th>'+
            '<th>巨额赎回顺延标记</th>'+
            '<th>赎回不出款标记</th>'+
            '<th>交易回款方式</th>'+
            ' </tr>' ;

        $("#redeemOrderTableInfo").empty();
        $("#redeemOrderTableInfo").append(trAppendTableHtml);

        var tdList = [];
        tdList.push('<td>&nbsp;</td>');
        tdList.push('<td><div class="searchIn"><input id="fundCode" type="text" ><a href="javascript:void(0)" class="searchIcon"></a></div></td>');
        tdList.push('<td id="fundName">--</td>');
        tdList.push('<td id="fundStatus">--</td>');
        tdList.push('<td><span class="select-box inline"><select name="cpAcctNo" class="select" id="selectBank"></select></span><input type="hidden" id="bankCode" name="bankCode" value="0"></td>');
        tdList.push('<td id="unconfirmedVol"></td>');//冻结份额(待确认份额+申请份额)
        tdList.push('<td id="availVol"></td>');
        tdList.push('<td id="openRedeDt"></td>');
        tdList.push('<td><input type="text" placeholder="请输入" class="appVolForSell" name="appVol" id="appVol" isnull="false" datatype="s" errormsg="申请份额"></td>');
        tdList.push('<td><input type="text" class="appVolCapitalForSell" name="appVolCapitalForSell" id="appVolCapitalForSell" isnull="false" datatype="s" errormsg="申请份额" readonly="true"></td>');
        tdList.push('<td id="protocolNo">--</td>');
        tdList.push('<td id="protocolType">--</td>');
        // 巨额赎回顺延标记
        tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option><option selected="selected" value="1">顺延</option></select></span></td>');
        // 赎回不出款标记
        tdList.push('<td><span class="select-box inline"><select name="unusualTransType" class="select" isnull="false" datatype="s" errormsg="异常交易标识"><option value="0" selected="selected">否</option><option value="1">是</option></select></span></td>');
        // 交易回款方式
        tdList.push('<td><span class="select-box inline"><select name="redeemCapitalFlag" class="select" isnull="false" datatype="s" errormsg="交易回款方式"></select></span>');


        var trAppendHtml = '<tr class="text-c">' + tdList.join() + '</tr>';
        $("#redeemOrderInfoId").empty();
        $("#redeemOrderInfoId").append(trAppendHtml);

    },

    initRedeemAdviserTable: function () {
        var trAppendTableHtml =
            '<tr className="text-c">' +
            '<th>选择</th>            '+
            '<th>基金/产品代码</th>'+
            '<th>基金/产品简称</th>'+
            '<th>基金/产品状态</th>'+
            '<th>银行账户</th>'+
            '<th>冻结份额</th>'+
            '<th>当前可用份额</th>'+
            '<th>可赎回日期</th>'+
            '<th>赎回比例</th>'+
            '<th>协议号</th>'+
            '<th>协议类型</th>'+
            '<th>巨额赎回顺延标记</th>'+
            '<th>赎回不出款标记</th>'+
            '<th>交易回款方式</th>'+
            ' </tr>' ;

        $("#redeemOrderTableInfo").empty();
        $("#redeemOrderTableInfo").append(trAppendTableHtml);

        var tdList = [];
        tdList.push('<td>&nbsp;</td>');
        tdList.push('<td><div class="searchIn"><input id="fundCode" type="text" ><a href="javascript:void(0)" class="searchIcon"></a></div></td>');
        tdList.push('<td id="fundName">--</td>');
        tdList.push('<td id="fundStatus">--</td>');
        tdList.push('<td><span class="select-box inline"><select name="cpAcctNo" class="select" id="selectBank"></select></span><input type="hidden" id="bankCode" name="bankCode" value="0"></td>');
        tdList.push('<td id="unconfirmedVol"></td>');//冻结份额(待确认份额+申请份额)
        tdList.push('<td id="availVol"></td>');
        tdList.push('<td id="openRedeDt"></td>');
        tdList.push('<td><input type="text" placeholder="请输入" class="redeemRatioForSell" name="redeemRatio" id="redeemRatio" isnull="false" datatype="s" errormsg="赎回比例">%</td>');
        tdList.push('<td id="protocolNo">--</td>');
        tdList.push('<td id="protocolType">--</td>');
        // 巨额赎回顺延标记
        tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option></select></span></td>');
        // 赎回不出款标记
        tdList.push('<td><span class="select-box inline"><select name="unusualTransType" class="select" isnull="false" datatype="s" errormsg="异常交易标识"><option value="0" selected="selected">否</option><option value="1">是</option></select></span></td>');
        // 交易回款方式
        tdList.push('<td><span class="select-box inline"><select name="redeemCapitalFlag" class="select" isnull="false" datatype="s" errormsg="交易回款方式"></select></span>');


        var trAppendHtml = '<tr class="text-c">' + tdList.join() + '</tr>';
        $("#redeemOrderInfoId").empty();
        $("#redeemOrderInfoId").append(trAppendHtml);


    },

    validateFund:function(jointOpenDayRegion){
        var appTm = $("#appTm").val();
        if(appTm === undefined || appTm.length === 0){
            CommonUtil.layer_tip("下单时间不能为空");
            CommonUtil.enabledBtn("confimSellBtn");
            return false
        }

        var flag = appTm >= SellFund.endTm && appTm <= CONSTANTS.HIGH_COUNTER_END_TIME
        if (jointOpenDayRegion !== undefined && jointOpenDayRegion.length !== 0 && flag){
            layer.open({
                title: ['风险提示', true],
                type: 1,
                area: ['320px', 'auto'],
                btn: ['确定', '取消'],
                skin: 'layui-layer-rim',
                btnAlign: 'l',
                content: jointOpenDayRegion,
                yes: function (index, layero) {
                    layer.closeAll();
                    SellFund.confirm();
                },
                cancel: function (index) {
                    CommonUtil.enabledBtn("confimSellBtn");
                }
            });
            return false;
        }
        return true;
    },



};

