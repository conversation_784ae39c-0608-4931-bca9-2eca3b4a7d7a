/**
 * 投顾列表
 */


var ConsCode = {
		
};


ConsCode.consCodes =[];
ConsCode.consCodesMap ={};

ConsCode.getConsCodeSelectHtml=function(){
	var selectConsCodesHtml = '';
	var uri = TmsCounterConfig.QUERY_CONS_CODE_INFO_URL;
	var reqparamters = {};
	var paramters = CommonUtil.buildReqParams(uri,reqparamters);
	CommonUtil.ajaxAndCallBack(paramters, function(data){
		var respCode = data.code || '';
		var body = data.body || {};
		if(CommonUtil.isSucc(respCode)){
			ConsCode.consCodes = body.consCodes || [];
			$(ConsCode.consCodes).each(function(index,element){
				selectConsCodesHtml +='<option  value="'+element.consCode+'">'+element.consName +'</option>';
				ConsCode.consCodesMap[element.consCode] = element.consName;
			});
		}		
	});
	$(".selectconsCode").html(selectConsCodesHtml);
}