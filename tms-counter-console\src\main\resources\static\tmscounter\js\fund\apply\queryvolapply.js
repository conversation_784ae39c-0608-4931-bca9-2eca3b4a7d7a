/**
 *查询份额合并或迁移申请
 *<AUTHOR>
 *@date 2018-05-10 11:23
 */
$(function () {
    Init.init();
    QueryVolApply.init();
    QueryVolApply.custInfo = {};
    QueryVolApply.checkOrders = [];

    var selectTxCodeHtml = '<option value="">全部</option>';
    $.each(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP, function (name, value) {
        selectTxCodeHtml += '<option value="' + name + '">' + value + ' </option>';
    });
    $("#selectTxCode").empty();
    $("#selectTxCode").html(selectTxCodeHtml);
    
    QueryVolApply.operatorNo = CommonUtil.getParam("operatorNo");

});
var QueryVolApply = {
		
		initDetailPoP: function (dealAppNo) {
			var uri = '../../../html/fund/trade/appratetchangedetail.html?operatorNo=';
	        uri = uri + QueryVolApply.operatorNo + '&dealAppNo=' +  dealAppNo;
	        console.info(dealAppNo);
			// POPUP
			layer.open({
				title: ['详情', false],
				type: 2,
				area: ['95%', '90%'],
				skin: 'layui-layer-rim', //加上边框
				btnAlign: 'l',
				content: uri
			});
		},
		
        init: function () {
            $("#queryBtn").on('click', function () {
                QueryVolApply.queryOrderInfo();
            });
            /**
             * 双击客户号查询客户信息
             */
            $("#custNo").on('dblclick',function(){
                QueryCustInfoSubPage.selectCustNo($(this));
            });
        },

        /**
         * 查询待审核订单信息
         */
        queryOrderInfo: function () {
            var uri = TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_URL || {};
            var reqparamters = {};
            var queryOrderConditionForm = $("#queryConditonForm").serializeObject();
            var queryOrderCondition = {};
            $.each(queryOrderConditionForm, function (name, value) {
                if (!CommonUtil.isEmpty(value)) {
                    queryOrderCondition[name] = value;
                }
            });
            reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
            reqparamters.page = 1;
            reqparamters.pageSize = 20;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxPaging(uri, paramters, QueryVolApply.queryOrderInfoCallBack, "pageView");
        },

        queryOrderInfoCallBack: function (data) {
            var bodyData = data;
            QueryVolApply.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];

            var staticData = bodyData.counterQueryOrderRespDto || {};
            $("#staticId").html("当页小计：申请笔数【" + QueryVolApply.checkOrders.length + "】申请份额【" + CommonUtil.formatAmount(staticData.pageAppVol) + "】 合计：申请笔数【" + staticData.totalCount + "】申请份额【" + CommonUtil.formatAmount(staticData.totalAppVol) + "】");

            $("#rsList").empty();
            if (QueryVolApply.checkOrders.length <= 0) {
                var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
                $("#rsList").append(trHtml);
            }

            var i = 1;
            $(QueryVolApply.checkOrders).each(function (index, element) {
                var trList = [];
                trList.push(i++);
                trList.push(CommonUtil.formatData(element.dealAppNo));
                trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
                trList.push(CommonUtil.formatData(element.custNameEncrypt));
                trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP, element.txCode, ''));
                trList.push(CommonUtil.formatData(element.fundCode, '--'));
                trList.push(CommonUtil.formatData(element.fundName, '--'));
                if (element.appVol > 0) {
                    trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
                } else {
                    trList.push('--');
                }
                trList.push(CommonUtil.formatData(element.appDt));
                trList.push(CommonUtil.formatData(element.appTm));
                trList.push('柜台');
                trList.push(CommonUtil.formatData(element.creator, ''));
                trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, element.checkFlag, ''));
                trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_APP_FLAG_MAP, element.appFlag, ''));
                if (element.checkFlag == '1') {
                	if(CONSTANTS.QUERY_SUBMIT_APP_ORDER == element.txCode){
                		trList.push('');
                	} else {
                        trList.push('<a class="reQuery" href="javascript:void(0);" indexvalue = ' + index +  ' checknode='+ '2' +'  style="color: #06c;">查看</a>');
                	}
                } else {
                    trList.push('');
                }

                if(CONSTANTS.QUERY_SUBMIT_APP_ORDER == element.txCode){
                	var dealAppNoShow = '<a type="button" style="color: #06c;" onclick="QueryVolApply.initDetailPoP(\'' + element.dealAppNo + '\')">查看</a>';
                    trList.push(dealAppNoShow);
                	//trList.push('<a href="' + uri + ' " target="_blank" style="color: #06c;" indexvalue = ' + index + ' checknode=' +'1'+'>详情</a>');
                } else {
                	trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = ' + index + ' checknode=' +'1'+'>详情</a>');
                }
                var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                $("#rsList").append(trHtml);
            });

            // 查订单详情
            $(".reQuery").off();
            $(".reQuery").on('click', function () {
                var indexValue = $(this).attr("indexvalue");
                var checkedOrder = QueryVolApply.checkOrders[indexValue] || {};
                QueryVolApply.checkedOrder = checkedOrder;
                var dealNo = checkedOrder.dealNo;
                var dealAppNo = checkedOrder.dealAppNo;
                var protocolType = checkedOrder.protocolType;
                var disCode = checkedOrder.disCode;
                var checkNode = $(this).attr('checkNode');

                QueryCheckOrder.queryMergeTransTradeOrderById(dealNo, dealAppNo, protocolType, disCode, QueryVolApply.queryDealOrderInfo, checkNode);



            });

            // 查申请详情
            $(".reCheck").off();
            $(".reCheck").on('click', function () {

                var indexValue = $(this).attr("indexvalue");
                var checkedOrder = QueryVolApply.checkOrders[indexValue] || {};

                var txCode = checkedOrder.txCode;
                var dealAppNo = checkedOrder.dealAppNo;

                var  checknode = $(this).attr("checknode");

                // 份额合并详情
                if (CONSTANTS.MERGE_VOL_TXCODE == txCode) {
                    QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, QueryVolApply.queryMergeVolOrderByIdBack);
                    // 份额迁移详情
                } else if (CONSTANTS.TRANS_VOL_TXCODE == txCode) {
                    QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, QueryVolApply.queryTransVolOrderByIdBack, checknode);
                } else if(CONSTANTS.ONLINE_TRANS_VOL_TXCODE == txCode){
                    QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, OnlineTransferVol.queryCheckVolOrderByIdBack, checknode, "Z910105");
                } 
            });
        },

        queryBankAcctStat: function () {
            $("#unregisterCard").empty();
            var reqparamters = {};

            reqparamters.bankAcct = QueryVolApply.dtlOrderDtos[0].bankAcct;
            reqparamters.txAcctNo = QueryVolApply.checkedOrder.txAcctNo;

            var uri = TmsCounterConfig.QUERY_BANK_ACCT_STAT || {};

            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, QueryVolApply.queryBankAcctStatCallBack);
        },

        queryBankAcctStatCallBack: function (data) {
            var bodyData = data.body || {};
            var respData = bodyData.batchStatList || [];


            var trList = [];
            trList.push(CommonUtil.formatData(respData.txAcctNo));
            trList.push(CommonUtil.formatData(QueryVolApply.checkedOrder.custName));
            trList.push(CommonUtil.formatData(QueryVolApply.dtlOrderDtos[0].bankAcct));
            trList.push(CommonUtil.getMapValue(CONSTANTS.BANKACCT_STATUS_MAP,respData.bankAcctStatus));

            var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            $("#unregisterCard").append(trAppendHtml);


        },
        queryAssetInfo: function () {
            // 转入银行卡资产信息
            $("#assetRealBody").empty();

            // 查询客户银行卡持仓
            var uri = TmsCounterConfig.QUERY_INTRANSIT_ASSET_URL || {};
            var reqparamters = {};

            reqparamters.cpAcctNo = QueryVolApply.dtlOrderDtos[0].cpAcctNo;


            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, QueryVolApply.queryAssetInfoCallBack);
        },

        queryAssetInfoCallBack: function (data) {
            var bodyData = data.body || {};
            var respData = bodyData.batchStatList || [];

            QueryVolApply.assetDetailList = respData.detailList || [];


            if (QueryVolApply.assetDetailList.length <= 0) {
                var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
                $("#assetRealBody").append(trHtml);
                return false;

            } else {
                $(QueryVolApply.assetDetailList).each(function (index, element) {
                    var trList = [];
                    trList.push(CommonUtil.formatData(element.prodCode));
                    trList.push(CommonUtil.formatData(element.fundName));
                    trList.push(CommonUtil.formatData(element.busiCode));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                    trList.push(CommonUtil.formatData(element.bankAcct));
                    trList.push(CommonUtil.formatData(element.bankAcctName));
                    trList.push(CommonUtil.formatAmount(element.occurBalance));
                    var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                    $("#assetRealBody").append(trAppendHtml);
                });
            }
        }
        ,
        queryMergeVolOrderByIdBack: function (data) {
            var bodyData = data.body || {};
            QueryVolApply.appCheckOrder = bodyData.checkOrder || {};
            QueryVolApply.appCheckDtlOrder = bodyData.checkDtlOrder || [];

            // 客户信息
            BodyView.setCustInfo("queryMergeCustInfoId", QueryVolApply.appCheckOrder.txAcctNo,
                QueryVolApply.appCheckOrder.idNo, QueryVolApply.appCheckOrder.disCode, QueryVolApply.setMergeCustInfoTable);
            // 转出信息
            BodyView.setTransOutTableView("mergeOutInfo", QueryVolApply.appCheckDtlOrder, QueryVolApply.appCheckOrder.disCode);
            // 转入信息
            BodyView.setTransInTableView("mergeInInfo", QueryVolApply.appCheckOrder);
            // 订单信息
            BodyView.setAppOrderInfoTableView("mergeOrderInfo", QueryVolApply.appCheckOrder);

            // POPUP
            layer.open({
                title: ['份额合并详情', true],
                type: 1,
                area: ['95%', '90%'],
                skin: 'layui-layer-rim', //加上边框
                btnAlign: 'l',
                content: $('#popupMergeVolInfo')
            });
        }
        ,

        setMergeCustInfoTable: function (data) {
            BodyView.setCustInfoTableView("queryMergeCustInfoId", data);
        }
        ,

        setTransferCustInfoTable: function (data) {
            BodyView.setCustInfoTableView("queryTransCustInfoId", data);
        }
        ,

        setTransInBankInfo: function (data) {
            var body = data.body || {};
            var custBanks = body.custBanks || [];

            var transInBanks = {};
            transInBanks.bankAcct = QueryVolApply.appCheckOrder.bankAcct;
            transInBanks.bankCode = QueryVolApply.appCheckOrder.bankCode;

            $(custBanks).each(function (index, element) {
                if (element.cpAcctNo == QueryVolApply.appCheckOrder.cpAcctNo) {
                    transInBanks.bankRegionName = element.bankRegionName;
                    return;
                }
            });

            // 转入银行卡
            BodyView.setTransInBankTableView("transInBanks", transInBanks);
        }
        ,

        queryTransVolOrderByIdBack: function (data) {
            var bodyData = data.body || {};
            QueryVolApply.appCheckOrder = bodyData.checkOrder || {};
            QueryVolApply.appCheckDtlOrder = bodyData.checkDtlOrder || [];
            var orderFile = bodyData.orderFile || {};// CRM线上资料
            // 转入持仓
            var respData = bodyData.respData;
            QueryVolApply.transInDisCode = respData.disCode;
            QueryVolApply.transInBalDtl = respData.custBalDtlList || [];

            OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料

            // 客户信息
            BodyView.setCustInfo("queryTransCustInfoId", QueryVolApply.appCheckOrder.txAcctNo,
                QueryVolApply.appCheckOrder.idNo, QueryVolApply.appCheckOrder.disCode, QueryVolApply.setTransferCustInfoTable);
            
			// 转出信息
            BodyView.setTransOutTableViewNew("highTransOutCustBals", "transOutCustBals", QueryVolApply.appCheckDtlOrder, QueryVolApply.appCheckOrder.disCode,"assetRealBody");
            // 设置转入银行卡信息
            QueryCustInfo.getCustBankInfos(QueryVolApply.appCheckOrder.txAcctNo, QueryVolApply.appCheckOrder.disCode, QueryVolApply.setTransInBankInfo);
            // 转入银行卡资产信息
            BodyView.setTransInCustBalsTableView("transInCustBals", QueryVolApply.transInBalDtl, QueryVolApply.transInDisCode);
            // 订单信息
            BodyView.setAppOrderInfoTableView("transOrderInfo", QueryVolApply.appCheckOrder);

            // POPUP
            layer.open({
                title: ['份额迁移详情', true],
                type: 1,
                area: ['95%', '90%'],
                skin: 'layui-layer-rim', //加上边框
                btnAlign: 'l',
                content: $('#popupTransVolInfo')
            });

            //如果选择注销银行卡，需要调用资金接口查询在途资产
            var selectCancelCard = QueryVolApply.appCheckOrder.cancelCard;
            $("#selectCancelCard").val(selectCancelCard);
            if (selectCancelCard == '1') {

                $("#intransAssetDiv").show();
                 //QueryVolApply.buildAssetTableView();
            }
        }
        ,


        buildAssetTableView: function (data) {

            QueryVolApply.detailList = QueryVolApply.intransitAssetList;

            // 转入银行卡资产信息
            $("#assetBody").empty();
            if (QueryVolApply.detailList.length <= 0) {
                var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
                $("#assetBody").append(trHtml);
                return false;

            } else {
                $(QueryVolApply.detailList).each(function (index, element) {
                    var trList = [];
                    trList.push('');
                    trList.push(CommonUtil.formatData(element.prodCode));
                    trList.push(CommonUtil.formatData(element.fundName));
                    trList.push(CommonUtil.formatData(element.busiCode));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                    trList.push(CommonUtil.formatData(element.bankAcct));
                    trList.push(CommonUtil.formatData(element.bankAcctName));
                    trList.push(CommonUtil.formatAmount(element.occurBalance));
                    var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                    $("#assetBody").append(trAppendHtml);
                });
            }
        }
        ,


        queryDealOrderInfo: function (data) {
            var bodyData = data.body || {};
            QueryVolApply.tradeOrders = bodyData.tradeOrders || {};

            QueryVolApply.dtlOrderDtos = bodyData.dtlOrderDtos || {};

            // 订单详情信息
            BodyView.setTradeOrderInfoTableView("tradeOrderInfo", QueryVolApply.tradeOrders);

            /*//在途资产
            BodyView.setIntrasitAssetTableView("assetBody", QueryVolApply.dtlOrderDtos);
*/
            // 定投合约信息
            BodyView.setScheduleInfoTableView("schedulePlanInfo", bodyData.schedulePlans || {});

            // POPUP
            layer.open({
                title: ['详情', true],
                type: 1,
                area: ['95%', '90%'],
                skin: 'layui-layer-rim', //加上边框
                btnAlign: 'l',
                content: $('#popupDealOrderInfo')
            });
            //份额迁移查询销卡状态和在途资产
            var txCode =  QueryVolApply.checkedOrder.txCode;
            var selectCancelCard = QueryVolApply.checkedOrder.cancelCard;
            if ((CONSTANTS.TRANS_VOL_TXCODE == txCode && '1' == selectCancelCard) || CONSTANTS.ONLINE_TRANS_VOL_TXCODE == txCode) {
                $("#unregisterCardDiv").show();
                $("#assetBodyDiv").show();
                //查询储蓄罐销卡状态
                QueryVolApply.queryBankAcctStat();
                //查询在途资产
                QueryVolApply.queryAssetInfo();
            }
        }
    }
;
