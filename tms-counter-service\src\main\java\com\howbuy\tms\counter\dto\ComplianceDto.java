package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * 
* @Title: ComplianceBean.java 
* @Description: 文件内容 
* <AUTHOR>
* @date 2021年7月14日 下午3:07:01 
*
 */
public class ComplianceDto implements Serializable {

	private static final long serialVersionUID = 2521205111263023072L;
	/**
	 * 合规明细号
	 */
	private String caDtlNo;

	/**
	 * 合规协议代码
	 */
	private String caCode;

	/**
	 * 合规协议名称
	 */
	private String caName;

	/**
	 * 合规协议说明
	 */
	private String caDesc;

	/**
	 * 基金代码
	 */
	private String fundCode;


	/**
	 * 基金名称
	 */
	private String fundName;

	/**
	 * 阅读类型 0-非强阅读 1-强阅读
	 */
	private String readType;

	/**
	 * 是否已阅读 1=已阅读 0=未阅读
	 */
	private String isRead;

	/**
	 * 文件内容
	 */
	private String caFileContent;

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getCaDtlNo() {
		return caDtlNo;
	}

	public void setCaDtlNo(String caDtlNo) {
		this.caDtlNo = caDtlNo;
	}

	public String getCaCode() {
		return caCode;
	}

	public void setCaCode(String caCode) {
		this.caCode = caCode;
	}

	public String getCaName() {
		return caName;
	}

	public void setCaName(String caName) {
		this.caName = caName;
	}

	public String getCaDesc() {
		return caDesc;
	}

	public void setCaDesc(String caDesc) {
		this.caDesc = caDesc;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getReadType() {
		return readType;
	}

	public void setReadType(String readType) {
		this.readType = readType;
	}

	public String getCaFileContent() {
		return caFileContent;
	}

	public void setCaFileContent(String caFileContent) {
		this.caFileContent = caFileContent;
	}

	public String getIsRead() {
		return isRead;
	}

	public void setIsRead(String isRead) {
		this.isRead = isRead;
	}
}
