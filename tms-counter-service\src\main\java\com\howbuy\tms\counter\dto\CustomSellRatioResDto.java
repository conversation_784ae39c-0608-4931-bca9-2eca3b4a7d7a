package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/1 11:13
 * @since JDK 1.8
 */
public class CustomSellRatioResDto implements Serializable {

    private static final long serialVersionUID = 7081416057611036227L;

    private String returnCode;

    private String description;

    private BigDecimal totalSellRatio;

    private List<CustomFundInfoResDto> customFundInfoResDtos;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getTotalSellRatio() {
        return totalSellRatio;
    }

    public void setTotalSellRatio(BigDecimal totalSellRatio) {
        this.totalSellRatio = totalSellRatio;
    }

    public List<CustomFundInfoResDto> getCustomFundInfoResDtos() {
        return customFundInfoResDtos;
    }

    public void setCustomFundInfoResDtos(List<CustomFundInfoResDto> customFundInfoResDtos) {
        this.customFundInfoResDtos = customFundInfoResDtos;
    }

}