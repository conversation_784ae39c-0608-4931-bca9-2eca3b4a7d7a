/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.regularservice.trade;

import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;

import java.util.List;

/**
 * @description:(中台柜台控制台服务)
 * <AUTHOR>
 * @date 2018年6月22日 下午1:40:54
 * @since JDK 1.6
 */
public interface TmsRegularCounterService {

    /**
     *
     * CounterPurchase:(零售柜台购买)
     *
     * @param counterPurchaseReqDto
     * @return
     * <AUTHOR>
     * @date 2017年3月31日 上午9:21:51
     */
    CounterPurchaseRespDto counterPurchase(RegularCounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * queryAcctBalanceDtl:(查询基金持仓明细)<br>协议类型：1-普通公募
     *
     * @param reqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午4:46:25
     */
    QueryAcctBalanceDtlRespDto queryAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * queryAcctBalDtlByMultiProtocol:(查询客户基金多个协议持仓明细)<br>协议类型：1-普通公募 + 7-普通公募定投...
     * @param reqDto
     * @param disInfoDto
     * @param protocolTypeList
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年1月23日 下午8:16:14
     */
    QueryAcctBalanceDtlRespDto queryAcctBalDtlByMultiProtocol(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception;


    /**
     *
     * counterCancel:(零售柜台撤单)
     *
     * @param counterCancelReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月16日 下午7:18:08
     */
    RegularCounterCancelRespDto counterCancel(RegularCounterCancelReqDto counterCancelReqDto, DisInfoDto disInfoDto) throws Exception;


    /**
     *
     * queryCanCancelOrder:(查询可撤销订单)
     *
     * @param custNo
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月17日 上午11:16:47
     */
    List<RegularCancelDealDto> queryCanCancelOrder(String dealNo, String custNo, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * counterQueryOrderById:(查询未审核订单)
     *
     * @param counterQueryOrderReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:04:49
     */
    RegularCounterOrderDto counterQueryOrderById(RegularCounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * counterQueryOrder:(查询未审核订单)
     *
     * @param counterQueryOrderReqDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:12:13
     */
    RegularCounterQueryOrderRespDto counterQueryOrder(RegularCounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * checkOrder:(下审核订单)
     *
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:20:34
     */
    boolean checkOrder(RegularSubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception;

    /**
     *
     * counterEnd:(柜台收市)
     *
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月20日 下午2:19:16
     */
    boolean counterEnd(DisInfoDto disInfoDto) throws Exception;

}
