/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.common;

/**
 * 和BooleanflagEnum 字段意义相反，
 * <AUTHOR>
 * @date 2022/1/24 17:24
 * @since JDK 1.8
 */
public enum BooleanFlagReverseEnum {
    /**
     * 是
     */
    YES("0", "是、已开通、开"),
    /**
     * 否
     */
    NO("1", "否、未开通、关");

    private String code;
    private String desc;

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    private BooleanFlagReverseEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public static String getCode(boolean b) {
        return b ? YES.code : NO.code;
    }
}
