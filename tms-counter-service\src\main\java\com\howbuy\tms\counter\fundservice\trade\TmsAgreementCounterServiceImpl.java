package com.howbuy.tms.counter.fundservice.trade;

import com.howbuy.common.utils.JacksonUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.fund.dto.portfolio.ZhZjzhZxpbDto;
import com.howbuy.fund.service.portfolio.ZhZjzhService;
import com.howbuy.interlayer.product.dto.PortfolioUProductInfoDto;
import com.howbuy.interlayer.product.model.fund.FundBasicInfoModel;
import com.howbuy.interlayer.product.model.portfolio.PortfolioProductFullModel;
import com.howbuy.interlayer.product.service.FundProductService;
import com.howbuy.interlayer.product.service.PortfolioProductService;
import com.howbuy.interlayer.product.service.PortfolioUProductInfoService;
import com.howbuy.tms.counter.dto.ComplianceDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.QueryAgreementDto;
import com.howbuy.tms.orders.search.facade.query.agreement.ComplianceBean;
import com.howbuy.tms.orders.search.facade.query.agreement.QueryAgreementFacade;
import com.howbuy.tms.orders.search.facade.query.agreement.QueryAgreementRequest;
import com.howbuy.tms.orders.search.facade.query.agreement.QueryAgreementResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/26 15:02
 * @since JDK 1.8
 */
@Service("tmsAgreementCounterService")
public class TmsAgreementCounterServiceImpl implements TmsAgreementCounterService{

    private static final Logger logger = LoggerFactory.getLogger(TmsFundCounterServiceImpl.class);

    @Autowired
    @Qualifier("tmscounter.fundProductService")
    private FundProductService fundProductService;

    @Autowired
    private ZhZjzhService zhZjzhService;

    @Autowired
    private QueryAgreementFacade queryAgreementFacade;

    @Autowired
    @Qualifier("tmscounter.portfolioProductService")
    private PortfolioProductService portfolioProductService;

    @Autowired
    @Qualifier("tmscounter.portfolioUProductInfoService")
    private PortfolioUProductInfoService portfolioUProductInfoService;

    @Override
    public QueryAgreementDto queryAgreement(CustInfoDto custInfoDto, String productCode, List<String> fundCodes) {
        QueryAgreementDto queryAgreementDto = new QueryAgreementDto();

        if(!StringUtil.isEmpty(productCode)){
            return queryAgreementDto;
        }
        try {
            QueryAgreementRequest request = new QueryAgreementRequest();
            request.setTxAcctNo(custInfoDto.getCustNo());
            request.setDisCode(custInfoDto.getDisCode());
            request.setProductCode(productCode);
            request.setFundCodes(fundCodes);
            QueryAgreementResponse response = queryAgreementFacade.execute(request);
            if(response != null){
                queryAgreementDto.setAgreementList(getAgreementList(response.getAgreementList()));
                queryAgreementDto.setOutlineList(getAgreementList(response.getOutlineList()));
                queryAgreementDto.setIntroduceList(getAgreementList(response.getIntroduceList()));
                queryAgreementDto.setFundContractList(getAgreementList(response.getFundContractList()));
            }

            //唯一标识
            onlyOutlineList(queryAgreementDto);

            //基金名称处理
            outlineFundListFundName(queryAgreementDto);

            //组合名称处理
            outlinePortfolioListFundName(queryAgreementDto, productCode);
        } catch (Exception e){
            logger.error("queryAgreement is error:", e);
        }
        return queryAgreementDto;
    }

    private List<ComplianceDto> getAgreementList(List<ComplianceBean> agreementList) {
        List<ComplianceDto> complianceList = new ArrayList<>();
        if(agreementList != null && agreementList.size() > 0){
            ComplianceDto complianceModel = null;
            for(ComplianceBean bean : agreementList){
                complianceModel = new ComplianceDto();
                complianceModel.setCaCode(bean.getCaCode());
                complianceModel.setCaDesc(bean.getCaDesc());
                complianceModel.setCaDtlNo(bean.getCaDtlNo());
                complianceModel.setCaFileContent(bean.getCaFileContent());
                complianceModel.setCaName("《"+ bean.getCaName() + "》");
                complianceModel.setFundCode(bean.getFundCode());
                complianceModel.setReadType(bean.getReadType());
                complianceModel.setIsRead(bean.getIsRead());
                complianceList.add(complianceModel);
            }
        }
        return complianceList;
    }

    /**
     * 组合名称
     * @Title: outlinePortfolioListFundName
     * @Description: 组合名称
     * @param @param queryAgreementModel
     * @param @param productCode 参数说明
     * @return void 返回类型
     * <AUTHOR>
     * @date 2021年8月20日 上午10:25:07
     */
    private void outlinePortfolioListFundName(QueryAgreementDto queryAgreementModel, String productCode) {
        if(StringUtil.isEmpty(productCode)){
            return;
        }

        String productName = null;
        //组合
        PortfolioProductFullModel portfolioProductFull = portfolioProductService.getPortfolioProductFull(productCode);
        if(portfolioProductFull != null){
            productName = portfolioProductFull.getProductName();
        }

        //潜龙
        if(StringUtil.isEmpty(productName)){
            PortfolioUProductInfoDto portfoliouProductInfoDto = portfolioUProductInfoService.queryByProductCode(productCode);
            if(portfoliouProductInfoDto != null){
                productName = portfoliouProductInfoDto.getProductName();
            }
        }

        //自定义组合
        if(StringUtil.isEmpty(productName)){
            logger.info("request zhZjzhService.getZjzhZxgdpb:" + JacksonUtil.objToJson(productCode));
            ZhZjzhZxpbDto userCompositeDto = zhZjzhService.getZjzhZxgdpb(Long.parseLong(productCode));
            logger.info("response zhZjzhService.getZjzhZxgdpb:" + JacksonUtil.objToJson(userCompositeDto));
            if(userCompositeDto != null){
                productName = userCompositeDto.getMc();
            }
        }

        List<ComplianceDto> agreementList = queryAgreementModel.getAgreementList();
        if(agreementList != null && agreementList.size() > 0){
            for(ComplianceDto model : agreementList){
                String caName = model.getCaName();
                if(!StringUtil.isEmpty(caName) && !StringUtil.isEmpty(productName)){
                    model.setCaName(String.format(caName.toString(), productName));
                }
            }
            queryAgreementModel.setAgreementList(agreementList);
        }
    }

    /**
     * 基金名称转译
     * @Title: outlineListFundName
     * @Description: 基金名称转译
     * @param @param queryAgreementModel 参数说明
     * @return void 返回类型
     * <AUTHOR>
     * @date 2021年8月19日 下午8:41:32
     */
    private void outlineFundListFundName(QueryAgreementDto queryAgreementModel) {
        List<ComplianceDto> outlineList = queryAgreementModel.getOutlineList();
        List<ComplianceDto> agreementList = queryAgreementModel.getAgreementList();
        // 招募说明书
        List<ComplianceDto> introduceList = queryAgreementModel.getIntroduceList();
        // 基金合同
        List<ComplianceDto> fundContractList = queryAgreementModel.getFundContractList();
        Map<String, FundBasicInfoModel> fundBasicInfoMap = getFundInfo(outlineList, agreementList, introduceList, fundContractList);
        outlineList(queryAgreementModel, outlineList, fundBasicInfoMap);

        agreementList(queryAgreementModel, agreementList, fundBasicInfoMap);

        introduceList(queryAgreementModel, introduceList, fundBasicInfoMap);

        fundContractList(queryAgreementModel, fundContractList, fundBasicInfoMap);
    }

    private static void fundContractList(QueryAgreementDto queryAgreementModel, List<ComplianceDto> fundContractList,
            Map<String, FundBasicInfoModel> fundBasicInfoMap) {
        if(CollectionUtils.isNotEmpty(fundContractList)){
            for(ComplianceDto model : fundContractList){
                String caName = model.getCaName();
                FundBasicInfoModel fundBasicInfoModel = fundBasicInfoMap.get(model.getFundCode());
                if(!StringUtil.isEmpty(caName) && fundBasicInfoModel != null && !StringUtil.isEmpty(fundBasicInfoModel.getFundAttr())){
                    model.setCaName(String.format(caName, fundBasicInfoModel.getFundAttr()));
                }
            }
            queryAgreementModel.setFundContractList(fundContractList);
        }
    }

    private static void introduceList(QueryAgreementDto queryAgreementModel, List<ComplianceDto> introduceList, Map<String, FundBasicInfoModel> fundBasicInfoMap) {
        if(CollectionUtils.isNotEmpty(introduceList)){
            for(ComplianceDto model : introduceList){
                String caName = model.getCaName();
                FundBasicInfoModel fundBasicInfoModel = fundBasicInfoMap.get(model.getFundCode());
                if(!StringUtil.isEmpty(caName) && fundBasicInfoModel != null && !StringUtil.isEmpty(fundBasicInfoModel.getFundAttr())){
                    model.setCaName(String.format(caName, fundBasicInfoModel.getFundAttr()));
                }
            }
            queryAgreementModel.setIntroduceList(introduceList);
        }
    }

    private static void agreementList(QueryAgreementDto queryAgreementModel, List<ComplianceDto> agreementList, Map<String, FundBasicInfoModel> fundBasicInfoMap) {
        if(agreementList != null && agreementList.size() > 0){
            for(ComplianceDto model : agreementList){
                String caName = model.getCaName();
                FundBasicInfoModel fundBasicInfoModel = fundBasicInfoMap.get(model.getFundCode());
                if(!StringUtil.isEmpty(caName) && fundBasicInfoModel != null && !StringUtil.isEmpty(fundBasicInfoModel.getFundAttr())){
                    model.setCaName(String.format(caName, fundBasicInfoModel.getFundAttr()));
                }
            }
            queryAgreementModel.setAgreementList(agreementList);
        }
    }

    private static void outlineList(QueryAgreementDto queryAgreementModel, List<ComplianceDto> outlineList, Map<String, FundBasicInfoModel> fundBasicInfoMap) {
        if(outlineList != null && outlineList.size() > 0){
            for(ComplianceDto model : outlineList){
                String caName = model.getCaName();
                FundBasicInfoModel fundBasicInfoModel = fundBasicInfoMap.get(model.getFundCode());
                if(!StringUtil.isEmpty(caName) && fundBasicInfoModel != null && !StringUtil.isEmpty(fundBasicInfoModel.getFundAttr())){
                    model.setCaName(String.format(caName, fundBasicInfoModel.getFundAttr()));
                }
            }
            queryAgreementModel.setOutlineList(outlineList);
        }
    }

    /**
     * 基金信息Map
     *
     * @param @param           outlineList
     * @param @return          参数说明
     * @param introduceList
     * @param fundContractList
     * @return Map<String, FundBasicInfoModel> 返回类型
     * @Title: getFundInfo
     * @Description: 基金信息Map
     * <AUTHOR>
     * @date 2021年8月19日 下午8:38:54
     */
    private Map<String, FundBasicInfoModel> getFundInfo(List<ComplianceDto> outlineList, List<ComplianceDto> agreementList, List<ComplianceDto> introduceList, List<ComplianceDto> fundContractList) {
        Map<String, FundBasicInfoModel> fundBasicInfoMap = new HashMap<String, FundBasicInfoModel>(16);
        List<String> allFundCodes = new ArrayList<String>();

        List<String> outlineListFundCodes = getOutlineListFundCodes(outlineList);
        if(!CollectionUtils.isEmpty(outlineListFundCodes)){
            allFundCodes.addAll(outlineListFundCodes);
        }

        List<String> agreementListFundCodes = getAgreementListFundCodes(agreementList);
        if(!CollectionUtils.isEmpty(agreementListFundCodes)){
            allFundCodes.addAll(agreementListFundCodes);
        }

        List<String> introduceListFundCodes = getIntroduceListFundCodes(introduceList);
        if(!CollectionUtils.isEmpty(introduceListFundCodes)){
            allFundCodes.addAll(introduceListFundCodes);
        }

        List<String> fundContractListFundCodes = getFundContractListFundCodes(fundContractList);
        if(!CollectionUtils.isEmpty(fundContractListFundCodes)){
            allFundCodes.addAll(fundContractListFundCodes);
        }

        List<FundBasicInfoModel> fundBasicInfoList = null;
        if(allFundCodes != null && allFundCodes.size() > 0){
            fundBasicInfoList = fundProductService.getBatchFundInfo(allFundCodes);
        }

        if(fundBasicInfoList != null && fundBasicInfoList.size() > 0){
            for(FundBasicInfoModel model : fundBasicInfoList){
                fundBasicInfoMap.put(model.getFundCode(), model);
            }
        }
        return fundBasicInfoMap;
    }

    private static List<String> getFundContractListFundCodes(List<ComplianceDto> fundContractList) {
        List<String> fundCodes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(fundContractList)){
            for(ComplianceDto model : fundContractList){
                if(!StringUtil.isEmpty(model.getFundCode()) && !model.getFundCode().contains("*")){
                    fundCodes.add(model.getFundCode());
                }
            }
        }
        return fundCodes;
    }

    private static List<String> getIntroduceListFundCodes(List<ComplianceDto> introduceList) {
        List<String> fundCodes = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(introduceList)){
            for(ComplianceDto model : introduceList){
                if (!StringUtil.isEmpty(model.getFundCode()) && !model.getFundCode().contains("*")) {
                    fundCodes.add(model.getFundCode());
                }
            }
        }
        return fundCodes;
    }

    private static List<String> getAgreementListFundCodes(List<ComplianceDto> agreementList) {
        List<String> fundCodes = new ArrayList<>();
        if(agreementList != null && agreementList.size() > 0){
            for(ComplianceDto model : agreementList){
                if(!StringUtil.isEmpty(model.getFundCode()) && !model.getFundCode().contains("*")){
                    fundCodes.add(model.getFundCode());
                }
            }
        }
        return fundCodes;
    }

    private static List<String> getOutlineListFundCodes(List<ComplianceDto> outlineList) {
        List<String> fundCodes = new ArrayList<>();
        if(outlineList != null && outlineList.size() > 0){
            for(ComplianceDto model : outlineList){
                if(!StringUtil.isEmpty(model.getFundCode()) && !model.getFundCode().contains("*")){
                    fundCodes.add(model.getFundCode());
                }
            }
        }
        return fundCodes;
    }

    /**
     * 唯一标识别
     * @Title: onlyOutlineList
     * @Description: 唯一标识别
     * @param @param queryAgreementModel 参数说明
     * @return void 返回类型
     * <AUTHOR>
     * @date 2021年8月19日 下午8:24:08
     */
    private void onlyOutlineList(QueryAgreementDto queryAgreementModel) {
        List<ComplianceDto> agreementList = queryAgreementModel.getAgreementList();
        // 协议
        if(agreementList != null && agreementList.size() > 0){
            for(ComplianceDto model : agreementList){
                model.setCaDtlNo(model.getCaDtlNo() + "|" + model.getFundCode());
            }
            queryAgreementModel.setAgreementList(agreementList);
        }

        // 概要
        List<ComplianceDto> outlineList = queryAgreementModel.getOutlineList();
        if(outlineList != null && outlineList.size() > 0){
            for(ComplianceDto model : outlineList){
                model.setCaDtlNo(model.getCaDtlNo() + "|" + model.getFundCode());
            }
            queryAgreementModel.setOutlineList(outlineList);
        }
    }


}