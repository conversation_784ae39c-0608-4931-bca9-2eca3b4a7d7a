/**
*查询可撤销订单
*<AUTHOR>
*@date 2017-04-11 16:17
*/

var operatorNo = CommonUtil.getParam("operatorNo");

var QueryCanCancel ={
		/**
		 * 查询可撤单订单，过滤掉已申请过的撤单订单
		 */
		queryCanCancelForApply:function(custNo,dealNo,callback){
			var uri= TmsCounterConfig.QUERY_FUND_CAN_CANCEL_FOR_APPLY_URL ||  {};
			var reqparamters = {};
			reqparamters.custNo = custNo;
			reqparamters.dealNo = dealNo;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCanCancel.queryCanCancelBack, callback);
		},
		
		/**
		 * 查询可撤单订单
		 */
		queryCanCancel:function(custNo,dealNo,operNo){
			if(!CommonUtil.isEmpty(operNo)){
				operatorNo = operNo;
			}
			var uri= TmsCounterConfig.QUERY_FUND_CAN_CANCEL_URL ||  {};
			var reqparamters = {};
			reqparamters.custNo = custNo;
			reqparamters.dealNo = dealNo;
			console.info(uri);
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCanCancel.queryCanCancelBack);
		},
		/**
		 * 可撤单订单信息
		 */
		queryCanCancelBack:function(data){
			var bodyData = data.body || {};
			QueryCanCancel.canCancelOrders= bodyData.canCancelOrders || [];
			if($("#cancelList").length > 0){
				$("#cancelList").html('');
				if(QueryCanCancel.canCancelOrders.length <=0){
					var trHtml = '<tr><td colspan="12">没有查询到可以撤销交易</td></tr>';
					$("#cancelList").append(trHtml);
					return false;
				}
				$(QueryCanCancel.canCancelOrders).each(function(index,element){
					console.info(element.withdrawDirection);
					var trList = [];
					trList.push(CommonUtil.formatData(element.productCode));
					trList.push(CommonUtil.formatData(element.productName));
					trList.push(CommonUtil.getMapValue(CONSTANTS.Z_BUSICODE_MAP,element.zBusiCode,'--'));
					if(element.appAmt > 0){
						trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
					}else {
						trList.push('--');
					}
					if(element.appVol > 0){
						trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
					}else {
						trList.push('--');
					}
					if(element.appRatio > 0){
						trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appRatio)));
					}else {
						trList.push('--');
					}
					trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_ALL_TYPE,element.paymentType,'--'));
					trList.push(CommonUtil.getMapValue(CONSTANTS.ALL_REDEEM_FLAG_MAP,element.allRedeemFlag,'--'));
					trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP,element.orderStatus,'--'));
					//trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_TYPE, element.withdrawDirection, '--'));
					trList.push(CommonUtil.formatData(element.dealNo));
					trList.push(CommonUtil.formatData(element.appDate));
					trList.push(CommonUtil.formatData(element.appTime));
					trList.push(CommonUtil.formatData(operatorNo,"操作员"));
					var trHtml = '<tr class="text-c"><td><input type="radio" class="selectCancleOrder" name="orderIndex" index="' + index + '" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td></tr>';
					$("#cancelList").append(trHtml);
				});
				
				$(".selectCancleOrder").click(
					function() {
						$(this).attr('checked', 'checked').siblings().removeAttr('checked');
					});
				$('input[name="orderIndex"][index="0"]').click();
			}
		}	
};
