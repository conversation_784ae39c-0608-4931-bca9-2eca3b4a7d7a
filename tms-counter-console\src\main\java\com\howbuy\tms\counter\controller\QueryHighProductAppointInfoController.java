/**
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.counter.controller;


import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.fundservice.trade.HighProductAppointService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:(查询预约产品开放日历信息)
 * @reason:TODO ADD REASON(可选)
 * @date 2018年3月9日 下午3:54:31
 * @since JDK 1.6
 */
@Controller
public class QueryHighProductAppointInfoController {

    private Logger logger = LoggerFactory.getLogger(QueryHighProductAppointInfoController.class);

    @Autowired
    private HighProductAppointService highProductAppointService;

    @RequestMapping("tmscounter/queryhighproductappointinfo.htm")
    public void queryHighProductAppointInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String productCode = request.getParameter("productCode");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        String busiType = request.getParameter("busiType");
        logger.info("QueryHighProductController|queryhighproductinfo|productCode:{}, appDt:{}, appTm:{}, busiType:{}", productCode, appDt, appTm, busiType);
        HighProductAppointmentInfoModel productAppointmentInfoModel = highProductAppointService.queryTradeProductAppointInfo(productCode, appDt, appTm, busiType);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<>(16);
        body.put("productAppointmentInfoModel", productAppointmentInfoModel);
        rst.setBody(body);
        WebUtil.write(response, rst);
    }

    /**
     * isSupportAdvance
     *
     * @param supportAdvanceFlag 0-不支持提前购买赎回 1-支持提前购买 2-支持提前赎回 3-支持提前购买赎回
     * @param busiType           业务类型 0-购买 1-赎回
     * @return
     * <AUTHOR>
     * @date 2018年3月9日 下午3:58:49
     */
    private boolean isSupportAdvance(String supportAdvanceFlag, String busiType) {

        if (StringUtils.isEmpty(supportAdvanceFlag)) {
            return false;
        }

        // 是否支持购买提前下单
        if (BusiTypeEnum.BUY.getCode().equals(busiType)) {
            if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(supportAdvanceFlag)) {
                return true;
            }
        }

        // 是否支持赎回提前下单
        if (BusiTypeEnum.SELL.getCode().equals(busiType)) {
            if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag)) {
                return true;
            }
        }

        return false;

    }


}

