<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>份额迁移</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 特殊业务 <span class="c-gray en">&gt;</span> 份额迁移 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box" id="searchForm">
                <p class="mainTitle mt10">份额迁移</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="hidden" id="hboneNo"/>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span ml30">证件号：</span>
                    <input name="idNo" id="idNo" type="text" placeholder='请输入'>
                    <span class="normal_span ml30">分销机构：</span>
                    <span class="select-box inline">
                       <select name="disCode" class="select" id="selectDisCode"></select>
                    </span>
                </div>
                <div class="cp_top mt10">
                    <span class="normal_span">银行卡号<span style="color:#FF0000;">*</span>：</span>
                    <input type="text" name="bankAcct" id="bankAcct" style="width: 200px;">
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryCustBalInfoBtn">查询</a>
                    <a href="javascript:void(0)" class="btn radius btn-success ml30" id="reset">重置</a>
                </div>
            </div>
        </div>
		<div class="containner_all">
			<div class="cp_top mt10">
				<span class="normal_span red">*是否注销原卡：</span>
				<span class="select-box inline"> 
				<select name="cancelCard" class="select" id="selectCancelCard" >
						<option value="1" selected="selected">是</option>
						<option value="0">否</option>
				</select>
				</span>
                <span class="normal_span red" id="alertSpanId">&nbsp &nbsp 当选择为‘是’时，资金系统在途资产会迁移至新卡，原卡会注销，相关定投合约会终止</span>
            </div>
		</div>
	</div>

    <p class="main_title mt30" id="showMaterial">柜台材料信息</p>
    <div class="result2_tab" id="onLineMaterial">
    </div>

    <div class="page-container w1000">
        <p class="main_title mt10">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th style="display: none;">投资者类型</th>
                        <th style="display: none;">风险等级</th>
                        <th style="display: none;">分销机构</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                         <td style="display: none;">--</td>
                         <td style="display: none;">--</td>
                         <td style="display: none;">--</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <p class="main_title mt30">转出银行卡资产信息-在途资产</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <thead>
                <tr class="text-c">
                    <th><input  name="checkAsset" type="checkbox" id="assetBox" checked disabled></input></th>
                    <th>基金代码</th>
                    <th>基金名称</th>
                    <th>业务类型</th>
                    <th>交易通道</th>
                    <th>银行卡号</th>
                    <th>银行名称</th>
                    <th>在途资金</th>

                </tr>
                </thead>
                <tbody id="assetBody">
                <tr class="text-c">
                    <td></td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                </tr>
                </tbody>
            </table>
        </div>
        <p class="main_title mt30">转出银行卡资产信息-零售</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th><input name="checkFund" type="checkbox" id="checkFundBox"></input></th>
                        <th>基金代码</th>
                        <th>基金名称</th>
                        <th>交易通道</th>
                        <th>银行卡号</th>
                        <th>银行名称</th>
                        <th>总份额</th>
                        <th>可用份额</th>                   
                        <th>冻结份额</th>
                        <th>司法冻结份额</th> 
                        <th>协议类型</th>
       					<th>协议号</th>
                        <th>分销机构名称</th>                          
                    </tr>
               </thead>
                <tbody id="transOutCustBals">
                	 <tr class="text-c">
                	 	<td></td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30">转出银行卡资产信息-高端</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort" id="highCustBooksTable">
               <thead>
                   <tr class="text-c">
                   		<th><input  type="checkbox" id="checkHighBox" checked disabled></input></th>
                   		<th>TA代码</th>
                        <th>基金代码</th>
                        <th>基金名称</th>
                        <th>交易通道</th>
                        <th>银行卡号</th>
                        <th>银行名称</th>
                        <th>总份额</th>
                        <th>可用份额</th>                   
                        <th>冻结份额</th>
                        <th>司法冻结份额</th> 
                        <th>协议类型</th>
       					<th>协议号</th>
                        <th>分销机构名称</th>                          
                    </tr>
               </thead>
                <tbody id="highTransOutCustBals">
                	 <tr class="text-c">
                	 	<td></td>
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30">转入银行卡</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>选择</th>
                        <th>转入银行卡号</th>
                        <th>银行名称</th>
                        <th>银行全称</th>
                        <th>账户状态</th>                      
                    </tr>
               </thead>
                <tbody id="transInBanks">
                	 <tr class="text-c">
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30" style="display: none;">转入银行卡资产信息</p>
        <div class="result2_tab" style="display: none;">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>基金代码</th>
                        <th>基金名称</th>
                        <th>银行卡号</th>
                        <th>银行名称</th>
                        <th>总份额</th>
                        <th>可用份额</th>                   
                        <th>冻结份额</th>
                        <th>司法冻结份额</th>
                        <th>协议类型</th>  
                        <th>协议号</th>
                        <th>分销机构名称</th>     
                    </tr>
               </thead>
                <tbody id="transInCustBals">
                	 <tr class="text-c">
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
       <p class="main_title mt30">其他信息</p>
       <form id="transactorInfoForm" >
        <div class="cp_top">
            <span>网点：中台柜台<input type="hidden" name="outletCode" value="W20170215"/></span>
            <span class="ml30">投资顾问代码：
                <span class="select-box inline">
                	<select name="consCode" class="select selectconsCode" ></select>
            	</span>
            </span>
            <span class="ml30">是否经办：
               <span class="select-box inline">
           			<select name="agentFlag" class="select selectAgened">
              			<option value="0">否</option>
             		 	<option value="1">是</option>
           			</select>
       			</span>
            </span>
        </div>
        <div class="cp_top">
            <span>下单日期：</span>
            <span> 
            	<input class="input-text laydate-icon"  id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" readonly="true">
            </span>
            <span class="ml30">下单时间：</span>
            <span>
            	<input class="input-text laydate-icon" type="text" id="appTm" name="appTm" isnull="false" datatype="s" errormsg="下单时间" maxlength="6">     
            </span>
        </div>
        
        <!-- 是否经办: 个人用户默认为否, 机构客户默认为是, 为是时显示经办人信息 -->
       <div class="result2_tab" id="agentInfoDiv" style="display: none;">
        <p class="main_title mt20">经办人信息</p>
         <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
            	<tr class="text-c">
                   	<td>经办人姓名：</td>
                   	<td>
                   		<input type="text" placeholder="请输入"  name="transactorName"  datatype="s" errormsg="经办人姓名">
                   	</td>
                   	<td>经办人证件类型：</td>
              		<td>
                  		<span class="select-box inline">
                    		<select name="transactorIdType" class="select selectTransactorIdType"   datatype="s" errormsg="经办人证件类型" >
                    		</select>
                		</span>
                	</td>
                </tr>
             	<tr class="text-c">
              		<td>经办人证件号：</td>
              		<td> <input type="text" placeholder="请输入"  name="transactorIdNo"  datatype="s" errormsg="经办人证件号" ></td>
               		<td>&nbsp;</td>
               		<td>&nbsp;</td>
               	</tr>
            </tbody>
          </table>
        </div>
        </form>
         
         <p class="mt30">
            <a href="javascript:void(0)" id ="confimSubmitBtn" class="btn radius btn-secondary">确认提交</a>
        </p>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/common/validate.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfosubpage.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/high/common/onlineorderfile.js?v=20200731"></script>
    <script type="text/javascript" src="../../../js/fund/trade/transvol.js?v=20200731"></script>
</body>

</html>