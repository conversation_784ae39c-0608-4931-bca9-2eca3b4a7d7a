/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common.util;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @description:(http 请求工具类) 
 * <AUTHOR>
 * @date 2017年4月10日 下午1:26:56
 * @since JDK 1.6
 */
public class RequestUtil {
    
    private static final Logger logger = LogManager.getLogger(RequestUtil.class);

    public static HttpServletRequest getHttpRequest() {
        ServletRequestAttributes requestAttributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
        HttpServletRequest request = requestAttributes.getRequest();

        if (request == null) {
            logger.error("http request is null!");
            throw new RuntimeException("please definition org.springframework.web.context.request.RequestContextListener in web.xml!!!");
        }
        return request;
    }

    /**
     * 获取请求参数：放在request.params中，且未加密
     * 
     * @param name
     * @return
     */
    public static String getHttpParameter(String name) {
        return getHttpRequest().getParameter(name);
    }
}

