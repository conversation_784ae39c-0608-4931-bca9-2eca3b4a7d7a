$(function(){
	CheckBuy.productInfo = {};
	CheckBuy.isAdviser = false;
	Init.init();
	$("#returnBtn").on('click',function(){
		CheckBuy.confirm(CounterCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckBuy.confirm(CounterCheck.Succ);
	});
	
	$("#fundCode").on('blur',function(){
		CheckBuy.queryProductInfo();
	});

    $("#downloadFile").on('click',function(){
        CheckBuy.download("downloadFile");
    });

    $("#openFile").on('click',function(){
        CheckBuy.download("openFile");
    });

    $("#closeVideo").on('click',function(){
        CheckBuy.closeVideo();
    });

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckBuy.checkOrder = {};	 
	CheckBuy.init(checkId,custNo,disCode,idNo);
});

var CheckBuy = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,CheckBuy.queryCheckOrderByIdBack);
		
		$("#applyAmount").blur(function(){
			$("#applyAmount").val(CommonUtil.formatAmount($("#applyAmount").val()));
		});
	}, 
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var buyConfirmForm = $("#buyConfirmForm").serializeObject();
		if (CheckBuy.isAdviser) {
			buyConfirmForm.discountRate = null;
			buyConfirmForm.originalFeeRate = null;
		}
		
		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckBuy.checkFaildDesc = $("#checkFaildDesc").val();
		}else{
			var validRst = Valid.valiadateFrom($("#buyConfirmForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}
			
			var checkResultReply = CheckBuy.checkBuyValid(buyConfirmForm,CheckBuy.checkOrder);
			if(!checkResultReply.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(checkResultReply.tip);
				return false;
			}
		}
		
		var reqparamters ={"checkFaildDesc":CheckBuy.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckBuy.checkOrder), "productInfoForm":JSON.stringify(CheckBuy.productInfo)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckBuy.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	checkBuyValid:function(checkForm , orderForm){
		var fundCode = checkForm.fundCode || '';
		var appAmt = CommonUtil.unFormatAmount(checkForm.appAmt) || '';
		var cpAcctNo = checkForm.cpAcctNo || '';
		var discountRate = checkForm.discountRate || '';
		
		var result = {"status":true,"tip":''};
		
		if(fundCode != (orderForm.fundCode || '')){
			result.status = false;
			result.tip = "基金代码不匹配，请重新确认";
			return result;
		}
		if(appAmt != (orderForm.appAmt || '')){
			result.status = false;
			result.tip = "申请金额不匹配，请重新确认";
			return result;
		}

		var orderCpAcctNo = orderForm.cpAcctNo || '';
		if(cpAcctNo != orderCpAcctNo){
			result.status = false;
			result.tip = "银行卡不匹配，请重新确认";
			return result;
		}
		
		if(!CheckBuy.isAdviser && discountRate != orderForm.discountRate){
			result.status = false;
			result.tip = "申请折扣率不匹配，请重新确认";
			return result;
		}
		return result;
		
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckBuy.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(CheckBuy.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckBuy.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}

		CheckBuy.queryProductInfo(CheckBuy.checkOrder.fundCode);
		
		if($("#selectBank").length > 0){
			$("#selectBank").val(CheckBuy.checkOrder.cpAcctNo);
		}
		
		if($("#originalFeeRate").length > 0){
			$("#originalFeeRate").html(CheckBuy.checkOrder.feeRate);
		}
		
		if($("#riskFlag").length > 0){
			$("#riskFlag").html(CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, CheckBuy.checkOrder.riskFlag, ''));
		}

		if($("#filePath").length > 0 && typeof(CheckBuy.checkOrder.doubleRecordFilePath)!="undefined"){
			$("#filePath").attr("data", CheckBuy.checkOrder.doubleRecordFilePath);
			$("#downloadFile").show();
			$("#openFile").show();
		}

		if($("#discountRate").length > 0){
			$("#discountRate").val(CheckBuy.checkOrder.discountRate);
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(CheckBuy.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").html(CheckBuy.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckBuy.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(CheckBuy.checkOrder.transactorName);
		}
		
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckBuy.checkOrder.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(CheckBuy.checkOrder.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(CheckBuy.checkOrder.memo);
		}
	},

	queryProductInfo:function(fundCode){
		if(!fundCode){
			fundCode = $("#fundCode").val();
		}

		var uri= TmsCounterConfig.QUERY_PRODUCT_INFO_URL ||  {};
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo) || {};
		console.info("custInfoForm : " + custInfoForm);
		var reqparamters = {"productCode":fundCode, "custInfoForm": custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckBuy.queryProductInfoCallBack);
	},

	queryProductInfoCallBack:function(data){

		var productInfo = data.body || {};
		CheckBuy.productInfo = productInfo;
		var isAdviser = productInfo.adviserFlag || false;
		CheckBuy.isAdviser = isAdviser;
		if (isAdviser) {
			$("#originalFeeRate").html("--");
			$("#discountRate").val("--");

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(productInfo.isBuyOpen == '1' ? "可申购" : "暂停");
			}
			var adviserQuestRule = productInfo.adviserQuestRuleFlag || false;
			if (adviserQuestRule) {
				$("#questionAnswerDiv").show();
				$("#questionAnswer").val(CheckBuy.checkOrder.surveyAnswer || '');

			}else {
				$("#questionAnswerDiv").hide();
				$("#questionAnswer").val('');
			}

		}else {
			$("#questionAnswerDiv").hide();
			$("#questionAnswer").val('');

			var isCommonFund = QueryFundInfo.checkFundInfo(productInfo);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, productInfo.fundStat));
			}

			if(!isCommonFund){
				return false;
			}
		}

	},

    download:function (type) {
        var url = TmsCounterConfig.GET_DOUBLE_RECORD_FILE_URL;
        var filePath = $("#filePath").attr("data");
        var fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        var xhr = new XMLHttpRequest();
        xhr.open('POST', url, true); // 设置请求方式POST方式
        xhr.responseType = "blob"; // 返回类型blob
        xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded");//设置请求内容类型
        // 请求回调函数
        xhr.onload = function (data) {
            if (this.status === 200) {
                var content  = this.response;
                var blob = new Blob([content]);
                var videoUrl = URL.createObjectURL(blob);
                if(type=="downloadFile"){
                    var elink = document.createElement('a');
                    elink.download = fileName;
                    elink.style.display = 'none';
                    elink.href = videoUrl;
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
				} else {
                    $("#videoUrl").attr("src", videoUrl);
                    document.getElementsByClassName('cover2')[0].classList.remove('hide2');
                    document.getElementsByClassName('modal2')[0].classList.remove('hide2');
				}
            }
        };
        // 发送ajax请求
        xhr.send("filePath="+filePath);
    },

    closeVideo:function () {
        $("#videoUrl")[0].pause();
        document.getElementsByClassName('cover2')[0].classList.add('hide2');
        document.getElementsByClassName('modal2')[0].classList.add('hide2');
    }

}
