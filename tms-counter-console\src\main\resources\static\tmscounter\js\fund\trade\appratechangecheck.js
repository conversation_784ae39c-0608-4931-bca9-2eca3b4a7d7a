
$(function(){
	var operatorNo = cookie.get("operatorNo");
    Init.init();
	AppRateChangeCheck.init();
});

var AppRateChangeCheck = {
	init:function(){
		//通过
		AppRateChangeCheck.Succ = '1';
		//驳回
		AppRateChangeCheck.Faild = '2';

		$("#queryBtn").on('click',function(){
			AppRateChangeCheck.queryOrderInfo();
		});

		$("#succBtn").on('click',function(){
			AppRateChangeCheck.confirm(AppRateChangeCheck.Succ);
		});

		$("#returnBtn").on('click',function(){
			/*layer.prompt({title: '请填写拒绝原因', formType: 1}, function(pass, index){
				layer.close(index);
				//QueryHighForceDeal.reSerSubmitFlag(dealNo,pass);
			});
*/
			layer.prompt({
				formType: 2,
				title: '请填写审核拒绝原因（注：必填项）',
				area: ['500px', '150px'],
				btnAlign: 'c',
				yes: function(index, layero){
					// 获取文本框输入的值
					var value = layero.find(".layui-layer-input").val();
					if (value) {
						AppRateChangeCheck.confirm(AppRateChangeCheck.Faild,value);
						layer.close(index);
					} else {
						alert("请填写审核拒绝原因！");
					}
				}
			});

		});

	/*	// 设置转出和转入信息
		QueryCheckOrder.queryMergeTransCheckOrderById(checkId, AppRateChangeCheck.queryOrderByIdBack, checkNode);
*/
	},
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo: function () {
		var uri = TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_URL || {};
		var reqparamters = {};
		var queryOrderConditionForm = $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm, function (name, value) {
			if (!CommonUtil.isEmpty(value)) {
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxPaging(uri, paramters, AppRateChangeCheck.queryOrderInfoCallBack, "pageView");
	},

	queryOrderInfoCallBack: function (data) {
		var bodyData = data;
		AppRateChangeCheck.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];

		var staticData = bodyData.counterQueryOrderRespDto || {};
		$("#staticId").html("当页小计：申请笔数【" + AppRateChangeCheck.checkOrders.length + "】申请份额【" + CommonUtil.formatAmount(staticData.pageAppVol) + "】 合计：申请笔数【" + staticData.totalCount + "】申请份额【" + CommonUtil.formatAmount(staticData.totalAppVol) + "】");

		$("#rsList").empty();
		if (AppRateChangeCheck.checkOrders.length <= 0) {
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
			$("#rsList").append(trHtml);
		}

		var i = 1;
		$(AppRateChangeCheck.checkOrders).each(function (index, element) {
			var trList = [];
			trList.push(index+1);

			trList.push('认申购');
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.fundCode));
			trList.push(CommonUtil.formatData(element.fundName));
			trList.push(CommonUtil.formatData(element.discountRate));
			trList.push(CommonUtil.formatData(element.appDt));
			trList.push(CommonUtil.formatData(element.operatorNo));

			if (element.checkFlag == '0') {
				trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = ' + index + ' checknode=' + '2' + '  style="color: #06c;">审核</a>');
			} else {
				trList.push('<a class="reQuery" href="javascript:void(0);" indexvalue = ' + index + ' checknode=' + '2' + '  style="color: #06c;">查看</a>');
			}
			var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
			$("#rsList").append(trHtml);
		});

		// 查订单详情
		$(".reQuery").off();
		$(".reQuery").on('click', function () {
			var indexValue = $(this).attr("indexvalue");
			var checkedOrder = AppRateChangeCheck.checkOrders[indexValue] || {};
			AppRateChangeCheck.checkedOrder = checkedOrder;
			AppRateChangeCheck.operationType = "query";
			var dealAppNo = checkedOrder.dealAppNo;
			var checkNode = $(this).attr('checkNode');

			// 复审
			var checkNode = "1";
			AppRateChangeCheck.queryDetail(dealAppNo,checkNode);

		});

		// 审核详情
		$(".reCheck").off();
		$(".reCheck").on('click', function () {
			var indexValue = $(this).attr("indexvalue");
			var checkedOrder = AppRateChangeCheck.checkOrders[indexValue] || {};
			AppRateChangeCheck.checkedOrder = checkedOrder;
			AppRateChangeCheck.operationType = "check";
			var dealAppNo = checkedOrder.dealAppNo;
			var checkNode = $(this).attr('checkNode');

			// 复审
			var checkNode = "2";
			AppRateChangeCheck.queryDetail(dealAppNo,checkNode);
		});
	},

	queryDetail : function(dealAppNo,checkNode){
		var url = "../check/ratechangecheck.html?dealAppNo="+dealAppNo+"&checkNode="+checkNode;
		// POPUP
		layer.open({
			title: ['详情', false],
			type: 2,
			area: ['95%', '90%'],
			skin: 'layui-layer-rim', //加上边框
			btnAlign: 'l',
			content: url

		});
		/*AppRateChangeCheck.queryChangeDiscountCheckOrderById(dealAppNo, AppRateChangeCheck.queryCheckDiscountOrderByIdBack, checkNode, "");

		// 查询明细
		var uri = TmsCounterConfig.QUERY_SUBMIT_APP_ORDER_TRADE_BY_ID_URL || {};
		var reqparamters = {};
		reqparamters.dealAppNo = dealAppNo;
		reqparamters.page = 1;
		reqparamters.pageSize = 5;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxPaging(uri, paramters, AppRateChangeCheck.queryRateChangeCheckInfoCallBack, "pageViewDetail");*/

	},
	queryChangeDiscountCheckOrderById:function(dealAppNo, callBack, checkNode,txCode){
		var  uri= TmsCounterConfig.QUERY_CHANGE_DISCOUNT_CHECK_ORDER_BY_ID_URL  ||  {};
		var reqparamters = {};
		reqparamters.dealAppNo = dealAppNo;
		reqparamters.pageNum = 1;
		reqparamters.pageSize = 100;
		reqparamters.checkNode = checkNode;
		reqparamters.txCode = txCode;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
		CommonUtil.ajaxAndCallBack(paramters, callBack);
	},
	queryCheckDiscountOrderByIdBack: function (data) {
		var bodyData = data.body || {};
		AppRateChangeCheck.appCheckOrder = bodyData.checkOrder || {};
		//AppRateChangeCheck.checkDtlOrder = bodyData.checkDtlOrder || [];

		if (CommonUtil.isEmpty(AppRateChangeCheck.appCheckOrder.dealAppNo)) {
			CommonUtil.layer_tip("无此订单");
			return false;
		}

		if (AppRateChangeCheck.operationType == 'query') {
			$('#succBtn').hide();
			$('#returnBtn').hide();
		} else {
			$('#succBtn').show();
			$('#returnBtn').show();
			CommonUtil.enabledBtn("returnBtn");
			CommonUtil.enabledBtn("succBtn");
		}

		/*if (popupModifyDiscountInfo.operationType == 'check' && popupModifyDiscountInfo.appCheckOrder.checkFlag != 0) {
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}*/
		// POPUP
		layer.open({
			title: ['详情', false],
			type: 1,
			area: ['95%', '90%'],
			skin: 'layui-layer-rim', //加上边框
			btnAlign: 'l',
			content: $('#popupModifyDiscountInfo')

		});

		AppRateChangeCheck.queryFundInfo(AppRateChangeCheck.appCheckOrder.fundCode);

		$("#modifyRateId").val(AppRateChangeCheck.appCheckOrder.discountRate);
	},

	queryRateChangeCheckInfoCallBack:function(data){
		var checkDtlOrder = data.dtlOrderDtoList;
		var appCheckOrder = data.counterOrderDto;
		$("#rsCheckList").empty();
		var trHtml = '';
		if(checkDtlOrder.length <= 0){
			trHtml = '<tr><td colspan="13">无查询记录</td></tr>';
			$("#rsCheckList").append(trHtml);
		}else{
			$(checkDtlOrder).each(function(index, element){
				var trList = [];
				trList.push(element.submitDealNo);
				trList.push(element.fundCode);
				trList.push(element.fundName);
				trList.push(element.discountRate);//当前折扣率
				trList.push(CommonUtil.getMapValue(CONSTANTS.BUSI_CODES_MAP, element.busiCode));//标准业务名称
				if(element.appVol > 0){
					trList.push(CommonUtil.formatData(element.appVol));
				}else {
					trList.push('--');
				}
				trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_ALL_TYPE, appCheckOrder.paymentType));//支付方式
				trList.push(appCheckOrder.appDate + ' ' +  appCheckOrder.appTime);//申请时间
				trList.push(appCheckOrder.taTradeDt);//申请TA日
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#rsCheckList").append(trAppendHtml);
			});
		}

	},
	/**
	 * 查询基金信息
	 *
	 * fundCode 基金代码毕传
	 */
	queryFundInfo:function(fundCode){
		var uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":fundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, AppRateChangeCheck.queryFundInfoCallBack);
	},

	/**
	 * 处理基金信息
	 */
	queryFundInfoCallBack:function(data){

		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		AppRateChangeCheck.fundInfo = fundInfo;

		$("#fundInfoId").empty();
		var trHtml = '<tr class="text-c"><td>' + AppRateChangeCheck.fundInfo.fundCode + '</td><td>' + AppRateChangeCheck.fundInfo.fundAttr + '</td><td>' + CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, AppRateChangeCheck.fundInfo.fundType) + '</td></tr>';
		$("#fundInfoId").append(trHtml);

	},
	/***
	 * 审核确认
	 */
	confirm : function(checkStatus,checkFaildDesc){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';


		var uri= TmsCounterConfig.CHECK_CHANGE_DISCOUNT_CONFIRM_URL ||  {};

		if(AppRateChangeCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty(checkFaildDesc)){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入退回原因");
				return false;
			}
			AppRateChangeCheck.checkFaildDesc = checkFaildDesc;
		}

		var reqparamters ={
			"checkFaildDesc":AppRateChangeCheck.checkFaildDesc || '',
			"checkStatus":checkStatus,
			"checkedOrderForm":JSON.stringify(AppRateChangeCheck.checkedOrder)


		};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, AppRateChangeCheck.callBack);
		return true;
	},

	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_alert(respDesc);
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_alert(respDesc);
		}
	}
};