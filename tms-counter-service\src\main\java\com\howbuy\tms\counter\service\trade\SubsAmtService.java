package com.howbuy.tms.counter.service.trade;

import com.howbuy.tms.counter.dto.*;

/**
 * @Description:认缴金额相关
 * @Author: yun.lu
 * Date: 2024/7/15 14:15
 */
public interface SubsAmtService {
    /**
     * 查询客户认缴信息
     *
     * @param customerSubsAmtPageReqDto 入参
     * @return 分页认缴信息
     */
    CustomerSubsAmtResultDto querySubsAmtPage(CustomerSubsAmtPageReqDto customerSubsAmtPageReqDto) throws Exception;

    /**
     * 查询用户产品认缴信息详情
     * @param customerSubsAmtDetailReqDto 入参
     * @return 用户产品认缴信息详情
     */
    CustomerSubsAmtInfoDto querySubsAmtDetail(CustomerSubsAmtDetailReqDto customerSubsAmtDetailReqDto) throws Exception;

    /**
     * 用户产品认缴金额修改结果
     * @param customerSubsAmtApplyReqDto 入参
     * @return 用户产品认缴金额修改结果
     */
    CustomerSubsAmtApplyResultDto subsAmtDetailApply(CustomerSubsAmtApplyReqDto customerSubsAmtApplyReqDto) throws Exception;

    /**
     * 查询认缴金额变更明细
     * @param dealAppNo 申请单号
     * @return 认缴金额变更明细
     * @throws Exception
     */
    CustomerSubsAmtChangeDetailDto querySubsAmtChangeDetail(String dealAppNo) throws Exception;

    CustomerSubsAmtApplyResultDto updateApply(UpdateSubsAmtDetailApplyDto updateSubsAmtDetailApplyDto) throws Exception;
}
