/**
*购买
*<AUTHOR>
*@date 2017-09-15 10:39
*/
$(function(){
    var operatorNo = cookie.get("operatorNo");
    Init.init();
    BuyFund.init();
    BuyFund.currDate = '';
	BuyFund.productInfo = {};
	BuyFund.isAdviser = false;
});

var BuyFund = {
	init:function(){
		/**
		 * 确认购买
		 */
		$("#confimBuyBtn").on('click',function(){
			if (BuyFund.validateFund(BuyFund.productInfo)){
				BuyFund.confirm(BuyFund.productInfo);
			}
		});
		
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询客户基本信息
		 */
		$("#queryCustInfoBtn").on('click',function(){
			QueryCustInfo.queryCustInfo();

			// 仅机构投资普通投资者 需要显示双录材料上传入口
            if(QueryCustInfo.custInfo.invstType == '0' && QueryCustInfo.custInfo.investorType == "0"){
				$("#uploadFileText").show();
				$("#uploadFileForm").show();
			} else {
                $("#uploadFileText").hide();
                $("#uploadFileForm").hide();
            }
			// 清空之前的双录文件
            $("#fileNameShow").val("");
            $("#fileNameShow").attr("filePath", "");
		});
		
		/**
		 * 查询基金基本信息
		 */
		$(".searchIcon").on('click blur',function(){
			if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
				CommonUtil.layer_tip("请先选择客户信息");
				return false;
			}
			BuyFund.queryProductInfo();
			if (!BuyFund.isAdviser) {
				$("#discountRate").attr('readonly', false);
				$("#originalFeeRate").val("");
				$("#discountRate").val("");
				QueryFundInfo.queryDiscount();
			}else {
				$("#discountRate").attr('readonly', true);
				$("#originalFeeRate").val("--");
				$("#discountRate").val("--");
			}
		});
		
		/**
		 * 计算费率
		 */
		$("#applyAmount").blur(function(){
			$("#applyAmount").val(CommonUtil.formatAmount($("#applyAmount").val()));
		});
		
		$("#applyAmount").change(function(){
			if (BuyFund.isAdviser) {
				$("#originalFeeRate").val("--");
				$("#discountRate").val("--");
				$("#discountRate").attr('readonly', true);
			}else {
				$("#discountRate").attr('readonly', false);
				BuyFund.calFundBuyFee();
			}
		});

        /**
		 * 上传文件 文件名展示处理
         */
        $("#videoFile").change(function(){
			BuyFund.videoFileChange();
        });

        /**
         * 上传文件
         */
        $("#fileSubmit").on('click',function(){
            BuyFund.fileUpload();
        });
	},

	queryProductInfo:function(fundCode){
		if(!fundCode){
			fundCode = $("#fundCode").val();
		}
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo) || {};
		console.info("queryProductInfo custInfoForm " + custInfoForm)
		var uri= TmsCounterConfig.QUERY_PRODUCT_INFO_URL ||  {};
		var reqparamters = {"productCode":fundCode, "custInfoForm": custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, BuyFund.queryProductInfoCallBack);
	},

	queryProductInfoCallBack:function(data){

		var productInfo = data.body || {};
		BuyFund.productInfo = productInfo;
		var isAdviser = productInfo.adviserFlag || false;
		BuyFund.isAdviser = isAdviser;
		if (isAdviser) {
			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}
			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}
			if($("#fundStatus").length > 0){
				$("#fundStatus").html(productInfo.isBuyOpen == '1' ? "可申购" : "暂停");
			}
			var adviserQuestRule = productInfo.adviserQuestRuleFlag || false;
			if (adviserQuestRule) {
				$("#questionAnswerDiv").show();
				$("#questionAnswer").val(productInfo.questionAnswer || '');
			}else {
				$("#questionAnswerDiv").hide();
				$("#questionAnswer").val('');
			}
		}else {
			$("#questionAnswerDiv").hide();
			$("#questionAnswer").val('');

			var isCommonFund = QueryFundInfo.checkFundInfo(productInfo);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, productInfo.fundStat));
			}

			if(!isCommonFund){
				return false;
			}
		}
		var queryAgreementDto = productInfo.queryAgreementDto;

		$("#queryAgreementId").html("");

		//匹配的概要
		var outlineList = queryAgreementDto.outlineList || [];
		BuyFund.showQueryAgreement(outlineList);
		//匹配基金合同
		var fundContractList = queryAgreementDto.fundContractList || [];
		BuyFund.showQueryAgreement(fundContractList);
		//匹配招募说明书
		var introduceList = queryAgreementDto.introduceList || [];
		BuyFund.showQueryAgreement(introduceList);

		if(outlineList.length <=0 && fundContractList <=0 && introduceList <=0){
			$("#queryAgreementMainId").hide();
		} else {
			$("#queryAgreementMainId").show();
		}
	},

	showQueryAgreement:function(list){
		if(list.length <=0){
			return;
		}
		$(list).each(function(index, element){
			//var appendHtml = '<a href="' + element.caFileContent + '">';
			var appendHtml = '<a>';
			appendHtml = appendHtml + element.caName + '</a>   ';
			$("#queryAgreementId").append(appendHtml);
		});
	},
	
	calFundBuyFee:function(){
		var  uri= TmsCounterConfig.CAL_BASE_FATE_URL ||  {};
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			$("#applyAmount").val();
			return false;
		}

		var applyAmount = $("#applyAmount").val() || '';
		applyAmount = CommonUtil.unFormatAmount(applyAmount);
		if(CommonUtil.isEmpty(applyAmount)){
			CommonUtil.layer_tip("请填写净申请金额");
			return false;
		}
		
		var paymentType = $("#paymentType").val();
		if(CommonUtil.isEmpty(paymentType)){
			//$("#applyAmount").val();
			CommonUtil.layer_tip("支付方式异常");
			return false;
		}
		

		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		if(CommonUtil.isEmpty(custInfoForm)){
			showMsg("请先选择客户信息");
			return false; 
		}
		
		var reqparamters = {};
		reqparamters.custInfoForm = custInfoForm;
		reqparamters.fundCode = fundCode;
		reqparamters.applyAmount = applyAmount;
		reqparamters.paymentType = paymentType;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, BuyFund.calFundBuyFeeCallBack);
	},
	
	calFundBuyFeeCallBack:function(data){
		var bodyData = data.body || {};
		var respData = bodyData.respData || [];
		var discount = bodyData.discount;
		$("#originalFeeRate").val(respData.feeRate || 1.0);
		$("#feeRate").val(respData.feeRate || 1.0);
		$("#discountRate").val(discount || 1.0);
	},
	
	validatorRetailRiskLevel:function(productInfoForm,custInfoForm){
		var uri = TmsCounterConfig.VALIDATOR_RETAIL_RISK_LEVEL_URL ||  {};
		var reqparamters ={"productInfoForm":productInfoForm,"custInfoForm":custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, BuyFund.validatorRetailRiskLevelCallBack);
		
	},
	
	validatorRetailRiskLevelCallBack:function(data){
		var uri = TmsCounterConfig.BUY_FUND_CONFIRM_URL ||  {};
		
		/**风险确认标记：1-确认，0-未确认*/
		var riskFlag = '0'; 
		
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var productInfoForm = JSON.stringify(BuyFund.productInfo);
		
		var buyConfirmForm = $("#buyConfirmForm").serializeObject();
		var bankAcct = $('#selectBank').find('option:selected').attr('bankacct');
		buyConfirmForm.bankAcct = bankAcct || '';
		buyConfirmForm.appAmt = CommonUtil.unFormatAmount(buyConfirmForm.appAmt);
		
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		var dealAppNo ="";
		if(!(typeof ApplyBuy == "undefined")){
			dealAppNo = ApplyBuy.checkOrder.dealAppNo;
		}

        var filePath = $("#fileNameShow").attr("filePath");

		if (BuyFund.isAdviser) {
			buyConfirmForm.discountRate = null;
			buyConfirmForm.originalFeeRate = null;
			console.info("confirm commit isAdviser " + BuyFund.isAdviser);

		}
		
		if(CommonUtil.isSucc(respCode)){
			buyConfirmForm.riskFlag = riskFlag;
			var reqparamters ={"dealAppNo":dealAppNo,"productInfoForm":productInfoForm,"buyConfirmForm": JSON.stringify(buyConfirmForm),"custInfoForm":custInfoForm,"transactorInfoForm":JSON.stringify(transactorInfoForm),"filePath":filePath};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, BuyFund.callBack);
		}else if ('Z3000023' == respCode){

			if(!BuyFund.isAdviser && '0' == QueryCustInfo.custInfo.custRiskLevel && BuyFund.productInfo.fundType != '3'){
				CommonUtil.layer_tip(respDesc);
			//2、客户风险等级为极低，不能购买除货币基金以外的基金
				CommonUtil.layer_tip("客户风险等级为极低，不能购买除货币基金之外的基金！");
				CommonUtil.enabledBtn("confimBuyBtn");
				return false;
			}
			
			layer.open({
	            title: ['风险提示', true],
	            type: 1,
	            area: ['320px', 'auto'],
	            btn: ['确定', '取消'],
	            skin: 'layui-layer-rim', 
	            btnAlign: 'l',
	            content: "产品风险高于户风险等级承受能力，确认继续吗",
	            yes: function (index, layero) { //或者使用btn1
	            	//layer.close(index);
	            	layer.closeAll();
	            	riskFlag = '1';
	            	buyConfirmForm.riskFlag = riskFlag; 
	        		var reqparamters ={"dealAppNo":dealAppNo,"productInfoForm":productInfoForm,"buyConfirmForm": JSON.stringify(buyConfirmForm),"custInfoForm":custInfoForm,"transactorInfoForm":JSON.stringify(transactorInfoForm),"filePath":filePath};
	        		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
	        		CommonUtil.ajaxAndCallBack(paramters, BuyFund.callBack);
	            },
	            cancel: function (index) { 
	            	CommonUtil.enabledBtn("confimBuyBtn");
	            }
	        }); 
		} else if('Z9999999' == respCode){
			CommonUtil.layer_tip("系统异常");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		} else if('Z3000005' == respCode){
			CommonUtil.layer_tip("客户被要求的风险评测未完成");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}else if('Z3000006' == respCode){
			CommonUtil.layer_tip("客户风险承受能力评测已过期");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}else {
			CommonUtil.layer_tip(respDesc);
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
	},
	
	/***
	 * 确认购买
	 */	
	confirm : function(productInfo){
		CommonUtil.disabledBtn("confimBuyBtn");
		
		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#buyConfirmForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
		var buyConfirmForm = $("#buyConfirmForm").serializeObject();
		
		var bankAcct = $('#selectBank').find('option:selected').attr('bankacct');
		buyConfirmForm.bankAcct = bankAcct || '';
		buyConfirmForm.appAmt = CommonUtil.unFormatAmount(buyConfirmForm.appAmt);
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var productInfoForm = JSON.stringify(productInfo);
		var isAdviser = productInfo.adviserFlag || false;

		BuyFund.productInfo = productInfo
		BuyFund.isAdviser = isAdviser

		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		
		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}

		if (isAdviser) {
			buyConfirmForm.discountRate = null;
			buyConfirmForm.originalFeeRate = null;

			var adviserQuestRule = BuyFund.productInfo.adviserQuestRuleFlag || false;
			if (adviserQuestRule && CommonUtil.isEmpty(buyConfirmForm.questionAnswer)) {
				CommonUtil.layer_tip("该投顾供应商配置了投顾问卷，投顾问卷答案不能为空");
				return false;
			}

		}else if(!CommonUtil.isEmpty(buyConfirmForm.discountRate) && !BuyValid.validDisCount(buyConfirmForm.discountRate)){
			CommonUtil.layer_tip("申请折扣率错误");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
		buyConfirmForm.appDtm = buyConfirmForm.appDt +'' + buyConfirmForm.appTm;
		if(!Valid.valiadTradeTime(buyConfirmForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}

		var filePath = $("#fileNameShow").attr("filePath");
		// 仅机构投资普通投资者 需要校验双录材料是否必填
		if(filePath == "" && QueryCustInfo.custInfo.invstType == '0' && QueryCustInfo.custInfo.investorType == "0"){
            CommonUtil.layer_tip("双录材料为空");
            CommonUtil.enabledBtn("confimBuyBtn");
            return false
		}

		BuyFund.validatorRetailRiskLevel(productInfoForm,custInfoForm);

	},
		
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			if($(".confimBtn").length > 0){
				CommonUtil.disabledBtnWithClass("confimBtn");
				CommonUtil.disabledBtn("abolishBtn");
			}
			CommonUtil.layer_tip("提交成功");

            // 清空之前的双录文件
            $("#fileNameShow").val("");
            $("#fileNameShow").attr("filePath", "");
		}else{
			CommonUtil.layer_tip("提交失败，"+respDesc);
		}
		
		if(!$(".confimBtn").length > 0){
			CommonUtil.enabledBtn("confimBuyBtn");
		}
	},

    videoFileChange: function () {
        var value = $("#videoFile").val();
        var fileName = value.substring(value.lastIndexOf("\\") + 1);
        $("#fileName").val(fileName);
        var width = parseInt(fileName.length) * 10;
        $("#fileName").css("width", width + "px");
    },

    fileUpload: function () {
        var target = $("#videoFile")[0];
        var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
        var fileTypeArray = new Array("wav", "avi", "wmv", "mpg", "mpeg", "mov", "rm", "ram", "swf", "flv", "mp4", "mp3", "wma", "avi", "rm", "rmvb", "flv", "mpg", "mkv", "m4a");
        var fileSize = 0;
        // 校验文件大小
        if (isIE && !target.files) {
            var filePath = target.value;
            var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
            var file = fileSystem.GetFile(filePath);
            fileSize = file.Size;
        } else {
            fileSize = target.files[0].size;
        }
        var size = fileSize / 1024;
        if (size > 300 * 1024) {
            CommonUtil.layer_tip("文件不能超过300M");
            return
        }
        // 校验文件格式
        var name = target.value;
        var fileType = name.substring(name.lastIndexOf(".") + 1).toLowerCase();
        if (fileTypeArray.indexOf(fileType) == -1) {
            CommonUtil.layer_tip("请选择音频/视频文件格式");
            return
        }

        var formData = new FormData();
        var appDt = $("#appDt").val();
        formData.append("userFile", target.files[0]);
        formData.append("appDt", appDt);

        var url = TmsCounterConfig.UPLOAD_DOUBLE_RECORD_FILE_URL;

        $.ajax({
            url: url,
            dataType:'json',
            type:'POST',
            data: formData,
            processData : false, // 使数据不做处理
            contentType : false, // 不要设置Content-Type请求头
            success: function(data){
                if (data.code == '0000') {
                    CommonUtil.layer_tip("上传成功");

                    var fileName = name.substring(name.lastIndexOf("\\") + 1);
                    $("#fileNameShow").val(fileName);

                    $("#fileNameShow").attr("filePath", data.body);
                } else {
                    CommonUtil.layer_tip("上传失败");
				}
            },
            error:function(response){
                CommonUtil.layer_tip("上传失败");
            }
        });
    },


	validateFund:function(productInfo){
		var appTm = $("#appTm").val();
		if(appTm === undefined || appTm.length === 0){
			CommonUtil.layer_tip("下单时间不能为空");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false
		}

		var flag = appTm >= productInfo.endTm && appTm <= CONSTANTS.HIGH_COUNTER_END_TIME
		if (productInfo.jointOpenDayRegion !== undefined && productInfo.jointOpenDayRegion.length !== 0 && flag){
			layer.open({
				title: ['风险提示', true],
				type: 1,
				area: ['320px', 'auto'],
				btn: ['确定', '取消'],
				skin: 'layui-layer-rim',
				btnAlign: 'l',
				content: productInfo.jointOpenDayRegion,
				yes: function (index, layero) {
					layer.closeAll();
					BuyFund.confirm(BuyFund.productInfo);
				},
				cancel: function (index) {
					CommonUtil.enabledBtn("confimBuyBtn");
				}
			});
			return false;
		}
		return true;
	},

};



