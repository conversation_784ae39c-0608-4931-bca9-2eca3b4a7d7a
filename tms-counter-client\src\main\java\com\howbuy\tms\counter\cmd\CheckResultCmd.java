/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.cmd;

import java.io.Serializable;

/**
 * @description:(柜台审核内容) 
 * <AUTHOR>
 * @date 2017年4月13日 下午3:57:47
 * @since JDK 1.6
 */
public class CheckResultCmd implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 申请金额或份额
     */
    private String appAmtOrVol;
    
    /**
     * 银行卡后四位
     */
    private String bankAcct;
    
    /**
     * 审核失败原因
     */
    private String checkFaildDesc;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getAppAmtOrVol() {
        return appAmtOrVol;
    }

    public void setAppAmtOrVol(String appAmtOrVol) {
        this.appAmtOrVol = appAmtOrVol;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getCheckFaildDesc() {
        return checkFaildDesc;
    }

    public void setCheckFaildDesc(String checkFaildDesc) {
        this.checkFaildDesc = checkFaildDesc;
    }

    
}

