/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.service.trade;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.UUIDUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelordervalidate.ForcedCancelOrderValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.cancelorder.forcedcancelordervalidate.ForcedCancelOrderValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcountervalidate.ModifyDivCounterValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.modifydiv.modifydivcountervalidate.ModifyDivCounterValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcountervalidate.NoTradeOverAccountCounterValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.notradeoveraccount.notradeoveraccountcountervalidate.NoTradeOverAccountCounterValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.redeem.bean.RedeemDetailBean;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcountervalidate.RedeemCounterValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcountervalidate.RedeemCounterValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.BaseShareMergeRequest.ShareMergeOutDetail;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergevalidate.HighShareMergeValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.sharemerge.sharemergevalidate.HighShareMergeValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfervalidate.HighShareTransferValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfervalidate.HighShareTransferValidateRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.bean.PayInfoBean;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcountervalidate.SubsOrPurCounterValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcountervalidate.SubsOrPurCounterValidateRequest;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:(柜台高端校验服务)
 * <AUTHOR>
 * @date 2017年4月17日 下午8:34:30
 * @since JDK 1.6
 */
@Service("tmsCounterValidService")
public class TmsCounterValidServiceImpl implements TmsCounterValidService {
    private static Logger logger = LogManager.getLogger(TmsCounterValidServiceImpl.class);
    
    @Autowired
    private RedeemCounterValidateFacade redeemCounterValidateFacade;
    @Autowired
    private  SubsOrPurCounterValidateFacade subsOrPurCounterValidateFacade;
    @Autowired
    private ModifyDivCounterValidateFacade modifyDivCounterValidateFacade;
    @Autowired
    private ForcedCancelOrderValidateFacade forcedCancelOrderValidateFacade;
    @Autowired
    private HighShareMergeValidateFacade highShareMergeValidateFacade;
    @Autowired
    private HighShareTransferValidateFacade highShareTransferValidateFacade;
    @Autowired
    private NoTradeOverAccountCounterValidateFacade noTradeOverAccountCounterValidateFacade;

    @Override
    public boolean cancelOrderValidate(CounterCancelReqDto dto, DisInfoDto disInfoDto) throws Exception {
        
        ForcedCancelOrderValidateRequest request = new ForcedCancelOrderValidateRequest();
        if (dto != null) {
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 订单号
            request.setDealNo(dto.getDealNo());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setForceCancelFlag(dto.getForceCancelFlag());
            // 撤单来源
            logger.debug("High | cancelOrderValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(forcedCancelOrderValidateFacade, request, disInfoDto);
        return true;

    }

    @Override
    public boolean redeemValidate(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception {

        RedeemCounterValidateRequest request = new RedeemCounterValidateRequest();
        if (dto != null) {
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            // 基金代码
            request.setFundCode(dto.getFundCode());
            // 份额类型：A-前收费；B-后收费
            request.setFundShareClass(dto.getFundShareClass());
            // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
            request.setProtocolType("4");
            //协议号
            // request.setProtocolNo(dto.getProtocolNo());
            // 默认不顺延
            request.setLargeRedeemFlag(dto.getLargeRedmFlag());
            // 赎回资金去向：0-赎回到银行卡；1-赎回到储蓄罐；默认0
            request.setRedeemCapitalFlag(dto.getRedeemCapitalFlag());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setConsCode(dto.getConsCode());
            //预约订单号
            request.setAppointmentDealNo(dto.getAppointmentDealNo());
            // 经办人证件号
            request.setTransactorIdNo(dto.getTransactorIdNo());
            // 经办人证件类型
            request.setTransactorIdType(dto.getTransactorIdType());
            // 经办人姓名
            request.setTransactorName(dto.getTransactorName());

            // 赎回明细
            List<RedeemDetailBean> redeemDetailList = new ArrayList<>(dto.getAppList().size());
            for (CounterRedeemReqDto.AppDetail app : dto.getAppList()) {
                RedeemDetailBean bean = new RedeemDetailBean();
                bean.setAppVol(app.getAppVol());
                bean.setCpAcctNo(app.getCpAcctNo());
                redeemDetailList.add(bean);
            }
            request.setRedeemDetailList(redeemDetailList);
            logger.debug("High | redeemValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(redeemCounterValidateFacade, request, disInfoDto);
        return true; 
    }

    @Override
    public boolean subsOrPurValidate(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception {

        SubsOrPurCounterValidateRequest request = new SubsOrPurCounterValidateRequest();
        if (dto != null) {
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            // 申请金额
            request.setAppAmt(dto.getApplyAmountIncluFee());
            // 风险确认标记：1-确认，0-未确认
            request.setRiskFlag(dto.getRiskFlag());
            // 基金代码
            request.setFundCode(dto.getFundCode());
            // 份额类型：A-前收费；B-后收费
            request.setFundShareClass(dto.getFundShareClass());
            // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
            request.setProtocolType("4");
            // 协议号
            request.setProtocolNo(null);
            request.setSubsAmt(dto.getSubsAmt());
            request.setEsitmateFee(dto.getEsitmateFee());
            request.setAppointmentDealNo(dto.getAppointmentDealNo());
            request.setDiscountRate(dto.getDiscountRate());
            //经办人证件号
            request.setTransactorIdNo(dto.getTransactorIdNo());
            //经办人证件类型
            request.setTransactorIdType(dto.getTransactorIdType());
            //经办人姓名
            request.setTransactorName(dto.getTransactorName());

            List<PayInfoBean> payList = new ArrayList<>(1);
            PayInfoBean payInfoBean = new PayInfoBean();
            // 资金账号
            payInfoBean.setCpAcctNo(dto.getCpAcctNo());
            payInfoBean.setPayAmt(dto.getApplyAmountIncluFee());
            // 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
            payInfoBean.setPaymentType(dto.getPaymentType());
            payList.add(payInfoBean);
            request.setPayList(payList);
            logger.debug("High | subsOrPurValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(subsOrPurCounterValidateFacade, request, disInfoDto);
        return true;

    }

    @Override
    public boolean modifyDivValidate(CounterModifyDivReqDto dto, DisInfoDto disInfoDto) throws Exception {

        ModifyDivCounterValidateRequest request = new ModifyDivCounterValidateRequest();
        if (dto != null) {
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            // 基金代码
            request.setFundCode(dto.getFundCode());
            // request
            request.setFundShareClass(dto.getFundShareClass());
            // 目标基金分红方式
            request.setDivMode(dto.getFundDivMode());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            // 经办人证件号
            request.setTransactorIdNo(dto.getTransactorIdNo());
            // 经办人证件类型
            request.setTransactorIdType(dto.getTransactorIdType());
            // 经办人姓名
            request.setTransactorName(dto.getTransactorName());
            logger.debug("High | modifyDivValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(modifyDivCounterValidateFacade, request, disInfoDto);
        return true;

    }

    @Override
    public boolean shareMergeVolValidate(CounterShareMergeVolReqDto dto, DisInfoDto disInfoDto) throws Exception {
        if(dto == null || CollectionUtils.isEmpty(dto.getShareMergeVolOrderList())){
            return false;
        }
        
        HighShareMergeValidateRequest request = new HighShareMergeValidateRequest();
        request.setTransactorIdNo(dto.getTransactorIdNo());
        request.setTransactorIdType(dto.getTransactorIdType());
        request.setTransactorName(dto.getTransactorName());
        request.setOperatorNo(dto.getOperatorNo());
        request.setConsCode(dto.getConsCode());
        request.setDisCode(dto.getDisCode());
        request.setOutletCode(dto.getOutletCode());
        // 外部订单号
        request.setExternalDealNo(UUIDUtil.uuid());
        // 交易账号
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setAppDt(dto.getAppDt());
        request.setAppTm(dto.getAppTm());
        request.setFundCode(dto.getFundCode());
        request.setFundShareClass(dto.getFundShareClass());
        // 转入资金账号
        request.setInCpAcctNo(dto.getInCpAcctNo());
        request.setInProtocolNo(dto.getInProtocolNo());
        request.setInProtocolType(dto.getInProtocolType());
        
        List<ShareMergeOutDetail> shareMergeOutDetail = new ArrayList<ShareMergeOutDetail>();
        if(CollectionUtils.isNotEmpty(dto.getShareMergeVolOrderList())){
            ShareMergeOutDetail outDetail = null;
            for(ShareMergeVolOrderReqDto reqDto : dto.getShareMergeVolOrderList()){
                outDetail = new ShareMergeOutDetail();
                outDetail.setCpAcctNo(reqDto.getCpAcctNo());
                outDetail.setBankAcct(reqDto.getBankAcct());
                outDetail.setBankCode(reqDto.getBankCode());
                outDetail.setAppVol(reqDto.getAppVol());
                outDetail.setProtocolNo(reqDto.getProtocolNo());
                outDetail.setProtocolType(reqDto.getProtocolType());

                shareMergeOutDetail.add(outDetail);
            }
        }
        request.setShareMergeOutDetail(shareMergeOutDetail);
        logger.debug("High | shareMergeVolValidate requset:{}", JSON.toJSONString(request));
        
        TmsFacadeUtil.executeThrowException(highShareMergeValidateFacade, request, disInfoDto);
        return true;
    }
    
    @Override
    public boolean shareTransferVolValidate(CounterShareMergeVolReqDto dto, DisInfoDto disInfoDto) throws Exception {
        
        if(dto == null || CollectionUtils.isEmpty(dto.getShareMergeVolOrderList())){
            return false;
        }
        
        HighShareTransferValidateRequest request = new HighShareTransferValidateRequest();
        request.setTransactorIdNo(dto.getTransactorIdNo());
        request.setTransactorIdType(dto.getTransactorIdType());
        request.setTransactorName(dto.getTransactorName());
        request.setOperatorNo(dto.getOperatorNo());
        request.setConsCode(dto.getConsCode());
        request.setDisCode(dto.getDisCode());
        request.setOutletCode(dto.getOutletCode());
        // 外部订单号
        request.setExternalDealNo(UUIDUtil.uuid());
        // 交易账号
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setAppDt(dto.getAppDt());
        request.setAppTm(dto.getAppTm());
        // 转入资金账号
        request.setInCpAcctNo(dto.getInCpAcctNo());
        ShareMergeVolOrderReqDto outDetail = dto.getShareMergeVolOrderList().get(0);
        // 转出资金账号
        request.setOutCpAcctNo(outDetail.getCpAcctNo());
        request.setOutBankAcct(outDetail.getBankAcct());
        request.setOutBankCode(outDetail.getBankCode());
        logger.debug("High | shareTransferVolValidate requset:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(highShareTransferValidateFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            return true;
        }else{
            if(baseResp != null){
                throw new TmsCounterException(baseResp.getReturnCode(), "高端公募："+baseResp.getDescription());
            } else{
                return false; 
            }
        }
    }

    @Override
    public boolean noTradeOverAccountValidate(CounterNoTradeOverAccountReqDto dto, DisInfoDto disInfoDto) throws Exception {

        NoTradeOverAccountCounterValidateRequest request = new NoTradeOverAccountCounterValidateRequest();
        if (dto != null) {
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(dto.getCpAcctNo());
            request.setDisCode(dto.getDisCode());
            // 转入交易账号
            request.setInTxAcctNo(dto.getInTxAcctNo());
            // 转入资金账号
            request.setInCpAcctNo(dto.getInCpAcctNo());
            request.setInDisCode(dto.getInDisCode());
            // 基金代码
            request.setFundCode(dto.getFundCode());
            // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
            request.setProtocolType("4");
            // 申请份额
            request.setAppVol(dto.getAppVol());
            logger.debug("High | noTradeOverAccountValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(noTradeOverAccountCounterValidateFacade, request, disInfoDto);
        return true;
    }

}
