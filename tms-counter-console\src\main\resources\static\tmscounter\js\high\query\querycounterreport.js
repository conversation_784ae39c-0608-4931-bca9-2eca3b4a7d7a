/**
*柜台交易统计查询
*<AUTHOR>
*@date 2017-04-11 16:17
*/

$(function(){
	QueryCounterReport.init();
});

var QueryCounterReport ={
	
	/**
	 * 初始化
	 */
	init:function(){
		QueryCounterReport.CounterEndTime = '150000';
		QueryCounterReport.queryCounterReport();
		
		$("#counterEndBtn").on('click',function(){
			QueryCounterReport.counterEnd();
		});
		
		$("#simuCounterEndBtn").on('click',function(){
			QueryCounterReport.simuCounterEnd();
		});
		
		QueryCounterReport.getCurrDate();
	},
	/**
	 * 获取当前服务器端日期
	 */
	getCurrDate:function(){
		var uri = TmsCounterConfig.QUERY_CURR_DATE_URL;
		var paramters = CommonUtil.buildReqParams(uri);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var respCode = data.code || '';
			var body = data.body || {};
			
			if(CommonUtil.isSucc(respCode)){
				QueryCounterReport.currDate = body.currDate || '';
				var currTime = CommonUtil.formatDateToStr(body.currDate, 'hhmmss');
				if(currTime > QueryCounterReport.CounterEndTime){
					CommonUtil.enabledBtn("counterEndBtn");
					CommonUtil.enabledBtn("simuCounterEndBtn");
				}else{
					CommonUtil.disabledBtn("counterEndBtn");
					CommonUtil.disabledBtn("simuCounterEndBtn");
				}
				console.log(currTime);
			}
		});
	},
	/**
	 * 柜台收市
	 */
	counterEnd:function(){
		
		var uri = TmsCounterConfig.QUERY_COUNTER_END_URL || {};
		var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var code = data.code;
			var desc = data.desc;
			if(CommonUtil.isSucc(code)){
				CommonUtil.layer_tip("收市成功");
			}else{
				CommonUtil.layer_tip("收市失败:"+desc+"("+code+")");
			}
		});
	},
	 /** 
	  * 私募柜台收市
	 */
	simuCounterEnd:function(){
		
		var uri = TmsCounterConfig.QUERY_SIMU_COUNTER_END_URL || {};
		var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			var code = data.code;
			var desc = data.desc;
			if(CommonUtil.isSucc(code)){
				CommonUtil.layer_tip("私募柜台收市成功");
			}else{
				CommonUtil.layer_tip("私募柜台收市失败:"+desc+"("+code+")");
			}
		});
	},
	/**
	 * 柜台交易查询
	 */
	queryCounterReport : function(){
			var  uri= TmsCounterConfig.QUERY_COUNTER_REPORT_URL ||  {};
			var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCounterReport.callBack);
	},

	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		var body = data.body || {};
		var reportList = body.counterTradeDtoList || []
		if(CommonUtil.isSucc(respCode)){
			 $("#buyAmt").html(CommonUtil.formatAmount(body.checkedBuyAmt, '--'));
			 $("#sellVol").html(CommonUtil.formatAmount(body.checkedRedeemVol, '--'));
			 $(reportList).each(function(index,element){
				var txCode = element.txCode;
				var trList = [];
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP, txCode, ''));
				trList.push(CommonUtil.formatData(element.totalAppNum,'--'));
				trList.push(CommonUtil.formatData(element.notCheckedNum,'--'));
				trList.push(CommonUtil.formatData(element.checkedNum,'--'));
				trList.push(CommonUtil.formatData(element.checkedFaildNum,'--'));
				trList.push(CommonUtil.formatData(element.checkedSuccNum,'--'));
				trList.push(CommonUtil.formatData(element.appSuccNum,'--'));
				trList.push(CommonUtil.formatData(element.appFaildNum,'--'));
				trList.push(CommonUtil.formatData(element.appWaitedNum,'--'));
				var trListHtml ='<td>' +trList.join('</td><td>')+'</td>';
				$("#"+txCode).html(trListHtml);
			});
			
		}else{
			CommonUtil.layer_tip(respCode+'('+respDesc+')');
		}
	}
};
