<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>交易申请查询</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 业务查询 <span class="c-gray en">&gt;</span> 交易申请查询 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">交易申请查询</p>
                <form action="" id="searchCheckForm">
                <div class="cp_top mt30">
                	<span class="normal_span ml30">业务类型：</span>
                    <span class="select-box inline">
                       <select name="txCode" id="selectTxCode" class="select">
                       </select>
                    </span>
                    
                    <span class="normal_span ml30">证件号：</span>
                    <input type="text"  placeholder="请输入" id="idNo" name="idNo">
                    
                </div>
                
                <div class="cp_top mt30">
                	<span class="normal_span ml30">审核状态：</span>
                    <span class="select-box inline">
                       <select name="checkFlag" id="selectCheckFlag" class="select">
                       </select>
                    </span>
                    
                    <span class="normal_span">客户号：</span>
                    <input type="text"  placeholder="请输入" id="custNo" name="custNo">
                </div>
                
                <div class="cp_top mt30">
                
                	<span class="normal_span">产品代码：</span>
                    <input type="text"  placeholder="请输入" id="productCode" name="productCode">
                    
                    <span class="normal_span ml30">产品通道：</span>
                    <span class="select-box inline">
                       <select name="productChannel" id="selectProductChannel" class="select">
                       </select>
                    </span>
                </div>
                
                </form>
                <div class="cp_top mt30">
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryBtn">查询</a>
                </div>
            </div>
        </div>
    </div>
    <div class="page-container">
        <p class="main_title">查询结果</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                  		<th>操作</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>证件号</th>
                        <th>业务类型</th>
                        <th>基金代码</th>
                        <th>基金简称</th>
                        <th>申请金额</th>
                        <th>申请份额</th>
                        <th>转让价格</th>
                        <th>过户份额对应的认缴金额</th>
                        <th>过户的总认缴金额</th>
                        <th>柜台订单号</th>
                        <th>申请日期</th>
                        <th>审核状态</th>
                    </tr>
               </thead>
                <tbody id="rsList">
                </tbody>
            </table>
        </div>
        <div class="clear page_all">
            <div class="fy_part fr mt10" id="pageView"></div>
        </div>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../js/common.js"></script>
    <script type="text/javascript" src="../../../js/config.js"></script>
    <script type="text/javascript" src="../../../js/commonutil.js"></script>
    <script type="text/javascript" src="../../../js/high/common/viewdealcommon.js"></script>
    <script type="text/javascript" src="../../../js/high/query/querycounterapp.js"></script>
</body>

</html>