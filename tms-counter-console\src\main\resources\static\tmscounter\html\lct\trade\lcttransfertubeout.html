<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>理财通转托管转出</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 理财通 <span class="c-gray en">&gt;</span> 投资交易类<span class="c-gray en">&gt;</span> 理财通转托管转出 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">转托管转出下单</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span ml30">证件号：</span>
                    <input name="idNo" id="idNo" type="text" placeholder='请输入'>
                    <span class="normal_span ml30">分销机构：</span>
                    <span class="select-box inline">
                       <select name="disCode" class="select" id="selectLctDisCode">
                           <option value="LCT00K001">理财通</option>
                       </select>
                    </span>
                    <a href="javascript:void(0)" id="queryCustInfoBtn" class="btn radius btn-secondary ml30">查询</a>
                </div>
            </div>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title mt30">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>                   
                        <th>风险等级</th>
                        <th>分销机构</th>                        
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30">录入订单信息</p>
        <form action="add" id="transfertubeOutForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                    <tr class="text-c">
                    	<td>业务类型</td>
                   	 	<td>
                   	 	 	<span class="select-box inline">
			           			<select name="transferTubeBusiType" id="transferTubeBusiType" class="select selectTransferTubeBusiType">
			              			<option value="1">跨市场</option>
			             		 	<option value="2">场外跨销售机构</option>
			           			</select>
			       			</span>
                   	 	</td>
                        <td>基金代码</td>
                        <td>
                            <div class="searchIn"><input type="text" id="fundCode" placeholder="请输入"><a href="javascript:void(0)" class="searchIcon"></a></div>
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>基金简称</td>
                        <td id="fundName" >--</td>
                        <td>基金状态</td>
                        <td id="fundStatus">--</td>
                    </tr>
                    <tr class="text-c">
                    	<td>总份额（份）</td>
                    	<td>
                            <input type="text" id="totalVol" name="totalVol" readonly="readonly">
                        </td>
                        <td>对方销售人代码</td>
                    	<td id="tSellerTD">
                            <!-- 当业务类型选择"1"时销售人代码只有两个选项：‘101-上海’、‘102-深圳’ ; "2"时输入框，但不能输入304-->
                            <span class="select-box inline">
			           			<select name="tSellerCode" id="tSellerCode" class="select">
			              			<option value="101">101-上海</option>
			             		 	<option value="102">102-深圳</option>
			           			</select>
			       			</span>
                        </td>
                    </tr>

                    <tr class="text-c">
                    	<td>对方销售人处投资者基金交易账号</td>
                    	<td>
                            <input type="text" id="tSellerTxAcctNo" name="tSellerTxAcctNo" placeholder="请输入" maxlength="30">
                        </td>
                        <td>对方网点/席位号</td>
                        <td>
                           <input type="text" id="tOutletCode" name="tOutletCode" placeholder="请输入" maxlength="6">
                        </td>
                    </tr>
                     <tr class="text-c">
                     	<td>下单日期</td>
                        <td>
                            <input class="input-text laydate-icon"  id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" >
                        </td>
                        <td>下单时间</td>
                        <td>
                            <input class="input-text laydate-icon" type="text" id="appTm" name="appTm" isnull="false" datatype="s" errormsg="下单时间" maxlength="6">
                        </td>
                     </tr>   
                </tbody>
            </table>
        </div>
       </form>
       
       <p class="main_title mt30">转出持仓份额信息</p>
       <!-- <div class="fl page_sj mt5" id="statics">转出笔数：xx笔；总转出份额：0.00份，大写：xx整</div>-->
       <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>基金代码</th>
                        <th>可用份额（份）</th> 
                        <th>冻结份额（份）</th>
                        <th>转出份额（份）</th>
                        <th>转出份额（大写）</th>
       					<th>银行名称</th>
                        <th>银行账号</th>
                         <th>协议号</th>
                   </tr>
               </thead>
                <tbody id="transOutCustBals">
                	 <tr class="text-c">
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
       
        </form>
         
         <p class="mt30">
            <a href="javascript:void(0)" id ="confimTransOutBtn" class="btn radius btn-secondary">确认提交</a>
        </p>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/lct/lcttransfertubeout.js?v=20191022"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfosubpage.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20181009"></script>
    <script type="text/javascript" src="../../../js/fund/query/queryfundinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/validate.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
</body>

</html>