/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;

import com.howbuy.tms.counter.dto.CounterPurchaseReqDto;

/**
 * @className BuyContext
 * @description
 * <AUTHOR>
 * @date 2019/6/10 14:20
 */
public class BuyContext extends TradeCommonContext{
    /**
     * 购买信息
     */
    private CounterPurchaseReqDto counterPurchaseReqDto;

    public CounterPurchaseReqDto getCounterPurchaseReqDto() {
        return counterPurchaseReqDto;
    }

    public void setCounterPurchaseReqDto(CounterPurchaseReqDto counterPurchaseReqDto) {
        this.counterPurchaseReqDto = counterPurchaseReqDto;
    }
}
