/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:(订单记录)
 * <AUTHOR>
 * @date 2017年4月1日 下午3:54:59
 * @since JDK 1.6
 */
public class OrderDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -7125845194931997337L;

    /**
     * 客户订单号
     */
    private String dealNo;
    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 分销机构
     */
    private String disCode;

    /**
     * 分销交易账号
     */
    private String disTxAcctNo;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 子交易账号
     */
    private String subTxAcctNo;

    /**
     * 银行账号
     */
    private String bankAcct;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 中台交易代码 Z310001-组合申购接口(买入) Z310002-组合赎回接口(卖出) Z310003-组合转投(拉杆平衡)
     * Z310004-组合调仓(波动平衡) Z310005-分红方式修改 Z310006-强制赎回 Z310007-分红 Z310008-强增
     * Z310009-强减
     */
    private String txCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 协议名称
     */
    private String protocolName;

    /**
     * 协议号
     */
    private String protocolNo;

    /**
     * 支付方式 01-自划款 04-代扣款 06-储蓄罐
     */
    private String paymentType;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 申请比例
     */
    private BigDecimal appRatio;

    /**
     * 申请日期
     */
    private String appDate;

    /**
     * 申请时间
     */
    private String appTime;

    /**
     * 申请日期时间
     */
    private Date appDtm;

    /**
     * 更新日期时间
     */
    private Date updateDtm;

    /**
     * 付款状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败
     */
    private String payStatus;

    /**
     * 订单状态 1-申请成功 2-部分确认 3-确认成功 4-确认失败 5-自行撤销 6-强制取消
     */
    private String orderStatus;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；5-App
     */
    private String txChannel;

    /**
     * 投资者类型 0-机构；1-个人
     *
     */
    private String invstType;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 数据跟踪
     */
    private String dataTrack;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    /**
     * 协议类型 1-普通公募基金投资 2-组合公募基金投资
     */
    private String protocolType;

    /**
     * 外部订单号
     */
    private String externalDealNo;

    /**
     * 预约标志：1-普通交易；2-预约交易
     */
    private String advanceFlag;
    /**
     * 订单类型：1-公募；2-高端；3-定期
     */
    private String dealType;

    /**
     * 渠道代码，101-自助渠道；102-预约渠道
     */
    private String channelCode;

    /**
     * 手续费
     */
    private BigDecimal feeRate;
    /**
     * 中台业务码
     */
    private String mBusiCode;

    private String fundShareClass;
    /**
     * 上报TA日期
     */
    private String submitTaDt;

    /**
     * 预约订单号
     */
    private String appointmentDealNo;
    /**
     * 产品通道
     */
    private String productChannel;

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getDisTxAcctNo() {
        return disTxAcctNo;
    }

    public void setDisTxAcctNo(String disTxAcctNo) {
        this.disTxAcctNo = disTxAcctNo;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getSubTxAcctNo() {
        return subTxAcctNo;
    }

    public void setSubTxAcctNo(String subTxAcctNo) {
        this.subTxAcctNo = subTxAcctNo;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getAppRatio() {
        return appRatio;
    }

    public void setAppRatio(BigDecimal appRatio) {
        this.appRatio = appRatio;
    }

    public String getAppDate() {
        return appDate;
    }

    public void setAppDate(String appDate) {
        this.appDate = appDate;
    }

    public String getAppTime() {
        return appTime;
    }

    public void setAppTime(String appTime) {
        this.appTime = appTime;
    }

    public Date getAppDtm() {
        return appDtm;
    }

    public void setAppDtm(Date appDtm) {
        this.appDtm = appDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTxChannel() {
        return txChannel;
    }

    public void setTxChannel(String txChannel) {
        this.txChannel = txChannel;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getDataTrack() {
        return dataTrack;
    }

    public void setDataTrack(String dataTrack) {
        this.dataTrack = dataTrack;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getExternalDealNo() {
        return externalDealNo;
    }

    public void setExternalDealNo(String externalDealNo) {
        this.externalDealNo = externalDealNo;
    }

    public String getAdvanceFlag() {
        return advanceFlag;
    }

    public void setAdvanceFlag(String advanceFlag) {
        this.advanceFlag = advanceFlag;
    }

    public String getDealType() {
        return dealType;
    }

    public void setDealType(String dealType) {
        this.dealType = dealType;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }
}
