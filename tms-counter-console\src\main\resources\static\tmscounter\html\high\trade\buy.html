<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css?v="+Math.random() />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>认申购</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易申请 <span class="c-gray en">&gt;</span> 认申购 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <input name="是否包含预约信息;默认false" id="isContainAppointmentFlag" type="hidden" value = "false">
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">认申购下单</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span ml30">证件号：</span>
                    <input name="idNo" id="idNo" type="text" placeholder='请输入'>
                    <span class="normal_span ml30">分销机构：</span>
                    <span class="select-box inline">
                       <select name="disCode" class="select" id="selectDisCode">
                       </select>
                    </span>
                    <a href="javascript:void(0)" id="queryCustInfoBtn" class="btn radius btn-secondary ml30">查询</a>
                </div>
            </div>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title mt30 cust_info" >客户基本信息</p>
        <div class="result2_tab cust_info" >
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>风险测评结果</th>
                        <th>开户分销机构</th>
                        <th>私募合格投资者认证</th>
                        <th>资管合格投资者认证</th>
                        <th>客户状态</th>
                        <th>投资者类型</th>
                        <th>协议回款方式</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30">客户预约信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>选择</th>
                        <th>预约单号</th>
                        <th>预约产品代码</th>
                        <th>预约产品名称</th>
                        <th>预约业务</th>
                        <th>预约日期</th>
                        <th>预约时间</th>
                        <th>预约金额(不含费)</th>
                        <th>预约份额</th>
                        <th>预约折扣</th>
                        <th>预约单状态</th>
                       <th>是否需要双录</th>
                       <th>双录状态</th>
                        <th>双录完成时间</th>
                        <th>是否首次实缴预约</th>
                        <th>认缴金额</th>
                    </tr>
               </thead>
                <tbody class="text-c" id="rsList">
                		<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                </tbody>
            </table>
             <div class="clear page_all">
            <div class="fy_part fr mt20" id="pageView"></div>
        </div>
        </div>

        <p class="main_title mt30" id="showMaterial">柜台材料信息</p>
           <div class="result2_tab" id="onLineMaterial">
           </div>

        <p class="main_title mt30">录入订单信息</p>
        <form action="" id="buyConfirmForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                
                	<tr class="text-c">
                        <td>TA代码</td>
                        <td id="taCodeId" class="readText">
                        <td>业务名称</td>
                        <td id="buyBusiTypeId" class="readText">
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>基金代码</td>
                        <td>
                            <div class="searchIn"><input type="text" id="fundCode" name="fundCode" placeholder="双击查询产品代码"><a href="javascript:void(0)" class="searchIcon"></a></div>
                        </td>
                        
                        <td>基金简称</td>
                        <td id="fundName"  class="readText">--</td>
                        
                    </tr>
                    
                     <tr class="text-c">
                        <td>产品通道</td>
                        <td id="productChannelId" class="readText">--</td>
                        <td>产品风险等级</td>
                        <td id="fundRiskLevel" class="readText">--</td>
                     </tr>
                    
                    <tr class="text-c">
                        <td>产品类型</td>
                        <td id="productTypeId" class="readText">
                        </td>
                        <td>基金状态</td>
                        <td id="fundStatus" class="readText">--</td>
                        
                    </tr>
                    
                    <tr class="text-c">
                        <td>收费方式</td>
                        <td id="shareClassId" class="readText">--</td>
                        <td>银行账号</td>
                        <td>
                            <span class="select-box inline">
                                <select name="cpAcctNo" class="select" id="selectBank" isnull="false" datatype="s" errormsg="银行卡">
                                    <option value="">请选择</option>
                                </select>
                            </span>
                        </td>
                    </tr>
                    
                    <tr class="text-c">
                        <td>净申请金额</td>
                        <td>
                           <div class="convertCon">
                               <input type="text" placeholder="请输入" id="applyAmount" class="applyAmount" name="appAmt" isnull="false" datatype="s" errormsg="净申请金额">
                               <input type="hidden" name="applyAmountIncluFee" id="applyAmountIncluFee" />
                           </div>
                        </td>
                        <td>大写净金额</td>
                        <td id = "convertAmtId" class="readText">
                        	
                        </td>
                    </tr>

                    <tr class="text-c">
                        <td>缴款金额</td>
                        <td>
                            <div class="convertCon">
                                <input type="text" readonly="readonly" id="payRatioAmount" class="payRatioAmount" name="payRatioAmount"  datatype="s" errormsg="缴款金额" />
                                <input type="hidden"  id="payRatioId" />
                            </div>
                        </td>
                        <td></td>
                        <td id = "" class="">
                        </td>
                    </tr>
                    
                    <tr class="text-c">
                        <td>首次最低购买净金额</td>
                        <td id="netMinAppAmtId" class="readText" >--</td>
                        <td>最低追加申请净金额</td>
                        <td id="netMinSuppleAmtId" class="readText">--</td>
                    </tr>
                    
                    
                    <tr class="text-c">
                        <td>申请折扣率</td>
                        <td>
                        	<input id ="discountRate"  class="discountRate" name="discountRate" type="text" datatype="s" errormsg="申请折扣率">
                        </td>
                        <td>申请总金额</td>
                        <td class="readText" id="applyAmountIncluFeeId"></td>
                        
                    </tr>
                    <tr class="text-c">
                   		 <td>标准费率</td>
                        <td>             
							<input id="originalFeeRate" class="originalFeeRate" name="originalFeeRate" type="text" datatype="s" errormsg="原始费率" readonly="readonly" >
							<input id="feeRate" class="feeRate" name="feeRate" type="hidden">
						</td>
                        <td>费用</td>
                        <td class="readText" id="feeId">
                        </td>
                    </tr>
                   
                     <tr class="text-c">
                        <td>预约开始日期</td>
                        <td class="readText appointinfo" id="appointStartDtId">
                        </td>
                       <td>开放开始日期</td>
                        <td class="readText appointinfo" id="openStartDtId">
                        </td>
                    </tr>
                    
                     <tr class="text-c">
                        <td>预约结束日期</td>
                        <td class="readText appointinfo" id="apponitEndDtId"></td>
                        
                        <td>开放结束日期</td>
                        <td class="readText appointinfo" id="openEndDtId"></td>
                     </tr>
                    
                     <tr class="text-c">
                     	<td>下单日期</td>
                        <td>
                            <input class="" id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" />
                        </td>
                        
                        <td>手续费计算方式</td>
                        <td class="readText" id="feeCalModeId"></td>
                     </tr>   
                     <tr class="text-c">
                    	 <td>下单时间</td>
                         <td>
                            <input class="" type="text" id="appTm" name="appTm" isnull="false" datatype="time" errormsg="下单时间" maxlength="6" value="" />
                         </td>
                         <td>支付方式</td>
                          <td>自划款<input type="hidden" name="paymentType" value="01"/></td>
                     </tr>
                     
                      <tr class="text-c">
                        <td>币种</td>
                        <td id="currencyId">
                       		 人民币
                        </td>
                          <td>认缴金额</td>
                          <td>
                              <div class="convertCon">
                                  <input type="text" placeholder="请输入" id="subsAmt" class="subsAmt" name="subsAmt" datatype="s" errormsg="认缴金额"/>
                              </div>
                          </td>
                    </tr>
                      <tr class="text-c" hidden id="redeemExpireTr">
                        <td id="redeemExpireDescId"></td>
                        <td id="redeemExpireTd">
                        </td>
                          <td></td>
                          <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
       </form>
        <p class="main_title mt30">其他信息</p>
        <form id="othetInfoForm">
        <div class="info">
            <span>网点：中台柜台</span>
            <span class="ml30">投资顾问代码：
                <span class="select-box inline">
                    <select name="consCode" class="select selectconsCode" >
                    </select>
                </span>
            </span>
            <span class="ml30">是否代理：
                <span class="select-box inline">
                   <select name="agentFlag" class="select selectAgened">
                      <option value="0">否</option>
                      <option value="1">是</option>
                   </select>
                </span>
            </span>
        </div>
       </form> 
       
       <form id="transactorInfoForm" style="display:none">
         <p class="main_title mt30">经办人信息</p>
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                 <tr class="text-c">
                        <td>经办人姓名：</td>
                        <td>
                       <input id ='transactorName' type="text" placeholder="请输入"  name="transactorName" isnull="false" datatype="s" errormsg="经办人姓名">
                       </td>
                        <td>经办人证件类型：</td>
              <td>
                  <span class="select-box inline">
                    <select id = 'transactorIdType' name="transactorIdType" class="select selectTransactorIdType"  isnull="false" datatype="s" errormsg="经办人证件类型" >
                    </select>
                </span>
                </td>
             <tr class="text-c">
              <td>经办人证件号：</td>
               <td> <input id='transactorIdNo' type="text" placeholder="请输入"  name="transactorIdNo" isnull="false" datatype="s" errormsg="经办人证件号" ></td>
                <td></td>
                 <td></td>
            </tbody>
          </table>
        </div>
         </form>
         
         <p class="mt30">
            <a href="javascript:void(0)" id ="confimBuyBtn" class="btn radius btn-secondary">确认提交</a>
        </p>
    </div>
    
	<input type="hidden" id="parentTargetFundId" />

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../lib/math/Math.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../js/common.js"></script>
    <script type="text/javascript" src="../../../js/config.js"></script>
    <script type="text/javascript" src="../../../js/commonutil.js"></script>
    <script type="text/javascript" src="../../../js/valid.js"></script>
    <script type="text/javascript" src="../../../js/high/common/init.js"></script>
    <script type="text/javascript" src="../../../js/high/conscode.js"></script>
    <script type="text/javascript" src="../../../js/high/query/querycustinfosubpage.js"></script>
    <script type="text/javascript" src="../../../js/high/query/queryhighproductinfosubpage.js"></script>
    <script type="text/javascript" src="../../../js/high/common/custinfo.js"></script>
    <script type="text/javascript" src="../../../js/high/query/queryhighproduct.js"></script>
    <script type="text/javascript" src="../../../js/high/common/onlineorderfile.js?v=3.6.22"></script>
    <script type="text/javascript" src="../../../js/high/common/appoint.js?v=3.5.28"></script>
    <script type="text/javascript" src="../../../js/high/trade/buy.js?v=********"></script>
</body>

</html>