package com.howbuy.tms.counter.fundservice.trade;

import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;

/**
 * @Description:高端产品预约日历信息查询
 * @Author: yun.lu
 * Date: 2025/6/3 15:01
 */
public interface HighProductAppointService {
    /**
     * 查询预约日历信息
     *
     * @param fundCode 产品编码
     * @param appDt    申请日期
     * @param appTm    申请时间
     * @param busiType 业务类型
     * @return 预约日历信息
     * @throws Exception
     */
    HighProductAppointmentInfoModel queryTradeProductAppointInfo(String fundCode, String appDt, String appTm, String busiType) throws Exception;

    /**
     * 查询购买上报日信息
     *
     * @param highProductBaseModel 产品基础信息
     * @param busiType             业务类型
     * @param appDt                申请日期
     * @param appTm                申请时间
     * @param disCode              分销渠道
     * @return 上报日
     */
    String getBuySubmitTaDt(HighProductBaseInfoModel highProductBaseModel, String busiType, String appDt, String appTm, String disCode) throws Exception;


    /**
     * 查询购买上报日信息
     * @param highProductBaseModel 产品基础信息
     * @param openStartDt 开始日期
     * @param appDt 申请日期
     * @return
     */
    String getBuySubmitTaDt(HighProductBaseInfoModel highProductBaseModel, String openStartDt, String appDt) ;
}
