package com.howbuy.tms.counter.dto;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/4 13:37
 * @since JDK 1.8
 */
public class CounterPortfolioRedeemReqDto implements Serializable {

    private static final long serialVersionUID = 3755207998639770448L;

    private String dealAppNo;
    private String txAcctNo;
    private String disCode;
    private String protocolNo;

    //NEW
    private String protocolType;

    private String productCode;
    //NEW
    private String productName;
    private String appRatio;
    private String cpAcctNo;
    private String customOrRatio;
    private String appDt;
    private String appTm;
    private String invstType;
    private String transactorIdNo;
    private String transactorIdType;
    private String transactorName;
    private String operatorNo;
    private String consCode;
    private String memo;
    private String creator;
    private String modifier;
    private Date createDtm;
    private Date updateDtm;
    private String custName;
    private String idNo;
    private String idType;
    private String bankAcct;
    private String bankCode;
    private String outletCode;
    private String checkFlag;
    private String appFlag;

    /**
     * 巨额赎回顺延 NEW
     */
    private String largeRedmFlag;

    /**
     * NEW
     */
    private String unusualTransType;

    /**
     * 赎回去向 0-储蓄罐（人工选择），1-回银行卡（人工选择），2-回银行卡（协议默认）,3-回储蓄罐（协议默认）
     * NEW
     */
    private String redeemCapitalFlag;

    /**
     * NEW
     */
    private String productClass;

    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是) NEW
     */
    private String agentFlag;

    /**
     * NEW
     */
    private String checker;

    /**
     * 开关
     */
    private String openFlag;


    private List<CounterPortfolioRedeemListDto> counterPortfolioRedeemListDtos;

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag;
    }

    public String getRedeemCapitalFlag() {
        return redeemCapitalFlag;
    }

    public void setRedeemCapitalFlag(String redeemCapitalFlag) {
        this.redeemCapitalFlag = redeemCapitalFlag;
    }

    public String getUnusualTransType() {
        return unusualTransType;
    }

    public void setUnusualTransType(String unusualTransType) {
        this.unusualTransType = unusualTransType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<CounterPortfolioRedeemListDto> getCounterPortfolioRedeemListDtos() {
        return counterPortfolioRedeemListDtos;
    }

    public void setCounterPortfolioRedeemListDtos(List<CounterPortfolioRedeemListDto> counterPortfolioRedeemListDtos) {
        this.counterPortfolioRedeemListDtos = counterPortfolioRedeemListDtos;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAppRatio() {
        return appRatio;
    }

    public void setAppRatio(String appRatio) {
        this.appRatio = appRatio;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getCustomOrRatio() {
        return customOrRatio;
    }

    public void setCustomOrRatio(String customOrRatio) {
        this.customOrRatio = customOrRatio;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }
}