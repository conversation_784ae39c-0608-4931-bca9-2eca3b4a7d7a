/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;
/**
 * @description:(无纸化柜台操作code) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年1月25日 下午2:32:53
 * @since JDK 1.6
 */
public class OperationCodes {
    /**
     * 开户并绑卡
     */
    public static final String SUBSCRIPTION_OPEN_ACCOUNT_AND_BINGING_CARD = "SUBSCRIPTION_OPEN_ACCOUNT_AND_BINGING_CARD";
    
    /**
     * 绑卡
     */
    public static final String SUBSCRIPTION_BINGING_CARD = "SUBSCRIPTION_BINGING_CARD";
    
    /**
     * 下单
     */
    public static final String SUBSCRIPTION_ORDER = "SUBSCRIPTION_ORDER";
    /**
     * 撤销
     */
    public static final String SUBSCRIPTION_CANCEL = "SUBSCRIPTION_CANCEL";
    
    /**
     * 驳回
     */
    public static final String SUBSCRIPTION_REJECT = "SUBSCRIPTION_REJECT";
    
}

