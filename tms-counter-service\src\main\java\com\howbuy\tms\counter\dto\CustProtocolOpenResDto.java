package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/6 13:52
 * @since JDK 1.8
 */
public class CustProtocolOpenResDto implements Serializable {

    private static final long serialVersionUID = -3248330318781727494L;

    /**
     * 交易业务码
     */
    private String txCode;
    /**
     * 协议开关 0 关，1 开
     */
    private String openFlag;

    /**
     * 自定义组合开关名称
     */
    private String custProtocolOpenName;

    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 下次开放日期
     */
    private String nextOpenDt;

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag;
    }

    public String getCustProtocolOpenName() {
        return custProtocolOpenName;
    }

    public void setCustProtocolOpenName(String custProtocolOpenName) {
        this.custProtocolOpenName = custProtocolOpenName;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getNextOpenDt() {
        return nextOpenDt;
    }

    public void setNextOpenDt(String nextOpenDt) {
        this.nextOpenDt = nextOpenDt;
    }
}