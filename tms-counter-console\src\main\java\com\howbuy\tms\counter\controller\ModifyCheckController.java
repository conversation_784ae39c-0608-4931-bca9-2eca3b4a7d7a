/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.counter.controller;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.ForceCancelFlagEnum;
import com.howbuy.tms.common.enums.busi.OpCheckNode;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.ModifyCounterOrderCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.enums.CheckTypeEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.service.trade.TmsCounterValidService;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(修改审核订单)
 * @date 2018年3月19日 上午9:36:41
 * @since JDK 1.6
 */
@Controller
public class ModifyCheckController {
    private Logger logger = LogManager.getLogger(ModifyCheckController.class);
    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private TmsCounterValidService tmsCounterValidService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    private static final String defTime = "150000";

    /**
     * modifyCheckOrderl:(修改审核订单)
     */
    @RequestMapping("/tmscounter/modifycheckorder.htm")
    public ModelAndView modifyCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        //审核类型
        String checkType = request.getParameter("checkType");
        //修改订单
        String modifyForm = request.getParameter("modifyForm");
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");

        ModifyCounterOrderCmd modifyCounterOrderCmd = JSON.parseObject(modifyForm, ModifyCounterOrderCmd.class);

        logger.info("ModifyCheckController|modifyCheckOrder|checkType:{}, modifyCounterOrderCmd:{}", checkType, JSON.toJSONString(modifyCounterOrderCmd));
        if ((modifyCounterOrderCmd == null || StringUtil.isEmpty(modifyCounterOrderCmd.getDealAppNo())) || StringUtils.isEmpty(checkType)) {
            logger.info("ModifyCheckController|modifyCheckOrder|counterOrderNo:{}, checkType:{} ", JSON.toJSONString(modifyCounterOrderCmd), checkType);
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        //获取柜台订单
        CounterOrderDto counterOrderDto = getCounterOrder(modifyCounterOrderCmd.getDealAppNo());
        if(counterOrderDto==null){
            logger.info("ModifyCheckController-modifyCheckOrder,根据申请单号查不到柜台订单,dealAppNo={} ", modifyCounterOrderCmd.getDealAppNo());
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(),"根据申请单号查不到柜台订单");
        }
        //创建人
        String creator = counterOrderDto.getCreator();
        if (isModify(checkType)) {
            //是否修改
            if (!creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_MODIFY_ERROR);
            }
        } else {
            // 无效操作
            throw new TmsCounterException(TmsCounterResultEnum.UN_KNOW_CHECK_ACTION);
        }
        // 查询产品信息
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(counterOrderDto.getFundCode());
        if (highProductBaseBean == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }
        if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
            counterOrderDto.setFundShareClass(highProductBaseBean.getShareClass());
            //修改信息校验
            if (!CheckFlagEnum.CHECK_MATERIAL_REJECT.getCode().equals(counterOrderDto.getCheckFlag()) && !validModifyInfo(modifyCounterOrderCmd, counterOrderDto)) {
                // 修改信息与原订单信息
                throw new TmsCounterException(TmsCounterResultEnum.NOT_MODIFY);
            }
        }

        AuditingOrderFileCmd auditingOrderFileCmd = null;
        if (!org.springframework.util.StringUtils.isEmpty(materialinfoForm)) {
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
        }

        // 校验
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        // 构建修改订单
        SubmitUncheckOrderDto submitUncheckOrderDto = buildSubmitUncheckOrderDto(checkType, counterOrderDto.getTxAcctNo(),
                modifyCounterOrderCmd, auditingOrderFileCmd, operatorInfoCmd, counterOrderDto.getTxCode());
        // 处理合并单订单信息
        submitUncheckOrderDto.setOrderFormMemo(genNewOrderFormMemo(modifyCounterOrderCmd, counterOrderDto));

        logger.info("modifyCheckOrder|submitUncheckOrderDto:{}", JSON.toJSONString(submitUncheckOrderDto));
        // 股权份额转让的不需要校验
        if (!TxCodes.OWNERSHIP_RIGHT_TRANSFER_TX_CODE.equals(counterOrderDto.getTxCode())) {
            validate(checkType, counterOrderDto, disInfoDto, submitUncheckOrderDto, auditingOrderFileCmd);
        }
        // 修改订单
        tmsCounterService.modifyCheckOrder(submitUncheckOrderDto, checkType, disInfoDto);
        modifyAuditingOrderFile(operatorInfoCmd, checkType, submitUncheckOrderDto, auditingOrderFileCmd, counterOrderDto.getMaterialId());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        WebUtil.write(response, rst);
        return null;
    }

    private String genNewOrderFormMemo(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) {
        // 处理赎回合并上报
        if (TxCodes.HIGH_COUNTER_REDEEM.equals(counterOrderDto.getTxCode())) {
            // 将参数中赎回卡列表转成map
            Map<String, BigDecimal> dtlMap = new HashMap<>(modifyCounterOrderCmd.getSubOrders().size());
            for (ModifyCounterOrderCmd.SubOrder app : modifyCounterOrderCmd.getSubOrders()) {
                dtlMap.put(app.getCpAcctNo(), app.getAppVol());
            }
            // 解析原订单信息
            CounterOrderFormDto formMemo = JSON.parseObject(counterOrderDto.getOrderFormMemo(), CounterOrderFormDto.class);
            for (CounterOrderFormDto.CounterCustBalanceVolDtlBean bean : formMemo.getDtlBeanList()) {
                BigDecimal appVol = dtlMap.get(bean.getCpAcctNo());
                if (appVol == null) {
                    throw new TmsCounterException(TmsCounterResultEnum.COUNTER_MERGE_SUBMIT_SUBORDER_ERROR);
                }
                bean.setAppVol(appVol);
                // 更新回款方向
                bean.setRedeemCapitalFlag(modifyCounterOrderCmd.getRedeemCapitalFlag());
                // 更新回款备注
                bean.setRefundFinaAvailMemo(modifyCounterOrderCmd.getRefundFinaAvailMemo());
                // 更新回可用余额金额
                bean.setRefundFinaAvailAmt(modifyCounterOrderCmd.getRefundFinaAvailAmt());
            }

            return JSON.toJSONString(formMemo);
        }
        return null;
    }

    /**
     * @param operatorInfoCmd
     * @param checkType
     * @param submitUncheckOrderDto
     * @param auditingOrderFileCmd
     * @return void
     * @Description 修改材料
     * <AUTHOR>
     * @Date 2019/6/26 17:23
     **/
    private void modifyAuditingOrderFile(OperatorInfoCmd operatorInfoCmd, String checkType,
                                         SubmitUncheckOrderDto submitUncheckOrderDto,
                                         AuditingOrderFileCmd auditingOrderFileCmd,
                                         String oldMaterialId) {
        // 修改显示资料状态
        if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
            if (auditingOrderFileCmd != null && !StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())) {
                // 修改
                tmsCounterOutService.auditingFile(operatorInfoCmd, auditingOrderFileCmd, submitUncheckOrderDto.getDealAppNo());
            }
        } else if (CheckTypeEnum.CHECK_CANCEL.getCode().equals(checkType) && StringUtils.isNotBlank(oldMaterialId)) {
            tmsCounterOutService.cancelFile(oldMaterialId);
        }

    }

    private void validate(String checkType, CounterOrderDto counterOrderDto, DisInfoDto disInfoDto, SubmitUncheckOrderDto submitUncheckOrderDto, AuditingOrderFileCmd auditingOrderFileCmd) throws Exception {
        if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
            if (auditingOrderFileCmd != null && !StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())) {
                // CRM线上资料ID
                submitUncheckOrderDto.setMaterialId(auditingOrderFileCmd.getOrderid());
                if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
                    // 修改
                    tmsCounterOutService.validateOrderFileStatus(auditingOrderFileCmd);

                }
            } else {
                if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
                    String hboneNo = tmsCounterOutService.queryHboneNoByTxAccountNo(submitUncheckOrderDto.getTxAcctNo());
                    QueryOrderFileContext queryOrderFileContext = new QueryOrderFileContext();
                    queryOrderFileContext.setHboneno(hboneNo);
                    queryOrderFileContext.setPcode(counterOrderDto.getFundCode());
                    queryOrderFileContext.setPreid(counterOrderDto.getAppointmentDealNo());
                    queryOrderFileContext.setBusiid(TmsCounterConstant.TXCODEANDCRMTRADETYPE.get(counterOrderDto.getTxCode()));
                    tmsCounterOutService.validateOrderFileExist(queryOrderFileContext, OpCheckNode.MODIFY.getCode());
                }
            }

            CounterOrderDto condition = new CounterOrderDto();
            condition.setTxCode(counterOrderDto.getTxCode());
            condition.setTxAcctNo(counterOrderDto.getTxAcctNo());
            condition.setFundCode(counterOrderDto.getFundCode());
            condition.setAppointmentDealNo(counterOrderDto.getAppointmentDealNo());
            condition.setDealAppNo(counterOrderDto.getDealAppNo());
            if (auditingOrderFileCmd != null) {
                condition.setMaterialId(auditingOrderFileCmd.getOrderid());
            }

            tmsCounterService.validateReplyOrder(condition, disInfoDto);

        }

    }

    private SubmitUncheckOrderDto buildSubmitUncheckOrderDto(String checkType, String txAcctNo, ModifyCounterOrderCmd modifyCounterOrderCmd,
                                                             AuditingOrderFileCmd auditingOrderFileCmd,
                                                             OperatorInfoCmd operatorInfoCmd, String txCode) {
        //修改订单
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        // 公共信息
        CommonUtil.setCommonOperInfo(operatorInfoCmd, submitUncheckOrderDto);
        if (modifyCounterOrderCmd != null) {
            // 柜台申请订单号
            submitUncheckOrderDto.setDealAppNo(modifyCounterOrderCmd.getDealAppNo());
            // 交易账号
            submitUncheckOrderDto.setTxAcctNo(txAcctNo);
            submitUncheckOrderDto.setTxCode(txCode);
            if (CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType)) {
                // 修改
                // 申请日期
                submitUncheckOrderDto.setAppDt(modifyCounterOrderCmd.getAppDt());
                // 申请时间
                submitUncheckOrderDto.setAppTm(modifyCounterOrderCmd.getAppTm());
                // 含手续费申请金额
                submitUncheckOrderDto.setAppAmt(modifyCounterOrderCmd.getApplyAmountIncluFee());
                // 费率
                submitUncheckOrderDto.setFeeRate(modifyCounterOrderCmd.getFeeRate());
                // 手续费
                submitUncheckOrderDto.setEsitmateFee(modifyCounterOrderCmd.getFee());
                // 折扣
                submitUncheckOrderDto.setDiscountRate(modifyCounterOrderCmd.getDiscountRate());
                if (TxCodes.HIGH_COUNTER_REDEEM.equals(txCode)) {
                    BigDecimal appVol = BigDecimal.ZERO;
                    for (ModifyCounterOrderCmd.SubOrder app : modifyCounterOrderCmd.getSubOrders()) {
                        appVol = appVol.add(app.getAppVol());
                    }
                    submitUncheckOrderDto.setAppVol(appVol);
                } else if (TxCodes.HIGH_COUNTER_NOTRADE_OVERACCOUNT.equals(txCode)) {
                    submitUncheckOrderDto.setAppVol(modifyCounterOrderCmd.getAppVol());
                    submitUncheckOrderDto.setSubsAmt(modifyCounterOrderCmd.getSubsAmt());
                    submitUncheckOrderDto.setFundCode(modifyCounterOrderCmd.getFundCode());
                } else {
                    // 申请份额
                    submitUncheckOrderDto.setAppVol(modifyCounterOrderCmd.getAppVol());
                }
                // 赎回去向
                submitUncheckOrderDto.setRedeemCapitalFlag(modifyCounterOrderCmd.getRedeemCapitalFlag());
                // 是否强制取消
                submitUncheckOrderDto.setForceCancelFlag(modifyCounterOrderCmd.getForceCancelFlag());
                // 巨额赎回顺延标识
                submitUncheckOrderDto.setLargeRedmFlag(modifyCounterOrderCmd.getLargeRedmFlag());
                // 异常标识
                submitUncheckOrderDto.setUnusualTransType(modifyCounterOrderCmd.getUnusualTransType());
                // 银行卡号
                submitUncheckOrderDto.setBankAcct(modifyCounterOrderCmd.getBankAcct());
                // 银行代码
                submitUncheckOrderDto.setBankCode(modifyCounterOrderCmd.getBankCode());
                // 撤单原因
                submitUncheckOrderDto.setCancelMemo(modifyCounterOrderCmd.getCancelMemo());
                // 转让价格
                submitUncheckOrderDto.setTransferPrice(modifyCounterOrderCmd.getTransferPrice());
                // 是否非交易转让标识
                submitUncheckOrderDto.setMatchedTransfer(modifyCounterOrderCmd.getIsNoTradeTransfer());

                // 复购类型
                submitUncheckOrderDto.setRepurchaseType(modifyCounterOrderCmd.getRepurchaseType());
                if (!StringUtils.isEmpty(modifyCounterOrderCmd.getCpAcctNo())) {
                    // 资金账号
                    submitUncheckOrderDto.setCpAcctNo(modifyCounterOrderCmd.getCpAcctNo());
                }
                // 到期是否赎回
                submitUncheckOrderDto.setIsRedeemExpire(modifyCounterOrderCmd.getIsRedeemExpire());
                // 预计到期时间
                submitUncheckOrderDto.setPreExpireDate(modifyCounterOrderCmd.getPreExpireDate());
                if (auditingOrderFileCmd != null) {
                    // 材料ID
                    submitUncheckOrderDto.setMaterialId(auditingOrderFileCmd.getOrderid());
                } else {
                    //材料ID
                    submitUncheckOrderDto.setMaterialId("");
                }
            }

        }
        //修改人
        submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
        //更新时间
        submitUncheckOrderDto.setUpdateDtm(new Date());

        return submitUncheckOrderDto;
    }

    /**
     * getCounterOrder:(获取柜台订单)
     *
     * @param dealAppNo
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月12日 下午4:30:19
     */
    private CounterOrderDto getCounterOrder(String dealAppNo) throws Exception {

        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(1);
        queryReqDto.setPageSize(1);
        CounterQueryOrderRespDto counterQueryOrderRespDto = tmsCounterService.counterQueryOrder(queryReqDto, null);

        List<CounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();
        if (CollectionUtils.isEmpty(counterOrderList)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_DEAL_NOT_EXIT);
        }

        if (CollectionUtils.isEmpty(counterOrderList)) {
            return null;
        }

        for (CounterOrderDto counterOrderDto : counterOrderList) {
            if (counterOrderDto.getDealAppNo().equals(dealAppNo)) {
                return counterOrderDto;
            }
        }

        return null;
    }

    /**
     * isModify:(是否修改)
     *
     * @param checkType
     * @return
     * <AUTHOR>
     * @date 2018年3月8日 下午5:13:05
     */
    private boolean isModify(String checkType) {
        return CheckTypeEnum.CHECK_CANCEL.getCode().equals(checkType) || CheckTypeEnum.CHECK_MODIFY.getCode().equals(checkType);
    }

    private boolean validModifyInfo(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {
        if (TxCodes.HIGH_COUNTER_PURCHASE.equals(counterOrderDto.getTxCode())) {
            // 购买校验
            return modiyBuyValid(modifyCounterOrderCmd, counterOrderDto);
        } else if (TxCodes.HIGH_COUNTER_REDEEM.equals(counterOrderDto.getTxCode())) {
            // 赎回校验
            return modiySellValid(modifyCounterOrderCmd, counterOrderDto);
        } else if (TxCodes.HIGH_COUNTER_MODIFYDIV.equals(counterOrderDto.getTxCode())) {
            // 修改分红方式校验
            modifyDivValid(modifyCounterOrderCmd, counterOrderDto);
        } else if (TxCodes.HIGH_COUNTER_CANCEL.equals(counterOrderDto.getTxCode()) ||
                TxCodes.HIGH_COUNTER_FORCE_CANCEL.equals(counterOrderDto.getTxCode())) {
            // 撤单校验
            modifyCancelValid(modifyCounterOrderCmd, counterOrderDto);
        }

        return true;
    }

    /**
     * modifyConmmonValid:(公共参数修改校验)
     *
     * @param modifyCounterOrderCmd
     * @param counterOrderDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年3月27日 下午5:50:48
     */
    private boolean modifyConmmonValid(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {
        if (modifyCounterOrderCmd == null || counterOrderDto == null) {
            logger.info("modifyCounterOrderCmd:{},counterOrderDto:{}", modifyCounterOrderCmd, counterOrderDto);
            return true;
        }
        // 公共校验
        if (counterOrderDto.getAppTm().equals(modifyCounterOrderCmd.getAppTm())) {
            //申请日期和申请时间相等
            throw new TmsCounterException(TmsCounterResultEnum.MODIFY_DATETIME_ERROR);
        }

        return true;
    }

    private boolean modifyDivValid(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {
        if (!CheckFlagEnum.CHECK_MATERIAL_REJECT.getCode().equals(counterOrderDto.getCheckFlag())) {
            // 公共校验
            if (!modifyConmmonValid(modifyCounterOrderCmd, counterOrderDto)) {
                //申请日期和申请时间
                return false;
            }
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        // 修改方式请求
        CounterModifyDivReqDto counterModifyDivReqDto = buildModifyDivReqDto(modifyCounterOrderCmd, counterOrderDto);
        // 修改分红方式校验
        tmsCounterValidService.modifyDivValidate(counterModifyDivReqDto, disInfoDto);
        return true;
    }

    private boolean modifyCancelValid(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {
        // 公共校验
        if (!CheckFlagEnum.CHECK_MATERIAL_REJECT.getCode().equals(counterOrderDto.getCheckFlag())) {
            if (!modifyConmmonValid(modifyCounterOrderCmd, counterOrderDto)) {
                //申请日期和申请时间
                return false;
            }
        }

        if (ForceCancelFlagEnum.YES.getCode().equals(modifyCounterOrderCmd.getForceCancelFlag())) {
            // 强制取消，必须输入撤单原因
            if (StringUtils.isEmpty(modifyCounterOrderCmd.getCancelMemo())) {
                throw new TmsCounterException(TmsCounterResultEnum.CANCEL_MEMO_CAN_NOT_NULL);
            }
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        // 撤单请求
        CounterCancelReqDto counterCancelReqDto = buildCancelReqDto(modifyCounterOrderCmd, counterOrderDto);
        // 撤单校验
        tmsCounterValidService.cancelOrderValidate(counterCancelReqDto, disInfoDto);
        return true;
    }

    private CounterCancelReqDto buildCancelReqDto(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) {
        CounterCancelReqDto counterCancelReqDto = new CounterCancelReqDto();
        // 订单号
        counterCancelReqDto.setDealNo(counterOrderDto.getDealNo());
        // 交易账号
        counterCancelReqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
        // 外部订单号
        counterCancelReqDto.setDealAppNo(counterOrderDto.getDealAppNo());
        return counterCancelReqDto;
    }

    public CounterModifyDivReqDto buildModifyDivReqDto(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {

        CounterModifyDivReqDto counterModifyDivReqDto = new CounterModifyDivReqDto();
        // 交易账号
        counterModifyDivReqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
        // 基金代码
        counterModifyDivReqDto.setFundCode(counterOrderDto.getFundCode());
        // request
        counterModifyDivReqDto.setFundShareClass(counterOrderDto.getFundShareClass());
        // 目标基金分红方式
        counterModifyDivReqDto.setFundDivMode(counterOrderDto.getFundDivMode());
        // 交易账号
        counterModifyDivReqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
        // 申请日期
        counterModifyDivReqDto.setAppDt(counterOrderDto.getAppDt());
        // 申请时间
        counterModifyDivReqDto.setAppTm(counterOrderDto.getAppTm());
        // 经办人证件号
        counterModifyDivReqDto.setTransactorIdNo(counterOrderDto.getTransactorIdNo());
        // 经办人证件类型
        counterModifyDivReqDto.setTransactorIdType(counterOrderDto.getTransactorIdType());
        // 经办人姓名
        counterModifyDivReqDto.setTransactorName(counterOrderDto.getTransactorName());
        return counterModifyDivReqDto;
    }

    /**
     * modiyBuyValid:(购买修改校验)
     *
     * @param modifyCounterOrderCmd
     * @param counterOrderDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年3月27日 下午6:11:05
     */
    private boolean modiyBuyValid(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {
        if (!CheckFlagEnum.CHECK_MATERIAL_REJECT.getCode().equals(counterOrderDto.getCheckFlag())) {
            // 购买修改校验
            if (!modifyConmmonValid(modifyCounterOrderCmd, counterOrderDto)
                    && counterOrderDto.getAppAmt().compareTo(modifyCounterOrderCmd.getApplyAmountIncluFee()) == 0
                    && counterOrderDto.getCpAcctNo().equals(modifyCounterOrderCmd.getCpAcctNo())
                    && modifyBuyValidExpireInfo(modifyCounterOrderCmd, counterOrderDto)) {
                //申请金额和银行卡
                return false;
            }

        }


        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        // 购买请求
        CounterPurchaseReqDto counterPurchaseReqDto = buildBuyReqDto(modifyCounterOrderCmd, counterOrderDto);
        // 购买校验
        tmsCounterValidService.subsOrPurValidate(counterPurchaseReqDto, disInfoDto);
        return true;
    }

    /**
     * modifyBuyValidExpireInfo:到期信息校验
     *
     * @param modifyCounterOrderCmd
     * @param counterOrderDto
     * @return
     * <AUTHOR>
     * @date 2018年10月17日 下午5:22:57
     */
    private boolean modifyBuyValidExpireInfo(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) {
        String modifyIsRedeemExpire = modifyCounterOrderCmd.getIsRedeemExpire() == null ? "" : modifyCounterOrderCmd.getIsRedeemExpire();
        String dtoIsRedeemExpire = counterOrderDto.getIsRedeemExpire() == null ? "" : counterOrderDto.getIsRedeemExpire();
        String modifyPreExpireDate = modifyCounterOrderCmd.getPreExpireDate() == null ? "" : modifyCounterOrderCmd.getPreExpireDate();
        String dtoPreExpireDate = counterOrderDto.getPreExpireDate() == null ? "" : counterOrderDto.getPreExpireDate();
        if (modifyIsRedeemExpire.equals(dtoIsRedeemExpire) && modifyPreExpireDate.equals(dtoPreExpireDate)) {
            return true;
        }
        return false;
    }

    /**
     * buildBuyReqDto:(组装购买请求)
     *
     * @param modifyCounterOrderCmd
     * @param counterOrderDto
     * @return
     * <AUTHOR>
     * @date 2018年4月8日 上午9:16:57
     */
    private CounterPurchaseReqDto buildBuyReqDto(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) {

        CounterPurchaseReqDto counterPurchaseReqDto = new CounterPurchaseReqDto();
        // 申请日期
        counterPurchaseReqDto.setAppDt(modifyCounterOrderCmd.getAppDt());
        // 申请时间
        counterPurchaseReqDto.setAppTm(modifyCounterOrderCmd.getAppTm());
        // 资金账号
        counterPurchaseReqDto.setCpAcctNo(modifyCounterOrderCmd.getCpAcctNo());
        // 交易账号
        counterPurchaseReqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
        // 申请金额
        counterPurchaseReqDto.setAppAmt(modifyCounterOrderCmd.getApplyAmountIncluFee());
        // 含费申请金额
        counterPurchaseReqDto.setApplyAmountIncluFee(modifyCounterOrderCmd.getApplyAmountIncluFee());
        // 认缴金额
        counterPurchaseReqDto.setSubsAmt(modifyCounterOrderCmd.getSubsAmt());
        // 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
        counterPurchaseReqDto.setPaymentType(counterOrderDto.getPaymentType());
        // 手续费
        counterPurchaseReqDto.setEsitmateFee(modifyCounterOrderCmd.getFee());
        // 折扣率
        counterPurchaseReqDto.setDiscountRate(modifyCounterOrderCmd.getDiscountRate());
        // 风险确认标记：1-确认，0-未确认
        counterPurchaseReqDto.setRiskFlag(counterOrderDto.getRiskFlag());
        // 基金代码
        counterPurchaseReqDto.setFundCode(counterOrderDto.getFundCode());
        // 份额类型：A-前收费；B-后收费
        counterPurchaseReqDto.setFundShareClass(counterOrderDto.getFundShareClass());
        // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
        counterPurchaseReqDto.setProtocolType(ProtocolTypeEnum.HIGH_FUND.getCode());
        // 协议号
        counterPurchaseReqDto.setProtocolNo(null);
        // 投顾预约订单号
        counterPurchaseReqDto.setAppointmentDealNo(counterOrderDto.getAppointmentDealNo());
        //经办人证件号
        counterPurchaseReqDto.setTransactorIdNo(counterOrderDto.getTransactorIdNo());
        //经办人证件类型
        counterPurchaseReqDto.setTransactorIdType(counterOrderDto.getTransactorIdType());
        //经办人姓名
        counterPurchaseReqDto.setTransactorName(counterOrderDto.getTransactorName());
        return counterPurchaseReqDto;
    }

    private boolean modiySellValid(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) throws Exception {
        if (!CheckFlagEnum.CHECK_MATERIAL_REJECT.getCode().equals(counterOrderDto.getCheckFlag())) {
            if (defTime.compareTo(modifyCounterOrderCmd.getAppTm()) < 0) {
                //修改的申请时间需要小于150000
                throw new TmsCounterException(TmsCounterResultEnum.DATETIME_BIG_ERROR);
            }
            // 赎回修改校验
            if (!modifyConmmonValid(modifyCounterOrderCmd, counterOrderDto)
                    && counterOrderDto.getAppVol().compareTo(modifyCounterOrderCmd.getAppVol()) == 0) {
                //申请金额和银行卡
                return false;
            }
        }
        // 赎回请求
        CounterRedeemReqDto counterRedeemReqDto = buildSellReqDto(modifyCounterOrderCmd, counterOrderDto);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        // 赎回校验
        tmsCounterValidService.redeemValidate(counterRedeemReqDto, disInfoDto);
        return true;
    }

    /**
     * buildSellReqDto:(构建赎回请求)
     *
     * @param modifyCounterOrderCmd
     * @param counterOrderDto
     * @return
     * <AUTHOR>
     * @date 2018年4月8日 上午9:28:07
     */
    private CounterRedeemReqDto buildSellReqDto(ModifyCounterOrderCmd modifyCounterOrderCmd, CounterOrderDto counterOrderDto) {
        CounterRedeemReqDto counterRedeemReqDto = new CounterRedeemReqDto();
        // 申请日期
        counterRedeemReqDto.setAppDt(counterOrderDto.getAppDt());
        // 申请时间
        counterRedeemReqDto.setAppTm(counterOrderDto.getAppTm());
        // 交易账号
        counterRedeemReqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
        // 资金账号
        counterRedeemReqDto.setCpAcctNo(counterOrderDto.getCpAcctNo());
        // 基金代码
        counterRedeemReqDto.setFundCode(counterOrderDto.getFundCode());
        // 份额类型：A-前收费；B-后收费
        counterRedeemReqDto.setFundShareClass(counterOrderDto.getFundShareClass());
        // 协议类型：1-普通公募协议；2-普通公募智能投顾协议；3-暴力定投协议；4-高端公募协议
        counterRedeemReqDto.setProtocolType(ProtocolTypeEnum.HIGH_FUND.getCode());
        // 申请份额
        counterRedeemReqDto.setAppVol(modifyCounterOrderCmd.getAppVol());
        // 巨额赎回顺延标识
        counterRedeemReqDto.setLargeRedmFlag(counterOrderDto.getLargeRedmFlag());
        // 赎回资金去向：0-赎回到银行卡；1-赎回到储蓄罐；默认0
        counterRedeemReqDto.setRedeemCapitalFlag(counterOrderDto.getRedeemCapitalFlag());
        // 交易账号
        counterRedeemReqDto.setTxAcctNo(counterOrderDto.getTxAcctNo());
        counterRedeemReqDto.setConsCode(counterOrderDto.getConsCode());
        //预约订单号
        counterRedeemReqDto.setAppointmentDealNo(counterOrderDto.getAppointmentDealNo());
        // 经办人证件号
        counterRedeemReqDto.setTransactorIdNo(counterOrderDto.getTransactorIdNo());
        // 经办人证件类型
        counterRedeemReqDto.setTransactorIdType(counterOrderDto.getTransactorIdType());
        // 经办人姓名
        counterRedeemReqDto.setTransactorName(counterOrderDto.getTransactorName());

        List<CounterRedeemReqDto.AppDetail> appList = new ArrayList<>(modifyCounterOrderCmd.getSubOrders().size());
        for (ModifyCounterOrderCmd.SubOrder sub : modifyCounterOrderCmd.getSubOrders()) {
            CounterRedeemReqDto.AppDetail app = new CounterRedeemReqDto.AppDetail();
            app.setCpAcctNo(sub.getCpAcctNo());
            app.setAppVol(sub.getAppVol());
            appList.add(app);
        }
        counterRedeemReqDto.setAppList(appList);
        return counterRedeemReqDto;
    }
}
