var Validate = {
	validateTransactorInfo:function(transactorInfoForm,custInfo){
		if(!custInfo){
			CommonUtil.layer_tip("请选择用户");
			return false;
		}
		
		/**
		 * 1、机构用户 0 选择代理 1 必须填写经办人信息
		 * 2、机构用户 必须选择代理
		 */
		if('0' == custInfo.invstType || '2' == custInfo.invstType){
			if('1' == transactorInfoForm.agentFlag){
				if(CommonUtil.isEmpty(transactorInfoForm.transactorName)){
					CommonUtil.layer_tip("机构、产品用户必须填写经办人姓名信息");
					return false;
				}
				
				if(CommonUtil.isEmpty(transactorInfoForm.transactorIdNo)){
					CommonUtil.layer_tip("机构、产品用户必须填写经办人证件号信息");
					return false;
				}
				
				if(CommonUtil.isEmpty(transactorInfoForm.transactorIdType)){
					CommonUtil.layer_tip("机构、产品用户必须填写经办人证件类型信息");
					return false;
				}
				
				var validTransactorRst = Valid.valiadateFrom($("#transactorInfoForm"));
				if(!validTransactorRst.status){
					CommonUtil.layer_tip(validTransactorRst.msg);
					CommonUtil.enabledBtn("confimBuyBtn");
					return false;
				}
				
				if('0' == transactorInfoForm.transactorIdType && !Valid.id_rule(transactorInfoForm.transactorIdNo)){
					CommonUtil.layer_tip("经办人证件号格式不正确");
					CommonUtil.enabledBtn("confimBuyBtn");
					return false;
				}
				
			} else {
				CommonUtil.layer_tip("机构、产品用户必选代理");
				return false;
			}
		} else if ('0' == transactorInfoForm.agentFlag && '0' != custInfo.invstType){
			/**
			 * 个人用户 选择非经办不用填写经办人信息
			 */
			if(!CommonUtil.isEmpty(transactorInfoForm.transactorName)){
				CommonUtil.layer_tip("个人用户不需要填写经办人姓名信息");
				return false;
			}
			
			if(!CommonUtil.isEmpty(transactorInfoForm.transactorIdNo)){
				CommonUtil.layer_tip("个人用户不需要填写经办人证件号信息");
				return false;
			}
			
			if(!CommonUtil.isEmpty(transactorInfoForm.transactorIdType)){
				CommonUtil.layer_tip("个人用户不需要填写经办人证件类型信息");
				return false;
			}
		} else if ('1' == transactorInfoForm.agentFlag && '0' != custInfo.invstType){
			/**
			 * 个人用户选择经办为是时，请填写经办人信息
			 */
			if(CommonUtil.isEmpty(transactorInfoForm.transactorName)){
				CommonUtil.layer_tip("个人用户选择经办为是时，请填写经办人姓名信息");
				return false;
			}
			
			if(CommonUtil.isEmpty(transactorInfoForm.transactorIdNo)){
				CommonUtil.layer_tip("个人用户选择经办为是时，请填写经办人证件号信息");
				return false;
			}
			
			if(CommonUtil.isEmpty(transactorInfoForm.transactorIdType)){
				CommonUtil.layer_tip("个人用户选择经办为是时，请填写经办人证件类型信息");
				return false;
			}
			
			var validTransactorRst = Valid.valiadateFrom($("#transactorInfoForm"));
			if(!validTransactorRst.status){
				CommonUtil.layer_tip(validTransactorRst.msg);
				CommonUtil.enabledBtn("confimBuyBtn");
				return false;
			}
			
			if('0' == transactorInfoForm.transactorIdType && !Valid.id_rule(transactorInfoForm.transactorIdNo)){
				CommonUtil.layer_tip("经办人证件号格式不正确");
				CommonUtil.enabledBtn("confimBuyBtn");
				return false;
			}
		}
		
		return true;
	}
} 