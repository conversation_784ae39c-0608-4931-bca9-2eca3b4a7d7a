/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularservice.trade;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.ProductTypeEnum;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.enums.database.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.database.CounterCheckFlagEnum;
import com.howbuy.tms.common.enums.database.ZBusiCodeEnum;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.BooleanFlagReverseEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.RegularSubmitUncheckOrderDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.regular.batch.facade.trade.submitcheckorder.SubmitCheckOrderFacade;
import com.howbuy.tms.regular.batch.facade.trade.submitcheckorder.SubmitCheckOrderRequest;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.subspurchase.oragnize.SubsPurchaseOragnizeFacade;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.subspurchase.oragnize.SubsPurchaseOragnizeRequest;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.subspurchase.oragnize.SubsPurchaseOragnizeResponse;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.withdraw.CancelOrderFacade;
import com.howbuy.tms.regular.orders.facade.trade.trustreceipt.withdraw.CancelOrderRequest;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:(定期订单服务类)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年6月22日 下午3:34:49
 * @since JDK 1.6
 */
@Service
public class RegularOrderService {

    private static final Logger logger = LogManager.getLogger(RegularOrderService.class);

    @Autowired
    private SubsPurchaseOragnizeFacade subsPurchaseOragnizeFacade;

    @Autowired
    private CancelOrderFacade cancelOrderFacade;

    @Autowired
    private SubmitCheckOrderFacade submitCheckOrderFacade;

    /**
     * 
     * subsOrPurCounter:(柜台审核购买)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:28:28
     */
    public boolean subsOrPurCounter(RegularSubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        //SubsOrPurCounterRequest request = new SubsOrPurCounterRequest();
        SubsPurchaseOragnizeRequest request = new SubsPurchaseOragnizeRequest();
        if (submitUncheckOrderDto != null) {
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            request.setProtocolType(submitUncheckOrderDto.getProtocolType());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            // 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
            request.setPaymentType(submitUncheckOrderDto.getPaymentType());
            // 申请金额
            request.setAppAmt(submitUncheckOrderDto.getAppAmt());
            // 风险确认标记：1-确认，0-未确认
            request.setRiskFlag(submitUncheckOrderDto.getRiskFlag());
            // 产品代码
            request.setProductCode(submitUncheckOrderDto.getProductCode());
            // 份额类型：A-前收费；B-后收费
            //request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            // 协议号
            request.setProtocolNo(null);
            //request.setDiscount(submitUncheckOrderDto.getDiscountRate());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setProductType(ProductTypeEnum.REGULAR.getCode());
            request.setProtocolNo("");
            //不需要校验密码
            request.setTxPasswordValidateFlag(BooleanFlagReverseEnum.NO.getCode());
            request.setTxPwd("");
            request.setzBusiCode(ZBusiCodeEnum.BUY.getCode());
            request.setZdtlBusiCode("");
            request.setDataTrack("");
            request.setOperIp(submitUncheckOrderDto.getOperIp());

        }
        logger.info("request:{}", JSON.toJSONString(request));
        BaseResponse baseResp = TmsFacadeUtil.execute(subsPurchaseOragnizeFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                SubsPurchaseOragnizeResponse subsOrPurCounterResponse = (SubsPurchaseOragnizeResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(subsOrPurCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                // submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_FAILD.getKey());
                // String memo = new StringBuilder(baseResp.getDescription() ==
                // null ? "" :
                // baseResp.getDescription()).append("(").append(baseResp.getReturnCode())
                // .append(")").toString();
                // submitUncheckOrderDto.setMemo(memo);
                // checkCounterOrder(submitUncheckOrderDto, disInfoDto,
                // CounterCheckFlagEnum.CANCEL.getKey());
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    public boolean cancelOrder(RegularSubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String cancelType) throws Exception {
        CancelOrderRequest request = new CancelOrderRequest();
        if (submitUncheckOrderDto != null) {
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 订单号
            request.setDealNo(submitUncheckOrderDto.getDealNo());
            // 是否需要校验校验密码(柜台撤单不需要校验校验密码)
            request.setNeedValidateTxPwdFlag(BooleanFlagReverseEnum.NO.getCode());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            // 撤单类型
            request.setCancelFlag(cancelType);
        }
        logger.info("request:{}", JSON.toJSONString(request));
        BaseResponse baseResp = TmsFacadeUtil.execute(cancelOrderFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    /**
     * 
     * checkCounterOrder:(更新订单审核状态)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:30:17
     */
    public void checkCounterOrder(RegularSubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String checkFlag) throws Exception {
        logger.info("submitUncheckOrderDto:{}", JSON.toJSONString(submitUncheckOrderDto));
        SubmitCheckOrderRequest request = new SubmitCheckOrderRequest();
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        request.setCheckFlag(checkFlag);
        request.setOrderReturnCode(submitUncheckOrderDto.getReturnCode());
        request.setOrderReturnMsg(submitUncheckOrderDto.getDescription());
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setMemo(submitUncheckOrderDto.getMemo());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setChecker(submitUncheckOrderDto.getChecker());
        request.setCheckDtm(submitUncheckOrderDto.getCheckDtm());
        logger.info("request:{}", JSON.toJSONString(request));
        TmsFacadeUtil.executeThrowException(submitCheckOrderFacade, request, disInfoDto);
    }

}
