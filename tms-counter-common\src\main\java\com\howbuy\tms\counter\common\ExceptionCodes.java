package com.howbuy.tms.counter.common;

/**
 * @className ExceptionCodes
 * @description <br>
 *              异常码；必须严格按照如下规则定义异常码 <br>
 *              异常码规则：3位系统编码 + 1位异常类型 + 4位异常码 <br>=
 *              ================================== <br>
 *              系统编码： <br>
 *              Z51 simu-reallocate <br>
 *              异常类型：系统 0，业务类 1 <br>=
 *              ================================== <br>
 *              例子：批量中心参数错误-********
 * 
 * <AUTHOR>
 * @date 2015-4-9 下午1:29:53
 * 
 */
public class ExceptionCodes {
    /**
     * 成功
     */
    public static final String SUCCESS = "********";
    /**
     * 参数错误 （工具类校验专用错误码）
     */
    public static final String PARAMS_ERROR = "********";

    /**
     * 已达系统处理上限
     */
    public static final String SYSTEM_LIMIT_HANDLE_ERROR = "********";
    /**
     * 未设置可用的数据源
     */
    public final static String NOT_SET_AVAILABLE_DATASOURCE = "********";
    
    /**
     * 未知错误
     */
    public final static String UNEXCEPTED_ERROR = "********";

    /**
     * 请求处理中
     */
    public final static String IN_EXECUTING = "********";

    /**
     * 业务异常
     */
    public final static String  BUS_EXCEPTION = "********";
    
    /**
     * 私募中心参数错误通用异常
     */
    public static final String SIME_REALLOCATE_PARAM_ERROR = "*********";
    /**
     * 私募中心未找到相关记录异常
     */
    public static final String SIME_REALLOCATE_NOT_FOUND_RECORDER_ERROR = "*********";
    
    /**
     * 私募订单校验出错
     */
    public static final String SIME_REALLOCATE_SIMU_ORDER_VALIDATION_ERROR = "*********";
    /**
     * 群济系统返回出错
     */
    public static final String SIME_REALLOCATE_CALL_QUNJI_ERROR = "Z51100010";
    
    /**
     * 中台系统返回出错
     */
    public static final String SIME_REALLOCATE_CALL_ZHONGTAI_ERROR="Z51100011";

    /**
     * 产品风险高于客户承受风险能力.
     */
    public static final String PRODUCT_RISK_OVER_CUST_RISK = "Z3000023";
}
