/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller;

import com.howbuy.tms.batch.facade.trade.counterendprecheck.CounterEndPreCheckResponse;
import com.howbuy.tms.common.enums.database.BusinessProcessingStepEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.batch.facade.query.querycounterendcheck.QueryUnFinishRepurchaseProtocolFacade;
import com.howbuy.tms.high.batch.facade.query.querycounterendcheck.QueryUnFinishRepurchaseProtocolRequest;
import com.howbuy.tms.high.batch.facade.query.querycounterendcheck.QueryUnFinishRepurchaseProtocolResponse;
import com.howbuy.tms.high.batch.facade.query.querycounterendcheck.bean.UnFinishRepurchaseProtocolBean;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.QueryTaBusinessBatchCountResponse;
import com.howbuy.tms.high.batch.facade.query.querytabusinessbatchcount.bean.TaBusinessBatchFlowBean;
import com.howbuy.tms.high.batch.facade.query.querytacounternotend.QueryTaCountNotEndResponse;
import com.howbuy.tms.high.batch.facade.query.querytacounternotend.bean.TaCounterNotEndBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @className QueryUnFinishRepurchaseController
 * @description 查询未完成复购协议
 * <AUTHOR>
 * @date 2019/8/19 17:40
 */
@Controller
public class QueryUnFinishRepurchaseController {
    @Autowired
    @Qualifier(value = "tmscounter.queryUnFinishRepurchaseProtocolFacade")
    private QueryUnFinishRepurchaseProtocolFacade queryUnFinishRepurchaseProtocolFacade;

    @Autowired
    private TmsCounterService tmsCounterService;

    @RequestMapping("tmscounter/high/queryunfinishrepurchase.htm")
    public void queryUnFinishRepurchase(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String workDay = tmsCounterService.getHighSystemWorkDay();
        List<String> taCodes = getCounterEndTa();
        List<UnFinishRepurchaseProtocolBean> unFinishRepurchaseProtocolList = new ArrayList<>();

        if(!CollectionUtils.isEmpty(taCodes)){
            QueryUnFinishRepurchaseProtocolRequest queryRequest = new QueryUnFinishRepurchaseProtocolRequest();
            queryRequest.setTaCodes(taCodes);
            queryRequest.setRedeemAppointEndDt(workDay);
            TmsFacadeUtil.doFillBaseRequest(queryRequest, null);
            QueryUnFinishRepurchaseProtocolResponse queryResponse = queryUnFinishRepurchaseProtocolFacade.execute(queryRequest);

            if(!TmsFacadeUtil.isSuccess(queryResponse)){
                throw new TmsCounterException(queryResponse.getReturnCode(), queryResponse.getDescription());
            }
            unFinishRepurchaseProtocolList = queryResponse.getUnFinishRepurchaseProtocolList();
        }
        CounterEndPreCheckResponse preCheckResponse = tmsCounterService.queryCounterEndPreCheck(SysCodeEnum.BATCH_HIGH.getCode());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("rsList", unFinishRepurchaseProtocolList);
        body.put("preCheckList", preCheckResponse.getCheckResultList());
        rst.setBody(body);
        WebUtil.write(response, rst);
    }

    private List<String> getCounterEndTa() throws Exception{
        QueryTaBusinessBatchCountResponse counterEndTA = tmsCounterService.queryTaBusinessBatchCount(BusinessProcessingStepEnum.BPS_COUNTER_DAY_CLOSE.getCode());
        List<TaBusinessBatchFlowBean> taList = counterEndTA.getList();
        QueryTaCountNotEndResponse notNeedCounterEnd = tmsCounterService.queryTaCounterNotEnd();

        Set<String> notNeedCounterEndSet = new HashSet<>();
        if(!CollectionUtils.isEmpty( notNeedCounterEnd.getList())){
            for (TaCounterNotEndBean taCounterNotEndBean : notNeedCounterEnd.getList()) {
                notNeedCounterEndSet.add(taCounterNotEndBean.getTaCode());
            }
        }

        List<String> counterEndTaList = new ArrayList<>();
        for(TaBusinessBatchFlowBean taBusinessBatchFlowBean : taList){
            if(!notNeedCounterEndSet.contains(taBusinessBatchFlowBean.getTaCode())){
                counterEndTaList.add(taBusinessBatchFlowBean.getTaCode());
            }

        }

        return counterEndTaList;
    }
}
