/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;

/**
 * @description:(查询客户持有银行卡信息) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月31日 下午2:58:54
 * @since JDK 1.6
 */
public class QueryCustBankCardRespDto extends BaseResponseDto{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -8537719249692349266L;

    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 银行卡号
     */
    private String bankAcct;
    /**
     * 银行编号
     */
    private String bankCode;
    /**
     * 分行代码
     */
    private String bankRegionCode;
    /**
     * 分行名称
     */
    private String bankRegionName;
    /**
     * 银行账户名称
     */
    private String bankAcctName;
    /**
     * 银行账户状态0-正常；1-待审核；2-注销；3-冻结；4-销户待确认；5-冻结待确认
     */
    private String bankAcctStatus;
    /**
     * 银行卡验证状态 2-验证通过，3-验证失败
     */
    private String bankAcctVrfyStat;
    /**
     * 开户行省份编码
     */
    private String provCode;
    /**
     * 开户行城市编码
     */
    private String cityCode;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 代扣签约状态 1-未开通；2-开通；3-关闭
     */
    private String paySign;
    /**
     * 代扣签约日期
     */
    private String paySignDt;
    /**
     * 银行预留手机
     */
    private String mobileBank;
    /**
     * 手机验证状态 0-已验证；1-未验证
     */
    private String mobileVrfyStat;
    public String getTxAcctNo() {
        return txAcctNo;
    }
    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }
    public String getCpAcctNo() {
        return cpAcctNo;
    }
    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }
    public String getBankAcct() {
        return bankAcct;
    }
    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }
    public String getBankCode() {
        return bankCode;
    }
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }
    public String getBankRegionCode() {
        return bankRegionCode;
    }
    public void setBankRegionCode(String bankRegionCode) {
        this.bankRegionCode = bankRegionCode;
    }
    public String getBankRegionName() {
        return bankRegionName;
    }
    public void setBankRegionName(String bankRegionName) {
        this.bankRegionName = bankRegionName;
    }
    public String getBankAcctName() {
        return bankAcctName;
    }
    public void setBankAcctName(String bankAcctName) {
        this.bankAcctName = bankAcctName;
    }
    public String getBankAcctStatus() {
        return bankAcctStatus;
    }
    public void setBankAcctStatus(String bankAcctStatus) {
        this.bankAcctStatus = bankAcctStatus;
    }
    public String getBankAcctVrfyStat() {
        return bankAcctVrfyStat;
    }
    public void setBankAcctVrfyStat(String bankAcctVrfyStat) {
        this.bankAcctVrfyStat = bankAcctVrfyStat;
    }
    public String getProvCode() {
        return provCode;
    }
    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }
    public String getCityCode() {
        return cityCode;
    }
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
    public String getBankName() {
        return bankName;
    }
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    public String getPaySign() {
        return paySign;
    }
    public void setPaySign(String paySign) {
        this.paySign = paySign;
    }
    public String getPaySignDt() {
        return paySignDt;
    }
    public void setPaySignDt(String paySignDt) {
        this.paySignDt = paySignDt;
    }
    public String getMobileBank() {
        return mobileBank;
    }
    public void setMobileBank(String mobileBank) {
        this.mobileBank = mobileBank;
    }
    public String getMobileVrfyStat() {
        return mobileVrfyStat;
    }
    public void setMobileVrfyStat(String mobileVrfyStat) {
        this.mobileVrfyStat = mobileVrfyStat;
    }
    
}

