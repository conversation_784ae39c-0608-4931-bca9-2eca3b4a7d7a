/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundservice.out.impl;

import com.howbuy.tms.batch.facade.query.queryfundtxacct.QueryFundTxAcctFacade;
import com.howbuy.tms.batch.facade.query.queryfundtxacct.QueryFundTxAcctRequest;
import com.howbuy.tms.batch.facade.query.queryfundtxacct.QueryFundTxAcctResponse;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryfunddivmode.QueryFundDivModeContext;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryfunddivmode.QueryFundDivModeOuterService;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryfunddivmode.QueryFundDivModeResult;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.QueryFundInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.ConsultantBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.DisChannelBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundActiDiscountRatioBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.context.QueryFundActiRateDiscountParams;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.CounterModifyDivRespDto;
import com.howbuy.tms.counter.dto.FundDivDto;
import com.howbuy.tms.counter.dto.ValidatorRetailRiskLevelDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.fundservice.out.TmsFundCounterOutService;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * @description:(中台柜台外部服务)
 * <AUTHOR>
 * @date 2017年4月12日 上午10:30:25
 * @since JDK 1.6
 */
@Service("tmsFundCounterOutService")
public class TmsFundCounterOutServiceImpl implements TmsFundCounterOutService {
    private final static Logger LOGGER = LoggerFactory.getLogger(TmsFundCounterOutServiceImpl.class);

    @Autowired
    @Qualifier("queryFundInfoOuterService")
    QueryFundInfoOuterService queryFundInfoOuterService;

    @Autowired
    @Qualifier("queryFundDivModeOuterService")
    private QueryFundDivModeOuterService queryFundDivModeOuterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private QueryFundTxAcctFacade queryFundTxAcctFacade;

    @Override
    public FundProductFeeRateBean getFundFeeRateByAmt(String midProductId, String busiCode, String invstType, String shareClass, BigDecimal appAmt) {
        FundProductFeeRateBean model = queryFundInfoOuterService.getFundFeeRateByAmt(midProductId, busiCode, invstType, shareClass, appAmt);
        return model;
    }

    @Override
    public List<ConsultantBean> getConsInfo(String outletCode) {
        return queryFundInfoOuterService.getAllConsultant(outletCode);
    }

    @Override
    public List<DisChannelBean> selectAllDisChannel() {
        return queryFundInfoOuterService.selectAllDisChannel();
    }

    @Override
    public ValidatorRetailRiskLevelDto validatorRetailRiskLevel(String txAcctNo, String invstType, String qualificationType, String riskFlag,
            String productRiskLevel) throws Exception {
        try {
            LOGGER.info("11111111111111");
            ValidatorRetailRiskLevelDto dto = new ValidatorRetailRiskLevelDto();
            dto.setReturnCode("Z0000000");
            dto.setDescription("验证成功");
            return dto;
        } catch (ValidateException e) {
            LOGGER.error("",e);
            throw new TmsCounterException(e.getErrorCode(), e.getErrorDesc(),e);
        } catch (Exception e) {
            throw new TmsCounterException("Z9999999", "系统异常",e);
        }
    }

    @Override
    public CounterModifyDivRespDto queryFundDiv(String custNo, String fundCode, DisInfoDto disInfoDto,String fundTxAcctNo) throws Exception {
        CounterModifyDivRespDto counterModifyDivRespDto = null;
        String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
        FundInfoAndNavBean fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(fundCode, workDay);
        if (null != fundInfoAndNavBean) {
            QueryFundDivModeContext ctx = new QueryFundDivModeContext();
            ctx.setTxAcctNo(custNo);
            ctx.setFundCode(fundCode);
            ctx.setFundShareClass(fundInfoAndNavBean.getFundShareClass());
            ctx.setFundTxAcctNo(fundTxAcctNo);
            QueryFundDivModeResult result = queryFundDivModeOuterService.getFundDivMode(ctx);
            if (result != null) {
                counterModifyDivRespDto = new CounterModifyDivRespDto();
                FundDivDto fundDiv = new FundDivDto();
                fundDiv.setDivMode(result.getDivMode());
                fundDiv.setAllowModifyDivMode("1");
                fundDiv.setFundAttr(fundInfoAndNavBean.getFundAttr());
                fundDiv.setShareClass(fundInfoAndNavBean.getFundShareClass());
                fundDiv.setFundShareClass(fundInfoAndNavBean.getFundShareClass());
                fundDiv.setFundStat(fundInfoAndNavBean.getFundStat());
                fundDiv.setFundCode(fundInfoAndNavBean.getFundCode());
                fundDiv.setFundType(fundInfoAndNavBean.getFundType());
                fundDiv.setTaCode(fundInfoAndNavBean.getTaCode());
                counterModifyDivRespDto.setFundDivDto(fundDiv);

                if(!CollectionUtils.isEmpty(result.getFundDivModeList())){
                    List<FundDivDto> fundDivDtoList = result.getFundDivModeList().stream().map(mode -> {
                        FundDivDto fundDivDto = new FundDivDto();
                        fundDivDto.setDivMode(mode.getDivMode());
                        fundDivDto.setAllowModifyDivMode("1");
                        fundDivDto.setFundShareClass(fundInfoAndNavBean.getFundShareClass());
                        fundDivDto.setFundAttr(fundInfoAndNavBean.getFundAttr());
                        fundDivDto.setShareClass(fundInfoAndNavBean.getFundShareClass());
                        fundDivDto.setFundStat(fundInfoAndNavBean.getFundStat());
                        fundDivDto.setFundCode(fundInfoAndNavBean.getFundCode());
                        fundDivDto.setFundType(fundInfoAndNavBean.getFundType());
                        fundDivDto.setTaCode(fundInfoAndNavBean.getTaCode());
                        fundDivDto.setFundTxAcctNo(mode.getFundTxAcctNo());
                        // 设置基金交易账号信息
                        setFundTxAcctInfo(custNo,mode.getFundTxAcctNo(),fundDivDto);
                        return fundDivDto;
                    }).collect(Collectors.toList());
                    counterModifyDivRespDto.setFundDivDtoList(fundDivDtoList);
                }else {
                    counterModifyDivRespDto.setFundDivDtoList(Collections.singletonList(fundDiv));
                }

            }
        }
        return counterModifyDivRespDto;
    }

    /**
     * 获取协议号
     * @param custNo
     * @param fundTxAcctNo
     * @return
     */
    private void setFundTxAcctInfo(String custNo, String fundTxAcctNo,FundDivDto fundDivDto) {
        QueryFundTxAcctRequest request = new QueryFundTxAcctRequest();
        request.setTxAcctNo(custNo);
        request.setFundTxAcctNo(fundTxAcctNo);

        QueryFundTxAcctResponse resp = (QueryFundTxAcctResponse)TmsFacadeUtil.execute(queryFundTxAcctFacade, request, null);
        if(resp != null ){
            fundDivDto.setProtocolNo(resp.getProtocolNo());
            fundDivDto.setCpAcctNo(resp.getCpAcctNo());
            fundDivDto.setDisCode(resp.getDisCode());
            fundDivDto.setProtocolType(resp.getProtocolType());
        }
    }

    @Override
    public BigDecimal getActiRateDiscount(String productId, String shareClass, String paymentType, String invstType, String busiCode, String bankCode,
            String disCode, BigDecimal appAmt) {
        QueryFundActiRateDiscountParams ctx = new QueryFundActiRateDiscountParams();
        ctx.setAppAmt(appAmt);
        ctx.setBankCode(bankCode);
        ctx.setBusiCode(busiCode);
        ctx.setDisCode(disCode);
        ctx.setInvstType(invstType);
        ctx.setPaymentType(paymentType);
        ctx.setProductId(productId);
        ctx.setShareClass(shareClass);
        FundActiDiscountRatioBean result = queryFundInfoOuterService.getActiRateDiscount(ctx);
        if (result != null) {
            return result.getActualDiscount();
        }
        return null;
    }

}
