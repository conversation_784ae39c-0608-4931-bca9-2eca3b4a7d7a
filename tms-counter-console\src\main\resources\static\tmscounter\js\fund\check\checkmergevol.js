$(function(){
	Init.init();
	$("#returnBtn").on('click',function(){
		CheckMerge.confirm(CounterVolCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckMerge.confirm(CounterVolCheck.Succ);
	});
	
	var checkId = CommonUtil.getParam("checkId");// 预申请单号
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");


	CheckMerge.checkOrder = {};	 
	CheckMerge.init(checkId,custNo,disCode,idNo);
});

var CheckMerge = {	

	init:function(checkId, custNo, disCode,idNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		
		// 设置转出和转入信息
		QueryCheckOrder.queryMergeTransCheckOrderById(checkId, CheckMerge.queryCheckVolOrderByIdBack);
	}, 
	
	queryCheckVolOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckMerge.checkOrder = bodyData.checkOrder || {};
		CheckMerge.checkDtlOrder = bodyData.checkDtlOrder || [];

		if(CommonUtil.isEmpty(CheckMerge.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckMerge.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		
		// 转出信息
		BodyView.setTransOutTableView("checkOutInfo", CheckMerge.checkDtlOrder, CheckMerge.checkOrder.disCode);
		// 转入信息
		BodyView.setTransInTableView("checkInInfo", CheckMerge.checkOrder);
		/**other*/
		BodyView.setCheckOperInfoView(CheckMerge.checkOrder);


	},
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		
		var uri= TmsCounterConfig.CHECK_MERGE_TRANS_CONFIRM_URL ||  {};
		
		if(CounterVolCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入退回原因");
				return false;
			}
			CheckMerge.checkFaildDesc = $("#checkFaildDesc").val();
		}
		
		var reqparamters ={"checkFaildDesc":CheckMerge.checkFaildDesc || '',
				"checkStatus":checkStatus,
				"checkedOrderForm":JSON.stringify(CheckMerge.checkOrder),
				//"checkDtlOrderForm":JSON.stringify(CheckMerge.checkDtlOrder)
				};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckMerge.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	}
}
