/**
*份额迁移
*<AUTHOR>
*@date 2018-05-103 14:39
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
    // url 参数
    TransVol.urlParams = CommonUtil.getParamJson() || {};
    // 初始化CRM参数数据
    OnLineOrderFile.initCrmUrlWithOutAppointAndSelectCustInfo(TransVol.urlParams, TransVol.initCustQuery);
    Init.initDisCodeTrans();
    TransVol.init();
    TransVol.transOutFunds = [];
    TransVol.transInBank = {};
    // 转出基金中是否存在税延基金
	TransVol.existTaxDelayFundFlag = false;

});
var fundCustBooksCount = 0;
var TransVol = {
	init:function(){
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询客户基本和持仓信息
		 */
		$("#queryCustBalInfoBtn").on('click',function(){
			TransVol.queryCustBalInfo();
		});
		
		$("#reset").on('click',function(){
			CommonUtil.clearForm("searchForm");
		});
		
		/**
		 * 确认提交
		 */
		$("#confimSubmitBtn").on('click',function(){
			TransVol.confirm();
		});

        /**
		 * 是否注销银行卡select
         */
        $("#selectCancelCard").on('click',function(){
        	var value = $(this).val();
        	if(value == '1'){
        		$("#alertSpanId").show();
                $("#assetBox").prop("checked",true);
				//所有资产全部选中且不可编辑
				$("input[name ='checkHigh']").prop("checked",true);
				$("input[name ='checkHigh']").prop("disabled",true);
				// $("#checkFundBox").prop("checked",true);
				// $("#checkFundBox").prop("disabled",true);
				$("#checkHighBox").prop("checked",true);
				$("#checkHighBox").prop("disabled",true);
			}else{
        		$("#alertSpanId").hide();
                $("#assetBox").prop("checked",false);
                $("input[name ='checkHigh']").prop("checked",false);
                $("input[name ='checkHigh']").prop("disabled",false);
                // $("#checkFundBox").prop("checked",false);
                // $("#checkFundBox").prop("disabled",false);
                $("#checkHighBox").prop("checked",false);
                $("#checkHighBox").prop("disabled",false);
			}
		});
	},

    initCustQuery:function(hboneNo){
        Init.initDisCodeAll();

        // 查询客户基本信息
        // 转出卡
        $("#bankAcct").val(TransVol.urlParams["outBankAcct"]);
        $("#hboneNo").val(hboneNo);

        QueryCustInfo.queryBasicCustInfo();

        TransVol.queryCustBalInfo();

        // 查询材料
        OnLineOrderFile.initMaterial(TransVol.urlParams, null, OnLineOrderFile.CRM_OP_CHECK_NODE_PRE);

        // 重定向到修改页面
        var forId = TransVol.urlParams["forId"];
        if(!CommonUtil.isEmpty(forId)){
            TransVol.urlParams['checkId']  = forId;
            var param = OnLineOrderFile.buildUrlParams(TransVol.urlParams);
            window.open("../apply/applytransvol.html?" + param, "_blank");
		}
    },

    queryAssetInfo : function(){
        var  transOutBalDtlList = TransVol.transOutBalDtlList;
		// 转入银行卡资产信息
        $("#assetBody").empty();

        // 查询客户银行卡持仓
        var  uri= TmsCounterConfig.QUERY_INTRANSIT_ASSET_URL ||  {};
        var reqparamters = {};

        if(transOutBalDtlList != null && transOutBalDtlList.length != 0 ){
            reqparamters.cpAcctNo = transOutBalDtlList[0].cpAcctNo;
        }
        reqparamters.bankAcct = $("#bankAcct").val();
        reqparamters.txAcctNo = QueryCustInfo.custInfoDtoList[0].custNo;

        var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
        CommonUtil.ajaxAndCallBack(paramters, TransVol.queryAssetInfoCallBack);
	},

    queryAssetInfoCallBack:function(data){
        var bodyData = data.body || {};
        var respData = bodyData.batchStatList || [];

        TransVol.assetDetailList = respData.detailList || [];


        if(TransVol.assetDetailList.length <=0){
            var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
            $("#assetBody").append(trHtml);
            return false;

        }else{
            $(TransVol.assetDetailList).each(function(index,element){
                var trList = [];
                trList.push('');
                trList.push(CommonUtil.formatData(element.prodCode));
                trList.push(CommonUtil.formatData(element.fundName));
                trList.push(CommonUtil.formatData(element.busiCode));
                trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.formatData(element.bankAcctName));
                trList.push(CommonUtil.formatAmount(element.occurBalance));
                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#assetBody").append(trAppendHtml);
            });
        }
    },
	
	queryCustBalInfo:function(){
		// 默认flase
		TransVol.existTaxDelayFundFlag = false;
		var custNo = $("#custNo").val();
		var idNo = $("#idNo").val();
		if(CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo)){
			CommonUtil.layer_tip("请先输入客户号或证件号");
			return false;
		}
		
		var bankAcct = $("#bankAcct").val();
		if(CommonUtil.isEmpty(bankAcct)){
			CommonUtil.layer_tip("请输入必要条件：银行卡号");
			return false;
		}
		
		var searchDisCode = $("#selectDisCode").val();
		if(CommonUtil.isEmpty(searchDisCode)){
			CommonUtil.layer_tip("请选择分销机构");
			return false;
		}

		// 查询客户基本信息
		QueryCustInfo.queryBasicCustInfoTransVol();
		
		// 查询客户银行卡持仓
		var  uri= TmsCounterConfig.QUERY_CUST_VOL_BAL_URL ||  {};
		var reqparamters = {};
		reqparamters.custNo = custNo;
		reqparamters.idNo = idNo;
		reqparamters.disCode = searchDisCode;
		reqparamters.bankAcct = bankAcct;
		reqparamters.shareType = CONSTANTS.VOL_TRANS;//份额迁移

		// 转出查询持仓参数
		TransVol.transOutParams = reqparamters;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, TransVol.queryTransVolBalCallBack);
	},

	queryTransVolBalCallBack:function(data){
		var bodyData = data.body || {};
		var respData = bodyData.respData || [];
		
		var txAcctNo = respData.txAcctNo;
		var disCode = respData.disCode;

		TransVol.txAcctNo = txAcctNo;
		TransVol.transOutBalDtlList = respData.custBalDtlList || [];
		TransVol.custBankCardList = respData.custBankCardList || [];
		$("#checkFundBox").prop("checked",true);
		
		// 转出银行卡资产信息
		$("#transOutCustBals").empty();
		$("#transOutCustBals").empty();
		$("#highTransOutCustBals").empty();

        // 转入银行卡资产信息
        $("#assetBody").empty();
		var trHtml = '<tr><td colspan="13">没有查询到转出银行卡资产信息</td></tr>';
		if(TransVol.transOutBalDtlList.length <=0){
			$("#transOutCustBals").append(trHtml);
			$("#highTransOutCustBals").append(trHtml);

		}else{
			var count = 0;
			var highCount = 0;
			$(TransVol.transOutBalDtlList).each(function(index,element){
				if (element.protocolType == "12"){
					TransVol.existTaxDelayFundFlag = true;
				}
				if (element.protocolType == "4") {
					var highTrList = [];
					highTrList.push('<input  name="checkHigh" type="checkbox" index="'+index+'" fundCode="'+element.fundCode+'"></input>');
					highTrList.push(CommonUtil.formatData(element.taCode));
					highTrList.push(CommonUtil.formatData(element.fundCode));
					highTrList.push(CommonUtil.formatData(element.fundAttr));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
					highTrList.push(CommonUtil.formatData(element.bankAcct));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
					highTrList.push(CommonUtil.formatAmount(element.balanceVol));
					highTrList.push(CommonUtil.formatAmount(element.availVol));
					highTrList.push(CommonUtil.formatAmount(element.unconfirmedVol));
					highTrList.push(CommonUtil.formatAmount(element.justFrznVol));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
					highTrList.push(CommonUtil.formatData(element.protocolNo));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, element.disCode));
					
					var highTrAppendHtml = '<tr class="text-c"><td>'+ highTrList.join('</td><td>') + '</td></tr>';
					$("#highTransOutCustBals").append(highTrAppendHtml);
					highCount ++;
				} else if(element.protocolType == "91"){

                        var trList = [];
                        trList.push('');
                        trList.push(CommonUtil.formatData(element.fundCode));
                        trList.push(CommonUtil.formatData(element.fundAttr));
                        trList.push(CommonUtil.formatData(element.protocolNo));
                        trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                        trList.push(CommonUtil.formatData(element.bankAcct));
                        trList.push(CommonUtil.formatData(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode)));
                        trList.push(CommonUtil.formatAmount(element.balanceVol));
                        var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                        $("#assetBody").append(trAppendHtml);

				}else {
					var trList = [];
					trList.push('');
					trList.push(CommonUtil.formatData(element.fundCode));
					trList.push(CommonUtil.formatData(element.fundAttr));
					trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
					trList.push(CommonUtil.formatData(element.bankAcct));
					trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
					trList.push(CommonUtil.formatAmount(element.balanceVol));
					trList.push(CommonUtil.formatAmount(element.availVol));
					trList.push(CommonUtil.formatAmount(element.unconfirmedVol));
					trList.push(CommonUtil.formatAmount(element.justFrznVol));
					trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
					trList.push(CommonUtil.formatData(element.protocolNo));
					trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, element.disCode));
					
					var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
					$("#transOutCustBals").append(trAppendHtml);
					fundCustBooksCount ++;
				}
			});
			if (highCount == 0) {
				$("#highTransOutCustBals").append(trHtml);
			}
			if (fundCustBooksCount == 0) {
				$("#transOutCustBals").append(trHtml);
			}

            $("input[name ='checkHigh']").prop("checked",true);
            $("input[name ='checkHigh']").prop("disabled",true);


			var selectCancelCard = $("#selectCancelCard").val();
        	//如果选择销卡，必须全部资产转出
			if(selectCancelCard == '0'){
            	$("#alertSpanId").hide();
        		$("#assetBox").prop("checked",false);
        		$("input[name ='checkHigh']").prop("checked",false);
       			$("input[name ='checkHigh']").prop("disabled",false);
       			//$("#checkFundBox").prop("checked",false);
       			//$("#checkFundBox").prop("disabled",false);
       			$("#checkHighBox").prop("checked",false);
        		$("#checkHighBox").prop("disabled",false);
			}
		}
		
		// 转入银行卡
		$("#transInBanks").empty();
		$("#transInCustBals").empty();
		if(TransVol.custBankCardList.length <=0){
			var trHtml = '<tr><td colspan="4">没有查询到转入银行卡信息</td></tr>';
			$("#transInBanks").append(trHtml);
			return false;
			
		}else{
			$(TransVol.custBankCardList).each(function(index,element){
				var trList = [];
				var bankAcctStatus = element.bankAcctStatus;
				var disableAttr = '';
				var disableCol = '';
				if(element.bankAcct == TransVol.transOutParams.bankAcct || bankAcctStatus != '0') {
					disableCol = 'style="background-color: darkgray;"';
					disableAttr = 'disabled = "disabled"';
				}
				
				trList.push('<input class="selectTransInBank" ' + disableAttr + ' id="selectTransInBank_'+index+'" name="checkTransInBank" type="radio" data-index="' + index + '"></input>');
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
				trList.push(CommonUtil.formatData(element.bankRegionName));
				trList.push(CommonUtil.getMapValue(CONSTANTS.BANKACCT_STATUS_MAP, bankAcctStatus));
				var trAppendHtml = '<tr class="text-c" ' + disableCol + '><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#transInBanks").append(trAppendHtml);
				
				// 绑定点击事件
				$(".selectTransInBank").on('click',function(){
					var dataIndex = $(this).attr('data-index');
					var selBankAcct = TransVol.custBankCardList[dataIndex].bankAcct;
					var selBankAcctStatus = TransVol.custBankCardList[dataIndex].bankAcctStatus || '0';

					TransVol.selBankAcct = selBankAcct;
					TransVol.selBankAcctStatus = selBankAcctStatus;

					if(selBankAcct == TransVol.transOutParams.bankAcct){
						$(".selectTransInBank").attr("checked", false);
						CommonUtil.layer_tip("不能原卡转入到原卡中！");
						return false;
					}
					
					if(selBankAcctStatus != '0'){
						$(".selectTransInBank").attr("checked", false);
						CommonUtil.layer_tip("银行卡状态不正常！");
						return false;
					}
					
					TransVol.queryTransInCustBals(txAcctNo, disCode, selBankAcct);
				});
			});
		}

        // 转入银行卡
        var inBankAcct = TransVol.urlParams["inBankAcct"];
		if(!CommonUtil.isEmpty(inBankAcct)){

			var exitsInBankAcct = false;
            //默认选中转入卡
            $(".selectTransInBank").each(function (index, element) {
                var dataIndex = $(element).attr('data-index');
                var selBankAcct = TransVol.custBankCardList[dataIndex].bankAcct;
                if(selBankAcct === inBankAcct){
                    exitsInBankAcct = true;
                    $(element).click();
                }
            });
		}


	},
	
	queryTransInCustBals:function(txAcctNo, disCode, selBankAcct){
		// 查询客户银行卡持仓
		var  uri= TmsCounterConfig.QUERY_CUST_VOL_BAL_URL ||  {};
		var reqparamters = {};
		reqparamters.custNo = txAcctNo;
		reqparamters.disCode = disCode;
		reqparamters.bankAcct = selBankAcct;
		reqparamters.shareType = CONSTANTS.VOL_TRANS;//份额迁移
		
		// 转入查询持仓参数
		TransVol.transInParams = reqparamters;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, TransVol.queryTransInBalCallBack);
	},
	
	queryTransInBalCallBack:function(data){
		var bodyData = data.body || {};
		var respData = bodyData.respData || [];
		
		TransVol.transInBalDtlList = respData.custBalDtlList || [];
		
		// 转入银行卡资产信息
		$("#transInCustBals").empty();
		if(TransVol.transInBalDtlList.length <=0){
			var trHtml = '<tr><td colspan="10">没有查询到转入银行卡资产信息</td></tr>';
			$("#transInCustBals").append(trHtml);
			return false;
			
		}else{
			$(TransVol.transInBalDtlList).each(function(index,element){
				var trList = [];
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.fundAttr));
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
				trList.push(CommonUtil.formatAmount(element.balanceVol));
				trList.push(CommonUtil.formatAmount(element.availVol));
				trList.push(CommonUtil.formatAmount(element.unconfirmedVol));
				trList.push(CommonUtil.formatAmount(element.justFrznVol));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
				trList.push(CommonUtil.formatData(element.protocolNo));
				
				var disCodeShow = element.disCode;
				if(CommonUtil.isEmpty(disCodeShow)){
					disCodeShow = respData.disCode;
				}
				trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCodeShow));
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#transInCustBals").append(trAppendHtml);
			});
		}
	},
	
	
	/***
	 * 份额迁移确认提交
	 */	
	confirm : function(){
		//注销银行卡 需选择所有分销
		var searchDisCode = $("#selectDisCode").val();
		var selectCancelCard = $("#selectCancelCard").val();
		if(selectCancelCard == '1' && !CommonUtil.isEmpty(searchDisCode) && 'ALL' != searchDisCode){
			CommonUtil.layer_tip("若注销原卡，则所有分销的份额都需要换卡");
			return false;
		}
		
		/*if(selectCancelCard == '0' && !CommonUtil.isEmpty(searchDisCode) && 'ALL' == searchDisCode){
			CommonUtil.layer_tip("若不注销原卡，不能选择所有分销");
			return false;
		}*/
		if (TransVol.existTaxDelayFundFlag){
			CommonUtil.layer_tip("柜台不支持Y份额基金交易");
			return false;
		}
		
		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先查出客户信息");
			return false;
		}
		
		if(TransVol.transOutBalDtlList == null || TransVol.transOutBalDtlList.length <=0){
			CommonUtil.layer_tip("转出银行卡资产信息为空，不能提交！");
			return false;
		}

		// 0-全部 1-公募 2-高端
		var shareTransferFlag = "";
		var checkFundFlag = false;
		if (fundCustBooksCount > 0) {
			checkFundFlag = $("#checkFundBox").is(":checked");
			if (checkFundFlag) {
				shareTransferFlag = "1";
			}
		}
        var highLength = $("#highTransOutCustBals").find("input[type='checkbox'][name='checkHigh']").length;

		var selectedHighCheckboxs = $("#highTransOutCustBals").find("input[type='checkbox'][name='checkHigh']:checked");
		if (selectedHighCheckboxs.length > 0) {
			if (shareTransferFlag == "1" || shareTransferFlag == "") {
				shareTransferFlag = "0";
			} else {
				shareTransferFlag = "2";
			}
		}else if(highLength == 0){
			shareTransferFlag = "0";
		}

        var selectCancelCard = $("#selectCancelCard").val();
        //如果选择销卡，必须全部资产转出
		if(selectCancelCard == '1' && shareTransferFlag != "0"){
            CommonUtil.layer_tip("注销原卡不允许留有资产!");
            return false;
		}

        var highFundCodes = "";
		$(selectedHighCheckboxs).each(function(){
			highFundCodes += $(this).attr("fundCode") + ",";
		});

		var transOutBalList = [];
		$(TransVol.transOutBalDtlList).each(function(index,element){
			if (checkFundFlag && "4" != element.protocolType && '91' != element.protocolType) {
				transOutBalList.push(element);
			}
			if (selectedHighCheckboxs.length > 0 && (highFundCodes.indexOf(element.fundCode) >= 0)) {
				transOutBalList.push(element);
			}
			//注销银行卡需要迁移在途资金
			if(selectCancelCard== '1' && '91' == element.protocolType){
				transOutBalList.push(element);
			}
		});

		if (transOutBalList.length == 0) {
			CommonUtil.layer_tip("请选择需要迁移的记录！");
			return false;
		}
		
		var checkAvailVolFlag = true;
		var checkUnVolFlag = true;
		var balAvaiFlag = true;
		$(transOutBalList).each(function(index,element){
			if(element.availVol == '0'){
				checkAvailVolFlag = false;
			}
			if(element.unconfirmedVol != '0'){
				checkUnVolFlag = false;
			}
			if(element.balanceVol != element.availVol){
				balAvaiFlag = false;
			}
		});
		if(!checkAvailVolFlag){
			CommonUtil.layer_tip("转出存在可用份额为0，不可以份额迁移！");
			return false;
		}
		if(!checkUnVolFlag){
			CommonUtil.layer_tip("转出存在冻结份额， 不可以份额迁移！");
			return false;
		}
		if(!balAvaiFlag){
			CommonUtil.layer_tip("转出可用份额与总份额不相等，存在在途，不可以份额迁移！");
			return false;
		}
		
		var selectedInCheckboxs = $("#transInBanks").find("input[type='radio'][name='checkTransInBank']:checked");
		if(selectedInCheckboxs.length != 1){
			CommonUtil.layer_tip("请选择一条转入的银行卡信息");
			return false;
		}
		
		// 校验其他录入信息
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		transactorInfoForm.appDtm = transactorInfoForm.appDt +'' + transactorInfoForm.appTm;
		if(CommonUtil.isEmpty(transactorInfoForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			return false;
		}
		if(!Valid.valiadTradeTime(transactorInfoForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			return false;
		}
		
		if(!Validate.validateTransactorInfo(transactorInfoForm, QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimSubmitBtn");
			return false;
		}
		
		// 转入的银行卡
		var selInIndex = $(selectedInCheckboxs[0]).attr('data-index');
		var transInBank = {};
		transInBank.cpAcctNo = TransVol.custBankCardList[selInIndex].cpAcctNo;
		transInBank.bankAcct = TransVol.custBankCardList[selInIndex].bankAcct;
		transInBank.bankCode = TransVol.custBankCardList[selInIndex].bankCode;
		TransVol.transInBank = transInBank;
		
		// 转出银行卡信息中若有状态为正常的定投协议号，提交时进行提示，提示信息：转出信息包含未关闭的定投协议，定投协议号为xxx，是否要继续？
		var scheProtocoNos = [];
		$(transOutBalList).each(function(index,element){
			//1-定投状态正常; 定投协议:(机器人定投，智能定投，推荐组合定投，公募定投)
			if(element.scheStatus == '1'
				&& (element.protocolType == '2' || element.protocolType == '3' || element.protocolType == '5' || element.protocolType == '7')){
				scheProtocoNos.push(element.protocolNo);
			}
		});
		
		var tipmsg = "";
		if(scheProtocoNos.length >0 ){
			tipmsg = "转出信息包含未关闭的定投协议，定投协议号为" + JSON.stringify(scheProtocoNos) + ", 是否要继续？"
		} else{
			tipmsg = "确认提交吗？";
		}
		
		//询问框  
		layer.confirm(tipmsg, {  
		  btn: ['是','否']
		}, function(index){  
			
			CommonUtil.disabledBtn("confimSubmitBtn");

			var uri = TmsCounterConfig.CUST_VOL_TRANSFER_SUBMIT_URL ||  {};
			var reqparamters ={"dealAppNo" : null,
					"transOutParams" : JSON.stringify(TransVol.transOutParams),
					"transInParams" : JSON.stringify(TransVol.transInParams),
					"transInBank" : JSON.stringify(TransVol.transInBank), 
					"custInfoForm" : JSON.stringify(QueryCustInfo.custInfo),
					"transactorInfoForm" : JSON.stringify(transactorInfoForm),
					"intransitAssetMemo" : JSON.stringify(TransVol.assetDetailList),
					"shareTransferFlag" : shareTransferFlag,
					"highFundCodes" : highFundCodes,
					"cancelCard" : $("#selectCancelCard").val(),
                   "materialinfoForm":JSON.stringify(OnLineOrderFile.buildOrderCheckFile())};
			
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, TransVol.callBack);
			
		}, function(){  
			layer.closeAll();
			CommonUtil.enabledBtn("confimSubmitBtn");
		}); 
		
	},
		
	callBack:function(data){

		layer.closeAll();
		
		var retMsg = "提交成功";
		if(data == null){
			retMsg = "提交失败。";
			
		} else{
			if(!CommonUtil.isSucc(data.code) && !CommonUtil.isArray(data)){
				retMsg = "提交失败, "+data.desc+"("+data.code+")。";
			} 
			if(CommonUtil.isArray(data) && data.length > 0){
				retMsg = "";
				$(data).each(function(index,element){
					var respCode = element.code || '';
					var respDesc = element.desc || '';
					var bodyData = element.body || {};
					var outFundList = bodyData.outFundList || {};
					var productChannel = bodyData.productChannel || {};
					
					var outFunds = [];
					var outHighFunds = [];
					if(outFundList.length > 0){
						$(outFundList).each(function(ins,obj){
							if (obj.protocolType == '4') {
								outHighFunds.push(obj.fundCode);
							} else {
								outFunds.push(obj.fundCode);
							}
						});
					}
					
					if(outFunds.length > 0){
						retMsg += "银行卡:"+TransVol.transOutParams.bankAcct +"下持有零售公募基金: "+JSON.stringify(outFunds)+", 份额迁移转出到: "+ TransVol.selBankAcct +", 确认提交"+ respDesc+"。<br>";
					} 
					if(outHighFunds.length > 0){
						retMsg += "银行卡:"+TransVol.transOutParams.bankAcct +"下持有高端基金: "+JSON.stringify(outHighFunds)+", 份额迁移转出到: "+ TransVol.selBankAcct +", 确认提交"+ respDesc+"。<br>";
					}
				});
			}
		}
		
		layer.open({type: 1,
			  skin: 'layui-layer-rim', //加上边框
			  area: ['400px', '300px'], //宽高
			  content: '<div style="text-align: center;margin: 10px 10px;">'+retMsg+'</div>'
			});
		CommonUtil.enabledBtn("confimSubmitBtn");
		// 提交后禁用按钮，不可重复提交
		if (CommonUtil.isArray(data) && data.length > 0) {
			CommonUtil.disabledBtnWithClass("confimSubmitBtn");
			CommonUtil.disabledBtn("confimSubmitBtn");
		}
	}

};
