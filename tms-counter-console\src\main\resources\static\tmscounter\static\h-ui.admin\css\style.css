@charset "utf-8";
/* 你自己的样式 */
.pt5 {padding-top:5px;}
.mt5 {margin-top:5px;}
.mt10 {margin-top:10px;}
.mt15 {margin-top:15px;}
.mt20 {margin-top:20px;}
.mt25 {margin-top:25px;}
.mt30 {margin-top:30px !important;}
.mt50 {margin-top:50px !important;;}
.ml5 {margin-left:5px;}
.ml10 {margin-left:10px;}
.ml20 {margin-left:20px;}
.ml30 {margin-left:30px;}
.red,.cR {color:#c00;}
.fwb {font-weight:bold;}
.main_title {font-size:14px;font-weight:bold;position:relative;}
.word_date {text-align:center;font-size:12px;line-height:30px;background:#FFC;color:#333;margin:10px 20px 0;}
.fl {float:left;}
.fr {float:right;}
.clear{ clear: both; *zoom:1}
.clear:after{content:"."; display:block; height: 0; clear: both; visibility:hidden;}
/*翻页结构*/
.page_all {width:100%;margin:0 auto;}
.page_item {text-align:right;padding-bottom:20px;}
.page_item a {width:58px;height:22px; text-align:center; text-decoration:none;line-height:22px;display:inline-block;color:#000;border:1px solid #c5c5c5;border-radius:3px;
background:#EDF0F4;
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee'); /* for IE */
background: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#eee)); /* for webkit browsers */
background: -moz-linear-gradient(top,  #fff,  #eee); /* for firefox 3.6+ */}
.page_item a:hover {color:#000;background:#f2f2f2;}
.page_item .pg_1 {width:40px;height:22px;}
.page_item .pg_gray,.page_item .pg_gray:hover {color:#999; text-decoration:none; cursor:default;background:#fafafa; }
.page_item .it01 {width:50px;height:22px;line-height:22px;margin:0 3px;border:1px solid #ddd;padding:0 2px;}
.page_item .it02 {width:50px;height:24px;cursor:pointer;}
.page_item input {*position:relative;*top:2px;}
.page_item span {text-align:left;}
.page_sj {padding-bottom:20px;}

/*业务处理页面*/
.normal_btn {position:relative;left:50%;margin-left:-41px;}
.text_con {font-size:14px;line-height:35px;margin-bottom:0;}
.text_con .date_span {position:relative;top:2px;}
.text_con .select-box {line-height:21px;margin-left:5px;}
.text_con .state_span {margin-left:100px;position:relative;top:2px;} 
.text_con input {display:inline-block;width:115px;margin-left:5px;padding:15px 5px;}

/*支付对账页面*/
.sj_box {width:100%;overflow-x: scroll;margin:0 auto;}
.sj_box table tr th {white-space:nowrap;}
.sj_box table tr td {height:20px;white-space:nowrap;}
.secondary_title {font-size:18px;line-height:30px;background:#FFC;color:#f00;border-top:1px solid #ccc;border-bottom:1px solid #ccc;padding-left:15px;}
/*支付对账确认页面*/
.data_box table tr th {white-space:nowrap;}
.data_box table tr td {white-space:nowrap;}

/*业务处理流程图页面*/
.flowsheet_box {padding-right:300px;position:relative;min-width:500px;}
.flowsheet_box .btn {width:130px;text-align:center;}
.flowsheet_box .two_fl {position:absolute;top:36px;left:50%;margin-left:-159px;}
.flowsheet_box .btn2 {position:absolute;top:105px;left:50%;margin-left:-215px;}
.flowsheet_box .btn3 {position:absolute;top:87px;left:50%;margin-left:-44px;}
.flowsheet_box .btn4 {position:absolute;top:153px;left:50%;margin-left:-215px;}
.flowsheet_box .btn5 {position:absolute;top:145px;left:50%;margin-left:-44px;}
.flowsheet_box .btn6 {position:absolute;top:198px;left:50%;margin-left:-215px;}
.flowsheet_box .btn7 {position:absolute;top:203px;left:50%;margin-left:-44px;}
.flowsheet_box .btn8 {position:absolute;top:275px;left:50%;margin-left:-215px;}
.flowsheet_box .btn9 {position:absolute;top:351px;left:50%;margin-left:-215px;}
.flowsheet_box .only_lf1 {position:absolute;top:136px;left:50%;margin-left:-159px;}
.flowsheet_box .only_lf2 {position:absolute;top:120px;left:50%;margin-left:8px;}
.flowsheet_box .only_lf4 {position:absolute;top:178px;left:50%;margin-left:8px;}
.flowsheet_box .only_lf5 {position:absolute;top:309px;left:50%;margin-left:-159px;}
.flowsheet_box .one_lf {position:absolute;top:231px;left:50%;margin-left:-159px;}

/*交易申请日终页面*/
.trade_box {width:800px;margin:0 auto;}
.trade_box tr td {height:21px;position:relative;font-size:14px;}
.trade_box tr td input {position:absolute;left:40px;top:16px;}
.trade_box tr td span {display:block;}

/*导入确认页面*/
.import_title {line-height:36px;font-size:16px;text-align:center;background:#ccc;border-top:1px solid #666;border-bottom:1px solid #666;margin:0 20px;}

/*产品上线参数录入页面*/
.cp_top {margin-top:10px;line-height:25px;font-size:14px;}
.cp_top input[type='text'],.result2_tab td input[type='text']{height:28px;}
.cp_top input,.result2_tab td input {border:none;display:inline-block;font-size:14px;border:1px solid #ccc;padding:0 4px;height:28px;border-radius: 3px;}
.cp_top .normal_span {display:inline-block;line-height:25px;text-align:right;/*width:120px;*/}
.cp_top .input-text {display: inline-block;width:171px;padding: 15px 5px;margin-left:0;}
.message_con {display:inline-block;width:700px;height:60px;margin-left:10px;border:1px solid #ccc;overflow-y: scroll;padding:4px;}
.select-box.inline{width:171px;}
.select-box.inline .select{width:160px;}
.select-box{padding:4px 0 4px 5px;}
.search_box {width:1150px;}
.search_box label {position:relative;}
.search_box label input {display:inline-block;font-size:14px;line-height:normal;padding:5px;border:1px solid #ccc;width:120px;}
.search_box label span {position:absolute;white-space:nowrap;color:#999;left:3px;top:0px;}
.search_box label i {position:absolute;right:5px;top:0;}
.search_box .sp_label span {color:#333;}
.search_box .sp_label2 input {padding-right:20px;width:110px;}
.add_pz {color:#0099FF;font-size:14px;display:inline-block;}
.kt_box label {line-height:35px;}
.kt_box label input {margin-right:3px;position:relative;top:1px;}
.cp_tab table tr td {font-size:14px;white-space:nowrap;}
.cp_tab table tr td .select {display:inline-block;width:100px;margin:0 auto;}

/*登陆页面*/
.dl_box {width:350px;position:absolute;left:50%;margin-left:-175px;border:1px solid #333;top:50%;margin-top:-85px;}
.dl_box h4 {font-size:18px;color:#fff;background:rgba(51, 102, 255, 1);line-height:40px;text-align:center;margin-top:0;}
.dl_box ul li {width:100%;margin-top:10px;}
.dl_box ul li span {display:inline-block;width:100px;line-height:30px;text-align:right;padding-right:3px;}
.dl_box ul li input {font-size:14px;padding:3px;width:160px;border:1px solid #ccc;}
.dl_box p a {display:inline-block;width:80px;text-align:center;border:1px solid #333;padding:5px 0;font-size:14px;line-height:14px;background:#ccc;text-decoration:none;}

.wel_con {font-size:50px;font-weight:bold;padding-top:200px;text-align:center;}

/*产品基本信息维护页面*/
.result_tab {width:800px;margin:0 auto;}
.result_tab table tr td {height:21px;font-size:14px;white-space:nowrap;}
.result2_tab {width:100%;margin:0 auto;overflow-x:auto;}
.result2_tab table tr td,.result2_tab table tr th {height:30px;font-size:14px;white-space:nowrap;}

/*客户余额查询*/
.chose_box .result_btn {display:inline-block;float:left;padding:5px 10px;background:#fff;border:1px solid #333;cursor:pointer;}
.chose_box .current {background:#5a98de;color:#fff;border:1px solid #5a98de;}
.message_tab {width:100%;}
.message_tab table tr td {white-space:nowrap;height:25px;}
.message_tab table tr th {background:#ccc;}
.message_tab table .fl_part td {background:#ccc;}
.message_tab table .fl_part .no_bg {background:transparent;}


.w1000{width:1000px;}
.w1500{width:1500px;}
.mgc{margin:0 auto;}
.ml60{margin-left:60px;}
.ml15{margin-left:15px;}
/*基本信息*/
.infoTab th{text-align:center;color:#333;font-size:24px;}
.infoTab td,.infoTab th{border:1px solid #cdcdcd;height:30px;}
.infoTab td{text-align:left;padding-left:10px;}
.infoTab td input[type="text"]{border:1px solid #cdcdcd;line-height:22px;}
.infoTac th,.infoTac td{text-align:center;}
.infoTab td a{color:#1D92CB;}
.infoTab td a.download{margin-right:10px;display:inline-block;}
.resultInfo{width:800px;margin:0 auto;}
.resultInfo .unReason{width:600px;height:50px;vertical-align:middle;padding:5px;}
.uploadInfo{position:relative;}
.seeInfo{width:480px;height:400px;overflow-y:auto;padding:10px;}
/*批处理*/
.plInfo{width:500px;height:380px;background:url('../../../images/pic.jpg') no-repeat;position:relative;}
.pro_success:hover,.pro_success{color:#fff !important;}
.pro_success,.pro_normal,.pro_default{
	background:url(../../../images/successbtn.gif) no-repeat;
	width:151px;
	height:32px;
	border:#119600 1px solid;
	padding-right:35px;
	text-align:center;
	color:#fff;
	font-size:14px;
	line-height:32px;
	display:inline-block;
	cursor:pointer;	
}
.pro_default{
	background:url(../../../images/normalbtn.gif) no-repeat;
	color:#666;
	border:#bcbcbc 1px solid;
}
.pro_normal{
	background:url(../../../images/normalbtn_success.gif) no-repeat;
	color:#666;
	border:#bcbcbc 1px solid;
}
.plBtn1{position:absolute;left:210px;top:5px;}
.plBtn2{position:absolute;left:80px;top:67px;}
.plBtn3{position:absolute;right:20px;top:68px;}
.plBtn4{position:absolute;left:10px;top:137px;}
.plBtn5{position:absolute;left:170px;top:137px;}
.plBtn6{position:absolute;right:20px;top:137px;}
.plBtn7{position:absolute;left:90px;top:207px;}
.plBtn8{position:absolute;right:20px;top:207px;}
.plBtn9{position:absolute;left:170px;bottom:88px;}
.plBtn10{position:absolute;right:20px;bottom:88px;}
.plBtn11{position:absolute;right:180px;bottom:30px;}
.plInfo a{display:inline-block;border-radius:2px;color:#666;}
.plTab{width:500px;}
.plTab th,.plTab td{border:1px solid #cdcdcd;text-align:center;height:30px;}
/*设置弹窗*/
.install{position:absolute;right:5px;bottom:5px;}
.installInfo{width:500px;height:700px;overflow:auto;text-align:center;}
.installInfo th,.installInfo td{text-align:center;}
/*i消息配置*/
.sleConInfo .normal_span{display:inline-block;line-height:25px;text-align:right;width:120px;}
.sleConInfo p{display:inline-block;width:156px;border:1px solid #cdcdcd;padding:6px;vertical-align:top;}
.sleConInfo .tel {border: none;display: inline-block;font-size: 14px;padding: 4px;border: 1px solid #ccc;margin-left: 5px;width: 160px;}
.drag-area{display:block;background:red;height:20px;;width:100px;}
.reCheck{color:#2DB7F5;}
.mainTitle{font-size:18px;font-weight:bold;}
.container_box{padding-bottom:20px;border-bottom:1px solid #dcdcdc;}
.result2_tab table th,.result2_tab table td{white-space:nowrap;}
.searchIn,.convertCon{width:160px;position:relative;margin:0 auto;}
.searchIn input,.convertCon input{width:150px;}
.searchIcon{position:absolute;right:4px;top:1px;width:20px;height:28px;background:#fff url('../../../images/searchIcon.png') no-repeat right center;}
.searchCurrentIcon{position:absolute;right:4px;top:1px;width:20px;height:28px;background:#fff url('../../../images/searchIcon.png') no-repeat right center;}
.convertInfo{position:absolute;text-align:center;background:#fff;border-radius:5px;}
.laydate-icon{width:180px;}

/*柜台交易复核弹窗*/
.reCheckInfo{width:760px;padding:20px;}
.detailInfo{width:660px;padding:20px 20px 0;}
.tabPop{border-collapse:collapse;margin-left:10px;}
.tabPop td{line-height:28px;border:1px solid #dcdcdc;padding:0 10px;text-align:left;}
.type{background:#eee;}
.checkIn .reCheckTitle{margin-bottom:5px;}
.checkIn table{margin-left:10px;}
.checkIn td input{height:28px;padding:0 5px;-webkit-appearance:none;    border: 1px solid #ccc;}
.checkIn td{height:40px;width:50%;}
.layui-layer-btn{text-align:center !important;}
.cancleReason{margin:10px;}
.cancleReason textarea{width:280px;height:66px;padding:5px;border-radius:5px;}
.details{color:#2DB7F5;}
.rgInfo{width:1000px;height:50px;}
.rgInfo span{color:#333;font-size:14px;width:200px;height:60px; float:left;overflow:hidden;}
.rgInfo span b{color:#666;font-size:20px;}


/**工作日*/
.wd_title{text-align: center;font-size: 14px;}

/**TA输入选择*/
.taInputSelBox{min-width: 580px;}
.taInputSelBox .selectInputTa{width: 340px;height: 36px;}
.taInputSelBox .picture_click{
	background: url(../images/select-icon.png);
    opacity: 1;
    width: 18px;
    height: 17px;
    position: relative;
	margin-top: -27px;
    margin-left: 400px;
}
.taInputSelBox .taList_box{display:none;width: 343px;margin-left: 78px;margin-top: 20px;background-color: #fff;box-shadow: 0px 0px 10px 5px #aaa;position: absolute;}
.taInputSelBox #confirm{position: relative;float: right;}
.taInputSelBox .taTable{overflow-y: scroll;height: 150px;width: 343px;}
span.required {
	color: #FF0000;
	font-size: 150%;
}
