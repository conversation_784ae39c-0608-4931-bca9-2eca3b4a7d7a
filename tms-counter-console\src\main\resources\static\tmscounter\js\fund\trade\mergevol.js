/**
*份额合并
*<AUTHOR>
*@date 2018-05-103 14:39
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();
	MergeVol.init();
	MergeVol.volOutFunds = [];
	MergeVol.volInFunds = {};
	MergeVol.bankAcctDigestMap = {};
});

var MergeVol = {
	init:function(){
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询客户基本和持仓信息
		 */
		$("#queryCustBalInfoBtn").on('click',function(){
			MergeVol.queryCustBalInfo();
		});
		
		$("#reset").on('click',function(){
			CommonUtil.clearForm("searchForm");
		});
		
		/**
		 * 确认提交
		 */
		$("#confimSubmitBtn").on('click',function(){
			MergeVol.confirm();
		});
	},
	
	queryCustBalInfo:function(){
		var custNo = $("#custNo").val();
		var idNo = $("#idNo").val();
		if(CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo)){
			CommonUtil.layer_tip("请先输入客户号或证件号");
			return false;
		}
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入必要查询条件：基金代码");
			return false;
		}
		
		var searchDisCode = $("#selectDisCode").val();
		if(CommonUtil.isEmpty(searchDisCode)){
			CommonUtil.layer_tip("请选择分销机构");
			return false;
		}
		
		var searchBankAcct = $("#bankAcct").val();
		
		// 查询客户基本信息
		QueryCustInfo.queryCustInfo();
		MergeVol.queryCustBankInfo(custNo,searchDisCode);
		
		// 查询客户银行卡持仓
		var  uri= TmsCounterConfig.QUERY_CUST_VOL_BAL_URL ||  {};
		var reqparamters = {};
		reqparamters.custNo = custNo;
		reqparamters.idNo = idNo;
		reqparamters.disCode = searchDisCode;
		reqparamters.fundCode = fundCode;
		reqparamters.bankAcct = searchBankAcct;
		reqparamters.shareType = CONSTANTS.VOL_MERGE;//份额合并
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, MergeVol.queryMergeVolBalCallBack);
	},
	
	queryMergeVolBalCallBack:function(data){
		var bodyData = data.body || {};
		var respData = bodyData.respData || [];
		
		var txAcctNo = respData.txAcctNo;
		var disCode = respData.disCode;
		
		MergeVol.custBalDtlList = respData.custBalDtlList || [];
		
		// 转出信息
		$("#transOutCustBals").empty();
		// 转入信息
		$("#transInBals").empty();
		if(MergeVol.custBalDtlList.length <=0){
			var trHtml = '<tr><td colspan="13">没有查询到可以转出信息</td></tr>';
			$("#transOutCustBals").append(trHtml);
			return false;
			
		}else{
			$(MergeVol.custBalDtlList).each(function(index,element){
				var trList = [];
				trList.push('<input class="selectTransOutCustBal" id="selectTransOutCustBal_'+index+'" name="checkTransOutBal" type="checkbox" data-index="' + index + '"></input>');
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.fundAttr));
				trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
				trList.push(CommonUtil.formatAmount(element.balanceVol));
				trList.push(CommonUtil.formatAmount(element.availVol));
				trList.push(CommonUtil.formatAmount(element.unconfirmedVol));
				trList.push(CommonUtil.formatAmount(element.justFrznVol));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
				trList.push(CommonUtil.formatData(element.protocolNo));
				trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCode));
				
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#transOutCustBals").append(trAppendHtml);
				
				// 绑定点击事件
				$(".selectTransOutCustBal").on('click',function(){
					var selOutIndex = $(this).attr('data-index');
					var selBalVol = MergeVol.custBalDtlList[selOutIndex].balanceVol;
					var selAvailVol = MergeVol.custBalDtlList[selOutIndex].availVol;
					var selUnconfirmedVol = MergeVol.custBalDtlList[selOutIndex].unconfirmedVol;
					if(selAvailVol == '0'){
						$("#selectTransOutCustBal_"+selOutIndex).attr("checked", false);
						CommonUtil.layer_tip("可用份额为0，不可转出！");
						return false;
					}
					if(selUnconfirmedVol != '0'){
						$("#selectTransOutCustBal_"+selOutIndex).attr("checked", false);
						CommonUtil.layer_tip("存在冻结份额， 不可转出！");
						return false;
					}
					if(selAvailVol != selBalVol){
						$("#selectTransOutCustBal_"+selOutIndex).attr("checked", false);
						CommonUtil.layer_tip("可用份额与总份额不相等，存在在途，不可转出！");
						return false;
					}
				});
			});
		}
		
		// 转入信息
		$("#transInBals").empty();
		if(MergeVol.custBalDtlList.length <=0){
			var trHtml = '<tr><td colspan="6">没有查询到可转入信息</td></tr>';
			$("#transInBals").append(trHtml);
			return false;
			
		}else{
			$(MergeVol.custBalDtlList).each(function(index,element){
				var trList = [];
				trList.push('<input class="selectTransInBank" id="selectTransInBank_'+index+'" name="checkTransInBank" type="radio" data-index="' + index + '"></input>');
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCode));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
				trList.push(CommonUtil.formatData(element.protocolNo));
				// 并入后份额
				trList.push('<input type="text" class="mergeVolClass" name="mergeVol_'+index+'" id="mergeVol_'+index+'" readonly="true">');
				
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#transInBals").append(trAppendHtml);
				
				// 绑定点击事件
				$(".selectTransInBank").on('click',function(){
					var selInIndex = $(this).attr('data-index');
					var selInVol = MergeVol.custBalDtlList[selInIndex].availVol;
					
					var selectedOutCheckboxs = $("#transOutCustBals").find("input[type='checkbox'][name='checkTransOutBal']:checked");
					if(selectedOutCheckboxs.length <= 0){
						CommonUtil.layer_tip("请先选择转出信息");
						return false;
					}
					
					if(MergeVol.validateTransVol(selInIndex, selectedOutCheckboxs)){
						
						// 并入后份额
						var selOutTotalVol = 0;
						$(selectedOutCheckboxs).each(function(index,obj){
							var selOutIndex = $(obj).attr('data-index');
							var selOutVol = MergeVol.custBalDtlList[selOutIndex].availVol;
							//selOutTotalVol = selOutTotalVol + selOutVol;
							selOutTotalVol = CommonUtil.accAdd(selOutTotalVol,selOutVol);
						});
						var mergeTotalVol = CommonUtil.accAdd(selInVol, selOutTotalVol);
						//console.log(mergeTotalVol);
						MergeVol.mergeTotalVol = mergeTotalVol;
						$("#mergeVol_"+selInIndex).val(CommonUtil.formatAmount(mergeTotalVol));
					}
				});
			});
		}
	},
	
	/**
	 * 校验转出份额
	 * @returns {Boolean}
	 */
	validateTransVol : function(selInIndex, selectedOutCheckboxs){

		var checkFlag = true;
		var checkVolFlag = true;
		$(selectedOutCheckboxs).each(function(index,obj){
			var selOutIndex = $(obj).attr('data-index');
			if(selInIndex == selOutIndex){
				checkFlag = false;
			}
			var unconfirmedVol = MergeVol.custBalDtlList[selOutIndex].unconfirmedVol;
			if(unconfirmedVol != '0'){
				checkVolFlag = false;
			}
		});
		if(!checkFlag){
			$(".selectTransInBank").attr("checked", false);
			$(".mergeVolClass").val("");
			CommonUtil.layer_tip("不能原卡转入到原卡中！");
			return false;
		}
		if(!checkVolFlag){
			$(".selectTransInBank").attr("checked", false);
			$(".mergeVolClass").val("");
			CommonUtil.layer_tip("存在冻结份额， 不可以份额合并！");
			return false;
		}
		
		return true;
	},
	
	/***
	 * 份额迁移确认提交
	 */	
	confirm : function(){
		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		
		var selectedOutCheckboxs = $("#transOutCustBals").find("input[type='checkbox'][name='checkTransOutBal']:checked");
		if(selectedOutCheckboxs.length <= 0){
			CommonUtil.layer_tip("请先选择转出信息");
			return false;
		}
		var selectedInCheckboxs = $("#transInBals").find("input[type='radio'][name='checkTransInBank']:checked");
		if(selectedInCheckboxs.length != 1){
			CommonUtil.layer_tip("请选择一条转入信息");
			return false;
		}
		
		var selInIndex = $(selectedInCheckboxs[0]).attr('data-index');
		// console.log(selInIndex);
		MergeVol.validateTransVol(selInIndex, selectedOutCheckboxs);
		
		// 校验其他录入信息
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		transactorInfoForm.appDtm = transactorInfoForm.appDt +'' + transactorInfoForm.appTm;
		if(CommonUtil.isEmpty(transactorInfoForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			return false;
		}
		if(!Valid.valiadTradeTime(transactorInfoForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			return false;
		}
		
		if(!Validate.validateTransactorInfo(transactorInfoForm, QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimSubmitBtn");
			return false;
		}
		
		MergeVol.volOutFunds = [];
		// 转出的基金份额
		$(selectedOutCheckboxs).each(function(index,obj){
			var selOutIndex = $(obj).attr('data-index');
			
			var outVolFund = {};
			outVolFund.dealDtlAppNo  = null;
			outVolFund.fundCode		 = MergeVol.custBalDtlList[selOutIndex].fundCode;
			outVolFund.fundName  	 = MergeVol.custBalDtlList[selOutIndex].fundAttr;
			outVolFund.fundShareClass= MergeVol.custBalDtlList[selOutIndex].fundShareClass;
			outVolFund.taCode   	 = MergeVol.custBalDtlList[selOutIndex].taCode;
			outVolFund.cpAcctNo		 = MergeVol.custBalDtlList[selOutIndex].cpAcctNo;
			outVolFund.protocolNo 	 = MergeVol.custBalDtlList[selOutIndex].protocolNo;
			outVolFund.protocolType	 = MergeVol.custBalDtlList[selOutIndex].protocolType;
			outVolFund.balanceVol	 = MergeVol.custBalDtlList[selOutIndex].balanceVol;
			outVolFund.appVol		 = MergeVol.custBalDtlList[selOutIndex].availVol;//转出份额
			outVolFund.unconfirmedVol= MergeVol.custBalDtlList[selOutIndex].unconfirmedVol;
			outVolFund.productChannel= MergeVol.custBalDtlList[selOutIndex].productChannel;//转出产品渠道
			outVolFund.bankAcct      = MergeVol.custBalDtlList[selOutIndex].bankAcct;
			outVolFund.bankCode      = MergeVol.custBalDtlList[selOutIndex].bankCode;
			outVolFund.bankAcctDigest= MergeVol.bankAcctDigestMap[MergeVol.custBalDtlList[selOutIndex].cpAcctNo];
			outVolFund.custNo        = QueryCustInfo.custInfo.custNo;

			MergeVol.volOutFunds.push(outVolFund);
		});
		
		// 转入的银行卡
		var volInFunds = {};
		volInFunds.fundCode		 = MergeVol.custBalDtlList[selInIndex].fundCode;
		volInFunds.fundName  	 = MergeVol.custBalDtlList[selInIndex].fundAttr;
		volInFunds.fundShareClass= MergeVol.custBalDtlList[selInIndex].fundShareClass;
		volInFunds.taCode   	 = MergeVol.custBalDtlList[selInIndex].taCode;
		volInFunds.cpAcctNo		 = MergeVol.custBalDtlList[selInIndex].cpAcctNo;
		volInFunds.protocolNo 	 = MergeVol.custBalDtlList[selInIndex].protocolNo;
		volInFunds.protocolType  = MergeVol.custBalDtlList[selInIndex].protocolType;
		volInFunds.bankAcct      = MergeVol.custBalDtlList[selInIndex].bankAcct;
		volInFunds.bankCode      = MergeVol.custBalDtlList[selInIndex].bankCode;
		volInFunds.appVol        = MergeVol.custBalDtlList[selInIndex].balanceVol;//转入前卡总份额
		volInFunds.bankAcctDigest= MergeVol.bankAcctDigestMap[MergeVol.custBalDtlList[selInIndex].cpAcctNo];
		volInFunds.custNo        = QueryCustInfo.custInfo.custNo;

		MergeVol.volInFunds = volInFunds;
		
		
		// 转出银行卡信息中若有状态为正常的定投协议号，提交时进行提示，提示信息：转出信息包含未关闭的定投协议，定投协议号为xxx，是否要继续？
		var scheProtocoNos = [];
		$(MergeVol.custBalDtlList).each(function(index,element){
			//1-定投状态正常; 定投协议:(机器人定投，智能定投，推荐组合定投，公募定投)
			if(element.scheStatus == '1'
				&& (element.protocolType == '2' || element.protocolType == '3' || element.protocolType == '5' || element.protocolType == '7')
			){
				scheProtocoNos.push(element.protocolNo);
			}
		});
		
		var tipmsg = "";
		if(scheProtocoNos.length >0 ){
			tipmsg = "转出信息包含未关闭的定投协议，定投协议号为" + JSON.stringify(scheProtocoNos) + ", 是否要继续？"
		} else{
			tipmsg = "确认提交吗？";
		}
		
		//询问框  
		layer.confirm(tipmsg, {  
		  btn: ['是','否']
		}, function(index){  
			
			CommonUtil.disabledBtn("confimSubmitBtn");
			
			var uri = TmsCounterConfig.CUST_VOL_MERGE_SUBMIT_URL ||  {};
			
			var reqparamters ={"dealAppNo" : null, 
					"mergeTotalVol" : MergeVol.mergeTotalVol,
					"volOutFunds" : JSON.stringify(MergeVol.volOutFunds), 
					"volInFunds" : JSON.stringify(MergeVol.volInFunds), 
					"custInfoForm" : JSON.stringify(QueryCustInfo.custInfo),
					"transactorInfoForm" : JSON.stringify(transactorInfoForm)};
			
			var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
			CommonUtil.ajaxAndCallBack(paramters, MergeVol.callBack);
			
		}, function(){  
			layer.closeAll();
			CommonUtil.enabledBtn("confimSubmitBtn");
		}); 
	},
		
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confimSubmitBtn");
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.enabledBtn("confimSubmitBtn");
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	},
	/**
	 * 查询客户银行卡信息
	 */
	queryCustBankInfo : function(custNo, disCode) {
		var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
		var reqparamters = {
			"custNo" : custNo,
			"disCode" : disCode
		};

		var paramters = CommonUtil.buildReqParams(uri, reqparamters);
		CommonUtil.ajaxAndCallBack(paramters, function(data) {
			var respCode = data.code || '';
			var body = data.body || {};

			if (CommonUtil.isSucc(respCode)) {
				QueryCustInfo.custBanks = body.custBanks || [];
				$(QueryCustInfo.custBanks).each(
					function(index, element) {
						MergeVol.bankAcctDigestMap[element.cpAcctNo] = element.bankAcctDigest;
					});
			}
		});
	},

};
