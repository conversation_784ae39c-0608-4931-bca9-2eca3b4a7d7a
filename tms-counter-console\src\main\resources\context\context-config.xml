<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="
http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd 
http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd   
http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd">
	<bean id="localeResolver"
		class="org.springframework.web.servlet.i18n.CookieLocaleResolver">
	</bean>
	
    <bean id="multipartResolver"  class="org.springframework.web.multipart.commons.CommonsMultipartResolver">   
        <!-- 设置上传文件的最大尺寸为300MB -->
        <property name="maxUploadSize">   
          <!--<value>19242880</value>-->
          <value>314572800</value>
        </property>
    </bean>
	

</beans>
