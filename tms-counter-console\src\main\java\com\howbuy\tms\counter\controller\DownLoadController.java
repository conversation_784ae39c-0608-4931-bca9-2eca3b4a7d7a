/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.howbuy.cc.center.member.haodoushop.enu.WhetherDeleteEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.tms.counter.common.exception.TmsCounterException;

/**
 * @description:(下载文件控制器) 
 * <AUTHOR>
 * @date 2017年4月14日 下午2:24:03
 * @since JDK 1.6
 */
@Controller
public class DownLoadController {
    private static final Logger LOGGER = LogManager.getLogger(DownLoadController.class);

    /**
     * 
     * counterDown:(柜台下载)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月14日 下午2:25:15
     */
    @RequestMapping("/tmscounter/counterdown.htm")
    public ModelAndView counterDown(HttpServletRequest request,HttpServletResponse response) throws Exception{
        String fileName = request.getParameter("fileName");
        /**
         * 是否删除文件 0-不删除；1-删除
         */
        String deleteFlag = request.getParameter("deleteFlag");
        if(StringUtils.isEmpty(fileName)){
            throw new TmsCounterException("0001", "文件不存在");
        }
        //通过文件路径获得File对象(假如此路径中有一个download.pdf文件)  
        File file = new File(fileName);
        //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型  
        response.setContentType("application/octet-stream");
        //2.设置文件头：最后一个参数是设置下载文件名(假如我们叫a.pdf)  
        String fileNameStr = file.getName();
         fileNameStr = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.name());
        response.setHeader("Content-Disposition", "attachment;fileName="+fileNameStr);  
        ServletOutputStream out = null;  
        FileInputStream inputStream = null;
        try {  
            inputStream = new FileInputStream(file);  
            //3.通过response获取ServletOutputStream对象(out)  
            out = response.getOutputStream();  
            int b = 0;  
            byte[] buffer = new byte[512];  
            while ((b = inputStream.read(buffer)) >=0 ){
                LOGGER.info("lengh:{}",b);
                //4.写到输出流(out)中  
                out.write(buffer,0,b);  
            }  
            inputStream.close();  
            out.close();  
            out.flush();  
  
        } catch (Exception e) {
            LOGGER.error("文件下载异常：",e);
            throw new TmsCounterException("0002", "文件下载异常",e);
        }finally{
            if(WhetherDeleteEnum.YES.getValue().equals(deleteFlag)){
                file.deleteOnExit();
            }
            if(inputStream!=null){
                inputStream.close();
            }
            if(out!=null){
                out.close();
                out.flush();
            }
        }
        return null;
    }
}

