package com.howbuy.tms.counter.fundservice.trade;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.enums.TxChannelEnum;
import com.howbuy.tms.common.enums.busi.CancelFlagEnum;
import com.howbuy.tms.common.enums.busi.TxPasswordValidateFlagEnum;
import com.howbuy.tms.common.enums.database.ZBusiCodeEnum;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.dto.SubmitUncheckOrderDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.BatchCancelOrderFacade;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.BatchCancelOrderRequest;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.CancelOrderFacade;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.CancelOrderRequest;
import com.howbuy.tms.robot.orders.facade.trade.cancelportfolio.CancelPortfolioFacade;
import com.howbuy.tms.robot.orders.facade.trade.cancelportfolio.CancelPortfolioRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/15 9:30
 * @since JDK 1.8
 */

@Service
public class CancelOrderService {

    private static final Logger logger = LogManager.getLogger(CancelOrderService.class);


    @Autowired
    private CancelOrderFacade cancelFundOrderFacade;

    @Autowired
    private BatchCancelOrderFacade batchCancelOrderFacade;
    @Autowired
    private CancelPortfolioFacade cancelPortfolioFacade;

    public BaseResponse cancelFundOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String cancelFlag) {
        CancelOrderRequest request = new CancelOrderRequest();

        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 订单号
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        // 是否需要校验校验密码(柜台撤单不需要校验校验密码)
        request.setNeedValidateTxPwdFlag(TxPasswordValidateFlagEnum.NOT_REQUIRED_VALIDATE.getCode());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        // 撤单标记：1-自行撤单;2-强制撤单;
        request.setCancelFlag(cancelFlag);
        request.setCancelMemo(submitUncheckOrderDto.getCancelMemo());
        request.setWithdrawDirection(submitUncheckOrderDto.getWithdrawDirection());

        logger.info("Fund | cancelOrder request:{}", JSON.toJSONString(request));

        return TmsFacadeUtil.execute(cancelFundOrderFacade, request, disInfoDto);
    }


    public BaseResponse cancelBatchFundOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String cancelFlag) {
        BatchCancelOrderRequest request = new BatchCancelOrderRequest();

        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 订单号
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        // 是否需要校验校验密码(柜台撤单不需要校验校验密码)
        request.setNeedValidateTxPwdFlag(TxPasswordValidateFlagEnum.NOT_REQUIRED_VALIDATE.getCode());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        // 撤单标记：1-自行撤单;2-强制撤单;
        request.setCancelFlag(cancelFlag);
        request.setCancelMemo(submitUncheckOrderDto.getCancelMemo());
        request.setWithdrawDirection(submitUncheckOrderDto.getWithdrawDirection());

        logger.info("batchFund | cancelOrder request:{}", JSON.toJSONString(request));

        return TmsFacadeUtil.execute(batchCancelOrderFacade, request, disInfoDto);
    }


    public BaseResponse cancelPortfolioOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String cancelFlag) {
        CancelPortfolioRequest request = new CancelPortfolioRequest();

        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 订单号
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        // 是否需要校验校验密码(柜台撤单不需要校验校验密码)
        request.setNeedValidateTxPwdFlag(TxPasswordValidateFlagEnum.NOT_REQUIRED_VALIDATE.getCode());
        // 交易账号
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        // 外部订单号
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        // 撤单标记：1-自行撤单;2-强制撤单;
        if (StringUtils.equals(CancelFlagEnum.FORCE.getCode(), cancelFlag)) {
            // 参数为空表示 自行撤单
            request.setZBusiCode(ZBusiCodeEnum.SYS_FORCE_REDEEM.getCode());
        }
        request.setWithdrawDirection(submitUncheckOrderDto.getWithdrawDirection());
        request.setTxChannel(TxChannelEnum.COUNTER.getKey());
        request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());

        logger.info("portfolio | cancelOrder request:{}", JSON.toJSONString(request));

        return TmsFacadeUtil.execute(cancelPortfolioFacade, request, disInfoDto);
    }
}
