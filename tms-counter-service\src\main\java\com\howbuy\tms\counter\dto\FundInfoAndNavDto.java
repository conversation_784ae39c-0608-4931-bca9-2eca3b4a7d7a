/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.math.BigDecimal;

/**
 * @description:(基金基本信息)
 * <AUTHOR>
 * @date 2017年4月12日 下午2:32:27
 * @since JDK 1.6
 */
public class FundInfoAndNavDto {
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金名称
     */
    private String fundName;
    /**
     * 份额类型，A-前收费；B-后收费
     */
    private String fundShareClass;
    /**
     * 基金简称
     */
    private String fundAttr;
    /**
     * 基金类型，0-股票型1-混合型2-债券型3-货币型4-QDII5-封闭式6-结构型7-一对多专户8-券商资管产品_大集合9-券商资管产品_小集合
     */
    private String fundType;
    /**
     * 基金二级类型，01-指数型；21-理财型
     */
    private String fundSubType;
    /**
     * 基金默认分红方式
     */
    private String dfltDivMode;
    /**
     * 追加申购判断规则：第1位：有未完成的认申购；第2位：有过成功的认购购确认；第3位：有过余额；
     */
    private String suppleSubsRule;
    /**
     * 最低持有份额
     */
    private BigDecimal minAcctVol;
    /**
     * 可销售规模
     */
    private BigDecimal distributeSize;
    /**
     * 交易截止时间
     */
    private String endTm;
    /**
     * 产品开放周期：0-按天；1-按周；2-双周；3-三周；4-四周；5-按月；6-双月；7-按季；8-半年；9-一年
     */
    private String redeOpenTerm;
    /**
     * 产品开放类型：1-滚动开放；2-定期开放；3-指定日开发；4-行情通知
     */
    private String fundOpenMode;
    /**
     * 开通标志 0-未开通;1-已开通
     */
    private String openFlag;

    /**
     * 购买状态：1-认购，2-申购，3-不可购买
     */
    private String buyStatus;
    /**
     * 赎回状态 1-可赎回，2-不可赎回
     */
    private String redeemStatus;
    /**
     * TA代码
     */
    private String taCode;
    /**
     * 基金风险等级
     */
    private String fundRiskLevel;
    /**
     * 基金类别， 1-货币型；2-非货币型
     */
    private String fundClass;

    /**
     * 净值日期
     */
    private String navDate;
    /**
     * 基金净值
     */
    private BigDecimal nav;
    /**
     * 每份分红
     */
    private BigDecimal perShareDividend;
    /**
     * 基金状态：0-交易；1-发行；2-发行成功；3-发行失败；4-停止交易；5-停止申购；6-停止赎回；7-权益登记；8-红利发放；9-基金封闭；
     */
    private String fundStat;
    /**
     * 产品类别
     */
    private String productClass;

    private BigDecimal fundBuyFee;
    
    /**
     * 购买净金额下限
     */
    private BigDecimal netMinAppAmt;
    /**
     * 追加购买净金额下限
     */
    private BigDecimal netMinSuppleAmt;

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public BigDecimal getFundBuyFee() {
        return fundBuyFee;
    }

    public void setFundBuyFee(BigDecimal fundBuyFee) {
        this.fundBuyFee = fundBuyFee;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundSubType() {
        return fundSubType;
    }

    public void setFundSubType(String fundSubType) {
        this.fundSubType = fundSubType;
    }

    public String getDfltDivMode() {
        return dfltDivMode;
    }

    public void setDfltDivMode(String dfltDivMode) {
        this.dfltDivMode = dfltDivMode;
    }

    public String getSuppleSubsRule() {
        return suppleSubsRule;
    }

    public void setSuppleSubsRule(String suppleSubsRule) {
        this.suppleSubsRule = suppleSubsRule;
    }

    public BigDecimal getMinAcctVol() {
        return minAcctVol;
    }

    public void setMinAcctVol(BigDecimal minAcctVol) {
        this.minAcctVol = minAcctVol;
    }

    public BigDecimal getDistributeSize() {
        return distributeSize;
    }

    public void setDistributeSize(BigDecimal distributeSize) {
        this.distributeSize = distributeSize;
    }

    public String getEndTm() {
        return endTm;
    }

    public void setEndTm(String endTm) {
        this.endTm = endTm;
    }

    public String getRedeOpenTerm() {
        return redeOpenTerm;
    }

    public void setRedeOpenTerm(String redeOpenTerm) {
        this.redeOpenTerm = redeOpenTerm;
    }

    public String getFundOpenMode() {
        return fundOpenMode;
    }

    public void setFundOpenMode(String fundOpenMode) {
        this.fundOpenMode = fundOpenMode;
    }

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag;
    }

    public String getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(String buyStatus) {
        this.buyStatus = buyStatus;
    }

    public String getRedeemStatus() {
        return redeemStatus;
    }

    public void setRedeemStatus(String redeemStatus) {
        this.redeemStatus = redeemStatus;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getFundClass() {
        return fundClass;
    }

    public void setFundClass(String fundClass) {
        this.fundClass = fundClass;
    }

    public String getNavDate() {
        return navDate;
    }

    public void setNavDate(String navDate) {
        this.navDate = navDate;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public BigDecimal getPerShareDividend() {
        return perShareDividend;
    }

    public void setPerShareDividend(BigDecimal perShareDividend) {
        this.perShareDividend = perShareDividend;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public BigDecimal getNetMinAppAmt() {
        return netMinAppAmt;
    }

    public void setNetMinAppAmt(BigDecimal netMinAppAmt) {
        this.netMinAppAmt = netMinAppAmt;
    }

    public BigDecimal getNetMinSuppleAmt() {
        return netMinSuppleAmt;
    }

    public void setNetMinSuppleAmt(BigDecimal netMinSuppleAmt) {
        this.netMinSuppleAmt = netMinSuppleAmt;
    }

}
