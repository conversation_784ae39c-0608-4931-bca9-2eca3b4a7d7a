<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>转托管转入</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 零售业务 <span class="c-gray en">&gt;</span> 投资交易类<span class="c-gray en">&gt;</span> 修改回款方向 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">修改回款方向</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span ml30">证件号：</span>
                    <input name="idNo" id="idNo" type="text" placeholder='请输入'>
                    <span class="normal_span ml30">分销机构：</span>
                    <span class="select-box inline">
                       <select name="disCode" class="select" id="selectDisCode">
                       </select>
                    </span>
                </div>
                <div class="cp_top mt30">
                    <span class="normal_span">订单号：</span>
                    <input type="text" name="dealNo" id="dealNo"  placeholder="请输入">
                    <span class="normal_span ml30">申请T日：</span>
                    <input  class="input-text laydate-icon" name="appBeginDt" id="appBeginDt"  placeholder="yyyymmdd"  maxlength = "8">~<input  class="input-text laydate-icon" name="appEndDt" id="appEndDt"  placeholder="yyyymmdd"  maxlength = "8">
                    <a href="javascript:void(0)" id="queryDealListBtn" class="btn radius btn-secondary ml30">查询</a>
                </div>
            </div>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title mt30">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>
                        <th>风险等级</th>
                        <th>分销机构</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <p class="main_title mt30">可修改回款方向的交易申请</p>
        <form class="w1500">
            <div class="result2_tab">
                <table id="projectTable" class="table table-border table-bordered table-hover table-bg table-sort">
                    <thead>
                    <tr class="text-c">
                        <th>选择</th>
                        <th>订单号</th>
                        <th>业务类型</th>
                        <th>产品简称</th>
                        <th>产品代码</th>
                        <th>申请T日</th>
                        <th>申请金额</th>
                        <th>申请份额</th>
                        <th>转入基金代码</th>
                        <th>转入基金简称</th>
                        <th>转入申请金额</th>
                        <th>转入申请份额</th>
                        <th>交易状态</th>
                        <th>申请日期</th>
                        <th>申请时间</th>
                    </tr>
                    <tbody id="dealInfoId">
                    <tr class="text-c">
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                </tr>
                    </tbody>
                </table>
                <br/>
            </div>
        </form>

        <form id="modifyDirection" >
            <p class="main_title mt30">可修改回款方向的交易申请</p>
            <form class="w1500">
                <div class="result2_tab">
                    <table id="redeemDirection" class="table table-border table-bordered table-hover table-bg table-sort">
                        <thead>
                        <tr class="text-c">
                            <td>修改前回款方向</td>
                            <input class="hidden" id="beforeModifyId"/>
                            <input class="hidden" id="uncheckCount"/>
                            <td id="beforeModify">--</td>
                            <td>修改后回款方向</td>
                            <td>
                            <select class="select-box inline" id ="afterModify">
                                <option class="select">请选择</option>
                            </select>
                            </td>
                        </tr>
                    </table>
                </div>
            </form>
        </form>
        <div class="mt30">
        <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="submitModifyBtn">确认提交</a>
        </div>
    </div>


    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/trade/modifyredeemdirection.js?v=20181009"></script>
    <script type="text/javascript" src="../../../js/fund/query/querymodifydirection.js?v=20230605"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfosubpage.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20181009"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/validate.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
</body>

</html>