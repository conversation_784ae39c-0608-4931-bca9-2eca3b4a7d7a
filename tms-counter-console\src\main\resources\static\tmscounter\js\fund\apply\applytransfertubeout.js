$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplyTransferTubeOut.checkOrder = {};	
	ApplyTransferTubeOut.checkDtlOrder = [];
	ApplyTransferTubeOut.init(checkId,custNo,disCode,idNo);
});

var ApplyTransferTubeOut = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,ApplyTransferTubeOut.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyTransferTubeOut.checkOrder);
		});
	}, 
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplyTransferTubeOut.checkOrder = bodyData.checkOrder || {};
		ApplyTransferTubeOut.checkDtlOrder = bodyData.checkDtlOrder || {};
				
		if(CommonUtil.isEmpty(ApplyTransferTubeOut.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(ApplyTransferTubeOut.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		if($("#fundCode").length > 0){
			$("#fundCode").val(ApplyTransferTubeOut.checkOrder.fundCode);
		}
		QueryFundInfo.queryFundInfo(ApplyTransferTubeOut.checkOrder.fundCode,false);

		/** 录入订单信息*/
		if($("#transferTubeBusiType").length > 0){
			$("#transferTubeBusiType").val(ApplyTransferTubeOut.checkOrder.transferTubeBusiType);
		}
		if($("#tSellerTD").length > 0){
			Init.setTSellerCodeTD(ApplyTransferTubeOut.checkOrder.transferTubeBusiType);
			$("#tSellerCode").val(ApplyTransferTubeOut.checkOrder.tSellerCode);
		}
		if($("#tSellerTxAcctNo").length > 0){
			$("#tSellerTxAcctNo").val(ApplyTransferTubeOut.checkOrder.tSellerTxAcctNo);
		}
		if($("#tOutletCode").length > 0){
			$("#tOutletCode").val(ApplyTransferTubeOut.checkOrder.tOutletCode);
		}
		
		/** 转出明细*/
		BodyView.setTransferTubeOutTableView("transOutCustBals", ApplyTransferTubeOut.checkDtlOrder, "update");
		/** other*/
		BodyView.setShowOperInfo(ApplyTransferTubeOut.checkOrder);
		
		/** 修改提交*/
		var formAction = $("#transfertubeOutForm").attr("action");
		if(formAction == 'update'){
			$("#confimTransOutBtn").on('click',function(){
				ApplyTransferTubeOut.confirm(formAction, ApplyTransferTubeOut.checkOrder.dealAppNo, ApplyTransferTubeOut.checkDtlOrder);
			});
		}
	},
	
	/***
	 * 确认转托管转出
	 */	
	confirm : function(action, dealAppNo, checkDtlOrder){
		CommonUtil.disabledBtn("confimTransOutBtn");
		
		if(CommonUtil.isEmpty(dealAppNo)){
			CommonUtil.layer_tip("原始订单不存在不可修改！");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入转出基金代码");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var transferTubeBusiType = $("#transferTubeBusiType").val();
		var tSellerCode = $("#tSellerCode").val();
		/*if(transferTubeBusiType == '2' && tSellerCode == '304'){
			CommonUtil.layer_tip("场外跨销售机构, 对方销售人代码不能输入好买304！");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}*/
		
		/*
		var tOutletCode = $("#tOutletCode").val();
		if(CommonUtil.isEmpty(tOutletCode) && transferTubeBusiType == '2'){
			CommonUtil.layer_tip("请输入场外跨销售机构席位号");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}*/
		
		 /**
	     * 转托管方式,1-一次转托管；2-两次转托管
	     */
		var chgTrusteeMode = QueryFundInfo.fundInfo.chgTrusteeMode;
		var tSellerTxAcctNo = $("#tSellerTxAcctNo").val();
		if(CommonUtil.isEmpty(tSellerTxAcctNo) && chgTrusteeMode == "1"){
			CommonUtil.layer_tip("转出基金为一次转托管，对方销售人处投资者基金交易账号不能为空！");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#transfertubeOutForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		// 转出录入信息
		var fundInfoForm = JSON.stringify(QueryFundInfo.fundInfo);
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var transfertubeOutForm = $("#transfertubeOutForm").serializeObject();
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		
		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		transfertubeOutForm.appDtm = transfertubeOutForm.appDt +'' + transfertubeOutForm.appTm;
		if(CommonUtil.isEmpty(transfertubeOutForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		if(!Valid.valiadTradeTime(transfertubeOutForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		var selCount = 0;
		var selTotalOutVol = 0;
		var volOutFunds = [];
		
		// 转出的基金份额
		if(action == "update"){
			
			$(checkDtlOrder).each(function(index,obj){
				var outVolFund = {};
				outVolFund.selIndex      = index;
				outVolFund.dealDtlAppNo  = obj.dealDtlAppNo;
				outVolFund.appVol        = CommonUtil.unFormatAmount($('#outVol_'+index).val());//转出份额
				outVolFund.cpAcctNo		 = obj.cpAcctNo;
				outVolFund.bankAcct      = obj.bankAcct;
				outVolFund.bankCode      = obj.bankCode;
				outVolFund.protocolNo 	 = obj.protocolNo;
				outVolFund.protocolType	 = obj.protocolType;
				outVolFund.preAppVol	 = obj.preAppVol;//转出前份额
				outVolFund.preFrznVol	 = obj.preFrznVol;//转出前冻结份额
				outVolFund.productChannel= QueryFundInfo.fundInfo.productChannel;//转出产品渠道
			
				selTotalOutVol = Number(selTotalOutVol) + Number(CommonUtil.unFormatAmount(outVolFund.appVol));
				volOutFunds.push(outVolFund);
			});
			selCount = checkDtlOrder.length;
		}
		
		// 校验提交的转托管基金转出份额是否为空
		var checkFlag = true;
		$(".outVolClass").css("border-color","");
		$(volOutFunds).each(function(index,obj){
			var outVol = obj.appVol;
			var selIndex = obj.selIndex;
			if(isEmpty(outVol)){
				CommonUtil.layer_tip("转出份额不能为空");
				$("#outVol_"+selIndex).css("border-color","red");
				checkFlag = false;
			}
		});
		if(!checkFlag){
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		//console.log(selTotalOutVol);
		var statisHtml = '转出笔数：'+selCount+'笔；总转出份额：'+CommonUtil.formatAmount(selTotalOutVol)+'份，大写：'+CommonUtil.digit_uppercase(selTotalOutVol).replace('元', '份')+"。";
		var tipmsg = statisHtml + "<br/>是否确认提交？";
		
		//询问框  
		layer.confirm(tipmsg, {  
		  btn: ['确认','取消']
		}, function(index){  
			
			CommonUtil.disabledBtn("confimTransOutBtn");
			
			// 提交
			var uri = TmsCounterConfig.TRANSFERTUBE_OUT_FUND_CONFIRM_URL ||  {};
			var reqparamters = {"dealAppNo":CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
					"fundInfoForm": fundInfoForm,
					"custInfoForm":custInfoForm,
					"transOutVolCmd": JSON.stringify(volOutFunds),
					"transfertubeOutForm": JSON.stringify(transfertubeOutForm),
					"transactorInfoForm":JSON.stringify(transactorInfoForm)
				};
			//console.log(reqparamters);
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, ApplyTransferTubeOut.callBack);
			
		}, function(){  
			layer.closeAll();
			CommonUtil.enabledBtn("confimTransOutBtn");
		}); 
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confimTransOutBtn");
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.enabledBtn("confimTransOutBtn");
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	}
	
}
