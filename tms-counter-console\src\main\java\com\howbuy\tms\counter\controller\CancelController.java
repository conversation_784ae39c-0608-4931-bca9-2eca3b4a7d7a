/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;


import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService;
import com.howbuy.interlayer.product.service.high.request.QueryNotHBJGFundListRequest;
import com.howbuy.interlayer.product.service.high.response.QueryNotHBJGFundListResponse;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.OpCheckNode;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.context.CancelContext;
import com.howbuy.tms.counter.controller.context.ContextUtils;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.SourceDealOrderBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.OtherInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.service.validate.ValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.counter.util.CounterOrderFormUtil;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(撤单控制器) 
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class CancelController {
	
    private Logger logger = LogManager.getLogger(CancelController.class);
    
    @Autowired
    private TmsCounterService  tmsCounterService;
    
    @Autowired
    private ValidateService validateService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    @Autowired
    @Qualifier("tmscounter.queryNotHBJGFundListService")
    private QueryNotHBJGFundListService queryNotHBJGFundListService;
    /**
     * 
     * cancelConfirm:(柜台撤单确认)
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception 
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/cancelconfirm.htm")
    public ModelAndView cancelConfirm(HttpServletRequest request,HttpServletResponse response) throws Exception{
        CancelContext cancelContext = builContext(request);

        // 校验是否存在未审核撤单
        boolean exitUnCheckOrderFlag =  tmsCounterService.exitUnCheckOrder(cancelContext.getOrderDto().getDealNo(),
                cancelContext.getCustInfoDto().getCustNo(), TxCodes.HIGH_COUNTER_FORCE_CANCEL);
        logger.info("CancelController|cancelConfirm|exitUnCheckOrder:{}, dealNo:{}, txAcctNo, txCode:{}", exitUnCheckOrderFlag, cancelContext.getOrderDto().getDealNo(), cancelContext.getCustInfoDto().getCustNo(), TxCodes.HIGH_COUNTER_FORCE_CANCEL);
        if(exitUnCheckOrderFlag){
            throw new TmsCounterException(TmsCounterResultEnum.EXIT_UNCHECK_CANCEL_DEAL);
        }
        
        //校验产品信息
        validateService.validateProductInfo(cancelContext.getOrderDto().getProductCode());
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(cancelContext.getOrderDto().getProductCode());
        cancelContext.setHighProduct(highProductBaseBean);

        // 校验线上资料
        if(cancelContext.getAuditingOrderFileCmd() != null
                && !StringUtils.isEmpty(cancelContext.getAuditingOrderFileCmd().getOrderid())) {
            tmsCounterOutService.validateOrderFileStatus(cancelContext.getAuditingOrderFileCmd());
        }else{
            tmsCounterOutService.validateOrderFileExist(ContextUtils.buildQueryCotext(cancelContext, TmsCounterConstant.CRM_TRADE_TYPE_CANCEL),
                    OpCheckNode.PRE_CHECK.getCode());

        }

        CounterOrderDto condition = ContextUtils.getValidateReplyCondition(cancelContext, TxCodes.HIGH_COUNTER_FORCE_CANCEL);
        tmsCounterService.validateReplyOrder(condition, cancelContext.getDisInfoDto());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        CounterForceCancelReqDto counterCancelReqDto = fillOrderDto(cancelContext);

        CounterForceCancelRespDto responseDto = tmsCounterService.counterForceCancel(counterCancelReqDto,cancelContext.getDisInfoDto());

        // 初审通过
        tmsCounterOutService.auditingFile(cancelContext.getOperatorInfoCmd(), cancelContext.getAuditingOrderFileCmd(), responseDto.getDealAppNo());

        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    private CounterForceCancelReqDto fillOrderDto(CancelContext cancelContext) {
        CounterForceCancelReqDto counterCancelReqDto  = new CounterForceCancelReqDto();
        counterCancelReqDto.setTxAcctNo(cancelContext.getCustInfoDto().getCustNo());
        counterCancelReqDto.setFundCode(cancelContext.getOrderDto().getProductCode());
        counterCancelReqDto.setDealNo(cancelContext.getOrderDto().getDealNo());
        counterCancelReqDto.setFundName(cancelContext.getHighProduct().getFundAttr());
        counterCancelReqDto.setCustName(cancelContext.getCustInfoDto().getCustName());
        counterCancelReqDto.setIdNo(PrivacyUtil.encryptIdCard(cancelContext.getCustInfoDto().getIdNo()));
        counterCancelReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        counterCancelReqDto.setAppAmt(cancelContext.getOrderDto().getAppAmt());
        counterCancelReqDto.setAppVol(cancelContext.getOrderDto().getAppVol());
        counterCancelReqDto.setBankAcct(PrivacyUtil.encryptBankAcct(cancelContext.getOrderDto().getBankAcct()));
        counterCancelReqDto.setDisCode(cancelContext.getOrderDto().getDisCode());
        counterCancelReqDto.setConsCode(cancelContext.getOtherInfoDto().getConsCode());
        counterCancelReqDto.setAgentFlag(cancelContext.getOtherInfoDto().getAgentFlag());
        counterCancelReqDto.setTransactorIdNo(cancelContext.getTransactorInfoDto().getTransactorIdNo());
        counterCancelReqDto.setTransactorIdType(cancelContext.getTransactorInfoDto().getTransactorIdType());
        counterCancelReqDto.setTransactorName(cancelContext.getTransactorInfoDto().getTransactorName());
        //用户撤单标识
        counterCancelReqDto.setForceCancelFlag(cancelContext.getUserCancelFlag());
        //撤单原因
        counterCancelReqDto.setCancelMemo(cancelContext.getCancelMemo());
        // 产品通道
        counterCancelReqDto.setProductChannel(cancelContext.getHighProduct().getProductChannel());
        counterCancelReqDto.setTaCode(cancelContext.getHighProduct().getTaCode());
        counterCancelReqDto.setWithdrawDirection(cancelContext.getRefundDto().getWithdrawDirection());
        if(cancelContext.getAuditingOrderFileCmd() != null){
            counterCancelReqDto.setMaterialId(cancelContext.getAuditingOrderFileCmd().getOrderid());
        }

        CommonUtil.setCommonOperInfo(cancelContext.getOperatorInfoCmd(),counterCancelReqDto);

        SourceDealOrderBean sourceDealOrderBean = new SourceDealOrderBean();
        BeanUtils.copyProperties(cancelContext.getOrderDto(), sourceDealOrderBean);
        RefundDto refundDto = cancelContext.getRefundDto();
        CounterOrderFormDto.RefundBean refundBean = null;
        if (refundDto != null) {
            refundBean = new CounterOrderFormDto.RefundBean();
            refundBean.setRefundFinaAvailAmt(refundDto.getRefundFinaAvailAmt());
            refundBean.setRefundFinaAvailMemo(refundDto.getRefundFinaAvailMemo());
        }

        String orderFormJson = CounterOrderFormUtil.createCounterOrderForm(sourceDealOrderBean, refundBean);
        counterCancelReqDto.setOrderFormMemo(orderFormJson);

        return counterCancelReqDto;
    }

    private CancelContext builContext(HttpServletRequest request){
        OperatorInfoCmd operatorInfoCmd  =  SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO,request);
        String cancelConfirmCmd = request.getParameter("cancelConfirmForm");
        String custInfoForm =  request.getParameter("custInfoForm");
        //1-用户发起；0-非用户发起
        String userCancelFlag = request.getParameter("userCancelFlag");
        //撤单原因
        String cancelMemo = request.getParameter("cancelMemo");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        OrderDto orderDto =  JSON.parseObject(cancelConfirmCmd,OrderDto.class);
        String othetInfoForm = request.getParameter("othetInfoForm");
        OtherInfoDto otherInfoDto = JSON.parseObject(othetInfoForm, OtherInfoDto.class);
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");
        String refundForm = request.getParameter("refundForm");
        RefundDto refundDto = JSON.parseObject(refundForm, RefundDto.class);
        AuditingOrderFileCmd auditingOrderFileCmd =  null;
        if(!org.springframework.util.StringUtils.isEmpty(materialinfoForm)){
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
        }


        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm,TransactorInfoDto.class);

        CancelContext context = new CancelContext();
        context.setUserCancelFlag(userCancelFlag);
        context.setCancelMemo(cancelMemo);
        context.setOrderDto(orderDto);
        context.setAuditingOrderFileCmd(auditingOrderFileCmd);
        context.setCustInfoDto(custInfoDto);
        context.setOtherInfoDto(otherInfoDto);
        context.setTransactorInfoDto(transactorInfoDto);
        context.setOperatorInfoCmd(operatorInfoCmd);
        CustomerAppointmentInfoDto customerAppointmentInfoDto = new CustomerAppointmentInfoDto();
        customerAppointmentInfoDto.setAppointId(orderDto.getAppointmentDealNo());
        context.setCustomerAppointmentInfoDto(customerAppointmentInfoDto);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(orderDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        context.setDisInfoDto(disInfoDto);
        context.setRefundDto(refundDto);
        return context;
    }


    /**
     * 
     * queryCanCancel:(查询可撤单订单)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午3:53:16
     */
    @RequestMapping("/tmscounter/querycancancel.htm")
    public ModelAndView queryCanCancel(HttpServletRequest request,HttpServletResponse response) throws Exception{
        String custNo= request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        List<OrderDto> canCancelOrderList = new ArrayList<>();
        List<OrderDto> canCancelOrders = tmsCounterService.queryCanForceCancelOrder(custNo, disInfoDto);
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        if(user.getInHBJG() && !CollectionUtils.isEmpty(canCancelOrders)){
            QueryNotHBJGFundListResponse queryNotHBJGFundListResponse = queryNotHBJGFundListService.query(new QueryNotHBJGFundListRequest());
            for(OrderDto orderDto : canCancelOrders){
                if(!queryNotHBJGFundListResponse.getFundCodes().contains(orderDto.getProductCode())){
                    canCancelOrderList.add(orderDto);
                }
            }
        }else {
            canCancelOrderList.addAll(canCancelOrders);
        }
        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("canCancelOrders",canCancelOrderList);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    /**
     * 
     * cancelValid:(撤单校验)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年4月11日 下午9:03:42
     */
    @RequestMapping("/tmscounter/high/cancelvalid.htm")
    public ModelAndView cancelValid(HttpServletRequest request,HttpServletResponse response) throws Exception{
        // 产品代码
        String productCode = request.getParameter("productCode");
        String mBusiCode = request.getParameter("mBusiCode");

        String workDay = tmsCounterService.getHighSystemWorkDay();

        String appDt = workDay;
        String appTm = "145959";

        logger.info("cancelValid|appDt:{}, appTm:{}, productCode:{}, mBusiCode:{}", new Object[] {appDt, appTm, productCode, mBusiCode});
        if(StringUtils.isEmpty(appDt) || StringUtils.isEmpty(appTm) || StringUtils.isEmpty(productCode) || StringUtils.isEmpty(mBusiCode)){
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
        if(highProductBaseBean == null ){
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }
        
        String busiType = convertBusiType(mBusiCode);
        // 撤单日期超过预约结束日期标识
        boolean overAppointFlag = false;
        if(StringUtils.isNotEmpty(busiType) && isSupportAdvance(highProductBaseBean.getIsScheduledTrade(), busiType)){
            String appDtmStr = appDt + appTm;
            Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
            ProductAppointmentInfoBean  productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(productCode, busiType, highProductBaseBean.getShareClass(), TmsCounterConstant.HOWBUY_DISCODE, appDtm);
            
            if(productAppointmentInfoBean == null){
                overAppointFlag = true;
            }else{

                if(workDay.compareTo(productAppointmentInfoBean.getApponitEndDt()) > 0){
                    overAppointFlag = true;
                } 
            }
        }
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        CancelValidRstDto cancelValidRstDto = new CancelValidRstDto();
        cancelValidRstDto.setOverAppointFlag(overAppointFlag);
        rst.setBody(cancelValidRstDto);
        WebUtil.write(response, rst);
        return null;
    }
    
    private String convertBusiType(String mBusiCode){
        
        if(BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode) || BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode)){
            // 购买
            return "0";
        }
        
        if(BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode)){
            return "1";
        }
        
        return null;
    }
    
    
    /**
     * 
     * isSupportAdvance:(TODO 这里用一句话描述这个方法的作用)
     * @param supportAdvanceFlag 0-不支持提前购买赎回 1-支持提前购买 2-支持提前赎回 3-支持提前购买赎回
     * @param busiType 业务类型 0-购买 1-赎回 
     * @return
     * <AUTHOR>
     * @date 2018年3月9日 下午3:58:49
     */
    private boolean isSupportAdvance(String supportAdvanceFlag, String busiType){
        
        if(StringUtils.isEmpty(supportAdvanceFlag)){
            return false;
        }
        
        // 是否支持购买提前下单
        if(BusiTypeEnum.BUY.getCode().equals(busiType)){
            if(SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(supportAdvanceFlag)){
                return true;
            }
        }
        
        // 是否支持赎回提前下单
        if(BusiTypeEnum.SELL.getCode().equals(busiType)){
            if(SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag)){
                return true;
            }
        }
        
        return false;
        
    }
}

