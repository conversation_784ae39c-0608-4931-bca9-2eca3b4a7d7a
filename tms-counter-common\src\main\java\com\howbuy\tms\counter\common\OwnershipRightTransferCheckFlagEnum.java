package com.howbuy.tms.counter.common;

import com.howbuy.tms.counter.common.util.StringUtils;

/**
 * @Description:股权份额维护订单审核状态枚举
 * @Author: yun.lu
 * Date: 2023/5/22 13:40
 */
public enum OwnershipRightTransferCheckFlagEnum {
    WAIT_OPERATE("0", "待维护"),
    WAIT_CHECK("1", "待审核"),
    CHECKED("2", "已审核");


    private String checkFlag;

    private String desc;

    OwnershipRightTransferCheckFlagEnum(String checkFlag, String desc) {
        this.checkFlag = checkFlag;
        this.desc = desc;
    }

    /**
     * 根据checkFlag获取中文描述
     */
    public static String getDescByFlag(String checkFlag) {
        if (StringUtils.isEmpty(checkFlag)) {
            return null;
        }
        for (OwnershipRightTransferCheckFlagEnum value : OwnershipRightTransferCheckFlagEnum.values()) {
            if (value.checkFlag.equals(checkFlag)) {
                return value.desc;
            }
        }
        return null;
    }
}
