/**
*基金转换
*<AUTHOR>
*@date 2017-04-01 15:12
*/
$(function(){
	Init.init();
	Exchange.init();
});
var Exchange = {
	init:function(){
		
		// 初始录入订单信息
		Exchange.initExchangeOrderInfoTable();
		
		// 确认提交
		$("#confimExchangeBtn").on('click',function(){
			if (Exchange.validateFund()){
				Exchange.confirm();
			}
		});

		// 查询客户信息
		$("#queryCustInfoBtn").on('click',function(){
			Exchange.initExchangeOrderInfoTable();
			QueryCustInfo.queryCustInfo();

			// 仅机构投资普通投资者 需要显示双录材料上传入口
            if(QueryCustInfo.custInfo.invstType == '0' && QueryCustInfo.custInfo.investorType == "0"){
                $("#uploadFileText").show();
                $("#uploadFileForm").show();
            } else {
                $("#uploadFileText").hide();
                $("#uploadFileForm").hide();
			}
            // 清空之前的双录文件
            $("#fileNameShow").val("");
            $("#fileNameShow").attr("filePath", "");
		});
		
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});

        /**
         * 上传文件 文件名展示处理
         */
        $("#videoFile").change(function(){
            Exchange.videoFileChange();
        });

        /**
         * 上传文件
         */
        $("#fileSubmit").on('click',function(){
            Exchange.fileUpload();
        });

	},

	validateFund:function(){
		// 转入基金
		var tfundInfo = Exchange.tfundInfo;
		// 转出基金
		var fundInfo = Exchange.fundInfo;

		var appTm = $("#appTm").val();
		if(appTm === undefined || appTm.length === 0){
			CommonUtil.layer_tip("下单时间不能为空");
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false
		}

		if ((fundInfo.jointOpenDayRegion !== undefined && fundInfo.jointOpenDayRegion !== '') || (tfundInfo.jointOpenDayRegion !== undefined && tfundInfo.jointOpenDayRegion !== '')){
			var jointOpenDayRegion = fundInfo.jointOpenDayRegion;
			var endTm = fundInfo.endTm;
			var tendTm = tfundInfo.endTm;
			if (endTm === undefined || endTm === ''){
				endTm = tendTm;
				jointOpenDayRegion = tfundInfo.jointOpenDayRegion;
			}else if (tendTm !== undefined && tendTm !== '' && tendTm < endTm){
				endTm = tendTm;
				jointOpenDayRegion = tfundInfo.jointOpenDayRegion;
			}
			var flag = appTm >= endTm && appTm <= CONSTANTS.HIGH_COUNTER_END_TIME
			if (jointOpenDayRegion !== undefined && jointOpenDayRegion.length !== 0 && flag){
				layer.open({
					title: ['风险提示', true],
					type: 1,
					area: ['320px', 'auto'],
					btn: ['确定', '取消'],
					skin: 'layui-layer-rim',
					btnAlign: 'l',
					content: jointOpenDayRegion,
					yes: function (index, layero) {
						layer.closeAll();
						Exchange.confirm();
					},
					cancel: function (index) {
						CommonUtil.enabledBtn("confimExchangeBtn");
					}
				});
				return false;
			}


		}

		return true;
	},
	
	
	/***
	 * 确认基金转换
	 */	
	confirm : function(dealAppNo){
		
		// 暂只支持单选
		var selectedCheckboxs = $("#exchangeConfirmForm").find("input[type='radio'][name='checkExchangeFund']:checked");
		
		CommonUtil.disabledBtn("confimExchangeBtn");
		
		if(selectedCheckboxs.length <= 0){
			CommonUtil.layer_tip("请选择要转换的订单");
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false;
		}
		
		// 选择的转换基金信息list
		var exchangeFunds = Exchange.selectExchangeFundInfos(selectedCheckboxs);
		
		// 校验提交的转换基金申请份额是否为空
		var checkOutVolFlag = true;
		var checkInfundFlag = true;
		$(".appVolClass").css("border-color","");
		$(".tFundCodeClass").css("border-color","");
		$(exchangeFunds).each(function(index,obj){
			var selIndex = obj.selIndex;
			var appVol = obj.appVol;
			var tFundCode = obj.tFundCode;
			
			if(isEmpty(appVol)){
				$("#appVol_"+selIndex).css("border-color","red");
				CommonUtil.layer_tip("申请转出份额不能为空");
				checkOutVolFlag = false;
				return false;
			}
			
			if(isEmpty(tFundCode)){
				$("#tFundCode_"+selIndex).css("border-color","red");
				CommonUtil.layer_tip("申请转入基金代码不能为空");
				checkInfundFlag = false;
				return false;
			}
		});
		
		if(!checkOutVolFlag || !checkInfundFlag){
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false;
		}

        var filePath = $("#fileNameShow").attr("filePath");
        // 仅机构投资普通投资者 需要校验双录材料是否必填
        if(filePath == "" && QueryCustInfo.custInfo.invstType == "0" && QueryCustInfo.custInfo.investorType == "0"){
            CommonUtil.layer_tip("双录材料为空");
            CommonUtil.enabledBtn("confimExchangeBtn");
            return false
        }

		// 校验其他录入信息
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		transactorInfoForm.appDtm = transactorInfoForm.appDt +'' + transactorInfoForm.appTm;
		if(CommonUtil.isEmpty(transactorInfoForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false;
		}
		if(!Valid.valiadTradeTime(transactorInfoForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false;
		}
		
		if(!Validate.validateTransactorInfo(transactorInfoForm, QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false;
		}
		
		var dealAppNo ="";
		if(!(typeof ApplyExchange == "undefined")){
			dealAppNo = ApplyExchange.checkOrder.dealAppNo;
		}
		
		layer.confirm('确定提交吗？', {
            btn: ['确定', '取消']
        }, function (index) {
			// 风险提示
			var riskFlag = '0';
			if('PRO' !=  QueryCustInfo.custInfo.investorType && BuyValid.validRisk(QueryCustInfo.custInfo.custRiskLevel, Exchange.fundInfo.fundRiskLevel)){
				layer.open({
		            title: ['风险提示', true],
		            type: 1,
		            area: ['320px', 'auto'],
		            btn: ['确定', '取消'],
		            skin: 'layui-layer-rim', 
		            btnAlign: 'l',
		            content: "转入基金风险高于户风险等级承受能力，确认继续吗？",
		            yes: function (index, layero) { //或者使用btn1
		            	layer.closeAll();
		            	riskFlag = '1';
		            	Exchange.confirmValidate(dealAppNo, riskFlag, exchangeFunds, transactorInfoForm);
		            },
		            cancel: function (index) { 
		            	layerall_close();
		            	CommonUtil.enabledBtn("confimExchangeBtn");
		            }
		        }); 
			}else{
				Exchange.confirmValidate(dealAppNo, riskFlag, exchangeFunds, transactorInfoForm,filePath);
			}
		
        }, function(){  
 			layerall_close();
 		});
		
	},
	
	/**
	 * 基金转换当天存在确认份额--校验提示
	 * @param dealAppNo
	 * @param riskFlag
	 * @param exchangeFunds
	 * @param transactorInfoForm
	 */
	confirmValidate:function(dealAppNo, riskFlag, exchangeFunds, transactorInfoForm, filePath){

        	// 校验当前TA交易日是否有确认份额是否基金转换
			var uri = TmsCounterConfig.EXCHANGE_FUND_TADTACKVOL_VALIDATE_URL ||  {};
			var reqparamters = {
					"exchangeFunds": JSON.stringify(exchangeFunds), 
					"custInfoForm": JSON.stringify(QueryCustInfo.custInfo)};
			
			//console.log(reqparamters);
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
    		CommonUtil.ajaxAndCallBack(paramters, function(data){
    			layerall_close();
    			
    			if(CommonUtil.isArray(data) && data.length > 0){
    				
    				var retMsg = "";
    				$(data).each(function(index,element){
    					
    					var ackVol = element.desc || '0';
    					var bodyData = element.body || {};
    					var reqVdlDto = bodyData.reqVdlDto || {};
    					
    					retMsg += "转出基金: "+reqVdlDto.fundCode+", 申请转出: "+ reqVdlDto.appVol +"份, 当前TA交易日已经存在确认份额："+ ackVol+"份。<br>";
    		
    				});
    				retMsg = retMsg + "<br>是否继续转换？";
    				// 否继续赎回
    				layer.confirm(retMsg, {
    		            btn: ['是', '否']
    		        }, function (index) {
    		        	layerall_close();
    		        	Exchange.confirmSubmit(dealAppNo, riskFlag, exchangeFunds, transactorInfoForm,filePath);
    		        	
    		        }, function(){  
    		 			layerall_close();
    		 		});
    				
    			} else{
    				Exchange.confirmSubmit(dealAppNo, riskFlag, exchangeFunds, transactorInfoForm,filePath);
    			}
    		});
	},
	
	/**
	 * 确认提交
	 * @param dealAppNo
	 * @param riskFlag
	 * @param exchangeFunds
	 * @param transactorInfoForm
	 */
	confirmSubmit:function(dealAppNo, riskFlag, exchangeFunds, transactorInfoForm,filePath){
		var uri= TmsCounterConfig.EXCHANGE_FUND_CONFIRM_URL  ||  {};
		var reqparamters ={"dealAppNo": dealAppNo, 
				"riskFlag":riskFlag, 
				"exchangeFunds": JSON.stringify(exchangeFunds), 
				"custInfoForm": JSON.stringify(QueryCustInfo.custInfo), 
				"transactorInfoForm": JSON.stringify(transactorInfoForm),
				"filePath":filePath};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Exchange.callBack);
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			if($(".confimBtn").length > 0){
				CommonUtil.disabledBtnWithClass("confimBtn");
				CommonUtil.disabledBtn("abolishBtn");
			}
			CommonUtil.layer_tip("提交成功");

            // 清空之前的双录文件
            $("#fileNameShow").val("");
            $("#fileNameShow").attr("filePath", "");
		}else{
			CommonUtil.layer_tip("提交失败,"+respDesc+"("+respCode+")");
		}
		
		if(!$(".confimBtn").length > 0){
			CommonUtil.enabledBtn("confimExchangeBtn");
		}
	},
	
	/**
	 * 获取选择的TR data
	 */
	selectExchangeFundInfos:function (selectedCheckboxs){
		var excOutFundList=[];
		$(selectedCheckboxs).each(function(index,obj){
			var selIndex = $(obj).attr('data-index');
			//console.log(selIndex);
			
			// 注：属性与CounterExchangeReqDto对应
			var excOutFund = {};
			excOutFund.selIndex = selIndex;
			// 转出基金代码
			excOutFund.fundCode = $('#fundCode_'+selIndex).val();
			excOutFund.appVol = $('#appVol_'+selIndex).val();
			excOutFund.largeRedmFlag = $('#largeRedmFlag_'+selIndex).val();
			
			excOutFund.cpAcctNo = $('#cpAcctNo_'+selIndex).val();
			excOutFund.bankAcct = $('#bankAcct_'+selIndex).val();
			excOutFund.bankCode = $('#bankCode_'+selIndex).val();
			
			excOutFund.protocolNo = $('#protocolNo_'+selIndex).val();
			excOutFund.protocolType = $('#protocolType_'+selIndex).val();
			
			// 转入基金代码
			excOutFund.tFundCode = $('#tFundCode_'+selIndex).val();
			
			// 开放赎回日期
			excOutFund.openRedeDt = CommonUtil.formatData($('#openRedeDt_'+selIndex).val());

			excOutFundList.push(excOutFund);
		});
		//console.log(redeFundList);
		return excOutFundList;
	},
	
	/**
	 * 零售基金转换查询客户持仓
	 */
	queryCustHodlInfo:function(){
		var uri= TmsCounterConfig.FUND_QUERY_EXCHANGE_FUND_INFO_URL ||  {};
		var custNo = QueryCustInfo.custInfo.custNo || '';
		var disCode = QueryCustInfo.custInfo.disCode || '';
		var fundCode = $("#fundCode").val();
		//console.log("select custNo: "+ custNo + " disCode: " + disCode + "fundCode: "+fundCode);
		
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		if(isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入转出的基金代码");
			return false;
		}
		
		var reqparamters = {"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
		CommonUtil.ajaxAndCallBack(paramters, Exchange.queryCustHoldFundInfoCallBack);
	},
	
	/**
	 * 处理基金持仓信息
	 */
	queryCustHoldFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		var dtlResp = bodyData.dtlResp || {};
		var taTradeDt = bodyData.taTradeDt;
		Exchange.fundHold = dtlResp || {};
		Exchange.dtlList = dtlResp.balanceDtlList || [];
		Exchange.fundInfo = fundInfo;
		//console.log(QueryFundInfo.dtlList);

		if(Exchange.dtlList == null || Exchange.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
			
		} else{
			// 组装客户转出基金持仓列表
			var exchangeOrderInfoList=[];
			$(Exchange.dtlList).each(function(index,element){
				var holdInfo = {};
				holdInfo.fundCode = fundInfo.fundCode;
				holdInfo.fundStatus = fundInfo.fundStat;
				holdInfo.fundName = fundInfo.fundAttr;
				
				holdInfo.cpAcctNo = element.cpAcctNo;
				holdInfo.bankCode = element.bankCode;//银行代码
				holdInfo.bankName = element.bankName;//银行名称
				holdInfo.bankAcctNo = element.bankAcctNo;//银行卡号
				holdInfo.availVol = element.availVol;//可用份额
				holdInfo.protocolNo = element.protocolNo;//协议号
				holdInfo.protocolType = element.protocolType;//协议类型
				
				holdInfo.openRedeDt = CommonUtil.formatData(element.openRedeDt);//开放赎回日期

				exchangeOrderInfoList.push(holdInfo);
			});
			
			$("#exchangeOrderInfoId").empty();
			// 重新渲染列表
			$(exchangeOrderInfoList).each(function(index,element){
				
				var hiddenHTML = '';
				hiddenHTML +='<input type="hidden" id="cpAcctNo_'+index+'" name="cpAcctNo_'+index+'" value="'+element.cpAcctNo+'">';
				hiddenHTML +='<input type="hidden" id="bankAcct_'+index+'" name="bankAcct_'+index+'" value="'+element.bankAcctNo+'">';
				hiddenHTML +='<input type="hidden" id="bankCode_'+index+'" name="bankCode_'+index+'" value="'+element.bankCode+'">';
				hiddenHTML +='<input type="hidden" id="protocolNo_'+index+'" name="protocolNo_'+index+'" value="'+element.protocolNo+'">';
				hiddenHTML +='<input type="hidden" id="protocolType_'+index+'" name="protocolType_'+index+'" value="'+element.protocolType+'">';
				hiddenHTML +='<input type="hidden" id="openRedeDt_'+index+'" name="openRedeDt_'+index+'" value="'+CommonUtil.formatData(element.openRedeDt)+'">';

				var tdList = [];
				tdList.push('<td><input class="selectExchangeFund" name="checkExchangeFund" onclick="Exchange.queryClickAgreement(' + index + ')" type="radio" data-index="' +index+ '"></input>'+hiddenHTML+'</td>');
				tdList.push('<td><input id="fundCode_'+index+'" name="fundCode_'+index+'" type="text" value="'+element.fundCode+'" readonly="true"></td>');
				tdList.push('<td>'+element.fundName+'</td>');
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.FUND_STATE, element.fundStatus)+'</td>');
				tdList.push('<td><input type="text" value="'+element.bankName+' '+element.bankAcctNo+'" readonly="true"></td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.availVol)+'</td>');
				// 可赎回日期可编辑
				var openRedeDt = '--';
				if(!CommonUtil.isEmpty(element.openRedeDt) && element.openRedeDt > $("#appDt").val()){
					openRedeDt = element.openRedeDt;
				}
				tdList.push('<td>'+openRedeDt+'</td>');
				tdList.push('<td><input type="text" class="appVolClass" name="appVol_'+index+'" id="appVol_'+index+'" isnull="false" datatype="s" errormsg="申请转出份额" placeholder="请输入" onkeyup="Exchange.validatorAppVol('+index+','+element.availVol+', this);"></td>');
				tdList.push('<td><input type="text" name="appVolCapitalForSell_'+index+'" id="appVolCapitalForSell_'+index+'" isnull="false" datatype="s" errormsg="申请转出份额" readonly="true"></td>');
				tdList.push('<td>'+element.protocolNo+'</td>');
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType)+'</td>');

				// 转入基金
				tdList.push('<td><div class="searchIn"><input type="text" class="tFundCodeClass" data-tFundCode-index="'+index+'" id="tFundCode_'+index+'" name="tFundCode_'+index+'"><a href="javascript:void(0)" class="searchIcon"></a><div/></td>');
				tdList.push('<td id="tFundName_'+index+'">--</td>');
				tdList.push('<td id="tFundStatus_'+index+'">--</td>');
				tdList.push('<td id="tFundRiskLevel_'+index+'">--</td>');//转入风险等级
				
				// 巨额赎回顺延标记
				tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag_'+index+'" id="largeRedmFlag_'+index+'" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option><option selected="selected" value="1">顺延</option></select></span></td>');
				
				var trAppendHtml = '<tr class="text-c" id="exchangeFundInfo_tr_'+index+'">'+tdList.join() +'</tr>';
				$("#exchangeOrderInfoId").append(trAppendHtml);
			});
			
			// 重新绑定转入基金输入框事件
			$(".tFundCodeClass").on('blur',function(){
				Exchange.queryTFundInfo(this);
			});
		}
	},

	queryClickAgreement:function(index){
		var custNo = QueryCustInfo.custInfo.custNo || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}

		if(Exchange.dtlList == null || Exchange.dtlList.length <=0){
			CommonUtil.layer_tip("该转出基金没有查询到持仓信息，无法转换。");
			return false;
		}
		var tFundCode = $('#tFundCode_'+index).val();
		if(isEmpty(tFundCode)){
			return false;
		}

		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo) || {};
		var  uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":tFundCode, "custInfoForm": custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Exchange.queryClickAgreementCallBack);


	},
	queryClickAgreementCallBack:function(data){
		var bodyData = data.body || {};
		Exchange.tfundInfo = bodyData.fundInfo || {};

		var queryAgreementDto = bodyData.queryAgreementDto;
		//匹配的概要
		var outlineList = queryAgreementDto.outlineList || [];
		//匹配基金合同
		var fundContractList = queryAgreementDto.fundContractList || [];
		//匹配招募说明书
		var introduceList = queryAgreementDto.introduceList || [];

		$("#queryAgreementId").html("");
		Exchange.showQueryAgreement(outlineList);
		Exchange.showQueryAgreement(fundContractList);
		Exchange.showQueryAgreement(introduceList);
		$("#queryAgreementMainId").show();

	},

	/**
	 * 查询转入基金
	 * @param thisObj
	 */
	queryTFundInfo:function(thisObj){
		var custNo = QueryCustInfo.custInfo.custNo || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		
		if(Exchange.dtlList == null || Exchange.dtlList.length <=0){
			CommonUtil.layer_tip("该转出基金没有查询到持仓信息，无法转换。");
			return false;
		}
		
		var tFundCode = $(thisObj).val();
		if(isEmpty(tFundCode)){
			CommonUtil.layer_tip("请输入转入的基金代码");
			return false;
		}
		// 记录当前点击转入基金 input 的行索引
		var input_tFundCode_index = $(thisObj).attr('data-tFundCode-index');
		Exchange.input_tFundCode_index = input_tFundCode_index;

		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo) || {};
		var  uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":tFundCode, "custInfoForm": custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Exchange.queryTFundInfoCallBack);
	},
	
	queryTFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		Exchange.tfundInfo = bodyData.fundInfo || {};
		QueryFundInfo.checkFundInfo(Exchange.tfundInfo);

		$("#tFundName_"+Exchange.input_tFundCode_index).html(Exchange.tfundInfo.fundAttr || '');
		$("#tFundStatus_"+Exchange.input_tFundCode_index).html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, Exchange.tfundInfo.fundStat));
		$("#tFundRiskLevel_"+Exchange.input_tFundCode_index).html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, Exchange.tfundInfo.fundRiskLevel, ''));

		var queryAgreementDto = bodyData.queryAgreementDto;
		//匹配的概要
		var outlineList = queryAgreementDto.outlineList || [];
		//匹配基金合同
		var fundContractList = queryAgreementDto.fundContractList || [];
		//匹配招募说明书
		var introduceList = queryAgreementDto.introduceList || [];

		// 暂只支持单选
		var selectedCheckboxs = $("#exchangeConfirmForm").find("input[type='radio'][name='checkExchangeFund']:checked");
		$(selectedCheckboxs).each(function(index,obj){
			var selIndex = $(obj).attr('data-index');
			// 转入基金代码
			var tFundCode = $('#tFundCode_'+selIndex).val();
			if(isEmpty(tFundCode)){
				return false;
			}
			console.log(tFundCode);

			$("#queryAgreementId").html("");
			Exchange.showQueryAgreement(outlineList);
			Exchange.showQueryAgreement(fundContractList);
			Exchange.showQueryAgreement(introduceList);
			if(outlineList.length <=0 && fundContractList <=0 && introduceList <=0){
				$("#queryAgreementMainId").hide();
			} else {
				$("#queryAgreementMainId").show();
			}
		});

	},

	showQueryAgreement:function(list){
		if(list.length <=0){
			return;
		}
		$(list).each(function(index, element){
			//var appendHtml = '<a href="' + element.caFileContent + '">';
			var appendHtml = '<a>';
			appendHtml = appendHtml + element.caName + '</a>   ';
			$("#queryAgreementId").append(appendHtml);
		});
	},
	
	/**
	 * 校验输入份额(事件:onkeyup)
	 */
	validatorAppVol : function(index, availVol, thisObj){
		// console.log(availVol + " "+ thisObj.value);
		var appVol = thisObj.value;
		if(!/^[0-9]+\.?[0-9]{0,2}$/.test(appVol)){CommonUtil.layer_tip('只能输入数字且小数点后两位');thisObj.value='';}
		
		var cnAppVol = CommonUtil.digit_uppercase(appVol);
		$("#appVolCapitalForSell_"+index).val(cnAppVol.replace('元', '份'));
		
		if(appVol > availVol ){
			CommonUtil.layer_tip("申请份额不能大于可用份额");
			$(thisObj).css("border-color","red");
			CommonUtil.enabledBtn("confimExchangeBtn");
			return false;
		} else{
			$(thisObj).css("border-color","");
		}
	},
	
	/**
	 * 初始化基金转换订单信息Table
	 */
	initExchangeOrderInfoTable:function(){
		var tdList = [];
		tdList.push('<td>&nbsp;</td>');
		// 转出
		tdList.push('<td><div class="searchIn"><input id="fundCode" type="text" ><a href="javascript:void(0)" class="searchIcon" id="searchFundOut"></a></div></td>');
		tdList.push('<td id="fundName">--</td>');
		tdList.push('<td id="fundStatus">--</td>');
		tdList.push('<td><span class="select-box inline"><select name="cpAcctNo" class="select" id="selectBank"></select></span><input type="hidden" id="bankCode" name="bankCode" value="0"></td>');
		tdList.push('<td id="availVol"></td>');
		tdList.push('<td><input type="text" placeholder="请输入" class="appVolForExchange" name="appVol" id="appVol" isnull="false" datatype="s" errormsg="申请转出份额"></td>');
		tdList.push('<td><input type="text" class="appVolCapitalForExchange" name="appVolCapitalForExchange" id="appVolCapitalForExchange" isnull="false" datatype="s" errormsg="申请转出份额" readonly="true"></td>');
		tdList.push('<td id="protocolNo">--</td>');
		tdList.push('<td id="protocolType">--</td>');
		
		// 转入
		tdList.push('<td><div class="searchIn"><input type="text" class="tFundCodeClass" id="tFundCode" name="tFundCode"><a href="javascript:void(0)" class="searchIcon" id="searchFundIn"></a></div></td>');
		tdList.push('<td id="tFundName">--</td>');
		tdList.push('<td id="tFundStatus">--</td>');
		tdList.push('<td id="tFundRiskLevel">--</td>');
		
		// 巨额赎回顺延标记
		tdList.push('<td><span class="select-box inline"><select name="largeRedmFlag" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记"><option value="0">不顺延</option><option selected="selected" value="1">顺延</option></select></span></td>');
		
		var trAppendHtml = '<tr class="text-c">'+tdList.join() +'</tr>';
		$("#exchangeOrderInfoId").empty();
		$("#exchangeOrderInfoId").append(trAppendHtml);
		
		// 转出绑定事件
		$("#searchFundOut").on('click',function(){
			Exchange.queryCustHodlInfo();
		});
		
		// 转入绑定事件
		$(".tFundCodeClass").on('blur',function(){
			Exchange.queryTFundInfo(this);
		});
	},

    videoFileChange: function () {
        var value = $("#videoFile").val();
        var fileName = value.substring(value.lastIndexOf("\\") + 1);
        $("#fileName").val(fileName);
        var width = parseInt(fileName.length) * 10;
        $("#fileName").css("width", width + "px");
    },

    fileUpload: function () {
        var target = $("#videoFile")[0];
        var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
        var fileTypeArray = new Array("wav", "avi", "wmv", "mpg", "mpeg", "mov", "rm", "ram", "swf", "flv", "mp4", "mp3", "wma", "avi", "rm", "rmvb", "flv", "mpg", "mkv", "m4a");
        var fileSize = 0;
        // 校验文件大小
        if (isIE && !target.files) {
            var filePath = target.value;
            var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
            var file = fileSystem.GetFile(filePath);
            fileSize = file.Size;
        } else {
            fileSize = target.files[0].size;
        }
        var size = fileSize / 1024;
        if (size > 300 * 1024) {
            CommonUtil.layer_tip("文件不能超过300M");
            return
        }
        // 校验文件格式
        var name = target.value;
        var fileType = name.substring(name.lastIndexOf(".") + 1).toLowerCase();
        if (fileTypeArray.indexOf(fileType) == -1) {
            CommonUtil.layer_tip("请选择音频/视频文件格式");
            return
        }

        var formData = new FormData();
        var appDt = $("#appDt").val();
        formData.append("userFile", target.files[0]);
        formData.append("appDt", appDt);

        var url = TmsCounterConfig.UPLOAD_DOUBLE_RECORD_FILE_URL;

        $.ajax({
            url: url,
            dataType:'json',
            type:'POST',
            data: formData,
            processData : false, // 使数据不做处理
            contentType : false, // 不要设置Content-Type请求头
            success: function(data){
                if (data.code == '0000') {
                    CommonUtil.layer_tip("上传成功");

                    var fileName = name.substring(name.lastIndexOf("\\") + 1);
                    $("#fileNameShow").val(fileName);

                    $("#fileNameShow").attr("filePath", data.body);
                } else {
                    CommonUtil.layer_tip("上传失败");
				}
            },
            error:function(response){
                CommonUtil.layer_tip("上传失败");
            }
        });
    }
	
};

