$(function(){

	$("#returnBtn").on('click',function(){
		CheckTrans.confirm(CounterVolCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckTrans.confirm(CounterVolCheck.Succ);
	});

    if('crm' == CommonUtil.getParam("source")){
        OnLineOrderFile.selfLogin(CommonUtil.getParamJson());
    }

    Init.init();

    var checkId = CommonUtil.getParam("checkId") ||  CommonUtil.getParam("forId");
    var hboneNo = CommonUtil.getParam("hboneNo");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckTrans.checkOrder = {};	 
	CheckTrans.init(checkId,custNo,disCode,idNo, hboneNo);
});

var CheckTrans = {	

	init:function(checkId, custNo, disCode,idNo, hboneNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfoTransVolCheck(custNo, idNo, disCode, hboneNo);

		// 复审
		var checkNode = "1";
		
		// 设置转出和转入信息
		QueryCheckOrder.queryMergeTransCheckOrderById(checkId, CheckTrans.queryCheckVolOrderByIdBack, checkNode);
	}, 
	
	queryCheckVolOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckTrans.checkOrder = bodyData.checkOrder || {};
		CheckTrans.checkDtlOrder = bodyData.checkDtlOrder || [];
        var orderFile = bodyData.orderFile || {};// CRM线上资料
		// 转入持仓
		var respData = bodyData.respData;
		CheckTrans.transInDisCode = respData.disCode;
		CheckTrans.transInBalDtl = respData.custBalDtlList || [];
				
		if(CommonUtil.isEmpty(CheckTrans.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckTrans.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}

        OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料

		// 转出银行卡资产信息
		BodyView.setTransOutTableViewNew("highTransOutCustBals","transOutCustBals",CheckTrans.checkDtlOrder, CheckTrans.checkOrder.disCode,"assetBody");
		// 设置转入银行卡信息
		QueryCustInfo.getCustBankInfos(CheckTrans.checkOrder.txAcctNo, CheckTrans.checkOrder.disCode, CheckTrans.setTransInBankInfo);
		// 转入银行卡资产信息
		BodyView.setTransInCustBalsTableView("checkInCustBals", CheckTrans.transInBalDtl, CheckTrans.transInDisCode);
		/**other*/
		BodyView.setCheckOperInfoView(CheckTrans.checkOrder);
        //如果选择注销银行卡，需要调用资金接口查询在途资产
		var selectCancelCard = $("#selectCancelCard").val();
		if(selectCancelCard == '1'){
			$("#intransAssetDiv").show();
            CheckTrans.queryAssetInfo();
		}
	},

    queryAssetInfo : function(){

        // 查询客户银行卡持仓
        var  uri= TmsCounterConfig.QUERY_INTRANSIT_ASSET_URL ||  {};
        var reqparamters = {};
        //优先取订单详情数据 取不到取在途资产
        if(CheckTrans.checkDtlOrder != null && CheckTrans.checkDtlOrder.length != 0 ){
            reqparamters.cpAcctNo = CheckTrans.checkDtlOrder[0].cpAcctNo;
        }else{
            CheckTrans.intransitAssetList = JSON.parse(CheckTrans.checkOrder.intransitAssetMemo);
            reqparamters.bankAcct = CheckTrans.intransitAssetList[0].bankAcct;
		}
        reqparamters.txAcctNo = CheckTrans.checkOrder.txAcctNo;

        var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
        CommonUtil.ajaxAndCallBack(paramters, CheckTrans.queryAssetInfoCallBack);
    },

    queryAssetInfoCallBack:function(data){
        var bodyData = data.body || {};
        var respData = bodyData.batchStatList || [];

        CheckTrans.assetDetailList = respData.detailList || [];

        // 转入银行卡资产信息
        $("#assetBody").empty();
        if(CheckTrans.assetDetailList.length <=0){
            var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
            $("#assetBody").append(trHtml);
            return false;

        }else{
            $(CheckTrans.assetDetailList).each(function(index,element){
                var trList = [];
                trList.push('');
                trList.push(CommonUtil.formatData(element.prodCode));
                trList.push(CommonUtil.formatData(element.fundName));
                trList.push(CommonUtil.formatData(element.busiCode));
                trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.formatData(element.bankAcctName));
                trList.push(CommonUtil.formatAmount(element.occurBalance));
                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#assetBody").append(trAppendHtml);
            });
        }
    },
	setTransInBankInfo : function(data){
		var body = data.body || {};
		var custBanks = body.custBanks || [];
		
		var checkInBanks = {};
		checkInBanks.bankAcct = CheckTrans.checkOrder.bankAcct;
		checkInBanks.bankCode = CheckTrans.checkOrder.bankCode;
		
		$(custBanks).each(function(index, element) {
			if(element.cpAcctNo == CheckTrans.checkOrder.cpAcctNo){
				checkInBanks.bankRegionName = element.bankRegionName;
				return;
			}		
		});
		
		// 转入银行卡
		BodyView.setTransInBankTableView("checkInBanks", checkInBanks);
	},
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		
		var uri= TmsCounterConfig.CHECK_MERGE_TRANS_CONFIRM_URL ||  {};
		
		if(CounterVolCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入退回原因");
				return false;
			}
			CheckTrans.checkFaildDesc = $("#checkFaildDesc").val();
		}
		
		var checkDtlOrderForm = [];
		checkDtlOrderForm.push(CheckTrans.checkDtlOrder[0]);
		var reqparamters ={
				"checkFaildDesc":CheckTrans.checkFaildDesc || '',
				"checkStatus":checkStatus,
				"checkedOrderForm":JSON.stringify(CheckTrans.checkOrder),
            	"intransitAssetMemo" : JSON.stringify(CheckTrans.assetDetailList),
                "materialinfoForm":JSON.stringify(OnLineOrderFile.buildOrderCheckFile())
				//"checkDtlOrderForm":JSON.stringify(checkDtlOrderForm)
				};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckTrans.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_alert(respDesc);
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_alert(respDesc);
		}
	}
}
