package com.howbuy.tms.counter.service.trade;

import com.howbuy.tms.counter.dto.CounterOwnershipRightTransferReqDto;
import com.howbuy.tms.counter.dto.QueryOwnershipRightTransferReqDto;
import com.howbuy.tms.counter.dto.QueryOwnershipRightTransferRespDto;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.AbstractOwnershipRightTransferDtlBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2023/5/19 14:38
 */
public interface OwnershipRightTransferOrderService {
    /**
     * 查询股权份额转让订单信息列表
     *
     * @param queryOwnershipRightTransferReqDto 请求入参
     * @return 股权份额转让订单信息查询相应
     */
    QueryOwnershipRightTransferRespDto queryOwnershipRightTransfer(QueryOwnershipRightTransferReqDto queryOwnershipRightTransferReqDto) throws Exception;

    /**
     * 根据订单号查询股权份额转让详情
     *
     * @param dealDtlNo 订单号
     * @return 股权份额转让详情
     */
    AbstractOwnershipRightTransferDtlBean queryOwnershipRightTransferDtlByDealDtlNo(String dealDtlNo) throws Exception;

    /**
     * 根据订单号查询股权份额转让详情
     *
     * @param dealAppNo 变更申请单号
     * @return 股权份额转让详情
     */
    AbstractOwnershipRightTransferDtlBean queryOwnershipRightTransferDtlByDealAppNo(String dealAppNo) throws Exception;

    /**
     * 股权份额转让修改
     *
     * @param counterOwnershipRightTransferReqDto 请求入参
     * @return 申请单号
     */
    String counterOwnershipRightTransfer(CounterOwnershipRightTransferReqDto counterOwnershipRightTransferReqDto) throws Exception;

    /**
     * 下载股权份转让订单信息
     */
    void downloadOwnershipRightTransfer(HttpServletRequest request, HttpServletResponse response) throws Exception;
}
