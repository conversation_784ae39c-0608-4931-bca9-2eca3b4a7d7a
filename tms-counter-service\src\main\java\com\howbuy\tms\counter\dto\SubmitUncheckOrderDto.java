/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseRequestDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:(柜台提交审核订单)
 * @date 2017年3月28日 下午8:58:29
 * @since JDK 1.6
 */
public class SubmitUncheckOrderDto extends BaseRequestDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 1L;
    /**
     * 申请单号
     */
    private String dealAppNo;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 子交易账号
     */
    private String subTxAcctNo;
    /**
     * 资金交易账号
     */
    private String cpAcctNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金份额类
     */
    private String fundShareClass;
    /**
     * 对方基金代码
     */
    private String tFundCode;
    /**
     * 对方基金份额类
     */
    private String tFundShareClass;
    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    /**
     * 申请份额
     */
    private BigDecimal appVol;
    /**
     * 申请日期
     */
    private String appDt;
    /**
     * 申请时间
     */
    private String appTm;
    /**
     * 交易代码
     */
    private String txCode;
    /**
     * 支付方式
     */
    private String paymentType;
    /**
     * 折扣率
     */
    private BigDecimal discountRate;
    /**
     * 协议类型
     */
    private String protocolType;
    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 校验标识
     */
    private String checkFlag;
    /**
     * 分红方式
     */
    private String fundDivMode;
    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 交易员证件号
     */
    private String transactorIdNo;

    /**
     * 交易员证件类型
     */
    private String transactorIdType;
    /**
     * 交易员姓名
     */
    private String transactorName;
    /**
     * 操作员号
     */
    private String operatorNo;
    /**
     * 投顾号
     */
    private String consCode;
    /**
     * 返回码
     */
    private String returnCode;
    /**
     * 描述
     */
    private String description;
    /**
     * 备注
     */
    private String memo;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 审核人
     */
    private String checker;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 申请标志
     */
    private String appFlag;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 基金名称
     */
    private String fundName;
    /**
     * 大额赎回标志
     */
    private String largeRedmFlag;
    /**
     * 代理标志
     */
    private String agentFlag;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 创建时间
     */
    private Date createDtm;
    /**
     * 审核时间
     */
    private Date checkDtm;
    /**
     * 修改时间
     */
    private Date updateDtm;
    /**
     * 银行账号
     */
    private String bankAcct;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 交易日期
     */
    private String taTradeDt;
    /**
     * 基金净值
     */
    private BigDecimal nav;
    /**
     * 异常交易类型
     */
    private String unusualTransType;
    /**
     * 风险标志
     */
    private String riskFlag;
    /**
     * 预约折扣
     */
    private BigDecimal appointmentDiscount;
    /**
     * 估算费用
     */
    private BigDecimal esitmateFee;
    /**
     * 预约交易单号
     */
    private String appointmentDealNo;

    /**
     * 赎回去向
     * 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
     */
    private String redeemCapitalFlag;
    /**
     * 垫资金额
     */
    private BigDecimal advanceAmt;
    /**
     * 费率
     */
    private BigDecimal feeRate;
    /**
     * 对方基金名称
     */
    private String tFundName;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 预约金额
     */
    private BigDecimal appointmentAmt;

    /**
     * 强制撤单标识(柜台校验必传): 0-强制; 1-非强制
     */
    private String forceCancelFlag;

    /**
     * 预约类型0-系统生成 1-投顾生成
     */
    private String appointmentDealNoType;

    /**
     * 折扣模式  0-预约生成 1-人工生成
     */
    private String discountModel;

    /**
     * 撤单原因
     */
    private String cancelMemo;

    /**
     * 份额业务类型 (份额合并或迁移)
     */
    private String shareType;

    /**
     * 全赎带出未付收益1：是; 0-否
     */
    private String allRedeemFlag;
    /**
     * 是否到期赎回
     */
    private String isRedeemExpire;
    /**
     * 预计到期日期
     */
    private String preExpireDate;

    /**
     * 对方网点
     */
    private String tOutletCode;

    /**
     * 转托管业务类型1-跨市场；2-场外跨销售机构
     */
    private String transferTubeBusiType;

    /**
     * 对方销售人处投资者基金交易账号
     */
    private String tSellerTxAcctNo;

    /**
     * 对方销售人代码
     */
    private String tSellerCode;

    /**
     * 原申请单号
     */
    private String originalAppDealNo;
    /**
     * 高端明细号
     */
    private Map<String, String> fundCodeAndDtlNoMap;

    /**
     * 可赎回日期
     */
    private String allowDt;
    /**
     * 复购协议号
     */
    private String repurchaseProtocolNo;
    /**
     * 复购类型
     */
    private String repurchaseType;

    /**
     * 是否注销原卡：1-是，0-否
     */
    private String cancelCard;

    /**
     * 在途资产
     */
    private String intransitAssetMemo;

    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;

    /**
     * 转入客户信息
     */
    private String inTxAcctNo;
    /**
     * 转入资金交易账号
     */
    private String inCpAcctNo;
    /**
     * 转入分销商代码
     */
    private String inDisCode;

    /**
     * 撤单实时回储蓄罐 04=银行卡 06=储蓄罐
     */
    private String withdrawDirection;
    /**
     * 订单信息
     */
    private String orderFormMemo;
    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 是否匹配非交易
     */
    private String matchedTransfer;

    /**
     * 下单订单号
     */
    private List<String> orderedDealNos;

    /**
     * 垫资状态 1-垫资 0-不垫资
     */
    private String advanceStatus;

    /**
     * 问卷答案
     */
    private String surveyAnswer;

    /**
     * 赎回比例
     */
    private BigDecimal appRatio;
    /**
     * 中台详细业务码
     */
    private String zBusiCode;

    /**
     * 赎回方式
     */
    private String customRatioType;

    /**
     * 赎回开关
     */
    private String openFlag;

    public String getCustomRatioType() {
        return customRatioType;
    }

    public void setCustomRatioType(String customRatioType) {
        this.customRatioType = customRatioType;
    }

    public String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFlag) {
        this.openFlag = openFlag;
    }

    public String getzBusiCode() {
        return zBusiCode;
    }

    public void setzBusiCode(String zBusiCode) {
        this.zBusiCode = zBusiCode;
    }

    public BigDecimal getAppRatio() {
        return appRatio;
    }

    public void setAppRatio(BigDecimal appRatio) {
        this.appRatio = appRatio;
    }

    public String getSurveyAnswer() {
        return surveyAnswer;
    }

    public void setSurveyAnswer(String surveyAnswer) {
        this.surveyAnswer = surveyAnswer;
    }

    public List<String> getOrderedDealNos() {
        return orderedDealNos;
    }

    public void setOrderedDealNos(List<String> orderedDealNos) {
        this.orderedDealNos = orderedDealNos;
    }

    public String getMatchedTransfer() {
        return matchedTransfer;
    }

    public void setMatchedTransfer(String matchedTransfer) {
        this.matchedTransfer = matchedTransfer;
    }

    public BigDecimal getTransferPrice() {
        return transferPrice;
    }

    public void setTransferPrice(BigDecimal transferPrice) {
        this.transferPrice = transferPrice;
    }

    /**
     * 修改前赎回方向
     */
    private String beforeModifyDirection;

    public String getBeforeModifyDirection() {
        return beforeModifyDirection;
    }

    public void setBeforeModifyDirection(String beforeModifyDirection) {
        this.beforeModifyDirection = beforeModifyDirection;
    }

    public String getOrderFormMemo() {
        return orderFormMemo;
    }

    public void setOrderFormMemo(String orderFormMemo) {
        this.orderFormMemo = orderFormMemo;
    }

    public String getWithdrawDirection() {
        return withdrawDirection;
    }

    public void setWithdrawDirection(String withdrawDirection) {
        this.withdrawDirection = withdrawDirection;
    }

    public String getCancelCard() {
        return cancelCard;
    }

    public void setCancelCard(String cancelCard) {
        this.cancelCard = cancelCard;
    }

    public String getAllowDt() {
        return allowDt;
    }

    public void setAllowDt(String allowDt) {
        this.allowDt = allowDt;
    }

    public String getAllRedeemFlag() {
        return allRedeemFlag;
    }

    public void setAllRedeemFlag(String allRedeemFlag) {
        this.allRedeemFlag = allRedeemFlag;
    }

    public BigDecimal getAppointmentAmt() {
        return appointmentAmt;
    }

    public void setAppointmentAmt(BigDecimal appointmentAmt) {
        this.appointmentAmt = appointmentAmt;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo == null ? null : dealAppNo.trim();
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo == null ? null : txAcctNo.trim();
    }

    public String getSubTxAcctNo() {
        return subTxAcctNo;
    }

    public void setSubTxAcctNo(String subTxAcctNo) {
        this.subTxAcctNo = subTxAcctNo == null ? null : subTxAcctNo.trim();
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo == null ? null : cpAcctNo.trim();
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode == null ? null : fundCode.trim();
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass == null ? null : fundShareClass.trim();
    }

    public String gettFundCode() {
        return tFundCode;
    }

    public void settFundCode(String tFundCode) {
        this.tFundCode = tFundCode == null ? null : tFundCode.trim();
    }

    public String gettFundShareClass() {
        return tFundShareClass;
    }

    public void settFundShareClass(String tFundShareClass) {
        this.tFundShareClass = tFundShareClass == null ? null : tFundShareClass.trim();
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt == null ? null : appDt.trim();
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm == null ? null : appTm.trim();
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode == null ? null : txCode.trim();
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType == null ? null : paymentType.trim();
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType == null ? null : protocolType.trim();
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo == null ? null : protocolNo.trim();
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag == null ? null : checkFlag.trim();
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode == null ? null : fundDivMode.trim();
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo == null ? null : dealNo.trim();
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo == null ? null : transactorIdNo.trim();
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType == null ? null : transactorIdType.trim();
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName == null ? null : transactorName.trim();
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo == null ? null : operatorNo.trim();
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode == null ? null : consCode.trim();
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode == null ? null : returnCode.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker == null ? null : checker.trim();
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag == null ? null : appFlag.trim();
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName == null ? null : fundName.trim();
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag == null ? null : largeRedmFlag.trim();
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag == null ? null : agentFlag.trim();
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo == null ? null : idNo.trim();
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getCheckDtm() {
        return checkDtm;
    }

    public void setCheckDtm(Date checkDtm) {
        this.checkDtm = checkDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct == null ? null : bankAcct.trim();
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode == null ? null : bankCode.trim();
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt == null ? null : taTradeDt.trim();
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getUnusualTransType() {
        return unusualTransType;
    }

    public void setUnusualTransType(String unusualTransType) {
        this.unusualTransType = unusualTransType == null ? null : unusualTransType.trim();
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag == null ? null : riskFlag.trim();
    }

    public BigDecimal getAppointmentDiscount() {
        return appointmentDiscount;
    }

    public void setAppointmentDiscount(BigDecimal appointmentDiscount) {
        this.appointmentDiscount = appointmentDiscount;
    }

    public BigDecimal getEsitmateFee() {
        return esitmateFee;
    }

    public void setEsitmateFee(BigDecimal esitmateFee) {
        this.esitmateFee = esitmateFee;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo == null ? null : appointmentDealNo.trim();
    }

    public BigDecimal getAdvanceAmt() {
        return advanceAmt;
    }

    public void setAdvanceAmt(BigDecimal advanceAmt) {
        this.advanceAmt = advanceAmt;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String gettFundName() {
        return tFundName;
    }

    public void settFundName(String tFundName) {
        this.tFundName = tFundName == null ? null : tFundName.trim();
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType == null ? null : idType.trim();
    }

    public String getRedeemCapitalFlag() {
        return redeemCapitalFlag;
    }

    public void setRedeemCapitalFlag(String redeemCapitalFlag) {
        this.redeemCapitalFlag = redeemCapitalFlag;
    }

    public String getForceCancelFlag() {
        return forceCancelFlag;
    }

    public void setForceCancelFlag(String forceCancelFlag) {
        this.forceCancelFlag = forceCancelFlag;
    }

    public String getAppointmentDealNoType() {
        return appointmentDealNoType;
    }

    public void setAppointmentDealNoType(String appointmentDealNoType) {
        this.appointmentDealNoType = appointmentDealNoType;
    }

    public String getDiscountModel() {
        return discountModel;
    }

    public void setDiscountModel(String discountModel) {
        this.discountModel = discountModel;
    }

    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }

    public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public String getIsRedeemExpire() {
        return isRedeemExpire;
    }

    public void setIsRedeemExpire(String isRedeemExpire) {
        this.isRedeemExpire = isRedeemExpire;
    }

    public String getPreExpireDate() {
        return preExpireDate;
    }

    public void setPreExpireDate(String preExpireDate) {
        this.preExpireDate = preExpireDate;
    }

    public String gettOutletCode() {
        return tOutletCode;
    }

    public void settOutletCode(String tOutletCode) {
        this.tOutletCode = tOutletCode;
    }

    public String getTransferTubeBusiType() {
        return transferTubeBusiType;
    }

    public void setTransferTubeBusiType(String transferTubeBusiType) {
        this.transferTubeBusiType = transferTubeBusiType;
    }

    public String gettSellerTxAcctNo() {
        return tSellerTxAcctNo;
    }

    public void settSellerTxAcctNo(String tSellerTxAcctNo) {
        this.tSellerTxAcctNo = tSellerTxAcctNo;
    }

    public String gettSellerCode() {
        return tSellerCode;
    }

    public void settSellerCode(String tSellerCode) {
        this.tSellerCode = tSellerCode;
    }

    public String getOriginalAppDealNo() {
        return originalAppDealNo;
    }

    public void setOriginalAppDealNo(String originalAppDealNo) {
        this.originalAppDealNo = originalAppDealNo;
    }

    public Map<String, String> getFundCodeAndDtlNoMap() {
        return fundCodeAndDtlNoMap;
    }

    public void setFundCodeAndDtlNoMap(Map<String, String> fundCodeAndDtlNoMap) {
        this.fundCodeAndDtlNoMap = fundCodeAndDtlNoMap;
    }

    public String getRepurchaseProtocolNo() {
        return repurchaseProtocolNo;
    }

    public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
        this.repurchaseProtocolNo = repurchaseProtocolNo;
    }

    public String getRepurchaseType() {
        return repurchaseType;
    }

    public void setRepurchaseType(String repurchaseType) {
        this.repurchaseType = repurchaseType;
    }

    public String getIntransitAssetMemo() {
        return intransitAssetMemo;
    }

    public void setIntransitAssetMemo(String intransitAssetMemo) {
        this.intransitAssetMemo = intransitAssetMemo;
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }

    public String getInTxAcctNo() {
        return inTxAcctNo;
    }

    public void setInTxAcctNo(String inTxAcctNo) {
        this.inTxAcctNo = inTxAcctNo;
    }

    public String getInCpAcctNo() {
        return inCpAcctNo;
    }

    public void setInCpAcctNo(String inCpAcctNo) {
        this.inCpAcctNo = inCpAcctNo;
    }

    public String getInDisCode() {
        return inDisCode;
    }

    public void setInDisCode(String inDisCode) {
        this.inDisCode = inDisCode;
    }

    public String getAdvanceStatus() {
        return advanceStatus;
    }

    public void setAdvanceStatus(String advanceStatus) {
        this.advanceStatus = advanceStatus;
    }
}
