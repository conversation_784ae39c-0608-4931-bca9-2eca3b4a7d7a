/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;


import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.QueryCounterTradeReqDto;
import com.howbuy.tms.counter.dto.QueryCounterTradeRespDto;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(查询柜台交易控制器) 
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class QueryCounterReportController {
   
    @Autowired
    private TmsCounterService  tmsCounterService;
    /**
     * 
     * queryCounterReport:(查询)
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception 
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/querycounterreport.htm")
    public ModelAndView queryCounterReport(HttpServletRequest request,HttpServletResponse response) throws Exception{
        
        QueryCounterTradeReqDto reqDto  = new QueryCounterTradeReqDto();
        String currDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        reqDto.setTradeDt(currDt);
        QueryCounterTradeRespDto responseDto = tmsCounterService.queryCounterTrade(reqDto,null);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }
   
}

