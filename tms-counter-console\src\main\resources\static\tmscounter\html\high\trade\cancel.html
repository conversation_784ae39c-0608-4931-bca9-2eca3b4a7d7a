<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit|ie-comp|ie-stand">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta http-equiv="Cache-Control" content="no-siteapp" />
	<!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
	<link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
	<link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
	<link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
	<link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
	<link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
	<link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
	<link rel="stylesheet" type="text/css" href="../../../lib/jquery/jquery-ui-1.9.2.custom.css" />
	<link rel="stylesheet" type="text/css" href="../../../lib/bootstrap/css/bootstrap.css" />
	<link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.css" />
	<link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.filter.css" />
	<title>撤单</title>
</head>

<body>
	<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易申请 <span class="c-gray en">&gt;</span> 撤单 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
	<div class="page-container">
		<div class="containner_all">
			<div class="container_box">
				<p class="mainTitle mt10">撤单</p>
				<div class="cp_top mt30">
					<span class="normal_span">客户号：</span>
					<input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
					<span class="normal_span ml30">证件号：</span>
					<input type="text"  placeholder="请输入" id="idNo">
					<span class="normal_span ml30">分销机构：</span>
					 <span class="select-box inline">
					   <select name="disCode" class="select" id="selectDisCode">
					   </select>
					</span>
					<a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryCustInfoBtn">查询</a>
				</div>
			</div>
		</div>
	</div>
	<div class="page-container">
		<p class="main_title">客户基本信息</p>
		<div class="result2_tab">
			<table class="table table-border table-bordered table-hover table-bg table-sort">
			   <thead>
				   <tr class="text-c">
						<th>选择</th>
						<th>客户号</th>
						<th>客户名称</th>
						<th>客户类型</th>
						<th>证件类型</th>
						<th>证件号</th>
						<th>风险测评结果</th>
						<th>开户分销机构</th>
					    <th>私募合格投资者认证</th>
					    <th>资管合格投资者认证</th>
						<th>客户状态</th>
						<th>投资者类型</th>
						<th>协议回款方式</th>
					</tr>
			   </thead>
				<tbody id="custInfoId">
				</tbody>
			</table>
		</div>

		<p class="main_title mt30" id="showMaterial">柜台材料信息</p>
		<div class="result2_tab" id="onLineMaterial">
		</div>

		<p class="main_title mt30">可撤销的交易申请</p>
		<div class="result2_tab">
			<table class="table table-border table-bordered table-hover table-bg table-sort">
			   <thead>
				   <tr class="text-c">
						<th>选择</th>
						<th>是否客户发起撤单</th>
						<th>基金代码</th>
						<th>基金简称</th>
					    <th>支付方式</th>
						<th>业务类型</th>
						<th>申请金额</th>
						<th>申请份额</th>
						<th>交易状态</th>
						<th>中台订单号</th>
						<th>支付状态</th>
						<th>订单申请时间</th>
						<th>上报TA日期</th>
					</tr>
			   </thead>
				<tbody id="rsList">
				</tbody>
			</table>
		</div>

		<form id = "refundForm">
			<div class="result2_tab">
				<table id="refundDirection" class="table table-border table-bordered table-hover table-bg table-sort">

				</table>

			</div>
		</form>

		<p class="main_title mt30">其他信息</p>
		<form id="othetInfoForm">
		<div class="result2_tab">
			<table class="table table-border table-bordered table-hover table-bg table-sort">
				<tbody>
				
					<tr class="text-c">
						<td>网点</td>
						<td>中台柜台</td>
						<td>投资顾问代码</td>
						<td>
						  <span class="select-box inline">
							<select name="consCode" class="select selectconsCode">
							</select>
						  </span>
						</td>
					</tr>
					
					<tr class="text-c">
						<td>是否代理</td>
						<td> 
							<span class="select-box inline">
				   			  <select name="agentFlag" class="select selectAgened">
								  <option value="0">否</option>
								  <option value="1">是</option>
							  </select>
							</span>
						</td>
						<td></td>
						<td></td>
					</tr>
					<tr class="text-l">
					 <td>撤单原因</td>
						<td colspan="3">
						  <textarea id="cancelMemo" maxlength="50" placeholder="最大50个汉字" name="cancelMemo" rows="4" cols="50"></textarea>
						</td>
					</tr>
				</tbody>
		   </table>
	   </div>
		</form>
		
		 <form id="transactorInfoForm" style="display:none">
		 <p class="main_title mt30">经办人信息</p>
		 <div class="result2_tab">
		 <table class="table table-border table-bordered table-hover table-bg table-sort">
				<tbody>
				 <tr class="text-c">
						<td>经办人姓名：</td>
						<td>
					   <input type="text" placeholder="请输入" id="transactorName"  name="transactorName" isnull="false" datatype="s" errormsg="经办人姓名">
					   </td>
						<td>经办人证件类型：</td>
			  <td>
				  <span class="select-box inline">
					<select id="transactorIdType" name="transactorIdType" class="select selectTransactorIdType"  isnull="false" datatype="s" errormsg="经办人证件类型" >
					</select>
				</span>
				</td>
			 <tr class="text-c">
			  <td>经办人证件号：</td>
			   <td> <input id = "transactorIdNo" type="text" placeholder="请输入"  name="transactorIdNo" isnull="false" datatype="s" errormsg="经办人证件号" ></td>
				<td></td>
				 <td></td>
			</tbody>
		  </table>
		</div>
		 </form>
		 
		<p class="mt30">
			<a href="javascript:void(0)" class="btn radius btn-secondary" id="confimCancelBtn">确认提交</a>
		</p>
	</div>

	<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
	<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
	<script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
	<script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
	<script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
	<script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
	<script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
	<script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
	<script type="text/javascript" src="../../../js/baseconfig.js"></script>
	<script type="text/javascript" src="../../../js/common.js"></script>
	<script type="text/javascript" src="../../../js/config.js"></script>
	<script type="text/javascript" src="../../../js/commonutil.js"></script>
	<script type="text/javascript" src="../../../js/valid.js"></script>
	<script type="text/javascript" src="../../../js/high/conscode.js"></script>
	<script type="text/javascript" src="../../../js/common.js"></script>
	<script type="text/javascript" src="../../../js/config.js"></script>
	<script type="text/javascript" src="../../../js/commonutil.js"></script>
	<script type="text/javascript" src="../../../js/valid.js"></script>
	<script type="text/javascript" src="../../../js/high/common/init.js"></script>
	<script type="text/javascript" src="../../../js/high/conscode.js"></script>
	<script type="text/javascript" src="../../../js/high/common/custinfo.js"></script>
	<script type="text/javascript" src="../../../js/high/common/onlineorderfile.js"></script>
	<script type="text/javascript" src="../../../js/high/trade/cancel.js?v=4.1.12"></script>
	<script type="text/javascript" src="../../../js/high/query/querycustinfosubpage.js"></script>
	<script type="text/javascript" src="../../../lib/jquery/1.9.1/ui-1.2.1/jquery-ui.min.js"></script>
	<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.js"></script>
	<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.filter.js"></script>
	
</body>

</html>