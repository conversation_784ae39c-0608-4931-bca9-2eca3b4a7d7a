<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/jquery-ui-1.9.2.custom.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.filter.css" />
    <title>柜台交易查询</title>
</head>

<body>
    <div class="page-container">
        <p class="main_title">查询结果</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>交易日</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>基金代码</th>
                        <th>基金简称</th>
                        <th>业务类型</th>
                        <th>中台订单号</th>
                        <th>申请金额</th>
                        <th>申请份额</th>
                        <th>确认金额</th>
                        <th>确认份额</th>
                        <th>申请日期</th>
                        <th>申请时间</th>
                        <th>订单状态</th>
                    </tr>
               </thead>
                <tbody id="rsList">
                </tbody>
            </table>
        </div>
        <div class="clear page_all">
            <div class="fy_part fr mt20" id="pageView"></div>
        </div>
    </div>
    <form id="refundDirectionInfo">
        <div class="result2_tab">
<!--            <table id="refundTableId" class="table table-border table-bordered table-hover table-bg table-sort">-->
<!--                <tr class="text-c">-->
<!--                    <td>修改前</td>-->
<!--                    <td></td>-->
<!--                    <td>修改后</td>-->
<!--                    <td></td>-->
<!--                </tr>-->
<!--                <tr class="text-c">-->
<!--                    <td>当前订单状态</td>-->
<!--                    <td id="orderStatusId"></td>-->
<!--                    <td></td>-->
<!--                    <td></td>-->
<!--                </tr>-->
<!--                <tr class="text-c">-->
<!--                    <td>当前回款方向</td>-->
<!--                    <td id="refundDirectionId"></td>-->
<!--                    <td>修改后的回款方向</td>-->
<!--                    <td id="newRefundDirectionId"></td>-->
<!--                </tr>-->
<!--                <tr class="text-c">-->
<!--                    <td>回可用余额的金额</td>-->
<!--                    <td id="refundAmtId"></td>-->
<!--                    <td>回可用余额的金额</td>-->
<!--                    <td id="newRefundAmtId"></td>-->
<!--                </tr>-->
<!--                <tr class="text-c">-->
<!--                    <td>回可用余额的备注</td>-->
<!--                    <td id="refundMemoId"></td>-->
<!--                    <td>回可用余额的备注</td>-->
<!--                    <td id="newRefundMemoId"></td>-->
<!--                </tr>-->
<!--            </table>-->
            <table id="refundTableId" class="table table-border table-bordered table-hover table-bg table-sort">

            </table>
        </div>
    </form>
    <form id="checkResult">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tr class="text-c">
                    <td colspan="4">
                        <span>&nbsp;驳回原因：<input type="" placeholder='请输入驳回原因，可选' name="checkFaildDesc">（仅审核驳回使用）</span>
                    </td>
                </tr>
            </table>
        </div>
    </form>
    <p class="text-c mt30" id="submitDiv">
        <a href="javascript:void(0)" id ="checkRejectBtn" class="btn radius  btn-danger ml30">审核驳回</a>
        <a href="javascript:void(0)" id ="checkModifyBtn" class="btn radius btn-warning ml30" >提交</a>
        <a href="javascript:void(0)" id ="checkCacelBtn" class="btn radius btn-secondary ml30">作废</a>
        <a href="javascript:void(0)" id ="checkConfirmBtn" class="btn radius btn-success ml30">审核通过</a>
        <a href="javascript:void(0)" id ="checkBackBtn" class="btn radius btn-success ml30">返回</a>
    </p>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
     <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/high/common/onlineorderfile.js"></script>
    <script type="text/javascript" src="../../../js/high/check/viewcounterdeal.js"></script>
    <script type="text/javascript" src="../../../js/high/check/modifyrefunddircheck.js"></script>
    <script type="text/javascript" src="../../../js/high/check/countercheck.js"></script>
    <script type="text/javascript" src="../../../js/high/common/init.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/ui-1.2.1/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.filter.js"></script>
</body>

</html>