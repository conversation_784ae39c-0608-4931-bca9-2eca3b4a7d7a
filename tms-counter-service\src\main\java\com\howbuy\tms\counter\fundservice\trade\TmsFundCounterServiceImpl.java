/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.fundservice.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.common.date.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.finonline.facade.query.cashintransit.QueryCashInTransitDetailFacade;
import com.howbuy.finonline.facade.query.cashintransit.QueryCashInTransitDetailRequest;
import com.howbuy.finonline.facade.query.cashintransit.QueryCashInTransitDetailResponse;
import com.howbuy.ftxonline.facade.piggy.trade.ftxvolmigratevalidate.FtxVolMigrateValidateRequest;
import com.howbuy.ftxonline.facade.piggy.trade.ftxvolmigratevalidate.FtxVolMigrateValidateResponse;
import com.howbuy.interlayer.common.enums.AdviserSplitFlagEnum;
import com.howbuy.interlayer.product.enums.FundSubTypeEnum;
import com.howbuy.interlayer.product.enums.FundTypeEnum;
import com.howbuy.interlayer.product.model.PortfolioPartnerInfoModel;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.PortfolioPartnerInfoService;
import com.howbuy.tms.batch.facade.query.confirmprocess.bean.ConfirmProcess;
import com.howbuy.tms.batch.facade.query.querycounterendtacount.QueryCounterEndTaCountFacade;
import com.howbuy.tms.batch.facade.query.querycounterendtacount.QueryCounterEndTaCountRequest;
import com.howbuy.tms.batch.facade.query.querycounterendtacount.QueryCounterEndTaCountResponse;
import com.howbuy.tms.batch.facade.query.queryfundcheckorder.QueryFundCheckOrderFacade;
import com.howbuy.tms.batch.facade.query.queryfundcheckorder.QueryFundCheckOrderRequest;
import com.howbuy.tms.batch.facade.query.queryfundcheckorder.QueryFundCheckOrderResponse;
import com.howbuy.tms.batch.facade.query.queryfundcheckorder.bean.FundCheckOrderBean;
import com.howbuy.tms.batch.facade.query.queryfundcheckorder.bean.QueryFundCheckOrderCondition;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlFacade;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlRequest;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.QueryFundDealOrderDtlResponse;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.bean.FundDealOrderDtlBean;
import com.howbuy.tms.batch.facade.query.queryfunddealorderdtl.bean.QueryFundDealOrderDtlCondition;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderFacade;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderRequest;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderResponse;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorder.bean.QuerySubmitCheckOrderCondition;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorder.bean.SubmitCheckOrderRespBean;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlFacade;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlRequest;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.QuerySubmitCheckOrderDtlResponse;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.bean.MaterialDtlRespBean;
import com.howbuy.tms.batch.facade.query.querysubmitcheckorderdtl.bean.SubmitCheckOrderDtlRespBean;
import com.howbuy.tms.batch.facade.trade.counterend.CounterEndFacade;
import com.howbuy.tms.batch.facade.trade.counterend.CounterEndRequest;
import com.howbuy.tms.batch.facade.trade.counterfundtransfer.CounterFundTransferFacade;
import com.howbuy.tms.batch.facade.trade.counterfundtransfer.CounterFundTransferRequest;
import com.howbuy.tms.batch.facade.trade.counterfundtransfer.CounterFundTransferResponse;
import com.howbuy.tms.batch.facade.trade.counterfundtransfer.bean.CounterFundTransferBean;
import com.howbuy.tms.batch.facade.trade.countermodifydicount.CounterModifyDicountFacade;
import com.howbuy.tms.batch.facade.trade.countermodifydicount.CounterModifyDicountRequest;
import com.howbuy.tms.batch.facade.trade.countermodifydiv.CounterModifyDivFacade;
import com.howbuy.tms.batch.facade.trade.countermodifydiv.CounterModifyDivRequest;
import com.howbuy.tms.batch.facade.trade.countermodifydiv.CounterModifyDivResponse;
import com.howbuy.tms.batch.facade.trade.countermodifydiv.bean.CounterModifyDivOrderBean;
import com.howbuy.tms.batch.facade.trade.counternotendta.CounterSaveOrDelNotEndTaFacade;
import com.howbuy.tms.batch.facade.trade.counternotendta.CounterSaveOrDelNotEndTaRequest;
import com.howbuy.tms.batch.facade.trade.counternotendta.CounterSaveOrDelNotEndTaResponse;
import com.howbuy.tms.batch.facade.trade.counternotendta.bean.CounterNotEndTaBean;
import com.howbuy.tms.batch.facade.trade.counterportfolioredeem.CounterPortfolioRedeemFacade;
import com.howbuy.tms.batch.facade.trade.counterportfolioredeem.CounterPortfolioRedeemRequest;
import com.howbuy.tms.batch.facade.trade.counterportfolioredeem.CounterPortfolioRedeemResponse;
import com.howbuy.tms.batch.facade.trade.counterpurchase.CounterAdviserPurchaseFacade;
import com.howbuy.tms.batch.facade.trade.counterpurchase.CounterPurchaseFacade;
import com.howbuy.tms.batch.facade.trade.counterpurchase.CounterPurchaseRequest;
import com.howbuy.tms.batch.facade.trade.counterpurchase.CounterPurchaseResponse;
import com.howbuy.tms.batch.facade.trade.counterpurchase.bean.CounterPurchaseOrderBean;
import com.howbuy.tms.batch.facade.trade.counterredeem.CounterRedeemFacade;
import com.howbuy.tms.batch.facade.trade.counterredeem.CounterRedeemRequest;
import com.howbuy.tms.batch.facade.trade.counterredeem.CounterRedeemResponse;
import com.howbuy.tms.batch.facade.trade.counterredeem.bean.CounterRedeemOrderBean;
import com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeFacade;
import com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeRequest;
import com.howbuy.tms.batch.facade.trade.countersharemerge.CounterShareMergeResponse;
import com.howbuy.tms.batch.facade.trade.countersharemerge.bean.CounterShareMergeOrderBean;
import com.howbuy.tms.batch.facade.trade.countersharemerge.bean.CounterShareMergeOrderBean.ShareMergeOrderDetail;
import com.howbuy.tms.batch.facade.trade.countertradecancel.CounterTradeCancelFacade;
import com.howbuy.tms.batch.facade.trade.countertradecancel.CounterTradeCancelRequest;
import com.howbuy.tms.batch.facade.trade.countertradecancel.CounterTradeCancelResponse;
import com.howbuy.tms.batch.facade.trade.countertradecancel.bean.CounterTradeCancelOrderBean;
import com.howbuy.tms.batch.facade.trade.countertransfertube.CounterTransferTubeFacade;
import com.howbuy.tms.batch.facade.trade.countertransfertube.CounterTransferTubeRequest;
import com.howbuy.tms.batch.facade.trade.countertransfertube.CounterTransferTubeResponse;
import com.howbuy.tms.batch.facade.trade.countertransfertube.bean.CounterTransferTubeBean;
import com.howbuy.tms.batch.facade.trade.countertransfertube.bean.CounterTransferTubeBean.TransferTubeDetail;
import com.howbuy.tms.batch.facade.trade.lctcountertransfertube.LctCounterTransferTubeFacade;
import com.howbuy.tms.batch.facade.trade.lctcountertransfertube.LctCounterTransferTubeRequest;
import com.howbuy.tms.batch.facade.trade.lctcountertransfertube.LctCounterTransferTubeResponse;
import com.howbuy.tms.common.ccms.CCMSRegister;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.bean.CustBankModel;
import com.howbuy.tms.common.outerservice.acccenter.querybankacctsensitiveinfo.QueryBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querybankacctsensitiveinfo.QueryBankCardSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.querybankacctsensitiveinfo.QueryBankCardSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.finonline.querylatestpaycust.QueryLatestPayCustContext;
import com.howbuy.tms.common.outerservice.finonline.querylatestpaycust.QueryLatestPayCustOutService;
import com.howbuy.tms.common.outerservice.finonline.querylatestpaycust.QueryLatestPayCustResult;
import com.howbuy.tms.common.outerservice.ftxonline.volmigrate.VolMigrateOuterService;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.querysavingboxvoldetail.QuerySavingBoxVolDetailOuterService;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.querysavingboxvoldetail.QuerySavingBoxVolDetailResult;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.querysavingboxvoldetail.QuerySavingBoxVolDetailResult.VolDetailRst;
import com.howbuy.tms.common.outerservice.interlayer.adviser.bean.PortfolioPartnerInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.QueryFundInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.QueryProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ActiDiscountRatioBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.context.QueryActiRateDiscountContext;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.validator.fundinfo.FundInfoValidator;
import com.howbuy.tms.counter.enums.VolShareTypeEnum;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.remote.TradeInvoker;
import com.howbuy.tms.counter.common.util.FileUtils;
import com.howbuy.tms.counter.common.util.RequestUtil;
import com.howbuy.tms.counter.config.TmsCounterNacosConfig;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto.DtlBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.utils.VolShareTypeUtils;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlFacade;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlRequest;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlResponse;
import com.howbuy.tms.orders.search.facade.query.fund.queryacctbalancedtl.QueryAcctBalanceDtlResponse.BalanceDtlBean;
import com.howbuy.tms.orders.search.facade.query.fund.queryisornotallredeem.QueryIsOrNotAllRedeemFacade;
import com.howbuy.tms.orders.search.facade.query.fund.queryisornotallredeem.QueryIsOrNotAllRedeemRequest;
import com.howbuy.tms.orders.search.facade.query.fund.queryisornotallredeem.QueryIsOrNotAllRedeemResponse;
import com.howbuy.tms.orders.search.facade.query.fund.queryprotocollist.QueryProtocolListFacade;
import com.howbuy.tms.orders.search.facade.query.fund.queryprotocollist.QueryProtocolListRequest;
import com.howbuy.tms.orders.search.facade.query.fund.queryprotocollist.QueryProtocolListResponse;
import com.howbuy.tms.orders.search.facade.query.querycustbalancedetail.QueryCustBalanceDetailFacade;
import com.howbuy.tms.orders.search.facade.query.querycustbalancedetail.QueryCustBalanceDetailRequest;
import com.howbuy.tms.orders.search.facade.query.querycustbalancedetail.QueryCustBalanceDetailResponse;
import com.howbuy.tms.orders.search.facade.query.querycustbalancedetail.QueryCustBalanceDetailResponse.BankCardInfo;
import com.howbuy.tms.orders.search.facade.query.querycustbalancedetail.QueryCustBalanceDetailResponse.CustBalanceDetail;
import com.howbuy.tms.orders.search.facade.query.querycustcanceldeal.CancelDealBean;
import com.howbuy.tms.orders.search.facade.query.querycustcanceldeal.QueryCustCancelDealFacade;
import com.howbuy.tms.orders.search.facade.query.querycustcanceldeal.QueryCustCancelDealRequest;
import com.howbuy.tms.orders.search.facade.query.querycustcanceldeal.QueryCustCancelDealResponse;
import com.howbuy.tms.orders.search.facade.query.queryfinancialbalancedtl.QueryFinancialBalanceDtlFacade;
import com.howbuy.tms.orders.search.facade.query.queryfinancialbalancedtl.QueryFinancialBalanceDtlRequest;
import com.howbuy.tms.orders.search.facade.query.queryfinancialbalancedtl.QueryFinancialBalanceDtlResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.howbuy.tms.common.validator.fundinfo.FundInfoValidator.isFinancial;

/**
 * @description:(零售柜台控制台服务)
 * <AUTHOR>
 * @date 2017年9月15日 下午4:47:34
 * @since JDK 1.6
 */
@Service("tmsFundCounterService")
public class TmsFundCounterServiceImpl implements TmsFundCounterService, CCMSRegister {

    private static final Logger logger = LoggerFactory.getLogger(TmsFundCounterServiceImpl.class);

    @Autowired
    private CounterPurchaseFacade counterPurchaseFacade;

    @Autowired
    private CounterAdviserPurchaseFacade counterAdviserPurchaseFacade;

    @Autowired
    private CounterRedeemFacade counterRedeemFacade;

    @Autowired
    private CounterPortfolioRedeemFacade counterPortfolioRedeemFacade;

    @Autowired
    private CounterTradeCancelFacade counterCancelFacade;

    @Autowired
    private QueryCashInTransitDetailFacade queryCashInTransitDetailFacade;

    @Autowired
    private TmsFundCounterValidService tmsFundCounterValidService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private QueryAllBankCardInfoOuterService queryAllBankCardInfoOuterService;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private QueryAcctBalanceDtlFacade queryAcctBalanceDtlFacade;

    @Autowired
    private CounterModifyDivFacade counterModifyDivFacade;

    @Autowired
    private QueryCustCancelDealFacade queryCustCancelDealFacade;

    @Autowired
    private QuerySubmitCheckOrderFacade querySubmitCheckOrderFacade;

    @Autowired
    private CounterFundTransferFacade counterFundTransferFacade;

    @Autowired
    private QueryFundCheckOrderFacade queryFundCheckOrderFacade;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CounterEndFacade counterEndFacade;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    private QueryFundInfoOuterService queryFundInfoOuterService;

    @Autowired
    private QueryProductOuterService queryProductOuterService;

    @Autowired
    private QueryIsOrNotAllRedeemFacade queryIsOrNotAllRedeemFacade;

    @Autowired
    private QueryCustBalanceDetailFacade queryCustBalanceDetailFacade;

    @Autowired
    private CounterShareMergeFacade counterShareMergeFacade;

    @Autowired
    private QuerySubmitCheckOrderDtlFacade querySubmitCheckOrderDtlFacade;

    @Autowired
    private QueryFundDealOrderDtlFacade queryFundDealOrderDtlFacade;

    @Autowired
    private CounterModifyDicountFacade counterModifyDicountFacade;

    @Autowired
    private CounterTransferTubeFacade counterTransferTubeFacade;

    @Autowired
    private QueryCounterEndTaCountFacade queryCounterEndTaCountFacade;

    @Autowired
    private CounterSaveOrDelNotEndTaFacade counterSaveOrDelNotEndTaFacade;

    @Autowired
    private QueryFinancialBalanceDtlFacade queryFinancialBalanceDtlFacade;

    @Autowired
    private QueryProtocolListFacade queryProtocolListFacade;

    @Autowired
    private QuerySavingBoxVolDetailOuterService querySavingBoxVolDetailOuterService;

    @Autowired
    private VolMigrateOuterService volMigrateOuterService;

    @Autowired
    private LctCounterTransferTubeFacade lctCounterTransferTubeFacade;

    @Autowired
    private QueryLatestPayCustOutService queryLatestPayCustOutService;

    @Autowired
    private QueryBankAcctSensitiveInfoOuterService queryBankAcctSensitiveInfoOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    
    @Autowired
    private TmsCounterNacosConfig tmsCounterNacosConfig;



    @Override
    public CounterPurchaseRespDto counterAdviserPurchase(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception {
        validateCounterEnd(counterPurchaseReqDto, disInfoDto);

        boolean validFlag = tmsFundCounterValidService.adviserPurValidate(counterPurchaseReqDto, disInfoDto);

        CounterPurchaseRespDto resp = null;
        CounterPurchaseRequest request = new CounterPurchaseRequest();
        if (validFlag) {
            if (counterPurchaseReqDto != null) {
                CounterPurchaseOrderBean counterPurchaseOrderBean = new CounterPurchaseOrderBean();

                BeanUtils.copyProperties(counterPurchaseReqDto, counterPurchaseOrderBean);

                if (StringUtils.isNotBlank(counterPurchaseReqDto.getQuestionAnswer())) {
                    String answers = String.join(",", counterPurchaseReqDto.getQuestionAnswer().toUpperCase().split(""));
                    counterPurchaseOrderBean.setSurveyAnswer(answers);
                }
                request.setCounterPurchaseOrderBean(counterPurchaseOrderBean);
            }

            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterAdviserPurchaseFacade, request, disInfoDto);
            if (baseResp != null) {
                CounterPurchaseResponse counterPurchaseResponse = (CounterPurchaseResponse) baseResp;
                resp = new CounterPurchaseRespDto();
                BeanUtils.copyProperties(counterPurchaseResponse, resp);
            }
        }
        return resp;
    }



    @Override
    public CounterPurchaseRespDto counterPurchase(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception {
        validateCounterEnd(counterPurchaseReqDto, disInfoDto);

        boolean validFlag = tmsFundCounterValidService.subsOrPurValidate(counterPurchaseReqDto, disInfoDto);
        CounterPurchaseRespDto resp = null;
        CounterPurchaseRequest request = new CounterPurchaseRequest();
        if (validFlag) {
            if (counterPurchaseReqDto != null) {
                CounterPurchaseOrderBean counterPurchaseOrderBean = new CounterPurchaseOrderBean();

                BeanUtils.copyProperties(counterPurchaseReqDto, counterPurchaseOrderBean);
                request.setCounterPurchaseOrderBean(counterPurchaseOrderBean);
            }

            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterPurchaseFacade, request, disInfoDto);
            if (baseResp != null) {
                CounterPurchaseResponse counterPurchaseResponse = (CounterPurchaseResponse) baseResp;
                resp = new CounterPurchaseRespDto();
                BeanUtils.copyProperties(counterPurchaseResponse, resp);
            }
        }
        return resp;
    }

    private void validateCounterEnd(CounterPurchaseReqDto counterPurchaseReqDto, DisInfoDto disInfoDto) throws Exception {
        String taCode = Optional.ofNullable(counterPurchaseReqDto).map(x -> x.getTaCode()).orElse("");

        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), taCode, disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterPurchaseReqDto.getAppDt(), counterPurchaseReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }
    }

    @Override
    public QueryAcctBalanceDtlRespDto queryAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        List<String> protocolTypeList = new ArrayList<String>();
        protocolTypeList.add(ProtocolTypeEnum.DEFAULT_FUND.getCode());
        return getAcctBalanceDtl(reqDto, disInfoDto, protocolTypeList);
    }

    @Override
    public QueryAcctBalanceDtlRespDto queryAcctBalDtlByMultiProtocol(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception {
        // 获取基金信息和基金净值信息
        FundInfoAndNavBean fundAndNav = queryFundInfoOuterService.getFundInfoAndNav(reqDto.getFundCode(), null);

        if (isFinancial(fundAndNav.getFundType(), fundAndNav.getFundSubType(), fundAndNav.getFundOpenMode()) ||
                FundInfoValidator.notRegularOpenFund(fundAndNav.getFundType(), fundAndNav.getFundSubType(), fundAndNav.getHasLockPeriod()) ||
                FundInfoValidator.isHasLockPeriod(fundAndNav.getHasLockPeriod(), fundAndNav.getIsCyclicLock())) {

            logger.info(">>>>>>>>>>>>>>>>>>>理财型基金");
            // 理财型基金
            return getFinancialAcctBalanceDtl(reqDto, disInfoDto, protocolTypeList, fundAndNav);
        }
        // 非理财型
        return getAcctBalanceDtl(reqDto, disInfoDto, protocolTypeList);
    }

    @Override
    public QueryAcctBalanceDtlRespDto queryAcctBalDtlByMultiProtocolType(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception {
        // 非理财型
        return getAcctBalanceDtl(reqDto, disInfoDto, protocolTypeList);
    }

    /**
     * 判断基金是否是有锁定期的养老FOF基金
     *
     * @param fundType
     * @param fundSubType
     * @return
     */
    public boolean isHasLockYLFof(String fundType, String fundSubType, String hasLockPeriod) {
        if (FundTypeEnum.FOF.getCode().equals(fundType)
                && (FundSubTypeEnum.PENSION_TARGET_RISK.getCode().equals(fundSubType)
                || FundSubTypeEnum.PENSION_TARGET_DATE.getCode().equals(fundSubType))
                && HasLockPeriodEnum.YES.getCode().equals(hasLockPeriod)) {
            // 基金类型为FOF，基金子类型为养老目标日期型或养老目标风险型,是否有锁定期为“是”
            return true;
        }
        return false;
    }

    private QueryAcctBalanceDtlRespDto getAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList) throws Exception {
        QueryAcctBalanceDtlRespDto queryFundBalDtlRespDto = null;
        QueryAcctBalanceDtlRequest request = new QueryAcctBalanceDtlRequest();
        request.setTxAcctNo(reqDto.getCustNo());
        request.setProductCode(reqDto.getFundCode());

        if (CollectionUtils.isNotEmpty(protocolTypeList)) {
            request.setProtocolType(null);
            request.setProtocolTypeList(protocolTypeList);
        }

        request.setDisCode(disInfoDto.getDisCode());
        request.setAppDt(reqDto.getAppDt());
        request.setAppTm(reqDto.getAppTm());
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryAcctBalanceDtlFacade, request, disInfoDto);
        if (baseResp != null) {
            queryFundBalDtlRespDto = new QueryAcctBalanceDtlRespDto();
            QueryAcctBalanceDtlResponse queryAcctBalanceDtlResponse = (QueryAcctBalanceDtlResponse) baseResp;
            BeanUtils.copyProperties(queryAcctBalanceDtlResponse, queryFundBalDtlRespDto);
            List<BalanceDtlBean> balanceDtlList = queryAcctBalanceDtlResponse.getBalanceDtlList();
            List<DtlBean> dtlList = new ArrayList<DtlBean>();
            if (!CollectionUtils.isEmpty(balanceDtlList)) {
                DtlBean dtlBean = null;
                for (BalanceDtlBean balanceDtlBean : balanceDtlList) {
                    dtlBean = new DtlBean();
                    BeanUtils.copyProperties(balanceDtlBean, dtlBean);
                    dtlBean.setBankAcctNo(balanceDtlBean.getBankAcct());
                    dtlBean.setTxAcctNo(queryAcctBalanceDtlResponse.getTxAcctNo());
                    dtlBean.setDisCode(queryAcctBalanceDtlResponse.getDisCode());
                    dtlBean.setProductCode(balanceDtlBean.getFundCode());
                    dtlBean.setFundShareClass(balanceDtlBean.getFundShareClass());
                    dtlBean.setProductName(balanceDtlBean.getFundName());
                    dtlBean.setProductType(queryAcctBalanceDtlResponse.getProductType());
                    dtlBean.setNav(queryAcctBalanceDtlResponse.getNav());
                    dtlBean.setProductStatus(queryAcctBalanceDtlResponse.getProductStatus());
                    dtlBean.setNavDt(queryAcctBalanceDtlResponse.getNavDt());
                    dtlBean.setBuyStatus(queryAcctBalanceDtlResponse.getBuyStatus());
                    dtlBean.setRedeemStatus(queryAcctBalanceDtlResponse.getRedeemStatus());
                    dtlBean.setFundType(balanceDtlBean.getFundType());
                    dtlBean.setFundSubType(balanceDtlBean.getFundSubType());
                    dtlBean.setChgTrusteeMode(balanceDtlBean.getChgTrusteeMode());
                    dtlBean.setTaxDelayFlag(balanceDtlBean.getTaxDelayFlag());
                    dtlBean.setTaCode(balanceDtlBean.getTaCode());
                    dtlBean.setProductClass(balanceDtlBean.getProductClass());

                    dtlList.add(dtlBean);
                }
            }
            queryFundBalDtlRespDto.setBalanceDtlList(dtlList);
        }
        return queryFundBalDtlRespDto;
    }

    /**
     * 查询理财型基金持仓明细
     *
     * @param reqDto
     * @param disInfoDto
     * @param protocolTypeList
     * @return
     * @throws Exception
     */
    private QueryAcctBalanceDtlRespDto getFinancialAcctBalanceDtl(QueryAcctBalanceDtlReqDto reqDto, DisInfoDto disInfoDto, List<String> protocolTypeList, FundInfoAndNavBean fundAndNav) throws Exception {
        QueryAcctBalanceDtlRespDto result = new QueryAcctBalanceDtlRespDto();
        List<DtlBean> balanceDtlList = new ArrayList<>();
        result.setBalanceDtlList(balanceDtlList);
        // 先查询协议号
        String protocolNo = null;
        String protocolType = null;
        QueryProtocolListRequest queryProtocolListRequest = new QueryProtocolListRequest();
        queryProtocolListRequest.setProtocolTypeList(protocolTypeList);
        queryProtocolListRequest.setTxAcctNo(reqDto.getCustNo());
        BaseResponse res = TmsFacadeUtil.executeThrowException(queryProtocolListFacade, queryProtocolListRequest, disInfoDto);
        if (null != res) {
            QueryProtocolListResponse queryProtocolListResponse = (QueryProtocolListResponse) res;
            List<QueryProtocolListResponse.CustProtocolInfo> custProtocolList = queryProtocolListResponse.getCustProtocolList();
            if (CollectionUtils.isNotEmpty(custProtocolList)) {

                for (QueryProtocolListResponse.CustProtocolInfo protocolInfo : custProtocolList) {
                    // 柜台工作日,修复柜台3点后的问题
                    String currDate = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), new DisInfoDto());
                    protocolNo = protocolInfo.getProtocolNo();
                    protocolType = protocolInfo.getProtocolType();

                    List<DtlBean> dtls = getBalanceDtl(disInfoDto, currDate, reqDto, protocolNo, protocolType, fundAndNav);
                    balanceDtlList.addAll(dtls);
                }
            }
        }
        return result;
    }

    private List<DtlBean> getBalanceDtl(DisInfoDto disInfoDto, String currDate, QueryAcctBalanceDtlReqDto reqDto, String protocolNo, String protocolType, FundInfoAndNavBean fundAndNav) throws Exception {
        QueryFinancialBalanceDtlRequest request = new QueryFinancialBalanceDtlRequest();
        request.setDisCode(disInfoDto.getDisCode());
        request.setAppDt(currDate);
        request.setAppTm("095000");
        request.setFundCode(reqDto.getFundCode());
        request.setTxAcctNo(reqDto.getCustNo());
        request.setCpAcctNo(reqDto.getCpAcctNo());
        request.setProtocolNo(protocolNo);

        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryFinancialBalanceDtlFacade, request, disInfoDto);
        List<DtlBean> dtlList = new ArrayList<DtlBean>();
        if (baseResp != null) {
            QueryFinancialBalanceDtlResponse response = (QueryFinancialBalanceDtlResponse) baseResp;

            List<QueryFinancialBalanceDtlResponse.FundBalanceDtlBean> list = response.getBalanceDtlList();

            if (!CollectionUtils.isEmpty(list)) {
                DtlBean dtlBean = null;
                for (QueryFinancialBalanceDtlResponse.FundBalanceDtlBean bean : list) {
                    dtlBean = new DtlBean();
                    dtlBean.setTxAcctNo(reqDto.getCustNo());
                    dtlBean.setDisCode(disInfoDto.getDisCode());
                    dtlBean.setProductCode(response.getFundCode());
                    dtlBean.setFundShareClass(fundAndNav.getFundShareClass());
                    dtlBean.setProductName(response.getFundAttr());
                    dtlBean.setProductType(response.getFundType());
                    dtlBean.setNav(response.getNav());
                    dtlBean.setProductStatus(fundAndNav.getFundStat());
                    dtlBean.setNavDt(response.getNavDt());
                    dtlBean.setBuyStatus(fundAndNav.getBuyStatus());
                    dtlBean.setRedeemStatus(fundAndNav.getRedeemStatus());
                    dtlBean.setCpAcctNo(bean.getCustBankId());
                    dtlBean.setBankCode(bean.getBankCode());
                    dtlBean.setBankName(bean.getBankRegionName());
                    dtlBean.setBankAcctNo(bean.getBankAcct());
                    dtlBean.setBalanceVol(bean.getBalanceVol());
                    dtlBean.setAvailVol(bean.getAvailVol());
                    dtlBean.setUnconfirmedVol(bean.getFrznVol());
                    dtlBean.setMarketValue(bean.getTotalAmt());

                    dtlBean.setOpenRedeDt(bean.getAllowRedeemDt());
                    dtlBean.setProtocolNo(protocolNo);
                    dtlBean.setProtocolType(protocolType);

                    dtlList.add(dtlBean);
                }
            }
        }
        return dtlList;
    }

    @Override
    public CounterRedeemRespDto counterRedeem(CounterRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterRedeemReqDto == null) {
            logger.info("counterRedeemReqDto is null");
            throw new TmsCounterException(TmsCounterResultEnum.PARAM_IS_ERROR);
        }
        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), counterRedeemReqDto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterRedeemReqDto.getAppDt(), counterRedeemReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                logger.info("tradeDt:{},workDay:{}", tradeDt, workDay);
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }

        CounterRedeemRespDto resp = null;
        CounterRedeemRequest request = new CounterRedeemRequest();
        boolean validFlag = tmsFundCounterValidService.redeemValidate(counterRedeemReqDto, disInfoDto);
        if (validFlag) {
            CounterRedeemOrderBean counterRedeemOrderBean = new CounterRedeemOrderBean();
            BeanUtils.copyProperties(counterRedeemReqDto, counterRedeemOrderBean);
            counterRedeemOrderBean.setAllowRedeemDt(counterRedeemReqDto.getOpenRedeDt());
            request.setCounterRedeemOrderBean(counterRedeemOrderBean);

            CounterRedeemResponse counterRedeemResponse = (CounterRedeemResponse) TmsFacadeUtil.executeThrowException(counterRedeemFacade, request, disInfoDto);
            resp = new CounterRedeemRespDto();
            BeanUtils.copyProperties(counterRedeemResponse, resp);
        }
        return resp;

    }

    @Override
    public CounterRedeemRespDto counterRedeemAdviser(CounterRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterRedeemReqDto == null) {
            logger.info("counterRedeemReqDto is null");
            throw new TmsCounterException(TmsCounterResultEnum.PARAM_IS_ERROR);
        }
        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), counterRedeemReqDto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterRedeemReqDto.getAppDt(), counterRedeemReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                logger.info("tradeDt:{},workDay:{}", tradeDt, workDay);
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }

        CounterRedeemRespDto resp = null;
        CounterRedeemRequest request = new CounterRedeemRequest();

        tmsFundCounterValidService.redeemAdviserValidate(counterRedeemReqDto, disInfoDto);

        CounterRedeemOrderBean counterRedeemOrderBean = new CounterRedeemOrderBean();
        BeanUtils.copyProperties(counterRedeemReqDto, counterRedeemOrderBean);
        counterRedeemOrderBean.setAllowRedeemDt(counterRedeemReqDto.getOpenRedeDt());
        request.setCounterRedeemOrderBean(counterRedeemOrderBean);
        request.setTxCode(TxCodes.ADVISER_REDEEM_COUNTER);
        CounterRedeemResponse counterRedeemResponse = (CounterRedeemResponse) TmsFacadeUtil.executeThrowException(counterRedeemFacade, request, disInfoDto);
        resp = new CounterRedeemRespDto();
        BeanUtils.copyProperties(counterRedeemResponse, resp);

        return resp;

    }

    @Override
    public CounterRedeemRespDto counterRedeempPortfolio(CounterPortfolioRedeemReqDto counterRedeemReqDto, DisInfoDto disInfoDto) throws Exception{
        if (counterRedeemReqDto == null) {
            logger.info("counterRedeemReqDto is null");
            throw new TmsCounterException(TmsCounterResultEnum.PARAM_IS_ERROR);
        }

        List<CounterPortfolioRedeemListDto> counterPortfolioRedeemListDtos = counterRedeemReqDto.getCounterPortfolioRedeemListDtos();
        if(!CollectionUtils.isEmpty(counterPortfolioRedeemListDtos)){
            for(CounterPortfolioRedeemListDto dto : counterPortfolioRedeemListDtos){
                if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), dto.getTaCode(), disInfoDto)) {
                    String tradeDt = queryTradeDayOuterService.getWorkDay(counterRedeemReqDto.getAppDt(), counterRedeemReqDto.getAppTm());
                    String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
                    if (tradeDt.equals(workDay)) {
                        logger.info("fundCode:{}, tradeDt:{}, workDay:{}", dto.getFundCode(), tradeDt, workDay);
                        throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
                    }
                }
            }
        }

        CounterRedeemRespDto resp = null;
        CounterPortfolioRedeemRequest request = buildCounterPortfolioRedeem(counterRedeemReqDto);
        request.setTxCode(TxCodes.REDEEM);
        CounterPortfolioRedeemResponse counterPortfolioRedeemResponse = (CounterPortfolioRedeemResponse) TmsFacadeUtil.executeThrowException(counterPortfolioRedeemFacade, request, disInfoDto);
        resp = new CounterRedeemRespDto();
        BeanUtils.copyProperties(counterPortfolioRedeemResponse, resp);
        return resp;
    }

    private CounterPortfolioRedeemRequest buildCounterPortfolioRedeem(CounterPortfolioRedeemReqDto counterRedeemReqDto) {
        CounterPortfolioRedeemRequest request = new CounterPortfolioRedeemRequest();
        BeanUtils.copyProperties(counterRedeemReqDto, request);

        List<CounterPortfolioRedeemRequest.CounterPortfolioRedeemBean> counterPortfolioRedeemBeans = new ArrayList<>();
        List<CounterPortfolioRedeemListDto> counterPortfolioRedeemListDtos = counterRedeemReqDto.getCounterPortfolioRedeemListDtos();
        if(!CollectionUtils.isEmpty(counterPortfolioRedeemListDtos)){
            CounterPortfolioRedeemRequest.CounterPortfolioRedeemBean bean = null;
            for(CounterPortfolioRedeemListDto dto : counterPortfolioRedeemListDtos){
                bean = new CounterPortfolioRedeemRequest.CounterPortfolioRedeemBean();
                BeanUtils.copyProperties(dto, bean);
                counterPortfolioRedeemBeans.add(bean);
            }
        }
        request.setCounterPortfolioRedeemBeans(counterPortfolioRedeemBeans);
        request.setMemo("柜台赎回");
        return request;
    }

    @Override
    public CounterExchangeRespDto counterExchange(CounterExchangeReqDto counterExchangeReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterExchangeReqDto == null || disInfoDto == null) {
            logger.info("counterExchangeReqDto:{},disInfoDto:{}", counterExchangeReqDto, disInfoDto);
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), counterExchangeReqDto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterExchangeReqDto.getAppDt(), counterExchangeReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }

        CounterExchangeRespDto resp = null;
        CounterFundTransferRequest request = new CounterFundTransferRequest();
        boolean validFlag = tmsFundCounterValidService.exchangeValidate(counterExchangeReqDto, disInfoDto);
        if (validFlag) {
            CounterFundTransferBean counterFundTransferBean = new CounterFundTransferBean();
            BeanUtils.copyProperties(counterExchangeReqDto, counterFundTransferBean);
            counterFundTransferBean.setAllowDt(counterExchangeReqDto.getOpenRedeDt());
            request.setCounterFundTransferBean(counterFundTransferBean);

            CounterFundTransferResponse counterFundTransferResponse = (CounterFundTransferResponse) TmsFacadeUtil.executeThrowException(counterFundTransferFacade, request, disInfoDto);
            resp = new CounterExchangeRespDto();
            BeanUtils.copyProperties(counterFundTransferResponse, resp);
        }
        return resp;
    }

    @Override
    public CounterCancelRespDto counterCancel(CounterCancelReqDto counterCancelReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterCancelReqDto == null || disInfoDto == null) {
            logger.info("counterCancelReqDto:{},disInfoDto:{}", counterCancelReqDto == null, disInfoDto == null);
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), counterCancelReqDto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterCancelReqDto.getAppDt(), counterCancelReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }

        // 投顾校验 ： 该投顾产品所属的投顾供应商为模式一（投顾拆单）且批处理‘投顾交易申请处理’已完成，则不允许撤单，提示：该笔订单已经上报给投顾方，不允许撤单。

        tmsCounterService.adviserCancelValid(disInfoDto, counterCancelReqDto.getPartnerCode(), counterCancelReqDto.getProtocolType());


        CounterCancelRespDto resp = null;
        CounterTradeCancelRequest request = new CounterTradeCancelRequest();
        boolean validFlag = tmsFundCounterValidService.cancelOrderValidate(counterCancelReqDto, disInfoDto);
        if (validFlag) {
            CounterTradeCancelOrderBean counterCancelOrderBean = new CounterTradeCancelOrderBean();
            BeanUtils.copyProperties(counterCancelReqDto, counterCancelOrderBean);
            request.setCounterTradeCancelOrderBean(counterCancelOrderBean);

            CounterTradeCancelResponse counterCancelResponse = (CounterTradeCancelResponse) TmsFacadeUtil.executeThrowException(counterCancelFacade, request, disInfoDto);
            resp = new CounterCancelRespDto();
            BeanUtils.copyProperties(counterCancelResponse, resp);
        }
        return resp;
    }

    @Override
    public CounterModifyDivRespDto counterModifyDiv(CounterModifyDivReqDto counterModifyDivReqDto, DisInfoDto disInfoDto) throws Exception {
        if (counterModifyDivReqDto == null) {
            logger.info("counterModifyDivReqDto is null");
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), counterModifyDivReqDto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(counterModifyDivReqDto.getAppDt(), counterModifyDivReqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                logger.info("tradeDt:{},workDay:{}", tradeDt, workDay);
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }
        CounterModifyDivRespDto resp = null;
        CounterModifyDivRequest request = new CounterModifyDivRequest();
        boolean validFlag = tmsFundCounterValidService.modifyDivValidate(counterModifyDivReqDto, disInfoDto);
        if (validFlag) {
            CounterModifyDivOrderBean counterModifyDivOrderBean = new CounterModifyDivOrderBean();
            BeanUtils.copyProperties(counterModifyDivReqDto, counterModifyDivOrderBean);
            request.setCounterModifyDivOrderBean(counterModifyDivOrderBean);

            CounterModifyDivResponse counterModifyDivResponse = (CounterModifyDivResponse) TmsFacadeUtil.executeThrowException(counterModifyDivFacade, request, disInfoDto);
            resp = new CounterModifyDivRespDto();
            BeanUtils.copyProperties(counterModifyDivResponse, resp);
        }
        return resp;

    }

    @Override
    public List<CancelDealDto> queryCanCancelOrder(String dealNo, String custNo, DisInfoDto disInfoDto) throws Exception {
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        List<CancelDealDto> orderList = null;
        QueryCustCancelDealRequest request = new QueryCustCancelDealRequest();
        request.setPageSize(0);
        request.setTxAcctNo(custNo);
        // request.setDealNo(dealNo);
        request.setTaTradeDt(workDay);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCustCancelDealFacade, request, disInfoDto);
        if (baseResp != null) {
            QueryCustCancelDealResponse queryHighCanCancelDealOrdersResponse = (QueryCustCancelDealResponse) baseResp;
            List<CancelDealBean> cancelOrderList = queryHighCanCancelDealOrdersResponse.getCancelDealList();
            if (!CollectionUtils.isEmpty(cancelOrderList)) {
                orderList = new ArrayList<CancelDealDto>();
                CancelDealDto orderDto = null;
                if (!StringUtils.isEmpty(dealNo)) {
                    for (CancelDealBean cancelBean : cancelOrderList) {
                        if (dealNo.equals(cancelBean.getDealNo())) {
                            orderDto = new CancelDealDto();
                            BeanUtils.copyProperties(cancelBean, orderDto);
                            orderList.add(orderDto);
                        }
                    }
                } else {
                    for (CancelDealBean cancelBean : cancelOrderList) {
                        orderDto = new CancelDealDto();
                        BeanUtils.copyProperties(cancelBean, orderDto);
                        orderList.add(orderDto);
                    }
                }
            }
        }
        return orderList;
    }

    @Override
    public CounterOrderDto counterQueryOrderById(CounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception {
        QuerySubmitCheckOrderRequest request = new QuerySubmitCheckOrderRequest();
        if (counterQueryOrderReqDto != null) {
            BeanUtils.copyProperties(counterQueryOrderReqDto, request);
            QuerySubmitCheckOrderCondition querySubmitCheckOrderCondition = new QuerySubmitCheckOrderCondition();
            BeanUtils.copyProperties(counterQueryOrderReqDto, querySubmitCheckOrderCondition);
            request.setQueryCondition(querySubmitCheckOrderCondition);
        }
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderFacade, request, disInfoDto);
        CounterOrderDto counterOrderDto = null;
        if (baseResp != null) {
            QuerySubmitCheckOrderResponse querySubmitCheckOrderResponse = (QuerySubmitCheckOrderResponse) baseResp;
            List<SubmitCheckOrderRespBean> pageList = querySubmitCheckOrderResponse.getQuerySubmitCheckOrderRespBeanList();
            if (!CollectionUtils.isEmpty(pageList)) {
                for (SubmitCheckOrderRespBean orderBean : pageList) {
                    if (Objects.equal(orderBean.getDealAppNo(), counterQueryOrderReqDto.getDealAppNo())) {
                        counterOrderDto = new CounterOrderDto();
                        BeanUtils.copyProperties(orderBean, counterOrderDto);
                        counterOrderDto.setAllRedeemFlag(orderBean.getIsAllRedeem());
                    }
                }
            }
        }
        return counterOrderDto;
    }

    @Override
    public CounterQueryOrderRespDto counterQueryOrder(CounterQueryOrderReqDto counterQueryOrderReqDto, DisInfoDto disInfoDto) throws Exception {
        CounterQueryOrderRespDto resp = null;
        QuerySubmitCheckOrderRequest request = new QuerySubmitCheckOrderRequest();
        if (counterQueryOrderReqDto != null) {
            BeanUtils.copyProperties(counterQueryOrderReqDto, request);
            QuerySubmitCheckOrderCondition querySubmitCheckOrderCondition = new QuerySubmitCheckOrderCondition();
            BeanUtils.copyProperties(counterQueryOrderReqDto, querySubmitCheckOrderCondition);

            List<String> checkFlagList = new ArrayList<String>();
            if (!CollectionUtils.isEmpty(counterQueryOrderReqDto.getCheckFlagLsit())) {
                checkFlagList.addAll(counterQueryOrderReqDto.getCheckFlagLsit());
            }
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getCheckFlag())) {
                checkFlagList.add(counterQueryOrderReqDto.getCheckFlag());
            }
            querySubmitCheckOrderCondition.setCheckFlagList(checkFlagList);

            // 业务类型码查询设置
            List<String> txCodes = new ArrayList<String>();
            if (!CollectionUtils.isEmpty(counterQueryOrderReqDto.getTxCodeList())) {
                txCodes.addAll(counterQueryOrderReqDto.getTxCodeList());
            }
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getTxCode())) {
                txCodes.add(counterQueryOrderReqDto.getTxCode());
            }
            querySubmitCheckOrderCondition.setSearchTxCode(txCodes);
            List<String> hbjgFundCodeList = queryHGJBFundCodeList();
            if (CollectionUtils.isNotEmpty(hbjgFundCodeList)) {
                querySubmitCheckOrderCondition.setHbjgFundList(hbjgFundCodeList);
            }

            request.setQueryCondition(querySubmitCheckOrderCondition);
        }
        // 接口调用码设置
        request.setTxCode(TxCodes.QUERY_COUNTER_ORDER);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderFacade, request, disInfoDto);

        List<CounterOrderDto> counterOrderList = null;
        if (baseResp != null) {
            QuerySubmitCheckOrderResponse querySubmitCheckOrderResponse = (QuerySubmitCheckOrderResponse) baseResp;
            resp = new CounterQueryOrderRespDto();
            List<SubmitCheckOrderRespBean> pageList = querySubmitCheckOrderResponse.getQuerySubmitCheckOrderRespBeanList();
            if (!CollectionUtils.isEmpty(pageList)) {
                CounterOrderDto counterOrderDto = null;
                counterOrderList = new ArrayList<CounterOrderDto>(16);
                for (SubmitCheckOrderRespBean orderBean : pageList) {
                    counterOrderDto = new CounterOrderDto();
                    BeanUtils.copyProperties(orderBean, counterOrderDto);
                    counterOrderDto.setAllRedeemFlag(orderBean.getIsAllRedeem());
                    // 姓名脱敏处理
                    counterOrderDto.setCustNameEncrypt(counterOrderDto.getCustName());
                    counterOrderList.add(counterOrderDto);
                }
            }
            resp.setCounterOrderList(counterOrderList);
            resp.setPageAppAmt(querySubmitCheckOrderResponse.getPageAppAmt());
            resp.setPageAppVol(querySubmitCheckOrderResponse.getPageAppVol());
            resp.setTotalAppAmt(querySubmitCheckOrderResponse.getTotalAppAmt());
            resp.setTotalAppVol(querySubmitCheckOrderResponse.getTotalAppVol());
            resp.setTotalCount(querySubmitCheckOrderResponse.getTotalCount());
            resp.setPageNo(querySubmitCheckOrderResponse.getPageNo());
            resp.setTotalPage(querySubmitCheckOrderResponse.getTotalPage());
        }
        return resp;
    }

    private List<String> queryHGJBFundCodeList() {
        try {
            HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
            UserAccountModel user = (UserAccountModel) httpRequest.getSession().getAttribute(Constants.SESSION_USER);
            boolean inHbgb = Boolean.TRUE.equals(Optional.ofNullable(user).map(UserAccountModel::getInHBJG).orElse(Boolean.TRUE));
            if (inHbgb) {
                List<String> fundCodeList = queryHighProductOuterService.queryNotHBJGFundListService();
                return fundCodeList;
            }
        }catch (Exception e){
            logger.error("queryHGJBFundCodeList error", e);
        }

        return Lists.newArrayList();
    }

    @Override
    public boolean checkOrder(SubmitUncheckOrderDto submitUncheckOrderDto, CounterPortfolioProductDto productDto, List<SubmitUncheckOrderDtlDto> dtlOrderDto, DisInfoDto disInfoDto) throws Exception {
        String txCode = submitUncheckOrderDto.getTxCode();
        String checkFlag = submitUncheckOrderDto.getCheckFlag();
        boolean flag = false;
        if (CounterCheckFlagEnum.CHECKED_SUCC.getKey().equals(checkFlag)) {
            // 份额合并、迁移
            if (TxCodes.COUNTER_MERGE_VOL.equals(txCode)) {
                flag = orderService.checkCounterMergeVolOrder(submitUncheckOrderDto,  dtlOrderDto, disInfoDto);

            } else if (TxCodes.COUNTER_TRANS_VOL.equals(txCode)) {
                flag = orderService.checkCounterTransVolOrder(submitUncheckOrderDto, dtlOrderDto, disInfoDto);
            }

            // 审核成功
            switch (txCode) {
                case TxCodes.ADVISER_PURCHASE_COUNTER:
                    flag = orderService.adviserPurCounter(submitUncheckOrderDto, productDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_PURCHASE:
                    flag = orderService.subsOrPurCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_SURCHASE:
                    flag = orderService.subsOrPurCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_REDEEM:
                    flag = orderService.redeemCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.ADVISER_REDEEM_COUNTER:
                    flag = orderService.redeemCounterAdviser(submitUncheckOrderDto, productDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_CANCEL:
                    flag = orderService.cancelOrder(submitUncheckOrderDto, disInfoDto, CancelFlagEnum.SELF.getCode());
                    break;
                case TxCodes.COUNTER_FORCE_CANCEL:
                    flag = orderService.cancelOrder(submitUncheckOrderDto, disInfoDto, CancelFlagEnum.FORCE.getCode());
                    break;
                case TxCodes.COUNTER_MODIFYDIV:
                    flag = orderService.modifyDivCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_FUND_TRANSFER:
                    flag = orderService.exchangeCounter(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_TRANSFER_TUBE_IN:
                    flag = orderService.checkCounterTransferTubeInOrder(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_TRANSFER_TUBE_OUT:
                    flag = orderService.checkCounterTransferTubeOutOrder(submitUncheckOrderDto, dtlOrderDto, disInfoDto);
                    break;
                case TxCodes.COUNTER_LCT_TRANSFER_TUBE_OUT:
                    flag = orderService.lctCheckCounterTransferTubeOutOrder(submitUncheckOrderDto, dtlOrderDto, disInfoDto);
                    break;
                case TxCodes.MODIFY_REDEEM_DIRECTION:
                    flag = orderService.checkModifyRedeemDirection(submitUncheckOrderDto, disInfoDto);
                    break;
                case TxCodes.REDEEM:
                    flag = orderService.redeemPortfolioCounter(submitUncheckOrderDto, dtlOrderDto, disInfoDto);
                    break;
                default:
                    break;
            }
        } else if (CounterCheckFlagEnum.CHECKED_REJECT.getKey().equals(checkFlag)) {
            // 审核退回
            flag = orderService.checkCounterOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        } else if (CounterCheckFlagEnum.CANCEL.getKey().equals(checkFlag)) {
            // 审核废单
            flag = orderService.checkCounterOrder(submitUncheckOrderDto, disInfoDto, checkFlag);
        }
        return flag;
    }

    @Override
    public boolean counterEnd(DisInfoDto disInfoDto) throws Exception {
        String tradeDt = tmsCounterService.getSystemWorkDay(disInfoDto);
        CounterEndRequest request = new CounterEndRequest();
        request.setTradeDt(tradeDt);
        TmsFacadeUtil.executeThrowException(counterEndFacade, request, disInfoDto);
        return true;
    }

    @Override
    public FeeDto calDiscountRate(CustInfoDto custInfoDto, String fundCode, String bankCode) throws Exception {
        // 查询工作日
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getRegDisCode());
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);

        // 查询产品信息
        FundInfoAndNavBean fundInfoAndNavBean = queryFundInfoOuterService.getFundInfoAndNav(fundCode, workDay);

        // 转换业务码
        String mBusiCode = tmsCounterOutService.getMBusiCode(custInfoDto, fundInfoAndNavBean, workDay);
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(mBusiCode);
        String businessCode = null;
        if (null != businessCodeEnum) {
            businessCode = businessCodeEnum.getCode();
        }

        // 查询折扣
        QueryActiRateDiscountContext ctx = new QueryActiRateDiscountContext();
        ctx.setBusiCode(businessCode);
        ctx.setBankCode(bankCode);
        ctx.setInvstType(custInfoDto.getInvstType());
        ctx.setPaymentType(PaySourceEnum.SELF_DRAWING.getCode());
        ctx.setProductId(fundCode);
        ctx.setShareClass(fundInfoAndNavBean.getFundShareClass());
        ctx.setDisCode(custInfoDto.getDisCode());
        ctx.setInvstType(custInfoDto.getInvstType());
        ActiDiscountRatioBean actiDiscountRatioBean = queryProductOuterService.queryActiRateDiscount(ctx);
        if (actiDiscountRatioBean == null) {
            return null;
        }

        // 计算最小折扣
        BigDecimal discountRate = getDiscount(actiDiscountRatioBean);
        FeeDto feeDto = new FeeDto();
        feeDto.setDiscountRate(discountRate);
        return feeDto;
    }

    public BigDecimal getDiscount(ActiDiscountRatioBean actiDiscountRatioBean) {
        logger.info("getDiscount|actiDiscountRatioBean:{}", JSON.toJSONString(actiDiscountRatioBean));
        // 代销折扣
        BigDecimal agentDisc = new BigDecimal(1);
        // 活动折扣
        BigDecimal actiDiscountRatio = new BigDecimal(1);

        // 折扣信息
        if (actiDiscountRatioBean != null) {
            logger.info("getDiscount|actiDiscountRatioBean:{}", JSON.toJSONString(actiDiscountRatioBean));
            if (actiDiscountRatioBean.getAgentDisc() != null) {
                agentDisc = actiDiscountRatioBean.getAgentDisc();
            }

            if (actiDiscountRatioBean.getActiDiscountRatio() != null && FundActiFlagEnum.JOIN.getCode().equals(actiDiscountRatioBean.getActiFlag())) {
                actiDiscountRatio = actiDiscountRatioBean.getActiDiscountRatio();
            }
        } else {
            logger.info("getDiscount|actiDiscountRatioBean is null");
        }
        logger.info("getDiscount|agentDisc:{}, actiDiscountRatio:{}", agentDisc, actiDiscountRatio);

        // 取 min（代销折扣率，活动折扣率）
        return getMinObject(agentDisc, actiDiscountRatio);

    }

    private BigDecimal getMinObject(BigDecimal oneObj, BigDecimal twoObj) {
        return oneObj.compareTo(twoObj) <= 0 ? oneObj : twoObj;
    }

    @Override
    public String queryIsOrNotAllRedeem(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception {
        QueryIsOrNotAllRedeemRequest request = new QueryIsOrNotAllRedeemRequest();
        if (dto != null) {
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setCpAcctNo(dto.getCpAcctNo());
            request.setFundCode(dto.getFundCode());
            request.setFundShareClass(dto.getFundShareClass());
            request.setProtocolType(dto.getProtocolType());
            request.setProtocolNo(dto.getProtocolNo());
        }
        QueryIsOrNotAllRedeemResponse response = (QueryIsOrNotAllRedeemResponse) TmsFacadeUtil.executeThrowException(queryIsOrNotAllRedeemFacade, request, disInfoDto);
        return response.getAllRedeemFlag();
    }

    @Override
    public QueryCustBankBalVolRespDto queryCustBankBalVol(QueryCustBankBalVolReqDto balVolDto, DisInfoDto disInfoDto) throws Exception {
        QueryCustBankBalVolRespDto respDto = null;

        HttpServletRequest httpRequest = RequestUtil.getHttpRequest();
        UserAccountModel user = (UserAccountModel) httpRequest.getSession().getAttribute(Constants.SESSION_USER);

        QueryCustBalanceDetailRequest request = new QueryCustBalanceDetailRequest();
        request.setTxAcctNo(balVolDto.getCustNo());
        request.setFundCode(balVolDto.getFundCode());
        request.setCpAcctNos(balVolDto.getCpAcctNos());
        request.setShareType(balVolDto.getShareType());
        //若无分销，则查询所有
        request.setDisCode(balVolDto.getDisCode());
        request.setSearchDisCode(TmsFacadeUtil.getAllDisCode(balVolDto.getDisCode()));
        request.setFilterHBJG(Boolean.TRUE.equals(Optional.ofNullable(user).map(UserAccountModel::getInHBJG).orElse(Boolean.TRUE)) ? "1" : "0");
        respDto = getQueryCustBankBalVolRespDto(disInfoDto, request);

        // 储蓄罐份额查询
        querySavingBoxVolDetailInfo(balVolDto, respDto);

        //如果是份额迁移业务，资金在途资产查询
        queryIntransitAsset(respDto, balVolDto);

        return respDto;
    }

    private void querySavingBoxVolDetailInfo(QueryCustBankBalVolReqDto balVolDto, QueryCustBankBalVolRespDto respDto) {
        QuerySavingBoxVolDetailResult piggyVols = querySavingBoxVolDetailOuterService.query(balVolDto.getCustNo(), TmsFacadeUtil.getAllDisCode(balVolDto.getDisCode()));
        if (piggyVols != null && !CollectionUtils.isEmpty(piggyVols.getDetails())) {
            List<CustBalDtlDto> custBalDtlList = respDto.getCustBalDtlList();
            if (custBalDtlList == null) {
                custBalDtlList = new ArrayList<CustBalDtlDto>(16);
                respDto.setCustBalDtlList(custBalDtlList);
            }
            List<String> cpAcctNos = balVolDto.getCpAcctNos();
            String bankAcct = null;
            for (VolDetailRst vdr : piggyVols.getDetails()) {
                if(VolShareTypeUtils.checkVolShareType(balVolDto.getShareType(), vdr.getDisCode())){
                    continue;
                }

                // 如果资金账号为空则查询银行卡号
                bankAcct = getBankAcctNo(balVolDto.getCustNo(), balVolDto.getDisCode(), vdr.getCustBankId());
                if (StringUtil.isEmpty(bankAcct) || cpAcctNos == null || cpAcctNos.size() <= 0) {
                    continue;
                }

                // 只要银行卡号或者资金账号有一个相等则继续下面逻辑
                if (!Objects.equal(bankAcct, balVolDto.getBankAcct()) && !cpAcctNos.contains(vdr.getCustBankId())) {
                    continue;
                }

                if (vdr.getTotalAmt() == null || vdr.getTotalAmt().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                CustBalDtlDto bal = new CustBalDtlDto();
                bal.setFundCode(vdr.getFundCode());
                bal.setFundAttr(vdr.getFundAttr());
                bal.setProductChannel(ProductChannelEnum.PIGGY.getCode());
                bal.setBankAcct(vdr.getBankAcct());
                bal.setBankCode(vdr.getBankCode());
                bal.setBalanceVol(vdr.getTotalAmt());
                bal.setAvailVol(vdr.getAvailRegAmt());
                bal.setUnconfirmedVol(MathUtils.add(vdr.getFrozenVol(), vdr.getBusiFrznVol()));
                // 无司法冻结
                bal.setJustFrznVol(null);
                // 暂定协议适配中台
                bal.setProtocolType("90");
                bal.setProtocolNo(null);
                bal.setCpAcctNo(vdr.getCustBankId());
                bal.setDisCode(vdr.getDisCode());
                custBalDtlList.add(bal);
            }
        }
    }

    /**
     * 查询用户银行信息
     */
    private QueryCustBankBalVolRespDto getQueryCustBankBalVolRespDto(DisInfoDto disInfoDto, QueryCustBalanceDetailRequest request) throws Exception {
        QueryCustBankBalVolRespDto respDto;
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryCustBalanceDetailFacade, request, disInfoDto);
        respDto = new QueryCustBankBalVolRespDto();
        if (baseResp != null) {
            QueryCustBalanceDetailResponse response = (QueryCustBalanceDetailResponse) baseResp;
            respDto.setReturnCode(response.getReturnCode());
            respDto.setDescription(response.getDescription());
            respDto.setTxAcctNo(response.getTxAcctNo());
            respDto.setDisCode(response.getDisCode());

            List<CustBalDtlDto> custBalDtlList = null;
            if (!CollectionUtils.isEmpty(response.getCustBalanceDetailList())) {
                custBalDtlList = new ArrayList<>(16);
                CustBalDtlDto custBalDtlDto = null;
                for (CustBalanceDetail balDtl : response.getCustBalanceDetailList()) {
                    if(VolShareTypeUtils.checkVolShareType(request.getShareType(), balDtl.getDisCode())){
                        continue;
                    }

                    custBalDtlDto = new CustBalDtlDto();
                    BeanUtils.copyProperties(balDtl, custBalDtlDto);
                    custBalDtlList.add(custBalDtlDto);
                }
            }
            respDto.setCustBalDtlList(custBalDtlList);

            List<CustBankCardDto> custBankCardList = null;
            if (!CollectionUtils.isEmpty(response.getBankCardInfoList())) {
                custBankCardList = new ArrayList<CustBankCardDto>(16);
                CustBankCardDto custBankCardDto = null;
                for (BankCardInfo bankCard : response.getBankCardInfoList()) {
                    custBankCardDto = new CustBankCardDto();
                    BeanUtils.copyProperties(bankCard, custBankCardDto);
                    custBankCardList.add(custBankCardDto);
                }
            }
            respDto.setCustBankCardList(custBankCardList);
        }
        return respDto;
    }

    private void queryIntransitAsset(QueryCustBankBalVolRespDto respDto, QueryCustBankBalVolReqDto balVolDto) {
        if (ShareTypeEnum.FUND_SHARE_TRANSFER.getCode().equals(balVolDto.getShareType())) {
            //取资金账号，如果custBalDtlList为空，则调用账户中心查询资金账号
            String cpAcctNo = "";

            //如果资金账号为空，通过银行卡号查询资金账号
            if (StringUtil.isEmpty(cpAcctNo)) {
                QueryBankCardInfoResponse queryRes = tmsCounterOutService.queryBankCardInfo(balVolDto.getCustNo(), balVolDto.getBankAcct());
                if (queryRes != null) {
                    cpAcctNo = queryRes.getCpAcctNo();
                }
            }

            QueryAllBankCardInfoContext ctx = new QueryAllBankCardInfoContext();
            ctx.setTxAcctNo(balVolDto.getCustNo());
            ctx.setDisCode(DefaultParamsConstant.All_DIS_CODE);
            Map<String, CustBankModel> custBankMap = queryAllBankCardInfoOuterService.queryCustBankMap(ctx);


            QueryCashInTransitDetailResponse queryRes = invokeCapitalIntransit(cpAcctNo, balVolDto.getCpAcctNos());
            if (queryRes != null && !CollectionUtils.isEmpty(queryRes.getDetailList())) {
                List<CustBalDtlDto> custBalDtlList = respDto.getCustBalDtlList();
                if (custBalDtlList == null) {
                    custBalDtlList = new ArrayList<CustBalDtlDto>(10);
                    respDto.setCustBalDtlList(custBalDtlList);
                }
                String disCode = null;
                for (QueryCashInTransitDetailResponse.TransitDetailPo po : queryRes.getDetailList()) {
                    CustBalDtlDto bal = new CustBalDtlDto();
                    bal.setFundCode(po.getProdCode());

                    CustBankModel custBankModel = custBankMap.get(po.getCpAcctNo());
                    if (custBankModel != null) {
                        bal.setBankAcct(custBankModel.getBankAcct());
                        bal.setBankCode(custBankModel.getBankCode());
                        disCode = custBankModel.getDisCode();
                    }
                    // fix:此处要设置disCode, com.howbuy.tms.counter.service.trade.TmsCounterServiceImpl#checkCounterTransVolOrder 会按分销进行分组，未空则报错
                    bal.setDisCode(StringUtils.defaultIfBlank(disCode, DisCodeEnum.HM.getCode()));
                    bal.setBalanceVol(po.getOccurBalance());
                    bal.setAvailVol(po.getOccurBalance());
                    bal.setUnconfirmedVol(new BigDecimal(0));
                    // 无司法冻结
                    bal.setJustFrznVol(null);
                    // 暂定协议适配中台
                    bal.setProtocolType("91");
                    bal.setProtocolNo(po.getBusiCode());
                    bal.setCpAcctNo(cpAcctNo);
                    //调用产品中心查询基金信息
                    FundInfoAndNavBean fundInfoAndNavBean = queryFundInfoOuterService.getFundInfoAndNav(po.getProdCode(), DateUtil.getAppDt());
                    if (null != fundInfoAndNavBean) {
                        bal.setFundAttr(fundInfoAndNavBean.getFundAttr());
                        bal.setProductChannel(fundInfoAndNavBean.getProductChannel());
                    }
                    custBalDtlList.add(bal);
                }
            }

        }
    }

    private QueryCashInTransitDetailResponse invokeCapitalIntransit(String cpAcctNo, List<String> cpAcctNos) {
        //调用资金
        QueryCashInTransitDetailRequest queryReq = new QueryCashInTransitDetailRequest();
        queryReq.setCpAcctNo(cpAcctNo);
        queryReq.setCpAcctNoList(cpAcctNos);
        QueryCashInTransitDetailResponse queryRes = queryCashInTransitDetailFacade.execute(queryReq);
        return queryRes;
    }

    /**
     * @param txAcctNo
     * @param disCode
     * @param cpAcctNo
     * @return java.lang.String
     * @description: 通过银行卡号查询客户资金账号
     * @author: hongdong.xie
     * @date: 2021/6/19 4:50
     * @since JDK 1.8
     */
    private String getBankAcctNo(String txAcctNo, String disCode, String cpAcctNo) {
        // 查询明文卡号
        QueryBankCardSensitiveInfoContext cardReq = new QueryBankCardSensitiveInfoContext();
        cardReq.setTxAcctNo(txAcctNo);
        cardReq.setDisCode(disCode);
        cardReq.setCpAcctNo(cpAcctNo);
        QueryBankCardSensitiveInfoResult result = queryBankAcctSensitiveInfoOuterService.queryBankAcctSensitiveInfo(cardReq);
        return result.getBankAcct();
    }

    public String getSysCode(String productChannel) {
        if (ProductChannelEnum.FUND.getCode().equals(productChannel)) {
            return SysCodeEnum.BATCH_GM.getCode();
        } else if (ProductChannelEnum.HIGH_FUND.getCode().equals(productChannel)
                || ProductChannelEnum.TP_SM.getCode().equals(productChannel)) {
            return SysCodeEnum.BATCH_HIGH.getCode();
        }

        return SysCodeEnum.BATCH_GM.getCode();
    }

    @Override
    public CounterShareMergeVolRespDto counterFundShareMergeVol(CounterShareMergeVolReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        if (reqDto == null || disInfoDto == null) {
            logger.info("reqDto:{},disInfoDto:{}", reqDto, disInfoDto);
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        String sysCode = getSysCode(reqDto.getProductChannel());
        boolean isCounterEndFlag = false;
        if (CollectionUtils.isNotEmpty(reqDto.getShareMergeVolOrderList())) {
            Set<String> taCodeSet = new HashSet<String>(16);
            for (ShareMergeVolOrderReqDto dto : reqDto.getShareMergeVolOrderList()) {
                taCodeSet.add(dto.getTaCode());
            }
            // 校验只要有一个基金TA收市，都不能份额迁移或合并
            if (taCodeSet.size() > 0) {
                for (String taCode : taCodeSet) {
                    isCounterEndFlag = tmsCounterService.isCounterEnd(sysCode, taCode, disInfoDto);
                    if (isCounterEndFlag) {
                        break;
                    }
                }
            }
        }

        if (isCounterEndFlag) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(reqDto.getAppDt(), reqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }

        CounterShareMergeVolRespDto resp = null;

        boolean validFlag = false;
        if (ShareTypeEnum.FUND_SHARE_MERGE.getCode().equals(reqDto.getShareType())) {
            validFlag = tmsFundCounterValidService.shareMergeVolValidate(reqDto, disInfoDto);
        }
        if (validFlag) {

            CounterShareMergeRequest request = new CounterShareMergeRequest();
            BeanUtils.copyProperties(reqDto, request);

            CounterShareMergeOrderBean orderBean = new CounterShareMergeOrderBean();
            BeanUtils.copyProperties(reqDto, orderBean);
            List<ShareMergeOrderDetail> outOrderList = null;
            if (CollectionUtils.isNotEmpty(reqDto.getShareMergeVolOrderList())) {
                outOrderList = new ArrayList<ShareMergeOrderDetail>(16);
                ShareMergeOrderDetail shareMergeOrder = null;
                for (ShareMergeVolOrderReqDto volOrder : reqDto.getShareMergeVolOrderList()) {
                    shareMergeOrder = new ShareMergeOrderDetail();
                    BeanUtils.copyProperties(volOrder, shareMergeOrder);
                    outOrderList.add(shareMergeOrder);
                }
            }
            orderBean.setOutShareMergeDetailList(outOrderList);
            request.setCounterShareMergeOrderBean(orderBean);

            CounterShareMergeResponse response = (CounterShareMergeResponse) TmsFacadeUtil.executeThrowException(counterShareMergeFacade, request, disInfoDto);
            resp = new CounterShareMergeVolRespDto();
            BeanUtils.copyProperties(response, resp);
        }

        return resp;
    }

    @Override
    public CounterShareMergeVolRespDto counterFundShareTransferVol(CounterShareTransferVolReqDto reqDto, String searchDisCode, DisInfoDto disInfoDto) throws Exception {
        // 日终校验
        validCounterEnd(reqDto.getShareMergeVolOrderList());

        boolean fundValidFlag = false;
        boolean highValidFlag = false;
        boolean piggyValidFlag = false;

        //零售
        if (reqDto.isHasFundVol()) {
            fundValidFlag = tmsFundCounterValidService.shareTransferVolValidate(reqDto, searchDisCode, disInfoDto);
        }

        //储蓄罐
        if (reqDto.isHasPiggyVol()) {
            piggyValidFlag = piggyValidate(reqDto);
        }

        //高端
        if (reqDto.isHasHighVol()) {
            highValidFlag = tmsFundCounterValidService.highShareTransferVolValidate(reqDto, searchDisCode, disInfoDto);
        }

        String productClass = getProductClass(fundValidFlag, highValidFlag, piggyValidFlag);

        CounterShareMergeVolRespDto resp = null;
        logger.info("counterFundShareTransferVol|validFlag:{},{},{}", fundValidFlag, highValidFlag, piggyValidFlag);
        if (fundValidFlag || highValidFlag || piggyValidFlag || CollectionUtils.isNotEmpty(reqDto.getShareMergeVolOrderList())) {
            CounterShareMergeRequest request = new CounterShareMergeRequest();
            BeanUtils.copyProperties(reqDto, request);

            CounterShareMergeOrderBean orderBean = new CounterShareMergeOrderBean();
            BeanUtils.copyProperties(reqDto, orderBean);
            List<ShareMergeOrderDetail> outOrderList = null;
            if (CollectionUtils.isNotEmpty(reqDto.getShareMergeVolOrderList())) {
                outOrderList = new ArrayList<ShareMergeOrderDetail>(16);
                ShareMergeOrderDetail shareMergeOrder = null;
                for (ShareMergeVolOrderReqDto volOrder : reqDto.getShareMergeVolOrderList()) {
                    shareMergeOrder = new ShareMergeOrderDetail();
                    BeanUtils.copyProperties(volOrder, shareMergeOrder);
                    outOrderList.add(shareMergeOrder);
                }
            }
            orderBean.setOutShareMergeDetailList(outOrderList);
            orderBean.setProductClass(productClass);
            request.setCounterShareMergeOrderBean(orderBean);

            BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterShareMergeFacade, request, disInfoDto);
            if (baseResp != null) {
                CounterShareMergeResponse response = (CounterShareMergeResponse) baseResp;
                resp = new CounterShareMergeVolRespDto();
                BeanUtils.copyProperties(response, resp);
            }
        }
        return resp;
    }

    private String getProductClass(boolean fundValidFlag, boolean highValidFlag, boolean piggyValidFlag) {
        if ((fundValidFlag || piggyValidFlag) && highValidFlag) {
            return ProductClassEnum.ALL.getCode();
        }

        if (fundValidFlag || piggyValidFlag) {
            return ProductClassEnum.RETAIL.getCode();
        }

        if (highValidFlag) {
            return ProductClassEnum.HIGH.getCode();
        }

        return "";

    }

    /**
     * 储蓄罐校验
     *
     * @return
     */
    private boolean piggyValidate(CounterShareTransferVolReqDto reqDto) {
        if (reqDto == null || CollectionUtils.isEmpty(reqDto.getShareMergeVolOrderList())) {
            logger.info("counterFundShareTransferVol|getShareMergeVolOrderList");
            return false;
        }
        List<String> disCodes = new ArrayList<String>();
        for (ShareMergeVolOrderReqDto dto : reqDto.getShareMergeVolOrderList()) {
            if (!disCodes.contains(dto.getDisCode()) && ProductChannelEnum.PIGGY.getCode().equals(dto.getProductChannel())) {
                disCodes.add(dto.getDisCode());
            }
        }

        if (CollectionUtils.isEmpty(disCodes)) {
            logger.info("counterFundShareTransferVol|disCodes:{}", disCodes);
            return false;
        }
        int count = 0;
        for (String disCode : disCodes) {
            FtxVolMigrateValidateRequest request = new FtxVolMigrateValidateRequest();
            request.setCustNo(reqDto.getTxAcctNo());
            request.setCustBankId(reqDto.getOutCpAcctNo());
            request.setTCustBankId(reqDto.getInCpAcctNo());
            request.setDisCode(disCode);
            FtxVolMigrateValidateResponse validate = volMigrateOuterService.validate(request);
            boolean flag = TradeInvoker.isSuccess(validate);
            if (flag) {
                count = count + 1;
            } else {
                if (validate != null) {
                    //换卡中存在投顾份额，只能选择注销原卡的换卡。请重新操作。
                    if (ExceptionCodes.FUND_SAHRE_TRANSFER_NOT_EXISTS.equals(validate.getReturnCode())) {
                        throw new TmsCounterException(TmsCounterResultEnum.CHANGE_BANK_ONLY.getCode(), TmsCounterResultEnum.CHANGE_BANK_ONLY.getDesc());
                    }
                    //此分销下银行卡不存在
                    if (ExceptionCodes.BANK_CARD_IS_NULL.equals(validate.getReturnCode())) {
                        throw new TmsCounterException(TmsCounterResultEnum.CHANGE_BANK_NULL.getCode(), TmsCounterResultEnum.CHANGE_BANK_NULL.getDesc());
                    }
                    throw new TmsCounterException(validate.getReturnCode(), "储蓄罐：" + validate.getDescription());
                }
            }
        }

        if (count == disCodes.size()) {
            return true;
        }
        return false;
    }

    /**
     * validCounterEnd:日终校验
     *
     * @param list
     * @throws Exception
     * <AUTHOR>
     * @date 2019年5月21日 下午7:21:04
     */
    private void validCounterEnd(List<ShareMergeVolOrderReqDto> list) throws Exception {
        Set<String> fundCodeList = new HashSet<>(16);
        for (ShareMergeVolOrderReqDto dto : list) {
            if (fundCodeList.contains(dto.getTaCode() + "" + dto.getProductChannel())) {
                continue;
            }
            fundCodeList.add(dto.getTaCode() + "" + dto.getProductChannel());
            String sysCode = getSysCode(dto.getProductChannel());
            boolean endFlag = tmsCounterService.isCounterEnd(sysCode, dto.getTaCode(), new DisInfoDto());
            if (endFlag) {
                logger.error("tacode:{},syscode:{} is counterEnd", dto.getTaCode(), sysCode);
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }
    }

    @Override
    public List<SubmitUncheckOrderDtlDto> querySubmitUnCheckDtlOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        QuerySubmitCheckOrderDtlRequest request = new QuerySubmitCheckOrderDtlRequest();
        if (reqDto != null) {
            request.setDealAppNo(reqDto.getDealAppNo());
        }
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderDtlFacade, request, disInfoDto);

        List<SubmitUncheckOrderDtlDto> dtlOrderList = new ArrayList<SubmitUncheckOrderDtlDto>();
        if (baseResp != null) {
            QuerySubmitCheckOrderDtlResponse dtlOrderResponse = (QuerySubmitCheckOrderDtlResponse) baseResp;
            List<SubmitCheckOrderDtlRespBean> pageList = dtlOrderResponse.getSubmitCheckOrderDtlRespBeanList();
            if (!CollectionUtils.isEmpty(pageList)) {
                SubmitUncheckOrderDtlDto dtlDto = null;
                for (SubmitCheckOrderDtlRespBean orderDtlBean : pageList) {
                    dtlDto = new SubmitUncheckOrderDtlDto();
                    BeanUtils.copyProperties(orderDtlBean, dtlDto);
                    dtlOrderList.add(dtlDto);
                }
            }
        }

        return dtlOrderList;
    }

    @Override
    public SubmitUncheckOrderDtlAllDto querySubmitUnCheckDtlOrderPage(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        SubmitUncheckOrderDtlAllDto dto = new SubmitUncheckOrderDtlAllDto();

        QuerySubmitCheckOrderDtlRequest request = new QuerySubmitCheckOrderDtlRequest();
        request.setIsPage(YesOrNoEnum.YES.getCode());
        if (reqDto != null) {
            request.setPageNo(reqDto.getPageNo());
            request.setPageSize(reqDto.getPageSize());
            request.setDealAppNo(reqDto.getDealAppNo());
        }
        QuerySubmitCheckOrderDtlResponse dtlOrderResponse = (QuerySubmitCheckOrderDtlResponse) TmsFacadeUtil.executeThrowException(querySubmitCheckOrderDtlFacade, request, disInfoDto);

        List<SubmitUncheckOrderDtlDto> dtlOrderList = new ArrayList<SubmitUncheckOrderDtlDto>();
        dto.setPageNo(dtlOrderResponse.getPageNo());
        dto.setTotalPage(dtlOrderResponse.getTotalPage());
        dto.setTotalCount(dtlOrderResponse.getTotalCount());
        List<SubmitCheckOrderDtlRespBean> pageList = dtlOrderResponse.getSubmitCheckOrderDtlRespBeanList();
        if (!CollectionUtils.isEmpty(pageList)) {
            SubmitUncheckOrderDtlDto dtlDto = null;
            for (SubmitCheckOrderDtlRespBean orderDtlBean : pageList) {
                dtlDto = new SubmitUncheckOrderDtlDto();
                BeanUtils.copyProperties(orderDtlBean, dtlDto);
                dtlOrderList.add(dtlDto);
            }
        }
        dto.setSubmitUncheckOrderDtlList(dtlOrderList);
        return dto;
    }


    @Override
    public boolean counterModifyDicount(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        CounterModifyDicountRequest request = new CounterModifyDicountRequest();
        request.setFundCode(reqDto.getFundCode());
        request.setBusiCodes(reqDto.getBusiCodes());
        request.setPaymemtTypes(reqDto.getPaymemtTypes());
        request.setTaTradeDtStart(reqDto.getTaTradeDtStart());
        request.setTaTradeDtEnd(reqDto.getTaTradeDtEnd());
        request.setAfterDiscountRate(reqDto.getAfterDiscountRate());
        request.setOperatorNo(reqDto.getOperatorNo());

        TmsFacadeUtil.executeThrowException(counterModifyDicountFacade, request, disInfoDto);
        return true;
    }

    @Override
    public QueryAppRateChangeResDto queryAppRateChange(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        QueryAppRateChangeResDto resDto = new QueryAppRateChangeResDto();

        QueryFundCheckOrderCondition queryCondition = new QueryFundCheckOrderCondition();
        queryCondition.setFundCode(reqDto.getFundCode());
        queryCondition.setBusiCodes(reqDto.getBusiCodes());
        queryCondition.setPaymemtTypes(reqDto.getPaymemtTypes());
        queryCondition.setTaTradeDtStart(reqDto.getTaTradeDtStart());
        queryCondition.setTaTradeDtEnd(reqDto.getTaTradeDtEnd());
        queryCondition.setIsAppDiscountFlag(YesOrNoEnum.YES.getCode());
        // 外部订单号
        QueryFundCheckOrderRequest request = new QueryFundCheckOrderRequest();
        request.setQueryCondition(queryCondition);
        request.setPageNo(reqDto.getPageNo());
        request.setPageSize(reqDto.getPageSize());
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryFundCheckOrderFacade, request, disInfoDto);

        List<FundCheckOrderBean> fundCheckOrderBeanList = null;
        QueryFundCheckOrderResponse response = (QueryFundCheckOrderResponse) baseResp;
        fundCheckOrderBeanList = response.getFundCheckOrderBeanList();

        resDto.setPageNo(baseResp.getPageNo());
        resDto.setTotalCount(baseResp.getTotalCount());
        resDto.setTotalPage(baseResp.getTotalPage());

        List<QueryAppRateChangeDto> returnDtoList = new ArrayList<QueryAppRateChangeDto>();
        if (CollectionUtils.isNotEmpty(fundCheckOrderBeanList)) {
            QueryAppRateChangeDto dto = null;
            for (FundCheckOrderBean fundCheckOrder : fundCheckOrderBeanList) {
                dto = new QueryAppRateChangeDto();
                dto.setDealNo(fundCheckOrder.getDealNo());
                dto.setDealDtlNo(fundCheckOrder.getDealDtlNo());
                dto.setSubmitDealNo(fundCheckOrder.getSubmitDealNo());

                dto.setProtocolNo(fundCheckOrder.getProtocolNo());
                dto.setProtocolType(fundCheckOrder.getProtocolType());

                dto.setTxAcctNo(fundCheckOrder.getTxAcctNo());
                dto.setCustName(fundCheckOrder.getCustName());
                dto.setIdNo(fundCheckOrder.getIdNo());

                dto.setFundCode(fundCheckOrder.getFundCode());
                dto.setFundName(fundCheckOrder.getFundName());
                dto.setNav(fundCheckOrder.getNav());

                dto.setAppAmt(fundCheckOrder.getAppAmt());
                dto.setAppVol(fundCheckOrder.getAppVol());
                dto.setAckAmt(fundCheckOrder.getAckAmt());
                dto.setAckVol(fundCheckOrder.getAckVol());

                dto.setAppDate(fundCheckOrder.getAppDate());
                dto.setAppTime(fundCheckOrder.getAppTime());

                dto.setCpAcctNo(fundCheckOrder.getCpAcctNo());
                dto.setBankAcct(fundCheckOrder.getBankAcct());
                dto.setPaymentType(fundCheckOrder.getPaymentType());
                dto.setTaTradeDt(fundCheckOrder.getTaTradeDt());
                dto.setDisCode(fundCheckOrder.getDisCode());
                dto.setDiscountRate(fundCheckOrder.getDiscountRate());
                dto.setzBusiCode(fundCheckOrder.getzBusiCode());
                dto.setmBusiCode(fundCheckOrder.getmBusiCode());
                dto.setBusiCode(fundCheckOrder.getBusiCode());
                dto.setTxAppFlag(fundCheckOrder.getTxAppFlag());

                returnDtoList.add(dto);
            }
        }
        resDto.setQueryAppRateChangeList(returnDtoList);
        return resDto;
    }

    @Override
    public List<CounterShareMergeTradeOrderDto> queryShareMergeTradeOrder(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception {

        // 订单明细信息
        QueryFundDealOrderDtlCondition queryCondition = new QueryFundDealOrderDtlCondition();
        queryCondition.setExternalDealNo(reqDto.getDealAppNo());
        // 外部订单号
        QueryFundDealOrderDtlRequest request = new QueryFundDealOrderDtlRequest();
        request.setQueryCondition(queryCondition);
        // 这个页面 没有分页按钮  先查100条吧 一般的用户数据都能够展示的下
        request.setPageSize(100);
        request.setPageNo(1);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(queryFundDealOrderDtlFacade, request, disInfoDto);

        List<FundDealOrderDtlBean> fundDealOrderDtlBeanList = null;
        if (baseResp != null) {
            QueryFundDealOrderDtlResponse response = (QueryFundDealOrderDtlResponse) baseResp;
            fundDealOrderDtlBeanList = response.getFundDealOrderDtlBeanList();
        }

        List<CounterShareMergeTradeOrderDto> returnDto = null;
        if (CollectionUtils.isNotEmpty(fundDealOrderDtlBeanList)) {
            // 查询审核订单明细表，查询转入份额
            QuerySubmitCheckOrderDtlRequest checkDtlRequest = new QuerySubmitCheckOrderDtlRequest();
            checkDtlRequest.setDealAppNo(reqDto.getDealAppNo());
            BaseResponse checkDtlResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderDtlFacade, checkDtlRequest, disInfoDto);
            Map<String, BigDecimal> preAppVolMap = Maps.newHashMap();
            if (checkDtlResp != null) {
                QuerySubmitCheckOrderDtlResponse dtlOrderResponse = (QuerySubmitCheckOrderDtlResponse) checkDtlResp;
                preAppVolMap.putAll(dtlOrderResponse.getPreAppVolMap());
            }

            returnDto = new ArrayList<CounterShareMergeTradeOrderDto>(16);

            CounterShareMergeTradeOrderDto dto = null;
            for (FundDealOrderDtlBean dealOrderDtl : fundDealOrderDtlBeanList) {
                dto = new CounterShareMergeTradeOrderDto();
                dto.setDealNo(reqDto.getDealNo());
                dto.setTxAcctNo(dealOrderDtl.getTxAcctNo());
                dto.setCustName(dealOrderDtl.getCustName());
                dto.setzBusiCode(dealOrderDtl.getzBusiCode());
                dto.setmBusiCode(dealOrderDtl.getmBusiCode());
                dto.setFundCode(dealOrderDtl.getFundCode());
                dto.setFundName(dealOrderDtl.getFundName());

                dto.setOutBankAcct(dealOrderDtl.getDtlBankAcct());
                dto.setOutProtocolNo(dealOrderDtl.getDtlProtocolNo());
                dto.setAppVol(dealOrderDtl.getAppVol());
                dto.setInBankAcct(dealOrderDtl.getBankAcct());
                dto.setInProtocolNo(dealOrderDtl.getProtocolNo());
                String key = dealOrderDtl.getFundCode() + dealOrderDtl.getDtlProtocolNo();
                BigDecimal preAppVol = preAppVolMap.get(key);
                if (preAppVol == null) {
                    preAppVol = BigDecimal.ZERO;
                }
                dto.setBeforeInAvailVol(preAppVol);
                dto.setAfterInAvailVol(preAppVol.add(dealOrderDtl.getAppVol()));
                dto.setTxAppFlag(dealOrderDtl.getTxAppFlag());
                dto.setTxAckFlag(dealOrderDtl.getTxAckFlag());
                dto.setRetDesc(dealOrderDtl.getResult());
                dto.setOutCpAcctNo(dealOrderDtl.getDtlCpAcctNo());
                returnDto.add(dto);
            }
        }
        return returnDto;
    }


    @Override
    public TmsCounterResult counterTransferInTube(List<CounterTransferTubeReqDto> reqDtoList, DisInfoDto disInfoDto) throws Exception {

        TmsCounterResult rst = null;
        // 校验交易工作日
        validateWorkDate(reqDtoList.get(0), disInfoDto);
        // 转投成功条数
        int succCount = 0;
        StringBuffer failMsg = new StringBuffer("");

        // 循环调用转托管接口
        for (CounterTransferTubeReqDto dto : reqDtoList) {
            boolean validFlag;
                try {
                    // 校验是否可转投
                    validFlag = tmsFundCounterValidService.transferTubeInVolValidate(dto, disInfoDto);

                } catch (TmsCounterException e) {
                    failMsg.append(Constants.FUND_STR + dto.getFundName() + Constants.TRANS_IN_FAIL + e.getDesc() + Constants.NEXT_LINE);
                    continue;
                }
            if (validFlag) {
                // 提示语
                CounterTransferTubeRespDto respDto = null;
                try {
                    // 调用转托管转入接口转托管
                    respDto = transferTube(dto, disInfoDto);
                } catch (TmsCounterException e) {
                    // 捕获异常前端提示
                    failMsg.append(Constants.FUND_STR + dto.getFundName() + Constants.TRANS_IN_FAIL + Constants.NEXT_LINE + e.getDesc() + Constants.NEXT_LINE);
                }

                if (respDto != null) {
                String returnCode = respDto.getReturnCode();
                    // 成功条数++
                    if (isSucc(ReturnCodeEnum.SUCC_TMS.getCode(), returnCode) || isSucc(ReturnCodeEnum.SUCC_NEW.getCode(), returnCode)){
                        succCount++;
                    } else {
                        // 失败数据
                        failMsg.append(Constants.FUND_STR + dto.getFundName() + Constants.TRANS_IN_FAIL + Constants.NEXT_LINE + respDto.getDescription() + Constants.NEXT_LINE);
                    }
                }

            }

        }

        String sucCode = TmsCounterResultEnum.SUCC.getCode();
        // 有失败的数据
        if(succCount == 0){
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD.getCode(),Constants.FUND_STR + (reqDtoList.size() - succCount) + Constants.NUM_STR + Constants.NEXT_LINE + failMsg);
        }else if (succCount != reqDtoList.size()) {
            rst = new TmsCounterResult(sucCode, Constants.TRANS_IN_SUC + succCount + Constants.NUM_STR +"，"+ Constants.FAIL_STR + (reqDtoList.size() - succCount) + Constants.NUM_STR + Constants.NEXT_LINE + failMsg);
        } else {
            rst = new TmsCounterResult(sucCode, Constants.TRANS_IN_SUC + succCount + Constants.NUM_STR);
        }
        return rst;
    }

    public static boolean isSucc(String succCode, String returnCode) {
        return succCode.equals(returnCode);
    }


    private void validateWorkDate(CounterTransferTubeReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), reqDto.getTaCode(), disInfoDto)) {
            String tradeDt = queryTradeDayOuterService.getWorkDay(reqDto.getAppDt(), reqDto.getAppTm());
            String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
            if (tradeDt.equals(workDay)) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
            }
        }
    }

    @Override
    public TmsCounterResult counterTransferTube(CounterTransferTubeReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        List<TransferTubeOrderReqDto> transferTubeDetailList = reqDto.getTransferTubeDetailList();
        Map<String, List<TransferTubeOrderReqDto>> dtoMap = transferTubeDetailList.stream().collect(Collectors.groupingBy(t->t.getFundCode() + "-" + t.gettSellerTxAcctNo()));
        // 1、校验基金所属ta是否收市
        for (Map.Entry<String, List<TransferTubeOrderReqDto>> entry : dtoMap.entrySet()){
            TransferTubeOrderReqDto transferTubeOrderReqDto = entry.getValue().get(0);
            if (tmsCounterService.isCounterEnd(SysCodeEnum.BATCH_GM.getCode(), transferTubeOrderReqDto.getTaCode(), disInfoDto)) {
                String tradeDt = queryTradeDayOuterService.getWorkDay(reqDto.getAppDt(), reqDto.getAppTm());
                String workDay = tmsCounterService.getSystemWorkDay(disInfoDto);
                if (tradeDt.equals(workDay)) {
                    throw new TmsCounterException(TmsCounterResultEnum.COUNTER_END);
                }
            }
        }

        // 转托管转出成功条数
        int succCount = 0;
        StringBuilder failMsg = new StringBuilder();

        // 循环调用转托管接口
        for (Map.Entry<String, List<TransferTubeOrderReqDto>> entry : dtoMap.entrySet()) {
            TransferTubeOrderReqDto transferTubeOrderReqDto = entry.getValue().get(0);
            CounterTransferTubeReqDto dto = new CounterTransferTubeReqDto();
            BeanUtils.copyProperties(reqDto, dto);
            dto.setFundCode(transferTubeOrderReqDto.getFundCode());
            dto.setFundName(transferTubeOrderReqDto.getFundName());
            dto.setFundShareClass(transferTubeOrderReqDto.getFundShareClass());
            dto.setProductClass(transferTubeOrderReqDto.getProductClass());
            dto.settSellerTxAcctNo(transferTubeOrderReqDto.gettSellerTxAcctNo());
            dto.settSellerCode(transferTubeOrderReqDto.gettSellerCode());
            dto.settOutletCode(transferTubeOrderReqDto.gettOutletCode());
            dto.setTaCode(transferTubeOrderReqDto.getTaCode());
            dto.setTransferTubeDetailList(entry.getValue());
            boolean validFlag;
            try {
                // 校验是否可以转托管
                validFlag = tmsFundCounterValidService.transferTubeOutVolValidate(dto, disInfoDto);

            } catch (TmsCounterException e) {
                failMsg.append(Constants.FUND_STR).append(transferTubeOrderReqDto.getFundName()).append(Constants.TRANS_IN_FAIL).append(e.getDesc()).append(Constants.NEXT_LINE);
                continue;
            }
            if (validFlag) {
                // 提示语
                CounterTransferTubeRespDto resp = null;
                try {
                    // 调用转托管转入接口转托管
                    resp = transferTubeOut(dto, disInfoDto);
                } catch (TmsCounterException e) {
                    // 捕获异常前端提示
                    failMsg.append(Constants.FUND_STR).append(dto.getFundName()).append(Constants.TRANS_IN_FAIL).append(Constants.NEXT_LINE).append(e.getDesc()).append(Constants.NEXT_LINE);
                }

                if (resp != null) {
                    String returnCode = resp.getReturnCode();
                    // 成功条数++
                    if (isSucc(ReturnCodeEnum.SUCC_TMS.getCode(), returnCode) || isSucc(ReturnCodeEnum.SUCC_NEW.getCode(), returnCode)){
                        succCount++;
                    } else {
                        // 失败数据
                        failMsg.append(Constants.FUND_STR).append(dto.getFundName()).append(Constants.TRANS_IN_FAIL).append(Constants.NEXT_LINE).append(resp.getDescription()).append(Constants.NEXT_LINE);
                    }
                }

            }

        }

        String sucCode = TmsCounterResultEnum.SUCC.getCode();
        TmsCounterResult rst = null;
        // 有失败的数据
        if(succCount == 0){
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD.getCode(),Constants.FUND_STR + (dtoMap.size() - succCount) + Constants.NUM_STR + Constants.NEXT_LINE + failMsg);
        }else if (succCount != dtoMap.size()) {
            rst = new TmsCounterResult(sucCode, Constants.TRANS_IN_SUC + succCount + Constants.NUM_STR +"，"+ Constants.FAIL_STR + (dtoMap.size() - succCount) + Constants.NUM_STR + Constants.NEXT_LINE + failMsg);
        } else {
            rst = new TmsCounterResult(sucCode, Constants.TRANS_IN_SUC + succCount + Constants.NUM_STR);
        }
        return rst;
    }

    private CounterTransferTubeRespDto transferTubeOut(CounterTransferTubeReqDto dto, DisInfoDto disInfoDto) throws TmsCounterException {
        CounterTransferTubeRespDto resp = null;
        CounterTransferTubeRequest request = new CounterTransferTubeRequest();
        BeanUtils.copyProperties(dto, request);

        CounterTransferTubeBean orderBean = new CounterTransferTubeBean();
        BeanUtils.copyProperties(dto, orderBean);
        List<TransferTubeDetail> outOrderList = null;
        if (CollectionUtils.isNotEmpty(dto.getTransferTubeDetailList())) {
            outOrderList = new ArrayList<TransferTubeDetail>(16);
            TransferTubeDetail dtlOrder = null;
            for (TransferTubeOrderReqDto volOrder : dto.getTransferTubeDetailList()) {
                dtlOrder = new TransferTubeDetail();
                BeanUtils.copyProperties(volOrder, dtlOrder);
                outOrderList.add(dtlOrder);
            }
        }
        orderBean.setTransferTubeDetailList(outOrderList);
        request.setCounterTransferTubeBean(orderBean);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowsException(counterTransferTubeFacade, request, disInfoDto);
        if (baseResp != null) {
            CounterTransferTubeResponse response = (CounterTransferTubeResponse) baseResp;
            resp = new CounterTransferTubeRespDto();
            BeanUtils.copyProperties(response, resp);
        }

        return resp;
    }

    private CounterTransferTubeRespDto transferTube(CounterTransferTubeReqDto reqDto, DisInfoDto disInfoDto) throws TmsCounterException {
        CounterTransferTubeRespDto resp = null;
        CounterTransferTubeRequest request = new CounterTransferTubeRequest();
        BeanUtils.copyProperties(reqDto, request);

        CounterTransferTubeBean orderBean = new CounterTransferTubeBean();
        BeanUtils.copyProperties(reqDto, orderBean);
        List<TransferTubeDetail> outOrderList = null;
        if (CollectionUtils.isNotEmpty(reqDto.getTransferTubeDetailList())) {
            outOrderList = new ArrayList<TransferTubeDetail>(16);
            TransferTubeDetail dtlOrder = null;
            for (TransferTubeOrderReqDto volOrder : reqDto.getTransferTubeDetailList()) {
                dtlOrder = new TransferTubeDetail();
                BeanUtils.copyProperties(volOrder, dtlOrder);
                outOrderList.add(dtlOrder);
            }
        }
        orderBean.setTransferTubeDetailList(outOrderList);
        request.setCounterTransferTubeBean(orderBean);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowsException(counterTransferTubeFacade, request, disInfoDto);
        if (baseResp != null) {
            CounterTransferTubeResponse response = (CounterTransferTubeResponse) baseResp;
            resp = new CounterTransferTubeRespDto();
            BeanUtils.copyProperties(response, resp);
        }
        return resp;
    }

    @Override
    public CounterEndTaRespDto queryCounterEndTaInfo(String sysCode, DisInfoDto disInfoDto) throws Exception {

        QueryCounterEndTaCountRequest request = new QueryCounterEndTaCountRequest();
        request.setSysCode(sysCode);
        QueryCounterEndTaCountResponse response = (QueryCounterEndTaCountResponse)TmsFacadeUtil.executeThrowException(queryCounterEndTaCountFacade, request, disInfoDto);

        CounterEndTaRespDto returnDto = null;
        returnDto = new CounterEndTaRespDto();
        returnDto.setTotalTaNum(response.getTotalTaNum());
        returnDto.setSuccEndTaNum(response.getSuccEndTaNum());
        returnDto.setNonEndTaNum(response.getNonEndTaNum());

        List<FundTaInfoDto> taList = new ArrayList<FundTaInfoDto>();
        FundTaInfoDto fundTaDto = null;
        if (CollectionUtils.isNotEmpty(response.getNotEndTaList())) {
            for (CounterNotEndTaBean bean : response.getNotEndTaList()) {
                fundTaDto = new FundTaInfoDto();
                fundTaDto.setTaCode(bean.getTaCode());
                fundTaDto.setTaName(bean.getTaName());
                fundTaDto.setFundCode(bean.getFundCode());
                fundTaDto.setFundAttr(bean.getFundAttr());
                fundTaDto.setFundType(bean.getFundType());

                taList.add(fundTaDto);
            }
        }
        returnDto.setNotEndTaList(taList);

        List<FundTaInfoDto> notProcessTaList = new ArrayList<FundTaInfoDto>();
        if (CollectionUtils.isNotEmpty(response.getNotProcessTaList())) {
            for (ConfirmProcess bean : response.getNotProcessTaList()) {
                fundTaDto = new FundTaInfoDto();
                fundTaDto.setTaCode(bean.getTaCode());
                fundTaDto.setTaName(bean.getTaName());

                notProcessTaList.add(fundTaDto);
            }
        }
        returnDto.setNotProcessTaList(notProcessTaList);
        return returnDto;
    }

    @Override
    public CounterRespDto counterSaveOrDelNotEndTa(String actionType, String sysCode, List<FundTaInfoDto> taDtoList, DisInfoDto disInfoDto) throws Exception {
        String tradeDt = tmsCounterService.getSystemWorkDay(disInfoDto);
        CounterSaveOrDelNotEndTaRequest request = new CounterSaveOrDelNotEndTaRequest();
        request.setActionType(actionType);
        request.setSysCode(sysCode);
        request.setTradeDt(tradeDt);
        if (CollectionUtils.isNotEmpty(taDtoList)) {
            List<CounterNotEndTaBean> notEndList = new ArrayList<CounterNotEndTaBean>(20);
            CounterNotEndTaBean bean = null;
            for (FundTaInfoDto fundTaDto : taDtoList) {
                bean = new CounterNotEndTaBean();
                bean.setTaCode(fundTaDto.getTaCode());
                bean.setTaName(fundTaDto.getTaName());
                bean.setFundCode(fundTaDto.getFundCode());
                bean.setFundAttr(fundTaDto.getFundAttr());
                bean.setFundType(fundTaDto.getFundType());

                notEndList.add(bean);
            }
            request.setNotEndTaBeanList(notEndList);
        }
        CounterRespDto resp = null;
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(counterSaveOrDelNotEndTaFacade, request, disInfoDto);
        if (baseResp != null) {
            CounterSaveOrDelNotEndTaResponse response = (CounterSaveOrDelNotEndTaResponse) baseResp;
            resp = new CounterRespDto();
            BeanUtils.copyProperties(response, resp);
        }
        return resp;
    }

    @Override
    public CounterTransferTubeRespDto counterLctTransferTube(CounterTransferTubeReqDto reqDto, DisInfoDto disInfoDto) throws Exception {
        CounterTransferTubeRespDto resp = null;

        LctCounterTransferTubeRequest request = new LctCounterTransferTubeRequest();
        BeanUtils.copyProperties(reqDto, request);

        CounterTransferTubeBean orderBean = new CounterTransferTubeBean();
        BeanUtils.copyProperties(reqDto, orderBean);
        List<TransferTubeDetail> outOrderList = null;
        if(CollectionUtils.isNotEmpty(reqDto.getTransferTubeDetailList())){
            outOrderList = new ArrayList<TransferTubeDetail>(16);
            TransferTubeDetail dtlOrder = null;
            for(TransferTubeOrderReqDto volOrder : reqDto.getTransferTubeDetailList()){
                dtlOrder = new TransferTubeDetail();
                BeanUtils.copyProperties(volOrder, dtlOrder);
                outOrderList.add(dtlOrder);
            }
        }
        orderBean.setTransferTubeDetailList(outOrderList);
        request.setCounterTransferTubeBean(orderBean);
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(lctCounterTransferTubeFacade, request, disInfoDto);
        if (baseResp != null) {
            LctCounterTransferTubeResponse response = (LctCounterTransferTubeResponse) baseResp;
            resp = new CounterTransferTubeRespDto();
            BeanUtils.copyProperties(response, resp);
        }
        return resp;
    }

    @Override
    public List<ExchangeCardMaterialDtlDto> queryMaterialDtl(CounterQueryOrderReqDto reqDto, DisInfoDto disInfoDto) throws Exception  {
        QuerySubmitCheckOrderDtlRequest request = new QuerySubmitCheckOrderDtlRequest();
        if (reqDto != null) {
            request.setDealAppNo(reqDto.getDealAppNo());
        }
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(querySubmitCheckOrderDtlFacade, request, disInfoDto);

        List<ExchangeCardMaterialDtlDto> dtlOrderList = new ArrayList<ExchangeCardMaterialDtlDto>();
        if (baseResp != null) {
            QuerySubmitCheckOrderDtlResponse dtlOrderResponse = (QuerySubmitCheckOrderDtlResponse) baseResp;
            List<MaterialDtlRespBean> pageList = dtlOrderResponse.getMaterialDtlRespBeanList();
            if (!CollectionUtils.isEmpty(pageList)) {
                ExchangeCardMaterialDtlDto dtlDto = null;
                for (MaterialDtlRespBean orderDtlBean : pageList) {
                    dtlDto = new ExchangeCardMaterialDtlDto();
                    BeanUtils.copyProperties(orderDtlBean, dtlDto);
                    dtlOrderList.add(dtlDto);
                }
            }
        }

        return dtlOrderList;
    }


    /**
     * @description:上传双录文件
     * @param inputStream	
     * @param fileName	
     * @param appDt
     * @return java.lang.String
     * @author: xingxing.wang
     * @date: 2020/8/21 18:24
     * @since JDK 1.8
     */
    @Override
    public String uploadDoubleRecordFile(InputStream inputStream, String fileName, String appDt) throws Exception {
        String dateDir = appDt.substring(0, 6);
        String targetFileName = UUID.randomUUID() + "-" + fileName;
        String targetDirName = tmsCounterNacosConfig.getTargetDir() + File.separator + dateDir;
        String filePath = targetDirName + File.separator + targetFileName;
        try {
            FileUtils.saveFile(inputStream, targetDirName, targetFileName);
        } catch (Exception e) {
            logger.error("", e);
            throw new TmsCounterException(TmsCounterResultEnum.UPLOAD_DOUBLE_RECORD_FILE_ERROR,e);
        }
        return filePath;
    }
   
    @Override
    public QueryLatestPayCustResult queryLatestPayRecord(String txAcctNo, String disCode, String cpAcctNo, String bankAcctDigest) {
        //查询最近一次打款记录
        QueryLatestPayCustContext context = new QueryLatestPayCustContext();
        context.setBankAcctDigest(bankAcctDigest);
        return queryLatestPayCustOutService.process(context);
    }

}
