/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.StringUtils;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.CheckTypeEnum;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.fundservice.trade.TmsFundCounterService;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.util.CommonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * @className AuditingOrderFileController
 * @description 线上化资料审核
 * <AUTHOR>
 * @date 2019/5/27 9:32
 */
@Controller
public class AuditingOrderFileController {

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    private TmsFundCounterService tmsFundCounterService;

    @RequestMapping("/tmscounter/orderfile.htm")
    public ModelAndView arderFile(HttpServletRequest request,
                                         HttpServletResponse response) throws Exception {
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String condion = request.getParameter("condition");
        if(StringUtils.isEmpty(condion)){
           throw new TmsCounterException("","参数错误，驳回订单信息为空");
        }
        AuditingOrderFileCmd cmd = JSON.parseObject(condion, AuditingOrderFileCmd.class);
        tmsCounterOutService.auditingFile(operatorInfoCmd, cmd, null);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/recheckrejectfile.htm")
    public ModelAndView recheckrejectFile(HttpServletRequest request,
                                  HttpServletResponse response) throws Exception {
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String condion = request.getParameter("condition");
        if(StringUtils.isEmpty(condion)){
            throw new TmsCounterException("","参数错误，驳回订单信息为空");
        }
        AuditingOrderFileCmd cmd = JSON.parseObject(condion, AuditingOrderFileCmd.class);

        if(StringUtils.isEmpty(cmd.getForderid())){
            throw new TmsCounterException("","关联外部预申请单号为空");
        }

        tmsCounterOutService.auditingFile(operatorInfoCmd, cmd, cmd.getForderid());

        rejectTradeOrder(operatorInfoCmd, cmd.getForderid(),  cmd);
        WebUtil.write(response, rst);
        return null;
    }
    
    /**
     * 
     * @Description 驳回交易订单
     * 
     * @param operatorInfoCmd
     * @param dealAppNo
     * @return void
     * <AUTHOR>
     * @Date 2019/1/4 17:28
     **/
    private void rejectTradeOrder(OperatorInfoCmd operatorInfoCmd, String dealAppNo, AuditingOrderFileCmd cmd) throws Exception {
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(1);
        queryReqDto.setPageSize(1);
        CounterQueryOrderRespDto counterQueryOrderRespDto = tmsCounterService.counterQueryOrder(queryReqDto,null);
        CounterOrderDto counterOrderDto =  getCounterOrder(counterQueryOrderRespDto.getCounterOrderList(), dealAppNo);

        if(counterOrderDto == null){
            // 查询零售
            counterOrderDto =  tmsFundCounterService.counterQueryOrderById(queryReqDto, null);
        }

        DisInfoDto disInfoDto =new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterOrderDto);
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        BeanUtils.copyProperties(counterOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setMemo("材料退回");

        if(TxCodes.COUNTER_TRANS_VOL.equals(submitUncheckOrderDto.getTxCode())){
            // 订单作废
            tmsCounterService.modifyCheckOrder(submitUncheckOrderDto, CheckTypeEnum.CHECK_CANCEL.getCode(), disInfoDto);
        }else{
            tmsCounterService.checkOrder(submitUncheckOrderDto, CheckTypeEnum.CHECK_MATERIAL_REJECT.getCode(), disInfoDto);
        }

    }


    /**
     *
     * getCounterOrder:(获取柜台订单)
     * @param counterOrderList
     * @param dealAppNo
     * @return
     * <AUTHOR>
     * @date 2018年2月12日 下午4:30:19
     */
    private CounterOrderDto getCounterOrder(List<CounterOrderDto> counterOrderList, String dealAppNo){

        if(org.springframework.util.CollectionUtils.isEmpty(counterOrderList)){
            return null;
        }

        for(CounterOrderDto counterOrderDto : counterOrderList){
            if(counterOrderDto.getDealAppNo().equals(dealAppNo)){
                return counterOrderDto;
            }
        }

        return null;
    }

}
