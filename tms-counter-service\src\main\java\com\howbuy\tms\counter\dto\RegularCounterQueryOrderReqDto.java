/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseRequestDto;

import java.util.Date;
import java.util.List;

/**
 * @description:(定期柜台查询订单请求)
 * <AUTHOR>
 * @date 2018年6月25日 下午15:40:47
 * @since JDK 1.6
 */
public class RegularCounterQueryOrderReqDto extends BaseRequestDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 297838474257359068L;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 
     * /** 交易码
     */
    private List<String> txCodeList;

    /**
     * 交易码
     */
    private String txCode;
    /**
     * /** 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;
    /**
     * 审核状态,0-未审核；1-审核通过；3-驳回；4-作废；
     */
    private List<String> checkFlagLsit;
    /**
     * 申请状态，0-未申请，1-申请通过，2-申请失败
     */
    private String appFlag;

    /**
     * 查询开始时间
     */
    private Date beginDtm;

    /**
     * 查询结束时间
     */
    private Date endDtm;

    /**
     * 证件号
     */
    private String idNo;
    /**
     * 预申请单号
     */
    private String dealAppNo;
    /**
     * 客户名
     */
    private String custName;
    /**
     * ta日期
     */
    private String tradeDt;
    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 基金代码
     */
    private String productCode;
    
    /**
     * 产品通道 3-群济私募 5-好买公募 6-好买高端公募
     */
    private String productChannel;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    private String appDt;

    public List<String> getCheckFlagLsit() {
        return checkFlagLsit;
    }

    public void setCheckFlagLsit(List<String> checkFlagLsit) {
        this.checkFlagLsit = checkFlagLsit;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public List<String> getTxCodeList() {
        return txCodeList;
    }

    public void setTxCodeList(List<String> txCodeList) {
        this.txCodeList = txCodeList;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }

    public Date getBeginDtm() {
        return beginDtm;
    }

    public void setBeginDtm(Date beginDtm) {
        this.beginDtm = beginDtm;
    }

    public Date getEndDtm() {
        return endDtm;
    }

    public void setEndDtm(Date endDtm) {
        this.endDtm = endDtm;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
}
