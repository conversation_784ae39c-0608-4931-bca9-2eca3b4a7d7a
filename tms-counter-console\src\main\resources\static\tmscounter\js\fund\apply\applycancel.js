$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplyCancel.checkOrder = {};	 
	ApplyCancel.init(checkId,custNo,disCode,idNo);
});

var ApplyCancel = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,ApplyCancel.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyCancel.checkOrder);
		});
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplyCancel.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(ApplyCancel.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(ApplyCancel.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		
		QueryCanCancel.queryCanCancel(QueryCustInfo.custInfo.custNo, ApplyCancel.checkOrder.dealNo, ApplyCancel.checkOrder.operatorNo);
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(ApplyCancel.checkOrder.agentFlag);
		}
		
		var txCode = ApplyCancel.checkOrder.txCode;
		if($("#cancelType").length > 0){
			if('Z910045' == txCode){
				$("#cancelType").val(1);
			}else {
				$("#cancelType").val(2);
			}
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").val(ApplyCancel.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(ApplyCancel.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, ApplyCancel.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(ApplyCancel.checkOrder.transactorName);
		}

		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(ApplyCancel.checkOrder.transactorIdNo);
		}
		
		var withdrawDirection = ApplyCancel.checkOrder.withdrawDirection;
		if($("#withdrawDirection").length > 0){
			if('06' == withdrawDirection){
				$("#withdrawDirection").val("06");
			}
			if('04' == withdrawDirection) {
				$("#withdrawDirection").val("04");
			}
		}
		
//		if($("#withdrawDirection").length > 0){
//			$("#withdrawDirection").html(CommonUtil.getMapValue(CONSTANTS.PAYMENT_TYPE, ApplyCancel.checkOrder.withdrawDirection, ''));
//		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(parseInt(ApplyCancel.checkOrder.transactorIdType));
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(ApplyCancel.checkOrder.memo);
		}
		
	},

}
