package com.howbuy.tms.counter;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.session.SessionAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ImportResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @author: xin.jiang.cn
 * @date: 2023年12月19日 17:18:49
 * @description:
 */
@ImportResource(value = "classpath:spring-all.xml")
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.howbuy.tms.counter"},exclude = {DataSourceAutoConfiguration.class, SessionAutoConfiguration.class})
public class TmsCounterConsoleApplication {
    public static void main(String[] args) {
        SpringApplication.run(TmsCounterConsoleApplication.class, args);
    }



    /**
     * @api {GET} /hello hello
     * @apiVersion 1.0.0
     * @apiGroup TmsCounterConsoleApplication
     * @apiName hello
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "U"
     */
    /**
     * @api {POST} /hello hello
     * @apiVersion 1.0.0
     * @apiGroup TmsCounterConsoleApplication
     * @apiName hello
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "U"
     */
    /**
     * @api {GET} /error hello
     * @apiVersion 1.0.0
     * @apiGroup TmsCounterConsoleApplication
     * @apiName hello
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "U"
     */
    /**
     * @api {PUT} /error hello
     * @apiVersion 1.0.0
     * @apiGroup TmsCounterConsoleApplication
     * @apiName hello
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "U"
     */
    /**
     * @api {DELETE} /error hello
     * @apiVersion 1.0.0
     * @apiGroup TmsCounterConsoleApplication
     * @apiName hello
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "U"
     */
    /**
     * @api {POST} /error hello
     * @apiVersion 1.0.0
     * @apiGroup TmsCounterConsoleApplication
     * @apiName hello
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "U"
     */
    @RequestMapping( value = "/hello", method = {RequestMethod.GET, RequestMethod.POST})
    public String hello() {
        return "hello";
    }
}
