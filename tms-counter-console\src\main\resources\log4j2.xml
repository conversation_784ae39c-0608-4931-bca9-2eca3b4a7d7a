<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">

	<properties>
		<property name="logPath">/data/logs/tomcat-tms-counter</property>
		<property name="rollingLogName">tms-counter-console</property>
		<property name="sensitiveInfoAccessLogName">sensitiveInfoAccessLog</property>
	</properties>

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}|${ctx:uuid}|%t|%-5p|%c{1}:%L|%msg%n" />
		</Console>

		<RollingFile name="RollingFile" filename="${logPath}/${rollingLogName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${rollingLogName}.log">
			<CustomPatternLayout>
				<pattern>
					%d{yyyy-MM-dd HH:mm:ss.SSS}|${ctx:uuid}|%-5p|%c{1}:%L|%msg%n
				</pattern>
				<replaces>
					<replace regex='(\"idNo":\"|\\"idNo\\":\\"|idNo=)(\w{14})\w{4}' replacement="$1$2****" />
					<replace regex='(\"mobile\":\"|\"mobileNo\":\"|\"mobileBank\":\"|mobileNo=)(\d{7})\d{4}' replacement="$1$2****" />
					<replace regex='(\"bankAcct\":\"|\"bankNo\":\"|\"bankAcctFull\":\"|bankAcct=)(\d{1,})(\d{6})' replacement="$1******$3" />
					<replace regex='(\"custName\":\"|\"userName\":\"|\"bankAcctName\":\")([\u4E00-\u9FA5]{1})(\u4E00-\u9FA5]{0,})(\u4E00-\u9FA5]{1})' replacement="$1$2*$3" />
					<replace regex='(\"address\":\"[^"]*?)[^"]{1,10}"' replacement='$1******\"' />
					<replace regex='(\"addr\":\"[^"]*?)[^"]{1,10}"' replacement='$1******\"' />
				</replaces>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
			<DefaultRolloverStrategy max="100" />
		</RollingFile>

		<RollingFile name="SensitiveInfoAccessLog" filename="${logPath}/${sensitiveInfoAccessLogName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${sensitiveInfoAccessLogName}.log">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}|${ctx:uuid}|%-5p|%c{1}:%L|%msg%n"/>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
			<DefaultRolloverStrategy max="100" />
		</RollingFile>

	</Appenders>

	<Loggers>
		<logger name="com.howbuy" level="info" additivity="true">
			<appender-ref ref="RollingFile"/>
		</logger>
		<logger name="sensitiveInfoAccessLog" level="info" additivity="false">
			<appender-ref ref="SensitiveInfoAccessLog"/>
		</logger>
		<root level="info">
			<appender-ref ref="Console" />
		</root>
	</Loggers>

</Configuration>
