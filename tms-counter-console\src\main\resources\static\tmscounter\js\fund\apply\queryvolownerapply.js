/**
 *查询份额合并或迁移申请
 *<AUTHOR>
 *@date 2018-05-10 11:23
 */
$(function () {
    Init.init();
    QueryVolOwnerApply.init();
    QueryVolOwnerApply.custInfo = {};
    QueryVolOwnerApply.checkOrders = [];

    var selectTxCodeHtml = '<option value="">全部</option>';
    $.each(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP, function (name, value) {
        selectTxCodeHtml += '<option value="' + name + '">' + value + ' </option>';
    });
    $("#selectTxCode").empty();
    $("#selectTxCode").html(selectTxCodeHtml);
    
    QueryVolOwnerApply.operatorNo = CommonUtil.getParam("operatorNo");
});
var QueryVolOwnerApply = {
		
	initDetailPoP: function (dealAppNo) {
		var uri = '../../../html/fund/trade/appratetchangedetail.html?operatorNo=';
        uri = uri + QueryVolOwnerApply.operatorNo + '&dealAppNo=' +  dealAppNo;
        console.info(dealAppNo);
		// POPUP
		layer.open({
			title: ['详情', false],
			type: 2,
			area: ['95%', '90%'],
			skin: 'layui-layer-rim', //加上边框
			btnAlign: 'l',
			content: uri
		});
	},
		
	init: function () {
        $("#queryBtn").on('click', function () {
            QueryVolOwnerApply.queryOrderInfo();
        });
        /**
         * 双击客户号查询客户信息
         */
        $("#custNo").on('dblclick',function(){
            QueryCustInfoSubPage.selectCustNo($(this));
        });
    },

    /**
     * 查询待审核订单信息
     */
    queryOrderInfo: function () {
        var uri = TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_URL || {};
        var reqparamters = {};
        var queryOrderConditionForm = $("#queryConditonForm").serializeObject();
        var queryOrderCondition = {};
        $.each(queryOrderConditionForm, function (name, value) {
            if (!CommonUtil.isEmpty(value)) {
                queryOrderCondition[name] = value;
            }
        });

        reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
        reqparamters.page = 1;
        reqparamters.pageSize = 20;
        var currWorkDay = CommonUtil.getWorkDay();
        reqparamters.tradeDt = currWorkDay;
        reqparamters.owner = "owner";
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxPaging(uri, paramters, QueryVolOwnerApply.queryOrderInfoCallBack, "pageView");
    },

    queryOrderInfoCallBack: function (data) {
        var bodyData = data;
        QueryVolOwnerApply.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];
        $("#rsList").empty();
        if (QueryVolOwnerApply.checkOrders.length <= 0) {
            var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
            $("#rsList").append(trHtml);
        }

        var staticData = bodyData.counterQueryOrderRespDto || {};
        $("#staticId").html("当页小计：申请笔数【" + QueryVolOwnerApply.checkOrders.length + "】申请份额【" + CommonUtil.formatAmount(staticData.pageAppVol) + "】 合计：申请笔数【" + staticData.totalCount + "】申请份额【" + CommonUtil.formatAmount(staticData.totalAppVol) + "】");

        var i = 1;
        $(QueryVolOwnerApply.checkOrders).each(function (index, element) {
            var trList = [];
            trList.push(i++);
            trList.push(CommonUtil.formatData(element.dealAppNo));
            trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
            trList.push(CommonUtil.formatData(element.custNameEncrypt));
            trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP, element.txCode, ''));
            trList.push(CommonUtil.formatData(element.fundCode, '--'));
            trList.push(CommonUtil.formatData(element.fundName, '--'));
            if (element.appVol > 0) {
                trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
            } else {
                trList.push('--');
            }
            trList.push(CommonUtil.formatData(element.appDt));
            trList.push(CommonUtil.formatData(element.appTm));
            trList.push('柜台');
            trList.push(CommonUtil.formatData(element.creator, ''));
            trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, element.checkFlag, ''));
            trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_APP_FLAG_MAP, element.appFlag, ''));

            if(CONSTANTS.QUERY_SUBMIT_APP_ORDER == element.txCode){
            	trList.push('');
            	var dealAppNoShow = '<a type="button" style="color: #06c;" onclick="QueryVolOwnerApply.initDetailPoP(\'' + element.dealAppNo + '\')">查看</a>';
                trList.push(dealAppNoShow);
            } else {
            	if (element.checkFlag == '1') {
                    trList.push('<a class="reQuery" href="javascript:void(0);" indexvalue = ' + index + ' style="color: #06c;">查看</a>');
                } else {
                    trList.push('');
                }

                if (element.checkFlag == '3' || element.checkFlag == '5') {
                    trList.push('<a class="read reCheck" href="javascript:void(0);" indexvalue = ' + index +  ' checknode='+'2' +' >查看</a>');
                    trList.push('<a class="update reCheck" href="javascript:void(0);" indexvalue = ' + index + ' checknode='+'3'+ '>修改</a>');
                } else {
                    trList.push('<a class="read reCheck" href="javascript:void(0);" indexvalue = ' + index + ' checknode='+'2' +'>查看</a>');
                    trList.push('--');
                }
            }

            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            $("#rsList").append(trHtml);
        });

        // 查订单详情
        $(".reQuery").off();
        $(".reQuery").on('click', function () {
            var indexValue = $(this).attr("indexvalue");
            var checkedOrder = QueryVolOwnerApply.checkOrders[indexValue] || {};
            QueryVolOwnerApply.checkedOrder = checkedOrder;

            var dealNo = checkedOrder.dealNo;
            var dealAppNo = checkedOrder.dealAppNo;
            var protocolType = checkedOrder.protocolType;
            var disCode = checkedOrder.disCode;
            var checkNode = $(this).attr('checknode') || '';
            QueryCheckOrder.queryMergeTransTradeOrderById(dealNo, dealAppNo, protocolType, disCode, QueryVolOwnerApply.queryDealOrderInfo, checkNode);


        });

        //绑定修改
        $(".update").off();
        $(".update").on('click', function () {
            var indexValue = $(this).attr("indexvalue");
            var checkedOrder = QueryVolOwnerApply.checkOrders[indexValue] || {};

            var txCode = checkedOrder.txCode;

            var param = "checkId=" + checkedOrder.dealAppNo + "&custNo=" + checkedOrder.txAcctNo + "&disCode=" + checkedOrder.disCode + "&idNo=" + checkedOrder.idNo;
            if (CONSTANTS.MERGE_VOL_TXCODE == txCode) {
                window.open("applymergevol.html?" + param, "_blank");
                return;
            } else if (CONSTANTS.TRANS_VOL_TXCODE == txCode) {
                window.open("applytransvol.html?" + param, "_blank");
                return;
            }
        });

        //绑定查看
        $(".read").off();
        $(".read").on('click', function () {
            var indexValue = $(this).attr("indexvalue");
            var checkedOrder = QueryVolOwnerApply.checkOrders[indexValue] || {};

            var txCode = checkedOrder.txCode;
            var dealAppNo = checkedOrder.dealAppNo;

            // 份额合并明细详情
            if (CONSTANTS.MERGE_VOL_TXCODE == txCode) {
                QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, QueryVolOwnerApply.queryMergeVolOrderByIdBack);
            } else if (CONSTANTS.TRANS_VOL_TXCODE == txCode) {
                QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, QueryVolOwnerApply.queryTransVolOrderByIdBack);
            }
        });
    },
    queryBankAcctStat: function () {
        $("#unregisterCard").empty();
        var reqparamters = {};

        reqparamters.bankAcct = QueryVolOwnerApply.dtlOrderDtos[0].bankAcct;
        reqparamters.txAcctNo = QueryVolOwnerApply.checkedOrder.txAcctNo;

        var uri = TmsCounterConfig.QUERY_BANK_ACCT_STAT || {};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, QueryVolOwnerApply.queryBankAcctStatCallBack);
    },

    queryBankAcctStatCallBack: function (data) {
        var bodyData = data.body || {};
        var respData = bodyData.batchStatList || [];


        var trList = [];
        trList.push(CommonUtil.formatData(respData.txAcctNo));
        trList.push(CommonUtil.formatData(QueryVolOwnerApply.checkedOrder.custName));
        trList.push(CommonUtil.formatData(QueryVolOwnerApply.dtlOrderDtos[0].bankAcct));
        trList.push(CommonUtil.getMapValue(CONSTANTS.BANKACCT_STATUS_MAP, respData.bankAcctStatus));

        var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
        $("#unregisterCard").append(trAppendHtml);


    },
    queryAssetInfo: function () {
        // 转入银行卡资产信息
        $("#assetRealBody").empty();

        // 查询客户银行卡持仓
        var uri = TmsCounterConfig.QUERY_INTRANSIT_ASSET_URL || {};
        var reqparamters = {};

        reqparamters.cpAcctNo = QueryVolOwnerApply.dtlOrderDtos[0].cpAcctNo;


        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, QueryVolOwnerApply.queryAssetInfoCallBack);
    },

    queryAssetInfoCallBack: function (data) {
        var bodyData = data.body || {};
        var respData = bodyData.batchStatList || [];

        QueryVolOwnerApply.assetDetailList = respData.detailList || [];


        if (QueryVolOwnerApply.assetDetailList.length <= 0) {
            var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
            $("#assetRealBody").append(trHtml);
            return false;

        } else {
            $(QueryVolOwnerApply.assetDetailList).each(function (index, element) {
                var trList = [];
                trList.push('');
                trList.push(CommonUtil.formatData(element.prodCode));
                trList.push(CommonUtil.formatData(element.fundName));
                trList.push(CommonUtil.formatData(element.busiCode));
                trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.formatData(element.bankAcctName));
                trList.push(CommonUtil.formatAmount(element.occurBalance));
                var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                $("#assetRealBody").append(trAppendHtml);
            });
        }
    },
    queryMergeVolOrderByIdBack: function (data) {
        var bodyData = data.body || {};
        QueryVolOwnerApply.appCheckOrder = bodyData.checkOrder || {};
        QueryVolOwnerApply.appCheckDtlOrder = bodyData.checkDtlOrder || [];

        // 客户信息
        BodyView.setCustInfo("queryOwnMergeCustInfoId", QueryVolOwnerApply.appCheckOrder.txAcctNo,
            QueryVolOwnerApply.appCheckOrder.idNo, QueryVolOwnerApply.appCheckOrder.disCode, QueryVolOwnerApply.setMergeCustInfoTable);
        // 转出信息
        BodyView.setTransOutTableView("mergeOutInfo", QueryVolOwnerApply.appCheckDtlOrder, QueryVolOwnerApply.appCheckOrder.disCode);
        // 转入信息
        BodyView.setTransInTableView("mergeInInfo", QueryVolOwnerApply.appCheckOrder);
        // 订单信息
        BodyView.setAppOrderInfoTableView("mergeOrderInfo", QueryVolOwnerApply.appCheckOrder);

        // POPUP
        layer.open({
            title: ['份额合并详情', true],
            type: 1,
            area: ['95%', '90%'],
            skin: 'layui-layer-rim', //加上边框
            btnAlign: 'l',
            content: $('#popupMergeVolInfo')
        });
    },

    setMergeCustInfoTable: function (data) {
        BodyView.setCustInfoTableView("queryOwnMergeCustInfoId", data);
    },

    setTransferCustInfoTable: function (data) {
        BodyView.setCustInfoTableView("queryOwnTransCustInfoId", data);
    },

    setTransInBankInfo: function (data) {
        var body = data.body || {};
        var custBanks = body.custBanks || [];

        var transInBanks = {};
        transInBanks.bankAcct = QueryVolOwnerApply.appCheckOrder.bankAcct;
        transInBanks.bankCode = QueryVolOwnerApply.appCheckOrder.bankCode;

        $(custBanks).each(function (index, element) {
            if (element.cpAcctNo == QueryVolOwnerApply.appCheckOrder.cpAcctNo) {
                transInBanks.bankRegionName = element.bankRegionName;
                return;
            }
        });

        // 转入银行卡
        BodyView.setTransInBankTableView("transInBanks", transInBanks);
    },

    queryTransVolOrderByIdBack: function (data) {
        var bodyData = data.body || {};
        QueryVolOwnerApply.appCheckOrder = bodyData.checkOrder || {};
        QueryVolOwnerApply.appCheckDtlOrder = bodyData.checkDtlOrder || [];
        QueryVolOwnerApply.transInBalDtl = bodyData.custBalDtlList || [];
        var orderFile = bodyData.orderFile || {};// CRM线上资料
        // 转入持仓
        var respData = bodyData.respData;
        QueryVolOwnerApply.transInDisCode = respData.disCode;
        QueryVolOwnerApply.transInBalDtl = respData.custBalDtlList || [];

        OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料

        // 客户信息
        BodyView.setCustInfo("queryOwnTransCustInfoId", QueryVolOwnerApply.appCheckOrder.txAcctNo,
            QueryVolOwnerApply.appCheckOrder.idNo, QueryVolOwnerApply.appCheckOrder.disCode, QueryVolOwnerApply.setTransferCustInfoTable);
        
		// 转出信息
        BodyView.setTransOutTableViewNew("highTransOutCustBals", "transOutCustBals", QueryVolOwnerApply.appCheckDtlOrder, QueryVolOwnerApply.appCheckOrder.disCode,"assetBody");
        // 设置转入银行卡信息
        QueryCustInfo.getCustBankInfos(QueryVolOwnerApply.appCheckOrder.txAcctNo, QueryVolOwnerApply.appCheckOrder.disCode, QueryVolOwnerApply.setTransInBankInfo);
        // 转入银行卡资产信息
        BodyView.setTransInCustBalsTableView("transInCustBals", QueryVolOwnerApply.transInBalDtl, QueryVolOwnerApply.transInDisCode);
        // 订单信息
        BodyView.setAppOrderInfoTableView("transOrderInfo", QueryVolOwnerApply.appCheckOrder);

        // POPUP
        layer.open({
            title: ['份额迁移详情', true],
            type: 1,
            area: ['95%', '90%'],
            skin: 'layui-layer-rim', //加上边框
            btnAlign: 'l',
            content: $('#popupTransVolInfo')
        });

        //如果选择注销银行卡，需要调用资金接口查询在途资产
        var selectCancelCard = QueryVolOwnerApply.appCheckOrder.cancelCard;
        $("#selectCancelCard").val(selectCancelCard);
        if (selectCancelCard == '1') {

            $("#intransAssetDiv").show();

        }
    },
    buildAssetTableView: function (data) {

        QueryVolOwnerApply.detailList = QueryVolOwnerApply.intransitAssetList;

        // 转入银行卡资产信息
        $("#assetBody").empty();
        if (QueryVolOwnerApply.detailList.length <= 0) {
            var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
            $("#assetBody").append(trHtml);
            return false;

        } else {
            $(QueryVolOwnerApply.detailList).each(function (index, element) {
                var trList = [];
                trList.push('');
                trList.push(CommonUtil.formatData(element.prodCode));
                trList.push(CommonUtil.formatData(element.fundName));
                trList.push(CommonUtil.formatData(element.busiCode));
                trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.formatData(element.bankAcctName));
                trList.push(CommonUtil.formatAmount(element.occurBalance));
                var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                $("#assetBody").append(trAppendHtml);
            });
        }
    },
    queryDealOrderInfo: function (data) {
        var bodyData = data.body || {};
        QueryVolOwnerApply.tradeOrders = bodyData.tradeOrders || {};

        QueryVolOwnerApply.dtlOrderDtos = bodyData.dtlOrderDtos || {};

        // 订单详情信息
        BodyView.setTradeOrderInfoTableView("tradeOrderInfo", QueryVolOwnerApply.tradeOrders);

        // 定投合约信息
        BodyView.setScheduleInfoTableView("schedulePlanInfo", bodyData.schedulePlans || {});

        // POPUP
        layer.open({
            title: ['订单详情', true],
            type: 1,
            area: ['95%', '90%'],
            skin: 'layui-layer-rim', //加上边框
            btnAlign: 'l',
            content: $('#popupDealOrderInfo')
        });

        //份额迁移查询销卡状态和在途资产
        var txCode = QueryVolOwnerApply.checkedOrder.txCode;
        var selectCancelCard = QueryVolOwnerApply.checkedOrder.cancelCard;
        if (CONSTANTS.TRANS_VOL_TXCODE == txCode && '1' == selectCancelCard) {
            $("#unregisterCardDiv").show();
            $("#assetBodyDiv").show();
            //查询储蓄罐销卡状态
            QueryVolOwnerApply.queryBankAcctStat();
            //查询在途资产
            QueryVolOwnerApply.queryAssetInfo();
        }
    }

};
