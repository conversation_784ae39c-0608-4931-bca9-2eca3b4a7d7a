/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @description:(客户基金持仓信息)
 * <AUTHOR>
 * @date 2018年5月4日 下午2:10:55
 * @since JDK 1.6
 */
public class CustBalDtlDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -2566033647700834893L;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 份额类型
     */
    private String fundShareClass;
    /**
     * 基金简称
     */
    private String fundAttr;
    /**
     * 基金TA代码
     */
    private String taCode;
    /**
     * 协议类型
     */
    private String protocolType;
    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 银行卡号
     */
    private String bankAcct;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行编码
     */
    private String bankCode;
    /**
     * 总份额
     */
    private BigDecimal balanceVol;
    /**
     * 可用份额
     */
    private BigDecimal availVol;
    /**
     * 冻结份额
     */
    private BigDecimal unconfirmedVol;
    /**
     * 司法冻结份额
     */
    private BigDecimal justFrznVol;
    /**
     * 交易通道：1 好买创新、2 创昱达、3 群济、4 好买储蓄罐、5 好买公募
     */
    private String productChannel;
    
    /**
     * 计划状态 :1-正常；2-暂停；3-终止
     */
    private String scheStatus;

    /**
     * 业务类型
     */
    private String busiCode;
    
    /**
     * 分销机构号
     */
    private String disCode;
    
    public String getDisCode() {
        return disCode;
    }
    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }
    public String getCpAcctNo() {
        return cpAcctNo;
    }
    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }
    public String getFundCode() {
        return fundCode;
    }
    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
    public String getFundAttr() {
        return fundAttr;
    }
    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }
    public String getTaCode() {
        return taCode;
    }
    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }
    public String getProtocolType() {
        return protocolType;
    }
    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }
    public String getProtocolNo() {
        return protocolNo;
    }
    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }
    public String getBankAcct() {
        return bankAcct;
    }
    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }
    public String getBankName() {
        return bankName;
    }
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    public BigDecimal getBalanceVol() {
        return balanceVol;
    }
    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }
    public BigDecimal getAvailVol() {
        return availVol;
    }
    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }
    public BigDecimal getUnconfirmedVol() {
        return unconfirmedVol;
    }
    public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
        this.unconfirmedVol = unconfirmedVol;
    }
    public BigDecimal getJustFrznVol() {
        return justFrznVol;
    }
    public void setJustFrznVol(BigDecimal justFrznVol) {
        this.justFrznVol = justFrznVol;
    }
    public String getProductChannel() {
        return productChannel;
    }
    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }
    public String getFundShareClass() {
        return fundShareClass;
    }
    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }
    public String getBankCode() {
        return bankCode;
    }
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }
    public String getScheStatus() {
        return scheStatus;
    }
    public void setScheStatus(String scheStatus) {
        this.scheStatus = scheStatus;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }
}

