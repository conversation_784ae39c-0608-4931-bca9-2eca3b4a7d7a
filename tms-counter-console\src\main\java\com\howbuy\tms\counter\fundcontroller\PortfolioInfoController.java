package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.interlayer.product.enums.RuleIdEnum;
import com.howbuy.interlayer.product.enums.RuleProductTestEnum;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.interlayer.product.model.adviser.AdviserProductRuleCfgModel;
import com.howbuy.interlayer.product.model.portfolio.PortfolioProductDetailModel;
import com.howbuy.interlayer.product.model.portfolio.PortfolioProductFullModel;
import com.howbuy.interlayer.product.service.AdviserProdInfoService;
import com.howbuy.interlayer.product.service.AdviserProductRuleCfgService;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.counter.dto.QueryAgreementDto;
import com.howbuy.tms.counter.fundservice.trade.TmsAgreementCounterService;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.config.FundRegionEnumConfig;
import com.howbuy.tms.counter.dto.CounterPortfolioProductDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.AdviserFundBalanceModel;
import com.howbuy.tms.robot.orders.facade.query.adviser.queryriskproductbylastofanswer.QueryRiskProductByLastOfAnswerFacade;
import com.howbuy.tms.robot.orders.facade.query.adviser.queryriskproductbylastofanswer.QueryRiskProductByLastOfAnswerRequest;
import com.howbuy.tms.robot.orders.facade.query.adviser.queryriskproductbylastofanswer.QueryRiskProductByLastOfAnswerResponse;
import com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolFacade;
import com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolRequest;
import com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/11/29 11:22
 * @since JDK 1.8
 */
@Controller
public class PortfolioInfoController extends AbstractController {
    private static Logger logger = LogManager.getLogger(PortfolioInfoController.class);

    @Resource
    private AdviserProdInfoService adviserProdInfoService;

    @Resource
    private TmsAgreementCounterService tmsAgreementCounterService;

    @Resource
    private AdviserProductRuleCfgService adviserProductRuleCfgService;

    @Resource
    private QueryRiskProductByLastOfAnswerFacade queryRiskProductByLastOfAnswerFacade;

    @Resource
    private CustProtocolFacade custProtocolFacade;

    /**
     * @api {GET} /tmscounter/queryproductinfo.htm queryProductInfo
     * @apiVersion 1.0.0
     * @apiGroup PortfolioInfoController
     * @apiName 判断产品是 投顾 还是 单基金
     * @apiParam (请求参数) {String} custInfoForm 客户信息
     * @apiParam (请求参数) {String} productCode  产品信息
     * @apiParamExample 请求参数示例
     * productCode=jXkwXnfV&custInfoForm=NfEQQ1Rk
     * @apiSuccess (响应结果) {String} code 响应码
     * @apiSuccess (响应结果) {String} desc 响应描述
     * @apiSuccess (响应结果) {Object} body *      响应体
     * @apiSuccessExample 响应结果示例
     * {"code":"ki","body":{},"desc":"XNO2J7Od"}
     */
    @RequestMapping("tmscounter/queryproductinfo.htm")
    public void queryProductInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        CounterPortfolioProductDto dto = new CounterPortfolioProductDto();

        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = getCustInfoDto(custInfoForm);

        String productCode = request.getParameter("productCode");
        boolean onlySearchProduct = StringUtils.equals(YesOrNoEnum.YES.getCode(), request.getParameter("onlySearchProduct"));
        DisInfoDto disInfoDto = new DisInfoDto();
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);

        FundInfoAndNavBean fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(productCode, workDay);
        if (fundInfoAndNavBean != null) {
            dto.setAdviserFlag(false);
            dto.setFundCode(fundInfoAndNavBean.getFundCode());
            dto.setProductCode(fundInfoAndNavBean.getFundCode());
            dto.setProductName(fundInfoAndNavBean.getFundAttr());
            dto.setFundType(fundInfoAndNavBean.getFundType());
            dto.setFundShareClass(fundInfoAndNavBean.getFundShareClass());
            dto.setRiskLevel(fundInfoAndNavBean.getFundRiskLevel());
            dto.setFundStat(fundInfoAndNavBean.getFundStat());
            dto.setTaCode(fundInfoAndNavBean.getTaCode());
            dto.setEndTm(fundInfoAndNavBean.getEndTm());
            dto.setJointOpenDayRegion(getJointOpenDayRegion(fundInfoAndNavBean));

            QueryAgreementDto queryAgreementDto = tmsAgreementCounterService.queryAgreement(custInfoDto, null, Arrays.asList(new String[]{dto.getFundCode()}));
            dto.setQueryAgreementDto(queryAgreementDto);
        } else {
            PortfolioProductFullModel portfolioProdInfoDto = adviserProdInfoService.queryAdviserProdInfoByProductCodeV2(productCode);
            if (portfolioProdInfoDto != null) {
                dto.setAdviserFlag(true);
                dto.setProductCode(portfolioProdInfoDto.getProductCode());
                dto.setProductName(portfolioProdInfoDto.getProductName());
                dto.setRiskLevel(portfolioProdInfoDto.getRistLevel());
                dto.setIsBuyOpen(portfolioProdInfoDto.getIsBuyOpen());
                dto.setIsSoldOpen(portfolioProdInfoDto.getIsSoldOpen());
                String partnerCode = portfolioProdInfoDto.getPartnerCode();
                dto.setPartnerCode(partnerCode);

                List<PortfolioProductDetailModel> portfolioProductDtls = portfolioProdInfoDto.getPortfolioProductDtls();
                if(!CollectionUtils.isEmpty(portfolioProductDtls)){
                    List<String> fundCodes = portfolioProductDtls.stream().map(PortfolioProductDetailModel::getDtlProductCode).distinct().collect(Collectors.toList());
                    QueryAgreementDto queryAgreementDto = tmsAgreementCounterService.queryAgreement(custInfoDto, productCode, fundCodes);
                    dto.setQueryAgreementDto(queryAgreementDto);
                }

                // 买入时需要展示  卖出时不用展示，加个字段不做查询
                if (!onlySearchProduct) {
                    AdviserProductRuleCfgModel productRuleCfgModel = adviserProductRuleCfgService.getByRuleIdAndPartnerCode(RuleIdEnum.RULE_PRODUCT_TEST.getCode(), partnerCode);
                    boolean hasQuestFlag = productRuleCfgModel != null && !StringUtils.equals(productRuleCfgModel.getRuleFlag(), RuleProductTestEnum.UNNEED_TEST.getCode());
                    dto.setAdviserQuestRuleFlag(hasQuestFlag);
                    if (hasQuestFlag) {
                        QueryRiskProductByLastOfAnswerRequest answerRequest = new QueryRiskProductByLastOfAnswerRequest();
                        answerRequest.setPartnerCode(partnerCode);
                        answerRequest.setHboneNo(custInfoDto.getHboneNo());
                        answerRequest.setDisCode(custInfoDto.getDisCode());
                        QueryRiskProductByLastOfAnswerResponse answerResponse = (QueryRiskProductByLastOfAnswerResponse) TmsFacadeUtil.execute(queryRiskProductByLastOfAnswerFacade, answerRequest, disInfoDto);
                        if (StringUtils.isNotBlank(answerResponse.getAnswer())) {
                            dto.setQuestionAnswer(String.join("", answerResponse.getAnswer().split(",")));
                        }
                    }
                }

            }
        }

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(dto);
        WebUtil.write(response, tmsCounterResult);
    }

    private static CustInfoDto getCustInfoDto(String custInfoForm) {
        CustInfoDto custInfoDto = new CustInfoDto();
        if(!StringUtil.isEmpty(custInfoForm)){
            custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        }
        return custInfoDto;
    }

    @RequestMapping("tmscounter/queryselfproductinfo.htm")
    public void querySelfProductInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        CounterPortfolioProductDto dto = new CounterPortfolioProductDto();

        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        String productCode = request.getParameter("productCode");

        CustProtocolResponse custProtocolResponse = queryCustProtocolResponse(productCode, custInfoDto);
        if (custProtocolResponse != null) {
            dto.setProductCode(custProtocolResponse.getProductCode());
            dto.setProductName(custProtocolResponse.getProductName());
            dto.setRiskLevel(custProtocolResponse.getRiskLevel());
            dto.setProtocolNo(custProtocolResponse.getProtocolNo());
            dto.setProtocolType(custProtocolResponse.getProtocolType());
            dto.setProtocolName(custProtocolResponse.getProtocolName());
        }
        dto.setSelfFlag(true);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(dto);
        WebUtil.write(response, tmsCounterResult);
    }

    private CustProtocolResponse queryCustProtocolResponse(String productCode, CustInfoDto custInfoDto) {
        CustProtocolResponse custProtocolResponse = getCustProtocolResponse(productCode, null, null, custInfoDto);
        if(custProtocolResponse != null && !StringUtil.isEmpty(custProtocolResponse.getProtocolNo())){
            return custProtocolResponse;
        }
        custProtocolResponse = getCustProtocolResponse(null, productCode, null, custInfoDto);
        if(custProtocolResponse != null && !StringUtil.isEmpty(custProtocolResponse.getProtocolNo())){
            return custProtocolResponse;
        }
        custProtocolResponse = getCustProtocolResponse(null, null, productCode, custInfoDto);
        return custProtocolResponse;
    }

    private CustProtocolResponse getCustProtocolResponse(String cpId, String productCode, String protocolNo, CustInfoDto custInfoDto) {
        CustProtocolRequest custProtocolRequest = new CustProtocolRequest();
        custProtocolRequest.setCpId(cpId);
        custProtocolRequest.setProductCode(productCode);
        custProtocolRequest.setProtocolNo(protocolNo);
        custProtocolRequest.setTxAcctNo(custInfoDto.getHboneNo());
        custProtocolRequest.setDisCode(custInfoDto.getDisCode());
        CustProtocolResponse custProtocolResponse = custProtocolFacade.execute(custProtocolRequest);
        return custProtocolResponse;
    }

    /**
     * 获取日经描述信息
     * @param fundInfo
     * @return java.lang.String
     * @author: junkai.du
     * @date: 2024/4/26 16:50
     * @since JDK 1.8
     */
    private String getJointOpenDayRegion(FundInfoAndNavBean fundInfo) {
        if (StringUtils.isNotBlank(fundInfo.getJointOpenDayRegion()) && StringUtils.isNotBlank(fundInfo.getEndTm()) && fundInfo.getEndTm().compareTo("150000") < 0){
            String desc = String.format("因%s投资范围内包含%s市场产品，交易日%s-15:00暂停交易受理。若继续下单会被TA判失败。", fundInfo.getFundName(), FundRegionEnumConfig.getDescByCode(fundInfo.getJointOpenDayRegion()), fundInfo.getEndTm().replaceAll("(\\d{2})(\\d{2})(\\d{2})", "$1:$2"));
            return desc;
        }
        return "";
    }

}
