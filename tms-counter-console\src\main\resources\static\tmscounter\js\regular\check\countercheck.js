/**
*零售柜台审核
*<AUTHOR>
*@date 2017-09-18 15:23
*/
$(function(){
	Init.init();
	RegularCounterCheck.init();
	
	RegularCounterCheck.custInfo = {};
	RegularCounterCheck.checkOrders = [];
	RegularCounterCheck.checkedOrder = {};
	//通过
	RegularCounterCheck.Succ = '1';
	//驳回
	RegularCounterCheck.Faild = '3';
	
	RegularCounterCheck.Abolish = '4';
	
	var selectTxCodeHtml = '<option value="">全部</option>';
	$.each(CONSTANTS.COUNTER_FUND_TXCODE_MAP,function(name,value){
		selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
	});
	$("#selectTxCode").empty();
	$("#selectTxCode").html(selectTxCodeHtml);
	
	//查询待审核订单
	RegularCounterCheck.queryOrderInfo();
});
 var RegularCounterCheck = {
	init:function(){
		$("#queryBtn").on('click',function(){
			RegularCounterCheck.queryOrderInfo();
		});
	},
	
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_REGULAR_CHECK_ORDER_URL  ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		reqparamters.checkFlag = 0; //只查未审核
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);;
		CommonUtil.ajaxPaging(uri,paramters, RegularCounterCheck.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){

		var bodyData = data;
		RegularCounterCheck.checkOrders = bodyData.counterOrderList || [];
		$("#rsList").empty();
		if(RegularCounterCheck.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无待审核记录</td></tr>';
			$("#rsList").append(trHtml);
		}
		
		var staticData = bodyData.counterQueryOrderRespDto || {};
		$("#staticId").html("当页小计：申请笔数【"+RegularCounterCheck.checkOrders.length+"】申请金额【"+CommonUtil.formatAmount(staticData.pageAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.pageAppVol)+"】 合计：申请笔数【"+staticData.totalCount+"】申请金额【"+CommonUtil.formatAmount(staticData.totalAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.totalAppVol)+"】");
		
		var i = 1;
		$(RegularCounterCheck.checkOrders).each(function(index,element){
			var trList = [];
			trList.push(i++);
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custName));
			if(element.invstType == '0'){//属于机构
				trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
			}
			if(element.invstType == '1'){
				trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
			}
			if(element.invstType == '2'){
				trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
			}
			trList.push(CommonUtil.formatData(element.idNo,'--'));  
			trList.push(CommonUtil.formatData(element.productCode));
			trList.push(CommonUtil.formatData(element.productName));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, element.txCode, ''));
			if(element.appAmt > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
			}else {
				trList.push('--');
			}
			if(element.appVol > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
			}else {
				trList.push('--');
			}
			trList.push(CommonUtil.formatData(element.appDt));
			trList.push(CommonUtil.formatData(element.appTm));
			trList.push(CommonUtil.formatData(element.creator,''));
			trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = '+index+'>复核</a>');
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
		});
		
		//绑定审核
		$(".reCheck").off();
		$(".reCheck").on('click',function(){			
			var indexValue = $(this).attr("indexvalue");
			var checkOrder = RegularCounterCheck.checkOrders[indexValue] || {};
			RegularCounterCheck.checkedOrder = checkOrder;
			var txCode = RegularCounterCheck.checkedOrder.txCode;
			var dealAppNo = RegularCounterCheck.checkedOrder.dealAppNo;
			var txAcctNo = RegularCounterCheck.checkedOrder.txAcctNo;
			var disCode = RegularCounterCheck.checkedOrder.disCode;
			var idNo = RegularCounterCheck.checkedOrder.idNo;
			var param = "checkId="+dealAppNo+"&custNo="+txAcctNo+"&disCode="+disCode+"&idNo="+idNo;
			if('Z910042' == txCode || 'Z910058' == txCode){
				window.open("checkbuy.html?"+param,"_blank");
				return;
			} else if('Z910043' == txCode){
				window.open("checksell.html?"+param,"_blank");
				return;
			} else if('Z910044' == txCode){
				window.open("checkmodifydiv.html?"+param,"_blank");
				return;
			} else if('Z910045' == txCode || 'Z910046' == txCode){
				window.open("checkcancel.html?"+param,"_blank");
				return;
			}else if('Z910057' == txCode){
				window.open("checkexchange.html?"+param,"_blank");
				return;
			}
		});
	}
};
