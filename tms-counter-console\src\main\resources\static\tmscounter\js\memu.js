/**
 * 菜单管理
 * <AUTHOR>
 * @date 2017-09-15 10:58
 * 
 */

/***
 * 菜单管理
 */
var MenuMange = {};

/**
 * 订单列表
 * -1 表示顶层菜单
 */
MenuMange.menuItmsList =  [
	{id: 14, parentId: -1, isParent: true, isFirst: true, title: " 高端业务"},
	{id: 0, parentId: 14, isParent: true, isFirst: false, title: " 交易下单"},
	{id: 1, parentId: 0, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/subscription/list.html", title: "无纸化柜台"},
	{id: 2, parentId: 0, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/subscription/stat.html", title: "无纸化预约单统计"},
	{id: 3, parentId: 0, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/trade/buy.html", title: "认申购"},
	{id: 4, parentId: 0, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/trade/sell.html", title: "赎回"},
	{id: 5, parentId: 0, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/trade/cancel.html", title: "撤单"},
	{id: 6, parentId: 0, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/trade/modifydiv.html", title: "修改分红方式"},
	
	{id: 15, parentId: -1, isParent: true, isFirst: true, title: " 零售业务"},
	{id: 16, parentId: 15, isParent: true, isFirst: false, title: " 投资交易类"},
	{id: 17, parentId: 16, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/buy.html", title: "认申购"},
	{id: 18, parentId: 16, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/sell.html", title: "赎回"},
	{id: 19, parentId: 16, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/exchange.html", title: "基金转换"},
	{id: 20, parentId: 16, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/modifydiv.html", title: "修改分红方式"},
	{id: 21, parentId: 16, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/cancel.html", title: "交易撤单"},
	{id: 22, parentId: 16, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/selfsell.html", title: "自建组合赎回"},
	
	{id: 32, parentId: -1, isParent: true, isFirst: true, title: " 特殊业务"},
	{id: 33, parentId: 32, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/mergevol.html", title: "份额合并"},
	{id: 34, parentId: 32, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/transvol.html", title: "份额迁移"},
    {id: 35, parentId: 32, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/onlinetransfervol.html", title: "线上份额迁移"},
	{id: 36, parentId: 32, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/trade/appratetchange.html", title: "人工干预折扣率"},

    {id: 151, parentId: -1, isParent: true, isFirst: true, title: " 定期业务"},
    {id: 152, parentId: 151, isParent: true, isFirst: false, title: " 收益凭证类"},
    {id: 153, parentId: 152, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/regular/trade/buy.html", title: "认申购"},
    {id: 154, parentId: 152, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/regular/trade/cancel.html", title: "交易撤单"},



	{id: 22, parentId: -1, isParent: true, isFirst: true, title: " 业务审核"},
	{id: 23, parentId: 22, isParent: true, isFirst: false, title: " 交易审核(零售)"},
	{id: 24, parentId: 23, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/check/countercheck.html", title: "柜台交易复核"},
	{id: 7, parentId: 22, isParent: true, isFirst: false, title: " 交易审核(高端)"},
	{id: 8, parentId: 7, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/check/countercheck.html", title: "柜台交易复核"},
    {id: 155, parentId: 22, isParent: true, isFirst: false, title: "交易审核(定期)"},
    {id: 156, parentId: 155, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/regular/check/countercheck.html", title: "柜台交易复核"},


	{id: 35, parentId: 22, isParent: true, isFirst: false, title: " 特殊业务审核"},
	{id: 36, parentId: 35, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/check/countervolchecklist.html", title: "份额合并/迁移复核"},
	
	{id: 25, parentId: -1, isParent: true, isFirst: true, title: " 业务查询"},
	{id: 26, parentId: 25, isParent: true, isFirst: true, title: "交易类业务查询(零售)"},
	{id: 27, parentId: 26, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/apply/querytradeapply.html", title: "交易申请查询"},
	{id: 28, parentId: 26, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/apply/queryowertradeapply.html", title: "我的交易申请查询"},

    {id: 157, parentId: 25, isParent: true, isFirst: true, title: "交易类业务查询(定期)"},
    {id: 158, parentId: 157, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/regular/apply/querytradeapply.html", title: "交易申请查询"},
    {id: 159, parentId: 157, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/regular/apply/queryowertradeapply.html", title: "我的交易申请查询"},

	
	{id: 9, parentId: 25, isParent: false, isFirst: true, title: "交易类业务查询(高端)"},
	{id: 10, parentId: 9, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/query/querycountertrade.html", title: "柜台交易查询"},
	{id: 901, parentId: 9, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/query/querycounterapp.html", title: "交易申请查询"},
	{id: 902, parentId: 9, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/query/querymycounterapp.html", title: "我的交易申请查询"},
	{id: 903, parentId: 9, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/query/querycustassert.html", title: "客户资产查询"},
	{id: 11, parentId: 9, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/high/query/querycounterreport.html", title: "柜台交易汇总报表"},
	
	{id: 37, parentId: 25, isParent: false, isFirst: true, title: " 特殊业务查询"},
	{id: 38, parentId: 37, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/apply/queryvolapply.html", title: "交易申请查询"},
	{id: 39, parentId: 37, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/fund/apply/queryvolownerapply.html", title: "我的交易申请查询"},
	
	{id: 29, parentId: -1, isParent: true, isFirst: true, title: " 业务处理"},
	{id: 30, parentId: 29, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/other/fundcounterend.html", title: "柜台收市(公募)"},
	{id: 31, parentId: 29, baseUrl: TmsCounterConfig.STATIC_BASE_URL, url: "html/other/highcounterend.html", title: "柜台收市(高端)"},
	
	{id: 12, parentId: -1, isParent: true, isFirst: true, title: " 资产证明审核"},
	{id: 13, parentId: 12, baseUrl: TmsCounterConfig.CENTER_PLATFORM_URL, url: "asset/index.htm", title: "客户资产证明审核"}

];


/**
 * 权限集合
 */
MenuMange.roleList = [
   {
	   "role": "1",
	   "name":"公募运营",
	   "group": ["*"]
   },
   {
	   "role": "2",
	   "name":"私募运营",
	   "group": [14,0,1,2,12,13]
   }
 ];

/**
 * 生成菜单列表
 * @param role 角色
 * @param operatorNo 操作员
 */
MenuMange.makeMenuItemList = function(role,operatorNo){
	var group = new Array();
	if(CommonUtil.isEmpty(role)){
		//角色为空生成所有菜单
		group = ["*"];
	}else{
		group = MenuMange.getGroupByRole(role);
	}
	// console.log(MenuMange.menuItmsList);
	var itemList = MenuMange.getTreeData(-1, MenuMange.menuItmsList, group,operatorNo);
	var itemHtml = itemList.join('');
	//置空
	MenuMange.menueItms = [];
	
	return itemHtml;
	
};
/**
 * 菜单是否存在组中
 * @param itemId
 * @param group 所属组
 */
MenuMange.exitInGroup = function(itemId,group){
	var flag = false;
	$(group).each(function(index,element){
		if( "*" == element){
			flag = true;
			return false;
		}else if(itemId == element){
			flag = true;
			return false;
		}
	});
	
	return flag;
},
/**
 * 根据权限获取对应的组
 * @param role 权限
 */
MenuMange.getGroupByRole = function(role){
	var roleGroup = [];
	$(MenuMange.roleList).each(function(index,element){
		if(role == element.role){
			roleGroup = MenuMange.makeRoleGroup(roleGroup,element.group || []);
		}
	});
	
	return roleGroup;
};
/**
 * 生成权限数组
 */
MenuMange.makeRoleGroup = function(roleGroup,group){
	var rstGroup = roleGroup.concat(group);
	return rstGroup;
};
MenuMange.menueItms = [];
/**
 * 获取菜单列表数据
 */
MenuMange.getTreeData = function(parentId,arr,group,operatorNo){
	var childArr = MenuMange.getChildList(parentId,arr);
	$(childArr).each(function(index,element){
		if(MenuMange.exitInGroup(element.id,group)){
			if(element.isFirst){
				MenuMange.menueItms.push('<dl id="menu-article">');
				MenuMange.menueItms.push('<dt><i class="Hui-iconfont">&#xe616;</i>'+element.title+'<i class="Hui-iconfont menu_dropdown-arrow">&#xe6d5;</i></dt>') ;
				MenuMange.menueItms.push('<dd><ul>');
				MenuMange.menueItms.concat( MenuMange.getTreeData(element.id,arr,group,operatorNo));
				MenuMange.menueItms.push('</ul></dd>');
				
				MenuMange.menueItms.push('</dl>');
			}else if(element.isParent){
				MenuMange.menueItms.push('<dt><i class="Hui-iconfont">&#xe616;</i>'+element.title+'<i class="Hui-iconfont menu_dropdown-arrow">&#xe6d5;</i></dt>') ;
				MenuMange.menueItms.push('<dd><ul>');
				MenuMange.menueItms.concat(MenuMange.getTreeData(element.id,arr,group,operatorNo));
				MenuMange.menueItms.push('</ul></dd>');
			}else{
				MenuMange.menueItms.push('<li>');
				MenuMange.menueItms.push('<a _href="'+element.baseUrl +''+ element.url+'?operatorNo='+operatorNo+'" data-title="'+element.title+'" href="javascript:void(0)">'+element.title+'</a>');
				MenuMange.menueItms.concat(MenuMange.getTreeData(element.id,arr,group,operatorNo));
				MenuMange.menueItms.push('</li>');
			}
		}
		
	});
	
	return MenuMange.menueItms;
};

/**
 * 根据父id获取子节点集合
 */
MenuMange.getChildList = function(parentId,arr){
	var childList = [];
	$(arr).each(function(index,element){
		if(element.parentId == parentId){
			childList.push(element);
		}
	});
	
	return childList;
};

