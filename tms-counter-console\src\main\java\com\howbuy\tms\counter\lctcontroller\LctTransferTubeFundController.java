/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.lctcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.outerservice.lctonline.queryfundbaldtl.QueryFundBalDtlContext;
import com.howbuy.tms.common.outerservice.lctonline.queryfundbaldtl.QueryFundBalDtlOuterService;
import com.howbuy.tms.common.outerservice.lctonline.queryfundbaldtl.QueryFundBalDtlResult;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.HttpUtil;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 
 * @description:(转托管转入或转出)
 * <AUTHOR>
 * @date 2018年10月9日 下午3:20:24
 * @since JDK 1.6
 */
@Controller
public class LctTransferTubeFundController extends AbstractController {
    private static Logger logger = LogManager.getLogger(LctTransferTubeFundController.class);


    @Autowired
    private QueryFundBalDtlOuterService queryFundBalDtlOuterService;


    /**
     * 
     * transferTubeOutConfirm:(转托管转出确认)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月9日 下午3:23:24
     */
    @RequestMapping("/tmscounter/lct/lcttransfertubeoutconfirm.htm")
    public ModelAndView transferTubeOutConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        String transfertubeOutCmd = request.getParameter("transfertubeOutForm");
        String transOutVolCmd = request.getParameter("transOutVolCmd");
        String custInfoForm = request.getParameter("custInfoForm");
        String fundInfoForm = request.getParameter("fundInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.debug("TransferTubeFundController|transferTubeOutConfirm|transfertubeOutForm:{},transOutVolCmd:{}, custInfoForm:{},fundInfoForm:{},transactorInfoForm:{}", 
                transfertubeOutCmd, transOutVolCmd, custInfoForm, fundInfoForm, transactorInfoForm);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        CounterTransferTubeReqDto transferTubeOutReqDto = JSON.parseObject(transfertubeOutCmd, CounterTransferTubeReqDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        FundInfoAndNavDto fundInfo = JSON.parseObject(fundInfoForm, FundInfoAndNavDto.class);
        
        List<TransferTubeOrderReqDto> outDtoList = JSON.parseArray(transOutVolCmd, TransferTubeOrderReqDto.class);
        if(CollectionUtils.isEmpty(outDtoList)){
            throw new TmsCounterException(TmsCounterResultEnum.TRANSFER_TUBE_OUT_ORDER_NOT_SELECTED);
        }
        transferTubeOutReqDto.setTransferTubeDetailList(outDtoList);

        transferTubeOutReqDto.setDealAppNo(dealAppNo);
        // 1128-转托管转出申请
        transferTubeOutReqDto.setmBusiCode(BusinessCodeEnum.TRANS_MANAGE_OUT_APP.getMCode());
//        transferTubeOutReqDto.setTransferTubeBusiType(transferTubeOutReqDto.getTransferTubeBusiType());
        // 对方销售人代码
//        transferTubeOutReqDto.settSellerCode(transferTubeOutReqDto.gettSellerCode());
//        transferTubeOutReqDto.settSellerTxAcctNo(transferTubeOutReqDto.gettSellerTxAcctNo());
//        transferTubeOutReqDto.settOutletCode(transferTubeOutReqDto.gettOutletCode());
//        transferTubeOutReqDto.setAppDt(transferTubeOutReqDto.getAppDt());
//        transferTubeOutReqDto.setAppTm(transferTubeOutReqDto.getAppTm());
        
        // 转出基金
        transferTubeOutReqDto.setFundCode(fundInfo.getFundCode());
        transferTubeOutReqDto.setFundName(fundInfo.getFundAttr());
        transferTubeOutReqDto.setFundShareClass(fundInfo.getFundShareClass());
        transferTubeOutReqDto.setProductClass(fundInfo.getProductClass());
        transferTubeOutReqDto.setTaCode(fundInfo.getTaCode());

        // 转出客户信息
        transferTubeOutReqDto.setCustName(custInfoDto.getCustName());
        transferTubeOutReqDto.setTxAcctNo(custInfoDto.getCustNo());
        transferTubeOutReqDto.setDisCode(custInfoDto.getDisCode());
        transferTubeOutReqDto.setIdNo(custInfoDto.getIdNo());
        transferTubeOutReqDto.setIdType(custInfoDto.getIdType());
        transferTubeOutReqDto.setInvstType(custInfoDto.getInvstType());

        transferTubeOutReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        transferTubeOutReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        transferTubeOutReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        
        // 柜台操作经办信息
        CommonUtil.setCommonOperInfo(operatorInfoCmd, transferTubeOutReqDto);
        transferTubeOutReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        transferTubeOutReqDto.setConsCode(transactorInfoDto.getConsCode());
        transferTubeOutReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        transferTubeOutReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        transferTubeOutReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        transferTubeOutReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        transferTubeOutReqDto.setProductClass(ProductClassEnum.LCT.getCode());
        
        
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        CounterTransferTubeRespDto responseDto = tmsFundCounterService.counterLctTransferTube(transferTubeOutReqDto, disInfoDto);
        
        TmsCounterResult rst = null;
        if (responseDto != null) {
            rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
        } else {
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
        }

        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/lct/lctqueryTransOutFundCustHodlInfo.htm")
    public ModelAndView lctqueryTransOutFundCustHodlInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String idNo = request.getParameter("idNo");
        String custNo = request.getParameter("custNo");
        String cpAcctNo = request.getParameter("cpAcctNo");
        String txAcctNo = custNo;
        if(StringUtils.isEmpty(txAcctNo) && StringUtils.isNotEmpty(idNo)){
            String operIp = HttpUtil.getIpAddr(request);
            txAcctNo = getCustNo(idNo, operIp);
        }

        QueryFundBalDtlContext queryFundBalDtlContext = new QueryFundBalDtlContext();
        queryFundBalDtlContext.setFundCode(fundCode);
        queryFundBalDtlContext.setCustNo(txAcctNo);
        queryFundBalDtlContext.setCpAcctNo(cpAcctNo);

        QueryFundBalDtlResult queryFundBalDtlResult =  queryFundBalDtlOuterService.queryFundBalDtl(queryFundBalDtlContext);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(queryFundBalDtlResult.getBalDtls());
        WebUtil.write(response, rst);
        return null;
    }

}
