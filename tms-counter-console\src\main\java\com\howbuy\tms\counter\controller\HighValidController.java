/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;


import com.howbuy.interlayer.product.enums.InvstTypeEnum;
import com.howbuy.tms.common.enums.busi.CollectProtocolMethodEnum;
import com.howbuy.tms.common.enums.busi.SignFlagEnum;
import com.howbuy.tms.common.enums.database.CustRiskLevelEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.bean.DisAcTxAcctBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustrisksurvey.QueryCustRiskSurveyOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustrisksurvey.QueryCustRiskSurveyResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.aspect.BusinessAspect;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description:(TODO 请在此添加描述) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年11月13日 下午4:08:52
 * @since JDK 1.6
 */
@Controller
public class HighValidController {
    
    private Logger logger = LogManager.getLogger(HighValidController.class);
    
    @Autowired
    private QueryCustRiskSurveyOuterService queryCustRiskSurveyOuterService;
    
    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    
    @Autowired
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    
    @Autowired
    private QueryCustInfoOuterService queryCustInfoOuterService;
    
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    private final String FUND_RISK_LEAST = "0";

    /**
     * 
     * validRisk:(风险等级校验)
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2017年11月13日 下午2:24:42
     */
    @RequestMapping("/tmscounter/high/validrisk.htm")
    public void validRisk(HttpServletRequest request, HttpServletResponse response) throws Exception{
         
         String fundCode = request.getParameter("fundCode");
         String custNo =request.getParameter("custNo");
        
         //查询客户信息
         List<CustInfoDto> custInfoList =   getCustInfo(custNo, TmsCounterConstant.HOWBUY_DISCODE, TmsCounterConstant.HOWBUY_DISCODE, TmsCounterConstant.VERSION_TYPE_HIGH);
         //查询产品信息
         HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(fundCode);
         if(highProductBaseBean == null){
             throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
         }
         if(CollectionUtils.isEmpty(custInfoList) ){
            throw new TmsCounterException(TmsCounterResultEnum.CUST_NOT_EXIST);
         }
         
         CustInfoDto custInfoDto = custInfoList.get(0);
         if(!SignFlagEnum.DONE.getCode().equals(custInfoDto.getSignFlag())){
             throw new TmsCounterException(TmsCounterResultEnum.NOT_SIGN_FLAG);
         }
         
         //风险匹配标识
         boolean mathRiskFlag = true;
         //非专业投资者校验风险
         if(!InvstTypeEnum.INDI.getCode().equals(custInfoDto.getInvestorType())){
             
             if(StringUtils.isEmpty(custInfoDto.getCustRiskLevel())){
                 throw new TmsCounterException(TmsCounterResultEnum.NO_RISK_TEST);
             }else if(custInfoDto.isOverdue()){
                 throw new TmsCounterException(TmsCounterResultEnum.OVER_RISK_TEST);
             }
             
            //风险匹配标识
            mathRiskFlag =  compareRisk(custInfoDto.getCustRiskLevel(),highProductBaseBean.getFundRiskLevel());
         }
         //1-默认回储蓄罐；0-不默认回储蓄罐
         String isDefautCxg = "0";
         if(StringUtils.isEmpty(custInfoDto.getCollectProtocolMethod()) || CollectProtocolMethodEnum.DEFAULT_CXG.getCode().equals(custInfoDto.getCollectProtocolMethod())){
             isDefautCxg = "1";
         }
         
         Map<String,Object> rstBody = new HashMap<String,Object>(16);
         rstBody.put("mathRiskFlag", mathRiskFlag);
         rstBody.put("isDefautCxg", isDefautCxg);
         TmsCounterResult rs = new TmsCounterResult(TmsCounterResultEnum.SUCC);
         rs.setBody(rstBody);
         WebUtil.write(response, rs);
         
   }
    
    
     /**
     * 构造客户信息
     * 
     * @param txAcctNo
     * @param disCode
     * @return
     * @throws Exception
     */
    private List<CustInfoDto> getCustInfo(String txAcctNo, String disCode, String selectDisCode,String versionType) throws Exception {
        List<CustInfoDto> custInfoDtoList = new ArrayList<CustInfoDto>();
        if (!StringUtils.isEmpty(txAcctNo)) {
            if (StringUtils.isEmpty(disCode)) {
                disCode = "HB000A001";
            }
            // 查询客户信息
            QueryCustInfoAndTxAcctForCounterResult queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);
            QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
            queryAllCustInfoContext.setDisCode(disCode);
            queryAllCustInfoContext.setTxAcctNo(txAcctNo);
            QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);

            CustInfoBean custInfoBean = null;
            if (queryAllCustInfoResult != null) {
                custInfoBean = queryAllCustInfoResult.getCustInfo();
            }

            // 查询客户风险等级
            QueryCustRiskSurveyResult queryCustRiskSurveyResult = null;
            if(custInfoBean != null){
                if (TmsCounterConstant.VERSION_TYPE_GONGMU.equals(versionType) || InvstTypeEnum.INST.getCode().equals(custInfoBean.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(custInfoBean.getInvstType())) {
                    queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurvey(txAcctNo, TmsCounterConstant.VERSION_TYPE_GONGMU, disCode);
                }else{
                    queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurvey(txAcctNo, TmsCounterConstant.VERSION_TYPE_HIGH, disCode);
                }
            }
            BusinessAspect.setTradeCommomParams(disCode, null);
            QueryCustInfoResult  queryCustInfoResult =  queryCustInfoOuterService.queryCustInfoPlaintext(txAcctNo);
            String collectProtocolMethod = null;
            if(queryCustInfoResult != null){
                collectProtocolMethod  =  queryCustInfoResult.getCollectProtocolMethod();
            }
            
            if (queryCustInfoAndTxAcctForCounterResult != null) {
                List<DisAcTxAcctBean> disAcTxAcctBeanList = queryCustInfoAndTxAcctForCounterResult.getDisAcTxAcctBeanList();
                if (!CollectionUtils.isEmpty(disAcTxAcctBeanList)) {
                    CustInfoDto custInfoDto = null;
                    for (DisAcTxAcctBean disAcTxAcctBean : disAcTxAcctBeanList) {
                        custInfoDto = new CustInfoDto();
                        BeanUtils.copyProperties(queryCustInfoAndTxAcctForCounterResult, custInfoDto);
                        custInfoDto.setCollectProtocolMethod(collectProtocolMethod); 
                        if (custInfoBean != null) {
                            // 投资者类型
                            custInfoDto.setInvestorType(custInfoBean.getQualificationType());
                            // 客户类型
                            custInfoDto.setInvstType(custInfoBean.getInvstType());
                        }

                        custInfoDto.setDisCode(disAcTxAcctBean.getDisCode());
                        custInfoDtoList.add(custInfoDto);

                        if (queryCustRiskSurveyResult != null) {
                            custInfoDto.setCustRiskLevel(queryCustRiskSurveyResult.getCustRiskLevel());
                            custInfoDto.setRiskSurveyDt(queryCustRiskSurveyResult.getRiskSurveyDt());
                            if (null != queryCustRiskSurveyResult.getRiskExpireDate()
                                    && new Date().compareTo(queryCustRiskSurveyResult.getRiskExpireDate()) > 0) {
                                custInfoDto.setOverdue(true);
                            } else {
                                custInfoDto.setOverdue(false);
                            }
                        } else {
                            custInfoDto.setCustRiskLevel(null);
                            custInfoDto.setRiskSurveyDt(null);
                            custInfoDto.setOverdue(true);
                        }
                    }
                }
            }

        }
        List<CustInfoDto> custInfoResultList = null;
        if (!StringUtils.isEmpty(selectDisCode)) {
            if (!CollectionUtils.isEmpty(custInfoDtoList)) {
                for (CustInfoDto custInfoDto : custInfoDtoList) {
                    if (selectDisCode.equals(custInfoDto.getDisCode())) {
                        custInfoResultList = new ArrayList<CustInfoDto>();
                        custInfoResultList.add(custInfoDto);
                        break;
                    }
                }
            }
        } else {
            custInfoResultList = custInfoDtoList;
        }
        return custInfoResultList;
    }
   /**
    * 
    * validRisk:(比较风险等级)
    * @param custRisk
    * @param fundRisk
    * @return
    * <AUTHOR>
    * @date 2017年11月13日 下午3:47:32
    */
   private boolean compareRisk(String custRisk, String fundRisk){
       logger.info("compareRisk|custRisk:{},fundRisk:{}",custRisk,fundRisk);
       if(CustRiskLevelEnum.LEAST.getCode().equals(custRisk) && FUND_RISK_LEAST.equals(fundRisk)){
           return true;
       }
       return custRisk.compareTo(fundRisk) >= 0;
   }
}

