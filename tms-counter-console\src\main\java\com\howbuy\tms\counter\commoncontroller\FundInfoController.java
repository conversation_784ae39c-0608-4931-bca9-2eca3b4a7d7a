/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.page.PageResult;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.interlayer.product.enums.ProductChannelNewEnum;
import com.howbuy.tms.batch.facade.query.querybatchflowinfo.QueryBatchFlowInfoResponse;
import com.howbuy.tms.common.enums.busi.BusiProcessProtocolTypeEnum;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankCardSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankCardSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.bean.CustAllBankSensitiveModel;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundTaInfoBean;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.HttpUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.config.FundRegionEnumConfig;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto.DtlBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import java.math.BigDecimal;
import java.util.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.howbuy.tms.counter.fundservice.trade.TmsAgreementCounterService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/***
 * 
 * @description:(产品信息控制器)
 * <AUTHOR>
 * @date 2017年9月15日 上午9:43:39
 * @since JDK 1.6
 */
@Controller
public class FundInfoController extends AbstractController {
    private final static Logger logger = LogManager.getLogger(FundInfoController.class);

    @Resource
    private TmsAgreementCounterService tmsAgreementCounterService;
    
    @Autowired
    @Qualifier("queryAllBankAcctSensitiveInfoOuterService")
    private QueryAllBankAcctSensitiveInfoOuterService queryAllBankAcctSensitiveInfoOuterService;

    /**
     * 
     * queryFundInfo:(查询基金信息)
     * 
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 上午9:44:13
     */
    @RequestMapping("tmscounter/queryfundinfo.htm")
    public void queryFundInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");

        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = getCustInfoDto(custInfoForm);

        DisInfoDto disInfoDto = new DisInfoDto();
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        FundInfoAndNavBean fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(fundCode, workDay);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        fundInfoAndNavBean.setJointOpenDayRegion(getJointOpenDayRegion(fundInfoAndNavBean));
        bodyResult.put("fundInfo", fundInfoAndNavBean);

        QueryAgreementDto queryAgreementDto = tmsAgreementCounterService.queryAgreement(custInfoDto, null, Arrays.asList(new String[]{fundInfoAndNavBean.getFundCode()}));
        bodyResult.put("queryAgreementDto", queryAgreementDto);

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    private static CustInfoDto getCustInfoDto(String custInfoForm) {
        CustInfoDto custInfoDto = new CustInfoDto();
        if(!StringUtil.isEmpty(custInfoForm)){
            custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        }
        return custInfoDto;
    }
    
    /**
     * 
     * queryFundTaInfo:(查询基金TA信息)
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2018年12月15日 下午4:18:49
     */
    @RequestMapping("tmscounter/queryFundTaInfo.htm")
    public void queryFundTaInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String fundName = request.getParameter("fundName");
        String pageNo = StringUtil.isEmpty(request.getParameter("page")) ? "1" : request.getParameter("page");
        String pageSize = StringUtil.isEmpty(request.getParameter("pageSize")) ? "10" : request.getParameter("pageSize");

        PageResult<FundTaInfoBean> pageResult = tmsCounterOutService.getFundTaInfoListByPage(ProductChannelNewEnum.FUND.getCode(), 
                fundCode, fundName, Integer.parseInt(pageNo), Integer.parseInt(pageSize));
        if (pageResult == null){
            pageResult = new PageResult<FundTaInfoBean>(Integer.valueOf(pageNo), Integer.valueOf(pageSize), 0);
        }

        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("fundTaList", pageResult.getPageList());
        rst.put("pageNum", pageResult.getPageNo());
        rst.put("totalPage", pageResult.getTotalPage());
        rst.put("totalCount", pageResult.getTotalCount());
    
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(rst);
        WebUtil.write(response, tmsCounterResult);
    }

    /***
     * 
     * calBaseFate:(计算原始费率)
     * 
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午3:22:53
     */
    @RequestMapping("tmscounter/calbasefate.htm")
    public void calBaseFate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);

        String fundCode = request.getParameter("fundCode");
        String applyAmount = request.getParameter("applyAmount");
        String paymentType = request.getParameter("paymentType");


        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getRegDisCode());
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        FundInfoAndNavBean fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(fundCode, workDay);
        String mBusiCode = tmsCounterOutService.getMBusiCode(custInfoDto, fundInfoAndNavBean, workDay);
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(mBusiCode);

        FundProductFeeRateBean fundProductFeeRateBean = null;

        if (null == businessCodeEnum){
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }

        fundProductFeeRateBean = tmsCounterOutService.getFundFeeRateByAmt(fundCode, businessCodeEnum.getCode(), custInfoDto.getInvstType(),
                fundInfoAndNavBean.getFundShareClass(), new BigDecimal(applyAmount));

        paymentType = StringUtils.isNotEmpty(paymentType)?paymentType:PaymentTypeEnum.AGENT_DRAWING.getCode();
        BigDecimal discount = tmsFundCounterOutService.getActiRateDiscount(fundCode, fundInfoAndNavBean.getFundShareClass(),
        		paymentType, custInfoDto.getInvstType(), businessCodeEnum.getCode(), null, custInfoDto.getDisCode(),
                new BigDecimal(applyAmount));


        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respData", fundProductFeeRateBean);
        bodyResult.put("discount", discount);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }
    
    /**
     * queryFundCustHodlInfo:(查询用户基金持仓信息)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年1月24日 上午9:39:48
     */
    @RequestMapping("/tmscounter/fund/queryCustHodlInfo.htm")
    public ModelAndView queryFundCustHodlInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        String protocolType = request.getParameter("protocolType");
        logger.info("appDt:{},appTm:{},fundCode:{},custNo:{},disCode:{},protocolType:{}", fundCode, custNo, disCode, appDt, appTm, protocolType);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        QueryAcctBalanceDtlReqDto queryAcctBalanceDtlReqDto = new QueryAcctBalanceDtlReqDto();
        queryAcctBalanceDtlReqDto.setCustNo(custNo);
        queryAcctBalanceDtlReqDto.setFundCode(fundCode);
        queryAcctBalanceDtlReqDto.setAppDt(appDt);
        queryAcctBalanceDtlReqDto.setAppTm(appTm);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);

        List<String> protocolTypeList = new ArrayList<String>();
        if (StringUtils.isNotEmpty(protocolType)) {
            protocolTypeList.add(protocolType);
        } else {
            protocolTypeList.add(ProtocolTypeEnum.DEFAULT_FUND.getCode());
            protocolTypeList.add(ProtocolTypeEnum.FUND_SCHEDULE.getCode());
            protocolTypeList.add(ProtocolTypeEnum.TAX_DELAY_FUND.getCode());
        }

        QueryAcctBalanceDtlRespDto responseDto = tmsFundCounterService.queryAcctBalDtlByMultiProtocol(queryAcctBalanceDtlReqDto, disInfoDto, protocolTypeList);
        
        Map<String, CustAllBankSensitiveModel> bankMap = getBankMap(custNo, disCode);
        
        if(responseDto != null && responseDto.getBalanceDtlList() != null && responseDto.getBalanceDtlList().size() > 0){
        	for(DtlBean bean : responseDto.getBalanceDtlList()){
        		CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(bean.getCpAcctNo());
        		if(custAllBankSensitiveModel != null){
        			bean.setBankAcctNo(custAllBankSensitiveModel.getBankAcct());
        		}
        	}
        }
        
//        Map<String, Object> body = new HashMap<String, Object>();
//        body.put("dtlResp", responseDto);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 
     * queryFundRedeemInfo:(零售公募赎回 --- 查询用户基金持仓信息)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月14日 下午7:07:48
     */
    @RequestMapping("/tmscounter/fund/queryfundredeeminfo.htm")
    public ModelAndView queryFundRedeemInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        logger.info("fundCode:{},custNo:{},disCode:{}", fundCode, custNo, disCode);

        QueryAcctBalanceDtlReqDto queryAcctBalanceDtlReqDto = new QueryAcctBalanceDtlReqDto();
        queryAcctBalanceDtlReqDto.setCustNo(custNo);
        queryAcctBalanceDtlReqDto.setFundCode(fundCode);
        List<String> protocolTypeList = new ArrayList<String>();
        protocolTypeList.add(ProtocolTypeEnum.DEFAULT_FUND.getCode());
        protocolTypeList.add(ProtocolTypeEnum.FUND_SCHEDULE.getCode());
        protocolTypeList.add(ProtocolTypeEnum.TAX_DELAY_FUND.getCode());
        protocolTypeList.add(ProtocolTypeEnum.CYCLE_LOCK_FUND.getCode());
        QueryAcctBalanceDtlRespDto responseDto = tmsFundCounterService.queryAcctBalDtlByMultiProtocol(queryAcctBalanceDtlReqDto, disInfoDto, protocolTypeList);
        
        Map<String, CustAllBankSensitiveModel> bankMap = getBankMap(custNo, disCode);
       
        // return balance dto
        QueryAcctBalanceDtlRespDto returnDto =  new QueryAcctBalanceDtlRespDto();
        if(responseDto!=null){
            returnDto.setReturnCode(responseDto.getReturnCode());
            returnDto.setDescription(responseDto.getDescription());
        }

        List<DtlBean> balanceDtlList = new ArrayList<DtlBean>();
        
        if(responseDto != null && CollectionUtils.isNotEmpty(responseDto.getBalanceDtlList())){
            
            //  查询"确认处理日终"是否已经执行完成, 如果"确认处理日终"节点已经执行或执行中，柜台就不可以下全赎单
            QueryBatchFlowInfoResponse batchFlowInfo = tmsCounterService.getBatchFlowInfo(BusinessProcessingStepEnum.BPS_ACK_DAY_END_PROCESS.getCode(), disInfoDto);
            String batchStat = null;
            if(batchFlowInfo != null && CollectionUtils.isNotEmpty(batchFlowInfo.getFlowList())){
                batchStat = batchFlowInfo.getFlowList().get(0).getBatchStat();
            }
            
            for(int i=0;i<responseDto.getBalanceDtlList().size();i++){
                DtlBean bal = responseDto.getBalanceDtlList().get(i);
                
                // 过滤掉冻结份额为0并且当前可用份额为0的，不能赎回
                if(bal.getUnconfirmedVol().compareTo(BigDecimal.ZERO) == 0 
                        && bal.getAvailVol().compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                
                String allRedeemFlag = null;
                // 执行中或执行成功
                if(BatchStatEnum.PROCESSING.getKey().equals(batchStat)
                        || BatchStatEnum.PROCESS_SUCCESS.getKey().equals(batchStat) ){
                    allRedeemFlag = AllRedeemFlagEnum.NOT_ALL_REDEEM.getCode();
                
                    // 未执行
                } else if(BatchStatEnum.NON_PROCESS.getKey().equals(batchStat)){
                    /**
                     * 货基全赎校验
                     * a. 校验基金是否支持全赎
                     * b. 校验该客户该基金有没有在途的申购及基金转换转入、超级转换、定投的交易申请，如果有，不能全赎；
                     *    同时不存在当天认申购及基金转换（含超级转换）转入的交易确认,不能全赎；
                     * 是否可以全赎：1:是(可以);0:否(不可以);
                     */
                    CounterRedeemReqDto dto = new CounterRedeemReqDto();
                    dto.setTxAcctNo(custNo);
                    dto.setCpAcctNo(bal.getCpAcctNo());
                    dto.setFundCode(bal.getProductCode());
                    dto.setFundShareClass(bal.getFundShareClass());
                    dto.setProtocolType(bal.getProtocolType());
                    dto.setProtocolNo(bal.getProtocolNo());
                    // 查询客户持仓基金是否可以全赎
                    allRedeemFlag = tmsFundCounterService.queryIsOrNotAllRedeem(dto, disInfoDto);
                    
                }
                logger.info("return allRedeemFlag:{}", allRedeemFlag);
                bal.setAllRedeemFlag(StringUtils.isEmpty(allRedeemFlag) ? AllRedeemFlagEnum.NOT_ALL_REDEEM.getCode() : allRedeemFlag);
                
                // set return dto
                balanceDtlList.add(bal);
                returnDto.setBalanceDtlList(balanceDtlList);
            }
        }
        
        
        if(balanceListIsNotNull(returnDto)){
        	for(DtlBean bean : returnDto.getBalanceDtlList()){
        		CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(bean.getCpAcctNo());
        		if(custAllBankSensitiveModel != null){
        			bean.setBankAcctNo(custAllBankSensitiveModel.getBankAcct());
        		}
        	}
        }
        
        // 赎回券商固收、保险类产品时，交易回款方式只能选择“回款至储蓄罐（协议默认）
        //(暂中台还未迁移券商固收、保险类产品，故产品回款都支持银行卡或储蓄罐)
        // 产品是否支持赎回到银行卡 0-不支持 1-支持
        String redeemDirectionIsSupCardFlag = "1";
        // 产品是否支持赎回到储蓄罐 0-不支持 1-支持
        String redeemDirectionIsSupCxgFlag = "1";

        returnDto.setRedeemDirectionIsSupCxgFlag(redeemDirectionIsSupCxgFlag);
        returnDto.setRedeemDirectionIsSupCardFlag(redeemDirectionIsSupCardFlag);
        returnDto.setTaTradeDt(tmsCounterOutService.getCurrTaTradeDt());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(returnDto);
        WebUtil.write(response, rst);
        return null;
    }

    private boolean balanceListIsNotNull(QueryAcctBalanceDtlRespDto returnDto){
        return returnDto != null && returnDto.getBalanceDtlList() != null && returnDto.getBalanceDtlList().size() > 0;
    }
    
    /**
     * queryExchangeFundInfo:(零售基金转换 --- 查询用户基金持仓信息)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年1月29日 下午3:01:31
     */
    @RequestMapping("/tmscounter/fund/queryExchangeFundInfo.htm")
    public ModelAndView queryExchangeFundInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        logger.info("fundCode:{},custNo:{},disCode:{}", fundCode, custNo, disCode);
        
        QueryAcctBalanceDtlReqDto queryAcctBalanceDtlReqDto = new QueryAcctBalanceDtlReqDto();
        queryAcctBalanceDtlReqDto.setCustNo(custNo);
        queryAcctBalanceDtlReqDto.setFundCode(fundCode);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        
        Map<String, CustAllBankSensitiveModel> bankMap = getBankMap(custNo, disCode);
        
        List<String> protocolTypeList = new ArrayList<String>();
        protocolTypeList.add(ProtocolTypeEnum.DEFAULT_FUND.getCode());
        protocolTypeList.add(ProtocolTypeEnum.FUND_SCHEDULE.getCode());
        protocolTypeList.add(ProtocolTypeEnum.TAX_DELAY_FUND.getCode());
        QueryAcctBalanceDtlRespDto responseDto = tmsFundCounterService.queryAcctBalDtlByMultiProtocol(queryAcctBalanceDtlReqDto, disInfoDto, protocolTypeList);
        if(responseDto != null && CollectionUtils.isNotEmpty(responseDto.getBalanceDtlList())){
            // 过滤掉冻结份额为0并且当前可用份额为0的，不能赎回
            for(int i=0;i<responseDto.getBalanceDtlList().size();i++){
                DtlBean bal = responseDto.getBalanceDtlList().get(i);
                if(bal.getUnconfirmedVol().compareTo(BigDecimal.ZERO) == 0 
                        && bal.getAvailVol().compareTo(BigDecimal.ZERO) == 0){
                    responseDto.getBalanceDtlList().remove(i);
                }
            }
        }
        
        if(responseDto != null && responseDto.getBalanceDtlList() != null && responseDto.getBalanceDtlList().size() > 0){
        	for(DtlBean bean : responseDto.getBalanceDtlList()){
        		CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(bean.getCpAcctNo());
        		if(custAllBankSensitiveModel != null){
        			bean.setBankAcctNo(custAllBankSensitiveModel.getBankAcct());
        		}
        	}
        }
        
        //  基金信息
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        FundInfoAndNavBean fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(fundCode, workDay);
        fundInfoAndNavBean.setJointOpenDayRegion(getJointOpenDayRegion(fundInfoAndNavBean));
        
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("dtlResp", responseDto);
        body.put("fundInfo", fundInfoAndNavBean);
        body.put("taTradeDt", tmsCounterOutService.getCurrTaTradeDt());
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    
    /**
     * queryTransOutFundCustHodlInfo:(零售公募转托管转出 --- 查询用户基金持仓信息)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年1月24日 上午9:39:48
     */
    @RequestMapping("/tmscounter/fund/queryTransOutFundCustHodlInfo.htm")
    public ModelAndView queryTransOutFundCustHodlInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String idNo = request.getParameter("idNo");
        String disCode = request.getParameter("disCode");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        logger.info("queryTransOutFundCustHodlInfo custNo:{},disCode:{}", custNo, disCode);
        String operIp = HttpUtil.getIpAddr(request);

        QueryAcctBalanceDtlReqDto queryAcctBalanceDtlReqDto = new QueryAcctBalanceDtlReqDto();
        String txAcctNo = custNo;
        if(StringUtils.isEmpty(txAcctNo) && StringUtils.isNotEmpty(idNo)){
            txAcctNo = getCustNo(idNo, operIp);
        }
        queryAcctBalanceDtlReqDto.setCustNo(txAcctNo);
//        queryAcctBalanceDtlReqDto.setAppDt("********");
//        queryAcctBalanceDtlReqDto.setAppTm("100000");
        QueryAcctBalanceDtlRespDto responseDto = tmsFundCounterService.queryAcctBalDtlByMultiProtocolType(queryAcctBalanceDtlReqDto, disInfoDto, BusiProcessProtocolTypeEnum.getAllExcludeSm());
        
        Map<String, CustAllBankSensitiveModel> bankMap = getBankMap(custNo, disCode);
        
        if(responseDto != null && responseDto.getBalanceDtlList() != null && responseDto.getBalanceDtlList().size() > 0){
        	for(DtlBean bean : responseDto.getBalanceDtlList()){
        		CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(bean.getCpAcctNo());
        		if(custAllBankSensitiveModel != null){
        			bean.setBankAcctNo(custAllBankSensitiveModel.getBankAcct());
        		}
        	}
        }
        
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respData", responseDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(bodyResult);
        WebUtil.write(response, rst);
        return null;
    }

	private Map<String, CustAllBankSensitiveModel> getBankMap(String custNo, String disCode) {
		QueryAllBankCardSensitiveInfoContext ctx = new QueryAllBankCardSensitiveInfoContext();
        ctx.setTxAcctNo(custNo);
        ctx.setDisCode(disCode);
        QueryAllBankCardSensitiveInfoResult allBankResult = queryAllBankAcctSensitiveInfoOuterService.queryAllBankAcctSensitiveInfo(ctx);
        Map<String, CustAllBankSensitiveModel> bankMap = new HashMap<String, CustAllBankSensitiveModel>(16);
        if(allBankResult.getCustBankModelList() != null && allBankResult.getCustBankModelList().size() > 0){
        	for(CustAllBankSensitiveModel model : allBankResult.getCustBankModelList()){
        		bankMap.put(model.getCpAcctNo(), model);
        	}
        }
		return bankMap;
	}


    /**
     * 获取日经描述信息
     * @param fundInfo
     * @return java.lang.String
     * @author: junkai.du
     * @date: 2024/4/26 16:50
     * @since JDK 1.8
     */
    private String getJointOpenDayRegion(FundInfoAndNavBean fundInfo) {
        if (StringUtils.isNotBlank(fundInfo.getJointOpenDayRegion()) && StringUtils.isNotBlank(fundInfo.getEndTm()) && fundInfo.getEndTm().compareTo("150000") < 0){
            String desc = String.format("因%s投资范围内包含%s市场产品，交易日%s-15:00暂停交易受理。若继续下单会被TA判失败。", fundInfo.getFundName(), FundRegionEnumConfig.getDescByCode(fundInfo.getJointOpenDayRegion()), fundInfo.getEndTm().replaceAll("(\\d{2})(\\d{2})(\\d{2})", "$1:$2"));
            return desc;
        }
        return "";
    }
}
