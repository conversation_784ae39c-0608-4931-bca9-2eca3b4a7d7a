package com.howbuy.tms.counter.utils;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.counter.enums.VolShareTypeEnum;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/24 10:48
 * @since JDK 1.8
 */
public class VolShareTypeUtils {

    public static boolean checkVolShareType(String shareType, String disCode){
        if(VolShareTypeEnum.VOL_TRANS.getCode().equals(shareType)){
            if(DisCodeEnum.LCT.getCode().equals(disCode)){
                return true;
            }
        }
        return false;
    }


    public static boolean checkTransVol(String shareType){
        if(VolShareTypeEnum.VOL_TRANS.getCode().equals(shareType)){
            return true;
        }
        return false;
    }



}