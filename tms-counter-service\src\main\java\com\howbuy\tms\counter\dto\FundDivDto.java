/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(基金分红方式)
 * <AUTHOR>
 * @date 2017年4月1日 下午4:01:16
 * @since JDK 1.6
 */
public class FundDivDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 9010654893324291378L;
    /** 基金简称. **/
    private String fundAttr;
    /** 基金代码. **/
    private String fundCode;

    /** 分红方式. 0-红利再投，1-现金红利 **/
    private String divMode;

    /** 份额类型. A-前收费；B-后收费 **/
    private String shareClass;

    /** 是否准许修改分红方式 :0-不准许修改，1-准许修改 **/
    private String allowModifyDivMode;

    /** 预计确认日期 **/
    private String expCnfmDt;

    /**
     * 基金状态：0-交易；1-发行；2-发行成功；3-发行失败；4-停止交易；5-停止申购；6-停止赎回；7-权益登记；8-红利发放；9-基金封闭；
     */
    private String fundStat;

    /**
     * 基金类型，0-股票型1-混合型2-债券型3-货币型4-QDII5-封闭式6-结构型7-一对多专户8-券商资管产品_大集合9-券商资管产品_小集合
     */
    private String fundType;
    
    /**
     * TA代码
     */
    private String taCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 协议类型
     */
    private String protocolType;

    /**
     * 份额类型. A-前收费；B-后收费
     */
    private String fundShareClass;

    /**
     * 协议号
     */
    private String protocolNo;

    /**
     * 卡号
     */
    private String cpAcctNo;

    /**
     * 分销
     */
    private String disCode;

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getDivMode() {
        return divMode;
    }

    public void setDivMode(String divMode) {
        this.divMode = divMode;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public String getAllowModifyDivMode() {
        return allowModifyDivMode;
    }

    public void setAllowModifyDivMode(String allowModifyDivMode) {
        this.allowModifyDivMode = allowModifyDivMode;
    }

    public String getExpCnfmDt() {
        return expCnfmDt;
    }

    public void setExpCnfmDt(String expCnfmDt) {
        this.expCnfmDt = expCnfmDt;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }
}
