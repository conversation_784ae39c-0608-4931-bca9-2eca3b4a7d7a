package com.howbuy.tms.counter.filter;

import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.tms.counter.common.Constants;
import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.web.filter.authc.PassThruAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;

/**
 * 
 * @description:shiro 鉴权拦截器
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年5月21日 下午8:00:43
 * @since JDK 1.6
 */
public class AccessAuthenticationFilter extends PassThruAuthenticationFilter {

	@Override
	protected void redirectToLogin(ServletRequest request,
			ServletResponse response) throws IOException {
		HttpServletRequest req = (HttpServletRequest) request;
        if (Constants.XML_HTTP_REQUEST.equals(req.getHeader(Constants.X_REQUESTED_WITH))) {
            response.setContentType("application/json;charset=UTF-8");
            String json = "{\"code\":\"301\", \"desc\":\"会话超时请重新登录\"}";
            PrintWriter pw = response.getWriter();
            pw.print(json);
            pw.close();
        } else {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            HttpSession session = httpServletRequest.getSession();

            if (session == null) {
                WebUtils.issueRedirect(request, response, "/tmscounter/loginView.htm");
                return;
            }

            Object obj = session.getAttribute(Constants.SESSION_USER);
            if (obj == null || StringUtils.isEmpty(((UserAccountModel)obj).getUserName())){
                WebUtils.issueRedirect(request, response, "/tmscounter/loginView.htm");
            }
        }
	}

}
