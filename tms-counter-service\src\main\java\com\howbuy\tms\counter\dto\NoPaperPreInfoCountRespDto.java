/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;
import java.io.Serializable;
import java.util.List;

/**
 * @description:(查询crm无纸化预约单统计)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 上午9:20:01
 * @since JDK 1.6
 */
public class NoPaperPreInfoCountResp<PERSON><PERSON> extends BaseResponseDto {
    private static final long serialVersionUID = 2071625239691832058L;

    private List<NoPaperPreInfoCountDto> preInfoCountList;

    public List<NoPaperPreInfoCountDto> getPreInfoCountList() {
        return preInfoCountList;
    }

    public void setPreInfoCountList(List<NoPaperPreInfoCountDto> preInfoCountList) {
        this.preInfoCountList = preInfoCountList;
    }

    public static class NoPaperPreInfoCountDto implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 预约日期
         */
        private String date;
        /**
         * 预约总数
         */
        private int total;
        /**
         * 未确认总数
         */
        private int unconfirmedNumber;
        /**
         * 预约撤销总数
         */
        private int cancelNumber;
        /**
         * 预约过期总数
         */
        private int outOfDateNumber;
        /**
         * 已确认总数
         */
        private int confirmedNumber;
        /**
         * 预约完成总数
         */
        private int finishedNumber;
        /**
         * 预约驳回总数
         */
        private int rejectNumber;
        /**
         * 预约订单总数
         */
        private int orderNumber;

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getUnconfirmedNumber() {
            return unconfirmedNumber;
        }

        public void setUnconfirmedNumber(int unconfirmedNumber) {
            this.unconfirmedNumber = unconfirmedNumber;
        }

        public int getCancelNumber() {
            return cancelNumber;
        }

        public void setCancelNumber(int cancelNumber) {
            this.cancelNumber = cancelNumber;
        }

        public int getOutOfDateNumber() {
            return outOfDateNumber;
        }

        public void setOutOfDateNumber(int outOfDateNumber) {
            this.outOfDateNumber = outOfDateNumber;
        }

        public int getConfirmedNumber() {
            return confirmedNumber;
        }

        public void setConfirmedNumber(int confirmedNumber) {
            this.confirmedNumber = confirmedNumber;
        }

        public int getFinishedNumber() {
            return finishedNumber;
        }

        public void setFinishedNumber(int finishedNumber) {
            this.finishedNumber = finishedNumber;
        }

        public int getRejectNumber() {
            return rejectNumber;
        }

        public void setRejectNumber(int rejectNumber) {
            this.rejectNumber = rejectNumber;
        }

        public int getOrderNumber() {
            return orderNumber;
        }

        public void setOrderNumber(int orderNumber) {
            this.orderNumber = orderNumber;
        }
    }
}
