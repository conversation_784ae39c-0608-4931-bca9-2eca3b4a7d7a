/**
 *Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.util.List;

/**
 * 
 * @description:(柜台修改赎回方向)
 * <AUTHOR>
 * @date 2023年05月9日 下午3:49:45
 * @since JDK 1.8
 */
public class CounterModifyRedeemDirectionReqDto extends OperInfoBaseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = 7438795312699404598L;

    /**
     * 审核申请流水号
     */
    private String dealAppNo;
    
    /**
     * 原申请单号
     */
    private String originalAppDealNo;

    /**
     * 业务类型(转托管转入或转出)
     */
    private String mBusiCode;

    /**
     * 转托管业务类型(1-跨市场；2-场外跨销售机构)
     */
    private String transferTubeBusiType;

    /**
     * 对方网点
     */
    private String tOutletCode;
    
    /**
     * 对方销售人处投资者基金交易账号
     */
    private String tSellerTxAcctNo;

    /**
     * 对方销售人代码
     */
    private String tSellerCode;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 基金份额类型
     */
    private String fundShareClass;
    
    /**
     * 基金名称
     */
    private String fundName;
    
    /**
     * TA代码
     */
    private String taCode;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 申请日期,格式：YYYYMMDD
     */
    private String appDt;

    /**
     * 申请时间,格式：HHMMSS
     */
    private String appTm;

    /**
     * 经办人证件号
     */
    private String transactorIdNo;

    /**
     * 经办人证件类型
     */
    private String transactorIdType;

    /**
     * 经办人姓名
     */
    private String transactorName;

    /**
     * 投资顾问代码
     */
    private String consCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;

    /**
     * 申请状态，0-未申请；1-申请通过；2-申请失败
     */
    private String appFlag;

    /**
     * 是否代理，1-是；0-否
     */
    private String agentFlag;

    /**
     * 转托管份额转入或转出明细信息
     */
    private List<TransferTubeOrderReqDto> transferTubeDetailList;

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getOriginalAppDealNo() {
        return originalAppDealNo;
    }

    public void setOriginalAppDealNo(String originalAppDealNo) {
        this.originalAppDealNo = originalAppDealNo;
    }

    public String getmBusiCode() {
        return mBusiCode;
    }

    public void setmBusiCode(String mBusiCode) {
        this.mBusiCode = mBusiCode;
    }

    public String getTransferTubeBusiType() {
        return transferTubeBusiType;
    }

    public void setTransferTubeBusiType(String transferTubeBusiType) {
        this.transferTubeBusiType = transferTubeBusiType;
    }

    public String gettOutletCode() {
        return tOutletCode;
    }

    public void settOutletCode(String tOutletCode) {
        this.tOutletCode = tOutletCode;
    }

    public String gettSellerTxAcctNo() {
        return tSellerTxAcctNo;
    }

    public void settSellerTxAcctNo(String tSellerTxAcctNo) {
        this.tSellerTxAcctNo = tSellerTxAcctNo;
    }

    public String gettSellerCode() {
        return tSellerCode;
    }

    public void settSellerCode(String tSellerCode) {
        this.tSellerCode = tSellerCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }
    
    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getAppFlag() {
        return appFlag;
    }

    public void setAppFlag(String appFlag) {
        this.appFlag = appFlag;
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public List<TransferTubeOrderReqDto> getTransferTubeDetailList() {
        return transferTubeDetailList;
    }

    public void setTransferTubeDetailList(List<TransferTubeOrderReqDto> transferTubeDetailList) {
        this.transferTubeDetailList = transferTubeDetailList;
    }
}
