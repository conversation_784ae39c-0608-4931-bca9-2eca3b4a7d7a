/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService;
import com.howbuy.interlayer.product.service.high.request.QueryNotHBJGFundListRequest;
import com.howbuy.interlayer.product.service.high.response.QueryNotHBJGFundListResponse;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.OpCheckNode;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.context.ContextUtils;
import com.howbuy.tms.counter.controller.context.ModifyDivContext;
import com.howbuy.tms.counter.controller.validate.ValidateUtils;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.OtherInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.service.validate.ValidateService;
import com.howbuy.tms.counter.util.CommonUtil;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(修改分红方式控制器) 
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class ModifyDivController {
    private static final Logger LOGGER = LogManager.getLogger(ModifyDivController.class);
    @Autowired
    private TmsCounterService  tmsCounterService;
    @Autowired
    private ValidateService validateService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    @Qualifier("tmscounter.queryNotHBJGFundListService")
    private QueryNotHBJGFundListService queryNotHBJGFundListService;
    
    /**
     * 
     * buyIndex:(修改分红方式)
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception 
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/modifydivconfirm.htm")
    public ModelAndView modifyDivConfirm(HttpServletRequest request,HttpServletResponse response) throws Exception{
        ModifyDivContext context = buildContext(request);
        Date appDtm = getAppDtm(context);
        context.setAppDtm(appDtm);

        // 校验申请日期
        ValidateUtils.validateAppDtm(appDtm);

        //校验产品信息
        validateService.validateProductInfo(context.getFundDivDto().getFundCode());
        //查询高端产品信息
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(context.getFundDivDto().getFundCode());
        context.setHighProduct(highProductBaseBean);

        // 校验线上资料
        if(context.getAuditingOrderFileCmd() != null
                && !StringUtils.isEmpty(context.getAuditingOrderFileCmd().getOrderid())) {
            tmsCounterOutService.validateOrderFileStatus(context.getAuditingOrderFileCmd());
        }else{
            tmsCounterOutService.validateOrderFileExist(ContextUtils.buildQueryCotext(context, TmsCounterConstant.CRM_TRADE_TYPE_MODIFYDIV),
                    OpCheckNode.PRE_CHECK.getCode());
        }

        CounterModifyDivReqDto counterModifyDivReqDto = fillOrderDto(context);


        CounterOrderDto condition = ContextUtils.getValidateReplyCondition(context, TxCodes.HIGH_MODIFYDIV_COUNTER);
        tmsCounterService.validateReplyOrder(condition, context.getDisInfoDto());

        LOGGER.info("ModifyDivController|modifyDivConfirm|counterModifyDivReqDto:{}", JSON.toJSONString(counterModifyDivReqDto));
        CounterModifyDivRespDto responseDto = tmsCounterService.counterModifyDiv(counterModifyDivReqDto, context.getDisInfoDto());

        // 初审通过
        tmsCounterOutService.auditingFile(context.getOperatorInfoCmd(), context.getAuditingOrderFileCmd(), responseDto.getDealAppNo());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    private Date getAppDtm(ModifyDivContext context) {
        String appDtmStr = context.getOtherInfoDto().getAppDtm();
        Date appDtm = null;
        if(!StringUtil.isEmpty(appDtmStr)){
            appDtm = DateUtil.string2Date(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
        }else{
            appDtm = new Date();
        }
        return appDtm;
    }


    private CounterModifyDivReqDto fillOrderDto(ModifyDivContext context) {
        CounterModifyDivReqDto counterModifyDivReqDto  = new CounterModifyDivReqDto();
        counterModifyDivReqDto.setTxAcctNo(context.getCustInfoDto().getCustNo());
        counterModifyDivReqDto.setFundCode(context.getFundDivDto().getFundCode());
        counterModifyDivReqDto.setFundDivMode(context.getTargetDiv());
        counterModifyDivReqDto.setFundShareClass(context.getFundDivDto().getShareClass());
        CommonUtil.setCommonOperInfo(context.getOperatorInfoCmd(),counterModifyDivReqDto);
        counterModifyDivReqDto.setFundName(context.getFundDivDto().getFundAttr());
        counterModifyDivReqDto.setCustName(context.getCustInfoDto().getCustName());
        counterModifyDivReqDto.setIdNo(PrivacyUtil.encryptIdCard(context.getCustInfoDto().getIdNo()));
        counterModifyDivReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        counterModifyDivReqDto.setDisCode(context.getCustInfoDto().getDisCode());
        counterModifyDivReqDto.setConsCode(context.getOtherInfoDto().getConsCode());
        counterModifyDivReqDto.setAgentFlag(context.getOtherInfoDto().getAgentFlag());
        counterModifyDivReqDto.setTransactorIdNo(context.getTransactorInfoDto().getTransactorIdNo());
        counterModifyDivReqDto.setTransactorIdType(context.getTransactorInfoDto().getTransactorIdType());
        counterModifyDivReqDto.setTransactorName(context.getTransactorInfoDto().getTransactorName());
        // 产品通道
        counterModifyDivReqDto.setProductChannel(context.getHighProduct().getProductChannel());
        counterModifyDivReqDto.setAppDt(DateUtil.shortDateString(context.getAppDtm(), DateUtils.YYYYMMDD));
        counterModifyDivReqDto.setAppTm(DateUtil.shortDateString(context.getAppDtm(),DateUtils.HHMMSS));
        counterModifyDivReqDto.setTaCode(context.getHighProduct().getTaCode());
        if(context.getAuditingOrderFileCmd() != null){
            counterModifyDivReqDto.setMaterialId(context.getAuditingOrderFileCmd().getOrderid());
        }
        return counterModifyDivReqDto;
    }

    private ModifyDivContext buildContext(HttpServletRequest request){
        OperatorInfoCmd operatorInfoCmd  =  (OperatorInfoCmd)SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO,request);
        String fundDiv = request.getParameter("fundDiv");
        String custInfoForm = request.getParameter("custInfoForm");
        String targetDiv  = request.getParameter("targetDiv");
        String othetInfoForm = request.getParameter("othetInfoForm");
        OtherInfoDto otherInfoDto = JSON.parseObject(othetInfoForm, OtherInfoDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        FundDivDto fundDivDto =  JSON.parseObject(fundDiv,FundDivDto.class);
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm,TransactorInfoDto.class);
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");
        AuditingOrderFileCmd auditingOrderFileCmd =  null;
        if(!org.springframework.util.StringUtils.isEmpty(materialinfoForm)){
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
        }

        ModifyDivContext context = new ModifyDivContext();
        context.setTargetDiv(targetDiv);
        context.setFundDivDto(fundDivDto);
        context.setAuditingOrderFileCmd(auditingOrderFileCmd);
        context.setCustInfoDto(custInfoDto);
        context.setOtherInfoDto(otherInfoDto);
        context.setTransactorInfoDto(transactorInfoDto);
        context.setOperatorInfoCmd(operatorInfoCmd);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        context.setDisInfoDto(disInfoDto);

        return context;
    }



    /**
     * 
     * queryModify:(查询基金分红方式)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午4:10:14
     */
    @RequestMapping("/tmscounter/querymodifydiv.htm")
    public ModelAndView queryModify(HttpServletRequest request,HttpServletResponse response) throws Exception{
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        List<FundDivDto> fundDivs = new ArrayList<>();
        List<FundDivDto> fundDivList = tmsCounterService.queryFundDiv(custNo, disInfoDto);
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        if(user.getInHBJG() && !CollectionUtils.isEmpty(fundDivList)){
            QueryNotHBJGFundListResponse queryNotHBJGFundListResponse = queryNotHBJGFundListService.query(new QueryNotHBJGFundListRequest());
            for(FundDivDto fundDivDto : fundDivList){
                if(!queryNotHBJGFundListResponse.getFundCodes().contains(fundDivDto.getFundCode())){
                    fundDivs.add(fundDivDto);
                }
            }
        }else {
            fundDivs.addAll(fundDivList);
        }

        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("fundDivList", fundDivs);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
}

