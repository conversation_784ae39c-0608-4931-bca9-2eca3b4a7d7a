$(function(){
    FixedRedeemProdcutProcess.init();
});

var FixedRedeemProdcutProcess = {
    init:function () {
        $("#queryBtn").on("click", function () {
            FixedRedeemProdcutProcess.queryData();
        });

        $("#resetBth").on("click", function () {
            CommonUtil.clearForm("queryForm");
        });

        $("#order").on("click", function () {
            FixedRedeemProdcutProcess.redeemOrder();
        });

        $("#all").on('click', function () {
            CommonUtil.checkedOrUncheckedAll("all");
        });

        $("#repurchaseVol").on("change", function () {
            var balanceVol =  $("#modifyForm #balanceVol").text();
            var repurchaseVol =  $("#repurchaseVol").val();

            $("#modifyForm #repurchaseType").val(CommonUtil.getRepurchaseType(repurchaseVol, balanceVol));
        });

        /**
         * 双击客户号查询客户信息
         */
        $("#custNo").on('dblclick',function(){
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        FixedRedeemProdcutProcess.custRepurchaseMap = {};

        // 初始化基金代码多选
        CommonUtil.mutiSelect("selectFundCode",TmsCounterConfig.HIGH_QUERY_PRODUCT_CODEANDNAME_URL);

        // TA多选框初始化
        CommonUtil.mutiSelect("selectTaCode",TmsCounterConfig.HIGH_QUERY_TAINFO_CODEANDNAME_URL);

        // 初始化是否下单
        $("#selectOrderFlag").html(CommonUtil.selectOptionsHtml(CONSTANTS.REPURCHASE_ORDER_FLAG_MAP));

        // 初始化意向来源下拉框
        $("#selectSource").html (CommonUtil.selectOptionsHtml(CONSTANTS.REPURCHASE_SORUCE_MAP));
    },

    /**
     * 查询滚动赎回报表
     */
    queryData:function(){
        var uri = TmsCounterConfig.HIGH_QUERY_FIXED_REDEEM_REPORT_URL;
        var reqparamters = FixedRedeemProdcutProcess.buildQueryReqParms();

        reqparamters.page = 1;
        reqparamters.pageSize = 20;

        var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
        CommonUtil.ajaxPaging(uri, paramters, FixedRedeemProdcutProcess.processView, "pageView");
    },

    buildQueryReqParms:function(){
        var dataForm = FixedRedeemProdcutProcess.buildQueryConditon() || {};
        var reqparamters ={"queryCondition":JSON.stringify(dataForm)};
        var fundCodes = CommonUtil.arrToStr($("#selectFundCode").val() || '');
        var taCodes =  CommonUtil.arrToStr($("#selectTaCode").val() || '');
        reqparamters['fundCodes'] = fundCodes;
        reqparamters['taCodes'] = taCodes;

        return reqparamters;
    },

    buildQueryConditon:function(){
        var dataForm = $("#queryForm").serializeObject();
        $.each(dataForm,function(name,value){
            if(isEmpty(value)){
                delete dataForm[name];
            }
            if(name == 'selectFundCode'){
                delete dataForm[name];
            }

            if(name == 'selectTaCode'){
                delete dataForm[name];
            }

        });

        dataForm['txAcctNo'] = $("#custNo").val();

        return dataForm;
    },

    /**
     * 数据处理
     */
    processView:function(data){
        var respData = data.respData || {};
        var rsList = respData.fixedRedeemReports || [];
        var staticsData = {};
        staticsData.totalVol = respData.totalVol || "0";
        staticsData.redeemVol =  respData.redeemVol || "0";
        staticsData.repurchaseVol = respData.repurchaseVol || "0";
        staticsData.repurchasePeoples =  respData.repurchasePeoples || "0";
        staticsData.redeemPeoples =  respData.redeemPeoples || "0";
        staticsData.totalPeoples =  respData.totalPeoples || "0";

        // 合计
        var staticsHtml = "合计：总份额【"+staticsData.totalVol+"】，赎回份额【"+staticsData.redeemVol+"】，复购份额【"+staticsData.repurchaseVol+"】，赎回人数【"+staticsData.redeemPeoples +"】，复购人数【"+staticsData.repurchasePeoples+"】，总人数【"+staticsData.totalPeoples+"】"
        $("#statics").html(staticsHtml);

        $("#rs").empty();
        var appendHtml = "";

        if(isEmptyList(rsList)){
            appendHtml = '<tr class="text-c"><td colspan="35">暂无数据</td></tr>';
            $("#rs").append(appendHtml);
        }else{

            $(rsList).each(function(index,element){
                var repurchaseProtocolNo = "";
                if(!CommonUtil.isEmpty(element.repurchaseProtocolNo)){
                    repurchaseProtocolNo = element.repurchaseProtocolNo;
                    FixedRedeemProdcutProcess.custRepurchaseMap[repurchaseProtocolNo] = element;
                }else{
                    repurchaseProtocolNo = element.txAcctNo+"_"+element.productCode
                    FixedRedeemProdcutProcess.custRepurchaseMap[repurchaseProtocolNo] = element;
                }

                var tdList = [];
                var modifyBtn ='<a href="javascript:void(0);" class="btn radius btn-primary modify-repurchase"  repurchaseprotocolno="'+repurchaseProtocolNo+'">干预</a>'
                var checkBoxItem = '<input type="checkbox" class = "selectId" repurchaseprotocolno="'+repurchaseProtocolNo+'">';
                tdList.push('<td>'+checkBoxItem+'</td>');
                tdList.push('<td>'+formatData(element.taCode)+'</td>');// TA代码
                tdList.push('<td>'+formatData(element.productCode)+'</td>');// 基金代码
                tdList.push('<td>'+formatData(element.productName)+'</td>');// 基金名称
                tdList.push('<td>'+formatData(element.txAcctNo)+'</td>');// 客户号
                tdList.push('<td>'+formatData(element.custName)+'</td>');// 客户姓名
                tdList.push('<td>'+formatData(element.idNo)+'</td>');// 证件号
                tdList.push('<td>'+formatAmount(element.balanceVol)+'</td>');// 总份额
                tdList.push('<td>'+formatAmount(element.repurchaseVol)+'</td>');// 复购份额
                tdList.push('<td>'+formatAmount(element.redeemVol)+'</td>');// 赎回份额
                tdList.push('<td>'+formatAmount(element.frznVol)+'</td>');// 冻结份额
                tdList.push('<td>'+getMapValue(CONSTANTS.REPURCHASE_ORDER_FLAG_MAP, element.orderFlag, '--')+'</td>');// 是否已下单
                tdList.push('<td>'+formatData(element.taTradeDt)+'</td>');// 下单日期
                tdList.push('<td>'+getMapValue(CONSTANTS.YES_OR_NO_MAP, element.repurchaseFlag, '--')+'</td>');// 产品是否复购
                tdList.push('<td>'+formatData(element.endModifyDt, '--')+'</td>');// 截止前端修改日期
                tdList.push('<td>'+formatData(FixedRedeemProdcutProcess.getExpectedDueDt(element.expectedDueDt))+'</td>');
                tdList.push('<td>'+getMapValue(CONSTANTS.REPURCHASE_SORUCE_MAP, formatData(element.source))+'</td>');// 意向来源
                tdList.push('<td>'+modifyBtn+'</td>');//干预
                var appendHtml = '<tr class="text-c">'+tdList.join('') +'</tr>';

                $("#rs").append(appendHtml);
            });

            $(".modify-repurchase").off();

            $(".modify-repurchase").on('click', function () {
                var repurchaseProtocolNo = $(this).attr("repurchaseprotocolno");
                FixedRedeemProdcutProcess.fillCustRepurchaseProtocol(repurchaseProtocolNo);
                CommonUtil.layer_open("干预客户复购", "modifyContentId", FixedRedeemProdcutProcess.modify)
            });
        }
    },

    fillCustRepurchaseProtocol:function(repurchaseProtocolNo){
        var repurchaseProtocol = FixedRedeemProdcutProcess.custRepurchaseMap[repurchaseProtocolNo];
        var tdList =  $("#modifyContentId").find("td") || [];
        var inputList = $("#modifyContentId").find("input") || [];
        FixedRedeemProdcutProcess.clealForm();
        $.each(repurchaseProtocol, function (name, value) {
             $(tdList).each(function (index, element) {
                 var idName = $(element).attr("id");
                 if(name == idName){
                     if(idName == 'expectedDueDt'){
                         $(element).html(FixedRedeemProdcutProcess.getExpectedDueDt(value));
                     }else{
                         $(element).html(value);
                     }
                 }
             });

             $(inputList).each(function (index, element) {
                 var idName = $(element).attr("id");
                 if(name == idName){
                     $(element).val(value);
                 }
             });
         });
        // 查询客户信息
        FixedRedeemProdcutProcess.queryCustInfo(repurchaseProtocol.txAcctNo);
    },

    clealForm:function(){
        var tdList =  $("#modifyContentId").find("td") || [];
        var inputList = $("#modifyContentId").find("input") || [];
            $(tdList).each(function (index, element) {
                var idName = $(element).attr("id");
                if(!CommonUtil.isEmpty(idName)){
                    $(element).html("");
                }
            });

            $(inputList).each(function (index, element) {
                var idName = $(element).attr("id");
                if(!CommonUtil.isEmpty(idName)){
                    $(element).val("");
                }
            });
    },

    modify:function(){
        var modifyForm = $("#modifyForm").serializeObject();
        modifyForm["balanceVol"] =  $("#modifyForm #balanceVol").text();
        modifyForm["redeemVol"] = $("#modifyForm #redeemVol").text();
        modifyForm["frznVol"] = $("#modifyForm #frznVol").text();
        modifyForm["expectedDueDt"] = $("#modifyForm #expectedDueDt").text();

        var reqparamters ={"modifyRepurchaseProtocol":JSON.stringify(modifyForm)};
        var data = {};
        data.uri = TmsCounterConfig.HIGH_MODIFY_REPURCHASE_PROTOCOL_URL;
        data.isAsync = true;
        data.needLoad = true;
        data.reqparamters = reqparamters;
        CommonUtil.ajaxAndCallBack(data,FixedRedeemProdcutProcess.modifyprocessView);
    },

    modifyprocessView:function(data){
        var respCode = data.code || '';
        var respDesc = data.desc || '';
        if(CommonUtil.isSucc(respCode)){
            CommonUtil.layer_tip("干预成功");

            FixedRedeemProdcutProcess.queryData();
        }else{
            CommonUtil.layer_tip("干预失败，"+respDesc);
        }
    },

    getExpectedDueDt:function(expectedDueDtList){
        if(isEmptyList(expectedDueDtList)){
            return "--";
        }else{
            return expectedDueDtList.join("/");
        }
    },

    redeemOrder:function(){
        var repurchaseProtocolNos = [];
        $(".selectId").each(function(index, element){
            var isChecked = $(element).is(':checked');
            if(isChecked){
                var repurchaseProtocolNo = $(element).attr("repurchaseprotocolno");
                repurchaseProtocolNos.push(repurchaseProtocolNo);
            }
        });

        if(repurchaseProtocolNos.length <=0){
            CommonUtil.layer_tip("请选需要下单记录");
        }

        var reqparamters ={"repurchaseProtocolNos":repurchaseProtocolNos.join(",")};
        var data = {};
        data.uri = TmsCounterConfig.HIGH_EXPIRED_REDEEM_URL;
        data.isAsync = true;
        data.needLoad = true;
        data.reqparamters = reqparamters;
        CommonUtil.ajaxAndCallBack(data,FixedRedeemProdcutProcess.redeemOrderprocessView);
    },

    redeemOrderprocessView:function(data){
        var respCode = data.code || '';
        var respDesc = data.desc || '';
        var expiredRedeemRstList = data.expiredRedeemRstList || [];


        if(CommonUtil.isSucc(respCode)){
            if(expiredRedeemRstList.length > 0){
                var bodyHtml = '<table class="table table-border table-bordered table-hover table-bg table-sort">'+
                    '<thead>'+
                    '<tr class="text-c">'+
                    '<th>意向单号</th>'+
                    '<th>客户号</th>'+
                    '<th>基金代码</th>'+
                    '<th>资金账号</th>'+
                    '<th>赎回份额</th>'+
                    '<th>描述</th>'+
                    '</tr>'+
                    '</thead>'+
                    '<tbody id="rs">';
                $(expiredRedeemRstList).each(function (index, element) {
                    var tdList = [];
                    tdList.push('<td>'+formatData(element.repurchaseProtocolNo)+'</td>');
                    tdList.push('<td>'+formatData(element.txAcctNo)+'</td>');
                    tdList.push('<td>'+formatData(element.fundCode)+'</td>');
                    tdList.push('<td>'+formatData(element.cpAcctNo)+'</td>');
                    tdList.push('<td>'+formatData(element.appVol)+'</td>');
                    tdList.push('<td>'+formatData(element.memo)+'</td>');

                    bodyHtml += '<tr class="text-c">'+tdList.join('') +'</tr>';
                });
                bodyHtml +='</tbody></table>';
                CommonUtil.layer_open_content("下单失败",bodyHtml);
            }else{
                CommonUtil.layer_tip("下单成功");
            }

        }else{
            CommonUtil.layer_tip("下单失败，"+respDesc);
        }
    },
    /**
     * 定位客户信息
     */
    queryCustInfo:function(custNo){
        if(isEmpty(custNo)){
            return false;
        }
        var uri= TmsCounterConfig.QUERY_CUST_INFO_SUB_PAGE_URL  ||  {};
        var reqparamters = {};
        if(!isEmpty(custNo)){
            reqparamters.custNo = custNo;
        }

        var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);

        CommonUtil.ajaxAndCallBack(paramters, FixedRedeemProdcutProcess.processCustInfoView);
    },
    processCustInfoView:function(data){
        var bodyData = data.body || {};
        var respData = bodyData.respData || [];
        var custBaseInfoList = respData.custBaseInfoBeanList || [];
        var len = custBaseInfoList.length;
        $("#custName").empty();
        $("#idNo").empty();
        if(len <=0){
            return;
        } else{
            $("#custName").html(custBaseInfoList[0].custName);
            $("#idNo").html(custBaseInfoList[0].idNo);
        }
    },

};