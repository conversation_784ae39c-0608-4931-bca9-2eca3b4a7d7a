$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	RegularApplyBuy.checkOrder = {};
	RegularApplyBuy.init(checkId,custNo,disCode,idNo);
});

var RegularApplyBuy = {
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		RegularQueryCheckOrder.queryCheckOrderById(checkId,RegularApplyBuy.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_REGULAR_CONFIRM_URL, RegularCounterCheck.Abolish, RegularApplyBuy.checkOrder);
		});

	}, 
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		RegularApplyBuy.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(RegularApplyBuy.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(RegularApplyBuy.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		
		$("#confimBuyBtn").on('click',function(){
			BuyFund.confirm(RegularApplyBuy.checkOrder.dealAppNo);
		});
		
		QueryProductInfo.queryProductInfo(RegularApplyBuy.checkOrder.productCode,false);
		
		if($("#selectBank").length > 0){
			$("#selectBank").val(RegularApplyBuy.checkOrder.cpAcctNo);
		}
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(RegularApplyBuy.checkOrder.agentFlag);
		}
		
		if($("#originalFeeRate").length > 0){
			$("#originalFeeRate").val(RegularApplyBuy.checkOrder.feeRate);
		}
		
		if($("#riskFlag").length > 0){
			$("#riskFlag").html(CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, RegularApplyBuy.checkOrder.riskFlag, ''));
		}
		
		if($("#productCode").length > 0){
			$("#productCode").val(RegularApplyBuy.checkOrder.productCode);
		}
		
		if($("#applyAmount").length > 0){
			$("#applyAmount").val(CommonUtil.formatAmount(RegularApplyBuy.checkOrder.appAmt));
		}
		
		if($("#applyAmountCapital").length > 0){
			$("#applyAmountCapital").val(Main.format(CommonUtil.formatAmount(RegularApplyBuy.checkOrder.appAmt)));
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").val(RegularApplyBuy.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(RegularApplyBuy.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, RegularApplyBuy.checkOrder.consCode, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(RegularApplyBuy.checkOrder.transactorIdNo);
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(RegularApplyBuy.checkOrder.transactorName);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(parseInt(RegularApplyBuy.checkOrder.transactorIdType));
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(RegularApplyBuy.checkOrder.memo);
		}
	},
}
