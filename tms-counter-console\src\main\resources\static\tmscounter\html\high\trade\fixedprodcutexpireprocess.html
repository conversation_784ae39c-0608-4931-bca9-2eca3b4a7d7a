<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <!--[if lt IE 9]>
    <script type="text/javascript" src="lib/html5.js"></script>
    <script type="text/javascript" src="lib/respond.min.js"></script>
    <script type="text/javascript" src="lib/PIE_IE678.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css?v="+Math.random() />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/jquery-ui-1.9.2.custom.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.filter.css" />
    <title>固收产品到期处理</title>
</head>

<body>
<nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易申请 <span class="c-gray en">&gt;</span> 固收产品到期处理 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
<div class="page-container">
    <div class="containner_all">
        <form id="queryForm">

        <div class="container_box">
            <span class="normal_span">客户号</span><input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
            <span class="normal_span">TA</span>
            <select  title="Basic example" multiple="multiple" id="selectTaCode" name="selectTaCode" size="5"></select>

            <span class="normal_span ml10">基金代码</span>
            <select  title="Basic example" multiple="multiple" id="selectFundCode" name="selectFundCode" size="5"></select>
        </div>
        <div class="container_box">
            <span class="normal_span">是否已下单</span>
            <span class="select-box ml5 inline">
                    <select name="orderFlag" id= "selectOrderFlag" class="select">
                    </select>
                </span>

            <span class="normal_span">意向来源</span>
            <span class="select-box ml5 inline">
                    <select name="source" class="select" id="selectSource">
                    </select>
                </span>

        </div>

            <div class="container_box">
                <span class="normal_span">产品是否复购</span>
                <span class="select-box ml5 inline">
                    <select name="repurchaseFlag" class="select">
                    	<option value="">全部</option>
                    	<option value="1">是</option>
                    	<option value="0">否</option>
                    </select>
                </span>

                <span class="normal_span">意向类型</span>
                <span class="select-box ml5 inline">
                    <select name="repurchaseVolType" class="select">
                    	<option value="">全部</option>
                    	<option value="1">复购意向</option>
                    	<option value="0">赎回意向</option>
                    </select>
                </span>
            </div>

        </form>

        <p class="mt30 text-c">
            <a href="javascript:void(0);" class="btn radius btn-primary" id="queryBtn">查询</a>
            <a href="javascript:void(0);" class="btn radius btn-primary ml20 resetBth" id="resetBth">重置</a>
            <a href="javascript:void(0);" class="btn radius btn-primary" id="order">下单</a>
        </p>
    </div>

    <p class="main_title mt30">数据结果</p>
    <div class="sj_box ">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
            <thead>
            <tr class="text-c">
                <th><input type="checkbox" id="all">全选</th>
                <th>TA代码</th>
                <th>基金代码</th>
                <th>基金名称</th>
                <th>客户号</th>
                <th>客户姓名</th>
                <th>证件号码</th>
                <th>总份额</th>
                <th>复购份额</th>
                <th>赎回份额</th>
                <th>冻结份额</th>
                <th>是否已下单</th>
                <th>下单日期</th>
                <th>产品是否复购</th>
                <th>截止前端修改日期</th>
                <th>预计到期日</th>
                <th>意向来源</th>
                <th>干预</th>
            </tr>
            </thead>
            <tbody id="rs">
            </tbody>
        </table>
    </div>

    <div class="clear page_all">
        <div class="fl page_sj mt5" id="statics">合计：总份额【】，赎回份额【】，复购份额【】，赎回人数【】，复购人数【】，总人数【】</div>
        <div class="fy_part fr mt10" id="pageView"></div>
    </div>

</div>


<div id="modifyContentId">

    <form id="modifyForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                <tr class="text-c">
                    <td>TA代码</td>
                    <td id="taCode">
                    </td>
                    <td>基金代码</td>
                    <td class="readText"> <input id ="productCode"  name="fundCode" readonly="readonly" datatype="s" errormsg=""/> </td>
                </tr>

                <tr class="text-c">
                    <td>基金名称</td>
                    <td id ="productName">
                    </td>
                    <td>客户号</td>
                    <td class="readText"> <input id ="txAcctNo"  readonly="readonly" name="txAcctNo"/></td>
                </tr>

                <tr class="text-c">
                    <td>客户姓名</td>
                    <td id ="custName">
                    </td>
                    <td>证件号</td>
                    <td class="readText" id="idNo"></td>
                </tr>

                <tr class="text-c">
                    <td>总份额</td>
                    <td id ="balanceVol" >
                        <input name="balanceVol" type="text" datatype="s" errormsg=""/>
                    </td>
                    <td>赎回份额</td>
                    <td class="readText" id="redeemVol">
                        <input name="redeemVol" type="text" datatype="s" errormsg=""/>
                    </td>
                </tr>

                <tr class="text-c">
                    <td>冻结份额</td>
                    <td class="readText" id="frznVol"></td>

                    <td>预计到期日</td>
                    <td class="readText" id="expectedDueDt"></td>
                </tr>

                <tr class="text-c">
                    <td>复购份额</td>
                    <td>
                        <input id ="repurchaseVol" name="repurchaseVol" type="text" datatype="s" errormsg=""/>
                        <input id ="repurchaseProtocolNo" name="repurchaseProtocolNo" type="hidden" datatype="s" errormsg=""/>
                        <input id ="disCode" name="disCode" type="hidden" datatype="s" errormsg=""/>
                        <input id ="repurchaseType" name="repurchaseType" type="hidden" datatype="s" errormsg=""/>
                    </td>

                </tr>

                </tbody>
            </table>
        </div>
    </form>

</div>

<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
<script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
<script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
<script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
<script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
<script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
<script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/ui-1.2.1/jquery-ui.min.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.filter.js"></script>
<script type="text/javascript" src="../../../js/baseconfig.js"></script>
<script type="text/javascript" src="../../../js/common.js"></script>
<script type="text/javascript" src="../../../js/config.js"></script>
<script type="text/javascript" src="../../../js/commonutil.js"></script>
<script type="text/javascript" src="../../../js/valid.js"></script>
<script type="text/javascript" src="../../../js/high/conscode.js"></script>
<script type="text/javascript" src="../../../js/high/query/querycustinfosubpage.js"></script>
<script type="text/javascript" src="../../../js/high/query/queryhighproductinfosubpage.js"></script>
<script type="text/javascript" src="../../../js/high/common/init.js"></script>
<script type="text/javascript" src="../../../js/high/common/custinfo.js"></script>
<script type="text/javascript" src="../../../js/high/query/queryhighproduct.js"></script>
<script type="text/javascript" src="../../../js/high/trade/fixedprodcutexpireprocess.js?v=3.1.89_1"></script>

</body>

</html>