<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - limitations 
	under the License. - See the License for the specific language governing 
	permissions and -->

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- 柜台认申购未审核订单提交 -->
    <dubbo:reference registry="tms-counter-console"  id="regularSubmitCheckOrderFacade" interface="com.howbuy.tms.regular.batch.facade.trade.submitcheckorder.SubmitCheckOrderFacade" check="false"/>

    <!-- 查询审核订单 -->
    <dubbo:reference registry="tms-counter-console"  id="regularQuerySubmitCheckOrderFacade" interface="com.howbuy.tms.regular.batch.facade.query.querysubmitcheckorder.QuerySubmitCheckOrderFacade" check="false"/>

    <!-- 柜台认申购未审核订单提交 -->
    <dubbo:reference registry="tms-counter-console"  id="counterRegularPurchaseFacade" interface="com.howbuy.tms.regular.batch.facade.trade.counterpurchase.CounterPurchaseFacade" check="false"/>
    <!-- 认申购验证 -->
    <dubbo:reference registry="tms-counter-console"  id="regularSubspurValidateFacade" interface="com.howbuy.tms.regular.orders.facade.trade.trustreceipt.validate.SubspurValidateFacade" check="false"/>
    <!-- 撤单验证 -->
    <dubbo:reference registry="tms-counter-console"  id="regularCancelOrderValidateFacade" interface="com.howbuy.tms.regular.orders.facade.trade.trustreceipt.validate.CancelOrderValidateFacade" check="false"/>
    <!-- 认申购 -->
    <dubbo:reference registry="tms-counter-console"  id="regularSubsPurchaseOragnizeFacade" interface="com.howbuy.tms.regular.orders.facade.trade.trustreceipt.subspurchase.oragnize.SubsPurchaseOragnizeFacade" check="false"/>
    <!-- 撤单 -->
    <dubbo:reference registry="tms-counter-console"  id="regularCancelOrderFacade" interface="com.howbuy.tms.regular.orders.facade.trade.trustreceipt.withdraw.CancelOrderFacade" check="false"/>
    <!--查询用户撤单信息-->
    <dubbo:reference registry="tms-counter-console"  id="regularQueryCustCancelDealFacade" interface="com.howbuy.tms.regular.orders.facade.query.querycustcanceldeal.QueryCustCancelDealFacade" check="false"/>

    <!--查询用户撤单信息-->
    <dubbo:reference registry="tms-counter-console"  id="regularCounterTradeCancelFacade" interface="com.howbuy.tms.regular.batch.facade.trade.countertradecancel.CounterTradeCancelFacade" check="false"/>

    <!--查询税延账户对应关系-->
    <dubbo:reference registry="pension-order-remote"  id="tms.queryAccountRelationFacade" interface="com.howbuy.tms.pension.order.client.facade.query.QueryAccountRelationFacade" check="false"/>
</beans>