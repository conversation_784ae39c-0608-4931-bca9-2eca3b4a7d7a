/**
*查询基金分红分式
*<AUTHOR>
*@date 2017-09-19 16:17
*/

var QueryFundDivInfo ={
		/**
		 * 查询基金分红方式
		 */
		queryFundDivInfo:function(disCode,custNo,fundCode){
			var  uri= TmsCounterConfig.QUERY_FUND_MODIFY_DIV_URL ||  {};
			if(!fundCode){
				fundCode = $("#fundCode").val();
			}
			var reqparamters = {"disCode":disCode,"custNo":custNo,"fundCode":fundCode};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryFundDivInfo.queryFundDivInfoCallBack);
		},
		
		/**
		 * 处理基金分红方式信息
		 */
		queryFundDivInfoCallBack:function(data){
			var bodyData = data.body || {};
			var fundDivList = bodyData.fundDivList || {};
			var fundDiv = fundDivList[0];
			QueryFundDivInfo.fundDiv = fundDiv;
			
			if(CommonUtil.isEmpty(fundDiv.fundCode)){
				CommonUtil.layer_tip("没有查询到此产品");
				return false;
			}
			
			if('5' == fundDiv.fundStat || '4' == fundDiv.fundStat){
				
			}
			
			/**
			 * 券商
			 */
			if("7" == fundDiv.fundType  || '9' == fundDiv.fundType){
				CommonUtil.layer_tip("该基金不是普通公募基金");
				return false;
			}
			
			/**
			 * 理财
			 */
			if("2" == fundDiv.fundType &&  '21'== fundDiv.fundSubType){
				CommonUtil.layer_tip("该基金不是普通公募基金");
				return false;
			}
			
			if($("#fundName").length > 0){
				$("#fundName").html(fundDiv.fundAttr || '');
			}
			
			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundDiv.fundStat));	
			}
			
			if($("#fundDivMode").length > 0){
				$("#fundDivMode").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,fundDiv.divMode, '--'));  
			}
			
			if('0' == fundDiv.divMode){
				$("#targetDivMode").val(1);  
			}else {
				$("#targetDivMode").val(0);  
			}
		}
};