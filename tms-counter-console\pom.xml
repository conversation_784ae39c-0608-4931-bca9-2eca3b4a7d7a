<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.tms</groupId>
		<artifactId>tms-counter</artifactId>
		<version>20250707-001-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>tms-counter-console</artifactId>
	<packaging>jar</packaging>
	<name>tms-counter-console</name>

	<dependencies>

		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-service</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-impl-nfs</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-impl-webdav</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>pension-order-client</artifactId>
		</dependency>
	
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
	    	<artifactId>log4j-core</artifactId>
	    	<version>2.15.0</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

   		<dependency>
	    	<groupId>org.apache.logging.log4j</groupId>
	    	<artifactId>log4j-api</artifactId>
	    	<version>2.15.0</version>
   		</dependency>


		<dependency>
			<groupId>org.jboss.netty</groupId>
			<artifactId>netty</artifactId>
		</dependency>
		<!-- howbuy middleware -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>netty</artifactId>
					<groupId>org.jboss.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>dubbo</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
				<exclusion>
					<artifactId>utils</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>zookeeper</artifactId>
					<groupId>org.apache.zookeeper</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>nacos-client</artifactId>
					<groupId>com.alibaba.nacos</groupId>
				</exclusion>
				<exclusion>
					<artifactId>howbuy-message-client</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-asyncqueue</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-session</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-cachemanagement</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- shiro -->
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-all</artifactId>
			<version>1.3.2</version>
		</dependency>

		<dependency>
			<groupId>net.mingsoft</groupId>
			<artifactId>shiro-freemarker-tags</artifactId>
			<version>0.1</version>
			<exclusions>
				<exclusion>
					<artifactId>javax.servlet-api</artifactId>
					<groupId>javax.servlet</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>net.unicon.springframework</groupId>
			<artifactId>springframework-addons</artifactId>
			<version>1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-core</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<!-- log4j2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
			<version>2.15.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>2.15.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
			<version>2.15.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-counter-service</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.mortbay.jetty</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-cachemanagement-1</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-cachemanagement</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<artifactId>tms-counter-common</artifactId>
			<groupId>com.howbuy.tms</groupId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>javax.servlet-api</artifactId>
					<groupId>javax.servlet</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<artifactId>tms-counter-client</artifactId>
			<groupId>com.howbuy.tms</groupId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
	    	<artifactId>product-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>batch-center-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-batch-center-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>webUtil</artifactId>
			<version>${com.howbuy.webUtil.version}</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.howbuy</groupId>
					<artifactId>howbuy-cachemanagement</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.howbuy.finonline</groupId>
			<artifactId>fin-online-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.boot</groupId>
			<artifactId>howbuy-boot-actuator</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-boot-starter-actuator</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-to-slf4j</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<finalName>tms-counter-console</finalName>
		<resources>
			<resource>
				<directory>${basedir}/src/main/resources/</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.woff</exclude>
					<exclude>**/*.woff2</exclude>
					<exclude>**/*.ttf</exclude>
					<exclude>**/*.xls</exclude>
					<exclude>**/*.xlsx</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>${basedir}/src/main/resources/</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.woff</include>
					<include>**/*.woff2</include>
					<include>**/*.ttf</include>
					<include>**/*.xls</include>
					<include>**/*.xlsx</include>
				</includes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
	                <archive>
						<manifest>
							<useUniqueVersions>false</useUniqueVersions>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>com.howbuy.tms.counter.TmsCounterConsoleApplication</mainClass>
						</manifest>
	                    <manifestEntries>
	                        <Package-Stamp>${parelease}</Package-Stamp>
	                    </manifestEntries>
	                </archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<appendAssemblyId>false</appendAssemblyId>
					<finalName>${project.build.finalName}</finalName>
					<descriptors>
						<descriptor>assembly.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy</id>
						<phase>install</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/${project.build.finalName}/remote/lib</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
					<encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            
        </plugins>
	</build>
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>
					maven-project-info-reports-plugin
				</artifactId>
			</plugin>
		</plugins>
	</reporting>
	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>howbuy-releases</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>howbuy-snapshots</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
	

</project>