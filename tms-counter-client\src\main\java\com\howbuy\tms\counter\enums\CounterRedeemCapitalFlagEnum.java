/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.enums;
/**
 * @description:(柜台赎回去向) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年10月10日 下午6:33:28
 * @since JDK 1.6
 */
public enum CounterRedeemCapitalFlagEnum {
    /**
     * 储蓄罐(人工选择)
     */
    TO_PIGGY_USER("0","储蓄罐(人工选择)"),
    /**
     * 银行卡(人工选择)
     */
    TO_BANK_USER("1","银行卡(人工选择)"),
    /**
     * 储蓄罐(协议默认)
     */
    TO_PIGGY_DEFAULT("3","储蓄罐(协议默认)"),
    /**
     * 银行卡(协议默认)
     */
    TO_BANK_DEFAULT("2","银行卡(协议默认)");
    
    private String code;
    private String desc;
    
    CounterRedeemCapitalFlagEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    
}

