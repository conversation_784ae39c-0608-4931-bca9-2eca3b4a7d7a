/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.util.List;

import java.io.Serializable;

/**
 * @description:(查询预约列表ReqDto) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年1月18日 下午5:40:17
 * @since JDK 1.6
 */
public class QueryPreBookListReqDto implements Serializable{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 7030741335173266769L;
    
    /**
     * 交易账号
     */
    private String txAcctNo;
    
    /**
     * 预约类型 1-纸质成单； 2-电子成单； 3-无纸化交易
     */
    private List<String> preType;
    /**
     * 交易类型 1-购买 2-追加 3-赎回
     */
    private List<String> tradeType;
    /**
     * 预约单状态:1-未确认；2-已确认；4-已撤销
     */
    private List<String> preBookState;

    /**
     * 无纸化预约单状态1-未确认；2-已确认；3-驳回
     */
    private List<String> noPaperState;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 证件号
     */
    private String idNo;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品名称
     */
    private String fundName;
    /**
     * 预约号
     */
    private String preId;
    /**
     * 开始日期
     */
    private String startDt;
    /**
     * 结束日期
     */
    private String endDt;
    /**
     * 页码
     */
    private int pageNo;
    
    /**
     * 每页记录数
     */
    private int pageSize;

    public List<String> getPreType() {
        return preType;
    }

    public void setPreType(List<String> preType) {
        this.preType = preType;
    }

    public List<String> getTradeType() {
        return tradeType;
    }

    public void setTradeType(List<String> tradeType) {
        this.tradeType = tradeType;
    }

    public List<String> getPreBookState() {
        return preBookState;
    }

    public void setPreBookState(List<String> preBookState) {
        this.preBookState = preBookState;
    }

    public List<String> getNoPaperState() {
        return noPaperState;
    }

    public void setNoPaperState(List<String> noPaperState) {
        this.noPaperState = noPaperState;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getPreId() {
        return preId;
    }

    public void setPreId(String preId) {
        this.preId = preId;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }
    
    

}

