package com.howbuy.tms.counter.common.enums;

public enum WebDevEnum {

    /**
     * 开户提前上传身份，临时文件存储
     */
    STORE_CONFIG("idCard", ""),
    
    /**
     * 证件文件 上传(账户中心身份证实际存储地址) 虚拟映射
     */
    ID_CARD_UP_FILE("acc.idCardUpFile", "/cim_web_server/center_feature"),
    
    /**
     * 证件文件 上传(账户中心身份证实际存储地址)
     */
    ID_CARD_UP_FILE_MAPPING("acc.idCardUpFile", "/cim_web_server"),
    
    
    /**
     * 人脸识别匹配成功后上传的图片(账户中心身份证实际存储地址)
     */
    FACE_PIC_UP_FILE("acc.facePicUpFile", "/cim_web_server/face_center_feature"),

    /**
     * 换卡视频认证
     */
    VIDEO_PROMISE_FILE("acc.videoPromiseFile", "/cim_web_server/video_promise_file"),

    /**
     * 双录材料
     * /data/files/middle/double_record_file/202409/acec8020-f58b-4649-8b52-4c2b0f53f7c6-海外赢双录.mp4
     * http://webdav-nfs01.inner.ehowbuy.com/middle/retail/202409/acec8020-f58b-4649-8b52-4c2b0f53f7c6-海外赢双录.mp4
     */
    DOUBLE_RECORD_FILE("DOUBLE_RECORD_FILE", "/middle/double_record_file"),




    /**
     * 换卡材料
     * /data/files/middle/exchangecard/20250617/1449495635/企业微信截图_1750153047574.png
     * http://webdav-nfs01.inner.ehowbuy.com/middle/exchangecard/20250617/1449495635/企业微信截图_1750153047574.png
     */
    EXCHANGE_CARD("EXCHANGE_CARD", ""),



    ;

    private String code;

    /**
     * 原/data/files/xxx替换的配置 路径
     */
    private String name;

    private WebDevEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    
}
