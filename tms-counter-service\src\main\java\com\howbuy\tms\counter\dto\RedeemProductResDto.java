package com.howbuy.tms.counter.dto;

import org.checkerframework.checker.guieffect.qual.SafeEffect;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/30 20:04
 * @since JDK 1.8
 */
public class RedeemProductResDto implements Serializable {

    private static final long serialVersionUID = 1817568758018328108L;
    private String fundName;
    private String fundCode;
    private String fundStat;
    private String fundCanRedeem;
    private String fundReason;
    private BigDecimal redeemVol;
    private BigDecimal redeemAmt;
    private String sameCode;
    private String sameName;
    private String sellRatio;
    private String holdRatio;
    private String redeemDetail;
    private BigDecimal minAcctVol;
    private String fundOtherVolLTMinAcctVol;
    private String canRedeemNew;

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundStat() {
        return fundStat;
    }

    public void setFundStat(String fundStat) {
        this.fundStat = fundStat;
    }

    public String getFundCanRedeem() {
        return fundCanRedeem;
    }

    public void setFundCanRedeem(String fundCanRedeem) {
        this.fundCanRedeem = fundCanRedeem;
    }

    public String getFundReason() {
        return fundReason;
    }

    public void setFundReason(String fundReason) {
        this.fundReason = fundReason;
    }

    public BigDecimal getRedeemVol() {
        return redeemVol;
    }

    public void setRedeemVol(BigDecimal redeemVol) {
        this.redeemVol = redeemVol;
    }

    public BigDecimal getRedeemAmt() {
        return redeemAmt;
    }

    public void setRedeemAmt(BigDecimal redeemAmt) {
        this.redeemAmt = redeemAmt;
    }

    public String getSameCode() {
        return sameCode;
    }

    public void setSameCode(String sameCode) {
        this.sameCode = sameCode;
    }

    public String getSameName() {
        return sameName;
    }

    public void setSameName(String sameName) {
        this.sameName = sameName;
    }

    public String getSellRatio() {
        return sellRatio;
    }

    public void setSellRatio(String sellRatio) {
        this.sellRatio = sellRatio;
    }

    public String getHoldRatio() {
        return holdRatio;
    }

    public void setHoldRatio(String holdRatio) {
        this.holdRatio = holdRatio;
    }

    public String getRedeemDetail() {
        return redeemDetail;
    }

    public void setRedeemDetail(String redeemDetail) {
        this.redeemDetail = redeemDetail;
    }

    public BigDecimal getMinAcctVol() {
        return minAcctVol;
    }

    public void setMinAcctVol(BigDecimal minAcctVol) {
        this.minAcctVol = minAcctVol;
    }

    public String getFundOtherVolLTMinAcctVol() {
        return fundOtherVolLTMinAcctVol;
    }

    public void setFundOtherVolLTMinAcctVol(String fundOtherVolLTMinAcctVol) {
        this.fundOtherVolLTMinAcctVol = fundOtherVolLTMinAcctVol;
    }

    public String getCanRedeemNew() {
        return canRedeemNew;
    }

    public void setCanRedeemNew(String canRedeemNew) {
        this.canRedeemNew = canRedeemNew;
    }
}