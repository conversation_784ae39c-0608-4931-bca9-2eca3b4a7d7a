/**
*赎回审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
    if('crm' == CommonUtil.getParam("source")){
        viewType = '1';
    }
	
	CounterCheck.initBtn(viewType, SellCheck.checkOrder);
	
	var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
	$(".selectconsCode").html(selectConsCodesHtml);

    // 查询订单
    CounterCheck.queryCounterDealOrder(viewType, SellCheck.queryCounterDealOrderCallBack, null);
	// 份额格式化
	$("#appVolCheck").on("blur", function(){
		$("#appVolCheck").val(CommonUtil.formatAmount($("#appVolCheck").val()));
	});
});

var SellCheck = {
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};
		var counterOrder = bodyData.counterOrder || {};
		var checkOrder = CounterCheck.counterOrderDto || {};//订单信息
		var highProduct = counterOrder.counterHighProductBaseBean || {};//产品信息
		var productAppointInfo = counterOrder.counterProductAppointmentInfoBean || {};//产品预约开放日信息
		var dtlBeanList = counterOrder.dtlBeanList || [];//客户持仓信息
		var appointList = bodyData.appointList || [];//预约信息
		var custInfofiList = bodyData.custInfofiList || [];//客户信息
        var orderFile = bodyData.orderFile || {};// CRM线上资料
		
		SellCheck.checkOrder = checkOrder;//赎回订单信息
		CounterCheck.checkOrder = checkOrder;//审核订单信息
		CounterCheck.dtlBeanList = dtlBeanList;//审核订单明细信息

		SellCheck.buildSellDealInfo(checkOrder);//赎回订单信息
		SellCheck.buildCustBalanceVol(dtlBeanList);//用户持有份额信息
		
		ViewCounterDeal.buildAppointmentInfo(appointList);//预约信息
		ViewCounterDeal.buildProductAppointmentInfo(productAppointInfo);//预约开放日历信息
		ViewCounterDeal.buildFundInfo(highProduct);//产品信息
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料

		SellCheck.createCheckInput(dtlBeanList);// 生成复核输入区
		
	},
	/**
	 * 创建赎回校验输入框
	 */
	createCheckInput:function(dtlBeanList) {
		let bankOptions = '<option value="">请选择</option>';
		$(dtlBeanList).each(function (index, element) {
			bankOptions += '<option value="'+element.bankAcctNo+'">' + element.bankAcctNo + '</option>';
		});
		let html = "";
		$(dtlBeanList).each(function (index) {
			html = html + '<tr id="checkVolRow' + index + '">' +
				'             <td>' +
				'                 <span>银行卡号：' +
				'                   <span class="select-box inline">' +
                '                       <select type="text" class="select" name="bankAcct">' + bankOptions + '</select>' +
                '                   </span>' +
            '                     （仅申购、赎回业务）</span>' +
				'             </td>' +
				'             <td>' +
				'                 <span>份额：<input type="text" placeholder="请确认输入" name="appVol">（仅申购、赎回业务）</span>' +
				'             </td>' +
				'          </tr>';
		});
		// 仅合并赎回时展示总份额校验
		if (dtlBeanList.length > 1) {
			html = html + '<tr>' +
				'		     <td>' +
				'              <span>份额：<input type="text" placeholder="请确认输入" name="totalAppVol">（仅申购、赎回业务）</span>' +
				'            </td>' +
				'		   </tr>';
		}
		$("#checkResult table").append(html);
	},
	/**
	 * 初始化下拉框
	 */
	initSelect:function(){
		//初始化异常赎回
		var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP,'0');
		$(".selectUnusualTransType").html(selectTransTypeHtml);
		
		//回款去向
		var selectRedeemDirHtml = CommonUtil.selectOptionsHtml(CONSTANTS.REDEEM_DIR_MAP);
		$(".redeemCapitalFlag").html(selectRedeemDirHtml);
		
		// 巨额赎回顺延标识 默认顺延
		var selectLargeRedmFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.LARGE_REDM_FLAG_MAP, '1');
		$(".selectLargeRedmFlag").html(selectLargeRedmFlagHtml);
	},
	
	/**
	 * 用户持有份额信息
	 * @param dtlBeanList
	 */
	buildCustBalanceVol:function(dtlBeanList){
		$(dtlBeanList).each(function (index, data) {
			let html = SellCheck.generateSellFormTable(index, data);
			$("#sellConfirmFormFoot").before(html);
		});
	},
	/** 生成赎回编辑表单的table */
	generateSellFormTable:function(sellFormTableId, data) {
		var table = '<tbody id="'+sellFormTableId+'">' +
			'              <tr class="text-c">' +
			'                <td>银行卡账号</td>' +
			'                <td class="readText bankAcctInfoId">'+data.bankAcctNo+'</td>' +
			'                <td>当前可用份额</td>' +
			'                <td class="readText availVol">'+CommonUtil.formatAmount(data.availVol)+'</td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'                <td>当前总份额</td>' +
			'                <td class="readText balanceVol">'+CommonUtil.formatAmount(data.balanceVol)+'</td>' +
			'                <td>申请份额（份）</td>' +
			'                <td class="readText"><input type="text" disabled="disabled" value="'+CommonUtil.formatAmount(data.appVol)+'"></td>' +
			'                </td>' +
			'              </tr>' +
			'              <tr class="text-c">' +
			'                <td>冻结份额</td>' +
			'                <td class="readText unconfirmedVol">'+CommonUtil.formatAmount(data.unconfirmedVol)+'</td>' +
			'                <td>费用</td>' +
			'                <td class="fee"></td>' +
			'              </tr>' +
			'			   <tr class="text-c">' +
			'				<td>交易回款方式</td>' +
			'               <td class="readText redeemCapitalFlag">'+CommonUtil.getMapValue(CONSTANTS.REDEEM_DIR_MAP, data.redeemCapitalFlag, '')+'</td>' +
			'               <td></td>' +
			'               <td></td>' +
			'              </tr>';
		if(data.redeemCapitalFlag == 5 || data.redeemCapitalFlag == 6 || data.redeemCapitalFlag == 7){
				table += '              <tr class="text-c">' +
					'                <td>回可用金额</td>' +
					'                <td class="readText refundFinaAvailAmt">'+CommonUtil.formatAmount(data.refundFinaAvailAmt)+'</td>' +
					'                <td>回可用备注</td>' +
					'                <td class="readText refundFinaAvailMemo">'+data.refundFinaAvailMemo+'</td>' +
					'              </tr>';
		}
		table +='        </tbody>';
		return table;
	},
	
	/**
     * 赎回订单信息
     * @param checkOrder
     */
    buildSellDealInfo:function(checkOrder){
    	$("#fundCodeId").html(checkOrder.fundCode);// 基金代码
    	$("#appDt").val(checkOrder.appDt);// 申请日期
    	$("#appTm").val(checkOrder.appTm);// 申请时间
		// 总份额
		$("#volSum").html(CommonUtil.formatAmount(checkOrder.appVol));
		$("#convertVolSum").html(CommonUtil.digit_vol_uppercase(checkOrder.appVol));
    	
    	// 初始化下拉框
    	SellCheck.initSelect();
    	
    	$(".selectUnusualTransType").val(checkOrder.unusualTransType);//异常赎回标识
    	$('.redeemCapitalFlag').val(checkOrder.redeemCapitalFlag);// 赎回回款方向
    	$('.selectLargeRedmFlag').val(checkOrder.largeRedmFlag);// 巨额赎回顺延标识
    	
    }
	
};
