$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckExchange.checkOrder = {};	 
	CheckExchange.init(checkId,custNo,disCode,idNo);
});

var CheckExchange = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,CheckExchange.queryCheckOrderByIdBack);
		
		$("#returnBtn").on('click',function(){
			CheckExchange.confirm(CounterCheck.Faild);
		});
		
		$("#succBtn").on('click',function(){
			CheckExchange.confirm(CounterCheck.Succ);
		});
		
		$("#fundCode").on('blur',function(){
			QueryFundInfo.queryFundInfo();
		});
		
		$("#tFundCode").on('blur',function(){
			QueryFundInfo.queryTFundInfo();
		});
		
		$("#appVol").blur(function(){
			$("#appVol").val(CommonUtil.formatAmount($("#appVol").val()));
		});

        $("#downloadFile").on('click',function(){
            CheckExchange.download("downloadFile");
        });

        $("#openFile").on('click',function(){
            CheckExchange.download("openFile");
        });

        $("#closeVideo").on('click',function(){
            CheckExchange.closeVideo();
        });
	}, 
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var exchangeConfirmForm = $("#exchangeConfirmForm").serializeObject();
		
		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckExchange.checkFaildDesc = $("#checkFaildDesc").val();
		}else{
			var validRst = Valid.valiadateFrom($("#exchangeConfirmForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}
			
			var checkResultReply = CheckExchange.checkExchangeValid(exchangeConfirmForm,CheckExchange.checkOrder);
			if(!checkResultReply.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(checkResultReply.tip);
				return false;
			}
		}
		
		var reqparamters ={"checkFaildDesc":CheckExchange.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckExchange.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckExchange.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},

	checkExchangeValid:function(checkForm , orderForm){
		var fundCode = checkForm.fundCode || '';
		var tFundCode = checkForm.tFundCode || '';
		var cpAcctNo = checkForm.cpAcctNo || '';
		var appVol = CommonUtil.unFormatAmount(checkForm.appVol) || '';
		var largeRedmFlag = checkForm.largeRedmFlag || '';

		var result = {"status":true,"tip":''};
		if(fundCode != (orderForm.fundCode || '')){
			result.status = false;
			result.tip = "转出基金代码不匹配，请重新确认";
			return result;
		}
		
		if(tFundCode != (orderForm.tFundCode || '')){
			result.status = false;
			result.tip = "转入基金代码不匹配，请重新确认";
			return result;
		}
		
		if(largeRedmFlag != (orderForm.largeRedmFlag || '')){
			result.status = false;
			result.tip = "巨额赎回不匹配，请重新确认";
			return result;
		}
		
		if(appVol != (orderForm.appVol || '')){
			result.status = false;
			result.tip = "申请份额 不匹配，请重新确认";
			return result;
		}
		
		var orderCpAcctNo = orderForm.cpAcctNo || '';
		if(cpAcctNo != orderCpAcctNo){
			result.status = false;
			result.tip = "银行卡不匹配，请重新确认";
			return result;
		}
		
		return result;
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckExchange.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(CheckExchange.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}

	
		if(CheckExchange.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}

		//转出
		QueryFundInfo.queryFundInfo(CheckExchange.checkOrder.fundCode);
		QueryFundInfo.queryCustHodlInfo(CheckExchange.checkOrder);
		
		//转入
		QueryFundInfo.queryTFundInfo(CheckExchange.checkOrder.tFundCode);
		
		if($("#riskFlag").length > 0){
			$("#riskFlag").html(CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, CheckExchange.checkOrder.riskFlag, ''));
		}
		
		if($("#selectBank").length > 0){
			$("#selectBank").val(CheckExchange.checkOrder.cpAcctNo);
		}
		
		var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
		var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
		$("#availVol").html(selectDtl.availVol);
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(CheckExchange.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").html(CheckExchange.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckExchange.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(CheckExchange.checkOrder.transactorName);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckExchange.checkOrder.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(CheckExchange.checkOrder.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(CheckExchange.checkOrder.memo);
		}

        if($("#filePath").length > 0 && typeof(CheckExchange.checkOrder.doubleRecordFilePath)!="undefined"){
            $("#filePath").attr("data", CheckExchange.checkOrder.doubleRecordFilePath);
            $("#downloadFile").show();
            $("#openFile").show();
        }

    },

    download:function (type) {
        var url = TmsCounterConfig.GET_DOUBLE_RECORD_FILE_URL;
        var filePath = $("#filePath").attr("data");
        var fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        var xhr = new XMLHttpRequest();
        xhr.open('POST', url, true); // 设置请求方式POST方式
        xhr.responseType = "blob"; // 返回类型blob
        xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded");//设置请求内容类型
        // 请求回调函数
        xhr.onload = function (data) {
            if (this.status === 200) {
                var content  = this.response;
                var blob = new Blob([content]);
                var videoUrl = URL.createObjectURL(blob);
                if(type=="downloadFile"){
                    var elink = document.createElement('a');
                    elink.download = fileName;
                    elink.style.display = 'none';
                    elink.href = videoUrl;
                    document.body.appendChild(elink);
                    elink.click();
                    document.body.removeChild(elink);
                } else {
                    $("#videoUrl").attr("src", videoUrl);
                    document.getElementsByClassName('cover2')[0].classList.remove('hide2');
                    document.getElementsByClassName('modal2')[0].classList.remove('hide2');
                }
            }
        };
        // 发送ajax请求
        xhr.send("filePath="+filePath);
    },

    closeVideo:function () {
        $("#videoUrl")[0].pause();
        document.getElementsByClassName('cover2')[0].classList.add('hide2');
        document.getElementsByClassName('modal2')[0].classList.add('hide2');
    }

}
