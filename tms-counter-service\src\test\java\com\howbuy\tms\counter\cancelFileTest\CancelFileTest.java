package com.howbuy.tms.counter.cancelFileTest;

import com.howbuy.tms.counter.service.out.impl.TmsCounterOutServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * @Description:资料作废
 * @Author: yun.lu
 * Date: 2023/12/1 17:25
 * Version: 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({TmsCounterOutServiceImpl.class})
@PowerMockIgnore({"javax.security.*","javax.net.ssl.*", "javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class CancelFileTest {
    @InjectMocks
    private TmsCounterOutServiceImpl tmsCounterOutService;

    @Test
    public void testCancelFile() {
        tmsCounterOutService.cancelFile("304200202205240000008455");
    }

}
