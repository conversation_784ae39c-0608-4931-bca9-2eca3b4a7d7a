package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/1 11:15
 * @since JDK 1.8
 */
public class CustomFundInfoResDto implements Serializable {

    private static final long serialVersionUID = -8162296012963072286L;

    private String fundCode;
    private String fundName;
    private String fundRiskLevel;
    private BigDecimal buyRatio;
    private BigDecimal sellRatio;
    private BigDecimal sellVol;
    private BigDecimal holdRatio;
    private String tradeStatus;
    private String redeemStatus;
    private String limitMax;
    private String limitMin;
    private BigDecimal minAcctVol;
    private BigDecimal balanceVol;
    private BigDecimal availableVol;
    private BigDecimal actualDiscount;
    private String feeRateMethod;
    private BigDecimal constantFee;
    private BigDecimal feeRate;
    private BigDecimal feeAmt;
    private BigDecimal oraFeeAmt;
    private BigDecimal sellAmt;
    private BigDecimal availableAmt;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public BigDecimal getBuyRatio() {
        return buyRatio;
    }

    public void setBuyRatio(BigDecimal buyRatio) {
        this.buyRatio = buyRatio;
    }

    public BigDecimal getSellRatio() {
        return sellRatio;
    }

    public void setSellRatio(BigDecimal sellRatio) {
        this.sellRatio = sellRatio;
    }

    public BigDecimal getSellVol() {
        return sellVol;
    }

    public void setSellVol(BigDecimal sellVol) {
        this.sellVol = sellVol;
    }

    public BigDecimal getHoldRatio() {
        return holdRatio;
    }

    public void setHoldRatio(BigDecimal holdRatio) {
        this.holdRatio = holdRatio;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getRedeemStatus() {
        return redeemStatus;
    }

    public void setRedeemStatus(String redeemStatus) {
        this.redeemStatus = redeemStatus;
    }

    public String getLimitMax() {
        return limitMax;
    }

    public void setLimitMax(String limitMax) {
        this.limitMax = limitMax;
    }

    public String getLimitMin() {
        return limitMin;
    }

    public void setLimitMin(String limitMin) {
        this.limitMin = limitMin;
    }

    public BigDecimal getMinAcctVol() {
        return minAcctVol;
    }

    public void setMinAcctVol(BigDecimal minAcctVol) {
        this.minAcctVol = minAcctVol;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getAvailableVol() {
        return availableVol;
    }

    public void setAvailableVol(BigDecimal availableVol) {
        this.availableVol = availableVol;
    }

    public BigDecimal getActualDiscount() {
        return actualDiscount;
    }

    public void setActualDiscount(BigDecimal actualDiscount) {
        this.actualDiscount = actualDiscount;
    }

    public String getFeeRateMethod() {
        return feeRateMethod;
    }

    public void setFeeRateMethod(String feeRateMethod) {
        this.feeRateMethod = feeRateMethod;
    }

    public BigDecimal getConstantFee() {
        return constantFee;
    }

    public void setConstantFee(BigDecimal constantFee) {
        this.constantFee = constantFee;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public BigDecimal getOraFeeAmt() {
        return oraFeeAmt;
    }

    public void setOraFeeAmt(BigDecimal oraFeeAmt) {
        this.oraFeeAmt = oraFeeAmt;
    }

    public BigDecimal getSellAmt() {
        return sellAmt;
    }

    public void setSellAmt(BigDecimal sellAmt) {
        this.sellAmt = sellAmt;
    }

    public BigDecimal getAvailableAmt() {
        return availableAmt;
    }

    public void setAvailableAmt(BigDecimal availableAmt) {
        this.availableAmt = availableAmt;
    }
}