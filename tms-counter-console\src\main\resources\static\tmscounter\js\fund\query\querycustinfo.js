/*******************************************************************************
 * 客户信息
 * 
 * <AUTHOR>
 * @date 2017-07-03 10:39
 */

$(function() {

});

var hideRadio = false;

var QueryCustInfo = {
	custInfo : {},
	custBanks: [],
	/**
	 * 查询客户信息
	 */
	queryCustInfo : function(custNo, idNo, disCode, hboneNo) {

		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

		var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

		if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
				null, null);
		CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryCustInfocallBack);
	},

	queryCustInfoTransVolCheck : function(custNo, idNo, disCode, hboneNo) {

		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

		var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

		if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
			null, null);
		CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryCustInfoTransVolCheckcallBack);
	},

    queryCustInfoByCancel : function(custNo, idNo, disCode, hboneNo, callback) {

        var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

        var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

        if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
            showMsg("客户号,证件号必须输入一项");
            return false;
        }

        var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
            null, null);
        CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryCustInfocallBackCancel, callback);
    },

	// 不显示radio
	queryCustInfoNoRadio : function(custNo, idNo, disCode, hboneNo) {
		hideRadio = true;
		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

		var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

		if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
			null, null);
		CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryCustInfocallBackHideRadio);
	},

	queryBasicCustInfoTransVol : function(custNo, idNo, disCode, hboneNo) {
		hideRadio = true;
		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

		var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

		if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
			null, null);
		CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryBasicCustInfoTransVolCallBack);
	},


	queryBasicCustInfoTransVolCallBack : function(data) {
		QueryCustInfo.queryBasicCustInfoTransVolCallBackCommon(data);
		if (QueryCustInfo.custInfoDtoList.length == 0) {
			return;
		}
		QueryCustInfo.custInfo = QueryCustInfo.custInfoDtoList[0] || {};

		if (isEmpty($("#custNo").val())) {
			$("#custNo").val(QueryCustInfo.custInfo.custNo)
		}

		// 判断选择的是个人客户还是机构客户，个人时"是否经办"默认选中否, 机构默认选中是
		Agent.setAgentDivByInvstType(QueryCustInfo.custInfo.invstType);
	},

	queryBasicCustInfoTransVolCallBackCommon : function(data) {
		var bodyData = data.body || {};
		var custInfoDtoList = bodyData.custInfoList || [];
		QueryCustInfo.custInfoDtoList = custInfoDtoList;
		if (custInfoDtoList.length == 0) {
			CommonUtil.layer_tip("没有查询到此用户");
			return false;
		}

		$("#custInfoId").empty();
		$(custInfoDtoList).each(
			function(index, element) {
				if(index == 0){
					var trList = [];
					if (!hideRadio) {
						trList.push('<input class="selectcust" name="checkCust" type="radio" index="' + index + '"></input>');
					}
					trList.push(CommonUtil.formatData(element.custNo,'--'));
					trList.push(CommonUtil.formatData(element.custName,
						'--'));
					trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));
					trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP, element.custStat,'--'));
					if(element.invstType == '0'){//属于机构
						trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
					}
					if(element.invstType == '1'){
						trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
					}
					if(element.invstType == '2'){
						trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
					}
					trList.push(CommonUtil.formatData(element.idNo,'--'));
					var trHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
					$("#custInfoId").append(trHtml);
				}
			});
	},



	queryBasicCustInfo : function(custNo, idNo, disCode, hboneNo) {
		hideRadio = true;
		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

		var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

		if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
			null, null);
		CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryBasicCustInfocallBack);
	},

	queryCustInfoByCancel : function(custNo, idNo, disCode, hboneNo, callback) {

		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};

		var reqparamters = QueryCustInfo.getUserReqParameter(custNo, idNo, disCode, hboneNo);

		if (isEmpty(reqparamters.custNo) && isEmpty(reqparamters.idNo) && isEmpty(reqparamters.hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, null,
			null, null);
		CommonUtil.ajaxAndCallBack(paramters, QueryCustInfo.queryCustInfocallBackCancel, callback);
	},

	getUserReqParameter: function(custNo, idNo, disCode, hboneNo) {
		if (!custNo) {
			custNo = $("#custNo").val();
		}

		if (!idNo) {
			idNo = $("#idNo").val();
		}

		if (!hboneNo) {
			hboneNo = $("#hboneNo").val();
		}

		if (!disCode) {
			disCode = $("#selectDisCode").val();
		}

		var reqparamters = {};

		if (!CommonUtil.isEmpty(disCode)) {
			reqparamters.disCode = disCode;
		}

		if (!CommonUtil.isEmpty(idNo)) {
			reqparamters.idNo = idNo;
		}

		if (!CommonUtil.isEmpty(custNo)) {
			reqparamters.custNo = custNo;
		}

		if (!CommonUtil.isEmpty(hboneNo)) {
			reqparamters.hboneNo = hboneNo;
		}
		
		//公募版本
		reqparamters.versionType = 0;
		return reqparamters;
	},
	queryCustInfocallBack : function(data) {
		QueryCustInfo.queryCustInfocallBackCommon(data);

		QueryCustInfo.selectCustClickListeren();
	},

	queryCustInfoTransVolCheckcallBack : function(data) {
		QueryCustInfo.queryCustInfoTransVolCheckcallBackCommon(data);

		QueryCustInfo.selectCustClickListeren();
	},

	queryCustInfocallBackCancel : function(data) {
		QueryCustInfo.queryCustInfocallBackCommon(data);
	},
	queryCustInfocallBackHideRadio : function(data) {
		QueryCustInfo.queryCustInfocallBackCommon(data);
		if (QueryCustInfo.custInfoDtoList.length == 0) {
			return;
		}
		QueryCustInfo.custInfo = QueryCustInfo.custInfoDtoList[0] || {};

		if (isEmpty($("#custNo").val())) {
			$("#custNo").val(QueryCustInfo.custInfo.custNo)
		}

		// 判断选择的是个人客户还是机构客户，个人时"是否经办"默认选中否, 机构默认选中是
		Agent.setAgentDivByInvstType(QueryCustInfo.custInfo.invstType);
	},

	queryBasicCustInfocallBack : function(data) {
		QueryCustInfo.queryBasicCustInfocallBackCommon(data);
		if (QueryCustInfo.custInfoDtoList.length == 0) {
			return;
		}
		QueryCustInfo.custInfo = QueryCustInfo.custInfoDtoList[0] || {};

		if (isEmpty($("#custNo").val())) {
			$("#custNo").val(QueryCustInfo.custInfo.custNo)
		}

		// 判断选择的是个人客户还是机构客户，个人时"是否经办"默认选中否, 机构默认选中是
		Agent.setAgentDivByInvstType(QueryCustInfo.custInfo.invstType);
	},

	queryCustInfocallBackCommon : function(data) {
		var bodyData = data.body || {};
		var custInfoDtoList = bodyData.custInfoList || [];
		QueryCustInfo.custInfoDtoList = custInfoDtoList;
		if (custInfoDtoList.length == 0) {
			CommonUtil.layer_tip("没有查询到此用户");
			return false;
		}

		$("#custInfoId").empty();
		$(custInfoDtoList).each(
						function(index, element) {
							var trList = [];
							if (!hideRadio) {
								trList.push('<input class="selectcust" name="checkCust" type="radio" index="' + index + '"></input>');
							}
							trList.push(CommonUtil.formatData(element.custNo,'--'));
							trList.push(CommonUtil.formatData(element.custName,
									'--'));
							trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));
							trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP, element.custStat,'--'));
							if(element.invstType == '0'){//属于机构
								trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
							}
							if(element.invstType == '1'){
								trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
							}
							if(element.invstType == '2'){
								trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
							}
							trList.push(CommonUtil.formatData(element.idNo,'--'));
							trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP,element.investorType, '--'));
							trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP,element.custRiskLevel, '--'));
							trList.push(CommonUtil.getMapValue(DisCode.disCodesMap, element.disCode,'--'));
							var trHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
							$("#custInfoId").append(trHtml);
						});



	},


	queryCustInfoTransVolCheckcallBackCommon : function(data) {
		var bodyData = data.body || {};
		var custInfoDtoList = bodyData.custInfoList || [];
		QueryCustInfo.custInfoDtoList = custInfoDtoList;
		if (custInfoDtoList.length == 0) {
			CommonUtil.layer_tip("没有查询到此用户");
			return false;
		}

		$("#custInfoId").empty();
		$(custInfoDtoList).each(
			function(index, element) {
				var trList = [];
				if (!hideRadio) {
					trList.push('<input class="selectcust" name="checkCust" type="radio" index="' + index + '"></input>');
				}
				trList.push(CommonUtil.formatData(element.custNo,'--'));
				trList.push(CommonUtil.formatData(element.custName,
					'--'));
				trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));
				trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP, element.custStat,'--'));
				if(element.invstType == '0'){//属于机构
					trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
				}
				if(element.invstType == '1'){
					trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
				}
				if(element.invstType == '2'){
					trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
				}
				trList.push(CommonUtil.formatData(element.idNo,'--'));
				// trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP,element.investorType, '--'));
				// trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP,element.custRiskLevel, '--'));
				// trList.push(CommonUtil.getMapValue(DisCode.disCodesMap, element.disCode,'--'));
				var trHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#custInfoId").append(trHtml);
			});



	},

	queryBasicCustInfocallBackCommon : function(data) {
		var bodyData = data.body || {};
		var custInfoDtoList = bodyData.custInfoList || [];
		QueryCustInfo.custInfoDtoList = custInfoDtoList;
		if (custInfoDtoList.length == 0) {
			CommonUtil.layer_tip("没有查询到此用户");
			return false;
		}

		$("#custInfoId").empty();
		$(custInfoDtoList).each(
			function(index, element) {
				var trList = [];
				if (!hideRadio) {
					trList.push('<input class="selectcust" name="checkCust" type="radio" index="' + index + '"></input>');
				}
				trList.push(CommonUtil.formatData(element.custNo,'--'));
				trList.push(CommonUtil.formatData(element.custName,
					'--'));
				trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));
				trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP, element.custStat,'--'));
				if(element.invstType == '0'){//属于机构
					trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
				}
				if(element.invstType == '1'){
					trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
				}
				if(element.invstType == '2'){
					trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
				}
				trList.push(CommonUtil.formatData(element.idNo,'--'));
				// trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP,element.investorType, '--'));
				// trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP,element.custRiskLevel, '--'));
				// trList.push(CommonUtil.getMapValue(DisCode.disCodesMap, element.disCode,'--'));
				var trHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#custInfoId").append(trHtml);
			});



	},

	selectCustClickListeren: function () {
		$(".selectcust").click(
			function () {
				$(this).attr('checked', 'checked').siblings().removeAttr('checked');
				var selectIndex = $(this).attr("index");

				QueryCustInfo.custInfo = QueryCustInfo.custInfoDtoList[selectIndex] || {};
				QueryCustInfo.queryCustBankInfo(QueryCustInfo.custInfo.custNo, QueryCustInfo.custInfo.disCode);

				if (isEmpty($("#custNo").val())) {
					$("#custNo").val(QueryCustInfo.custInfo.custNo)
				}

				// 判断选择的是个人客户还是机构客户，个人时"是否经办"默认选中否, 机构默认选中是
				Agent.setAgentDivByInvstType(QueryCustInfo.custInfo.invstType);
			});

		$('input[name="checkCust"][index="0"]').click();
	},
	
	getCustInfos : function(custNo, idNo, disCode, callBack) {
		var  uri= TmsCounterConfig.QUERY_CUST_INFO_URL  ||  {};
		var reqparamters = {};
		reqparamters.custNo = custNo;
		reqparamters.idNo = idNo;
		reqparamters.disCode = disCode;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
		CommonUtil.ajaxAndCallBack(paramters, callBack);
	},
	
	getCustBankInfos : function(custNo, disCode, callBack){
		var  uri= TmsCounterConfig.QUERY_CUST_BANKINFO_URL  ||  {};
		var reqparamters = {};
		reqparamters.custNo = custNo;
		reqparamters.disCode = disCode;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
		CommonUtil.ajaxAndCallBack(paramters, callBack);
	},

	/**
	 * 查询客户银行卡信息
	 */
	queryCustBankInfo : function(custNo, disCode) {
		var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
		var reqparamters = {
			"custNo" : custNo,
			"disCode" : disCode
		};

		var paramters = CommonUtil.buildReqParams(uri, reqparamters);
		CommonUtil.ajaxAndCallBack(paramters, function(data) {
			var respCode = data.code || '';
			var body = data.body || {};

			if (CommonUtil.isSucc(respCode)) {
				QueryCustInfo.custBanks = body.custBanks || [];
				var selectBankHtml = '';
				$(QueryCustInfo.custBanks).each(
						function(index, element) {
							selectBankHtml += '<option bankacct= "'+ element.bankAcct + '" bankcode= "'+ element.bankCode + '" value="' + element.cpAcctNo + '">'+ CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP,element.bankCode) + ''+ element.bankAcct + ' </option>';
						});
				$("#selectBank").empty();
				$("#selectBank").html(selectBankHtml);
				
				$("#selectBank").change(function(){
					if($("#selectBank").find("option:selected")[0]){
						var indexNum = $("#selectBank").find("option:selected")[0].index;
						var custBank = QueryCustInfo.custBanks[indexNum] || {} ;
						if($("#bankCode").length > 0){
							$("#bankCode").val(custBank.bankCode);
						}
					}
				});
				
				$("#selectBank").change();
			}
		});
	},
}