/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.service.trade;

import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;

/**
 * @description:(柜台高端校验服务)
 * <AUTHOR>
 * @date 2017年4月17日 下午8:27:38
 * @since JDK 1.6
 */
public interface TmsCounterValidService {

    /**
     * 
     * cancelOrderValidate:(撤单校验)
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2017年4月17日 下午8:30:47
     */
    public boolean cancelOrderValidate(CounterCancelReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * redeemValidate:(赎回校验)
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2017年4月17日 下午8:31:24
     */
    public boolean redeemValidate(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * subsOrPurValidate:(购买校验)
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2017年4月17日 下午8:31:53
     */
    public boolean subsOrPurValidate(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * modifyDivValidateFacade:(修改分红方式校验)
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2017年4月17日 下午8:32:22
     */
    public boolean modifyDivValidate(CounterModifyDivReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 
     * shareMergeVolValidate:(高端专户份额合并校验)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月8日 下午1:52:28
     */
    public boolean shareMergeVolValidate(CounterShareMergeVolReqDto dto, DisInfoDto disInfoDto) throws Exception;
    
    /**
     * 
     * shareTransferVolValidate:(高端专户份额迁移校验)
     * @param dto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月8日 下午1:52:28
     */
    public boolean shareTransferVolValidate(CounterShareMergeVolReqDto dto, DisInfoDto disInfoDto) throws Exception;

    /**
     * 高端非交易过户校验
     * @param dto
     * @param disInfoDto
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2020/9/24 18:19
     * @since JDK 1.8
     */
    boolean noTradeOverAccountValidate(CounterNoTradeOverAccountReqDto dto, DisInfoDto disInfoDto) throws Exception;
}
