/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.enums;
/**
 * @description:(柜台网点号) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年4月23日 上午10:07:46
 * @since JDK 1.6
 */
public enum TmsCounterOutCodeEnum {
    /**
     * 群济柜台网点号
     */
    QUJI_COUNTER_OUT_CODE("0222", "群济柜台网点号"),
    /**
     * 好买柜台网点号
     */
    HOWBUY_COUNTE_OUT_CODE("W20170215","好买柜台网点号");
    
    private String code;
    private String desc;
    private TmsCounterOutCodeEnum(String code, String desc){
        
        this.code = code;
        this.desc = desc;
    }
    public String getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

    
}

