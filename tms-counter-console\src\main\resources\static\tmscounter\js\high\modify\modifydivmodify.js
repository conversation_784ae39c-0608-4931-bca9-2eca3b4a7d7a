/**
*修改分红方式修改
*<AUTHOR>
*@date 2018-03-27 11:10：01
**/
$(function(){
	ModifyDivModify.init();
});

var ModifyDivModify = {
	
	/**
	 * 初始化
	 */
	init:function(){
		// 初始化数据
		ModifyDivModify.initData();
		
		// 查询订单信息
        Modify.queryCounterDealOrder(this.queryCounterDealOrderCallBack, null);
	},
		
	/**
	 * 初始化参数
	 */
	  initData:function(){
		  ModifyDivModify.checkOrder = {};// 订单信息
		  ModifyDivModify.custInfo = {};// 客户信息
		  ModifyDivModify.modifyDeal = {};// 修改订单
	},
	
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		ModifyDivModify.counterOrderDto = bodyData.counterOrderDto || {};//订单信息
		var checkOrder = ModifyDivModify.counterOrderDto || {};//订单信息
		
		var appointList = bodyData.appointList || [];// 投顾预约信息
		var custInfofiList = bodyData.custInfofiList || [];// 客户信息
		ModifyDivModify.checkOrder = checkOrder;// 柜台订单信息
		// 原订单信息
    	Modify.modifyDealOrder = checkOrder;
    	
		ModifyDivModify.builDealInfo(checkOrder);// 购买订单信息
		ViewCounterDeal.buildAppointmentInfo(appointList);// 预约信息
		if(custInfofiList.length > 0){
			ModifyDivModify.custInfo = custInfofiList[0] || {};// 客户信息
		}
		ViewCounterDeal.buildCustInfo(custInfofiList);// 客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);// 其他信息
		ViewCounterDeal.buildTransactor(checkOrder);// 经办人信息
		ViewCounterDeal.buildCheckInfo(checkOrder); // 审核信息
		var fundCode = checkOrder.fundCode;// 产品代码
		ModifyDivModify.queryFundInfo(fundCode);// 查询产品信息
        var viewType = CommonUtil.getParam("viewType");
        if('0' != viewType){
            OnLineOrderFile.query(null, Modify.getSelectCustMaterial(bodyData, OnLineOrderFile.CRM_MODIFY_DIV), Modify.getCheckNode());// CRM线上资料
        }else{
            var orderFile = bodyData.orderFile || {};
            OnLineOrderFile.buildOrderFileHtml(orderFile);
        }
	},
	
	/**
	 * 赎回订单信息
	 */
	builDealInfo:function(checkOrder){
		var targetDiv = checkOrder.fundDivMode || '';
    	var divMode = '';
		
		if(targetDiv == '0'){
			divMode = '1';
		}else if(targetDiv == '1'){
			divMode = '0';
		}
		$("#fundCodeId").html(checkOrder.fundCode);// 产品代码
		$("#divModeId").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,divMode,'--'));//当前修改分红方式
		$("#targetDivId").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,targetDiv,'--'));//目标分红方式
		$("#appDt").val(checkOrder.appDt);//申请日期
    	$("#appTm").val(checkOrder.appTm);//申请时间
	},
	/**
	 * 查询高端产品基本信息
	 * @param fundCode 产品代码
	 */
	queryFundInfo:function(fundCode){
		
		QueryHighProduct.queryFundInfo(fundCode);
	    ModifyDivModify.fundInfo = QueryHighProduct.fundInfo || {};
		
		if(CommonUtil.isEmpty(ModifyDivModify.fundInfo.fundCode)){
			CommonUtil.layer_tip("没有查询到此产品");
			return false;
		}
		//构建产品基本信息
		ViewCounterDeal.buildFundInfo(ModifyDivModify.fundInfo);
	}
};