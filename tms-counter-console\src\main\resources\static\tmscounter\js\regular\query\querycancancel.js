/**
*查询可撤销订单
*<AUTHOR>
*@date 2017-04-11 16:17
*/

var operatorNo = CommonUtil.getParam("operatorNo");

var RegularQueryCanCancel ={
		queryCanCancelForApply:function(custNo,dealNo){
			var uri= TmsCounterConfig.QUERY_REGULAR_CAN_CANCEL_FOR_APPLY_URL ||  {};
			var reqparamters = {};
			reqparamters.custNo = custNo;
			reqparamters.dealNo = dealNo;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);;
			CommonUtil.ajaxAndCallBack(paramters, RegularQueryCanCancel.queryCanCancelBack);
		},
		
		/**
		 * 查询可撤单订单
		 */
		queryCanCancel:function(custNo,dealNo,operNo){
			if(!CommonUtil.isEmpty(operNo)){
				operatorNo = operNo;
			}

			var uri= TmsCounterConfig.QUERY_REGULAR_CAN_CANCEL_URL ||  {};
			var reqparamters = {};
			reqparamters.custNo = custNo;
			reqparamters.dealNo = dealNo;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);;
			CommonUtil.ajaxAndCallBack(paramters, RegularQueryCanCancel.queryCanCancelBack);
		},
		/**
		 * 可撤单订单信息
		 */
		queryCanCancelBack:function(data){

			var bodyData = data.body || {};
            RegularQueryCanCancel.canCancelOrders= bodyData.canCancelOrders || [];
			if($("#cancelList").length > 0){
				$("#cancelList").html('');
				if(RegularQueryCanCancel.canCancelOrders.length <=0){
					var trHtml = '<tr><td colspan="11">没有查询到可以撤销交易</td></tr>';
					$("#cancelList").append(trHtml);
					return false;
				}
				$(RegularQueryCanCancel.canCancelOrders).each(function(index,element){
					var trList = [];
					trList.push(CommonUtil.formatData(element.productCode));
					trList.push(CommonUtil.formatData(element.productName));
					trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME_MAP,element.mBusiCode,'--'));
					if(element.appAmt > 0){
						trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
					}else {
						trList.push('--');
					}
					if(element.appVol > 0){
						trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
					}else {
						trList.push('--');
					}
					trList.push(CommonUtil.getMapValue(CONSTANTS.TX_APP_FLAG_MAP,element.txAppFlag));
					trList.push(CommonUtil.formatData(element.dealNo));
					trList.push(CommonUtil.formatData(element.appDate));
					trList.push(CommonUtil.formatData(element.appTime));
					trList.push(CommonUtil.formatData(operatorNo,"操作员"));
					var trHtml = '<tr class="text-c"><td><input type="radio" class="selectCancleOrder" name="orderIndex" index="' + index + '" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td></tr>';
					$("#cancelList").append(trHtml);
				});
				
				$(".selectCancleOrder").click(
					function() {
						$(this).attr('checked', 'checked').siblings().removeAttr('checked');
					});
				$('input[name="orderIndex"][index="0"]').click();
			}
		}	
};
