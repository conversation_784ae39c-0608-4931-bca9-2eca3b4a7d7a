/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.enums;

/**
 * @description: 资产范围枚举
 * <AUTHOR>
 * @date 2021/1/11 16:48
 * @since JDK 1.8
 */
public enum AssetScopeEnum {

    /**
     * 2000以下
     */
    SCOPE_BELOW_2000("1", "2000以下"),

    /**
     * 2000-20000
     */
    SCOPE_2000_TO_20000("2", "2000-20000"),

    /**
     * 20000以上
     */
    SCOPE_ABOVE_20000("3", "20000以上");

    public final String CODE;

    public final String DESC;

    AssetScopeEnum(String code, String desc) {
        this.CODE = code;
        this.DESC = desc;
    }

}
