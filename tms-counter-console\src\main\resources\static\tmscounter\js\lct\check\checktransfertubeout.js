/**
 * 理财通转托管转出审核
 */
$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();
	$("#returnBtn").on('click',function(){
		CheckTransferTubeOut.confirm(CounterCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckTransferTubeOut.confirm(CounterCheck.Succ);
	});
	
	$("#fundCode").on('blur',function(){
		QueryFundInfo.queryFundInfo();
	});
	
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckTransferTubeOut.checkOrder = {};
	CheckTransferTubeOut.checkDtlOrder = [];	
	CheckTransferTubeOut.init(checkId,custNo,disCode,idNo);
});

var CheckTransferTubeOut = {	
	init:function(checkId, custNo, disCode,idNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		
		// 设置录入订单信息
        CheckTransferTubeOut.queryCheckOrderById(checkId,CheckTransferTubeOut.queryCheckOrderByIdBack);
	},

    queryCheckOrderById:function(dealAppNo, callBack){
        var  uri= TmsCounterConfig.LCT_QUERY_CHECK_ORDER_BY_ID_URL  ||  {};
        var reqparamters = {};
        reqparamters.dealAppNo = dealAppNo;
        reqparamters.pageNum = 1;
        reqparamters.pageSize = 100;
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
        CommonUtil.ajaxAndCallBack(paramters, callBack);
    },

    /***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var transfertubeOutForm = $("#transfertubeOutForm").serializeObject();
		
		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckTransferTubeOut.checkFaildDesc = $("#checkFaildDesc").val();
		}else{
			var validRst = Valid.valiadateFrom($("#transfertubeOutForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}
			
			var checkResultReply = CheckTransferTubeOut.checkTransferTubeOutValid(transfertubeOutForm, CheckTransferTubeOut.checkOrder, CheckTransferTubeOut.checkDtlOrder);
			if(!checkResultReply.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(checkResultReply.tip);
				return false;
			}
		}
		
		var reqparamters ={"checkFaildDesc":CheckTransferTubeOut.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckTransferTubeOut.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckTransferTubeOut.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	checkTransferTubeOutValid:function(checkForm, orderForm, orderDtlForm){
		var fundCode = checkForm.fundCode || '';
		var tSellerCode = checkForm.tSellerCode || '';
		var tSellerTxAcctNo = checkForm.tSellerTxAcctNo || '';
		var tOutletCode = checkForm.tOutletCode || '';
		
		var result = {"status":true,"tip":''};
		
		if(fundCode != (orderForm.fundCode || '')){
			result.status = false;
			result.tip = "基金代码不匹配，请重新确认";
			return result;
		}
		
		if(tSellerCode != (orderForm.tSellerCode || '')){
			result.status = false;
			result.tip = "对方销售人代码不匹配，请重新确认";
			return result;
		}

		if(tSellerTxAcctNo != (orderForm.tSellerTxAcctNo || '')){
			result.status = false;
			result.tip = "对方销售人处投资者基金交易账号不匹配，请重新确认";
			return result;
		}
		
		if(tOutletCode != (orderForm.tOutletCode || '')){
			result.status = false;
			result.tip = "对方网点不匹配，请重新确认";
			return result;
		}
		
		// 转出份额
		$(".outVolClass").css("border-color","");
		var checkFormAppVols = $("#transOutCustBals").find("input[type='checkbox'][name='checkTransOutBal']:checked");
		$(checkFormAppVols).each(function(index,obj){
			var selOutIndex = $(obj).attr('data-index');
			var checkOutVol = $('#outVol_'+selOutIndex).val();
			if(isEmpty(checkOutVol)){
				result.status = false;
				result.tip = "转出份额不能为空";
				CommonUtil.layer_tip("转出份额不能为空");
				$("#outVol_"+selOutIndex).css("border-color","red");
				return result;
			}
			
			if(checkOutVol != orderDtlForm[index].appVol){
				result.status = false;
				result.tip = "转出份额不一致，请重新确认";
				CommonUtil.layer_tip("转出份额不一致，请重新确认");
				$("#outVol_"+selOutIndex).css("border-color","red");
				return result;
			}
			
			$("#outVol_"+selOutIndex).css("border-color","");
		});
		return result;
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckTransferTubeOut.checkOrder = bodyData.checkOrder || {};
		CheckTransferTubeOut.checkDtlOrder = bodyData.checkDtlOrder || {};
				
		if(CommonUtil.isEmpty(CheckTransferTubeOut.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckTransferTubeOut.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		
		QueryFundInfo.queryFundInfo(CheckTransferTubeOut.checkOrder.fundCode,false);
		
		if($("#transferTubeBusiType").length > 0){
			$("#transferTubeBusiType").val(CheckTransferTubeOut.checkOrder.transferTubeBusiType);
		}
		if($("#tSellerTD").length > 0){
			Init.setTSellerCodeTD(CheckTransferTubeOut.checkOrder.transferTubeBusiType);
			//$("#tSellerCode").val(CheckTransferTubeOut.checkOrder.tSellerCode);
		}

		/** 转托管转出审核份额信息 */
		BodyView.setLctTransferTubeOutTableView("transOutCustBals", CheckTransferTubeOut.checkDtlOrder, "check");
		/** other */
		BodyView.setCheckOperInfoView(CheckTransferTubeOut.checkOrder);
	}
}
