/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.batch.facade.query.querycurtadtack.QueryCurTaDtAckResponse;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterExchangeReqDto;
import com.howbuy.tms.counter.dto.CounterExchangeRespDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(零售柜台基金转换控制器)
 * <AUTHOR>
 * @date 2017年9月15日 下午5:14:25
 * @since JDK 1.6
 */
@Controller
public class ExchangeFundController extends AbstractController {
    private Logger logger = LogManager.getLogger(ExchangeFundController.class);

    @Autowired
    private TradeValidateService tradeValidateService;
    
    /**
     * 
     * validateExgCurTaDtAckVol:(校验当前TA交易日有确认份额是否继续基金转换)
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2019年1月15日 下午3:43:10
     */
    @RequestMapping("/tmscounter/fund/validateExgCurTaDtAckVol.htm")
    public void validateExgCurTaDtAckVol(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String exchangeFunds = request.getParameter("exchangeFunds");
        String custInfoForm = request.getParameter("custInfoForm");
        
        // 选择需要转换的订单
        List<CounterExchangeReqDto> exchangeReqList = JSON.parseArray(exchangeFunds, CounterExchangeReqDto.class);
        if(CollectionUtils.isEmpty(exchangeReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.EXCHANGE_ORDER_NOT_SELECTED);
        }
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        
        List<TmsCounterResult> rstList = new ArrayList<TmsCounterResult>();
        
        for(CounterExchangeReqDto reqDto : exchangeReqList){
            // do service
            QueryCurTaDtAckResponse responseDto = tmsCounterService.queryCustCurTaDtAckVol(custInfoDto.getCustNo(), reqDto.getCpAcctNo(), 
                    reqDto.getFundCode(), reqDto.getProtocolNo(), custInfoDto.getDisCode());
            
            if(responseDto != null && responseDto.getTotalAckVol().compareTo(BigDecimal.ZERO) > 0){
                CounterExchangeReqDto reqVdlDto = new CounterExchangeReqDto();
                reqVdlDto.setTxAcctNo(custInfoDto.getCustNo());
                reqVdlDto.setCpAcctNo(reqDto.getCpAcctNo());
                reqVdlDto.setFundCode(reqDto.getFundCode());
                reqVdlDto.setProtocolNo(reqDto.getProtocolNo());
                reqVdlDto.setAppVol(reqDto.getAppVol());
                
                TmsCounterResult rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getTotalAckVol().toString());
                Map<String, Object> body = new HashMap<String, Object>(16);
                body.put("reqVdlDto", reqVdlDto);
                rst.setBody(body);
                
                // set return
                rstList.add(rst);
            }
        }
        WebUtil.write(response, rstList);
    }
    
    /**
     * 
     * exchangeConfirm:基金转换确认(确认提交暂只支持单基金转换)
     *                  
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 下午5:15:04
     */
    @RequestMapping("/tmscounter/fund/exchangeconfirm.htm")
    public ModelAndView exchangeConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        
        String dealAppNo = request.getParameter("dealAppNo");
        String riskFlag = request.getParameter("riskFlag");
        String exchangeFunds = request.getParameter("exchangeFunds");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String filePath = request.getParameter("filePath");
        logger.debug("ExchangeFundController|exchangeConfirm|dealAppNo:{},riskFlag:{},exchangeFunds:{},custInfoForm:{},transactorInfoForm:{},filePath:{}",
                dealAppNo, riskFlag, exchangeFunds, custInfoForm, transactorInfoForm, filePath);
        
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        
        List<CounterExchangeReqDto> exchangeReqList = JSON.parseArray(exchangeFunds, CounterExchangeReqDto.class);
        if(CollectionUtils.isEmpty(exchangeReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.EXCHANGE_ORDER_NOT_SELECTED);
        }
        
        CounterExchangeReqDto reqDto = exchangeReqList.get(0);
        // 申请份额为空或0时，不去做转换申请
        if(reqDto.getAppVol() == null || BigDecimal.ZERO.compareTo(reqDto.getAppVol()) == 0){
            throw new TmsCounterException(TmsCounterResultEnum.EXCHANGE_APP_VOL_IS_NULLORZERO);
        }
        
        // 转出基金信息
        FundInfoAndNavBean fundInfo = null;
        if(StringUtils.isEmpty(reqDto.getFundCode())){
            throw new TmsCounterException(TmsCounterResultEnum.EXCHANGE_FUNDCODE_IS_NULL);
        }
        fundInfo = getFundInfoNav(reqDto.getFundCode(), transactorInfoDto.getAppDt(),
                transactorInfoDto.getAppTm());
        // 转出基金校验Y份额
        tradeValidateService.tradeFundValidate(fundInfo);
        
        // 转入基金信息
        FundInfoAndNavBean tFundInfo = null;
        if(StringUtils.isEmpty(reqDto.gettFundCode())){
            throw new TmsCounterException(TmsCounterResultEnum.EXCHANGE_T_FUNDCODE_IS_NULL);
        }
        tFundInfo = getFundInfoNav(reqDto.gettFundCode(), transactorInfoDto.getAppDt(),
                transactorInfoDto.getAppTm());
        // 转入基金校验Y份额
        tradeValidateService.tradeFundValidate(tFundInfo);
        
        // set request dto
        CounterExchangeReqDto counterExchangeReqDto = new CounterExchangeReqDto();
        
        counterExchangeReqDto.setFundCode(fundInfo.getFundCode());
        counterExchangeReqDto.setFundShareClass(fundInfo.getFundShareClass());
        counterExchangeReqDto.setFundName(fundInfo.getFundAttr());
        counterExchangeReqDto.setTaCode(fundInfo.getTaCode());
        
        counterExchangeReqDto.settFundCode(tFundInfo.getFundCode());
        counterExchangeReqDto.settFundShareClass(tFundInfo.getFundShareClass());
        counterExchangeReqDto.settFundName(tFundInfo.getFundAttr());
        
        counterExchangeReqDto.setDealAppNo(dealAppNo);
        counterExchangeReqDto.setRiskFlag(riskFlag);
        counterExchangeReqDto.setAppVol(reqDto.getAppVol());
        counterExchangeReqDto.setCpAcctNo(reqDto.getCpAcctNo());
        counterExchangeReqDto.setBankCode(reqDto.getBankCode());
        counterExchangeReqDto.setBankAcct(reqDto.getBankAcct());
        counterExchangeReqDto.setLargeRedmFlag(reqDto.getLargeRedmFlag());
        
        counterExchangeReqDto.setProtocolNo(reqDto.getProtocolNo());
        // 协议类型(普通公募或定投)
        counterExchangeReqDto.setProtocolType(reqDto.getProtocolType());
        counterExchangeReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        // 产品类别: 零售
        counterExchangeReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
        counterExchangeReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        // 可赎回日期
        counterExchangeReqDto.setOpenRedeDt(reqDto.getOpenRedeDt());
        
        counterExchangeReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterExchangeReqDto.setDisCode(custInfoDto.getDisCode());
        counterExchangeReqDto.setCustName(custInfoDto.getCustName());
        counterExchangeReqDto.setIdNo(custInfoDto.getIdNo());
        counterExchangeReqDto.setIdType(custInfoDto.getIdType());
        counterExchangeReqDto.setInvstType(custInfoDto.getInvstType());

        counterExchangeReqDto.setAppDt(transactorInfoDto.getAppDt());
        counterExchangeReqDto.setAppTm(transactorInfoDto.getAppTm());
        counterExchangeReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterExchangeReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        counterExchangeReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterExchangeReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterExchangeReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterExchangeReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterExchangeReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

        counterExchangeReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        counterExchangeReqDto.setChecker(operatorInfoCmd.getOperatorNo());
        // 双录文件
        counterExchangeReqDto.setDoubleRecordFilePath(filePath);
        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterExchangeReqDto);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        // do service
        CounterExchangeRespDto responseDto = tmsFundCounterService.counterExchange(counterExchangeReqDto, disInfoDto);
        TmsCounterResult rst = null;
        if(responseDto != null ){
            rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
        } else{
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
        }
        
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

}
