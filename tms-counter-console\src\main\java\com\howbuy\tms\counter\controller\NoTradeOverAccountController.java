/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.tms.common.enums.busi.FundTypeEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.validate.ValidateUtils;
import com.howbuy.tms.counter.dto.CounterNoTradeOverAccountReqDto;
import com.howbuy.tms.counter.dto.CounterNoTradeOverAccountRespDto;
import com.howbuy.tms.counter.dto.CounterOrderFormDto;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.counter.util.CounterOrderFormUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;

/**
 * 非交易过户
 *
 * <AUTHOR>
 * @date 2020/9/24 14:43
 * @since JDK 1.8
 */
@Controller
public class NoTradeOverAccountController {

    private Logger logger = LogManager.getLogger(NoTradeOverAccountController.class);

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private TmsCounterService tmsCounterService;

    @RequestMapping("tmscounter/notradeoveraccountconfirm.htm")
    public ModelAndView notradeoveraccountconfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 获取过户数据
        String confirmForm = request.getParameter("confirmForm");
        String balInfo = request.getParameter("balInfo");
        CounterNoTradeOverAccountReqDto reqDto = JSON.parseObject(confirmForm, CounterNoTradeOverAccountReqDto.class);
        QueryAcctBalanceDtlRespDto.DtlBean dtlBean = JSON.parseObject(balInfo, QueryAcctBalanceDtlRespDto.DtlBean.class);

        // 业务性校验
        checkNoTradeOverAccountRequest(reqDto);

        // 操作员信息
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        CommonUtil.setCommonOperInfo(operatorInfoCmd, reqDto);
        reqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());

        // 构建生成转让申请入参
        fillOrder(reqDto, dtlBean);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(reqDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());

        CounterNoTradeOverAccountRespDto responseDto = tmsCounterService.counterNoTradeOverAccount(reqDto, disInfoDto);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 校验非交易转让入参
     */
    private void checkNoTradeOverAccountRequest(CounterNoTradeOverAccountReqDto reqDto) {
        // 查询产品信息
        HighProductBaseInfoBean highProduct = queryHighProductOuterService.getHighProductBaseInfo(reqDto.getFundCode());
        // 产品非空校验
        if (highProduct == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }
        // 如果产品是股权产品：基金分类=11私募，基金二级分类=C,那么转让价格非空
        if (FundTypeEnum.PE_VC.getCode().equals(highProduct.getFundSubType()) && FundTypeEnum.SM.getCode().equals(highProduct.getFundType())) {
            if (reqDto.getTransferPrice() == null || BigDecimal.ZERO.compareTo(reqDto.getTransferPrice()) > 0) {
                throw new TmsCounterException(TmsCounterResultEnum.NO_TRADE_TRANSFER_NEED_TRANSFER_PRICE);
            }
        }

        // 校验申请日期
        Date appDtm = DateUtil.string2Date(reqDto.getAppDt() + reqDto.getAppTm(), DateUtils.YYYYMMDDHHMMSS);
        ValidateUtils.validateAppDtm(appDtm);
    }

    /**
     * 处理下单信息
     *
     * @param reqDto
     * @param dtlBean
     * @return void
     * @author: huaqiang.liu
     * @date: 2020/9/24 16:39
     * @since JDK 1.8
     */
    private void fillOrder(CounterNoTradeOverAccountReqDto reqDto, QueryAcctBalanceDtlRespDto.DtlBean dtlBean) {
        reqDto.setIdNo(PrivacyUtil.encryptIdCard(reqDto.getIdNo()));
        reqDto.setBankAcct(PrivacyUtil.encryptBankAcct(reqDto.getBankAcct()));
        //查询高端产品信息
        HighProductBaseInfoBean highProduct = queryHighProductOuterService.getHighProductBaseInfo(reqDto.getFundCode());

        reqDto.setFundName(highProduct.getFundName());
        reqDto.setTaCode(highProduct.getTaCode());
        reqDto.setProductChannel(highProduct.getProductChannel());

        //客户持有产品份额快照
        CounterOrderFormDto.CounterCustBalanceVolDtlBean balance = new CounterOrderFormDto.CounterCustBalanceVolDtlBean();
        //可用份额
        balance.setAvailVol(dtlBean.getAvailVol());
        //持有份额
        balance.setBalanceVol(dtlBean.getBalanceVol());
        //市值
        balance.setMarketValue(dtlBean.getMarketValue());
        //冻结份额
        balance.setUnconfirmedVol(dtlBean.getUnconfirmedVol());
        //开放日
        balance.setOpenRedeDt(dtlBean.getOpenRedeDt());
        String counterOrderFormMemo = CounterOrderFormUtil.createCounterOrderForm(highProduct, null, null, Collections.singletonList(balance));
        reqDto.setOrderFormMemo(counterOrderFormMemo);
    }
}