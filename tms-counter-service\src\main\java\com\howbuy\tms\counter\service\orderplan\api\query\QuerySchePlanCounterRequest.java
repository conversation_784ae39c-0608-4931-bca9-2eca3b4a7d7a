/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.service.orderplan.api.query;

import java.util.Date;
import java.util.List;

import com.howbuy.tms.counter.service.orderplan.api.BaseRequest;

/**
 * 
 * @description:查询定投计划
 * <AUTHOR>
 * @date 2020年5月18日 下午1:53:44
 * @since JDK 1.6
 */
public class QuerySchePlanCounterRequest extends BaseRequest {
    private static final long serialVersionUID = -2944530730574325246L;

    /**
     * 计划ID
     */
    private String scheId;

    /**
     * 计划类型
     */
    private String scheType;

    /**
     * 实际计划执行开始日期
     */
    private String scheActualStartDate;

    /**
     * 实际计划执行结束日期
     */
    private String scheActualEndDate;

    /**
     * 计划状态:1-正常；2-暂停；3-终止
     */
    private String scheStatus;

    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    /**
     * 资金账号列表
     */
    private List<String> cpAcctNos;

    /**
     * 支付方式
     */
    private String payMode;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 止盈类型
     */
    private String stopProfitType;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页记录数
     */
    private Integer pageSize;

    public List<String> getCpAcctNos() {
        return cpAcctNos;
    }

    public void setCpAcctNos(List<String> cpAcctNos) {
        this.cpAcctNos = cpAcctNos;
    }

    public String getScheId() {
        return scheId;
    }

    public void setScheId(String scheId) {
        this.scheId = scheId;
    }

    public String getScheType() {
        return scheType;
    }

    public void setScheType(String scheType) {
        this.scheType = scheType;
    }

    public String getScheActualStartDate() {
        return scheActualStartDate;
    }

    public void setScheActualStartDate(String scheActualStartDate) {
        this.scheActualStartDate = scheActualStartDate;
    }

    public String getScheActualEndDate() {
        return scheActualEndDate;
    }

    public void setScheActualEndDate(String scheActualEndDate) {
        this.scheActualEndDate = scheActualEndDate;
    }

    public String getScheStatus() {
        return scheStatus;
    }

    public void setScheStatus(String scheStatus) {
        this.scheStatus = scheStatus;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Date getCreateStartTime() {
        return createStartTime;
    }

    public void setCreateStartTime(Date createStartTime) {
        this.createStartTime = createStartTime;
    }

    public Date getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(Date createEndTime) {
        this.createEndTime = createEndTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getStopProfitType() {
        return stopProfitType;
    }

    public void setStopProfitType(String stopProfitType) {
        this.stopProfitType = stopProfitType;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    @Override
    public String toString() {
        return "QuerySchePlanForConsoleRequest{" +
                "scheId='" + scheId + '\'' +
                ", scheType='" + scheType + '\'' +
                ", scheActualStartDate='" + scheActualStartDate + '\'' +
                ", scheActualEndDate='" + scheActualEndDate + '\'' +
                ", scheStatus='" + scheStatus + '\'' +
                ", txAcctNo='" + txAcctNo + '\'' +
                ", payMode='" + payMode + '\'' +
                ", productCode='" + productCode + '\'' +
                ", productName='" + productName + '\'' +
                ", createStartTime=" + createStartTime +
                ", createEndTime=" + createEndTime +
                ", stopProfitType='" + stopProfitType + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
