/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @className ModifyRepurchaseProtocolDto
 * @description
 * <AUTHOR>
 * @date 2019/3/26 14:47
 */
public class ModifyRepurchaseProtocolDto implements Serializable {
    private static final long serialVersionUID = -2985392330563391098L;

    private String txAcctNo;

    private String repurchaseType;

    private String fundCode;

    private String repurchaseProtocolNo;

    private BigDecimal repurchaseVol;

    /**
     *持有份额(修改复购协议特有)
     */
    private BigDecimal balanceVol;
    /**
     *赎回份额(修改复购协议特有)
     */
    private BigDecimal redeemVol;
    /**
     *冻结份额(修改复购协议特有)
     */
    private BigDecimal frznVol;
    /**
     *冻结份额(预计到期日期)
     */
    private List<String> expectedDueDt;

    private String disCode;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getRepurchaseType() {
        return repurchaseType;
    }

    public void setRepurchaseType(String repurchaseType) {
        this.repurchaseType = repurchaseType;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getRepurchaseProtocolNo() {
        return repurchaseProtocolNo;
    }

    public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
        this.repurchaseProtocolNo = repurchaseProtocolNo;
    }

    public BigDecimal getRepurchaseVol() {
        return repurchaseVol;
    }

    public void setRepurchaseVol(BigDecimal repurchaseVol) {
        this.repurchaseVol = repurchaseVol;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getRedeemVol() {
        return redeemVol;
    }

    public void setRedeemVol(BigDecimal redeemVol) {
        this.redeemVol = redeemVol;
    }

    public BigDecimal getFrznVol() {
        return frznVol;
    }

    public void setFrznVol(BigDecimal frznVol) {
        this.frznVol = frznVol;
    }

    public List<String> getExpectedDueDt() {
        return expectedDueDt;
    }

    public void setExpectedDueDt(List<String> expectedDueDt) {
        this.expectedDueDt = expectedDueDt;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }


}
