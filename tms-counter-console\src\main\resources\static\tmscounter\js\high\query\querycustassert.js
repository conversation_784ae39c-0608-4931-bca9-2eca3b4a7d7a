/**
*查询客户资产
*<AUTHOR>
*@date 2018-02-23 16:51
**/

$(function(){
	QueryCustAssert.init();
});

var QueryCustAssert = {
		
		/**
		 * 初始化数据
		 */
		initData:function(){
			QueryCustAssert.serialNo = '';
		},
		
		init:function(){
			/**
			 * 分销机构代码列表
			 */
			QueryCustAssert.disCodeList=[]
			/**
			 * 双击客户号查询客户信息
			 */
			$("#custNo").on('dblclick',function(){
				QueryCustInfoSubPage.selectCustNo($(this));
			});
			
			/**
			 * 查询客户基本信息
			 */
			$("#queryCustInfoBtn").on('click',function(){
				/**
				 * 查询客户基本信息
				 */
				// HighCustInfo.queryCustInfo();
				HighCustInfo.queryCustInfoByDiscodeList();
				
				/**
				 * 绑定客户选择事件
				 */
				QueryCustAssert.queryCustAssertBind();
				 
			});
			
			/**
			 * 导出中台资产证明
			 */
			$("#exportBtn").on('click', function(index, element){
				QueryCustAssert.exportAssert("1");
			});

            /**
             * 导出CRM资产证明
             */
            $("#exportCrmBtn").on('click', function(index, element){
                QueryCustAssert.exportAssert("2");// 带章带底纹资产证明
            });

			/**
			 * 导出中台证券类总资产证明
			 */
			$("#exportTotalSecurityBtn").on('click', function(index, element){
				QueryCustAssert.exportAssert("5");
			});

			/**
			 * 导出CRM证券类总资产证明
			 */
			$("#exportTotalCrmSecurityBtn").on('click', function(index, element){
				QueryCustAssert.exportAssert("6");// 带章带底纹资产证明
			});


            /**
			 * 导出中台资产证明（中英文版）
			 */
			$("#exportBtnCE").on('click', function(index, element){
				QueryCustAssert.exportAssert("3");
			});

            /**
             * 导出CRM资产证明（中英文版）
             */
            $("#exportCrmBtnCE").on('click', function(index, element){
                QueryCustAssert.exportAssert("4");// 带章带底纹资产证明
            });

            /**
             * 证券类全选
             */
            $("#selectAllSecutities").on('click', function (index, element) {
                QueryCustAssert.checkedOrUncheckedAll("selectAllSecutities", "selectSecutities");
            });

            /**
             * 股权类全选
             */
            $("#selectAllStockAsset").on('click', function (index, element) {
                QueryCustAssert.checkedOrUncheckedAll("selectAllStockAsset", "selectStockAsset");
            });
		},
		
		/**
		 * 绑定客户
		 */
		queryCustAssertBind:function(){
			
			 $(".selectcust").click(function(){
				 var disCodeList = [];
				 $('input[name="checkCust"]:checked').each(function(){//遍历每一个名字为nodes的复选框，其中选中的执行函数
					 var selectIndex = $(this).attr("index");
					 QueryCustAssert.custInfo = HighCustInfo.custList[selectIndex] || {};
					 disCodeList.push(QueryCustAssert.custInfo.disCode)
				 });
				 console.log(disCodeList);
				 QueryCustAssert.disCodeList= disCodeList;
				 var assetEndDt=$("#assetEndDt").val();
		    	 QueryCustAssert.queryCustAssert(QueryCustAssert.custInfo.custNo || '', disCodeList,assetEndDt);
		    	 
		    });
		},
		
		/**
		 * 查询客户资产
		 * @param custNo 客户号
		 * @param hboneNo 一帐通号
		 */
		queryCustAssert:function(custNo, disCodeList,assetEndDt){
			var  uri= TmsCounterConfig.HIGH_QUERY_CUST_ASSERT||  {};
			var reqparamters = {};
			reqparamters.custNo = custNo;
			reqparamters.disCodeList = JSON.stringify(disCodeList);
			reqparamters.assetEndDt=assetEndDt;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCustAssert.queryCustAssertBack, null);
		},
		
		queryCustAssertBack:function(data){
			var code = data.code || '';
			var desc = data.desc || '';
			if(CommonUtil.isSucc(code)){
				var bodyData = data.body || {};
				var hboneNo = bodyData.hboneNo || '';
				QueryCustAssert.custInfo.hboneNo = hboneNo;

				var securitiesAssetList = bodyData.securitiesAssetList || [];//证券类资产
				var stockAssetList = bodyData.stockAssetList || [];//股权类资产
				var serialNo = bodyData.serialNo || '';//流水号
				QueryCustAssert.serialNo = serialNo;

				QueryCustAssert.createSecuritiesAssetList(securitiesAssetList);//证券类资产列表展示
				QueryCustAssert.createStockAssetList(stockAssetList);//股权类资产列表展示
			}else {
				CommonUtil.layer_tip(desc);
			}
		},
		
		/**
		 * 创建证券类资产列表
		 * @param securitiesAssetList
		 */
		createSecuritiesAssetList : function(securitiesAssetList){
			$("#securitiesAssetListId").html('');
			//证券类资产列表
			if(securitiesAssetList.length <=0){
				var trHtml = '<tr><td colspan="7">没有证券类资产</td></tr>';
				$("#securitiesAssetListId").append(trHtml);
			}else{
				$(securitiesAssetList).each(function(index, element){
					var trSecuritiesList = [];
					trSecuritiesList.push('<input class="selectSecutities" id="'+element.rowKey+'" type="checkbox" index="'+index+'"></input>');
					trSecuritiesList.push(CommonUtil.formatData(element.fundCode));
					trSecuritiesList.push(CommonUtil.formatData(element.fundName));
					trSecuritiesList.push(CommonUtil.formatAmount(element.balance, '--'));
					trSecuritiesList.push(CommonUtil.formatData(element.nav));
					trSecuritiesList.push(CommonUtil.formatData(element.navDate));
					trSecuritiesList.push(CommonUtil.formatAmount(element.market, '--'));
					var trSecuritiesHtml = '<tr class="text-c"><td>'+trSecuritiesList.join('</td><td>') +'</td></tr>';
					$("#securitiesAssetListId").append(trSecuritiesHtml);
					
				});
			}
		},
		/**
		 * 创建股权类资产列表
		 * @param stockAssetList
		 */
		createStockAssetList:function(stockAssetList){
			
			$("#stockAssetListId").html('');
			//股权类资产列表
			if(stockAssetList.length <=0){
				var trHtml = '<tr><td colspan="5">没有股权类资产</td></tr>';
				$("#stockAssetListId").append(trHtml);
			}else{
				$(stockAssetList).each(function(index, element){
					var trStockAssetList = [];
					trStockAssetList.push('<input class="selectStockAsset" id="'+element.rowKey+'" type="checkbox" index="'+index+'"></input>');
					trStockAssetList.push(CommonUtil.formatData(element.fundCode));
					trStockAssetList.push(CommonUtil.formatData(element.fundName));
					trStockAssetList.push(CommonUtil.formatAmount(element.balance));
					trStockAssetList.push(CommonUtil.formatAmount(element.amount));
					var trStockAssetHtml = '<tr class="text-c"><td>'+trStockAssetList.join('</td><td>') +'</td></tr>';
					$("#stockAssetListId").append(trStockAssetHtml);
				});
			}
			
		},
		
		/**
		 * 导出资产证明
		 */
		exportAssert:function(showShadingAndQrCodeFlag){
            var showShadingAndQRCodeFlag = showShadingAndQrCodeFlag || '';
			 var secutitiesRowKeyList = [];
			 $('input[class="selectSecutities"]:checked').each(function(index, element){
				 var rowKey = $(this).attr("id");
				 secutitiesRowKeyList.push(rowKey);
			 });
			 
			 var stockAssetRowKeyList = [];
			 $('input[class="selectStockAsset"]:checked').each(function(index, element){
				 var stockAssetRowKey = $(this).attr("id");
				 stockAssetRowKeyList.push(stockAssetRowKey);
			 });
			 if(stockAssetRowKeyList.length>0&&(showShadingAndQRCodeFlag==="5"||showShadingAndQRCodeFlag==="6")){
				 layer_tip("导出证券类总资产证明不允许选择股权类产品！", null, null);
				 return false;
			 }
			 
			 if(secutitiesRowKeyList.length <= 0 && stockAssetRowKeyList.length <=0 ){
				 layer_tip("请选择要导出的产品资产", null, null);
				 return false;
			 }
			 
			 var secutitiesRowKeys = secutitiesRowKeyList.join(',');
			 var stockAssetRowKeys = stockAssetRowKeyList.join(',');
			 
			 var  uri= TmsCounterConfig.HIGH_GENERATE_CUST_ASSERT_URL ||  '';
				var reqparamters = {};
				reqparamters["secutitiesRowKeys"] = secutitiesRowKeys;//债券类导出资产
				reqparamters ["stockAssetRowKeys"] = stockAssetRowKeys;//股权类导出资产
				reqparamters["serialNo"] = QueryCustAssert.serialNo;//流水号
				reqparamters["hboneNo"] = QueryCustAssert.custInfo.hboneNo;// 一帐通号
                reqparamters["showShadingAndQRCodeFlag"] = showShadingAndQRCodeFlag;// 是否带章带底纹
				var disCode = $("#selectDisCode").val();
				if(!CommonUtil.isEmpty(disCode)){
					reqparamters["disCode"] = disCode;
				}

				reqparamters["disCodeList"] = JSON.stringify(QueryCustAssert.disCodeList);

				var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
				CommonUtil.ajaxAndCallBack(paramters, QueryCustAssert.exportAssertCallBack, null);
	  },
			 
	  exportAssertCallBack:function(data){
		  
			var code = data.code || '';
			var desc = data.desc || '';
			var bodyData = data.body || {};
			var fileName = bodyData.fileName || [];
			if(CommonUtil.isSucc(code)){
				index = layer.load(0,{shade: [0.1,'#fff']});
				// for (var i=0; i<fileName.length; i++) {
				// 	window.location.href = TmsCounterConfig.HIGH_EXPORT_CUST_ASSERT_URL+"?fileName="+fileName[i];
				// }
				for (var i=0; i<fileName.length; i++) {
					var downloadUrl = TmsCounterConfig.HIGH_EXPORT_CUST_ASSERT_URL+"?fileName="+fileName[i];
					QueryCustAssert.downloadInFrame(downloadUrl, "custAssetFrame"+i);
				}
				layer.close(index);
			}else{
				CommonUtil.layer_tip(code+","+desc);
			}
	 },
	downloadInFrame:function(url, frameName) {
		// 删除原来的iframe
		var iframe = window.parent.document.getElementById(frameName);
		if (iframe != null) {
			window.parent.document.body.removeChild(iframe);
		}
		// 创建iframe
		iframe = window.parent.document.createElement("iframe");
		iframe.setAttribute("id", frameName);
		window.parent.document.body.appendChild(iframe);
		// 隐式调用
		iframe.src = url;
		iframe.style.display = "none";
	},

	/**
	 * 获取选中基金代码
	 * CS0001,CS0002
	 */
	getSearchDisCode:function(selectFundCodeList){
		if(isEmptyList(selectFundCodeList)){
			return '';
		}
		return selectFundCodeList.join(',');
	},

    /**
     * 全选或反选
     * @param allId
     */
    checkedOrUncheckedAll:function(allId, className){
        var checked = $("#"+allId).is(':checked');
        if(checked){
            $("input[class='"+className+"'][type='checkbox']").each(function(index,element){
                if($(element).attr("id") != allId){
                    $(element).prop("checked",true);
                }

            });
        }else{
            $("input[class='"+className+"'][type='checkbox']").each(function(index,element){
                if($(element).attr("id") != allId){
                    $(element).prop("checked",false);
                }
            });
        }
    }
};