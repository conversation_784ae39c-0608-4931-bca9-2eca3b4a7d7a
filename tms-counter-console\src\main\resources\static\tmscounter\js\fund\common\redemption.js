/**
 * 公募-赎回去向下拉框构建
 * 
 * 柜台赎回交易录入中增加交易回款方式选项，取值有“回款至银行卡（人工选择）”，“回款至储蓄罐（人工选择）”，“回款至银行卡（协议默认）”，“回款至储蓄罐（协议默认）”，默认值为：
 * a) 赎回产品只支持一种回款方式的，则默认为该回款方式，且不能修改。例如，赎回券商固收、保险类产品时，交易回款方式只能选择“回款至储蓄罐（协议默认）”。
 * b) 赎回产品支持多种回款方式的，根据协议回款方式判断，若协议回款方式为“回款至银行卡（系统默认）”或“回款至银行卡（用户选择）”时，
 *	交易回款方式默认为“回款至银行卡（协议默认）”。若协议回款方式为“回款至储蓄罐（系统默认）”或“回款至储蓄罐（用户选择）”时，交易回款方式默认为“回款至储蓄罐（协议默认）”
 */
var Redemption = {
		
		/**
		 * 构造交易回款方式选择selectHtml
		 * @param collectProtocolMethod 用户回款方式 1-回款至银行卡（系统默认）2-回款至银行卡（用户选择）3-回款至储蓄罐（系统默认）4-回款至储蓄罐（用户选择）
		 * @param invstType 0:机构; 1-个人；(机构不能修改回款方向, 默认只能是"0":"回款至银行卡（协议默认）")
	     * @param redeemDirectionIsSupCardFlag 产品是否支持赎回到银行卡 0-不支持 1-支持
	     * @param redeemDirectionIsSupCxgFlag 产品是否支持赎回到储蓄罐 0-不支持 1-支持
	     *
	     * @return combox options: 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
		 */
		buildSelectRedeemHtml:function(collectProtocolMethod, invstType, redeemDirectionIsSupCardFlag, redeemDirectionIsSupCxgFlag){
			
			var userProtocolMethod  = [];
			//1-回款至银行卡（系统默认）2-回款至银行卡（用户选择）3-回款至储蓄罐（系统默认）4-回款至储蓄罐（用户选择）
			userProtocolMethod[0] = '1';
			userProtocolMethod[1] = '2';
			userProtocolMethod[2] = '3';
			userProtocolMethod[3] = '4';
			
			/** 
			 * 中台: 回款方向
			 "0":"回款至银行卡（协议默认）",
			 "1":"回款至储蓄罐（协议默认）",
			 "2":"回款至银行卡（人工选择）",
			 "3":"回款至储蓄罐（人工选择）"
			**/
			var selectRedeemDirMap = CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG;
			var defalutSelect ='';
	
			//  collectProtocolMethod: 用户回款方式
			if(collectProtocolMethod == userProtocolMethod[0] 
				|| collectProtocolMethod == userProtocolMethod[1]){
				defalutSelect = '0';//银行卡
			} else if(collectProtocolMethod == userProtocolMethod[2]
					|| collectProtocolMethod == userProtocolMethod[3]){
				defalutSelect = '1';//储蓄罐
			}
			
			
			// 系统默认只能是其一显示
			if(defalutSelect == '0'){
				//delete selectRedeemDirMap['1']; 
				selectRedeemDirMap = {
						 "0":"回款至银行卡（协议默认）",
						 "2":"回款至银行卡（人工选择）",
						 "3":"回款至储蓄罐（人工选择）"
				}
			} else if(defalutSelect == '1'){
				//delete selectRedeemDirMap['0']; 
				selectRedeemDirMap = {
						 "1":"回款至储蓄罐（协议默认）",
						 "2":"回款至银行卡（人工选择）",
						 "3":"回款至储蓄罐（人工选择）"
				}
			}
			
			var appendHtml = '';
			if(invstType == '0' || invstType == '2'){// 机构
				appendHtml +='<option value="0" >回款至银行卡（协议默认）</option>';
			} else{
				$.each(selectRedeemDirMap,function(key,value){
					if(!isEmpty(key) && !isEmpty(value)){
						if(!isEmpty(defalutSelect) && defalutSelect == key){
							appendHtml +='<option value=\"'+key+'\"  selected>'+value+'</option>';
						}else{
							appendHtml +='<option value=\"'+key+'\" >'+value+'</option>';
						}
					}
				});	
			}
			
			return appendHtml;
		}
} 