/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.database.RepurchaseTypEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.ModifyRepurchaseProtocolDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.util.CounterOrderFormUtil;
import com.howbuy.tms.high.batch.facade.trade.countermodifyrepurchaseproctol.CounterModifyRepurchaseProctolFacade;
import com.howbuy.tms.high.batch.facade.trade.countermodifyrepurchaseproctol.CounterModifyRepurchaseProctolRequest;
import com.howbuy.tms.high.batch.facade.trade.countermodifyrepurchaseproctol.CounterModifyRepurchaseProctolResponse;
import com.howbuy.tms.high.batch.facade.trade.countermodifyrepurchaseproctol.bean.CounterModifyRepurchaseProctolBean;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @className ModifyRepurchaseProc
 * @description
 * <AUTHOR>
 * @date 2019/3/26 14:40
 */
@Controller
public class ModifyRepurchaseProctocolController{

    @Autowired
    @Qualifier(value = "tmscounter.counterModifyRepurchaseProctolFacade")
    private CounterModifyRepurchaseProctolFacade counterModifyRepurchaseProctolFacade;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @RequestMapping("/tmscounter/modifyrepurchaseprotocol.htm")
    public void modifyRepurchaseProtocol(@RequestParam("modifyRepurchaseProtocol") String modifyRepurchaseProtocol,
                                         HttpServletRequest request,
                                         HttpServletResponse response)throws Exception{
        ModifyRepurchaseProtocolDto modifyRepurchaseProtocolDto = JSON.parseObject(modifyRepurchaseProtocol, ModifyRepurchaseProtocolDto.class);
        QueryCustInfoAndTxAcctForCounterResult custInfoResult = tmsCounterOutService.queryAllCustInfo(modifyRepurchaseProtocolDto.getTxAcctNo(), modifyRepurchaseProtocolDto.getDisCode());
        if(custInfoResult == null){
            throw new TmsCounterException(TmsCounterResultEnum.NOT_FIND_CUST_INFO);
        }
        CustInfoDto custInfoDto = new CustInfoDto();
        BeanUtils.copyProperties(custInfoResult, custInfoDto);
        custInfoDto.setDisCode(modifyRepurchaseProtocolDto.getDisCode());

        //查询产品信息
        HighProductBaseInfoBean highProduct =  queryHighProductOuterService.getHighProductBaseInfo(modifyRepurchaseProtocolDto.getFundCode());
        if(highProduct  == null){
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }

        // 操作员信息
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);

        CounterModifyRepurchaseProctolRequest counterRequest = new CounterModifyRepurchaseProctolRequest();
        String orderFormJson = CounterOrderFormUtil.createCounterOrderFormForRepurchase(modifyRepurchaseProtocolDto);

        CounterModifyRepurchaseProctolBean counterModifyRepurchaseProctolBean = createCounterOrder(modifyRepurchaseProtocolDto,
                custInfoDto, highProduct, operatorInfoCmd);

        counterModifyRepurchaseProctolBean.setOrderFormMemo(orderFormJson);

        counterRequest.setCounterModifyRepurchaseProctolBean(counterModifyRepurchaseProctolBean);
        TmsFacadeUtil.doFillBaseRequest(counterRequest, null);
        CounterModifyRepurchaseProctolResponse resp =  counterModifyRepurchaseProctolFacade.execute(counterRequest);
        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("code", TmsCounterResultEnum.SUCC.getCode());
        rst.put("desc", TmsCounterResultEnum.SUCC.getDesc());

        if (resp != null && !ReturnCodeEnum.SUCC_TMS.getCode().equals(resp.getReturnCode())) {
            rst.put("code", resp.getReturnCode());
            rst.put("desc", resp.getDescription());
        }
        WebUtil.write(response, rst);
    }

    private String getRepurchaeType(BigDecimal repurchaseVol, BigDecimal totalVol){
        if(repurchaseVol == null || BigDecimal.ZERO.compareTo(repurchaseVol) == 0){
            return RepurchaseTypEnum.ALL_REDEEEM.getCode();
        }else if(repurchaseVol.compareTo(totalVol) == 0 ){
            return RepurchaseTypEnum.ALL_REPURCHASE.getCode();
        }else{
            return RepurchaseTypEnum.PART_REDEEM.getCode();
        }
    }

    private CounterModifyRepurchaseProctolBean createCounterOrder(ModifyRepurchaseProtocolDto modifyRepurchaseProtocolDto,
                                                                  CustInfoDto custInfoDto,  HighProductBaseInfoBean highProduct,
                                                                  OperatorInfoCmd operatorInfoCmd) {
        CounterModifyRepurchaseProctolBean bean = new CounterModifyRepurchaseProctolBean();
        bean.setTxAcctNo(modifyRepurchaseProtocolDto.getTxAcctNo());
        bean.setDisCode(custInfoDto.getDisCode());
        bean.setFundCode(modifyRepurchaseProtocolDto.getFundCode());
        bean.setFundShareClass(highProduct.getShareClass());
        bean.setAppVol(modifyRepurchaseProtocolDto.getRepurchaseVol());
        bean.setRepurchaseProtocolNo(modifyRepurchaseProtocolDto.getRepurchaseProtocolNo());
        // 创建人
        bean.setCreator(operatorInfoCmd.getOperatorNo());
        // 修改人
        bean.setModifier(operatorInfoCmd.getOperatorNo());
        bean.setCreateDtm(new Date());
        bean.setUpdateDtm(new Date());
        bean.setIdNo(PrivacyUtil.encryptIdCard(custInfoDto.getIdNo()));
        bean.setCustName(custInfoDto.getCustName());
        bean.setFundName(highProduct.getFundName());
        bean.setTaCode(highProduct.getTaCode());
        bean.setRepurchaseType(getRepurchaeType(modifyRepurchaseProtocolDto.getRepurchaseVol(), modifyRepurchaseProtocolDto.getBalanceVol()));
        return bean;
    }


}
