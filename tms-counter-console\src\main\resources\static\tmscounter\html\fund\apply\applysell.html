<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>赎回</title>
</head>

<body>
    <div class="page-container">
        <div class="containner_all">
             <p class="mainTitle mt10">赎回</p>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>                   
                        <th>风险等级</th>
                        <th>分销机构</th>                        
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <form action="" id="sellConfirmForm">
        <p class="main_title mt30">录入订单信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                    <tr class="text-c">
                        <td>基金/产品代码</td>
                        <td>
                            <div class="searchIn"><input id="fundCode" type="text" readonly="true"><a href="javascript:void(0)" ></a></div>
                        </td>
                        <td>基金/产品状态</td>
                        <td id="fundStatus">--</td>
                    </tr>
                    <tr class="text-c" >
                        <td>基金/产品简称</td>
                        <td id="fundName">--</td>
                        <td>银行账户</td>
                        <td>
                            <span class="select-box inline">
                                <select name="cpAcctNo" class="select" id="selectBank">
                                </select>
                            </span>
                        </td>
                        <input type="hidden" id="bankCode" name="bankCode" value="0">
                    </tr>
                    <tr class="text-c">
                        <td>当前可用份额</td>
                        <td id="availVol"></td>
                        <td>赎回比例(%)</td>
                        <td>
                            <input type="text" placeholder="请输入" class="appRatioForSell" name="appRatio" id="appRatio" datatype="s" errormsg="赎回比例" >
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>申请份额（份）</td>
                        <td>
                            <input type="text" placeholder="请输入" class="appVolForSell" name="appVol" id="appVol" isnull="false" datatype="s" errormsg="申请份额">
                        </td>
                        <td>申请份额（大写）</td>
                        <td>
                            <input type="text" class="appVolCapitalForSell" name="appVolCapitalForSell" id="appVolCapitalForSell" isnull="false" datatype="s" errormsg="申请份额" readonly='true'>
                        </td>

                    </tr>
                    <tr class="text-c" >
                        <td>巨额赎回顺延标记：</td>
                        <td>
	                  		<span class="select-box inline">
	                    		<select id="largeRedmFlag" name="largeRedmFlag" class="select"  isnull="false" datatype="s" errormsg="巨额赎回顺延标记" >
	                    			<option  value="0">不顺延</option>
	                    			<option  selected="selected" value="1">顺延</option>
	                    		</select>
	                		</span>
                        </td>
                        <td>赎回不出款标记：</td>
              			<td>
	                  		<span class="select-box inline">
                                <select name="unusualTransType" class="select selectUnusualTransType" isnull="false" datatype="s" errormsg="异常交易标识">
                                </select>
                            </span>
                		</td>

                    </tr>

                </tbody>
            </table>
        </div>
         </form>
        <p class="main_title mt30">其他信息</p>
       <form id="transactorInfoForm" >
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
                <tr class="text-c">
                    <td>下单日期</td>
                    <td>
                        <input class="input-text laydate-icon"  id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" readonly="true">
                    </td>
                    <td>下单时间</td>
                    <td>
                        <input class="" type="text" id="appTm" name="appTm" isnull="false" datatype="s" errormsg="下单时间" maxlength = "6">
                    </td>
                </tr>
                 <tr class="text-c">
                   	<td>网点：</td>
                   	<td>
                   		柜台<input type="hidden" name="outletCode" value="W20170215"/>
                   	</td>
                   	<td>投资顾问代码：</td>
              		<td>
                  		<span class="select-box inline">
                    		<select id="consCode" name="consCode" class="select selectconsCode" ></select>
                		</span>
                	</td>
                </tr>
            	<tr class="text-c">
                   	<td>经办人姓名：</td>
                   	<td>
                   		<input id="transactorName" type="text" placeholder="请输入"  name="transactorName"  datatype="s" errormsg="经办人姓名">
                   	</td>
                   	<td>经办人证件类型：</td>
              		<td>
                  		<span class="select-box inline">
                    		<select id="transactorIdType" name="transactorIdType" class="select selectTransactorIdType"   datatype="s" errormsg="经办人证件类型" >
                    		</select>
                		</span>
                	</td>
                </tr>
             	<tr class="text-c">
              		<td>经办人证件号：</td>
              		<td> <input type="text" placeholder="请输入"  id="transactorIdNo" name="transactorIdNo"  datatype="s" errormsg="经办人证件号" ></td>
               		<td>失败原因</td>
               		<td>
                        <input type="text" placeholder='请输入失败原因' id="checkFaildDesc" name="checkFaildDesc">
                    </td>
               	</tr>
               	<tr class="text-c">
               		<td>是否经办</td>	
               		<td>
               			<span class="select-box inline">
	                   		<select name="agentFlag" class="select selectAgened">
	                      		<option value="0">否</option>
	                     		 <option value="1">是</option>
	                   		</select>
                		</span>
                	</td>
               	</tr>
            </tbody>
          </table>
        </div>
         </form>
         
        <p class="mt10 text-c">
            <a href="javascript:void(0)" id ="abolishBtn" class="btn btn-default radius">废单</a> 
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <a href="javascript:void(0)" id ="confimSellBtn" class="btn radius btn-secondary confimBtn">提交</a>
        </p>
        
        <div class="clear page_all">
            	<div class="fy_part fr mt20" style="display: none" id="pageView"></div>
        </div>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
   <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/apply/applysell.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/queryfundinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycheckorder.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/validate.js"></script>
     <script type="text/javascript" src="../../../js/fund/check/countercheck.js"></script>
     <script type="text/javascript" src="../../../js/fund/check/counterabolish.js"></script>
  <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
</body>

</html>