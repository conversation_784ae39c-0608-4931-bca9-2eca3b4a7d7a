/**
 *Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller.vo;

import java.io.Serializable;

/**
 * @description:银行卡对象 
 * @reason:银行卡对象
 * <AUTHOR>
 * @date 2021年9月14日 上午11:12:00
 * @since JDK 1.6
 */
public class QueryBankCardInfo implements Serializable{

	private static final long serialVersionUID = 1177279005696043698L;
	
	/**
	 *  交易账号
	 */
    private String txAcctNo;
    /**
     *  资金账号
     */
    private String cpAcctNo;
    /**
     *  银行卡号
     */
    private String bankAcct;
    
    private String bankAcctDigest;
    
    private String bankAcctMask;
    
    private String bankAcctCipher;

    /**
     *  银行编号
     */
    private String bankCode;
    /**
     *  分行代码
     */
    private String bankRegionCode;
    /**
     *  分行名称
     */
    private String bankRegionName;
    /**
     *  银行账户名称
     */
    private String bankAcctName;
    /**
     *  银行账户状态0-正常；1-待审核；2-注销；3-冻结；4-销户待确认；5-冻结待确认
     */
    private String bankAcctStatus;
    /**
     *  银行卡验证状态 2-验证通过，3-验证失败
     */
    private String bankAcctVrfyStat;
    /**
     *  开户行省份编码
     */
    private String provCode;
    /**
     *  开户行城市编码
     */
    private String cityCode;
    /**
     *  银行名称
     */
    private String bankName;
    /**
     *  代扣签约状态 1-未开通；2-开通；3-关闭
     */
    private String paySign;
    /**
     *  代扣签约日期
     */
    private String paySignDt;
    /**
     *  银行预留手机
     */
    private String mobileBank;
    
    private String mobileBankDigest;
    
    private String mobileBankMask;
    
    private String mobileBankCipher;

    private String mobileVrfyStat;

	public String getTxAcctNo() {
		return txAcctNo;
	}

	public void setTxAcctNo(String txAcctNo) {
		this.txAcctNo = txAcctNo;
	}

	public String getCpAcctNo() {
		return cpAcctNo;
	}

	public void setCpAcctNo(String cpAcctNo) {
		this.cpAcctNo = cpAcctNo;
	}

	public String getBankAcct() {
		return bankAcct;
	}

	public void setBankAcct(String bankAcct) {
		this.bankAcct = bankAcct;
	}

	public String getBankAcctDigest() {
		return bankAcctDigest;
	}

	public void setBankAcctDigest(String bankAcctDigest) {
		this.bankAcctDigest = bankAcctDigest;
	}

	public String getBankAcctMask() {
		return bankAcctMask;
	}

	public void setBankAcctMask(String bankAcctMask) {
		this.bankAcctMask = bankAcctMask;
	}

	public String getBankAcctCipher() {
		return bankAcctCipher;
	}

	public void setBankAcctCipher(String bankAcctCipher) {
		this.bankAcctCipher = bankAcctCipher;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getBankRegionCode() {
		return bankRegionCode;
	}

	public void setBankRegionCode(String bankRegionCode) {
		this.bankRegionCode = bankRegionCode;
	}

	public String getBankRegionName() {
		return bankRegionName;
	}

	public void setBankRegionName(String bankRegionName) {
		this.bankRegionName = bankRegionName;
	}

	public String getBankAcctName() {
		return bankAcctName;
	}

	public void setBankAcctName(String bankAcctName) {
		this.bankAcctName = bankAcctName;
	}

	public String getBankAcctStatus() {
		return bankAcctStatus;
	}

	public void setBankAcctStatus(String bankAcctStatus) {
		this.bankAcctStatus = bankAcctStatus;
	}

	public String getBankAcctVrfyStat() {
		return bankAcctVrfyStat;
	}

	public void setBankAcctVrfyStat(String bankAcctVrfyStat) {
		this.bankAcctVrfyStat = bankAcctVrfyStat;
	}

	public String getProvCode() {
		return provCode;
	}

	public void setProvCode(String provCode) {
		this.provCode = provCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getPaySign() {
		return paySign;
	}

	public void setPaySign(String paySign) {
		this.paySign = paySign;
	}

	public String getPaySignDt() {
		return paySignDt;
	}

	public void setPaySignDt(String paySignDt) {
		this.paySignDt = paySignDt;
	}

	public String getMobileBank() {
		return mobileBank;
	}

	public void setMobileBank(String mobileBank) {
		this.mobileBank = mobileBank;
	}

	public String getMobileBankDigest() {
		return mobileBankDigest;
	}

	public void setMobileBankDigest(String mobileBankDigest) {
		this.mobileBankDigest = mobileBankDigest;
	}

	public String getMobileBankMask() {
		return mobileBankMask;
	}

	public void setMobileBankMask(String mobileBankMask) {
		this.mobileBankMask = mobileBankMask;
	}

	public String getMobileBankCipher() {
		return mobileBankCipher;
	}

	public void setMobileBankCipher(String mobileBankCipher) {
		this.mobileBankCipher = mobileBankCipher;
	}

	public String getMobileVrfyStat() {
		return mobileVrfyStat;
	}

	public void setMobileVrfyStat(String mobileVrfyStat) {
		this.mobileVrfyStat = mobileVrfyStat;
	}

}

