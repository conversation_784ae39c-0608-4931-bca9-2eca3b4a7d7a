/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.utils;

import com.howbuy.tms.common.utils.StringUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * @description:(TODO 请在此添加描述)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年11月14日 下午4:28:37
 * @since JDK 1.6
 */
public class ExcelExportUtils {
    // 默认字体
    private static String excelfont = "微软雅黑";

    /**
     * 
     * @param excelName
     *            导出的EXCEL名字
     * @param sheetName
     *            导出的SHEET名字 当前sheet数目只为1
     * @param headers
     *            导出的表格的表头
     * @param ds_titles
     *            导出的数据 map.get(key) 对应的 key
     * @param ds_format
     *            导出数据的样式 1:String left; 2:String center 3:String right 4 int
     *            right 5:float ###,###.## right 6:number: #.00% 百分比 right
     * @param widths
     *            表格的列宽度 默认为 256*14
     * @param data
     *            数据集 List<Map>
     * @param response
     * @throws IOException
     */
    @SuppressWarnings({ "resource", "rawtypes", "unchecked" })
    public static void export(String excelName, String sheetName, String[] headers, String[] ds_titles, int[] ds_format, int[] widths,
            List<Map<String, Object>> data, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (ds_format == null) {
            ds_format = new int[ds_titles.length];
            for (int i = 0; i < ds_titles.length; i++) {
                ds_format[i] = 1;
            }
        }
        // 设置文件名
        String fileName = "";
        if (StringUtils.isNotEmpty(excelName)) {
            fileName = excelName;
        }
        // 创建一个工作薄
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建一个sheet
        HSSFSheet sheet = wb.createSheet(StringUtils.isNotEmpty(sheetName) ? sheetName : "excel");
        
        // 创建表头，如果没有跳过
        int headerrow = 0;
        if (headers != null) {
            HSSFRow row = sheet.createRow(headerrow);
            // 表头样式
            HSSFCellStyle style = wb.createCellStyle();
            HSSFFont font = wb.createFont();
            font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
            font.setFontName(excelfont);
            font.setFontHeightInPoints((short) 11);
            style.setFont(font);
            style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
            style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
            style.setBorderRight(HSSFCellStyle.BORDER_THIN);
            style.setBorderTop(HSSFCellStyle.BORDER_THIN);
            for (int i = 0; i < headers.length; i++) {
                HSSFCell cell = row.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(style);
            }
            headerrow++;
        }
        praseBody(ds_titles, ds_format, data, wb, sheet, headerrow);

        // 设置列宽
        setColumnWidth(sheet, widths, ds_titles);
        
        fileName = fileName + ".xls";
        String filename = "";
        try {
            filename = encodeChineseDownloadFileName(request, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", filename);
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment;filename=" + filename);
        response.setHeader("Pragma", "No-cache");
        OutputStream ouputStream = response.getOutputStream();
        wb.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    private static void praseBody(String[] ds_titles, int[] ds_format, List<Map<String, Object>> data, HSSFWorkbook wb, HSSFSheet sheet, int headerrow) {
        // 表格主体 解析list
        if (data != null) {
            List styleList = new ArrayList();

            for (int i = 0; i < ds_titles.length; i++) { // 列数
                HSSFCellStyle style = wb.createCellStyle();
                HSSFFont font = wb.createFont();
                font.setFontName(excelfont);
                font.setFontHeightInPoints((short) 10);
                style.setFont(font);
                style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
                style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
                style.setBorderRight(HSSFCellStyle.BORDER_THIN);
                style.setBorderTop(HSSFCellStyle.BORDER_THIN);

                if (ds_format[i] == 1) {
                    style.setAlignment(HSSFCellStyle.ALIGN_LEFT);
                } else if (ds_format[i] == 2) {
                    style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
                } else if (ds_format[i] == 3) {
                    style.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
                } // int类型 }
                else if (ds_format[i] == 4) {
                    style.setAlignment(HSSFCellStyle.ALIGN_RIGHT); // int类型
                    style.setDataFormat(HSSFDataFormat.getBuiltinFormat("0"));
                } else if (ds_format[i] == 5) { // float类型
                    style.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
                    style.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0.00"));
                } else if (ds_format[i] == 6) { // 百分比类型
                    style.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
                    style.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00%"));
                }
                styleList.add(style);
            }

            for (int i = 0; i < data.size(); i++) { // 行数
                HSSFRow row = sheet.createRow(headerrow);
                Map map = data.get(i);
                for (int j = 0; j < ds_titles.length; j++) { // 列数
                    HSSFCell cell = row.createCell(j);
                    Object o = map.get(ds_titles[j]);
                    if (o == null || "".equals(o)) {
                        cell.setCellValue("");
                    } else if (ds_format[j] == 4) { // int
                        cell.setCellValue(Long.parseLong((map.get(ds_titles[j])) + ""));
                    } else if (ds_format[j] == 5 || ds_format[j] == 6) { // float
                        cell.setCellValue(Double.parseDouble((map.get(ds_titles[j])) + ""));
                    } else {
                        cell.setCellValue(map.get(ds_titles[j]) + "");
                    }

                    cell.setCellStyle((HSSFCellStyle) styleList.get(j));
                }
                headerrow++;
            }
        }
    }

    /**
     * 
     * setColumnWidth:(设置列宽)
     * 
     * @param sheet
     * @param widths
     * @param ds_titles
     * <AUTHOR>
     * @date 2019年6月6日 下午3:10:13
     */
    private static void setColumnWidth(HSSFSheet sheet, int[] widths, String[] ds_titles) {
        Assert.notNull(sheet, "sheet is null");
        if (ArrayUtils.isEmpty(widths)) {
            if (ArrayUtils.isEmpty(ds_titles)) {
                return;
            }

            for (int i = 0; i < ds_titles.length; i++) {
                sheet.autoSizeColumn(i, true);
            }

            return;
        }

        int columnIndex = 0;
        for (int colWidth : widths) {
            sheet.setColumnWidth(columnIndex, colWidth);
            columnIndex++;
        }
    }

    /**
     * 对文件流输出下载的中文文件名进行编码 屏蔽各种浏览器版本的差异性
     * 
     * @throws UnsupportedEncodingException
     */
    public static String encodeChineseDownloadFileName(HttpServletRequest request, String pFileName) throws Exception {

        String filename = null;
        String agent = request.getHeader("USER-AGENT");
        if (null != agent) {
            if (-1 != agent.indexOf("Firefox")) {// Firefox
                filename = "=?UTF-8?B?" + (new String(org.apache.commons.codec.binary.Base64.encodeBase64(pFileName.getBytes("UTF-8")))) + "?=";
            } else if (-1 != agent.indexOf("Chrome")) {// Chrome
                filename = new String(pFileName.getBytes(), "ISO8859-1");
            } else {// IE7+
                filename = java.net.URLEncoder.encode(pFileName, "UTF-8");
                filename = filename.replace("+", "%20");
            }
        } else {
            filename = pFileName;
        }
        return filename;
    }

    /**
     * excel列配置信息
     * @author: huaqiang.liu
     * @date: 2020/11/4 13:51
     * @since JDK 1.8
     */
    public static class ExcelColumConfig {
        private List<String> headerList = new LinkedList<>();
        private List<String> titleList = new LinkedList<>();
        private List<Integer> formatList = new LinkedList<>();
        /**
         * 添加一列
         * @param header
         * @param title
         * @param format 导出数据的样式 1:String left; 2:String center 3:String right 4 int
         *                right 5:float ###,###.## right 6:number: #.00% 百分比 right
         * @return void
         * @author: huaqiang.liu
         * @date: 2020/11/4 13:52
         * @since JDK 1.8
         */
        public void add(String header, String title, Integer format) {
            headerList.add(header);
            titleList.add(title);
            formatList.add(format);
        }
        /**
         * 添加一个string居中列
         * @param header
         * @param title
         * @return void
         * @author: huaqiang.liu
         * @date: 2020/11/4 13:54
         * @since JDK 1.8
         */
        public void addStringCenter(String header, String title) {
            headerList.add(header);
            titleList.add(title);
            formatList.add(2);
        }

        public String[] getHeaders(){
            return headerList.toArray(new String[headerList.size()]);
        }
        public String[] getTitles() {
            return titleList.toArray(new String[titleList.size()]);
        }
        public int[] getFormats() {
            int[] formats = new int[formatList.size()];
            Iterator<Integer> iterator = formatList.iterator();
            int i = -1;
            while (iterator.hasNext()) {
                i++;
                formats[i] = iterator.next();
            }
            return formats;
        }
    }
}
