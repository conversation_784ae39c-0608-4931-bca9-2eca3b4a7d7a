/**
 * 初始化页面事件
 */

$(function () {
    // 初始化下拉框
    QuerySubsAmtDetail.initSelect();

    // 初始化按钮时间
    QuerySubsAmtDetail.initBtn();
});

var QuerySubsAmtDetail = {

    /**
     * 初始化按钮绑定事件
     */
    initBtn: function () {
        $("#queryBtn").off();
        $("#queryBtn").on('click', function () {
            QuerySubsAmtDetail.querySubsAmtList();
        });

        $("#resetProductLayerBtn").off();
        $("#resetProductLayerBtn").on('click', function () {
            QuerySubsAmtDetail.reset('searchProductFormId');
        });

        //给父页面传值
        $('#submitProdBtn').on('click', function () {
            var checkedInputs = $("input[class='selectProduct'][type='checkbox']:checked");
            if (checkedInputs.length <= 0) {
                showMsg("必须选中一个产品");
                return false;
            } else if (checkedInputs.length > 1) {
                showMsg("不能多选，只能选择一个产品");
                return false;
            } else {
                var productCode = $(checkedInputs[0]).val();
                var targetBackId = parent.$('#parentTargetFundId').val();
                parent.$('#' + targetBackId).val(productCode);

                $("#layerrs").empty();
                QueryHighProdInfoSubPage.reset('searchProductFormId');
                parent.layer.closeAll();
            }
        });
    },

    /**
     * 初始化下拉框
     */
    initSelect: function () {
        var selectProductChannelHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PRODUCT_CHANNEL_MAP);
        $("#selectProductChannel").html(selectProductChannelHtml);
    },
    /**
     * 产品信息查询弹出
     */
    selectProductCode: function (targetobj, targetBackId) {

        //父页面目标回写对象
        targetBackId = targetBackId || 'fundCode';
        $("#parentTargetFundId").val(targetBackId);

        layer.open({
            type: 2,
            shade: [0.1, '#fff'],
            title: '产品信息查询',
            area: ['800px', '400px'],
            offset: 'auto',
            content: '../../../html/high/query/queryhighproductinfosubpage.html',
        });
    },
    /**
     * 查询认缴信息列表
     */
    querySubsAmtList: function () {
        var searchCheckForm = $("#searchCheckForm").serializeObject();
        var custNo = searchCheckForm['custNo'];
        var disCode = searchCheckForm['disCode'];
        if (isEmpty(custNo)) {
            showMsg("客户号不能为空");
            return false;
        }
        if (isEmpty(disCode)) {
            showMsg("分销编码不能为空");
            return false;
        }
        var uri = TmsCounterConfig.HIGH_QUERY_SUBS_AMT_LIST || "";
        var reqparamters = {};
        reqparamters.custNo = custNo;
        reqparamters.disCode = disCode;
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
        CommonUtil.ajaxAndCallBack(paramters, QuerySubsAmtDetail.processSubsAmtListView);

    },

    processSubsAmtListView: function (data) {
        QuerySubsAmtDetail.subsAmtList = data.body.rsList || [];
        $("#rsList").empty();
        if (QuerySubsAmtDetail.subsAmtList.length <= 0) {
            var trHtml = '<tr class="text-c" ><td colspan="9">暂无维护记录</td></tr>';
            $("#rsList").append(trHtml);
        }

        $(QuerySubsAmtDetail.subsAmtList).each(function (index, element) {
            var trList = [];
            var modifyBtn = '<a id="modify' + element.txAcctNo + "_" + element.fundCode + '" class="modifyBtn btn btn-success radius" href="javascript:void(0);" indexvalue = ' + index + '>修改认缴金额</a>';
            trList.push(modifyBtn);
            trList.push(CommonUtil.formatData(element.txAcctNo));
            trList.push(CommonUtil.formatData(element.custName));
            trList.push(CommonUtil.formatAmount(element.fundCode));
            trList.push(CommonUtil.formatAmount(element.fundName));
            trList.push(CommonUtil.formatAmount(element.subsAmt));
            trList.push(CommonUtil.formatAmount(element.balanceVol));
            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            $("#rsList").append(trHtml);
            $("#modify" + element.txAcctNo + "_" + element.fundCode).attr("txAcctNo", element.txAcctNo).attr("fundCode", element.fundCode);
        });

        function subsAmtChangerUrl(params) {
            return "../trade/subsAmtChangeDetail.html" + "?" + params;
        }

        //viewType 0-查看；1-审核；2-修改
        //修改
        $(".modifyBtn").off();
        $(".modifyBtn").on('click', function () {
            var txAcctNo = $(this).attr("txAcctNo");
            var fundCode = $(this).attr("fundCode");

            var params = [];
            params.push('txAcctNo=' + txAcctNo);
            params.push('fundCode=' + fundCode);
            params.push('viewType=2');
            var urlParams = ViewDealCommon.buildParams(params);
            var viewUrl = subsAmtChangerUrl(urlParams);
            ViewDealCommon.showDeal(viewUrl);
        });

    },


    /**
     * 重置查询条件
     * @param formId
     */
    reset: function (formId) {
        $("#" + formId).find("input").each(function (index, element) {
            $(element).val('');
        });

        $("#" + formId).find("select").each(function (index, element) {
            $(element).val('');
        });
    },


};