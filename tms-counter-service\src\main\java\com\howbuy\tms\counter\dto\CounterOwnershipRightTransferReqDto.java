package com.howbuy.tms.counter.dto;

import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.ExceptionCodes;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

/**
 * @Description:股权份额转让申请请求入参
 * @Author: yun.lu
 * Date: 2023/5/19 15:26
 */
@Data
public class CounterOwnershipRightTransferReqDto extends BaseDto {
    /**
     * 客户订单号
     */
    private String dealDtlNo;

    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 是否非交易过户
     */
    private String isNoTradeTransfer;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 过户份额对应的认缴金额
     */
    private BigDecimal subsAmt;
    /**
     * 总认缴金额
     */
    private BigDecimal totalSubsAmt;
    /**
     * 过户份额
     */
    private BigDecimal transferVol;


    public CounterOwnershipRightTransferReqDto(HttpServletRequest request, OperatorInfoCmd operatorInfoCmd) {
        this.dealDtlNo = request.getParameter("dealDtlNo");
        String strPrice = request.getParameter("transferPrice");
        if (StringUtils.isNotBlank(strPrice)) {
            this.transferPrice = new BigDecimal(strPrice);
        }
        this.isNoTradeTransfer = request.getParameter("isNoTradeTransfer");
        String mBusinessCode = request.getParameter("mBusinessCode");

        // 如果不是非交易转让,转让金额设为0
        if (YesOrNoEnum.NO.getCode().equals(isNoTradeTransfer) &&
                (!BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode().equals(mBusinessCode)
                        && !BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode().equals(mBusinessCode))) {
            this.transferPrice = BigDecimal.ZERO;
        }
        this.operatorNo = operatorInfoCmd.getOperatorNo();
        this.creator = operatorInfoCmd.getOperatorNo();
        String subsAmt = request.getParameter("subsAmt");
        if (StringUtils.isNotBlank(subsAmt)) {
            this.subsAmt = new BigDecimal(subsAmt);
        }
        String totalSubsAmt = request.getParameter("totalSubsAmt");
        if (StringUtils.isNotBlank(totalSubsAmt)) {
            this.totalSubsAmt = new BigDecimal(totalSubsAmt);
        }
        String transferVol = request.getParameter("transferVol");
        if (StringUtils.isNotBlank(transferVol)) {
            this.transferVol = new BigDecimal(transferVol);
        }
    }

    /**
     * 非空校验
     */
    public void check() {
        // 订单号不能为空
        if (StringUtils.isBlank(dealDtlNo)) {
            throw new TmsCounterException(ExceptionCodes.PARAMS_ERROR, "订单号不能为空");
        }
        // 非交易转让,转让金额不能为空/为负数
        if (YesOrNoEnum.YES.getCode().equals(isNoTradeTransfer)) {
            if (transferPrice == null || BigDecimal.ZERO.compareTo(transferPrice) > 0) {
                throw new TmsCounterException(ExceptionCodes.PARAMS_ERROR, "非交易转让,转让金额不能为空/为负数");
            }
        }
        // 过户份额对应的认缴金额不能为空
        if (subsAmt == null) {
            throw new TmsCounterException(ExceptionCodes.PARAMS_ERROR, "过户份额对应的认缴金额不能为空");
        }
        // 过户的总认缴金额不能为空
        if (totalSubsAmt == null) {
            throw new TmsCounterException(ExceptionCodes.PARAMS_ERROR, "过户的总认缴金额不能为空");
        }
    }
}
