/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @description:(柜台份额合并或迁移订单)
 * <AUTHOR>
 * @date 2018年5月8日 上午10:55:21
 * @since JDK 1.6
 */
public class ShareMergeVolOrderReqDto implements Serializable {
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -3009068131661180360L;
    
    /**
     * 审核申请明细流水号
     */
    private String dealDtlAppNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 基金简称
     */
    private String fundName;
    /**
     * 份额类型：A-前收费；B-后收费
     */
    private String fundShareClass;
    /**
     * 基金TA代码
     */
    private String taCode;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 协议类型
     */
    private String protocolType;
    
    /**
     * 交易通道
     */
    private String productChannel;
    
    /**
     * 冻结份额
     */
    private BigDecimal unconfirmedVol;
    /**
     * 持仓总份额
     */
    private BigDecimal balanceVol;
    
    /**
     * 银行卡号
     */
    private String bankAcct;
    /**
     * 银行编码
     */
    private String bankCode;
    
    /**
     * 转出申请份额
     */
    private BigDecimal appVol;
    
    /**
     * 转入前份额
     */
    private BigDecimal preAppVol;
    
    /**
     * 分销
     */
    private String  disCode;


    /**
     * 银行账号摘要
     */
    private String  bankAcctDigest;

    /**
     * 交易账号
     */
    private String  custNo;

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getBankAcctDigest() {
        return bankAcctDigest;
    }

    public void setBankAcctDigest(String bankAcctDigest) {
        this.bankAcctDigest = bankAcctDigest;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getDealDtlAppNo() {
        return dealDtlAppNo;
    }

    public void setDealDtlAppNo(String dealDtlAppNo) {
        this.dealDtlAppNo = dealDtlAppNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public BigDecimal getUnconfirmedVol() {
        return unconfirmedVol;
    }

    public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
        this.unconfirmedVol = unconfirmedVol;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public BigDecimal getPreAppVol() {
        return preAppVol;
    }

    public void setPreAppVol(BigDecimal preAppVol) {
        this.preAppVol = preAppVol;
    }
}
