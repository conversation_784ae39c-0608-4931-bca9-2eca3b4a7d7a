/**
 * Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.cmd;

import java.io.Serializable;

/**
 * @className QueryOrderFileCmd
 * @description
 * <AUTHOR>
 * @date 2019/5/24 15:59
 */
public class QueryOrderFileCmd implements Serializable {

    private static final long serialVersionUID = -5913018667255791765L;
    /**
     * 业务订单ID
     */
    private String orderid;

    /**
     * 一账通帐号
     */
    private String hboneno;

    /**
     * 产品代码
     */
    private String pcode;

    /**
     * 预约ID
     */
    private String preid;

    /**
     * 业务类型ID
     */
    private String busiid;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getHboneno() {
        return hboneno;
    }

    public void setHboneno(String hboneno) {
        this.hboneno = hboneno;
    }

    public String getPcode() {
        return pcode;
    }

    public void setPcode(String pcode) {
        this.pcode = pcode;
    }

    public String getPreid() {
        return preid;
    }

    public void setPreid(String preid) {
        this.preid = preid;
    }

    public String getBusiid() {
        return busiid;
    }

    public void setBusiid(String busiid) {
        this.busiid = busiid;
    }
}
