package com.howbuy.tms.counter.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日经基金修枚举配置
 * [{"code":"1","desc":"日本"},{"code":"2","desc":"韩国"},{"code":"9","desc":"亚太"}]
 * @return
 * @author: junkai.du
 * @date: 2024/7/17 16:55
 * @since JDK 1.8
 */
@Configuration
@RefreshScope
public class FundRegionEnumConfig {
    protected static Logger logger = LogManager.getLogger(FundRegionEnumConfig.class);


    @Autowired
    private TmsCounterNacosConfig tmsCounterNacosConfig;

    private static final List<FundRegion> fundRegionList = new ArrayList<>();

    @PostConstruct
    public void init() {
        if (StringUtils.isBlank(tmsCounterNacosConfig.getFundRegionEnum())){
            return;
        }
        try {
            List<FundRegion> fundProductVos = JSON.parseObject(tmsCounterNacosConfig.getFundRegionEnum(), new TypeReference<List<FundRegion>>(){});
            fundRegionList.addAll(fundProductVos);
        }catch (Exception e){
            logger.error("初始化日经枚举出错",e);
        }

    }

    /**
     * 获取区域枚举
     *
     * @param code
     * @return com.howbuy.tms.orders.infrastructure.config.FundRegionEnumConfig.FundRegion
     * @author: junkai.du
     * @date: 2024/7/17 16:54
     * @since JDK 1.8
     */
    public static FundRegionEnumConfig.FundRegion getFundRegionByCode(String code) {
        return fundRegionList
                .stream()
                .filter(region -> region.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有区域代码
     *
     * @return com.howbuy.tms.orders.infrastructure.config.FundRegionEnumConfig.FundRegion
     * @author: junkai.du
     * @date: 2024/7/17 16:54
     * @since JDK 1.8
     */
    public static List<String> getAllCode() {
        return fundRegionList
                .stream()
                .map(FundRegionEnumConfig.FundRegion::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 根据区域代码获取区域名称
     *
     * @return com.howbuy.tms.orders.infrastructure.config.FundRegionEnumConfig.FundRegion
     * @author: junkai.du
     * @date: 2024/7/17 16:54
     * @since JDK 1.8
     */
    public static String getDescByCode(String code) {
        return fundRegionList
                .stream()
                .filter(region -> region.getCode().equals(code))
                .map(FundRegionEnumConfig.FundRegion::getDesc)
                .findFirst()
                .orElse(null);
    }

    public List<FundRegion> getFundRegionEnum() {
        return fundRegionList;
    }

    public static class FundRegion {
        private String code;
        private String desc;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
