package com.howbuy.tms.counter.dto;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/30 19:55
 * @since JDK 1.8
 */
public class QueryRedmProductListResDto implements Serializable {

    private static final long serialVersionUID = 1869764104455532545L;

    private String canRedeem;

    private String redeemReason;

    private String reason;

    private String latestPurFlag;

    private String adviceDay;

    private String appRatio;

    private boolean checkIn = true;

    private String t0Flag;

    /**
     * 试算赎回
     */
    private List<RedeemProductResDto> redeemProductResDtos = new ArrayList<>();

    /**
     * 银行卡列表
     */
    private List<BankAcctSensitiveResModel> bankAcctSensitiveResModels = new ArrayList<>();

    /**
     * 当前持仓列表
     */
    private List<CustBalanceResDto> custBalanceResDtos = new ArrayList<>();

    /**
     * 当前平衡开关
     */
    private List<CustProtocolOpenResDto> custProtocolOpenResDtos = new ArrayList<>();

    public List<CustProtocolOpenResDto> getCustProtocolOpenResDtos() {
        return custProtocolOpenResDtos;
    }

    public void setCustProtocolOpenResDtos(List<CustProtocolOpenResDto> custProtocolOpenResDtos) {
        this.custProtocolOpenResDtos = custProtocolOpenResDtos;
    }

    public String getRedeemReason() {
        return redeemReason;
    }

    public void setRedeemReason(String redeemReason) {
        this.redeemReason = redeemReason;
    }

    public List<CustBalanceResDto> getCustBalanceResDtos() {
        return custBalanceResDtos;
    }

    public void setCustBalanceResDtos(List<CustBalanceResDto> custBalanceResDtos) {
        this.custBalanceResDtos = custBalanceResDtos;
    }

    public List<BankAcctSensitiveResModel> getBankAcctSensitiveResModels() {
        return bankAcctSensitiveResModels;
    }

    public void setBankAcctSensitiveResModels(List<BankAcctSensitiveResModel> bankAcctSensitiveResModels) {
        this.bankAcctSensitiveResModels = bankAcctSensitiveResModels;
    }

    public String getCanRedeem() {
        return canRedeem;
    }

    public void setCanRedeem(String canRedeem) {
        this.canRedeem = canRedeem;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getLatestPurFlag() {
        return latestPurFlag;
    }

    public void setLatestPurFlag(String latestPurFlag) {
        this.latestPurFlag = latestPurFlag;
    }

    public String getAdviceDay() {
        return adviceDay;
    }

    public void setAdviceDay(String adviceDay) {
        this.adviceDay = adviceDay;
    }

    public String getAppRatio() {
        return appRatio;
    }

    public void setAppRatio(String appRatio) {
        this.appRatio = appRatio;
    }

    public boolean isCheckIn() {
        return checkIn;
    }

    public void setCheckIn(boolean checkIn) {
        this.checkIn = checkIn;
    }

    public String getT0Flag() {
        return t0Flag;
    }

    public void setT0Flag(String t0Flag) {
        this.t0Flag = t0Flag;
    }

    public List<RedeemProductResDto> getRedeemProductResDtos() {
        return redeemProductResDtos;
    }

    public void setRedeemProductResDtos(List<RedeemProductResDto> redeemProductResDtos) {
        this.redeemProductResDtos = redeemProductResDtos;
    }
}