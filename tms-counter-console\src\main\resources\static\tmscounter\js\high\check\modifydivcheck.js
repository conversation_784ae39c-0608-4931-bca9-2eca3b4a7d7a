/**
*修改分红方式审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
    if('crm' == CommonUtil.getParam("source")){
        viewType = '1';
    }
	//初始化按钮
	CounterCheck.initBtn(viewType, ModifyDivCheck.checkOrder);
	var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
	$(".selectconsCode").html(selectConsCodesHtml);

    // 查询订单
    CounterCheck.queryCounterDealOrder(viewType, ModifyDivCheck.queryCounterDealOrderCallBack, null);
	
});

var ModifyDivCheck = {
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};
		CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};
		var checkOrder = CounterCheck.counterOrderDto || {};//订单信息
		var custInfofiList = bodyData.custInfofiList || [];//客户信息
        var orderFile = bodyData.orderFile || {};// CRM线上资料
		CounterCheck.checkOrder = checkOrder;
		
		ModifyDivCheck.checkOrder = checkOrder;
		CounterCheck.checkOrder = checkOrder;
		//查询产品信息
		QueryHighProduct.queryFundInfo(checkOrder.fundCode);
		
		ModifyDivCheck.buildDealInfo(checkOrder);//订单信息
		ViewCounterDeal.buildFundInfo( QueryHighProduct.fundInfo );//产品信息
		ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
		ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
		ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
		OnLineOrderFile.buildOrderFileHtml(orderFile);// CRM线上资料
		
	},
	
	/**
     * 订单信息
     * @param checkOrder
     */
    buildDealInfo:function(checkOrder){
    	var targetDiv = checkOrder.fundDivMode || '';
    	var divMode = '';
		
		if(targetDiv == '0'){
			divMode = '1';
		}else if(targetDiv == '1'){
			divMode = '0';
		}
		$("#fundCodeId").html(checkOrder.fundCode);// 产品代码
		$("#divModeId").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,divMode,'--'));//当前修改分红方式
		$("#targetDivId").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,targetDiv,'--'));//目标分红方式
		$("#appDt").val(checkOrder.appDt);//申请日期
    	$("#appTm").val(checkOrder.appTm);//申请时间
    },
    
};
