/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.cmd;

/**
 * @className ExpiredRedeemCmd
 * @description
 * <AUTHOR>
 * @date 2019/3/25 17:39
 */
public class ExpiredRedeemCmd {

    /**
     *  交易账号
     */
    private String txAcctNo;
    /**
     * 复购协议号
     */
    private String repurchaseProtocolNo;

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getRepurchaseProtocolNo() {
        return repurchaseProtocolNo;
    }

    public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
        this.repurchaseProtocolNo = repurchaseProtocolNo;
    }
}
