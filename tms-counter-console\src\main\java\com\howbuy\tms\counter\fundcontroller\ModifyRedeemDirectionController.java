/**
 *Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.MfDate;
import com.howbuy.common.utils.MfDateTime;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionFacade;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionRequest;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionResponse;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.applymodifyredeemdirection.ApplyModifyRedeemDirectionFacade;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.applymodifyredeemdirection.ApplyModifyRedeemDirectionRequest;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.applymodifyredeemdirection.ApplyModifyRedeemDirectionResponse;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.applymodifyredeemdirection.bean.ApplyModifyRedeemDirectionBean;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.bean.RedeemDirectionBean;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterModifyRedeemDirectionReqDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 
 * @description:(修改回款方向)
 * <AUTHOR>
 * @date 2023年05月23日 下午10:20:24
 * @since JDK 1.8
 */
@Controller
public class ModifyRedeemDirectionController extends AbstractController {
    private static Logger logger = LogManager.getLogger(ModifyRedeemDirectionController.class);
    @Autowired
    private ModifyRedeemDirectionFacade modifyRedeemDirectionFacade;
    @Autowired
    private ApplyModifyRedeemDirectionFacade applyModifyRedeemDirectionFacade;

    /**
     * 
     * ModifyRedeemDirection:(修改回款方向)
     * @param request
     * @param response
     * <AUTHOR>
     * @date 2023年05月23日 下午10:20:24
     * @since JDK 1.8
     */
    @RequestMapping("/tmscounter/fund/modifyredeemdirection.htm")
    public ModelAndView modifyRedeemDirectionConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));

        String dealNo = request.getParameter("dealNo");
        String redeemDirection = request.getParameter("redeemDirection");
        logger.debug("ModifyRedeemDirectionController|mdifyRedeemDirectionConfirm",
                dealNo, redeemDirection);

        ModifyRedeemDirectionRequest redeemDirectionRequest = new ModifyRedeemDirectionRequest();
        RedeemDirectionBean bean = new RedeemDirectionBean();
        bean.setDealNo(dealNo);
        bean.setRedeemDirection(redeemDirection);
        redeemDirectionRequest.setRedeemDirectionList(Lists.newArrayList(bean));
        ModifyRedeemDirectionResponse resp = modifyRedeemDirectionFacade.execute(redeemDirectionRequest);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if(resp != null && !TradeConstant.TMS_TRADE_SUCC_CODE.equals(resp.getReturnCode())){
            rst.setCode(resp.getReturnCode());
            rst.setDesc(resp.getDescription());
        }
        WebUtil.write(response, rst);
        return null;
    }




    /**
     *
     * ModifyRedeemDirection:(申请修改回款方向)
     * @param request
     * @param response
     * <AUTHOR>
     * @date 2023年05月23日 下午10:20:24
     * @since JDK 1.8
     */
    @RequestMapping("/tmscounter/fund/applymodifyredeemdirection.htm")
    public ModelAndView applyModifyRedeemDirectionConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));

        String dealNo = request.getParameter("dealNo");
        String redeemDirection = request.getParameter("redeemDirection");
        String custInfoForm = request.getParameter("custInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        logger.debug("ModifyRedeemDirectionController|mdifyRedeemDirectionConfirm",
                dealNo, redeemDirection);

        CounterModifyRedeemDirectionReqDto reqDto = new CounterModifyRedeemDirectionReqDto();
        reqDto.setDealAppNo(dealAppNo);
        // 转入客户信息
        reqDto.setCustName(custInfoDto.getCustName());
        reqDto.setTxAcctNo(custInfoDto.getCustNo());
        reqDto.setDisCode(custInfoDto.getDisCode());
        reqDto.setIdNo(custInfoDto.getIdNo());
        reqDto.setIdType(custInfoDto.getIdType());
        reqDto.setInvstType(custInfoDto.getInvstType());

        reqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
        reqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());

        // 柜台操作经办信息
        CommonUtil.setCommonOperInfo(operatorInfoCmd, reqDto);
        reqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        ApplyModifyRedeemDirectionBean bean = new ApplyModifyRedeemDirectionBean();
        BeanUtils.copyProperties(reqDto, bean);
        MfDateTime currDate = new MfDateTime();
        bean.setAppDt(currDate.toString(MfDate.strPatternYYYYMMDD));
        bean.setAppTm(currDate.toString(MfDate.strPatternHHMMSS));

        ApplyModifyRedeemDirectionRequest redeemDirectionRequest = new ApplyModifyRedeemDirectionRequest();
        BeanUtils.copyProperties(reqDto, redeemDirectionRequest);

        redeemDirectionRequest.setDealNo(dealNo);
        redeemDirectionRequest.setRedeemDirection(redeemDirection);
        redeemDirectionRequest.setInvstType(custInfoDto.getInvstType());
        redeemDirectionRequest.setApplyModifyRedeemDirectionBean(bean);
        redeemDirectionRequest.setDealAppNo(bean.getDealAppNo());
        ApplyModifyRedeemDirectionResponse resp = (ApplyModifyRedeemDirectionResponse) TmsFacadeUtil.executeThrowException(applyModifyRedeemDirectionFacade, redeemDirectionRequest, disInfoDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if(resp == null || !TradeConstant.TMS_TRADE_SUCC_CODE.equals(resp.getReturnCode())){
            rst.setCode(resp.getReturnCode());
            rst.setDesc(resp.getDescription());
        }
        WebUtil.write(response, rst);
        return null;
    }
    

}
