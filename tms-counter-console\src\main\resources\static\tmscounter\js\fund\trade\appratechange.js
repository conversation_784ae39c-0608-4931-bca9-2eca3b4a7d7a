

$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();
    AppRateChange.initSelect();
    AppRateChange.init();
    AppRateChange.fundInfo ={};	
    AppRateChange.queryAppRateChangeList = [];
    
    AppRateChange.workday = {};	
    AppRateChange.nextWorkday = {};	
    
});

var checkSubmitCount = 0;
var AppRateChange = {
		
	initSelect:function(){
		/**
		 * 标准业务名称
		 */
		var selectCheckFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.BUSI_CODES_CHANGE_MAP, '0');
		$("#selectCheckFlag").empty();
		$("#selectCheckFlag").html(selectCheckFlagHtml);
		
		/**
		 * 支付方式
		 */
		var selectTxCodeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PAYMENT_TYPE_CHANGE_MAP);
		$("#selectPayFlag").empty();
		$("#selectPayFlag").html(selectTxCodeHtml);
	},
	
	init:function(){
	
		/**
		 * 查询条件重置
		 */
		$("#reset").on('click',function(){
			CommonUtil.clearForm("searchForm");
		});
		
		/**
		 * 查询信息
		 */
		$("#queryAppRateChangeInfoBtn").on('click',function(){
			AppRateChange.queryAppRateChangeInfo();

		});
		
		/**
		 * 修改折扣
		 */
		$("#modifyRateBtn").on('click',function(){
			AppRateChange.modifyAppRateChangeInfo();
		});
		
	},
	
	
	modifyAppRateChangeInfo:function(){
		
		var afterDiscountRate = $("#modifyRateId").val();
		if(CommonUtil.isEmpty(afterDiscountRate)){
			CommonUtil.layer_tip("请先输入修改后的折扣费率");
			return false;
		}
		
		var afterDiscountRateT = parseFloat(afterDiscountRate);
		var afterDiscountRateMin = parseFloat("0");
		var afterDiscountRateMax = parseFloat("1");
		if(afterDiscountRateT < afterDiscountRateMin || afterDiscountRateT > afterDiscountRateMax){
			CommonUtil.layer_tip("请先输入正确的折扣费率");
			return false;
		}
		
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请先输入基金代码");
			return false;
		}
		
		// 查询基金基本信息
		AppRateChange.queryFundInfo(fundCode, false);
		
		var selectCheckFlag = $("#selectCheckFlag").val();
		if(CommonUtil.isEmpty(selectCheckFlag)){
			CommonUtil.layer_tip("请选择标准业务名称");
			return false;
		}

		var selectPayFlag = $("#selectPayFlag").val();
		if(CommonUtil.isEmpty(selectPayFlag)){
			CommonUtil.layer_tip("请先选择支付方式");
			return false;
		}

		var beginDtm = $("#beginDtm").val();
		var endDtm = $("#endDtm").val();
		if(CommonUtil.isEmpty(beginDtm) || CommonUtil.isEmpty(endDtm)){
			CommonUtil.layer_tip("申请TA日期不能为空");
			return false;
		}
		
		beginDtm = beginDtm.replace('-', '').replace('-', '');
		endDtm = endDtm.replace('-', '').replace('-', '');
		
		if(!(beginDtm == AppRateChange.workday || beginDtm == AppRateChange.nextWorkday)
				|| !(endDtm == AppRateChange.workday || endDtm == AppRateChange.nextWorkday)){
			CommonUtil.layer_tip("申请TA日期区间错误");
			return false;
		}
		
		//防止重复提交
//		if(checkSubmitCount == 0){
//			checkSubmitCount = checkSubmitCount + 1;
//		} else {
//			CommonUtil.layer_tip("请务重复提交");
//			return false;
//		}
		
		// 修改费率接口数据
		var  uri= TmsCounterConfig.MODIFY_APP_RATE_CHANGE_URL || {};
		var reqparamters = {};
		reqparamters.fundCode = fundCode;
		reqparamters.selectCheckFlag = selectCheckFlag;
		reqparamters.selectPayFlag = selectPayFlag;
		reqparamters.beginDtm = beginDtm;
		reqparamters.endDtm = endDtm;
		reqparamters.afterDiscountRate = afterDiscountRate;

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, 'post', null);
        CommonUtil.ajaxAndCallBack(paramters, AppRateChange.modifyAppRateChangeInfoCallBack);
	},
	
	modifyAppRateChangeInfoCallBack:function(data){
		var bodyData = data;
		var respCode = bodyData.code || '';
		var respDesc = bodyData.desc || '';
		if(CommonUtil.isSucc(respCode)){
			var modifyFlag = bodyData.body.modifyFlag || '0';
			if(modifyFlag == '1'){
				CommonUtil.layer_tip("人工干预折扣率修改申请成功");
			} else {
				CommonUtil.layer_tip("人工干预折扣率修改申请失败");
			}
		} else {
			CommonUtil.layer_tip("人工干预折扣率修改申请失败(" + respDesc + ")");
		}
		
	},
	
	queryAppRateChangeInfo:function(){
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请先输入基金代码");
			return false;
		}
		
		// 查询基金基本信息
		AppRateChange.queryFundInfo(fundCode, false);
		
		var selectCheckFlag = $("#selectCheckFlag").val();
		if(CommonUtil.isEmpty(selectCheckFlag)){
			CommonUtil.layer_tip("请选择标准业务名称");
			return false;
		}

		var selectPayFlag = $("#selectPayFlag").val();
		if(CommonUtil.isEmpty(selectPayFlag)){
			CommonUtil.layer_tip("请先选择支付方式");
			return false;
		}

		var beginDtm = $("#beginDtm").val();
		var endDtm = $("#endDtm").val();
		if(CommonUtil.isEmpty(beginDtm) || CommonUtil.isEmpty(endDtm)){
			CommonUtil.layer_tip("申请TA日期不能为空");
			return false;
		}
		
		beginDtm = beginDtm.replace('-', '').replace('-', '');
		endDtm = endDtm.replace('-', '').replace('-', '');
		$("#rateModifyId").empty();
		var trHtml = beginDtm + '-' + endDtm;
		$("#rateModifyId").append(trHtml);
		
		// 查询修改费率接口数据
		var  uri= TmsCounterConfig.QUERY_APP_RATE_CHANGE_URL || {};
		var reqparamters = {};
		reqparamters.fundCode = fundCode;
		reqparamters.selectCheckFlag = selectCheckFlag;
		reqparamters.selectPayFlag = selectPayFlag;
		reqparamters.beginDtm = beginDtm;
		reqparamters.endDtm = endDtm;
		reqparamters.page = 1;
		reqparamters.pageSize = 20;

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxPaging(uri, paramters, AppRateChange.queryAppRateChangeInfoCallBack, "pageView");
	},
	
	queryAppRateChangeInfoCallBack:function(data){
		var bodyData = data;
		AppRateChange.queryAppRateChangeList = bodyData.queryAppRateChangeList || [];
		AppRateChange.workday = bodyData.workday;
	    AppRateChange.nextWorkday = bodyData.nextWorkday;

		$("#rsList").empty();
		var trHtml = '';
		if(AppRateChange.queryAppRateChangeList.length <= 0){
			trHtml = '<tr><td colspan="13">无查询记录</td></tr>';
			$("#rsList").append(trHtml);
		}else{
			$(AppRateChange.queryAppRateChangeList).each(function(index, element){
                var trList = [];
                trList.push(element.submitDealNo);
                trList.push(element.fundCode);
                trList.push(element.fundName);
                trList.push(element.discountRate);//当前折扣率
                trList.push(CommonUtil.getMapValue(CONSTANTS.BUSI_CODES_MAP, element.busiCode));//标准业务名称
                if(element.appAmt > 0){
                	trList.push(CommonUtil.formatData(element.appAmt));
				}else {
					trList.push('--');
				}
                trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_ALL_TYPE, element.paymentType));//支付方式
                trList.push(element.appDate + ' ' +  element.appTime);//申请时间
                trList.push(element.taTradeDt);//申请TA日
                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#rsList").append(trAppendHtml);
			});
		}
		
	},
	
	/**
	 * 查询基金信息
	 * 
	 * fundCode 基金代码毕传
	 */
	queryFundInfo:function(fundCode){
		var uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":fundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, AppRateChange.queryFundInfoCallBack);
	},
	
	/**
	 * 处理基金信息
	 */
	queryFundInfoCallBack:function(data){
		
		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		AppRateChange.fundInfo = fundInfo;
				
		var isCommonFund = AppRateChange.checkFundInfo(fundInfo);
		
		if(!isCommonFund){
			return false;
		}
		
		$("#fundInfoId").empty();
		var trHtml = '<tr class="text-c"><td>' + AppRateChange.fundInfo.fundCode + '</td><td>' + AppRateChange.fundInfo.fundAttr + '</td><td>' + CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, AppRateChange.fundInfo.fundType) + '</td></tr>';
		$("#fundInfoId").append(trHtml);

	},
	
	checkFundInfo:function(fundInfo){
		if(CommonUtil.isEmpty(fundInfo.fundCode)){
			CommonUtil.layer_tip("没有查询到此产品");
			return false;
		}
		return true;
	},

};