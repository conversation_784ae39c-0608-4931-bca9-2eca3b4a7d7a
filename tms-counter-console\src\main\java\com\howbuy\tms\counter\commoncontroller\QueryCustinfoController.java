/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.acccenter.facade.query.querybatchcustdisandtxacctinfo.QueryBatchCustDisAndTxAcctInfoFacade;
import com.howbuy.acccenter.facade.query.querybatchcustdisandtxacctinfo.QueryBatchCustDisAndTxAcctInfoRequest;
import com.howbuy.acccenter.facade.query.querybatchcustdisandtxacctinfo.QueryBatchCustDisAndTxAcctInfoResponse;
import com.howbuy.acccenter.facade.query.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterFacade;
import com.howbuy.interlayer.product.model.HighProductControlModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.batch.facade.query.querycustbaseinfo.QueryCustBaseInfoResponse;
import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.database.InvstTypeEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.acccenter.querybindcustbankcard.QueryBindCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.QueryCustInfoAndTxAcctForCounterResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustinfoandtxacctforcounter.bean.DisAcTxAcctBean;
import com.howbuy.tms.counter.aspect.BusinessAspect;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.HttpUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.QueryCustBaseInfoReqDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:(客户信息控制器)
 * <AUTHOR>
 * @date 2017年9月15日 上午9:37:09
 * @since JDK 1.6
 */
@Controller
public class QueryCustinfoController extends AbstractController {
    private final static Logger LOGGER = LogManager.getLogger(QueryCustinfoController.class);
    @Autowired
    @Qualifier("tmsCounterService")
    private TmsCounterService tmsCounterService;
    @Autowired
    @Qualifier("tmsCounterOutService")
    private TmsCounterOutService tmsCounterOutService;
    @Autowired
    @Qualifier("queryAllCustInfoOuterService")
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    
    @Autowired
    @Qualifier("queryCustInfoOuterService")
    private QueryCustInfoOuterService queryCustInfoOuterService;

    @Autowired
    private QueryAccKycInfoOuterService queryAccKycInfoOuterService;

    @Autowired
    @Qualifier("tmscounter.highProductService")
    private HighProductService highProductService;

    @Autowired
    private QueryBatchCustDisAndTxAcctInfoFacade queryBatchCustDisAndTxAcctInfoFacade;
    /**
     * 普通公募版本
     */
    private static final String VERSION_TYPE_GONGMU = "0";
    
    /**
     * 
     * queryCustInfo:(查询客户信息)
     * 
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception
     * @date 2017年3月28日 下午5:50:13
     */
    @RequestMapping("tmscounter/querycustinfo.htm")
    public void queryCustInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String idNo = request.getParameter("idNo");
        String disCode = request.getParameter("disCode");
        String versionType = request.getParameter("versionType");
        String hboneNo = request.getParameter("hboneNo");

        LOGGER.info("custNo:{},idNo:{},disCode:{}", custNo, idNo, disCode);
        String operIp = HttpUtil.getIpAddr(request);

        String txAcctNo = custNo;
        if(StringUtils.isEmpty(txAcctNo) && StringUtils.isNotEmpty(idNo)){
            txAcctNo = getCustNo(idNo, operIp);
        }

        if(StringUtils.isEmpty(txAcctNo) && StringUtils.isNotEmpty(hboneNo)){
            txAcctNo = getTxAcctNoWithHbOneNo(hboneNo);
        }

        List<CustInfoDto> custInfoResultList = null;
        if(DefaultParamsConstant.DEFULT_DIS_CODE.equals(disCode)) {
            custInfoResultList = getCustInfoBySearchALlDisCode(txAcctNo, versionType);
        }else {
            custInfoResultList = getCustInfo(txAcctNo, disCode, disCode, versionType);
        }

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        
        bodyResult.put("custInfoList", custInfoResultList);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    /**
     * @description:(查询客户信息-多分销)
     * @param request
     * @param response
     * @return void
     * @author: haiguang.chen
     * @date: 2022/2/18 14:14
     * @since JDK 1.8
     */
    @RequestMapping("tmscounter/querycustinfoByDisCodeList.htm")
    public void querycustinfoByDisCodeList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String idNo = request.getParameter("idNo");
        String disCodeTmp = request.getParameter("disCodeList");
        List<String> disCodeList = Arrays.asList(disCodeTmp.split(","));
        String versionType = request.getParameter("versionType");
        String hboneNo = request.getParameter("hboneNo");

        LOGGER.info("custNo:{},idNo:{},disCodeList:{}", custNo, idNo, disCodeList);
        String operIp = HttpUtil.getIpAddr(request);

        String txAcctNo = custNo;
        if(StringUtils.isEmpty(txAcctNo) && StringUtils.isNotEmpty(idNo)){
            txAcctNo = getCustNo(idNo, operIp);
        }

        if(StringUtils.isEmpty(txAcctNo) && StringUtils.isNotEmpty(hboneNo)){
            txAcctNo = getTxAcctNoWithHbOneNo(hboneNo);
        }

        List<CustInfoDto> custInfoResultList = getCustInfo(txAcctNo, disCodeList, versionType);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>();

        bodyResult.put("custInfoList", custInfoResultList);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    private String getTxAcctNoWithHbOneNo(String hboneNo){
        return tmsCounterOutService.queryTxAccountNoByHboneNo(hboneNo);
    }

    private String getHboneNoByTxAcctNo(String txAcctNo){
        return tmsCounterOutService.queryHboneNoByTxAccountNo(txAcctNo);
    }

    /***
     * 
     * queryCustInfo2:(子页面查询客户信息)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月3日 下午4:17:45
     */
    @RequestMapping("tmscounter/querycustinfosubpage.htm")
    public ModelAndView queryCustInfoSubPage(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String idNo = request.getParameter("idNo");
        String custName = request.getParameter("custName");
        if (!StringUtils.isEmpty(custName)) {
            custName = URLDecoder.decode(custName, StandardCharsets.UTF_8.name());
        }
        String operIp = HttpUtil.getIpAddr(request);
        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        queryCustBaseInfoReqDto.setOperIp(operIp);
        queryCustBaseInfoReqDto.setIdNo(idNo);
        queryCustBaseInfoReqDto.setTxAcctNo(custNo);
        queryCustBaseInfoReqDto.setCustName(custName);
        QueryCustBaseInfoResponse queryRsp = tmsCounterService.queryCustBaseInfoSub(queryCustBaseInfoReqDto, null);

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respData", queryRsp);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
        return null;
    }

    @RequestMapping("tmscounter/querycustbankinfo.htm")
    public void queryCustBankInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        List<QueryBindCustBankCardResult> custBanks = tmsCounterOutService.queryCustBanks(custNo, disCode, null);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("custBanks", custBanks);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    @RequestMapping("tmscounter/getdefaultdiscode.htm")
    public void getDefaultDisCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String hbOneNo = request.getParameter("hbOneNo");
        String fundCode = request.getParameter("fundCode");
        String disCode = null;
        // 获取客户信息
        QueryAccHboneInfoResult queryAccHboneInfoResult = tmsCounterOutService.queryAccHboneInfo(hbOneNo);
        if(queryAccHboneInfoResult == null){
            LOGGER.error("hboneNo{},客户信息不存在",hbOneNo);
            throw new TmsCounterException(TmsCounterResultEnum.CUST_NOT_EXIST);
        }
        // 获取产品对应的分销代码
        HighProductControlModel highProductControlModel = highProductService.getHighProductControlInfo(fundCode);
        if(highProductControlModel == null){
            LOGGER.error("fundCode{},产品信息不存在",fundCode);
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }

        if(InvstTypeEnum.INDI.getCode().equals(queryAccHboneInfoResult.getUserType())){
            // 个人客户
            if(DisCodeEnum.HZ.getCode().equals(highProductControlModel.getDisCode())){
                disCode = DisCodeEnum.HZ.getCode();
            }else {
                disCode = DisCodeEnum.HM.getCode();
            }
        }else if(InvstTypeEnum.PRODUCT.getCode().equals(queryAccHboneInfoResult.getUserType()) ||
                InvstTypeEnum.INST.getCode().equals(queryAccHboneInfoResult.getUserType())){
            // 机构或者产品客户
            if(DisCodeEnum.HZ.getCode().equals(highProductControlModel.getDisCode())){
                disCode = DisCodeEnum.HZ.getCode();
            }else {
                disCode = DisCodeEnum.OTC_MIDDLE.getCode();
            }
        }

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("defaultDisCode", disCode);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);

    }



    private List<CustInfoDto> getCustInfoBySearchALlDisCode(String txAcctNo, String versionType) throws Exception {

        QueryBatchCustDisAndTxAcctInfoRequest request = new QueryBatchCustDisAndTxAcctInfoRequest();
        request.setTxAcctNo(txAcctNo);
        request.setDisCodeList(null);
        QueryBatchCustDisAndTxAcctInfoResponse res = queryBatchCustDisAndTxAcctInfoFacade.execute(request);

        List<String> disCodeList = Lists.newArrayList();
        if (res != null && CollectionUtils.isNotEmpty(res.getDisCustAndTxAcctList())) {
            disCodeList = res.getDisCustAndTxAcctList().stream().map(x -> x.getDisCode()).collect(Collectors.toList());
        }else {
            disCodeList.add(DisCodeEnum.HM.getCode());
        }

        List<CustInfoDto> custInfoList = getCustInfo(txAcctNo, disCodeList, versionType);
        if (CollectionUtils.isNotEmpty(custInfoList)) {
            // 若多分销 优先展示 好买分销
            custInfoList.sort(new Comparator<CustInfoDto>() {
                @Override
                public int compare(CustInfoDto o1, CustInfoDto o2) {
                    // 先比较是否等于DisCodeEnum.HM.getCode()
                    if (DisCodeEnum.HM.getCode().equals(o1.getDisCode())) {
                        return -1; // 如果o1是HM，放在前面，返回负数
                    } else if (DisCodeEnum.HM.getCode().equals(o2.getDisCode())) {
                        return 1; // 如果o2是HM，放在后面，返回正数
                    } else {
                        // 其他情况按照字符串自然顺序排序
                        return o1.getDisCode().compareTo(o2.getDisCode());
                    }
                }
            });
        }

        return custInfoList;
    }

    /**
     * 构造客户信息
     * 
     * @param txAcctNo
     * @param disCode
     * @return
     * @throws Exception
     */
    private List<CustInfoDto> getCustInfo(String txAcctNo, String disCode, String selectDisCode,String versionType) throws Exception {
        List<CustInfoDto> custInfoDtoList = new ArrayList<CustInfoDto>();
        if (!StringUtils.isEmpty(txAcctNo)) {
            if (StringUtils.isEmpty(disCode)) {
                disCode = "HB000A001";
            }

            String hbOneNo = getHboneNoByTxAcctNo(txAcctNo);
            // 查询客户信息
            QueryCustInfoAndTxAcctForCounterResult queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);


            LOGGER.info("queryCustInfoAndTxAcctForCounterResult:{}", JSON.toJSONString(queryCustInfoAndTxAcctForCounterResult));

            // 获取客户默认分销
            disCode = getDefaultDisCode(selectDisCode, queryCustInfoAndTxAcctForCounterResult.getInvstType());
            LOGGER.info("getDefaultDisCode:{}",disCode);
            // 重新查询客户信息
            if(StringUtils.isEmpty(selectDisCode)){
                queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);
            }

            custInfoDtoList = getCustInfoDtoList(txAcctNo, versionType, queryCustInfoAndTxAcctForCounterResult, disCode, hbOneNo);

        }
        List<CustInfoDto> custInfoResultList = null;
        if (!StringUtils.isEmpty(selectDisCode)) {
            if (!CollectionUtils.isEmpty(custInfoDtoList)) {
                for (CustInfoDto custInfoDto : custInfoDtoList) {
                    if (selectDisCode.equals(custInfoDto.getDisCode())) {
                        custInfoResultList = new ArrayList<CustInfoDto>();
                        custInfoResultList.add(custInfoDto);
                        break;
                    }
                }
            }
        } else {
            custInfoResultList = custInfoDtoList;
        }
        return custInfoResultList;
    }


    private List<CustInfoDto> getCustInfo(String txAcctNo, List<String> disCodeList,String versionType) throws Exception {
        List<CustInfoDto> custInfoDtoList = null;
        QueryCustInfoAndTxAcctForCounterResult queryCustInfoAndTxAcctForCounterResult;
        String hbOneNo = getHboneNoByTxAcctNo(txAcctNo);

        if (!StringUtils.isEmpty(txAcctNo)) {
            if (CollectionUtils.isEmpty(disCodeList)) {
                String disCode;
                // 查询客户信息-获取客户类型
                queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, DisCodeEnum.HM.getCode());
                LOGGER.info("queryCustInfoAndTxAcctForCounterResult:{}", JSON.toJSONString(queryCustInfoAndTxAcctForCounterResult));
                // 获取客户默认分销
                if (InvstTypeEnum.INST.getCode().equals(queryCustInfoAndTxAcctForCounterResult.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(queryCustInfoAndTxAcctForCounterResult.getInvstType())) {
                    disCode = DisCodeEnum.OTC_MIDDLE.getCode();
                } else {
                    disCode = DisCodeEnum.HM.getCode();
                }
                LOGGER.info("getDefaultDisCode:{}", disCode);
                // 重新查询客户信息
                if (StringUtils.isEmpty(disCodeList.get(0))) {
                    queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);
                }
                custInfoDtoList = getCustInfoDtoList(txAcctNo, versionType, queryCustInfoAndTxAcctForCounterResult, disCode, hbOneNo);
                return custInfoDtoList;
            }
            else {
                List<CustInfoDto> custInfoDtoLists = new ArrayList<>();
                for(String disCode : disCodeList){
                    try {
                        queryCustInfoAndTxAcctForCounterResult = tmsCounterOutService.queryAllCustInfo(txAcctNo, disCode);
                        custInfoDtoList = getCustInfoDtoList(txAcctNo, versionType, queryCustInfoAndTxAcctForCounterResult, disCode, hbOneNo);
                        custInfoDtoLists.addAll(custInfoDtoList);
                    }catch (Exception e){
                        LOGGER.error("disCode:{} 查询不到客户信息", disCode);
                    }
                }
                return custInfoDtoLists;
            }
        }
        return null;
    }

    private List<CustInfoDto> getCustInfoDtoList(String txAcctNo, String versionType, QueryCustInfoAndTxAcctForCounterResult queryCustInfoAndTxAcctForCounterResult, String disCode, String hbOneNo) {
        List<CustInfoDto> custInfoDtoList = new ArrayList<>();
        QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
        queryAllCustInfoContext.setDisCode(disCode);
        queryAllCustInfoContext.setTxAcctNo(txAcctNo);
        QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);

        CustInfoBean custInfoBean = null;
        if (queryAllCustInfoResult != null) {
            custInfoBean = queryAllCustInfoResult.getCustInfo();
        }

        // 查询客户风险等级
        QueryAccKycInfoResult queryAccKycInfoResult = null;
        if (custInfoBean != null) {
            queryAccKycInfoResult = queryAccKycInfoOuterService.queryAccKycInfoByTxAcctNo(txAcctNo,disCode);
//            if (VERSION_TYPE_GONGMU.equals(versionType) || InvstTypeEnum.INST.getCode().equals(custInfoBean.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(custInfoBean.getInvstType())) {
//                queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurvey(txAcctNo, VERSION_TYPE_GONGMU, disCode);
//            } else {
//                queryCustRiskSurveyResult = queryCustRiskSurveyOuterService.queryCustRiskSurvey(txAcctNo, VERSION_TYPE_HIGH, disCode);
//            }
        }
        //查询客户基本信息
        BusinessAspect.setTradeCommomParams(disCode, null);
        QueryCustInfoResult queryCustInfoResult = queryCustInfoOuterService.queryCustInfoPlaintext(txAcctNo);
        String collectProtocolMethod = null;
        // 客户资管投资承诺书签署状态 1-签署；0-未签署
        String fundFlag = null;
        //  客户私募投资承诺书签署状态 1-签署；0-未签署
        String signFlag = null;
        if (queryCustInfoResult != null) {
            collectProtocolMethod = queryCustInfoResult.getCollectProtocolMethod();
            fundFlag = queryCustInfoResult.getFundFlag();
            signFlag = queryCustInfoResult.getSignFlag();
        }
        if (queryCustInfoAndTxAcctForCounterResult != null) {
            List<DisAcTxAcctBean> disAcTxAcctBeanList = queryCustInfoAndTxAcctForCounterResult.getDisAcTxAcctBeanList();
            if (!CollectionUtils.isEmpty(disAcTxAcctBeanList)) {
                CustInfoDto custInfoDto = null;
                for (DisAcTxAcctBean disAcTxAcctBean : disAcTxAcctBeanList) {
                    custInfoDto = new CustInfoDto();
                    BeanUtils.copyProperties(queryCustInfoAndTxAcctForCounterResult, custInfoDto);
                    custInfoDto.setCollectProtocolMethod(collectProtocolMethod);
                    custInfoDto.setSignFlag(signFlag);
                    custInfoDto.setFundFlag(fundFlag);
                    custInfoDto.setHboneNo(hbOneNo);
                    if (custInfoBean != null) {
                        // 投资者类型
                        custInfoDto.setInvestorType(custInfoBean.getQualificationType());
                        // 客户类型
                        custInfoDto.setInvstType(custInfoBean.getInvstType());
                    }

                    custInfoDto.setDisCode(disAcTxAcctBean.getDisCode());
                    custInfoDtoList.add(custInfoDto);

                    if (queryAccKycInfoResult != null) {
                        if (VERSION_TYPE_GONGMU.equals(versionType) || InvstTypeEnum.INST.getCode().equals(custInfoBean.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(custInfoBean.getInvstType())) {
                            custInfoDto.setCustRiskLevel(queryAccKycInfoResult.getRiskToleranceLevel());
                            custInfoDto.setRiskSurveyDt(queryAccKycInfoResult.getRiskToleranceDate());
                            custInfoDto.setOverdue(queryAccKycInfoResult.getRiskToleranceExpire() == null || queryAccKycInfoResult.getRiskToleranceExpire());
                        }else{
                            // 高端
                            if("HIGH_END".equals(queryAccKycInfoResult.getRiskToleranceExamType()) || "INSTITUTION".equals(queryAccKycInfoResult.getRiskToleranceExamType())) {
                                custInfoDto.setCustRiskLevel(queryAccKycInfoResult.getRiskToleranceLevel());
                                custInfoDto.setRiskSurveyDt(queryAccKycInfoResult.getRiskToleranceDate());
                                custInfoDto.setOverdue(queryAccKycInfoResult.getRiskToleranceExpire() == null || queryAccKycInfoResult.getRiskToleranceExpire());
                            }else {
                                custInfoDto.setCustRiskLevel(null);
                                custInfoDto.setRiskSurveyDt(null);
                                custInfoDto.setOverdue(true);
                            }
                        }
                    } else {
                        custInfoDto.setCustRiskLevel(null);
                        custInfoDto.setRiskSurveyDt(null);
                        custInfoDto.setOverdue(true);
                    }
                }
            }

        }
        return custInfoDtoList;
    }

    /**
     * 
     * @Description 获取默认分销机构号 好买分销或者机构分销
     * 
     * @param selectDisCode
     * @param invstType
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2019/9/10 13:28
     **/
    private String getDefaultDisCode(String selectDisCode, String invstType) {
        LOGGER.info("getDefaultDisCode|selectDisCode:{}, invstType:{}", selectDisCode, invstType);
        String disCode = DisCodeEnum.HM.getCode();
        if(StringUtils.isEmpty(selectDisCode)){
            if(StringUtils.isNotEmpty(invstType)){
                if(InvstTypeEnum.INST.getCode().equals(invstType) || InvstTypeEnum.PRODUCT.getCode().equals(invstType)){
                    return DisCodeEnum.OTC_MIDDLE.getCode();
                }else{
                    return DisCodeEnum.HM.getCode();
                }
            }
        }else{
            return selectDisCode;
        }

        return disCode;
    }
    
}
