/**
 *Copyright (c) 2017, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.enums;

/**
 * @description:(TODO 请在此添加描述)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年9月20日 上午9:54:53
 * @since JDK 1.6
 */
public enum CancelTypeEnum {

    /**
     * 自行撤单
     */
    SELF_CANCEL("1", "自行撤单"),

    /**
     * 强制撤单
     */
    FORCE_CANCEL("2", "强制撤单");

    private String code;
    private String name;

    private CancelTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
