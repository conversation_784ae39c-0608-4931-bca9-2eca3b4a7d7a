package com.howbuy.tms.counter.auth;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.howbuy.interlayer.product.enums.UserStatus;
import com.howbuy.interlayer.product.model.PrivilegeModel;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.permission.PermissionService;
import com.howbuy.interlayer.product.service.permission.UserService;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.exception.BaseException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.crypto.hash.Md5Hash;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.util.ByteSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 
 * @description:用户认证、鉴权
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年5月21日 下午7:59:15
 * @since JDK 1.6
 */
public class AuthorizationRealm extends AuthorizingRealm {
	
    private final static Logger logger = LoggerFactory.getLogger(AuthorizationRealm.class);

    private static final String OR_OPERATOR = " or ";
    private static final String AND_OPERATOR = " and ";
    private static final String NOT_OPERATOR = "not ";

    @DubboReference(protocol = "dubbo",registry = "product-center-remote",check = false)
    private UserService userService;
    @DubboReference(protocol = "dubbo",registry = "product-center-remote",check = false)
    private PermissionService permissionService;

    /**
     * 
     * (鉴权服务)
     * 
     * @see org.apache.shiro.realm.AuthorizingRealm#doGetAuthorizationInfo(org.apache.shiro.subject.PrincipalCollection)
     */
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(
			PrincipalCollection principals) {
		SimpleAuthorizationInfo authorization = new SimpleAuthorizationInfo(); 
		Object principal = principals.getPrimaryPrincipal();
        if (principal instanceof String) {
            String userId = (String) principal;
			try {
				Set<String> roles = permissionService.getAllRolesOnUser(userId);
                if (CollectionUtils.isEmpty(roles)) {
                    return authorization;
                }
				authorization.setRoles(roles);

                List<PrivilegeModel> privilegeModelList = null;
                if (roles.contains(Constants.ROLE_ADMIN)) {
                    privilegeModelList = permissionService.getAllPrivilegeList();
				}else{
                    privilegeModelList = permissionService.getAllPermissionOnUser(userId);
				}


                if (CollectionUtils.isEmpty(privilegeModelList)) {
                    return authorization;
                }

                privilegeModelList = filterPrivilegeBlack(userId, privilegeModelList);

                Set<String> permissionCodes = privilegeModelList.stream().map(PrivilegeModel::getPrivilegeCode).collect(Collectors.toSet());
                authorization.setStringPermissions(permissionCodes);

                logger.info("doGetAuthorizationInfo|Permissions:{}", JSON.toJSONString(permissionCodes));

			} catch (Exception e) {
				logger.error("授权出错：userId={}", principal, e);
			}
		}
		return authorization;
	}


    private List<PrivilegeModel> filterPrivilegeBlack(String userId,  List<PrivilegeModel> privilegeModelList){
        try {
            // 看是否在黑名单用户和黑名单菜单中。
            //     若该用户即是黑名单用户且该菜单也在黑名单菜单中，则该用户不展示黑名单菜单，即菜单列表中不含黑名单菜单
            //     反之，用户不再黑名单中或者该菜单不再黑名单菜单中，则该用户的菜单正常展示。

            Boolean isHBJG = userService.verifyUserIsHBJG(userId);
            List<String> privilegeBlackList = permissionService.selectPrivilegeBlackList();

            if (!Boolean.FALSE.equals(isHBJG) && !CollectionUtils.isEmpty(privilegeBlackList)) {
                privilegeModelList = privilegeModelList.stream().filter(x -> !privilegeBlackList.contains(x.getPrivilegeId())).collect(Collectors.toList());
                logger.info("doGetAuthorizationInfo|user is hbjg Filter privilege:{}", JSON.toJSONString(privilegeBlackList));
            }
        }catch (Exception e){
            logger.error("filterPrivilegeBlack error：userId={}", userId, e);
        }

        return privilegeModelList;
    }

    /**
     * 
     * (认证服务)
     * 
     * @see org.apache.shiro.realm.AuthenticatingRealm#doGetAuthenticationInfo(org.apache.shiro.authc.AuthenticationToken)
     */
	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(
			AuthenticationToken authToken) throws AuthenticationException {
		try {
			if(authToken == null) {
                return null;
            }
            EasyTypeToken token = (EasyTypeToken) authToken;
			String username = (String) token.getPrincipal();
            UserAccountModel user = userService.getUserByUserName(username);
            if (Objects.equal(user, null)) {
                throw new BaseException("9999", "用户：" + username + "未找到");

            }

            // 判断用户状
            if (!UserStatus.NORMAL.getKey().equals(user.getStatus())) {
                throw new BaseException("9999", "用户：" + username + "状态异常");
            }

			ByteSource bsSalt = ByteSource.Util.bytes(user.getSalt());

			SimpleAuthenticationInfo authenticationInfo =
                    new SimpleAuthenticationInfo(user.getUserId(), Md5Hash.fromHexString(user.getPassword()), bsSalt, getName());
			return authenticationInfo;
        } catch (AuthenticationException e) {
			logger.error("认证出错：", e);
			throw new AuthenticationException("用户名或密码错误！");
		}
	}

    /**
     * 支持 shiro:hasPermission 标签 or and not 关键词  不支持and or混用
     * @param principals
     * @param permission
     * @return
     */
    @Override
    public boolean isPermitted(PrincipalCollection principals, String permission) {
        if(permission.contains(OR_OPERATOR)) {
            String[] permissions = permission.split(OR_OPERATOR);
            for(String orPermission : permissions) {
                if(isPermittedWithNotOperator(principals, orPermission)) {
                    return true;
                }
            }
            return false;
        } else if(permission.contains(AND_OPERATOR)) {
            String[] permissions = permission.split(AND_OPERATOR);
            for(String orPermission : permissions) {
                if(!isPermittedWithNotOperator(principals, orPermission)) {
                    return false;
                }
            }
            return true;
        } else {
            return isPermittedWithNotOperator(principals, permission);
        }
    }

    /**
     * 
     * isPermittedWithNotOperator:支持 <shiro:hasPermission name="005003001 or
     * 005002001> </shiro:hasPermission>格式
     * 
     * @param principals
     * @param permission
     * @return
     * <AUTHOR>
     * @date 2018年5月16日 下午1:16:54
     */
    private boolean isPermittedWithNotOperator(PrincipalCollection principals, String permission) {
        if(permission.startsWith(NOT_OPERATOR)) {
            return !super.isPermitted(principals, permission.substring(NOT_OPERATOR.length()));
        } else {
            return super.isPermitted(principals, permission);
        }
    }

    /**
     * 
     * removeUserAuthorizationInfoCache:(每次登录认证-鉴权一次，之后不再鉴权，如需要实时鉴权需要移除缓存中的鉴权)
     * 
     * @param username
     * <AUTHOR>
     * @date 2018年5月16日 下午1:15:17
     */
    public void removeUserAuthorizationInfoCache(String username) {
        SimplePrincipalCollection pc = new SimplePrincipalCollection();
        pc.add(username, super.getName());
        super.clearCachedAuthorizationInfo(pc);
    }

    public UserService getUserService() {
        return userService;
    }

    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    public PermissionService getPermissionService() {
        return permissionService;
    }

    public void setPermissionService(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

}
