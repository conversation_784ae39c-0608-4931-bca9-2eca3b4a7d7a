/**
 * 查询客户信息
 * 
 * <AUTHOR>
 * @date 2018-02-05 15:10
 * 
 */

var HighCustInfo = {
	
    /**
	* 查询客户信息
	*/
	queryCustInfo:function(hboneNo, completeFun){
	    var uri= TmsCounterConfig.QUERY_CUST_INFO_URL  ||  {};
	    var custNo = $("#custNo").val();
	    var idNo = $("#idNo").val();
        var reqparamters = {};
        if(!CommonUtil.isEmpty(hboneNo)){
            reqparamters['hboneNo'] = hboneNo;
			var disCode = $("#selectDisCode").val();
			if(!CommonUtil.isEmpty(disCode)){
				reqparamters.disCode = disCode;
			}
        }else{
            if(CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo)){
                CommonUtil.layer_tip("客户号和证件号必须输入一项");
                return false;
            }
            if(!CommonUtil.isEmpty(idNo)){
                reqparamters.idNo = idNo;
            }

            if(!CommonUtil.isEmpty(custNo)){
                reqparamters.custNo = custNo;
            }

            var disCode = $("#selectDisCode").val();
            if(!CommonUtil.isEmpty(disCode)){
                reqparamters.disCode = disCode;
            }

            if(isEmpty(custNo) && isEmpty(idNo)){
                showMsg("客户号,证件号必须输入一项");
                return false;
            }
        }

	    var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
	    CommonUtil.ajaxAndCallBack(paramters, HighCustInfo.queryCustInfocallBack, completeFun);
	},

	/**
	 * 查询默认分销代码
	 */
	getDefaultDisCode:function(hbOneNo, fundCode, completeFun){
		var uri= TmsCounterConfig.QUERY_DEFAULT_DISCODE_URL  ||  {};

		var reqparamters = {};

		if(CommonUtil.isEmpty(hbOneNo) || CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("一账通号和产品代码必须输入");
			return false;
		}
		reqparamters.hbOneNo = hbOneNo;
		reqparamters.fundCode = fundCode;

		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, HighCustInfo.getDefaultDisCodecallBack, completeFun);
	},

	/**
	 * 查询客户信息-多分销
	 */
	queryCustInfoByDiscodeList: function (hboneNo, completeFun) {
		var uri = TmsCounterConfig.QUERY_CUST_INFO_URL_BY_DISCODELIST || {};
		var custNo = $("#custNo").val();
		var hboneNo = $("#hboneNo").val();
		var idNo = $("#idNo").val();
		if (isEmpty(custNo) && isEmpty(hboneNo) && isEmpty(idNo)) {
			showMsg("客户号、一账通号、证件号必须输入一项");
			return false;
		}

		var reqparamters = {};
		if (!CommonUtil.isEmpty(hboneNo)) {
			reqparamters.hboneNo = hboneNo;
		}
		if (!CommonUtil.isEmpty(idNo)) {
			reqparamters.idNo = idNo;
		}
		if (!CommonUtil.isEmpty(custNo)) {
			reqparamters.custNo = custNo;
		}
		var disCode = $("#selectDisCode").val();
		if (!CommonUtil.isEmpty(disCode)) {
			reqparamters.disCode = disCode;
		}

		if ($("#multiSelectDisCode").length > 0) {
			var disCodeList = $("#multiSelectDisCode").val().join(',')
			if (!CommonUtil.isEmpty(disCodeList)) {
				reqparamters.disCodeList = disCodeList;
			}
		}

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
		CommonUtil.ajaxAndCallBack(paramters, HighCustInfo.queryCustInfoByDiscodeListcallBack, completeFun);
	},
	
	/**
	 * 客户信息查询结果处理
	 * @param data
	 * @returns {Boolean}
	 */
	queryCustInfocallBack:function(data){
	    var bodyData = data.body || {};
	    var custInfoDtoList = bodyData.custInfoList || [];
	    HighCustInfo.custList = custInfoDtoList;
	    if(custInfoDtoList.length === 0){
	    	CommonUtil.layer_tip("没有查询到此用户");
	    	return false;
	    }
	    
	    $("#custInfoId").empty();
	    $(custInfoDtoList).each(function(index,element){
	    	var trList = [];
	    	trList.push('<span class="radio-box"><input class="selectcust" name="checkCust" type="radio" index="'+index+'"></input></span>');
	    	
	    	trList.push(CommonUtil.formatData(element.custNo, '--'));//客户号
	    	trList.push(CommonUtil.formatData(element.custName, '--'));//客户姓名
	    	trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));//客户类型
	    	if('0' === element.invstType){
        		//机构类型用户
        		trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP,element.idType, ''));//客户证件类型
        	}
	    	if('1' === element.invstType){
        		//个人类型用户
        		trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP,element.idType, ''));//客户证件类型
        	}
	    	if('2' === element.invstType){
	    		trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP,element.idType, ''));
			}
	    	
	    	trList.push(CommonUtil.formatData(element.idNo, '--'));//证件号
	    	trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP,element.custRiskLevel, '--'));//客户风险等级
	    	trList.push(CommonUtil.getMapValue(DisCode.disCodesMap,element.disCode, '--'));//分销机构
	    	trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_SIGN_FLAG_MAP,element.signFlag, '--'));//是否签署合格投资认证(私募)
            trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_SIGN_FLAG_MAP,element.fundFlag, '--'));//客户资管投资承诺书签署状态 1-签署；0-未签署
	    	trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP,element.custStat, '--'));//客户状态
	    	trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP,element.investorType, '--'));//投资和类型0-普通 1-专业
	    	trList.push(CommonUtil.getMapValue(CONSTANTS.COLLECT_PROTOCOL_METHOD_MAP,element.collectProtocolMethod, '--'));//客户默认回款协议
	    	var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
	    	$("#custInfoId").append(trHtml);
	    });
	    
	},

	/**
	 * 默认分销代码查询结果处理
	 * @param data
	 * @returns {Boolean}
	 */
	getDefaultDisCodecallBack:function(data){
		var bodyData = data.body || {};
		var defaultDisCode = bodyData.defaultDisCode || [];
		if(defaultDisCode.length == 0){
			CommonUtil.layer_tip("没有查询到默认分销");
			return false;
		}
		if($("#selectDisCode").length>0){
			$("#selectDisCode").val(defaultDisCode);
		}
	},

	/**
	 * 客户信息查询结果处理
	 * @param data
	 * @returns {Boolean}
	 */
	queryCustInfoByDiscodeListcallBack:function(data){
		var bodyData = data.body || {};
		var custInfoDtoList = bodyData.custInfoList || [];
		HighCustInfo.custList = custInfoDtoList;
		if(custInfoDtoList.length == 0){
			CommonUtil.layer_tip("没有查询到此用户");
			return false;
		}

		$("#custInfoId").empty();
		$(custInfoDtoList).each(function(index,element){
			var trList = [];
			trList.push('<span class="checkboxTd"><input class="selectcust"+index name="checkCust" type="checkbox" index="'+index+'"></input></span>');

			trList.push(CommonUtil.formatData(element.custNo, '--'));//客户号
			trList.push(CommonUtil.formatData(element.custName, '--'));//客户姓名
			trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));//客户类型
			if('0' == element.invstType){
				//机构类型用户
				trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP,element.idType, ''));//客户证件类型
			}
			if('1' == element.invstType){
				//个人类型用户
				trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP,element.idType, ''));//客户证件类型
			}
			if('2' == element.invstType){
				trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP,element.idType, ''));
			}

			trList.push(CommonUtil.formatData(element.idNo, '--'));//证件号
			trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP,element.custRiskLevel, '--'));//客户风险等级
			trList.push(CommonUtil.getMapValue(DisCode.disCodesMap,element.disCode, '--'));//分销机构
			trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_SIGN_FLAG_MAP,element.signFlag, '--'));//是否签署合格投资认证(私募)
			trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_SIGN_FLAG_MAP,element.fundFlag, '--'));//客户资管投资承诺书签署状态 1-签署；0-未签署
			trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP,element.custStat, '--'));//客户状态
			trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP,element.investorType, '--'));//投资和类型0-普通 1-专业
			trList.push(CommonUtil.getMapValue(CONSTANTS.COLLECT_PROTOCOL_METHOD_MAP,element.collectProtocolMethod, '--'));//客户默认回款协议
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#custInfoId").append(trHtml);
		});

	},
	
	/**
	 * 查询客户经办人信息
	 */
	queryCustTransInfo:function(txAcctNo, disCode){
		var uri =  TmsCounterConfig.QUERY_CUST_TRANSINFO_URL;
		var reqparamters = {"txAcctNo": txAcctNo, "disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, HighCustInfo.queryCustTransInfoCallBack);
	},
	
	queryCustTransInfoCallBack:function(data){
		var bodyData = data.body || {};
		$("#transactorName").val(bodyData.linkMan);// 经办人姓名
		$("#transactorIdType").val(bodyData.linkIdType);// 经办人证件类型
		$("#transactorIdNo").val(bodyData.linkIdNo);// 经办人证件号
		
		// 机构经办人不可修改
		CommonUtil.addReadOnlyAllInput("transactorInfoForm");
	},

	selectFirstCust:function () {
        $(".selectcust").each(function (index, element) {
			if(index == 0){
				$(element).click();
			}
        });
    },
	
};