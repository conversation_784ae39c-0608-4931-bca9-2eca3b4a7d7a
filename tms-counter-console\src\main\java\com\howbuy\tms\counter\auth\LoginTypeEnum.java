/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.auth;

/**
 * @className LoginTypeEnum
 * @description
 * <AUTHOR>
 * @date 2019/6/13 16:16
 */
public enum  LoginTypeEnum {
    /**
     * 密码登录
     */
    PASSWORD("password"),
    /**
     * 免密登录
     */
    NOPASSWD("nopassword");

    /**
     * 状态值
     */
    private String code;

    LoginTypeEnum(String password) {
    }

    public String getCode () {
        return code;
    }
}
