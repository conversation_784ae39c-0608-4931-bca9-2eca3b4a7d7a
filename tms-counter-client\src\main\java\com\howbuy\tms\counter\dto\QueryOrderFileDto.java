/**
 * Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.dto;

import java.util.List;

/**
 * @className QueryOrderFileDto
 * @description
 * <AUTHOR>
 * @date 2019/5/24 15:50
 */
public class QueryOrderFileDto {
    /**
     * 业务订单ID
     */
    private String orderid;

    /**
     * 审核状态
     */
    private String curstat;

    /**
     * 业务类型id
     */
    private String busiid;

    /**
     * 是否可以审核 0-否 1-是
     */
    private String canCheck;
    /**
     * 审核节点 0-OP初审 1-OP复审
     */
    private String checkNode;

    /**
     * 统计文件列表
     */
    private List<OrderFileInfoDto> orderinfolist;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getCurstat() {
        return curstat;
    }

    public void setCurstat(String curstat) {
        this.curstat = curstat;
    }

    public String getBusiid() {
        return busiid;
    }

    public void setBusiid(String busiid) {
        this.busiid = busiid;
    }

    public String getCanCheck() {
        return canCheck;
    }

    public void setCanCheck(String canCheck) {
        this.canCheck = canCheck;
    }

    public String getCheckNode() {
        return checkNode;
    }

    public void setCheckNode(String checkNode) {
        this.checkNode = checkNode;
    }

    public List<OrderFileInfoDto> getOrderinfolist() {
        return orderinfolist;
    }

    public void setOrderinfolist(List<OrderFileInfoDto> orderinfolist) {
        this.orderinfolist = orderinfolist;
    }
}
