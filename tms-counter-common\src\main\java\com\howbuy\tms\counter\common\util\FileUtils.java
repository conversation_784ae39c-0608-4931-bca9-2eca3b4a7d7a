/**
 * Copyright (c) 2020, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.common.util;

import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;

import java.io.*;

/**
 * <AUTHOR>
 * @description: 文件工具类
 * @date 2020/8/21 14:26
 * @since JDK 1.8
 */
public class FileUtils {

    public static void saveFile(InputStream inputStream, String targetDir, String targetFileName) throws Exception {
        OutputStream os = null;
        try {
            // 保存到临时文件
            byte[] bs = new byte[inputStream.available()];
            // 读取到的数据长度
            int len;
            // 输出的文件流保存到本地文件
            File tempFile = new File(targetDir);
            if (!tempFile.exists()) {
                //如果创建失败
                if (!tempFile.mkdirs()){
                    throw new TmsCounterException(TmsCounterResultEnum.FAILD);
                };
            }
            os = new FileOutputStream(tempFile.getPath() + File.separator + targetFileName);
            // 开始读取
            while ((len = inputStream.read(bs)) != -1) {
                os.write(bs, 0, len);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            // 完毕，关闭所有链接
            try {
                if(os != null){
                    os.close();
                }
                if(inputStream != null){
                    inputStream.close();
                }
            } catch (IOException e) {
                throw e;
            }
        }
    }



}