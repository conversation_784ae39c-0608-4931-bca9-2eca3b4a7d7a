/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.regularcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.util.CommonUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * @description:(撤单控制器)
 * <AUTHOR>
 * @date 2018年6月26日 下午4:03:27
 * @since JDK 1.6
 */
@Controller
public class CanceRegularController extends AbstractController {
    private Logger logger = LogManager.getLogger(CanceRegularController.class);

    /**
     * 
     * cancelConfirm:(定期柜台撤单)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月16日 下午7:11:38
     */
    @RequestMapping("/tmscounter/regular/cancelconfirm.htm")
    public ModelAndView cancelConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String cancelConfirmCmd = request.getParameter("cancelConfirmForm");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.debug("CanceFundController|cancelconfirm: cancelConfirmCmd:{}, custInfoForm:{}, transactorInfoForm:{}",
                cancelConfirmCmd, custInfoForm, transactorInfoForm);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        RegularCancelDealDto orderDto = JSON.parseObject(cancelConfirmCmd, RegularCancelDealDto.class);

        RegularCounterCancelReqDto counterCancelReqDto = new RegularCounterCancelReqDto();
        counterCancelReqDto.setDealAppNo(dealAppNo);
        counterCancelReqDto.setProductCode(orderDto.getProductCode());
        //counterCancelReqDto.setFundShareClass(orderDto.getFundShareClass());
        counterCancelReqDto.setDealNo(orderDto.getDealNo());
        counterCancelReqDto.setProductName(orderDto.getProductName());
        counterCancelReqDto.setAppAmt(orderDto.getAppAmt());
        counterCancelReqDto.setAppVol(orderDto.getAppVol());

        counterCancelReqDto.setDisCode(custInfoDto.getDisCode());
        counterCancelReqDto.setCustName(custInfoDto.getCustName());
        counterCancelReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterCancelReqDto.setIdNo(custInfoDto.getIdNo());
        counterCancelReqDto.setIdType(custInfoDto.getIdType());
        counterCancelReqDto.setInvstType(custInfoDto.getInvstType());
        counterCancelReqDto.setCpAcctNo(custInfoDto.getCpAcctNo());
        counterCancelReqDto.setAppDt(transactorInfoDto.getAppDt());
        counterCancelReqDto.setAppTm(transactorInfoDto.getAppTm());
        counterCancelReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        counterCancelReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterCancelReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterCancelReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterCancelReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterCancelReqDto.setCancelType(transactorInfoDto.getCancelType());
        counterCancelReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterCancelReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

        counterCancelReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        counterCancelReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        // 定期协议
        counterCancelReqDto.setProtocolType(ProtocolTypeEnum.REGULAR.getCode());
        // 定期产品
        counterCancelReqDto.setProductClass(ProductClassEnum.FIX.getCode());
        counterCancelReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECK.getCode());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterCancelReqDto);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(orderDto.getDisCode());
        RegularCounterCancelRespDto responseDto = tmsRegularCounterService.counterCancel(counterCancelReqDto, disInfoDto);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/regular/querycancancelforapply.htm")
    public ModelAndView queryCanCancelForApply(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String dealNo = request.getParameter("dealNo");
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        List<RegularCancelDealDto> canCancelOrders = tmsRegularCounterService.queryCanCancelOrder(dealNo, custNo, null);
        Map<String, RegularCancelDealDto> mapData = convertMap(canCancelOrders);

        RegularCounterQueryOrderReqDto counterQueryOrderReqDto = new RegularCounterQueryOrderReqDto();
        List<String> txCodeList = new ArrayList<String>();
        txCodeList.add(TxCodes.COUNTER_CANCEL);
        txCodeList.add(TxCodes.COUNTER_FORCE_CANCEL);
        counterQueryOrderReqDto.setTxCodeList(txCodeList);

        List<String> checkFlagLsit = new ArrayList<String>();
        checkFlagLsit.add(CheckFlagEnum.NOT_CHECK.getCode());
        checkFlagLsit.add(CheckFlagEnum.CHECK_REJECT.getCode());
        checkFlagLsit.add(CheckFlagEnum.CHECK_SUCC.getCode());
        counterQueryOrderReqDto.setCheckFlagLsit(checkFlagLsit);
        // 定期
        counterQueryOrderReqDto.setProductClass(ProductClassEnum.FIX.getCode());

        RegularCounterQueryOrderRespDto counterQueryOrderRespDto = tmsRegularCounterService.counterQueryOrder(counterQueryOrderReqDto, null);
        List<RegularCounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();

        if (!CollectionUtils.isEmpty(counterOrderList)) {
            for (RegularCounterOrderDto counterOrderDto : counterOrderList) {
                if (mapData.containsKey(counterOrderDto.getDealNo())) {
                    canCancelOrders.remove(mapData.get(counterOrderDto.getDealNo()));
                }
            }
        }
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("canCancelOrders", canCancelOrders);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/regular/querycancancel.htm")
    public ModelAndView queryCanCancel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String dealNo = request.getParameter("dealNo");
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        List<RegularCancelDealDto> canCancelOrders = tmsRegularCounterService.queryCanCancelOrder(dealNo, custNo, null);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("canCancelOrders", canCancelOrders);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 
     * convertMap:(list to map)
     * 
     * @param canCancelOrders
     * @return
     * <AUTHOR>
     * @date 2017年11月15日 下午1:45:40
     */
    private Map<String, RegularCancelDealDto> convertMap(List<RegularCancelDealDto> canCancelOrders) {
        Map<String, RegularCancelDealDto> map = new HashMap<String, RegularCancelDealDto>(16);

        if (CollectionUtils.isEmpty(canCancelOrders)) {
            return map;
        }

        for (RegularCancelDealDto cancelDealDto : canCancelOrders) {
            map.put(cancelDealDto.getDealNo(), cancelDealDto);
        }
        return map;
    }
}
