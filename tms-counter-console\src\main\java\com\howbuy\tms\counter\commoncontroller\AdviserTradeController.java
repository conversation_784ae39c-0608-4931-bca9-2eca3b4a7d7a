package com.howbuy.tms.counter.commoncontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoFacade;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoRequest;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.interlayer.product.model.fund.FundBasicInfoModel;
import com.howbuy.interlayer.product.model.portfolio.PortfolioProductFullModel;
import com.howbuy.interlayer.product.service.AdviserProdInfoService;
import com.howbuy.interlayer.product.service.FundProductService;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.fundservice.trade.AdviserService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.robot.orders.facade.query.adviser.querycustadviserbalance.QueryCustAdviserBalanceRequest;
import com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolFacade;
import com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolRequest;
import com.howbuy.tms.robot.orders.facade.query.custprotocol.CustProtocolResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/2/7 13:28
 * @since JDK 1.8
 */
@Controller
public class AdviserTradeController extends AbstractController {
    private final static Logger logger = LogManager.getLogger(AdviserTradeController.class);

    @Resource
    private AdviserService activeService;

    @Resource
    private CustProtocolFacade custProtocolFacade;

    @Resource
    private AdviserProdInfoService adviserProdInfoService;

    @Autowired
    @Qualifier("tmscounter.queryBankCardInfoFacade")
    private QueryBankCardInfoFacade queryBankCardInfoFacade;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    @Qualifier("tmscounter.fundProductService")
    private FundProductService fundProductService;

    /**
     * @api {GET} /tmscounter/adviser/queryadviserredeeminfo.htm queryFundRedeemInfo()
     * @apiVersion 1.0.0
     * @apiGroup AdviserTradeController
     * @apiName queryFundRedeemInfo()
     * @apiParam (请求参数) {String} fundCode
     * @apiParam (请求参数) {String} custNo
     * @apiParam (请求参数) {String} disCode
     * @apiParam (请求参数) {String} protocolNo
     * @apiParam (请求参数) {String} appDt
     * @apiParam (请求参数) {String} appTm
     * @apiParamExample 请求参数示例
     * appTm=tMVO&custNo=S&fundCode=bATlzW2ehe&protocolNo=Frr&appDt=mL&disCode=iNsVy
     * @apiSuccess (响应结果) {String} code 响应码
     * @apiSuccess (响应结果) {String} desc 响应描述
     * @apiSuccess (响应结果) {Object} body *      响应体
     * @apiSuccessExample 响应结果示例
     * {"code":"v","body":{},"desc":"0"}
     */
    @RequestMapping("/tmscounter/adviser/queryadviserredeeminfo.htm")
    public ModelAndView queryFundRedeemInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String protocolNo = request.getParameter("protocolNo");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        logger.info("fundCode:{},custNo:{},disCode:{}, protocolNo:{}", fundCode, custNo, disCode, protocolNo);


        QueryCustAdviserBalanceRequest adviserBalanceRequest = new QueryCustAdviserBalanceRequest();
        adviserBalanceRequest.setTxAcctNo(custNo);
        adviserBalanceRequest.setProductCode(fundCode);
        adviserBalanceRequest.setProtocolNo(protocolNo);
        adviserBalanceRequest.setDisCode(disCode);
        adviserBalanceRequest.setAppDt(appDt);
        adviserBalanceRequest.setAppTm(appTm);
        QueryAcctBalanceDtlRespDto respDto = activeService.queryAdviserBalance(adviserBalanceRequest);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(respDto);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/self/queryselfredeeminfo.htm")
    public ModelAndView querySelfRedeemInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String protocolNo = request.getParameter("protocolNo");
        String productCode = request.getParameter("productCode");
        String cpAcctNo = request.getParameter("cpAcctNo");
        String appRatio = request.getParameter("appRatio");

        logger.info("custNo:{}, disCode:{}, productCode:{}, protocolNo:{},appRatio:{}", custNo, disCode, productCode, protocolNo, appRatio);

        QueryRedmProductListReqDto reqDto = new QueryRedmProductListReqDto();
        reqDto.setTxAcctNo(custNo);
        reqDto.setProductCode(productCode);
        reqDto.setProtocolNo(protocolNo);
        reqDto.setDisCode(disCode);
        reqDto.setCpAcctNo(cpAcctNo);
        if(!StringUtils.isEmpty(appRatio)){
            BigDecimal realRatio =  MathUtils.bigDecimalToStr(appRatio);
            BigDecimal realRatioStr = MathUtils.divide(realRatio, new BigDecimal("100"), 2);
            reqDto.setAppRatio(String.valueOf(realRatioStr));
        }
        QueryRedmProductListResDto respDto = activeService.queryRedmProductList(reqDto);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(respDto);
        WebUtil.write(response, rst);
        return null;
    }


    @RequestMapping("/tmscounter/self/querycustomsellratio.htm")
    public ModelAndView querySelfRedeemInfo1(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String protocolNo = request.getParameter("protocolNo");
        String productCode = request.getParameter("productCode");
        String cpAcctNo = request.getParameter("cpAcctNo");
        String appRatio = request.getParameter("appRatio");
        String customOrRatio = request.getParameter("customOrRatio");
        String redeemFundInfoList = request.getParameter("redeemFundInfoList");
        // 选择需要赎回的订单
        List<CounterRedeemReqDto> redeemReqList = JSON.parseArray(redeemFundInfoList, CounterRedeemReqDto.class);
        if(CollectionUtils.isEmpty(redeemReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.REDEEM_ORDER_NOT_SELECTED);
        }

        logger.info("custNo:{}, disCode:{}, productCode:{}, protocolNo:{}, cpAcctNo:{},customOrRatio:{}, appRatio:{}", custNo, disCode, productCode, protocolNo, cpAcctNo, customOrRatio, appRatio);
        logger.info("redeemFundInfoList:{}", JSON.toJSONString(redeemReqList));

        QueryCustomSellRatioResDto reqDto = new QueryCustomSellRatioResDto();
        reqDto.setTxAcctNo(custNo);
        reqDto.setDisCode(disCode);
        reqDto.setProtocolNo(protocolNo);
        reqDto.setCpAcctNo(cpAcctNo);
        reqDto.setCustomOrRatio(customOrRatio);

        if(!StringUtils.isEmpty(appRatio)){
            BigDecimal realRatio =  MathUtils.bigDecimalToStr(appRatio);
            BigDecimal realRatioValue = MathUtils.divide(realRatio, new BigDecimal("100"), 2);
            reqDto.setRedeemRatio(realRatioValue);
        }
        RedeemTrailResDto redeemTrailResDto = null;
        List<RedeemTrailResDto> redeemTrailResDtos = new ArrayList<>();
        for(CounterRedeemReqDto redeemReqDto : redeemReqList){
            redeemTrailResDto = new RedeemTrailResDto();
            redeemTrailResDto.setFundCode(redeemReqDto.getFundCode());
            redeemTrailResDto.setAppVol(redeemReqDto.getAppVol());
            redeemTrailResDtos.add(redeemTrailResDto);
        }
        reqDto.setRedeemTrailResDtos(redeemTrailResDtos);
        CustomSellRatioResDto respDto = activeService.queryCustomSellRatio(reqDto);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(respDto);
        WebUtil.write(response, rst);
        return null;
    }


    /**
     * @api {GET} /tmscounter/adviser/sell.htm redeemConfirm()
     * @apiVersion 1.0.0
     * @apiGroup AdviserTradeController
     * @apiName 赎回确认
     * @apiParam (请求参数) {String} dealAppNo
     * @apiParam (请求参数) {String} sellRedeemFunds
     * @apiParam (请求参数) {String} custInfoForm
     * @apiParam (请求参数) {String} transactorInfoForm
     * @apiParamExample 请求参数示例
     * dealAppNo=hnD212VPJl&transactorInfoForm=owQV&sellRedeemFunds=hKl7LQ4&custInfoForm=uxC
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccess (响应结果) {String} response.code 响应码
     * @apiSuccess (响应结果) {String} response.desc 响应描述
     * @apiSuccess (响应结果) {Object} response.body *      响应体
     * @apiSuccessExample 响应结果示例
     * [{"code":"s","body":{},"desc":"VBhWV"}]
     */
    @RequestMapping("/tmscounter/adviser/sell.htm")
    public ModelAndView redeemConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String dealAppNo = request.getParameter("dealAppNo");
        String sellRedeemFunds = request.getParameter("sellRedeemFunds");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        logger.debug("SellFundController|redeemConfirm|dealAppNo:{},sellRedeemFunds:{},custInfoForm:{},transactorInfoForm:{}",
                dealAppNo, sellRedeemFunds, custInfoForm, transactorInfoForm);

        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        // 选择需要赎回的订单
        List<CounterRedeemReqDto> redeemReqList = JSON.parseArray(sellRedeemFunds, CounterRedeemReqDto.class);
        if(CollectionUtils.isEmpty(redeemReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.REDEEM_ORDER_NOT_SELECTED);
        }

        List<TmsCounterResult> rstList = new ArrayList<TmsCounterResult>(16);

        // 循环落申请赎回单
        for(CounterRedeemReqDto reqDto : redeemReqList){
            // 赎回比例为空或0时，不去做赎回申请
            if(reqDto.getRedeemRatio() == null || reqDto.getRedeemRatio().compareTo(BigDecimal.ZERO) == 0 ){
                continue;
            }
            CounterRedeemReqDto counterRedeemReqDto = new CounterRedeemReqDto();

            PortfolioProductFullModel portfolioProdInfoDto = adviserProdInfoService.queryAdviserProdInfoByProductCodeV2(reqDto.getFundCode());
            counterRedeemReqDto.setPartnerCode(portfolioProdInfoDto.getPartnerCode());
            counterRedeemReqDto.setAppRatio(MathUtils.divide(reqDto.getRedeemRatio(), new BigDecimal("100"), 4));
            counterRedeemReqDto.setFundCode(portfolioProdInfoDto.getProductCode());
            counterRedeemReqDto.setFundName(portfolioProdInfoDto.getProductName());
            counterRedeemReqDto.setDealAppNo(dealAppNo);
            counterRedeemReqDto.setAppVol(reqDto.getAppVol());
            counterRedeemReqDto.setCpAcctNo(reqDto.getCpAcctNo());
            counterRedeemReqDto.setBankCode(reqDto.getBankCode());
            counterRedeemReqDto.setBankAcct(reqDto.getBankAcct());
            counterRedeemReqDto.setLargeRedmFlag(reqDto.getLargeRedmFlag());
            counterRedeemReqDto.setUnusualTransType(reqDto.getUnusualTransType());
            // 赎回去向 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
            counterRedeemReqDto.setRedeemCapitalFlag(reqDto.getRedeemCapitalFlag());
            // 是否全赎标识：1-是；0-否
            counterRedeemReqDto.setAllRedeemFlag(reqDto.getAllRedeemFlag());
            // 协议号
            counterRedeemReqDto.setProtocolNo(reqDto.getProtocolNo());
            // 协议类型(普通公募或定投)
            counterRedeemReqDto.setProtocolType(reqDto.getProtocolType());
            // 可赎回日期
            counterRedeemReqDto.setOpenRedeDt(reqDto.getOpenRedeDt());

            counterRedeemReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
            // 产品类别: 零售
            counterRedeemReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
            counterRedeemReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());
            counterRedeemReqDto.setEsitmateFee(BigDecimal.ZERO);

            counterRedeemReqDto.setTxAcctNo(custInfoDto.getCustNo());
            counterRedeemReqDto.setDisCode(custInfoDto.getDisCode());
            counterRedeemReqDto.setIdNo(custInfoDto.getIdNo());
            counterRedeemReqDto.setIdType(custInfoDto.getIdType());
            counterRedeemReqDto.setCustName(custInfoDto.getCustName());
            counterRedeemReqDto.setInvstType(custInfoDto.getInvstType());

            counterRedeemReqDto.setAppDt(transactorInfoDto.getAppDt());
            counterRedeemReqDto.setAppTm(transactorInfoDto.getAppTm());
            counterRedeemReqDto.setConsCode(transactorInfoDto.getConsCode());
            counterRedeemReqDto.setOutletCode(transactorInfoDto.getOutletCode());
            counterRedeemReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
            counterRedeemReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
            counterRedeemReqDto.setTransactorName(transactorInfoDto.getTransactorName());
            counterRedeemReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
            counterRedeemReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

            counterRedeemReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
            counterRedeemReqDto.setChecker(operatorInfoCmd.getOperatorNo());
            CommonUtil.setCommonOperInfo(operatorInfoCmd, counterRedeemReqDto);

            // do service
            CounterRedeemRespDto responseDto = tmsFundCounterService.counterRedeemAdviser(counterRedeemReqDto, disInfoDto);

            TmsCounterResult rst = null;
            if(responseDto != null ){
               rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
            } else{
                rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
            }

            Map<String, Object> body = new HashMap<String, Object>(16);
            body.put("responseDto", responseDto);
            body.put("requestDto", reqDto);
            rst.setBody(body);

            // set return
            rstList.add(rst);
        }
        WebUtil.write(response, rstList);
        return null;
    }


    @RequestMapping("/tmscounter/self/sell.htm")
    public ModelAndView redeemSelfConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("组合赎回确认下单");
        String dealAppNo = request.getParameter("dealAppNo");
        String sellRedeemForm = request.getParameter("sellRedeemForm");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String sellProtocolNoForm = request.getParameter("sellProtocolNoForm");

        logger.debug("SellFundController|redeemConfirm|dealAppNo:{},sellRedeemFunds:{},custInfoForm:{},transactorInfoForm:{}",
                dealAppNo, sellRedeemForm, custInfoForm, transactorInfoForm);

        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());

        QueryCustomSellRatioResDto queryCustomSellRatioResDto = JSON.parseObject(sellProtocolNoForm, QueryCustomSellRatioResDto.class);
        BigDecimal appRatio = queryCustomSellRatioResDto.getRedeemRatio();
        if(appRatio != null){
            BigDecimal realRatioValue = MathUtils.divide(appRatio, new BigDecimal("100"), 4);
            queryCustomSellRatioResDto.setRedeemRatio(realRatioValue);
        }
        // 选择需要赎回的订单
        List<CounterRedeemReqDto> redeemReqList = JSON.parseArray(sellRedeemForm, CounterRedeemReqDto.class);
        if(CollectionUtils.isEmpty(redeemReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.REDEEM_ORDER_NOT_SELECTED);
        }

        CounterPortfolioRedeemReqDto counterRedeemReqDto = getCounterPortfolioRedeemReqDto(dealAppNo, queryCustomSellRatioResDto, redeemReqList, transactorInfoDto, operatorInfoCmd, custInfoDto);

        // do service
        CounterRedeemRespDto responseDto = tmsFundCounterService.counterRedeempPortfolio(counterRedeemReqDto, disInfoDto);

        TmsCounterResult rst = null;
        if(responseDto != null ){
            rst = new TmsCounterResult(responseDto.getReturnCode(), responseDto.getDescription());
        } else{
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
        }

        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("responseDto", responseDto);
        body.put("requestDto", counterRedeemReqDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private CounterPortfolioRedeemReqDto getCounterPortfolioRedeemReqDto(String dealAppNo, QueryCustomSellRatioResDto queryCustomSellRatioResDto, List<CounterRedeemReqDto> redeemReqList,
            TransactorInfoDto transactorInfoDto, OperatorInfoCmd operatorInfoCmd, CustInfoDto custInfoDto) {
        CustProtocolResponse custProtocol = getCustProtocolResponse(queryCustomSellRatioResDto.getProtocolNo(), custInfoDto);
        if(custProtocol == null){
            throw new TmsCounterException(TmsCounterResultEnum.MESSAGE_INFO_NULL);
        }

        QueryBankCardInfoResponse bankCardInfo = queryBankCardInfo(queryCustomSellRatioResDto.getCpAcctNo(), custInfoDto);
        if(bankCardInfo == null){
            throw new TmsCounterException(TmsCounterResultEnum.MESSAGE_INFO_NULL);
        }

        CounterPortfolioRedeemReqDto counterRedeemReqDto = new CounterPortfolioRedeemReqDto();
        counterRedeemReqDto.setCustomOrRatio(queryCustomSellRatioResDto.getCustomOrRatio());
        counterRedeemReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterRedeemReqDto.setDisCode(custInfoDto.getDisCode());
        counterRedeemReqDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        counterRedeemReqDto.setProtocolNo(queryCustomSellRatioResDto.getProtocolNo());
        counterRedeemReqDto.setProtocolType(custProtocol.getProtocolType());
        counterRedeemReqDto.setProductCode(custProtocol.getProductCode());
        counterRedeemReqDto.setProductName(custProtocol.getProductName());
        counterRedeemReqDto.setCpAcctNo(queryCustomSellRatioResDto.getCpAcctNo());
        counterRedeemReqDto.setAppRatio(MathUtils.bigDecimalToString(queryCustomSellRatioResDto.getRedeemRatio()));
        counterRedeemReqDto.setDealAppNo(dealAppNo);

        counterRedeemReqDto.setAppRatio(String.valueOf(queryCustomSellRatioResDto.getRedeemRatio()));
        counterRedeemReqDto.setCpAcctNo(queryCustomSellRatioResDto.getCpAcctNo());
        counterRedeemReqDto.setBankCode(bankCardInfo.getBankCode());
        counterRedeemReqDto.setBankAcct(bankCardInfo.getBankAcctMask());
        counterRedeemReqDto.setLargeRedmFlag(queryCustomSellRatioResDto.getLargeRedmFlag());
        counterRedeemReqDto.setUnusualTransType(queryCustomSellRatioResDto.getUnusualTransType());
        // 赎回去向 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
        counterRedeemReqDto.setRedeemCapitalFlag(queryCustomSellRatioResDto.getRedeemCapitalFlag());
        counterRedeemReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        // 产品类别: 零售
        counterRedeemReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
        counterRedeemReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());

        counterRedeemReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterRedeemReqDto.setDisCode(custInfoDto.getDisCode());
        counterRedeemReqDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        counterRedeemReqDto.setIdNo(custInfoDto.getIdNo());
        counterRedeemReqDto.setIdType(custInfoDto.getIdType());
        counterRedeemReqDto.setCustName(custInfoDto.getCustName());
        counterRedeemReqDto.setInvstType(custInfoDto.getInvstType());

        counterRedeemReqDto.setAppDt(transactorInfoDto.getAppDt());
        counterRedeemReqDto.setAppTm(transactorInfoDto.getAppTm());
        counterRedeemReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterRedeemReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterRedeemReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterRedeemReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterRedeemReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterRedeemReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

        counterRedeemReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        counterRedeemReqDto.setChecker(operatorInfoCmd.getOperatorNo());
        counterRedeemReqDto.setCreator(operatorInfoCmd.getOperatorNo());
        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterRedeemReqDto);
        counterRedeemReqDto.setOpenFlag(queryCustomSellRatioResDto.getOpenFlag());

        if(CollectionUtils.isEmpty(redeemReqList)){
            throw new TmsCounterException(TmsCounterResultEnum.REDEEM_ORDER_NOT_SELECTED);
        }

        String tradeDt = queryTradeDayOuterService.getWorkDay(transactorInfoDto.getAppDt(), transactorInfoDto.getAppTm());
        Map<String, FundBasicInfoModel> fundBasicInfoMap = getBatchFundBasicInfo(redeemReqList, tradeDt);

        List<CounterPortfolioRedeemListDto> counterPortfolioRedeemListDtos = new ArrayList<>();
        CounterPortfolioRedeemListDto portfolioRedeemDto = null;
        for(CounterRedeemReqDto reqDto : redeemReqList){
            portfolioRedeemDto = new CounterPortfolioRedeemListDto();
            if(reqDto.getAppVol() == null || reqDto.getAppVol().compareTo(BigDecimal.ZERO) <= 0){
                continue;
            }
            portfolioRedeemDto.setTxAcctNo(custInfoDto.getCustNo());
            portfolioRedeemDto.setDisCode(custInfoDto.getDisCode());
            portfolioRedeemDto.setFundCode(reqDto.getFundCode());
            portfolioRedeemDto.setAppVol(reqDto.getAppVol());
            portfolioRedeemDto.setCpAcctNo(queryCustomSellRatioResDto.getCpAcctNo());
            portfolioRedeemDto.setProtocolNo(custProtocol.getProtocolNo());
            portfolioRedeemDto.setProtocolType(custProtocol.getProtocolType());

            portfolioRedeemDto.setBankAcct(bankCardInfo.getBankAcctMask());
            portfolioRedeemDto.setBankCode(bankCardInfo.getBankCode());
            portfolioRedeemDto.setProductChannel(ProductChannelEnum.FUND.getCode());
            portfolioRedeemDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());

            FundBasicInfoModel fundBasicInfoModel = fundBasicInfoMap.get(reqDto.getFundCode());
            if(fundBasicInfoModel == null){
                throw new TmsCounterException(TmsCounterResultEnum.MESSAGE_INFO_NULL);
            }
            portfolioRedeemDto.setFundName(fundBasicInfoModel.getFundName());
            portfolioRedeemDto.setTaCode(fundBasicInfoModel.getTaCode());
            portfolioRedeemDto.setFundShareClass(fundBasicInfoModel.getShareClass());

            counterPortfolioRedeemListDtos.add(portfolioRedeemDto);
        }
        counterRedeemReqDto.setCounterPortfolioRedeemListDtos(counterPortfolioRedeemListDtos);
        return counterRedeemReqDto;
    }

    private Map<String, FundBasicInfoModel> getBatchFundBasicInfo(List<CounterRedeemReqDto> redeemReqList, String taTradeDt) {
        Map<String, FundBasicInfoModel> fundBasicInfoMap = new HashMap<>();
        if(CollectionUtils.isEmpty(redeemReqList)){
            return fundBasicInfoMap;
        }
        List<String> fundCodes = redeemReqList.stream().map(CounterRedeemReqDto::getFundCode).distinct().collect(Collectors.toList());
        List<FundBasicInfoModel> fundBasicInfoModels = fundProductService.getBatchFundInfoByTradeDt(fundCodes, taTradeDt);
        //List<FundBasicInfoModel> fundBasicInfoModels = fundBaseInfoService.getBatchFundBasicInfo(fundCodes);
        if(CollectionUtils.isEmpty(fundBasicInfoModels)){
            return fundBasicInfoMap;
        }
        for(FundBasicInfoModel fundBasicInfoModel : fundBasicInfoModels){
            fundBasicInfoMap.put(fundBasicInfoModel.getFundCode(), fundBasicInfoModel);
        }
        return fundBasicInfoMap;
    }

    private CustProtocolResponse getCustProtocolResponse(String protocolNo, CustInfoDto custInfoDto) {
        if(StringUtils.isEmpty(protocolNo) || StringUtils.isEmpty(custInfoDto.getCustNo()) || StringUtils.isEmpty(custInfoDto.getDisCode())){
            return null;
        }
        CustProtocolRequest custProtocolRequest = new CustProtocolRequest();
        custProtocolRequest.setProtocolNo(protocolNo);
        custProtocolRequest.setTxAcctNo(custInfoDto.getHboneNo());
        custProtocolRequest.setTxAcctNo(custInfoDto.getCustNo());
        custProtocolRequest.setDisCode(custInfoDto.getDisCode());
        Date nowDate = new Date();
        custProtocolRequest.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        custProtocolRequest.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        CustProtocolResponse custProtocolResponse = custProtocolFacade.execute(custProtocolRequest);
        return custProtocolResponse;
    }

    public QueryBankCardInfoResponse queryBankCardInfo(final String cpAcctNo, CustInfoDto custInfoDto) {
        QueryBankCardInfoRequest request = new QueryBankCardInfoRequest();
        request.setTxAcctNo(custInfoDto.getCustNo());
        request.setDisCode(custInfoDto.getDisCode());
        request.setCpAcctNo(cpAcctNo);
        request.setCpAcctSwitch(true);
        Date nowDate = new Date();
        request.setAppDt(DateUtils.formatToString(nowDate, DateUtils.YYYYMMDD));
        request.setAppTm(DateUtils.formatToString(nowDate, DateUtils.HHMMSS));
        QueryBankCardInfoResponse res = queryBankCardInfoFacade.execute(request);
        return res;
    }


}
