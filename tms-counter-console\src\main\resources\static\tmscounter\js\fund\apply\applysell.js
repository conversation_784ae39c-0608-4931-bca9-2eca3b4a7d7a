$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplySell.checkOrder = {};	 
	ApplySell.init(checkId,custNo,disCode,idNo);
	ApplySell.isAdviser = false;
	ApplySell.productInfo = {};
});

var ApplySell = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,ApplySell.queryCheckOrderByIdBack);
		$("#confimSellBtn").on('click', function () {
			ApplySell.confirm();
		});

		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplySell.checkOrder);
		});

		$("#appRatio").on('keyup',function(){
			ApplySell.validatorRedeemRatio(this);
		});

		$("#appVol").blur(function(){
			$("#appVol").val(CommonUtil.formatAmount($("#appVol").val()));
		});
	},

	/**
	 * 校验赎回比例(事件:onkeyup)
	 */
	validatorRedeemRatio: function (thisObj) {
		var redeemRatio = thisObj.value;
		//if (!/^[0-9]+$/.test(redeemRatio)) {
		if (!/^[0-9]+\.?[0-9]{0,2}$/.test(redeemRatio)) {
            CommonUtil.layer_tip('只能输入数字且小数点后两位');
            thisObj.value = '';
		}

		if (!(redeemRatio <= 100 && redeemRatio >= 0)) {
			CommonUtil.layer_tip('请输入赎回比例1~100');
			thisObj.value = '';
		}
	},

	/***
	 * 确认赎回
	 */
	confirm: function () {
		var sellConfirmForm = $("#sellConfirmForm").serializeObject();
		var sellRedeemFunds = [];

		var checkOrderRedeem = ApplySell.checkOrder;

		if (ApplySell.isAdviser) {
			var appRatio = sellConfirmForm.appRatio;
			var redeemStatus = ApplySell.checkOrder.redeemStatus;
			// 校验提交的赎回申请份额是否为空
			if (isEmpty(appRatio)) {
				CommonUtil.layer_tip("赎回比例不能为空");
				CommonUtil.enabledBtn("confimSellBtn");
				return false;
			}
			//校验开放赎回日期是否大于当前日期
			if (redeemStatus != '1') {
				CommonUtil.layer_tip("该投顾不可赎回，请重新选择");
				CommonUtil.enabledBtn("confimSellBtn");
				return false;
			}
			checkOrderRedeem.redeemRatio = sellConfirmForm.appRatio;
		}else {
			var appVol = sellConfirmForm.appVol;
			// 校验提交的赎回申请份额是否为空
			if (isEmpty(appVol)) {
				CommonUtil.layer_tip("申请份额不能为空");
				CommonUtil.enabledBtn("confimSellBtn");
				return false;
			}
			checkOrderRedeem.appVol = sellConfirmForm.appVol;
		}
		checkOrderRedeem.unusualTransType = sellConfirmForm.unusualTransType;
		sellRedeemFunds.push(checkOrderRedeem);


		// 校验其他录入信息
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		transactorInfoForm.appDtm = transactorInfoForm.appDt + '' + transactorInfoForm.appTm;
		if (CommonUtil.isEmpty(transactorInfoForm.appTm)) {
			CommonUtil.layer_tip("请输入赎回下单时间");
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}
		if (!Valid.valiadTradeTime(transactorInfoForm.appTm)) {
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}

		if (!Validate.validateTransactorInfo(transactorInfoForm, QueryCustInfo.custInfo)) {
			CommonUtil.enabledBtn("confimSellBtn");
			return false;
		}

		var dealAppNo = "";
		if (!(typeof ApplySell == "undefined")) {
			dealAppNo = ApplySell.checkOrder.dealAppNo;
		}



		layer.confirm('确定提交吗？', {
			btn: ['确定', '取消']
		}, function (index) {

			if (ApplySell.isAdviser) {

				layerall_close();
				ApplySell.confirmAdviserSubmit(dealAppNo, sellRedeemFunds, transactorInfoForm);

			}else {
				// 校验当前TA交易日是否有确认份额是否继续赎回
				var uri = TmsCounterConfig.SELL_FUND_TADTACKVOL_VALIDATE_URL || {};
				var reqparamters = {
					"sellRedeemFunds": JSON.stringify(sellRedeemFunds),
					"custInfoForm": JSON.stringify(QueryCustInfo.custInfo)
				};

				//console.log(reqparamters);
				var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
				CommonUtil.ajaxAndCallBack(paramters, function (data) {
					layerall_close();

					if (CommonUtil.isArray(data) && data.length > 0) {

						var retMsg = "";
						$(data).each(function (index, element) {

							var ackVol = element.desc || '0';
							var bodyData = element.body || {};
							var reqVdlDto = bodyData.reqVdlDto || {};

							retMsg += "基金: " + reqVdlDto.fundCode + ", 申请赎回: " + reqVdlDto.appVol + "份, 当前TA交易日已经存在确认份额：" + ackVol + "份。<br>";

						});
						retMsg = retMsg + "<br>是否继续赎回？";
						// 否继续赎回
						layer.confirm(retMsg, {
							btn: ['是', '否']
						}, function (index) {
							layerall_close();
							ApplySell.confirmSubmit(dealAppNo, sellRedeemFunds, transactorInfoForm);

						}, function () {
							layerall_close();
						});

					} else {
						ApplySell.confirmSubmit(dealAppNo, sellRedeemFunds, transactorInfoForm);
					}
				});
			}



		}, function () {
			layerall_close();
		});
	},

	confirmAdviserSubmit: function (dealAppNo, sellRedeemFunds, transactorInfoForm) {

		var uri = TmsCounterConfig.SELL_ADVISER_CONFIRM_URL || {};
		var reqparamters = {
			"dealAppNo": dealAppNo,
			"sellRedeemFunds": JSON.stringify(sellRedeemFunds),
			"custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
			"transactorInfoForm": JSON.stringify(transactorInfoForm)
		};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxAndCallBack(paramters, ApplySell.adviserCallBack);
	},

	adviserCallBack: function (data) {
		var retMsg = "";
		if (data == null) {
			retMsg = "提交失败。";

		} else {
			if (!CommonUtil.isSucc(data.code) && !CommonUtil.isArray(data)) {
				retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
			} else if (CommonUtil.isArray(data) && data.length > 0) {
				retMsg = "";
				$(data).each(function (index, element) {
					//console.log(element);
					var respCode = element.code || '';
					var respDesc = element.desc || '';
					var bodyData = element.body || {};
					var responseDto = bodyData.responseDto || {};
					var requestDto = bodyData.requestDto || {};

					retMsg += "产品: " + requestDto.fundCode + ", 申请赎回比例: " + requestDto.redeemRatio + "%, 赎回提交" + respDesc + "。<br>";
				});
			}else {
				retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
			}
		}

		//CommonUtil.layer_tip(retMsg, 3000);
		layer.open({
			type: 1,
			skin: 'layui-layer-rim', //加上边框
			area: ['400px', '150px'], //宽高
			content: '<div style="text-align: center;margin: 10px 10px;">' + retMsg + '</div>'
		});

		if ($(".confimBtn").length > 0) {
			CommonUtil.disabledBtnWithClass("confimBtn");
			CommonUtil.disabledBtn("abolishBtn");
		}

	},

	/**
	 * 确认提交
	 * @param dealAppNo
	 * @param sellRedeemFunds
	 * @param transactorInfoForm
	 */
	confirmSubmit: function (dealAppNo, sellRedeemFunds, transactorInfoForm) {

		var uri = TmsCounterConfig.SELL_FUND_CONFIRM_URL || {};
		var reqparamters = {
			"dealAppNo": dealAppNo,
			"sellRedeemFunds": JSON.stringify(sellRedeemFunds),
			"custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
			"transactorInfoForm": JSON.stringify(transactorInfoForm)
		};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxAndCallBack(paramters, ApplySell.callBack);
	},

	callBack: function (data) {
		var retMsg = "";
		if (data == null) {
			retMsg = "提交失败。";

		} else {
			if (!CommonUtil.isSucc(data.code) && !CommonUtil.isArray(data)) {
				retMsg = "提交失败, " + data.desc + "(" + data.code + ")。";
			}
			if (CommonUtil.isArray(data) && data.length > 0) {
				retMsg = "";
				$(data).each(function (index, element) {
					var respDesc = element.desc || '';
					var bodyData = element.body || {};
					var requestDto = bodyData.requestDto || {};

					retMsg += "基金: " + requestDto.fundCode + ", 申请赎回份额: " + requestDto.appVol + "份, 赎回提交" + respDesc + "。<br>";
				});
			}
		}

		//CommonUtil.layer_tip(retMsg, 3000);
		layer.open({
			type: 1,
			skin: 'layui-layer-rim', //加上边框
			area: ['400px', '150px'], //宽高
			content: '<div style="text-align: center;margin: 10px 10px;">' + retMsg + '</div>'
		});

		if ($(".confimBtn").length > 0) {
			CommonUtil.disabledBtnWithClass("confimBtn");
			CommonUtil.disabledBtn("abolishBtn");
		}

	},



	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplySell.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(ApplySell.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(ApplySell.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}



		if($(".selectAgened").length > 0){
			$(".selectAgened").val(ApplySell.checkOrder.agentFlag);
		}
		
		if($("#unusualTransType").length > 0){
			$("#unusualTransType").val(CommonUtil.getMapValue(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP, ApplySell.checkOrder.unusualTransType, '0'));
		}
		
		if($("#fundCode").length > 0){
			$("#fundCode").val(ApplySell.checkOrder.fundCode);
		}
		
		if($("#appVol").length > 0){
			$("#appVol").val(CommonUtil.formatAmount(ApplySell.checkOrder.appVol));
		}

		if($("#appRatio").length > 0){
			$("#appRatio").val(CommonUtil.multiply(ApplySell.checkOrder.appRatio, 100));
		}
		
		if($("#appVolCapitalForSell").length > 0){
			$("#appVolCapitalForSell").val(Main.format(CommonUtil.formatAmount(ApplySell.checkOrder.appVol)));
		}
		
		if($("#largeRedmFlag").length > 0){
			$("#largeRedmFlag").val(CommonUtil.getMapValue(CONSTANTS.LARGE_REDM_FLAG_MAP, ApplySell.checkOrder.largeRedmFlag, '0'));
		}

		QueryFundInfo.queryAdviserOrFundProduct(ApplySell.checkOrder.fundCode, ApplySell.queryProductInfoCallBack);


		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").val(ApplySell.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(ApplySell.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, ApplySell.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(ApplySell.checkOrder.transactorName);
		}

		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(ApplySell.checkOrder.transactorIdNo);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(ApplySell.checkOrder.transactorIdType);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(ApplySell.checkOrder.memo);
		}
	},

	queryProductInfoCallBack:function(productInfo){
		var isAdviser = productInfo.adviserFlag || false;
		ApplySell.isAdviser = isAdviser;
		ApplySell.productInfo = productInfo;
		console.info("====>ApplySell.isAdviser" +  ApplySell.isAdviser);

		ApplySell.productChangeStyle(isAdviser);


		if (ApplySell.isAdviser) {
			// 使用 输入框中的 接口返回的 产品代码 做后续查询操作
			ApplySell.checkOrder.fundCode = productInfo.productCode;
			ApplySell.queryCustAdviserHodlInfo(ApplySell.checkOrder);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}
			if($("#fundStatus").length > 0){
				$("#fundStatus").html(productInfo.isSoldOpen == '1' ? "可赎回" : "暂停赎回");
			}

		}else {
			// 使用 输入框中的 接口返回的 产品代码 做后续查询操作
			ApplySell.checkOrder.fundCode = productInfo.fundCode;
			QueryFundInfo.queryCustHodlInfo(ApplySell.checkOrder);

			var isCommonFund = QueryFundInfo.checkFundInfo(productInfo);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, productInfo.fundStat));
			}

			if(!isCommonFund){
				return false;
			}
		}

		if($("#selectBank").length > 0){
			$("#selectBank").val(ApplySell.checkOrder.cpAcctNo);
		}

		var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
		var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
		$("#availVol").html(selectDtl.availVol);

	},

	productChangeStyle:function(isAdviser){
		if(isAdviser){
			$("#appVol").val("--");
			$("#appVolCapitalForSell").val("--");

			$("#appRatio").attr("isnull","false");
			$("#appVol").removeAttr("isnull");
			$("#appVolCapitalForSell").removeAttr("isnull");

			$("#appRatio").attr('readonly', false);
			$("#appVol").attr('readonly', true);
			$("#appVolCapitalForSell").attr('readonly', true);
			$("#largeRedmFlag").find("option[value='0']").attr("selected",true);
			$("#largeRedmFlag").find("option[value='1']").remove();
		}else {
			$("#appRatio").val("--");


			$("#appRatio").removeAttr("isnull");
			$("#appVol").attr("isnull","false");
			$("#appVolCapitalForSell").attr("isnull","false");

			$("#appRatio").attr('readonly', true);
			$("#appVol").attr('readonly', false);
			$("#appVolCapitalForSell").attr('readonly', false);
			// $("#largeRedmFlag").append('<option value="1">顺延</option>')
			// $("#largeRedmFlag").find("option[value='1']").attr("selected",true);

		}
	},

	queryCustAdviserHodlInfo:function(order){
		var uri= TmsCounterConfig.QUERY_ADVISER_REDEEM_INFO_URL ||  {};
		var custNo = QueryCustInfo.custInfo.custNo || '';
		var disCode = QueryCustInfo.custInfo.disCode || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		var fundCode ='';
		var appDt ='';
		var appTm ='';
		var protocolNo ='';
		if(!order){
			fundCode = $("#fundCode").val();
			appDt = $("#appDt").val();
			appTm = $("#appTm").val();
		}else {
			appDt = order.appDt;
			appTm = order.appTm;
			fundCode = order.fundCode;
			protocolNo = order.protocolNo;
		}
		var reqparamters = {'protocolNo':protocolNo,'appDt':appDt,'appTm':appTm,"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ApplySell.queryCustAdviserHoldFundInfoCallBack);
	},

	/**
	 * 处理基金持仓信息
	 */
	queryCustAdviserHoldFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		QueryFundInfo.dtlList = bodyData.balanceDtlList || [];

		if(QueryFundInfo.dtlList == null || QueryFundInfo.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
		}

		var selectHtml ='';
		$(QueryFundInfo.dtlList).each(function(index,element){
			selectHtml +='<option indexnum ="'+index+'" value="'+element.cpAcctNo+'">'+element.bankName+''+element.bankAcctNo+' </option>';
		});

		$("#selectBank").html(selectHtml);
		$("#selectBank").change(function(){
			var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
			var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
			if($("#bankCode").length > 0){
				$("#bankCode").val(selectDtl.bankCode);
			}
		});

		$("#selectBank").change();

		if(QueryFundInfo.dtlList.length >0){
			var selectDtl = QueryFundInfo.dtlList[0] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
		}
	},
}
