/**
*交易查询
*<AUTHOR>
*@date 2017-09-18 15:23
*/
$(function(){
	Init.init();
	RegularQueryTradeApply.init();
	RegularQueryTradeApply.custInfo = {};
	RegularQueryTradeApply.checkOrders = [];
	RegularQueryTradeApply.checkedOrder = {};

});
 var  RegularQueryTradeApply = {
	init:function(){
		$("#queryBtn").on('click',function(){
			RegularQueryTradeApply.queryOrderInfo();
		});
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
	},
	
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_REGULAR_CHECK_ORDER_URL  ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);
		CommonUtil.ajaxPaging(uri,paramters, RegularQueryTradeApply.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		RegularQueryTradeApply.checkOrders = bodyData.counterOrderList || [];
		
		var staticData = bodyData.counterQueryOrderRespDto || {};
		$("#staticId").html("当页小计：申请笔数【"+RegularQueryTradeApply.checkOrders.length+"】申请金额【"+CommonUtil.formatAmount(staticData.pageAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.pageAppVol)+"】 合计：申请笔数【"+staticData.totalCount+"】申请金额【"+CommonUtil.formatAmount(staticData.totalAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.totalAppVol)+"】");

		$("#rsList").empty();
		if(RegularQueryTradeApply.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
			$("#rsList").append(trHtml);
		}
		
		var i = 1;
		$(RegularQueryTradeApply.checkOrders).each(function(index,element){
			var trList = [];
			trList.push(i++);
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custNameEncrypt));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, element.txCode, ''));
			trList.push(CommonUtil.formatData(element.productCode));
			trList.push(CommonUtil.formatData(element.productName));
			if(element.appAmt > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
			}else {
				trList.push('--');
			}
			if(element.appVol > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
			}else {
				trList.push('--');
			}
			trList.push(CommonUtil.formatData(element.appDt));
			trList.push(CommonUtil.formatData(element.appTm));
			trList.push('柜台');
			trList.push(CommonUtil.formatData(element.creator,''));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, element.checkFlag, ''));
			trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = '+index+'>详情</a>');
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
		});
		
		$(".reCheck").off();
		$(".reCheck").on('click',function(){
			
		var indexValue = $(this).attr("indexvalue");
		var checkOrder = RegularQueryTradeApply.checkOrders[indexValue] || {};
		RegularQueryTradeApply.checkedOrder = checkOrder;
		
		var idTypeValue ='';
		if(checkOrder.invstType == '0'){//属于机构
			idTypeValue = CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, checkOrder.idType, '');
		}
		if(checkOrder.invstType == '1'){
			idTypeValue = CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, checkOrder.idType, '');
		}
		if(checkOrder.invstType == '2'){
			idTypeValue = CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, checkOrder.idType, '');
		}
		
		var detailList = [];
		detailList.push('<tr>'+
		         			'<td class="type" >预申请单号</td>'+
		         			'<td>'+CommonUtil.formatData(checkOrder.dealAppNo,'')+'</td>'+
		         			'<td class="type">客户账号</td>'+
		         			'<td>'+CommonUtil.formatData(checkOrder.txAcctNo,'')+'</td>'+
		     	 		'</tr>');
		
		detailList.push('<tr>'+
		     				'<td class="type" >客户名称</td>'+
		     				'<td>'+CommonUtil.formatData(checkOrder.custName,'')+'</td>'+
		     				'<td class="type">证件类型</td>'+
		     				'<td>'+idTypeValue+'</td>'+
 	 					'</tr>');
		
		detailList.push('<tr>'+
					         '<td class="type" >证件号码</td>'+
					         '<td>'+CommonUtil.formatData(checkOrder.idNo,'')+'</td>'+
					         '<td class="type">业务类型</td>'+
					         '<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, checkOrder.txCode, '')+'</td>'+
			     	 	'</tr>');
		
		detailList.push('<tr>'+
					         '<td class="type" >产品代码</td>'+
					         '<td>'+CommonUtil.formatData(checkOrder.fundCode,'')+'</td>'+
					         '<td class="type">产品简称</td>'+
					         '<td>'+CommonUtil.formatData(checkOrder.fundName,'')+'</td>'+
			    	 	'</tr>');
		
		detailList.push('<tr>'+
					         '<td class="type" >申请金额</td>'+
					         '<td>'+CommonUtil.formatAmount(CommonUtil.formatData(checkOrder.appAmt,''))+'</td>'+
					         '<td class="type">申请份额</td>'+
					         '<td>'+CommonUtil.formatAmount(CommonUtil.formatData(checkOrder.appVol,''))+'</td>'+
   	 					'</tr>');
		
		detailList.push('<tr>'+
				         '<td class="type" ></td>'+
				         '<td></td>'+
				         '<td class="type"></td>'+
				         '<td></td>'+
		   	 			'</tr>');
		
		detailList.push('<tr>'+
			             	'<td class="type">银行名称</td>'+
			             	'<td>'+ CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, checkOrder.bankCode, '')+'</td>'+
			             	'<td class="type"></td>'+
			             	'<td>'+CommonUtil.formatData(checkOrder.discountRate,'')+'</td>'+
         				'</tr>');
		detailList.push('<tr>'+
			             	'<td class="type">银行账号</td>'+
			             	'<td>'+ CommonUtil.formatData(checkOrder.bankAcct,'')+'</td>'+
			             	'<td class="type">支付方式</td>'+
			             	'<td> 自划款 </td>'+
						'</tr>');

		detailList.push('<tr>'+ 
			 				'<td class="type">订单编号</td>'+
			 				'<td>'+ CommonUtil.formatData(checkOrder.dealNo,'')+'</td>'+
			             	'<td class="type"></td>'+
			             	'<td></td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
			 				'<td class="type">二次确认标识</td>'+
			 				'<td>'+CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, checkOrder.riskFlag, '')+'</td>'+
			 				'<td class="type">交易回款方式</td>'+
			 				'<td>'+CommonUtil.getMapValue(CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG, checkOrder.redeemCapitalFlag, '')+'</td>'+
						'</tr>');

		detailList.push('<tr>'+ 
			 				'<td class="type">失败原因</td>'+
			 				'<td>'+ CommonUtil.formatData(checkOrder.memo,'')+'</td>'+
			 				'<td class="type">审核状态</td>'+
			             	'<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, checkOrder.checkFlag, '')+'</td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
			 				'<td class="type">录入操作员</td>'+
			 				'<td>'+ CommonUtil.formatData(checkOrder.operatorNo,'')+'</td>'+
			 				'<td class="type">网点</td>'+
			             	'<td> 柜台  </td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
							'<td class="type">申请日期</td>'+
			             	'<td>'+CommonUtil.formatData(checkOrder.appDt,'')+'</td>'+
			 				'<td class="type">申请时间</td>'+
			 				'<td>'+ CommonUtil.formatData(checkOrder.appTm,'')+'</td>'+
						'</tr>');

			
			var bodyHtml = detailList.join('');
			$(".tabPop").html(bodyHtml);
		    layer.open({
		        title: ['详情', true],
		        type: 1,
		        area: ['820px', 'auto'],
		        skin: 'layui-layer-rim', //加上边框
		        btnAlign: 'l',
		        content: $('.reCheckInfo')
		    });		
		});
	}
};
