/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.service.orderplan.api.query;

import com.howbuy.tms.counter.service.orderplan.api.BaseResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @description:查询定投计划
 * <AUTHOR>
 * @date 2020年5月18日 下午1:53:56
 * @since JDK 1.6
 */
public class QuerySchePlanCounterResponse extends BaseResponse {
    private static final long serialVersionUID = -8396055412780017349L;
    /**
     * 查询结果
     */
    List<SchePlanCounterDto> results;

    /**
     * 当页申请金额
     */
    BigDecimal pageTotalAmt;

    /**
     * 当页申请份额
     */
    BigDecimal pageTotalVol;

    /**
     * 总申请金额
     */
    BigDecimal totalAmt;

    /**
     * 总申请金额
     */
    BigDecimal totalVol;


    public List<SchePlanCounterDto> getResults() {
        return results;
    }

    public void setResults(List<SchePlanCounterDto> results) {
        this.results = results;
    }

    public BigDecimal getPageTotalAmt() {
        return pageTotalAmt;
    }

    public void setPageTotalAmt(BigDecimal pageTotalAmt) {
        this.pageTotalAmt = pageTotalAmt;
    }

    public BigDecimal getPageTotalVol() {
        return pageTotalVol;
    }

    public void setPageTotalVol(BigDecimal pageTotalVol) {
        this.pageTotalVol = pageTotalVol;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getTotalVol() {
        return totalVol;
    }

    public void setTotalVol(BigDecimal totalVol) {
        this.totalVol = totalVol;
    }
}
