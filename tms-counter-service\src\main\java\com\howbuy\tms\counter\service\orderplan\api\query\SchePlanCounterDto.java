/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.service.orderplan.api.query;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description:查询定投计划
 * <AUTHOR>
 * @date 2020年5月18日 下午1:54:06
 * @since JDK 1.6
 */
public class SchePlanCounterDto implements Serializable {
    private static final long serialVersionUID = 4163168421032169343L;

    /**
     * 计划ID
     */
    private String scheId;

    /**
     * 计划类型
     */
    private String scheType;

    /**
     * 计划二级分类:1-存工资，2-还房贷，3-其他
     */
    private String scheSubType;

    /**
     * 计划行为：0-无，1-立即存入一笔
     */
    private String scheAction;

    /**
     * 计划名称
     */
    private String scheName;

    /**
     * 计划执行周期
     */
    private String scheCycle;

    /**
     * 计划日期类型：1-自然日；2-交易日
     */
    private String scheDateType;

    /**
     * 计划生效日期
     */
    private String scheEffectiveDate;

    /**
     * 计划生效时间
     */
    private String scheEffectiveTime;

    /**
     * 计划终止原因：1-客户自行终止；2-系统终止；
     */
    private String scheEndSrc;

    /**
     * 计划执行日期:[YYYYMM]DD
     */
    private String scheDate;

    /**
     * 实际计划执行日期检查日，日期类型为自然日
     */
    private String scheCheckDate;

    /**
     * 实际计划执行日期
     */
    private String scheActualDate;

    /**
     * 计划状态:1-正常；2-暂停；3-终止
     */
    private String scheStatus;

    /**
     * 计划暂停类型：1-暂停 2-暂停指定期数后,计划恢复 3-暂停至指定日期后,计划恢复
     */
    private String schePauseType;

    /**
     * 计划暂停参数
     */
    private String schePauseParam;

    /**
     * 计划恢复日期
     */
    private String recoverDate;

    /**
     * 顺延标记
     */
    private String deferFlag;

    /**
     * 顺延次数
     */
    private Integer deferNum;

    /**
     * 顺延执行日期
     */
    private String deferDate;

    /**
     * 连续失败期数
     */
    private Integer continuesFails;

    /**
     * 最近一次执行成功时间
     */
    private Date lastSuccessDate;

    /**
     * 成功期数
     */
    private Integer succTimes;

    /**
     * 历史失败次数
     */
    private Integer failTimes;

    /**
     * 合约计划已执行期数
     */
    private Integer scheTimes;

    /**
     * 计划终止类型
     */
    private String scheEndType;

    /**
     * 计划终止参数
     */
    private String scheEndParam;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 客户身份证号
     */
    private String idNo;

    /**
     * 证件号类型
     */
    private String idType;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 支付方式
     */
    private String payMode;

    /**
     * 赎回类型 1-全部赎回 0-部分赎回
     */
    private String redeemType;

    /**
     * 赎回去向：06-储蓄罐，04-银行卡
     */
    private String redeemMode;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行编号
     */
    private String bankCode;

    /**
     * 银行卡号
     */
    private String bankAcct;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 计划购买金额-用于申购
     */
    private BigDecimal scheAmt;

    /**
     * 计划赎回份额-用于赎回
     */
    private BigDecimal scheVol;

    /**
     * 交易渠道
     */
    private String txChannel;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 协议号
     */
    private String protocolNo;

    /**
     * 赎回订单号
     */
    private String redeemOrderNo;

    /**
     * 定投策略
     */
    private String scheStrategy;

    /**
     * 机构分销号
     */
    private String disCode;

    /**
     * 风险确认标记：0-未确认；1-已确认
     */
    private String riskFlag;

    /**
     * 风险二次确认时间YYYYMMDDHHMMSS
     */
    private String riskAckDtm;

    /**
     * 普通投资者风险提示时间YYYYMMDDHHMMSS
     */
    private String normalCustTipDtm;

    /**
     * 高风险提示时间YYYYMMDDHHMMSS
     */
    private String highRiskTipDtm;

    /**
     * 客户风险等级
     */
    private String custRiskLevel;

    /**
     * 份额类型
     */
    private String shareClass;

    /**
     * 过期时间
     */
    private String riskExpiredDate;

    /**
     * 特定投资者承诺函时间YYYYMMDDHHMMSS
     */
    private String specInvestDtm;

    /**
     * 风险揭示书标记，1-确认;0-未确认
     */
    private String riskRevealBookAckFlag;

    /**
     * 风险揭示书时间YYYYMMDDHHMMSS
     */
    private String riskRevealBookAckDtm;

    /**
     * 电子合同时间YYYYMMDDHHMMSS
     */
    private String elecContractDtm;

    /**
     * 累计扣款成功金额
     */
    private BigDecimal sustainExecAmt;

    /**
     * 父计划ID
     */
    private String parentId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 处理状态：0-未执行，1-执行中，2-执行成功，3-执行失败，4-待重试
     */
    private String procStat;


    /**
     * 赎回策略类型
     */
    private String redeemStrategyType;


    /**
     * 目标收益率
     */
    private BigDecimal targetYield;

    /**
     * 止盈回撤比率
     */
    private BigDecimal retracementYield;


    public String getScheId() {
        return scheId;
    }

    public void setScheId(String scheId) {
        this.scheId = scheId;
    }

    public String getScheType() {
        return scheType;
    }

    public void setScheType(String scheType) {
        this.scheType = scheType;
    }

    public String getScheSubType() {
        return scheSubType;
    }

    public void setScheSubType(String scheSubType) {
        this.scheSubType = scheSubType;
    }

    public String getScheAction() {
        return scheAction;
    }

    public void setScheAction(String scheAction) {
        this.scheAction = scheAction;
    }

    public String getScheName() {
        return scheName;
    }

    public void setScheName(String scheName) {
        this.scheName = scheName;
    }

    public String getScheCycle() {
        return scheCycle;
    }

    public void setScheCycle(String scheCycle) {
        this.scheCycle = scheCycle;
    }

    public String getScheDateType() {
        return scheDateType;
    }

    public void setScheDateType(String scheDateType) {
        this.scheDateType = scheDateType;
    }

    public String getScheEffectiveDate() {
        return scheEffectiveDate;
    }

    public void setScheEffectiveDate(String scheEffectiveDate) {
        this.scheEffectiveDate = scheEffectiveDate;
    }

    public String getScheEffectiveTime() {
        return scheEffectiveTime;
    }

    public void setScheEffectiveTime(String scheEffectiveTime) {
        this.scheEffectiveTime = scheEffectiveTime;
    }

    public String getScheEndSrc() {
        return scheEndSrc;
    }

    public void setScheEndSrc(String scheEndSrc) {
        this.scheEndSrc = scheEndSrc;
    }

    public String getScheDate() {
        return scheDate;
    }

    public void setScheDate(String scheDate) {
        this.scheDate = scheDate;
    }

    public String getScheCheckDate() {
        return scheCheckDate;
    }

    public void setScheCheckDate(String scheCheckDate) {
        this.scheCheckDate = scheCheckDate;
    }

    public String getScheActualDate() {
        return scheActualDate;
    }

    public void setScheActualDate(String scheActualDate) {
        this.scheActualDate = scheActualDate;
    }

    public String getScheStatus() {
        return scheStatus;
    }

    public void setScheStatus(String scheStatus) {
        this.scheStatus = scheStatus;
    }

    public String getSchePauseType() {
        return schePauseType;
    }

    public void setSchePauseType(String schePauseType) {
        this.schePauseType = schePauseType;
    }

    public String getSchePauseParam() {
        return schePauseParam;
    }

    public void setSchePauseParam(String schePauseParam) {
        this.schePauseParam = schePauseParam;
    }

    public String getRecoverDate() {
        return recoverDate;
    }

    public void setRecoverDate(String recoverDate) {
        this.recoverDate = recoverDate;
    }

    public String getDeferFlag() {
        return deferFlag;
    }

    public void setDeferFlag(String deferFlag) {
        this.deferFlag = deferFlag;
    }

    public Integer getDeferNum() {
        return deferNum;
    }

    public void setDeferNum(Integer deferNum) {
        this.deferNum = deferNum;
    }

    public String getDeferDate() {
        return deferDate;
    }

    public void setDeferDate(String deferDate) {
        this.deferDate = deferDate;
    }

    public Integer getContinuesFails() {
        return continuesFails;
    }

    public void setContinuesFails(Integer continuesFails) {
        this.continuesFails = continuesFails;
    }

    public Date getLastSuccessDate() {
        return lastSuccessDate;
    }

    public void setLastSuccessDate(Date lastSuccessDate) {
        this.lastSuccessDate = lastSuccessDate;
    }

    public Integer getSuccTimes() {
        return succTimes;
    }

    public void setSuccTimes(Integer succTimes) {
        this.succTimes = succTimes;
    }

    public Integer getFailTimes() {
        return failTimes;
    }

    public void setFailTimes(Integer failTimes) {
        this.failTimes = failTimes;
    }

    public Integer getScheTimes() {
        return scheTimes;
    }

    public void setScheTimes(Integer scheTimes) {
        this.scheTimes = scheTimes;
    }

    public String getScheEndType() {
        return scheEndType;
    }

    public void setScheEndType(String scheEndType) {
        this.scheEndType = scheEndType;
    }

    public String getScheEndParam() {
        return scheEndParam;
    }

    public void setScheEndParam(String scheEndParam) {
        this.scheEndParam = scheEndParam;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getRedeemType() {
        return redeemType;
    }

    public void setRedeemType(String redeemType) {
        this.redeemType = redeemType;
    }

    public String getRedeemMode() {
        return redeemMode;
    }

    public void setRedeemMode(String redeemMode) {
        this.redeemMode = redeemMode;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public BigDecimal getScheAmt() {
        return scheAmt;
    }

    public void setScheAmt(BigDecimal scheAmt) {
        this.scheAmt = scheAmt;
    }

    public BigDecimal getScheVol() {
        return scheVol;
    }

    public void setScheVol(BigDecimal scheVol) {
        this.scheVol = scheVol;
    }

    public String getTxChannel() {
        return txChannel;
    }

    public void setTxChannel(String txChannel) {
        this.txChannel = txChannel;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getRedeemOrderNo() {
        return redeemOrderNo;
    }

    public void setRedeemOrderNo(String redeemOrderNo) {
        this.redeemOrderNo = redeemOrderNo;
    }

    public String getScheStrategy() {
        return scheStrategy;
    }

    public void setScheStrategy(String scheStrategy) {
        this.scheStrategy = scheStrategy;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getRiskAckDtm() {
        return riskAckDtm;
    }

    public void setRiskAckDtm(String riskAckDtm) {
        this.riskAckDtm = riskAckDtm;
    }

    public String getNormalCustTipDtm() {
        return normalCustTipDtm;
    }

    public void setNormalCustTipDtm(String normalCustTipDtm) {
        this.normalCustTipDtm = normalCustTipDtm;
    }

    public String getHighRiskTipDtm() {
        return highRiskTipDtm;
    }

    public void setHighRiskTipDtm(String highRiskTipDtm) {
        this.highRiskTipDtm = highRiskTipDtm;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public String getRiskExpiredDate() {
        return riskExpiredDate;
    }

    public void setRiskExpiredDate(String riskExpiredDate) {
        this.riskExpiredDate = riskExpiredDate;
    }

    public String getSpecInvestDtm() {
        return specInvestDtm;
    }

    public void setSpecInvestDtm(String specInvestDtm) {
        this.specInvestDtm = specInvestDtm;
    }

    public String getRiskRevealBookAckFlag() {
        return riskRevealBookAckFlag;
    }

    public void setRiskRevealBookAckFlag(String riskRevealBookAckFlag) {
        this.riskRevealBookAckFlag = riskRevealBookAckFlag;
    }

    public String getRiskRevealBookAckDtm() {
        return riskRevealBookAckDtm;
    }

    public void setRiskRevealBookAckDtm(String riskRevealBookAckDtm) {
        this.riskRevealBookAckDtm = riskRevealBookAckDtm;
    }

    public String getElecContractDtm() {
        return elecContractDtm;
    }

    public void setElecContractDtm(String elecContractDtm) {
        this.elecContractDtm = elecContractDtm;
    }

    public BigDecimal getSustainExecAmt() {
        return sustainExecAmt;
    }

    public void setSustainExecAmt(BigDecimal sustainExecAmt) {
        this.sustainExecAmt = sustainExecAmt;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getProcStat() {
        return procStat;
    }

    public void setProcStat(String procStat) {
        this.procStat = procStat;
    }

    public String getRedeemStrategyType() {
        return redeemStrategyType;
    }

    public void setRedeemStrategyType(String redeemStrategyType) {
        this.redeemStrategyType = redeemStrategyType;
    }

    public BigDecimal getTargetYield() {
        return targetYield;
    }

    public void setTargetYield(BigDecimal targetYield) {
        this.targetYield = targetYield;
    }

    public BigDecimal getRetracementYield() {
        return retracementYield;
    }

    public void setRetracementYield(BigDecimal retracementYield) {
        this.retracementYield = retracementYield;
    }
}
