/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common.util;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;



/**
 * @description:(session 工具类) 
 * <AUTHOR>
 * @date 2017年4月6日 下午6:09:55
 * @since JDK 1.6
 */
public class SessionUtil {
    private static final Logger logger = LogManager.getLogger(SessionUtil.class);
    public static <T> T getValue(String name,HttpServletRequest request){
       @SuppressWarnings("unchecked")
       T object = (T) request.getSession().getAttribute(name);
       if(object == null){
           
           logger.info("session {} 过期",name);
           throw new TmsCounterException(TmsCounterResultEnum.SESSION_TIME_OUT);
       }
       return object;
    }
    
    public static <T> void setValue(String name, T value , HttpServletRequest request){
        request.getSession().setAttribute(name, value);
     }
    
}

