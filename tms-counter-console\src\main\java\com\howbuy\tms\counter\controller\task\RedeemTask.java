/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller.task;

import com.howbuy.crm.prosale.request.QueryCurrentPreInfoRequest;
import com.howbuy.crm.prosale.response.GetCurrentPreInfoResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.utils.LoggerUtils;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.dto.ExpiredRedeemDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.high.orders.facade.trade.redeem.bean.RedeemDetailBean;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询结构化产品净值任务类
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月12日 下午5:52:16
 * @since JDK 1.7
 */
public class RedeemTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(RedeemTask.class);

    private RedeemCounterFacade redeemCounterFacade;

    private PreBookService preBookService;

    private TmsCounterOutService tmsCounterOutService;

    private Map<String, ExpiredRedeemDto> expiredRedeemRstMap;

    List<RedeemCounterRequest> redeemCounterRequestList;

    private String workDay;

   private static final String DEFAULT_TIME = "145959";

    private CountDownLatch latch;

    private String uuid;

    public RedeemTask(RedeemCounterFacade redeemCounterFacade,
                      PreBookService preBookService,
                      TmsCounterOutService tmsCounterOutService,
                      Map<String, ExpiredRedeemDto> expiredRedeemRstMap,
                      List<RedeemCounterRequest> redeemCounterRequestList,
                      String workDay,
                      CountDownLatch latch, String uuid) {
        this.redeemCounterRequestList = redeemCounterRequestList;
        this.redeemCounterFacade = redeemCounterFacade;
        this.preBookService = preBookService;
        this.tmsCounterOutService = tmsCounterOutService;
        this.expiredRedeemRstMap = expiredRedeemRstMap;
        this.workDay = workDay;
        this.latch = latch;
        this.uuid = uuid;
    }

    @Override
    public RuntimeException call() throws Exception {
        LoggerUtils.setConfig(uuid, null);
        logger.info("RedeemTask start");
        try {
            for (RedeemCounterRequest redeemCounterRequest : redeemCounterRequestList) {
                ExpiredRedeemDto expiredRedeemDto = expiredRedeemRstMap.get(buildKey(redeemCounterRequest));
                expiredRedeemDto.setRepurchaseProtocolNo(redeemCounterRequest.getRepurchaseProtocolNo());
                expiredRedeemDto.setTxAcctNo(redeemCounterRequest.getTxAcctNo());
                expiredRedeemDto.setFundCode(redeemCounterRequest.getFundCode());
                RedeemDetailBean redeemDetailBean = redeemCounterRequest.getRedeemDetailList().get(0);
                expiredRedeemDto.setCpAcctNo(redeemDetailBean.getCpAcctNo());
                expiredRedeemDto.setAppVol(redeemDetailBean.getAppVol());

                redeemCounterRequest.setExternalDealNo(UUID.randomUUID().toString());
                redeemCounterRequest.setDataTrack(UUID.randomUUID().toString());
                redeemCounterRequest.setAppDt(workDay);
                redeemCounterRequest.setAppTm(DEFAULT_TIME);

                redeemCounterRequest.setAppointmentDealNo(getAppointNo(redeemCounterRequest));
                redeemCounterRequest.setOperIp("127.0.0.1");
                TmsFacadeUtil.setCommonParameters(redeemCounterRequest, null);
                try{
                    RedeemCounterResponse redeemCounterResponse = redeemCounterFacade.execute(redeemCounterRequest);
                    expiredRedeemDto.setMemo(redeemCounterResponse.getDescription());
                    if (!"Z0000000".equals(redeemCounterResponse.getReturnCode())) {
                        expiredRedeemRstMap.put(buildKey(redeemCounterRequest), expiredRedeemDto);
                    }
                }catch (Exception e){
                    expiredRedeemDto.setMemo(e.getMessage());
                    expiredRedeemRstMap.put(buildKey(redeemCounterRequest), expiredRedeemDto);
                    logger.error("QueryStructProductNavTask|RuntimeException.", e);
                }
            }
        } catch (RuntimeException ex) {
            logger.error("QueryStructProductNavTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

    private String getAppointNo(RedeemCounterRequest redeemCounterRequest){
        QueryCurrentPreInfoRequest queryCurrentPreInfoRequest = new QueryCurrentPreInfoRequest();
        List<String> prebookStateList = new ArrayList<String>();
        prebookStateList.add(PreBookStateEnum.CONFIRM.getCode());
        queryCurrentPreInfoRequest.setPrebookState(prebookStateList);
        queryCurrentPreInfoRequest.setFundCode(redeemCounterRequest.getFundCode());
        List<String> preTypeList = new ArrayList<String>();
        preTypeList.add(OrderFormTypeEnum.PAPER.getCode());
        queryCurrentPreInfoRequest.setPreType(preTypeList);
        List<String> tradeTypeList = new ArrayList<String>();
        tradeTypeList.add(PreTradeTypeEnum.SELL.getCode());
        queryCurrentPreInfoRequest.setTradeType(tradeTypeList);
        String hbOneNo = tmsCounterOutService.queryHboneNoByTxAccountNo(redeemCounterRequest.getTxAcctNo());
        queryCurrentPreInfoRequest.setHboneNo(hbOneNo);
        queryCurrentPreInfoRequest.setUseFlag(PreBookUseFlagEnum.NOT_USED.getCode());
        GetCurrentPreInfoResponse currentPreInfoResponse = preBookService.getCurrentPreInfo(queryCurrentPreInfoRequest);

        if(currentPreInfoResponse != null){
            return currentPreInfoResponse.getPreId();
        }

        return null;
    }

    private String buildKey(RedeemCounterRequest redeemCounterRequest){
        String cpAcctNo = redeemCounterRequest.getRedeemDetailList().get(0).getCpAcctNo();
        return new StringBuilder(redeemCounterRequest.getTxAcctNo()).append("-")
                .append(cpAcctNo).append("-")
                .append(redeemCounterRequest.getFundCode()).toString();
    }

}

