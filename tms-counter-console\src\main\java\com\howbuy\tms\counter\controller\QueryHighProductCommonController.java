/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.controller;

import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.service.HighProductParamConfService;
import com.howbuy.interlayer.product.service.HighProductService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.howbuy.interlayer.product.service.QueryTaInfoService;
import com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService;
import com.howbuy.interlayer.product.service.high.request.QueryNotHBJGFundListRequest;
import com.howbuy.interlayer.product.service.high.response.QueryNotHBJGFundListResponse;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.util.StringUtils;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.FundDivDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(产品信息配置)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年9月14日 上午11:39:14
 * @since JDK 1.6
 */
@Controller
public class QueryHighProductCommonController {
    private static final Logger logger = LoggerFactory.getLogger(QueryHighProductCommonController.class);
    @Autowired
    @Qualifier("tmscounter.highProductService")
    private HighProductService highProductService;

    @Autowired
    @Qualifier("tmscounter.highProductParamConfService")
    private HighProductParamConfService highProductParamConfService;

    @Autowired
    @Qualifier("tmscounter.queryTaInfoService")
    private QueryTaInfoService queryTaInfoService;

    @Autowired
    @Qualifier("tmscounter.queryNotHBJGFundListService")
    private QueryNotHBJGFundListService queryNotHBJGFundListService;

    @RequestMapping("tmscounter/high/query/queryhighproductcodeandname.htm")
    public ModelAndView queryHighProductCodeAndName(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info(">>>>>>>查询所有的高端基金信息");

        String productChannel = request.getParameter("productChannel");
        if (StringUtils.isEmpty(productChannel)) {
            productChannel = null;
        }
        List<ListParam> highProducts = new ArrayList<>();
        List<ListParam> highProductList = getAllHighProductList(productChannel);
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        if(user.getInHBJG() && !CollectionUtils.isEmpty(highProductList)){
            QueryNotHBJGFundListResponse queryNotHBJGFundListResponse = queryNotHBJGFundListService.query(new QueryNotHBJGFundListRequest());
            for(ListParam listParam : highProductList){
                if(!queryNotHBJGFundListResponse.getFundCodes().contains(listParam.getCode())){
                    highProducts.add(listParam);
                }
            }
        }else {
            highProducts.addAll(highProductList);
        }

        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("list", highProducts);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("tmscounter/high/query/queryfundmancodeandname.htm")
    public ModelAndView queryFundManCodeAndName(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info(">>>>>>>查询所有的管理人");

        List<ListParam> fundManList = getAllFundMan();
        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("list", fundManList);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("tmscounter/high/query/querytainfocodeandname.htm")
    public ModelAndView queryTaInfoCodeAndName(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info(">>>>>>>查询所有TA信息");
        List<ListParam> taList = getAllTa();
        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("list", taList);
        WebUtil.write(response, rst);
        return null;
    }

    private List<ListParam> getAllFundMan() {
        List<ListParam> list = new ArrayList<ListParam>();
        List<FundManModel> fundManList = highProductParamConfService.getAllManagerInfo();
        if (!CollectionUtils.isEmpty(fundManList)) {
            ListParam obj = null;
            for (FundManModel model : fundManList) {
                obj = new ListParam();
                obj.setCode(model.getFundManCode());
                obj.setName(model.getFundManName());
                list.add(obj);
            }
        }
        return list;
    }

    /**
     *
     * 获取所有有效高端产品列表
     * @return
     */
    private List<ListParam> getAllHighProductList(String productChannel){
        List<ListParam> list = new ArrayList<ListParam>(16);
        // 高端产品列表
        List<HighProductBaseInfoModel> highProductList = new ArrayList<HighProductBaseInfoModel>();
        // 默认每页记录数
        int initPageSize = 500;
        // 初始查询页
        int initPageNum = 1;
        long currSize = 0L;
        // 查询高端产品
        // 第-页
        HighProductBaseInfoPageModel highProductBaseInfoPageModel = highProductService.getHighProductBaseInfo(productChannel,
                null, null, null, null, initPageNum, initPageSize);

        if(highProductBaseInfoPageModel != null){
            long total = highProductBaseInfoPageModel.getSumNum();
            currSize = highProductBaseInfoPageModel.getHighProductBaseInfoModelList() == null ? 0 :
                    highProductBaseInfoPageModel.getHighProductBaseInfoModelList().size();// 当前记录数
            if(total <= initPageSize){
                highProductList = highProductBaseInfoPageModel.getHighProductBaseInfoModelList();
            }else{
                highProductList.addAll(highProductBaseInfoPageModel.getHighProductBaseInfoModelList());
                while(total > currSize){
                    // 下一页
                    HighProductBaseInfoPageModel nextHighProductBaseInfoPageModel = highProductService.getHighProductBaseInfo(null,
                            null, null, null, null, ++initPageNum, initPageSize);
                    if(nextHighProductBaseInfoPageModel != null
                            && !CollectionUtils.isEmpty(nextHighProductBaseInfoPageModel.getHighProductBaseInfoModelList())){
                        highProductList.addAll(nextHighProductBaseInfoPageModel.getHighProductBaseInfoModelList());
                        currSize += nextHighProductBaseInfoPageModel.getHighProductBaseInfoModelList().size();

                    }
                }

            }
        }
        ListParam obj = null;
        for (HighProductBaseInfoModel model : highProductList) {
            obj = new ListParam();
            obj.setCode(model.getFundCode());
            obj.setName(model.getFundAttr());
            list.add(obj);
        }
        return list;
    }

    private List<ListParam> getAllTa() {
        List<ListParam> list = new ArrayList<ListParam>();
        List<TaInfoModel> taList =  queryTaInfoService.getAllTaInfoModel();
        if (!CollectionUtils.isEmpty(taList)) {
            ListParam obj = null;
            for (TaInfoModel model : taList) {
                obj = new ListParam();
                obj.setCode(model.getTaCode());
                obj.setName(model.getTaName());
                list.add(obj);
            }
        }
        return list;
    }


    static class ListParam {
        private String code;
        
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
