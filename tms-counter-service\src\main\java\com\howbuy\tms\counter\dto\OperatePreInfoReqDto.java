package com.howbuy.tms.counter.dto;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;

public class OperatePreInfoReqDto extends OperInfoBaseDto {
    private static final long serialVersionUID = -7229484372650585840L;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预约单标识", max = 100)
    private String subscriptionId;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预约单号(crm)", max = 100)
    private String serialNo;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "备注", max = 100)
    private String message;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "是否忽略校验", max = 100)
    private String ignoreValidation = "false";
    /**
     * 预约类型
     */
    private String preType;
    /**
     * 回款协议方式
     */
    private String collectProtocolMethod;

    public String getCollectProtocolMethod() {
        return collectProtocolMethod;
    }

    public void setCollectProtocolMethod(String collectProtocolMethod) {
        this.collectProtocolMethod = collectProtocolMethod;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getIgnoreValidation() {
        return ignoreValidation;
    }

    public void setIgnoreValidation(String ignoreValidation) {
        this.ignoreValidation = ignoreValidation;
    }

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }
}
