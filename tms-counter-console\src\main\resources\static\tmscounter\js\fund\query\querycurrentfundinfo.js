/***
*基金信息
*<AUTHOR>
*@date 2017-07-03 10:39
 */

$(function(){
	QueryCurrentFundInfo.fundInfo ={};
	QueryCurrentFundInfo.fundInfoList =[];
	QueryCurrentFundInfo.tfundInfo = {};
});

$('html').delegate('.searchCurrentIcon', 'click blur', function () {
	const fundCode = $(this).parent().parent().children().find('#fundCode').val();
	if(fundCode == ""){
		CommonUtil.layer_tip("没有查询到此产品");
		return ;
	}
	const data = QueryCurrentFundInfo.queryCurrentFundInfo(fundCode)
	if (data != null) {
		$(this).parent().parent().find('#fundStatus').html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, data.fundStat));
		$(this).parent().parent().find('#fundName').html(data.fundAttr);
		$(this).parent().parent().find('#taCode').html(data.taCode);
		var taCode = data.taCode;
		if(taCode == '--'){
			elementTaCode.html("--");
		}else{
			var infoData = QueryCurrentFundInfo.queryFundTxAcctNo(taCode);
			var fundAcctNo = "";
			var fundTxAcctNo = "";
			if(infoData != null){
				fundAcctNo = infoData.body.fundAcctNo || '-';
				fundTxAcctNo = infoData.body.fundTxAcctNo || '';
			}
			if(CommonUtil.isEmpty(fundTxAcctNo)){
				fundAcctNo = "--"
				fundTxAcctNo = "--"
			}
			$(this).parent().parent().find('#fundAcctNo').html(fundAcctNo);
			$(this).parent().parent().find('#fundTxAcctNo').html(fundTxAcctNo);
		}
	} else {
		CommonUtil.layer_tip("没有查询到此产品");
	}

}),
	$('html').delegate('.selectBank', 'change', function(){
		var cpAcctNo =$(this).find('option:selected').attr('value');
		var elementTaCode =  $(this).parent().parent().siblings().eq(-3);
		var taCode = elementTaCode.html();
		if(taCode == '--'){
			elementTaCode.html("--");
		}else{
			var infoData = QueryCurrentFundInfo.queryFundTxAcctNo(taCode);
			var fundAcctNo = "";
			var fundTxAcctNo = "";
			if(infoData != null){
				fundAcctNo = infoData.body.fundAcctNo || '-';
				fundTxAcctNo = infoData.body.fundTxAcctNo || '';
			}
			if(CommonUtil.isEmpty(fundTxAcctNo)){
				fundAcctNo = "--"
				fundTxAcctNo = "--"
			}
			$(this).parent().parent().siblings().eq(-2).html(fundAcctNo);
			$(this).parent().parent().siblings().last().html(fundTxAcctNo);
		}
})

var QueryCurrentFundInfo ={
		/**
		 * 查询基金信息
		 *
		 * fundCode 基金代码 可传可不传
		 */
		queryFundInfo:function(fundCode){
			if(!fundCode){
				fundCode = $("#fundCode").val();
			}

			var uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
			var reqparamters = {"fundCode":fundCode};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCurrentFundInfo.queryFundInfoCallBack);
			var cpAcctNo =$(this).find('option:selected').attr('value');
			var elementTaCode =  $(this).parent().parent().siblings().eq(-3);
			var taCode = elementTaCode.html();
			if(cpAcctNo == null || taCode == '--'){
				elementTaCode.html("--");
			}else{
				var infoData = QueryCurrentFundInfo.queryFundTxAcctNo(taCode);
				var fundAcctNo = "";
				var fundTxAcctNo = "";
				if(infoData != null){
					fundAcctNo = infoData.body.fundAcctNo;
					fundTxAcctNo = infoData.body.fundTxAcctNo;
				}
				$(this).parent().parent().siblings().eq(-2).html(fundAcctNo);
				$(this).parent().parent().siblings().last().html(fundTxAcctNo);
			}
		},


	   /**
		 * 查询基金信息
		 *
		 * fundCode 基金代码 可传可不传
		 */
		queryCurrentFundInfo:function(fundCode){
		   //var uri= "http://counter.it48.k8s.howbuy.com/tms-counter-console/tmscounter/queryfundinfo.htm" ||  {};
			var uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
			var reqparamters = {"fundCode":fundCode};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		    var inData = CommonUtil.getCurrentFundInfoData(paramters);
		    var currentInfo = QueryCurrentFundInfo.getCurrentFundInfoShow(inData);
		    var fundInfoList = QueryCurrentFundInfo.fundInfoList || [];
		    fundInfoList.push(currentInfo);
		   QueryCurrentFundInfo.fundInfoList = fundInfoList;
			return currentInfo;
		},

	   /**
		 * 查询默认基金账号
		 *
		 * fundCode 基金代码 可传可不传
		 */
		queryFundTxAcctNo:function(taCode){
		   var custNo = QueryCurrentCustInfo.custInfo.custNo || '';
		   var disCode = QueryCurrentCustInfo.custInfo.disCode || '';

			var uri= TmsCounterConfig.QUERY_FUND_TX_ACCT_NO ||  {};
			var reqparamters = {"custNo":custNo, "disCode":disCode, "taCode":taCode};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
			var infoData = CommonUtil.getCurrentFundInfoData(paramters);
		    return infoData;
		},


		queryFundTxAcctNoCallBack:function () {
			var bodyData = data.body || {};
			var fundTxAcctNo = bodyData.fundTxAcctNo || {};
			return fundTxAcctNo;
		},

		/**
		 * 处理基金信息
		 */
		queryFundInfoCallBack:function(data){

			var bodyData = data.body || {};
			var fundInfo = bodyData.fundInfo || {};
			QueryCurrentFundInfo.fundInfo = fundInfo;

			var isCommonFund = QueryCurrentFundInfo.checkFundInfo(fundInfo);

			if(!isCommonFund){
				return false;
			}

			if($("#fundName").length > 0){
				$("#fundName").html(fundInfo.fundAttr || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, fundInfo.fundRiskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundInfo.fundStat));
			}
		},


		/**
		 * 处理基金信息
		 */
		getCurrentFundInfoShow:function(data){

			var bodyData = data.body || {};
			var fundInfo = bodyData.fundInfo || {};
			QueryCurrentFundInfo.fundInfo = fundInfo;

			var isCommonFund = QueryCurrentFundInfo.checkFundInfo(fundInfo);

			if(!isCommonFund){
				return false;
			}
			return fundInfo;

		},

		queryTFundInfo:function(tFundCode){
			if(!tFundCode){
				tFundCode = $("#tFundCode").val();
			}
			var  uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
			var reqparamters = {"fundCode":tFundCode};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCurrentFundInfo.queryTFundInfoCallBack);
		},

		queryTFundInfoCallBack:function(data){

			var bodyData = data.body || {};
			QueryCurrentFundInfo.tfundInfo = bodyData.fundInfo || {};
			QueryCurrentFundInfo.checkFundInfo(QueryCurrentFundInfo.tfundInfo);

			if($("#tFundName").length > 0){
				$("#tFundName").html(QueryCurrentFundInfo.tfundInfo.fundAttr || '');
			}

			if($("#tFundRiskLevel").length > 0){
				$("#tFundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, QueryCurrentFundInfo.tfundInfo.fundRiskLevel, ''));
			}

			if($("#tFundStatus").length > 0){
				$("#tFundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, QueryCurrentFundInfo.tfundInfo.fundStat));
			}
		},

	// 全选
	selectAll: function () {
		var selectBox = $("input[class='selectBox'][type='checkbox']");
		if($("input[id='selectAll'][type='checkbox']:checked").length == 1){
			selectBox.each(function(index,element){
				$(element).prop("checked",true);
			});
		}else{
			selectBox.each(function(index,element){
				$(element).prop("checked",false);
			});
		}
	},


	addItem: function () {
		    let fundAcctHtml = `<tr class="text-c rowData"><td> <input class="selectBox" name="selectBox" type="checkbox" isdtl="1"/> </td>n" +
			<td class="searchIn"><input type="text" class ="fundCode" id="fundCode" placeholder="请输入"><a href="javascript:void(0)" class="searchCurrentIcon" ></a></td>n" +
			<td class = "fundName" id ="fundName">--</td>	
			<td class = "fundStatus" id ="fundStatus">--</td>
			<td><span class="select-box inline"> <select name="cpAcctNo" class="select selectBank" id="selectBank" isnull="false" datatype="s" errormsg="银行卡号"> <option value="">请选择</option> </select>  </span> </td>
			<td><input type="text"  placeholder="请输入" id="originalAppDealNo" name="originalAppDealNo"/></td>
			<td><input type="text" placeholder="请输入" id="inAppVol" name="inAppVol" onkeyup="if(!/^[0-9]+.?[0-9]{0,2}$/.test(this.value)){layer.alert('只能输入数字且小数点后两位');this.value='';}" isnull="false" datatype="s" errormsg="转入份额"/></td>
			<td class="taCode" name = "taCode"  id ="taCode">--</td>
			<td class="fundAcctNo" name = "fundAcctNo"  id ="fundAcctNo">--</td>
			<td class="fundTxAcctNo" name = "fundTxAcctNo"  id ="fundTxAcctNo">--</td>
			</tr>`
			$("#projectTable tbody").append(fundAcctHtml);
			$(".selectBank").last().html($($(".selectBank").parent().parent()[0]).find(".selectBank").html());

			$("#selectAll").prop("checked",false);
	},


	removeItem: function () {
		// 全选
		var selected = $("input[class='selectBox'][type='checkbox']:checked");
			if (selected.length == 0) {
				CommonUtil.layer_tip("请选择需要删除的数据");
			} else {
				$("#selectAll").prop("checked",false);
				selected.each(function (index, element) {
				var fundCode = $(element).find("#fundCode").html();
				var sameSetNode = $("#dtlForms input:checked").parent().parent();
				$(sameSetNode).each(function () {
					while (true) {
						if ($(this).next("tr[isdtl=1]").size() > 0) {
							$(this).next("tr[isdtl=1]").remove();
							continue;
						}
						break;
					}
					$(this).remove();
				});
			});
		}
	},

		checkFundInfo:function(fundInfo){
			if(CommonUtil.isEmpty(fundInfo.fundCode)){
				CommonUtil.layer_tip("没有查询到此产品");
				return false;
			}

			if('5' == fundInfo.fundStat || '4' == fundInfo.fundStat){
//				CommonUtil.layer_tip("此产品不开放认申购");
			}

			if("7" == fundInfo.fundType || '9' == fundInfo.fundType){
				CommonUtil.layer_tip("该基金不是普通公募基金");
				return false;
			}
			// 去除理财型基金限制
			// if("2" == fundInfo.fundType &&  '21'== fundInfo.fundSubType){
			// 	CommonUtil.layer_tip("该基金不是普通公募基金");
			// 	return false;
			// }

			return true;
		},


		/**
		 * 查询客户持仓
		 */
		queryCustHodlInfo:function(order){
			var uri= TmsCounterConfig.FUND_QUERY_CUST_HODL_INFO_URL ||  {};
			var custNo = QueryCustInfo.custInfo.custNo || '';
			var disCode = QueryCustInfo.custInfo.disCode || '';
			if(isEmpty(custNo)){
				CommonUtil.layer_tip("请先选择用户");
				return false;
			}
			var fundCode ='';
			var appDt ='';
			var appTm ='';
			var protocolType ='';
			if(!order){
				fundCode = $("#fundCode").val();
				appDt = $("#appDt").val();
				appTm = $("#appTm").val();
			}else {
				appDt = order.appDt;
				appTm = order.appTm;
				fundCode = order.fundCode;
				protocolType = order.protocolType;
			}
			var reqparamters = {'protocolType':protocolType,'appDt':appDt,'appTm':appTm,"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCurrentFundInfo.queryCustHoldFundInfoCallBack);
		},

		/**
		 * 处理基金持仓信息
		 */
		queryCustHoldFundInfoCallBack:function(data){
			var bodyData = data.body || {};
			var dtlResp = bodyData || {};
			QueryCurrentFundInfo.fundHold = dtlResp || {};
			QueryCurrentFundInfo.dtlList = dtlResp.balanceDtlList || [];
			//console.log(QueryFundInfo.dtlList);

			if(QueryCurrentFundInfo.dtlList.length <=0){
				CommonUtil.layer_tip("没有查询到持仓信息");
			}

			var selectHtml ='';
			$(QueryCurrentFundInfo.dtlList).each(function(index,element){
				selectHtml +='<option indexnum ="'+index+'" value="'+element.cpAcctNo+'">'+element.bankName+''+element.bankAcctNo+' </option>';
			});

			$("#selectBank").html(selectHtml);
			$("#selectBank").change(function(){
				var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
				var selectDtl = QueryCurrentFundInfo.dtlList[indexNum] || {} ;
				$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
				if($("#bankCode").length > 0){
					$("#bankCode").val(selectDtl.bankCode);
				}
			});

			$("#selectBank").change();

			if(QueryCurrentFundInfo.dtlList.length >0){
				var selectDtl = QueryCurrentFundInfo.dtlList[0] || {} ;
				$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
			}
		},

		queryDiscount:function(){
			var uri= TmsCounterConfig.FUND_CAL_DISCOUNT_RATE_URL ||  {};

			var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
			if(CommonUtil.isEmpty(custInfoForm)){
				showMsg("请先选择客户信息，以计算费率");
				return false;
			}

			var fundCode = $("#fundCode").val();
			if(CommonUtil.isEmpty(fundCode)){
				CommonUtil.layer_tip("请先选择基金");
				return false;
			}
			var bankCode = $('#selectBank').find('option:selected').attr('bankCode');
			if(CommonUtil.isEmpty(bankCode) && QueryCustInfo.custBanks.length > 0){
				bankCode = QueryCustInfo.custBanks[0].bankCode;
			}

			var reqparamters = {};
			reqparamters.custInfoForm = custInfoForm;
			reqparamters.fundCode = fundCode;
			reqparamters.bankCode = bankCode;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
			CommonUtil.ajaxAndCallBack(paramters, QueryCurrentFundInfo.queryDiscountCallBack);
		},

		queryDiscountCallBack:function(data){
			var bodyData = data.body || {};
			var respData = bodyData.respData || [];
			$("#discountRate").val(respData.discountRate);
		}
}