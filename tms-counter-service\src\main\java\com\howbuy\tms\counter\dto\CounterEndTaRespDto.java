/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;
import java.util.List;

/**
 * 
 * @description:(柜台收市信息返回)
 * <AUTHOR>
 * @date 2018年12月14日 下午3:11:11
 * @since JDK 1.6
 */
public class CounterEndTaRespDto extends BaseResponseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -8279119154197510773L;

    /**
     * 总计TA数
     */
    private Integer totalTaNum;

    /**
     * 未收市TA
     */
    private Integer nonEndTaNum;

    /**
     * 已收市TA
     */
    private Integer succEndTaNum;

    /**
     * 不收市TA列表
     */
    private List<FundTaInfoDto> notEndTaList;

    /**
     * 未处理收市TA列表
     */
    private List<FundTaInfoDto> notProcessTaList;
    
    public Integer getTotalTaNum() {
        return totalTaNum;
    }

    public void setTotalTaNum(Integer totalTaNum) {
        this.totalTaNum = totalTaNum;
    }

    public Integer getNonEndTaNum() {
        return nonEndTaNum;
    }

    public void setNonEndTaNum(Integer nonEndTaNum) {
        this.nonEndTaNum = nonEndTaNum;
    }

    public Integer getSuccEndTaNum() {
        return succEndTaNum;
    }

    public void setSuccEndTaNum(Integer succEndTaNum) {
        this.succEndTaNum = succEndTaNum;
    }

    public List<FundTaInfoDto> getNotEndTaList() {
        return notEndTaList;
    }

    public void setNotEndTaList(List<FundTaInfoDto> notEndTaList) {
        this.notEndTaList = notEndTaList;
    }

    public List<FundTaInfoDto> getNotProcessTaList() {
        return notProcessTaList;
    }

    public void setNotProcessTaList(List<FundTaInfoDto> notProcessTaList) {
        this.notProcessTaList = notProcessTaList;
    }
}

