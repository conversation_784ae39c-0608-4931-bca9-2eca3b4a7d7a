/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseRequestDto;

/**
 * @description:(查询客户银行卡信息请求) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月31日 下午3:01:46
 * @since JDK 1.6
 */
public class QueryCustBankCardReqDto extends BaseRequestDto{
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 5905359330504555208L;
    /**
     * 交易账号
     */
    private String txAccNo;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    public String getTxAccNo() {
        return txAccNo;
    }
    public void setTxAccNo(String txAccNo) {
        this.txAccNo = txAccNo;
    }
    public String getCpAcctNo() {
        return cpAcctNo;
    }
    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }
    
    
}

