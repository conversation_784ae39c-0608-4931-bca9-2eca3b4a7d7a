/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundservice.trade;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.howbuy.tms.common.enums.database.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.crstin.QureyCrsTinInfoRequest;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.IndiTaxResidTypeEnum;
import com.howbuy.tms.common.enums.busi.TxPasswordValidateFlagEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustcrsinfo.QueryAllCustCrsInfoOutService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustcrsinfo.QueryAllCustCrsInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustcrsinfo.bean.AcCustIndiTaxInfoModel;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustcrsinfo.bean.TaxesModel;
import com.howbuy.tms.common.outerservice.acccenter.querycrstin.QureyCrsTinInfoOutService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.BizCounterEnum;
import com.howbuy.tms.counter.common.BooleanFlagReverseEnum;
import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.UUIDUtil;
import com.howbuy.tms.counter.dto.CounterCancelReqDto;
import com.howbuy.tms.counter.dto.CounterExchangeReqDto;
import com.howbuy.tms.counter.dto.CounterModifyDivReqDto;
import com.howbuy.tms.counter.dto.CounterPurchaseReqDto;
import com.howbuy.tms.counter.dto.CounterRedeemReqDto;
import com.howbuy.tms.counter.dto.CounterShareMergeVolReqDto;
import com.howbuy.tms.counter.dto.CounterShareTransferVolReqDto;
import com.howbuy.tms.counter.dto.CounterTransferTubeReqDto;
import com.howbuy.tms.counter.dto.ShareMergeVolOrderReqDto;
import com.howbuy.tms.counter.dto.TransferTubeOrderReqDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfervalidate.HighShareTransferValidateFacade;
import com.howbuy.tms.high.orders.facade.trade.sharetransfer.sharetransfervalidate.HighShareTransferValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.cancelordervalidate.CancelOrderValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.cancelordervalidate.CancelOrderValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.exchange.exchangevalidate.ExChangeValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.exchange.exchangevalidate.ExChangeValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivvalidate.ModifyDivValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivvalidate.ModifyDivValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.redeem.redeemvalidate.RedeemValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.redeem.redeemvalidate.RedeemValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.BaseShareMergeRequest.ShareMergeOutDetail;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergevalidate.ShareMergeValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergevalidate.ShareMergeValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfervalidate.ShareTransferValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfervalidate.ShareTransferValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurvalidate.SubsOrPurValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurvalidate.SubsOrPurValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.transfertubein.validate.TransManageInValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.transfertubein.validate.TransManageInValidateRequest;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.BaseTransManageOutRequest.OutDetail;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.validate.TransManageOutValidateFacade;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.validate.TransManageOutValidateRequest;
import com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.CounterPurchaseAdviserFacadeValidateFacade;
import com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.CounterPurchaseAdviserValidateRequest;
import com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.CounterRedeemAdviserValidateFacade;
import com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.CounterRedeemAdviserValidateRequest;
import com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.RedeemPortfolioAdviserResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 
 * @description:(零售柜台交易审核下单校验)
 * <AUTHOR>
 * @date 2017年9月15日 下午5:32:39
 * @since JDK 1.6
 */
@Service("tmsFundCounterValidService")
public class TmsFundCounterValidServiceImpl implements TmsFundCounterValidService {
    private static Logger logger = LogManager.getLogger(TmsFundCounterValidServiceImpl.class);

    @Autowired
    private SubsOrPurValidateFacade subsOrPurValidateFacade;

    @Autowired
    private RedeemValidateFacade redeemValidateFacade;

    @Autowired
    private CounterRedeemAdviserValidateFacade counterRedeemAdviserValidateFacade;
    @Autowired
    private CancelOrderValidateFacade cancelOrderValidateFacade;

    @Autowired
    private ModifyDivValidateFacade modifyDivValidateFacade;

    @Autowired
    private ExChangeValidateFacade exChangeValidateFacade;
    
    @Autowired
    private ShareMergeValidateFacade fundShareMergeValidateFacade;
    
    @Autowired
    private ShareTransferValidateFacade fundShareTransferValidateFacade;
    
    @Autowired
    private HighShareTransferValidateFacade highShareTransferValidateFacade;
    
    @Autowired
    private TransManageInValidateFacade transManageInValidateFacade;
    
    @Autowired
    private TransManageOutValidateFacade transManageOutValidateFacade;

    @Autowired
    private CounterPurchaseAdviserFacadeValidateFacade counterPurchaseAdviserFacadeValidateFacade;

    @Autowired
    @Qualifier("qureyCrsTinInfoOutService")
    private QureyCrsTinInfoOutService qureyCrsTinInfoOutService;

    @Autowired
    @Qualifier("queryCustInfoOuterService")
    private QueryCustInfoOuterService queryCustInfoOuterService;

    @Autowired
    @Qualifier("queryAllCustCrsInfoOutService")
    private QueryAllCustCrsInfoOutService queryAllCustCrsInfoOutService;




    @Override
    public boolean subsOrPurValidate(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception {

        //校验九要素
        if(dto != null) {
            validateCustInfo(dto.getTxAcctNo(), dto.getDisCode());
        }

        SubsOrPurValidateRequest request = new SubsOrPurValidateRequest();
        if (dto != null) {
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            request.setCpAcctNo(dto.getCpAcctNo());
            request.setPaymentType(dto.getPaymentType());
            request.setAppAmt(dto.getAppAmt());
            request.setRiskFlag(dto.getRiskFlag());
            request.setFundCode(dto.getFundCode());
            request.setFundShareClass(dto.getFundShareClass());
            request.setProtocolType(dto.getProtocolType());
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            // 协议号
            request.setProtocolNo(null);
            request.setFundShareClass(dto.getFundShareClass());
            request.setDiscount(dto.getDiscountRate());
            logger.debug("Fund | subsOrPurValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(subsOrPurValidateFacade, request, disInfoDto);
        return true;

    }

    /**
     *
     * validateCustInfo:校验九要素
     * @param txAcctNo
     * @param disCode
     * <AUTHOR>
     * @date 2024年6月25日 上午10:05:53
     */
    private void validateCustInfo(String txAcctNo, String disCode) {
        QueryCustInfoResult queryCustInfoResult = queryCustInfoOuterService.queryCustInfoByDisCode(txAcctNo, disCode);

        // 只有个人用户校验反洗钱
        if(!InvstTypeEnum.INDI.getCode().equals(queryCustInfoResult.getInvstType())){
            return ;
        }

        String custInfoCompleteStat = YesOrNoEnum.YES.getCode();
        if(YesOrNoEnum.YES.getCode().equals(queryCustInfoResult.getCustInfoCompleteStat()) && !StringUtil.isEmpty(queryCustInfoResult.getIncLevel())){
            QueryAllCustCrsInfoResult result = queryAllCustCrsInfoOutService.queryAllCustCrsInfo(txAcctNo, disCode);
            custInfoCompleteStat = validateCustCrs(result, disCode);
        } else {
            custInfoCompleteStat = YesOrNoEnum.NO.getCode();
        }

        //九要素信息不全
        if(!YesOrNoEnum.YES.getCode().equals(custInfoCompleteStat)) {
            throw new TmsCounterException(TmsCounterResultEnum.CUST_ORDER_INFO_NULL);
        }

    }

    private String validateCustCrs(QueryAllCustCrsInfoResult result, String disCode) {
        String completeStat = YesOrNoEnum.NO.getCode();
        List<AcCustIndiTaxInfoModel> indiTaxList = result.getIndiTaxList();
        if (CollectionUtils.isEmpty(indiTaxList)) {
            return completeStat;
        }

        AcCustIndiTaxInfoModel acCustIndiTaxInfoBean = indiTaxList.get(0);
        if (IndiTaxResidTypeEnum.CHIAN_TAX_RESIDENT.getCode().equals(acCustIndiTaxInfoBean.getIndiTaxResidType())) {
            return YesOrNoEnum.YES.getCode();
        }


        // 除中国税收居民校验 2：非居民、3：既是中国税收居民又是其他国家税收居民
        String type = acCustIndiTaxInfoBean.getIndiTaxResidType();
        String firstName = acCustIndiTaxInfoBean.getIndiTaxFirstName();
        String secondName = acCustIndiTaxInfoBean.getIndiTaxSecondName();
        String chinCountry = acCustIndiTaxInfoBean.getIndiTaxChinCountry();
        String chinProvince = acCustIndiTaxInfoBean.getIndiTaxChinProvince();
        String chinCity = acCustIndiTaxInfoBean.getIndiTaxChinCity();
        String foreignCountry = acCustIndiTaxInfoBean.getIndiTaxForeigCountry();
        String foreignCity = acCustIndiTaxInfoBean.getIndiTaxForeigCity();

        JSONArray jsonArray = JSONArray.fromObject(acCustIndiTaxInfoBean.getIndiTaxesInfo());
        JSONObject jsonObject = JSONObject.fromObject(jsonArray.get(0));
        TaxesModel taxesModel = (TaxesModel) JSONObject.toBean(jsonObject, TaxesModel.class);
        //税收居民国
        String taxCountry = "";
        //纳税人识别号
        String indiTaxIdentNum = "";
        //不能提供识别号原因
        String indiTaxUnProvide = "";
        //具体原因
        String indiTaxUnReason = "";

        if (taxesModel != null) {
            taxCountry = taxesModel.getTaxCountry();
            indiTaxIdentNum = taxesModel.getTaxIdentNum();
            indiTaxUnProvide = taxesModel.getTaxUnProvide();
            indiTaxUnReason = taxesModel.getTaxUnReason();
        }

        //税收居民国为空
        if (StringUtil.isEmpty(taxCountry)) {
            return YesOrNoEnum.NO.getCode();
        }

        //是否符合tin规则
        String fitCrsTinInfo = qureyCrsTinInfoOutService.qureyCrsTinInfo(taxCountry, disCode);
        boolean fitTaxUnProvide = fitTaxUnProvide(indiTaxIdentNum, indiTaxUnProvide, indiTaxUnReason, fitCrsTinInfo);
        completeStat = YesOrNoEnum.NO.getCode();
        //用户税收信息是否完整
        if (!StringUtil.isEmpty(type) && nameNotEmpty(firstName, secondName) && countryAndCityNotEmpty(chinCountry, chinProvince,
                chinCity, foreignCountry, foreignCity) && fitTaxUnProvide) {
            completeStat = YesOrNoEnum.YES.getCode();
        }
        return completeStat;
    }

    private boolean fitTaxUnProvide(String indiTaxIdentNum, String indiTaxUnProvide, String indiTaxUnReason, String fitCrsTinInfo) {
        boolean fitTaxUnProvide = true;
        if (YesOrNoEnum.YES.getCode().equals(fitCrsTinInfo)) {
            if (StringUtil.isEmpty(indiTaxIdentNum)) {
                fitTaxUnProvide = false;
            }
        } else {
            //纳税人识别号为空且未选择无法提供原因
            if (StringUtil.isEmpty(indiTaxIdentNum)) {
                if (StringUtil.isEmpty(indiTaxUnProvide)) {
                    fitTaxUnProvide = false;
                } else {
                    //未填识别号，且未选原因
                    if (!TradeConstant.PROVIDE_NULL_CODE.equals(indiTaxUnProvide) && !TradeConstant.GET_NULL_CODE.equals(indiTaxUnProvide)) {
                        fitTaxUnProvide = false;
                        //原因为未获得纳税人识别号且具体原因为空
                    } else if (TradeConstant.GET_NULL_CODE.equals(indiTaxUnProvide) && StringUtil.isEmpty(indiTaxUnReason)) {
                        fitTaxUnProvide = false;
                    }
                }
            }
        }
        return fitTaxUnProvide;
    }

    @Override
    public boolean adviserPurValidate(CounterPurchaseReqDto dto, DisInfoDto disInfoDto) throws Exception {

        //校验九要素
        if(dto != null) {
            validateCustInfo(dto.getTxAcctNo(), dto.getDisCode());
        }


        CounterPurchaseAdviserValidateRequest request = new CounterPurchaseAdviserValidateRequest();
        if (dto != null) {
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            request.setCpAcctNo(dto.getCpAcctNo());
            request.setPaymentType(dto.getPaymentType());
            request.setAppAmt(dto.getAppAmt());
            request.setRiskFlag(dto.getRiskFlag());
            request.setProtocolType(ProtocolTypeEnum.ADVISER_PORTFOLIFO.getCode());
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setProductCode(dto.getFundCode());
            // 协议号
            request.setProtocolNo(null);
            request.setzBusiCode(ZBusiCodeEnum.BUY.getCode());
            request.setZdtlBusiCode(ZDtlBusiCodeEnum.PURCHASE.getMCode());
            request.setPartnerCode(dto.getPartnerCode());
            request.setTxPasswordValidateFlag(TxPasswordValidateFlagEnum.NOT_REQUIRED_VALIDATE.getCode());

            logger.debug("Fund | counterPurchaseAdviserFacadeValidateFacade requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(counterPurchaseAdviserFacadeValidateFacade, request, disInfoDto);
        return true;

    }


    @Override
    public boolean redeemValidate(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception {
        RedeemValidateRequest request = new RedeemValidateRequest();
        if (dto != null) {
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(dto.getCpAcctNo());
            // 基金代码
            request.setFundCode(dto.getFundCode());
            request.setFundShareClass(dto.getFundShareClass());
            // 份额类型：A-前收费；B-后收费
            request.setFundShareClass(dto.getFundShareClass());
            // 协议类型
            request.setProtocolType(dto.getProtocolType());
            // 协议号
            request.setProtocolNo(dto.getProtocolNo());
            // 申请份额
            request.setAppVol(dto.getAppVol());
            request.setLargeRedeemFlag(dto.getLargeRedmFlag());
            // 赎回去向 0-赎回到银行卡, 1-赎回到储蓄罐, 2-用户选择银行卡, 3-用户选择储蓄罐
            // 默认1, 储蓄罐
            request.setRedeemCapitalFlag(StringUtils.isEmpty(dto.getRedeemCapitalFlag())  ? "1" : dto.getRedeemCapitalFlag());
            //全赎标识
            request.setAllRedeemFlag(dto.getAllRedeemFlag());
            //理财型基金，赎回日期
            request.setAllowRedeemDt(dto.getOpenRedeDt());
            logger.debug("Fund | redeemValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(redeemValidateFacade, request, disInfoDto);
        return true;
    }

    @Override
    public boolean redeemAdviserValidate(CounterRedeemReqDto dto, DisInfoDto disInfoDto) throws Exception {
        CounterRedeemAdviserValidateRequest request = new CounterRedeemAdviserValidateRequest();
        if (dto != null) {
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(dto.getCpAcctNo());
            // 基金代码
            request.setProductCode(dto.getFundCode());
            // 协议号
            request.setProtocolNo(dto.getProtocolNo());
            // 申请份额
            request.setAppRatio(MathUtils.bigDecimalToString(dto.getAppRatio()));
            // 赎回去向
            request.setRedeemDirection(StringUtils.isEmpty(dto.getRedeemCapitalFlag())  ? "1" : dto.getRedeemCapitalFlag());
            // 商户号
            request.setPartnerCode(dto.getPartnerCode());
            request.setTxPasswordValidateFlag(YesOrNoEnum.YES.getCode());
            logger.debug("adviser | redeemAdviserValidate request:{}", JSON.toJSONString(request));
        }

        TmsFacadeUtil.doFillBaseRequest(request, disInfoDto);
        RedeemPortfolioAdviserResponse response = counterRedeemAdviserValidateFacade.execute(request);
        if (response == null){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD);
        }
        String code = response.getReturnCode();
        String desc = response.getDescription();
        if (TmsFacadeUtil.isSucc(ReturnCodeEnum.SUCC_TMS.getCode(), code)
                || TmsFacadeUtil.isSucc(ReturnCodeEnum.SUCC_NEW.getCode(), code)) {
            return true;
        }

        throwsTmsCounterException(code, response);

        throw new TmsCounterException(code, desc);
    }

    private static void throwsTmsCounterException(String code, RedeemPortfolioAdviserResponse response) {
        if ((StringUtils.equals(code, ExceptionCodes.ADVISER_REDEEM_LE_MIN_RATIO)
                || StringUtils.equals(code, ExceptionCodes.REDEEM_AMT_LT_MIN_AMT))
                && response.getMinRedeemRatio() != null) {
            throw new TmsCounterException(
                    code, String.format("最低赎回比例为【%s】，请重新输入比例 。", MathUtils.bigDecimalMultiply100ToStringAndTwoScale(response.getMinRedeemRatio()) + "%"));
        }else if (StringUtils.equals(code, ExceptionCodes.ADVISER_REDEEM_LE_MIN_RESERVE_RATIO) && response.getMinReserveAmt() != null) {
            throw new TmsCounterException(code, String.format("低于最小保留金额【%s】，请重新输入比例 。", MathUtils.bigDecimalToString(response.getMinReserveAmt())));
        }
    }

    @Override
    public boolean exchangeValidate(CounterExchangeReqDto dto, DisInfoDto disInfoDto) throws Exception {
        ExChangeValidateRequest request = new ExChangeValidateRequest();
        if (dto != null) {
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 资金账号
            request.setCpAcctNo(dto.getCpAcctNo());
            // 申请份额
            request.setAppVol(dto.getAppVol());
            request.setOutFundCode(dto.getFundCode());
            // 份额类型：A-前收费；B-后收费
            request.setOutShareClass(dto.getFundShareClass());
            // 基金代码
            request.setInFundCode(dto.gettFundCode());
            request.setInShareClass(dto.gettFundShareClass());
            request.setRiskFlag(dto.getRiskFlag());
            request.setProtocolType(dto.getProtocolType());
            request.setProtocolNo(dto.getProtocolNo());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setAllowRedeemDt(dto.getOpenRedeDt());
            logger.debug("Fund | exchangeValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(exChangeValidateFacade, request, disInfoDto);
        return true;

    }

    @Override
    public boolean cancelOrderValidate(CounterCancelReqDto dto, DisInfoDto disInfoDto) throws Exception {
        CancelOrderValidateRequest request = new CancelOrderValidateRequest();
        if (dto != null) {
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 订单号
            request.setDealNo(dto.getDealNo());
            // 是否需要校验校验密码(柜台撤单不需要校验校验密码)
            request.setNeedValidateTxPwdFlag(BooleanFlagReverseEnum.NO.getCode());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setCancelFlag(dto.getCancelType());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setTxChannel(TxChannelEnum.COUNTER.getCode());
            // request.setTransactorIdNo(dto.getTransactorIdNo());
            // request.setTransactorIdType(dto.getTransactorIdType());
            // request.setTransactorName(dto.getTransactorName());
            // request.setOperatorNo(dto.getOperatorNo());
            // request.setConsCode(dto.getConsCode());
            logger.info("Fund | cancelOrderValidate requset:{}", JSON.toJSONString(request));
        }
        // 订单层面的 撤单校验， 单基金、投顾组合、批量买入公用
        TmsFacadeUtil.executeThrowException(cancelOrderValidateFacade, request, disInfoDto);
        return true;

    }

    @Override
    public boolean modifyDivValidate(CounterModifyDivReqDto dto, DisInfoDto disInfoDto) throws Exception {
        ModifyDivValidateRequest request = new ModifyDivValidateRequest();
        if (dto != null) {
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(dto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            // 基金代码
            request.setFundCode(dto.getFundCode());
            // request
            request.setFundShareClass(dto.getFundShareClass());
            // 目标基金分红方式
            request.setDivMode(dto.getFundDivMode());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            request.setProtocolNo(dto.getProtocolNo());
            request.setCpAcctNo(dto.getCpAcctNo());

            logger.debug("Fund | modifyDivValidate requset:{}", JSON.toJSONString(request));
        }
        TmsFacadeUtil.executeThrowException(modifyDivValidateFacade, request, disInfoDto);
        return true;

    }

    @Override
    public boolean shareMergeVolValidate(CounterShareMergeVolReqDto dto, DisInfoDto disInfoDto) throws Exception {
        if(dto == null || CollectionUtils.isEmpty(dto.getShareMergeVolOrderList())){
            return false;
        }
        
        ShareMergeValidateRequest request = new ShareMergeValidateRequest();
        request.setTransactorIdNo(dto.getTransactorIdNo());
        request.setTransactorIdType(dto.getTransactorIdType());
        request.setTransactorName(dto.getTransactorName());
        request.setOperatorNo(dto.getOperatorNo());
        request.setConsCode(dto.getConsCode());
        request.setDisCode(dto.getDisCode());
        request.setOutletCode(dto.getOutletCode());
        // 外部订单号
        request.setExternalDealNo(UUIDUtil.uuid());
        // 交易账号
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setAppDt(dto.getAppDt());
        request.setAppTm(dto.getAppTm());
        request.setFundCode(dto.getFundCode());
        request.setFundShareClass(dto.getFundShareClass());
        // 转入资金账号
        request.setInCpAcctNo(dto.getInCpAcctNo());
        request.setInProtocolNo(dto.getInProtocolNo());
        request.setInProtocolType(dto.getInProtocolType());
        
        List<ShareMergeOutDetail> shareMergeOutDetail = new ArrayList<ShareMergeOutDetail>();
        if(CollectionUtils.isNotEmpty(dto.getShareMergeVolOrderList())){
            ShareMergeOutDetail outDetail = null;
            for(ShareMergeVolOrderReqDto reqDto : dto.getShareMergeVolOrderList()){
                outDetail = new ShareMergeOutDetail();
                outDetail.setCpAcctNo(reqDto.getCpAcctNo());
                outDetail.setBankAcct(reqDto.getBankAcct());
                outDetail.setBankCode(reqDto.getBankCode());
                outDetail.setAppVol(reqDto.getAppVol());
                outDetail.setProtocolNo(reqDto.getProtocolNo());
                outDetail.setProtocolType(reqDto.getProtocolType());

                shareMergeOutDetail.add(outDetail);
            }
        }
        request.setShareMergeOutDetail(shareMergeOutDetail);
        logger.debug("Fund | shareMergeVolValidate requset:{}", JSON.toJSONString(request));
   
        TmsFacadeUtil.executeThrowException(fundShareMergeValidateFacade, request, disInfoDto);
        return true;
    }
    
    @Override
    public boolean shareTransferVolValidate(CounterShareTransferVolReqDto dto, String searchDisCode, DisInfoDto disInfoDto) throws Exception {
        if(dto == null || CollectionUtils.isEmpty(dto.getShareMergeVolOrderList())){
            return false;
        }
        
        List<ShareMergeVolOrderReqDto> shareMergeVolOrderList = new ArrayList<ShareMergeVolOrderReqDto>();
        List<ShareMergeVolOrderReqDto> shareMergeVolOrderTempList = dto.getShareMergeVolOrderList();
        for(ShareMergeVolOrderReqDto balDto : shareMergeVolOrderTempList) {
            if(!ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType())
                    && !ProductChannelEnum.PIGGY.getCode().equals(balDto.getProductChannel())
                    //为兼容中台 资金在途资产默认为91
                    && !TradeConstant.CAPITAL_INTRANSIT_PROTOCOL_TYPE.equals(balDto.getProtocolType())) {
                shareMergeVolOrderList.add(balDto);
            }
        }
        if(CollectionUtils.isEmpty(shareMergeVolOrderList)){
            logger.info("Fund | shareMergeVolValidate shareMergeVolOrderList is null");
            return false;
        }
        Map<String, List<ShareMergeVolOrderReqDto>> orderDtlMap = shareMergeVolOrderList.stream().collect(Collectors.groupingBy(ShareMergeVolOrderReqDto::getDisCode));
        int count = 0;
        for (List<ShareMergeVolOrderReqDto> reqDtos : orderDtlMap.values()) {
            ShareMergeVolOrderReqDto reqDto = reqDtos.get(0);
            
            ShareTransferValidateRequest request = new ShareTransferValidateRequest();
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(reqDto.getDisCode());
            if(YesOrNoEnum.YES.getCode().equals(dto.getCancelCard())) {
                //request.setDisCode(DisCodeEnum.HM.getCode());
                request.setSearchDisCode(DefaultParamsConstant.DEFULT_DIS_CODE);
            }
            
            request.setOutletCode(dto.getOutletCode());
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            // 转入资金账号
            request.setInCpAcctNo(dto.getInCpAcctNo());
            // 转出资金账号
            request.setOutCpAcctNos(dto.getOutCpAcctNos());
            request.setCancelCard(dto.getCancelCard());
            logger.debug("Fund | shareTransferVolValidate requset:{}", JSON.toJSONString(request));

            BaseResponse baseResp = TmsFacadeUtil.execute(fundShareTransferValidateFacade, request, disInfoDto);
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                count = count + 1;
            }else{
                if(baseResp != null){
                    //换卡中存在投顾份额，只能选择注销原卡的换卡。请重新操作。
                    if(ExceptionCodes.FUND_SAHRE_TRANSFER_NOT_EXISTS.equals(baseResp.getReturnCode())){
                        throw new TmsCounterException(TmsCounterResultEnum.CHANGE_BANK_ONLY.getCode(), TmsCounterResultEnum.CHANGE_BANK_ONLY.getDesc());
                    }
                    //此分销下银行卡不存在
                    if(ExceptionCodes.BANK_CARD_IS_NULL.equals(baseResp.getReturnCode())){
                        throw new TmsCounterException(TmsCounterResultEnum.CHANGE_BANK_NULL.getCode(), TmsCounterResultEnum.CHANGE_BANK_NULL.getDesc());
                    }
                    throw new TmsCounterException(baseResp.getReturnCode(), "零售公募：" + baseResp.getDescription());
                } 
            }
        }
        
        if (count == orderDtlMap.size()){
            return true;
        }
        throw new TmsCounterException(BizCounterEnum.GONGMU_VAL_ERROR.getCode(), BizCounterEnum.GONGMU_VAL_ERROR.getDesc());
        
    }

    @Override
    public boolean transferTubeInVolValidate(CounterTransferTubeReqDto dto, DisInfoDto disInfoDto) throws TmsCounterException {
        
        if(dto == null || CollectionUtils.isEmpty(dto.getTransferTubeDetailList())){
            return false;
        }
        
        //校验九要素
        validateCustInfo(dto.getTxAcctNo(), dto.getDisCode());

        TransManageInValidateRequest request = new TransManageInValidateRequest();
        request.setTransactorIdNo(dto.getTransactorIdNo());
        request.setTransactorIdType(dto.getTransactorIdType());
        request.setTransactorName(dto.getTransactorName());
        request.setOperatorNo(dto.getOperatorNo());
        request.setConsCode(dto.getConsCode());
        request.setDisCode(dto.getDisCode());
        request.setOutletCode(dto.getOutletCode());
        // 外部订单号
        request.setExternalDealNo(UUIDUtil.uuid());
        // 交易账号
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setAppDt(dto.getAppDt());
        request.setAppTm(dto.getAppTm());
        request.setTransferTubeBusiType(dto.getTransferTubeBusiType());
        request.setFundCode(dto.getFundCode());
        request.setFundShareClass(dto.getFundShareClass());
        request.setTSellerCode(dto.gettSellerCode());
        request.setOriginalAppDealNo(dto.getOriginalAppDealNo());
        
        TransferTubeOrderReqDto dtlDto = dto.getTransferTubeDetailList().get(0);
        request.setInCpAcctNo(dtlDto.getCpAcctNo());
        request.setBankAcct(dtlDto.getBankAcct());
        request.setBankCode(dtlDto.getBankCode());
        request.setAppVol(dtlDto.getAppVol());
        logger.debug("Fund | transManageInValidateFacade requset:{}", JSON.toJSONString(request));
   
        TmsFacadeUtil.executeThrowsException(transManageInValidateFacade, request, disInfoDto);
        return true;
        
    }

    @Override
    public boolean transferTubeOutVolValidate(CounterTransferTubeReqDto dto, DisInfoDto disInfoDto) throws Exception {
        
        if(dto == null || CollectionUtils.isEmpty(dto.getTransferTubeDetailList())){
            return false;
        }
        
        TransManageOutValidateRequest request = new TransManageOutValidateRequest();
        request.setTransactorIdNo(dto.getTransactorIdNo());
        request.setTransactorIdType(dto.getTransactorIdType());
        request.setTransactorName(dto.getTransactorName());
        request.setOperatorNo(dto.getOperatorNo());
        request.setConsCode(dto.getConsCode());
        request.setDisCode(dto.getDisCode());
        request.setOutletCode(dto.getOutletCode());
        // 外部订单号
        request.setExternalDealNo(UUIDUtil.uuid());
        // 交易账号
        request.setTxAcctNo(dto.getTxAcctNo());
        request.setAppDt(dto.getAppDt());
        request.setAppTm(dto.getAppTm());
        request.setFundCode(dto.getFundCode());
        request.setFundShareClass(dto.getFundShareClass());
        request.setTransferTubeBusiType(dto.getTransferTubeBusiType());
        request.setFundCode(dto.getFundCode());
        request.setFundShareClass(dto.getFundShareClass());
        request.setTSellerCode(dto.gettSellerCode());
        request.setTSellerTxAcctNo(dto.gettSellerTxAcctNo());
        //对方网点
        request.setTOutletCode(dto.gettOutletCode());
        
        List<OutDetail> transOutDetail = new ArrayList<OutDetail>();
        if(CollectionUtils.isNotEmpty(dto.getTransferTubeDetailList())){
            OutDetail outDetail = null;
            for(TransferTubeOrderReqDto reqDto : dto.getTransferTubeDetailList()){
                outDetail = new OutDetail();
                outDetail.setCpAcctNo(reqDto.getCpAcctNo());
                outDetail.setBankAcct(reqDto.getBankAcct());
                outDetail.setBankCode(reqDto.getBankCode());
                outDetail.setAppVol(reqDto.getAppVol());
                outDetail.setProtocolNo(reqDto.getProtocolNo());
                outDetail.setProtocolType(reqDto.getProtocolType());

                transOutDetail.add(outDetail);
            }
        }
        request.setOutDetailList(transOutDetail);
        logger.debug("Fund | transferTubeOutVolValidate requset:{}", JSON.toJSONString(request));
   
        TmsFacadeUtil.executeThrowException(transManageOutValidateFacade, request, disInfoDto);
        return true;
        
    }

    @Override
    public boolean highShareTransferVolValidate(CounterShareTransferVolReqDto dto, String searchDisCode, DisInfoDto disInfoDto) throws Exception {
        if(dto == null || CollectionUtils.isEmpty(dto.getShareMergeVolOrderList())){
            return false;
        }
        
        List<ShareMergeVolOrderReqDto> shareMergeVolOrderList = new ArrayList<ShareMergeVolOrderReqDto>();
        List<ShareMergeVolOrderReqDto> shareMergeVolOrderTempList = dto.getShareMergeVolOrderList();
        for(ShareMergeVolOrderReqDto balDto : shareMergeVolOrderTempList) {
            if(ProtocolTypeEnum.HIGH_FUND.getCode().equals(balDto.getProtocolType())) {
                shareMergeVolOrderList.add(balDto);
            }
        }
        if(CollectionUtils.isEmpty(shareMergeVolOrderList)){
            logger.info("Fund | high shareMergeVolOrderList is null");
            return false;
        }
        
        Map<String, List<ShareMergeVolOrderReqDto>> orderDtlMap = shareMergeVolOrderList.stream().collect(Collectors.groupingBy(ShareMergeVolOrderReqDto::getDisCode));

        int count = 0;
        for (List<ShareMergeVolOrderReqDto> reqDtos : orderDtlMap.values()) {
            ShareMergeVolOrderReqDto reqDto = reqDtos.get(0);
            
            HighShareTransferValidateRequest request = new HighShareTransferValidateRequest();
            request.setTransactorIdNo(dto.getTransactorIdNo());
            request.setTransactorIdType(dto.getTransactorIdType());
            request.setTransactorName(dto.getTransactorName());
            request.setOperatorNo(dto.getOperatorNo());
            request.setConsCode(dto.getConsCode());
            request.setDisCode(reqDto.getDisCode());
            request.setOutletCode(dto.getOutletCode());
            // 外部订单号
            request.setExternalDealNo(UUIDUtil.uuid());
            // 交易账号
            request.setTxAcctNo(dto.getTxAcctNo());
            request.setAppDt(dto.getAppDt());
            request.setAppTm(dto.getAppTm());
            
            request.setFundCodes(getHighReqDtos(dto.getHighFundCodes(), reqDtos));
            // 转入资金账号
            request.setInCpAcctNo(dto.getInCpAcctNo());
            // 转出资金账号
            request.setOutCpAcctNo(dto.getOutCpAcctNo());
            request.setOutBankAcct(dto.getOutBankAcct());
            request.setOutBankCode(dto.getOutBankCode());
            logger.debug("highShareTransferVolValidate|requset:{}", JSON.toJSONString(request));

            BaseResponse baseResp = TmsFacadeUtil.execute(highShareTransferValidateFacade, request, disInfoDto);
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                count = count + 1;
            }else{
                if(baseResp != null){
                    //此分销下银行卡不存在
                    if(ExceptionCodes.BANK_CARD_IS_NULL.equals(baseResp.getReturnCode())){
                        throw new TmsCounterException(TmsCounterResultEnum.CHANGE_BANK_NULL.getCode(), TmsCounterResultEnum.CHANGE_BANK_NULL.getDesc());
                    }
                    throw new TmsCounterException(baseResp.getReturnCode(), "高端："+baseResp.getDescription());
                }
            }
        }
        if (count == orderDtlMap.size()){
            return true;
        }
        throw new TmsCounterException(BizCounterEnum.HIGH_VAL_ERROR.getCode(), BizCounterEnum.HIGH_VAL_ERROR.getDesc());
    }

    private List<String> getHighReqDtos(List<String> highFundCodes, List<ShareMergeVolOrderReqDto> reqDtos) {
        List<String> highDtos = new ArrayList<String>();
        if(CollectionUtils.isEmpty(highFundCodes)) {
            return highDtos;
        }
        if(CollectionUtils.isEmpty(reqDtos)) {
            return highDtos;
        }
        for(ShareMergeVolOrderReqDto dto : reqDtos) {
            if(highFundCodes.contains(dto.getFundCode())) {
                highDtos.add(dto.getFundCode());
            }
        }
        return highDtos;
    }


    private boolean nameNotEmpty(String firstName,String secondName){
        return  !StringUtil.isEmpty(firstName) && !StringUtil.isEmpty(secondName);
    }


    private boolean countryAndCityNotEmpty(String chinaCountry,String chinaProvince,String chinaCity,String foreignCountry,String foreignCity){
        return (!StringUtil.isEmpty(chinaCountry) && !StringUtil.isEmpty(chinaProvince) && !StringUtil.isEmpty(chinaCity)) ||
                (!StringUtil.isEmpty(foreignCountry) && !StringUtil.isEmpty(foreignCity));
    }

}
