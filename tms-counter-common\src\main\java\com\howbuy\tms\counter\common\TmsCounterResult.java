/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;


/**
 * @description:(中台柜台响应结果) 
 * <AUTHOR>
 * @date 2017年3月27日 下午5:21:17
 * @since JDK 1.6
 */
public class TmsCounterResult {
    /**
     * 响应码
     */
    private  String code;
    /**
     * 响应描述
     */
    private  String desc;
    /***
     * 响应体
     */
    private  Object body;
    
    public TmsCounterResult(String code,String desc,Object body){
        this.code = code;
        this.desc = desc;
        this.body = body;
    }
    
    public TmsCounterResult(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
    
    public TmsCounterResult(TmsCounterResultEnum tmsCounterResultEnum){
        this.code = tmsCounterResultEnum.getCode();
        this.desc = tmsCounterResultEnum.getDesc();
    }
    

    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
    public Object getBody() {
        return body;
    }
    public void setBody(Object body) {
        this.body = body;
    }
    
}

