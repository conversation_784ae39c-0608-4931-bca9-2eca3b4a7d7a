package com.howbuy.tms.counter.dto;

import java.util.Date;

public class ExchangeCardMaterialDtlDto {
    private String dealDtlAppNo;

    private String dealAppNo;

    private String materialType;

    private String uploadMethod;

    private String fileName;

    private String filePath;

    private String notProvideReason;

    private String checkFlag;

    private Date createDtm;

    private Date updateDtm;

    public String getDealDtlAppNo() {
        return dealDtlAppNo;
    }

    public void setDealDtlAppNo(String dealDtlAppNo) {
        this.dealDtlAppNo = dealDtlAppNo == null ? null : dealDtlAppNo.trim();
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo == null ? null : dealAppNo.trim();
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType == null ? null : materialType.trim();
    }

    public String getUploadMethod() {
        return uploadMethod;
    }

    public void setUploadMethod(String uploadMethod) {
        this.uploadMethod = uploadMethod;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath == null ? null : filePath.trim();
    }

    public String getNotProvideReason() {
        return notProvideReason;
    }

    public void setNotProvideReason(String notProvideReason) {
        this.notProvideReason = notProvideReason == null ? null : notProvideReason.trim();
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag == null ? null : checkFlag.trim();
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }
}