/**
 * Copyright (c) 2020, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseCouterTradeResponseDto;

/**
 * 非交易过户请求
 * <AUTHOR>
 * @date 2020/9/24 15:54
 * @since JDK 1.8
 */
public class CounterNoTradeOverAccountRespDto extends BaseCouterTradeResponseDto {

    private static final long serialVersionUID = -2211002311746886360L;
}