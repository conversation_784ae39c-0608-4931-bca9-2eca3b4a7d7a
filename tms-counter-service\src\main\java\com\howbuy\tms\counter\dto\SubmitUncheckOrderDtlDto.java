/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description:(柜台份额合并或迁移明细订单)
 * <AUTHOR>
 * @date 2018年5月8日 上午10:55:21
 * @since JDK 1.6
 */
public class SubmitUncheckOrderDtlDto implements Serializable {
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -977104479966418158L;
    
    /**
     * 修改后是折扣
     */
    private BigDecimal afterDiscountRate;
    
    /**
     * 上报单号
     */
    private String submitDealNo;
    
    /**
     * 申请状态
     */
    private String appFlag;

    /**
     * 申请审核流水号
     */
    private String dealAppNo;
    
    /**
     * 申请审核申明细流水号
     */
    private String dealDtlAppNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 基金简称
     */
    private String fundName;

    /**
     * 基金份额类型
     */
    private String fundShareClass;
    
    /**
     * 产品交易渠道
     */
    private String productChannel;

    /**
     * 申请份额
     */
    private BigDecimal appVol;
    
    /**
     * 资金账号(转出)
     */
    private String cpAcctNo;
    
    /**
     * 协议类型 (转出)
     */
    private String protocolType;

    /**
     * 协议号(转出)
     */
    private String protocolNo;
    
    /**
     * 创建日期时间
     */
    private Date createDtm;

    /**
     * 更新日期时间
     */
    private Date updateDtm;

    /**
     * 银行账号(转出)
     */
    private String bankAcct;
    
    /**
     *  银行账号摘要
     */
    private String bankAcctDigest;
    
    /**
     * 银行编号(转出)
     */
    private String bankCode;
    
    /**
     * 转出前份额(可用份额)
     */
    private BigDecimal preAppVol;
    
    /**
     * 转出前冻结份额
     */
    private BigDecimal preFrznVol;
    /**
     * 基金TA代码
     */
    private String taCode;

    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 标准业务码
     */
    private String busiCode;

    /**
     * 订单明细号
     */
    private String dealDtlNo;
    
    /**
     * 分销机构号
     */
    private String disCode;
    
    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getBankAcctDigest() {
		return bankAcctDigest;
	}

	public void setBankAcctDigest(String bankAcctDigest) {
		this.bankAcctDigest = bankAcctDigest;
	}

	public String getAppFlag() {
		return appFlag;
	}

	public void setAppFlag(String appFlag) {
		this.appFlag = appFlag;
	}

	public BigDecimal getAfterDiscountRate() {
		return afterDiscountRate;
	}

	public void setAfterDiscountRate(BigDecimal afterDiscountRate) {
		this.afterDiscountRate = afterDiscountRate;
	}

	public String getSubmitDealNo() {
		return submitDealNo;
	}

	public void setSubmitDealNo(String submitDealNo) {
		this.submitDealNo = submitDealNo;
	}

	public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getDealDtlAppNo() {
        return dealDtlAppNo;
    }

    public void setDealDtlAppNo(String dealDtlAppNo) {
        this.dealDtlAppNo = dealDtlAppNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public BigDecimal getPreAppVol() {
        return preAppVol;
    }

    public void setPreAppVol(BigDecimal preAppVol) {
        this.preAppVol = preAppVol;
    }

    public BigDecimal getPreFrznVol() {
        return preFrznVol;
    }

    public void setPreFrznVol(BigDecimal preFrznVol) {
        this.preFrznVol = preFrznVol;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getDealDtlNo() {
        return dealDtlNo;
    }

    public void setDealDtlNo(String dealDtlNo) {
        this.dealDtlNo = dealDtlNo;
    }
}
