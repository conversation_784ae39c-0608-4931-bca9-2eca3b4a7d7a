/**
 * 初始化页面事件
 */

$(function () {
    //viewType 0-查看；1-审核；2-修改
    var viewType = CommonUtil.getParam("viewType");
    var dealDtlNo = CommonUtil.getParam("dealDtlNo");

    OwnerShipRightTransferPage.initBtn(viewType, dealDtlNo);
    // 订单信息查询
    OwnerShipRightTransferPage.queryOwnershipRightTransferDtl(dealDtlNo);

});


function changeIsTransfer(value) {
    if (value === '0') {
        $("#transferPriceComponent").hide();
    }
    if (value === '1') {
        $("#transferPriceComponent").show();
        $("#transferPrice").val(0)
    }
}

var OwnerShipRightTransferPage = {
        // 按钮初始化
        initBtn: function (viewType, dealDtlNo) {
            //隐藏按钮
            CommonUtil.higdeAllBtnOfDiv("submitDiv");
            if ("2" === viewType) {
                //修改
                $("#modifyBtn").show();
                //按钮绑定事件
                $("#modifyBtn").on('click', function () {
                    OwnerShipRightTransferPage.confirmModify(dealDtlNo);
                });
            } else {
                $("#modifyBtn").hide();
            }
            // 显示返回按钮
            $("#checkBackBtn").show();
            // 返回
            $("#checkBackBtn").on("click", function () {
                parent.layer.closeAll();
            });
        },

        queryOwnershipRightTransferDtl: function (dealDtlNo) {
            if (isEmpty(dealDtlNo)) {
                showMsg("订单号不存在");
                return false;
            }

            var uri = TmsCounterConfig.OWNERSHIP_RIGHT_TRANSFER_DTL || "";
            var reqparamters = {};
            reqparamters.dealDtlNo = dealDtlNo;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
            CommonUtil.ajaxAndCallBack(paramters, OwnerShipRightTransferPage.buildOrderInfo);
        }
        ,

        check: function () {
            var forceForm = $("#forceForm").serializeObject();
            var isNoTradeTransfer = "0";
            if (forceForm.isNoTradeTransfer != null) {
                isNoTradeTransfer = forceForm.isNoTradeTransfer;
            }
            var transferPrice = forceForm.transferPrice
            if (forceForm.isNoTradeTransfer != null) {
                if (isNoTradeTransfer === OwnerShipRightTransferPage.isNoTradeTransfer
                    && OwnerShipRightTransferPage.isNoTradeTransfer === "0") {
                    CommonUtil.layer_tip("没有任何修改");
                    return false;
                }
            }
            if (transferPrice === OwnerShipRightTransferPage.transferPrice) {
                CommonUtil.layer_tip("没有任何修改");
                return false;
            }
        },
        /**
         * 提交修改
         */
        confirmModify: function (dealDtlNo) {
            if (window.checkedClick === '1') {
                CommonUtil.enabledBtn("modifyBtn");
                return false;
            }
            //防止重复点击
            window.checkedClick = '1';
            // 1.校验
            var forceForm = $("#forceForm").serializeObject();
            var isNoTradeTransfer = "0";
            if (forceForm.isNoTradeTransfer != null) {
                isNoTradeTransfer = forceForm.isNoTradeTransfer;
            }
            var transferPrice = forceForm.transferPrice

            var uri = TmsCounterConfig.COUNTER_OWNERSHIP_RIGHT_TRANSFET || {};
            var transferVolElement = document.getElementById("transferVol");
            // 2.构建查询参数
            var reqparamters = {
                "dealDtlNo": dealDtlNo,
                "isNoTradeTransfer": isNoTradeTransfer,
                "transferPrice": transferPrice,
                // 过户份额对应的认缴金额
                "subsAmt":forceForm.subsAmt,
                // 过户的总认缴金额
                "totalSubsAmt":forceForm.totalSubsAmt,
                // 过户份额
                "transferVol":transferVolElement.innerText,
                "mBusinessCode": OwnerShipRightTransferPage.mBusinessCode
            };

            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, OwnerShipRightTransferPage.confirmCallback);
        },

        confirmCallback: function (data) {
            var respCode = data.code || '';
            var respDesc = data.desc || '';
            if (CommonUtil.isSucc(respCode)) {
                layer.confirm('股份订单非交易信息维护成功', {
                    btn: ['确定'] //按钮
                }, function () {
                    // 回到上个页面,防止重复操作
                    layer.closeAll();
                    if (OnLineOrderFile.isCrm()) {
                        CommonUtil.closeCurrentUrl();// 关闭当前页面
                    } else {
                        //刷新父页面
                        window.parent.location.reload();

                        //获取窗口索引
                        var index = parent.layer.getFrameIndex(window.name);
                        //关闭弹窗
                        parent.layer.close(index);
                    }
                });
            } else {
                CommonUtil.layer_tip("提交失败," + respDesc + "(" + respCode + ")");
            }
            CommonUtil.enabledBtn("modifyBtn");
        }
        ,

        /**
         *渲染产品信息查询结果
         */
        buildOrderInfo: function (data) {
            var bodyData = data.body || {};
            var orderInfo = bodyData.orderDtl || [];
            var appendHtml = '';
            OwnerShipRightTransferPage.mBusinessCode = orderInfo.mBusinessCode;
            OwnerShipRightTransferPage.isNoTradeTransfer = orderInfo.isNoTradeTransfer;
            OwnerShipRightTransferPage.transferPrice = orderInfo.transferPrice;

            $("#layerrs").empty();
            if (orderInfo.mBusinessCode === 1142 || orderInfo.mBusinessCode === 1144 || orderInfo.mBusinessCode === 1145) {
                appendHtml =
                    '<tr className="text-c">' +
                    '<td>客户号:</td>' +
                    '<td id="txAcctNo">' + formatData(orderInfo.txAcctNo) + '</td>' +
                    '<td>客户姓名:</td>' +
                    '<td id="custName">' + formatData(orderInfo.custName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>基金代码:</td>' +
                    '<td id="fundCode">' + formatData(orderInfo.fundCode) + '</td>' +
                    '<td>基金名称:</td>' +
                    '<td id="fundName">' + formatData(orderInfo.fundName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>业务名称:</td>' +
                    '<td id="busiName">' + CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, orderInfo.mBusinessCode, "--") + '</td>' +
                    '<td>确认日期:</td>' +
                    '<td id="ackDt">' + formatData(orderInfo.ackDt) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>转让份额:</td>' +
                    '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                    '<td>是否非交易过户:</td>' +
                    '<td>' +
                    '<select name="isNoTradeTransfer" id="isNoTradeTransfer" class="select" onchange="changeIsTransfer(value)">';

                if (orderInfo.isNoTradeTransfer === '1') {
                    subAppendHtml =
                        '<option value="1" selected>是</option> ' +
                        '<option value="0">否</option> ' +
                        '</select> </td>' +
                        '</tr>' +

                        '<tr className="text-c" id="transferPriceComponent">' +
                        '<td>转让价格:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="transferPrice" name="transferPrice"  value="' + CommonUtil.formatData(orderInfo.transferPrice, 0) + '"/></td>' +
                        '</tr>' +

                        '<tr className="text-c">' +
                        '<td>过户份额对应的认缴金额:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="subsAmt" name="subsAmt"  value="' + CommonUtil.formatData(orderInfo.oldSubsAmt) + '"/></td>' +
                        '<td>过户的总认缴金额:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="totalSubsAmt" name="totalSubsAmt"  value="' + CommonUtil.formatData(orderInfo.oldTotalSubsAmt) + '"/></td>' +
                        '</tr>'
                } else {
                    subAppendHtml =
                        '<option value="1" >是</option> ' +
                        '<option value="0" selected>否</option> ' +
                        '</select> </td>' +
                        '</tr>' +
                        '<tr className="text-c" id="transferPriceComponent">' +
                        '<td>转让价格:</td>' +
                        '<td> <input type="text" placeholder="请输入" id="transferPrice" name="transferPrice"  value="' + CommonUtil.formatData(orderInfo.transferPrice, 0) + '"/></td>' +
                        '</tr>'
                }
                appendHtml = appendHtml + subAppendHtml;
            } else {
                // 这里是非交易转让/转入的
                appendHtml =
                    '<tr className="text-c">' +
                    '<td>转让人客户号:</td>' +
                    '<td id="outTxAcctNo">' + formatData(orderInfo.outTxAcctNo) + '</td>' +
                    '<td>转让人客户姓名:</td>' +
                    '<td id="outCustName">' + formatData(orderInfo.outCustName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>受让人客户号:</td>' +
                    '<td id="inTxAcctNo">' + formatData(orderInfo.inTxAcctNo) + '</td>' +
                    '<td>受让人客户姓名:</td>' +
                    '<td id="inCustName">' + formatData(orderInfo.inCustName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>基金代码:</td>' +
                    '<td id="fundCode">' + formatData(orderInfo.fundCode) + '</td>' +
                    '<td>基金名称:</td>' +
                    '<td id="fundName">' + formatData(orderInfo.fundName) + '</td>' +
                    '</tr>' +
                    '<tr className="text-c">' +
                    '<td>业务名称:</td>' +
                    '<td id="busiName">' + CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, orderInfo.mBusinessCode, "--") + '</td>' +
                    '<td>确认日期:</td>' +
                    '<td id="ackDt">' + formatData(orderInfo.ackDt) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>转让份额:</td>' +
                    '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                    '<td>转让价格:</td>' +
                    '<td> <input type="text" placeholder="请输入" id="transferPrice" name="transferPrice"  value="' + CommonUtil.formatData(orderInfo.transferPrice, 0) + '"/></td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>过户份额对应的认缴金额:</td>' +
                    '<td> <input type="text" placeholder="请输入" id="subsAmt" name="subsAmt"  value="' + CommonUtil.formatData(orderInfo.oldSubsAmt) + '"/></td>' +
                    '<td>过户的总认缴金额:</td>' +
                    '<td> <input type="text" placeholder="请输入" id="totalSubsAmt" name="totalSubsAmt"  value="' + CommonUtil.formatData(orderInfo.oldTotalSubsAmt) + '"/></td>' +
                    '</tr>'

            }
            $("#layerrs").append(appendHtml);
            if (orderInfo.isNoTradeTransfer === '0') {
                $("#transferPriceComponent").hide();
            }
        }
    }
;