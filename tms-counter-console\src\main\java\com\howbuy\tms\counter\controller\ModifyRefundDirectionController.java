/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.context.ModifyRefundDirectionContext;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.CounterModifyRefundDirectionResponse;
import com.howbuy.tms.high.batch.facade.trade.modifyrefunddirection.bean.CounterModifyRefundDirectionBean;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @description: 修改回款方向
 * @author: chuanguang.tang
 * @date: 2021/8/3 10:06
 * @since JDK 1.8
 */
@Controller
public class ModifyRefundDirectionController {
	
    private Logger logger = LogManager.getLogger(ModifyRefundDirectionController.class);
    
    @Autowired
    private TmsCounterService  tmsCounterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;


    /**
     * @description:查询订单
     * @param request
     * @param response
     * @return org.springframework.web.servlet.ModelAndView
     * @author: chuanguang.tang
     * @date: 2021/8/3 10:38
     * @since JDK 1.8
     */
    @RequestMapping("/tmscounter/modifyrefund/queryorder.htm")
    public ModelAndView queryorder(HttpServletRequest request,HttpServletResponse response) throws Exception{
        String queryConditonForm= request.getParameter("queryConditonForm");
        CounterOrderConditionDto dto = JSON.parseObject(queryConditonForm, CounterOrderConditionDto.class);
        QueryDealOrderRefundRequest dealOrderRequest = new QueryDealOrderRefundRequest();
        dealOrderRequest.setDealNo(dto.getDealNo());
        dealOrderRequest.setTxAcctNo(dto.getTxAcctNo());
        dealOrderRequest.setmBusiCode(dto.getmBusiCode());
        dealOrderRequest.setQueryBeginDt(dto.getQueryBeginDt());
        dealOrderRequest.setQueryEndDt(dto.getQueryEndDt());
        dealOrderRequest.setProductCode(dto.getProductCode());
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        QueryDealOrderRefundResponse rs = tmsCounterService.queryDealOrder(dealOrderRequest);
        List<QueryDealOrderRefundResponse.QueryDealOrderRefundBean> queryDealOrderRefundBeans = new ArrayList<>();
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        if(user.getInHBJG()){
            List<String> filterFundCodeList = queryHighProductOuterService.queryNotHBJGFundListService();
            if(!CollectionUtils.isEmpty(rs.getResultList())){
                for(QueryDealOrderRefundResponse.QueryDealOrderRefundBean queryDealOrderRefundBean : rs.getResultList()){
                    if(!filterFundCodeList.contains(queryDealOrderRefundBean.getProductCode())){
                        queryDealOrderRefundBeans.add(queryDealOrderRefundBean);
                    }
                }
            }
        }else {
            queryDealOrderRefundBeans.addAll(rs.getResultList());
        }

        Map<String,Object> body = new HashMap<>(16);
        body.put("orders",queryDealOrderRefundBeans);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }


    /**
     * @description:修改回款方向
     * @param request
     * @param response
     * @return org.springframework.web.servlet.ModelAndView
     * @author: chuanguang.tang
     * @date: 2021/8/4 16:57
     * @since JDK 1.8
     */
    @RequestMapping("/tmscounter/modifyrefund/modifyrefundconfirm.htm")
    public ModelAndView modifyRefundConfirm(HttpServletRequest request,HttpServletResponse response) throws Exception{
        ModifyRefundDirectionContext context = buildContext(request);
        context.setAppDtm(new Date());

        CounterModifyRefundDirectionBean bean = fillOrderDto(context);

        logger.info("ModifyRefundDirectionController|modifyRefundConfirm|bean:{}", JSON.toJSONString(bean));
        CounterModifyRefundDirectionResponse responseDto = tmsCounterService.modifyRefundDir(bean, context.getDisInfoDto());

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    private CounterModifyRefundDirectionBean fillOrderDto(ModifyRefundDirectionContext context) {
        CounterModifyRefundDirectionBean bean  = new CounterModifyRefundDirectionBean();
        bean.setDealNo(context.getOrderDto().getDealNo());
        bean.setTxAcctNo(context.getOrderDto().getTxAcctNo());
        bean.setFundCode(context.getOrderDto().getProductCode());
        bean.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        bean.setDisCode(context.getOrderDto().getDisCode());
        bean.setIdNo(context.getOrderDto().getIdNo());
        bean.setCustName(context.getOrderDto().getCustName());
        bean.setFundName(context.getOrderDto().getProductName());
        bean.setProductChannel(context.getHighProduct().getProductChannel());
        bean.setAppDt(DateUtil.shortDateString(context.getAppDtm(), DateUtils.YYYYMMDD));
        bean.setAppTm(DateUtil.shortDateString(context.getAppDtm(),DateUtils.HHMMSS));
        bean.setTaCode(context.getHighProduct().getTaCode());
        Date currDate = new Date();
        OperatorInfoCmd operInfo = context.getOperatorInfoCmd();
        bean.setCreator(operInfo.getOperatorNo());
        bean.setModifier(operInfo.getOperatorNo());
        bean.setCreateDtm(currDate);
        bean.setUpdateDtm(currDate);
        bean.setOrderFormMemo(JSON.toJSONString(context.getRefundDto()));
        bean.setDbFlag(context.getDbFlag());
        bean.setDealAppNo(context.getDealAppNo());
        return bean;
    }

    private ModifyRefundDirectionContext buildContext(HttpServletRequest request){
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd)SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO,request);
        String counterOrderForm = request.getParameter("counterOrderForm");
        OrderDto counterOrderDto =  JSON.parseObject(counterOrderForm,OrderDto.class);
        String refundForm = request.getParameter("refundForm");
        RefundDto refundDto = JSON.parseObject(refundForm, RefundDto.class);


        ModifyRefundDirectionContext context = new ModifyRefundDirectionContext();
        context.setOperatorInfoCmd(operatorInfoCmd);
        context.setOrderDto(counterOrderDto);
        context.setRefundDto(refundDto);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(counterOrderDto.getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());
        context.setDisInfoDto(disInfoDto);

        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(counterOrderDto.getProductCode());
        context.setHighProduct(highProductBaseBean);
        context.setDbFlag(request.getParameter("dbFlag"));
        context.setDealAppNo(request.getParameter("dealAppNo"));
        return context;
    }
}

