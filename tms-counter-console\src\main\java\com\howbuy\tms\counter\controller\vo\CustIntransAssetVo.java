package com.howbuy.tms.counter.controller.vo;



import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by dejun.gu on 2019/10/30.
 */
public class CustIntransAssetVo implements Serializable {

	private static final long serialVersionUID = 6230947012625924531L;
	
	public List<TransitDetailVo> detailList = new ArrayList<>();

    public static class TransitDetailVo implements Serializable {
        private static final long serialVersionUID = 8357962566920230581L;
        private String prodCode;
        private String busiCode;
        private String bankAcct;
        private String bankAcctName;
        private BigDecimal occurBalance;
        private String settleStatus;
        private String fundName;
        private String productChannel;

        public String getProdCode() {
            return this.prodCode;
        }

        public void setProdCode(String prodCode) {
            this.prodCode = prodCode;
        }

        public String getBusiCode() {
            return this.busiCode;
        }

        public void setBusiCode(String busiCode) {
            this.busiCode = busiCode;
        }

        public String getBankAcct() {
            return this.bankAcct;
        }

        public void setBankAcct(String bankAcct) {
            this.bankAcct = bankAcct;
        }

        public String getBankAcctName() {
            return this.bankAcctName;
        }

        public void setBankAcctName(String bankAcctName) {
            this.bankAcctName = bankAcctName;
        }

        public BigDecimal getOccurBalance() {
            return this.occurBalance;
        }

        public void setOccurBalance(BigDecimal occurBalance) {
            this.occurBalance = occurBalance;
        }

        public String getSettleStatus() {
            return this.settleStatus;
        }

        public void setSettleStatus(String settleStatus) {
            this.settleStatus = settleStatus;
        }

        public String getFundName() {
            return fundName;
        }

        public void setFundName(String fundName) {
            this.fundName = fundName;
        }

        public String getProductChannel() {
            return productChannel;
        }

        public void setProductChannel(String productChannel) {
            this.productChannel = productChannel;
        }
    }

    public List<TransitDetailVo> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<TransitDetailVo> detailList) {
        this.detailList = detailList;
    }
}
