/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.enums;

/**
 * @description:(柜台审核标识枚举)
 * <AUTHOR>
 * @date 2017年4月10日 下午6:42:36
 * @since JDK 1.6
 */
public enum CheckFlagEnum {
    /**
     * 未审核
     */
    NOT_CHECK("0", "未审核"),
    /**
     * 审核通过
     */
    CHECK_SUCC("1", "审核通过"),
    /**
     * 核失败
     */
    CHECK_FAILD("2", "审核失败"),
    /**
     * 审核驳回
     */
    CHECK_REJECT("3", "审核驳回"),
    /**
     * 废单
     */
    CHECK_ABOLISH("4", "废单"),
    /**
     * 材料复审退回，订单驳回
     */
    CHECK_MATERIAL_REJECT("5", "材料复审退回，订单驳回");

    private String code;
    private String name;

    private CheckFlagEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
