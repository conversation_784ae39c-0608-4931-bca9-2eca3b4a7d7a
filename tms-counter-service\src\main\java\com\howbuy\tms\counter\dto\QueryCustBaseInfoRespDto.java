/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;

/**
 * @description:(查询客户基本信息响应体) 
 * <AUTHOR>
 * @date 2017年3月29日 下午1:27:38
 * @since JDK 1.7
 */
public class QueryCustBaseInfoRespDto extends BaseResponseDto{
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 4934424112541982141L;
     
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 投资者名称
     */
    private String custName;
    /**
     * 投资者类型
     */
    private String invstType;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 客户状态
     */
    private String custStat;
    
    /**
     * 未成年人标识：0-否，1-是
     */
    private String minorFlag;
    /**
     * 客户合作风险等级
     */
    private String corpCustRiskLevel;
    /**
     * 客户风险等级
     */
    private String custRiskLevel;
    /**
     * 风险测试日期
     */
    private String riskSurveyDt;
    /**
     * 分销交易账号
     */
    private String disTxAcctNo;
    /**
     * 分销机构代码
     */
    private String disCode;
    
    /**
     * 交易账户状态 0-正常； 1-开户待审核； 2-注销； 3-冻结；a 4-销户待确认
     */
    private String txAcctStat;

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getCustStat() {
        return custStat;
    }

    public void setCustStat(String custStat) {
        this.custStat = custStat;
    }

    public String getMinorFlag() {
        return minorFlag;
    }

    public void setMinorFlag(String minorFlag) {
        this.minorFlag = minorFlag;
    }

    public String getCorpCustRiskLevel() {
        return corpCustRiskLevel;
    }

    public void setCorpCustRiskLevel(String corpCustRiskLevel) {
        this.corpCustRiskLevel = corpCustRiskLevel;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel;
    }

    public String getRiskSurveyDt() {
        return riskSurveyDt;
    }

    public void setRiskSurveyDt(String riskSurveyDt) {
        this.riskSurveyDt = riskSurveyDt;
    }

    public String getDisTxAcctNo() {
        return disTxAcctNo;
    }

    public void setDisTxAcctNo(String disTxAcctNo) {
        this.disTxAcctNo = disTxAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getTxAcctStat() {
        return txAcctStat;
    }

    public void setTxAcctStat(String txAcctStat) {
        this.txAcctStat = txAcctStat;
    }
    
}

