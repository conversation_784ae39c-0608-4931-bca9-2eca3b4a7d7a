<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>转托管转出复核</title>
</head>

<body>
	<div class="page-container">
        <div class="containner_all">
              <p class="mainTitle mt10">转托管转出审核</p>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>                   
                        <th>风险等级</th>
                        <th>分销机构</th>                        
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt10">录入订单信息</p>
        <form action="check" id="transfertubeOutForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                    <tr class="text-c">
                        <td>业务类型</td>
                   	 	<td>
                   	 	 	<span class="select-box inline">
			           			<select name="transferTubeBusiType" id="transferTubeBusiType" class="select selectTransferTubeBusiType">
			           				<option value="">请选择</option>
			              			<option value="1">跨市场</option>
			             		 	<option value="2">场外跨销售机构</option>
			           			</select>
			       			</span>
                   	 	</td>
                        <td>基金代码</td>
                        <td>
                            <div class="searchIn"><input type="text" name="fundCode"  id="fundCode" placeholder="请输入"><a href="javascript:void(0)" class="searchIcon"></a></div>
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>基金简称</td>
                        <td id="fundName" >--</td>
                        <td>基金状态</td>
                        <td id="fundStatus">--</td>
                    </tr>
                    <tr class="text-c">
                    	<td>总份额（份）</td>
                    	<td>
                            <input type="text" id="totalVol" name="totalVol" readonly="readonly">
                        </td>
                        <td>对方销售人代码</td>
                    	<td id="tSellerTD">
                            <!-- 当业务类型选择"1"时销售人代码只有两个选项：‘101-上海’、‘102-深圳’ ; "2"时输入框，但不能输入304-->
                            <!--  
                            <span class="select-box inline">
			           			<select name="tSellerCode" id="tSellerCode" class="select">
			           				<option value="">请选择</option>
			              			<option value="101">101-上海</option>
			             		 	<option value="102">102-深圳</option>
			           			</select>
			       			</span>
			       			-->
                        </td>
                    </tr>
                    <tr class="text-c">
                    	<td>对方销售人处投资者基金交易账号</td>
                    	<td>
                            <input type="text" id="tSellerTxAcctNo" name="tSellerTxAcctNo" placeholder="请输入">
                        </td>
                        <td>对方网点/席位号</td>
                        <td>
                           <input type="text" id="tOutletCode" name="tOutletCode" placeholder="请输入">
                        </td>
                    </tr>
                     <tr class="text-c">
                     	<td>下单日期</td>
                        <td id="appDt" >
                        </td>
                        <td>下单时间</td>
                        <td id="appTm">
                        </td>
                     </tr>  
                </tbody>
            </table>
        </div>
       </form>
       
       <p class="main_title mt10">转出持仓份额信息</p>
       <div class="fl page_sj mt5" id="statics">转出笔数：xx笔；总转出份额：0.00份，大写：xx整</div>
       <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>基金代码</th>
                        <th>可用份额（份）</th> 
                        <th>冻结份额（份）</th>
                        <th>转出份额（份）</th>
                        <th>协议类型</th>
       					<th>协议号</th>
       					<th>银行名称</th>
                        <th>银行账号</th>                        
                    </tr>
               </thead>
                <tbody id="transOutCustBals">
                	 <tr class="text-c">
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
       <p class="main_title mt30">其他信息</p>
       <form id="transactorInfoForm" >
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
         		<tr class="text-c">
                   	<td>网点：</td>
                   	<td>
                   		柜台<input type="hidden" name="outletCode" value="01"/>
                   	</td>
                   	<td>投资顾问代码：</td>
              		<td id="consCode" class ="selectconsCode">
                	</td>
                </tr>
            	<tr class="text-c">
                   	<td>经办人姓名：</td>
                   	<td id="transactorName">
                   	</td>
                   	<td>经办人证件类型：</td>
              		<td id="transactorIdType">
                	</td>
                </tr>
             	<tr class="text-c">
              		<td>经办人证件号：</td>
              		<td id="transactorIdNo"> 
              		</td>
              		<td>失败原因：</td>
              		<td>
                        <input type="text" placeholder='请输入失败原因' id="checkFaildDesc" name="checkFaildDesc">
                    </td>
               	</tr>
            </tbody>
          </table>
        </div>
         </form>
         <p class="mt10 text-c">
            <a href="javascript:void(0)" id ="returnBtn" class="btn btn-default radius">退回</a> 
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <a href="javascript:void(0)" id ="succBtn" class="btn radius btn-secondary">审核通过</a>
        </p>
        <div class="clear page_all">
            	<div class="fy_part fr mt20" style="display: none" id="pageView"></div>
        </div>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/trade/transfertubeout.js"></script>
    <script type="text/javascript" src="../../../js/fund/check/checktransfertubeout.js?v=20181013"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/queryfundinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycheckorder.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/check/countercheck.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/bodyview.js?v=20181013"></script>
</body>

</html>