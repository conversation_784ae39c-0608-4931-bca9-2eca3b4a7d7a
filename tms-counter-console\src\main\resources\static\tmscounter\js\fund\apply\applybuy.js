$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplyBuy.checkOrder = {};
	ApplyBuy.productInfo = {};
	ApplyBuy.isAdviser = false;
	ApplyBuy.init(checkId,custNo,disCode,idNo);

});

var ApplyBuy = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,ApplyBuy.queryCheckOrderByIdBack);
		
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyBuy.checkOrder);
		});

	},

	queryProductInfo:function(fundCode){
		var uri= TmsCounterConfig.QUERY_PRODUCT_INFO_URL ||  {};
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo) || {};
		console.info("custInfoForm : " + custInfoForm);
		var reqparamters = {"productCode":fundCode, "custInfoForm": custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ApplyBuy.queryProductInfoCallBack);
	},

	queryProductInfoCallBack:function(data){

		var productInfo = data.body || {};
		ApplyBuy.productInfo = productInfo;
		BuyFund.productInfo = productInfo;
		var isAdviser = productInfo.adviserFlag || false;
		ApplyBuy.isAdviser = isAdviser;
		BuyFund.isAdviser = productInfo;
		if (isAdviser) {
			$("#discountRate").attr('readonly', true);
			$("#originalFeeRate").val("--");
			$("#discountRate").val("--");

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(productInfo.isBuyOpen == '1' ? "可申购" : "暂停");
			}
			var adviserQuestRule = productInfo.adviserQuestRuleFlag || false;
			if (adviserQuestRule) {
				$("#questionAnswerDiv").show();
				$("#questionAnswer").val(ApplyBuy.checkOrder.surveyAnswer || '');

			}else {
				$("#questionAnswerDiv").hide();
				$("#questionAnswer").val('');
			}

		}else {
			$("#questionAnswerDiv").hide();
			$("#questionAnswer").val('');
			$("#discountRate").attr('readonly', false);
			var isCommonFund = QueryFundInfo.checkFundInfo(productInfo);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, productInfo.fundStat));
			}

			if(!isCommonFund){
				return false;
			}
		}

	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplyBuy.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(ApplyBuy.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(ApplyBuy.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		
		$("#confimBuyBtn").on('click',function(){
			BuyFund.confirm(ApplyBuy.productInfo);
		});


		ApplyBuy.queryProductInfo(ApplyBuy.checkOrder.fundCode);
		
		if($("#selectBank").length > 0){
			$("#selectBank").val(ApplyBuy.checkOrder.cpAcctNo);
		}
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(ApplyBuy.checkOrder.agentFlag);
		}
		
		if($("#originalFeeRate").length > 0){
			$("#originalFeeRate").val(ApplyBuy.checkOrder.feeRate);
		}
		
		if($("#riskFlag").length > 0){
			$("#riskFlag").html(CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, ApplyBuy.checkOrder.riskFlag, ''));
		}
		
		if($("#fundCode").length > 0){
			$("#fundCode").val(ApplyBuy.checkOrder.fundCode);
		}
		
		if($("#applyAmount").length > 0){
			$("#applyAmount").val(CommonUtil.formatAmount(ApplyBuy.checkOrder.appAmt));
		}
		
		if($("#applyAmountCapital").length > 0){
			$("#applyAmountCapital").val(Main.format(CommonUtil.formatAmount(ApplyBuy.checkOrder.appAmt)));
		}
		
		if($("#discountRate").length > 0){
			$("#discountRate").val(ApplyBuy.checkOrder.discountRate);
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").val(ApplyBuy.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(ApplyBuy.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, ApplyBuy.checkOrder.consCode, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(ApplyBuy.checkOrder.transactorIdNo);
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(ApplyBuy.checkOrder.transactorName);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(parseInt(ApplyBuy.checkOrder.transactorIdType));
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(ApplyBuy.checkOrder.memo);
		}
	},
}
