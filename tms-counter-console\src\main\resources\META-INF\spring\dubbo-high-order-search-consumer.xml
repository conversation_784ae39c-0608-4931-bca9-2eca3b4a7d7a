<?xml version="1.0" encoding="UTF-8"?>
<!-- - Copyright 1999-2011 Alibaba Group. - - Licensed under the Apache License, 
	Version 2.0 (the "License"); - you may not use this file except in compliance 
	with the License. - You may obtain a copy of the License at - - http://www.apache.org/licenses/LICENSE-2.0 
	- - Unless required by applicable law or agreed to in writing, software - 
	distributed under the License is distributed on an "AS IS" BASIS, - WITHOUT 
	WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. - limitations 
	under the License. - See the License for the specific language governing 
	permissions and -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	
       <!-- 查询客户持仓 -->
        <dubbo:reference registry="high-order-search-remote"   id="queryAcctBalanceFacade" interface="com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade" check="false"/>
       <!-- 查询客户持仓明细 -->
       <dubbo:reference registry="high-order-search-remote"   id="queryAcctBalanceDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlFacade" check="false"/>
       <!-- 查询柜台申请订单接口 -->
	   <dubbo:reference  id="queryDealOrderFacade" interface="com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderFacade" registry="high-order-search-remote"  check="false"/>
	   <!-- 查询交易订单明细接口 -->
	   <dubbo:reference id="queryHighDealOrderDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlFacade" registry="high-order-search-remote"  check="false"/>
	   	<!-- 查询订单列表接口 -->
	  	<dubbo:reference id="queryDealOrderListFacade" interface="com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade"  registry="high-order-search-remote"  check="false" />
	
	  	<!-- 查询客户持有基金分红方式 -->
	  	<dubbo:reference id="queryCustFundDivFacade" interface="com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivFacade" registry="high-order-search-remote"  check="false" />
	  
	   	<!-- 查询高端可撤订单-->
		<dubbo:reference id="queryCancelOrderListFacade"  interface="com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListFacade" registry="high-order-search-remote"  check="false"/>
	
     	<!-- 查询高端可强撤撤订单-->
		<dubbo:reference id="queryForceCancelOrderListFacade"  interface=" com.howbuy.tms.high.orders.facade.search.queryforcecancelorderlist.QueryForceCancelOrderListFacade" registry="high-order-search-remote"  check="false"/>
	
		<!-- 查询预约列表-->
		<dubbo:reference id="queryPreBookListFacade"  interface="com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade" registry="high-order-search-remote"  check="false"/>
		
		<!-- 购买状态查询 -->
		<dubbo:reference id="queryBuyFundStatusFacade" interface="com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade" registry="high-order-search-remote"  check="false"/>
		
		<!-- 产品购买额度查询 -->
		<dubbo:reference id="queryProductQuotaFacade" interface="com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade" registry="high-order-search-remote"  check="false"/>
		
		<!-- 追加状态查询 -->
		<dubbo:reference id="querySuppleStatusFacade" interface="com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusFacade" registry="high-order-search-remote"  check="false"/>
		
		<!-- 查询预约详细信息 -->
		<dubbo:reference id="queryPreBookDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlFacade" registry="high-order-search-remote"  check="false"/> 
		
		 <!-- 查看子账本明细 -->
		<dubbo:reference id="querySubBalanceFacade" interface="com.howbuy.tms.high.orders.facade.search.querysubbalance.QuerySubBalanceFacade" registry="high-order-search-remote"  check="false" />
		<!-- 查询赎回列表 -->
		<dubbo:reference id="queryRedeemFundListCounterFacade" interface="com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterFacade" registry="high-order-search-remote"  check="false" />

		<!-- 查询柜台申请订单回款信息接口 -->
		<dubbo:reference  id="queryDealOrderRefundFacade" interface="com.howbuy.tms.high.orders.facade.search.querydealorderrefund.QueryDealOrderRefundFacade" registry="high-order-search-remote"  check="false"/>
		<dubbo:reference  id="queryHighInTransitFacade" interface="com.howbuy.tms.high.orders.facade.search.queryhighintransit.QueryHighInTransitFacade" registry="high-order-search-remote"  check="false"/>

    <!-- 查询好臻认缴金额相关信息 -->
    <dubbo:reference id="queryHzSubscribeAmtInfoFacade"
                     interface="com.howbuy.tms.high.orders.facade.search.queryhzsubscribeamtinfo.QueryHzSubscribeAmtInfoFacade"
                     registry="high-order-search-remote" check="false"/>

    <dubbo:reference id="queryFeeInfoFacade"
                     interface="com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacade"
                     registry="high-order-search-remote" check="false"/>

    <dubbo:reference id="queryHzBuyOrderInfoFacade"
                     interface="com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoFacade"
                     registry="high-order-search-remote" check="false"/>

	<!-- 查询用户产品认缴金额信息-->
	<dubbo:reference id="queryCustomerFundSubsAmtFacade"
					 interface="com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtFacade"
					 registry="high-order-search-remote" check="false"/>
</beans>