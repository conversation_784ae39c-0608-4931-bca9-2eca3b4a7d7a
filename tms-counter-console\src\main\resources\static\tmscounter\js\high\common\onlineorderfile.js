var OnLineOrderFile ={

    CRM_BUY:'0',
    CRM_SELL:'1',
    CRM_MODIFY_DIV:'2',
    CRM_CANCEL:'3',

    /**最新当前状态(1:待审核、2:分部退回、3:分部已审核总部待复核、4:总部退回、5:总部已复核OP待审核、6:OP初审退回、7:OP初审通过、8:OP复审退回、9:审核通过、10作废)**/
    // op 未初审
    CRM_CHECK_STATUS_OP_UN_PRE_CHECK:'5',
    // op 初审退回
    CRM_CHECK_STATUS_OP_PRE_CHECK_REJECT:'6',
    // op 初审通过
    CRM_CHECK_STATUS_OP_PRE_CHECK_FINISH:'7',
    // op 复审退回
    CRM_CHECK_STATUS_OP_RE_CHECK_REJECT:'8',
    // OP复审通过
    CRM_CHECK_STATUS_OP_RE_CHECK_FINISH:'9',
    VIEW_URL_MAP:{
        "0": "../view/modify/modifybuydeal.html",
        "1": "../view/modify/modifyselldeal.html",
        "2": "../view/modify/modifymodifydiv.html",
        "3": "../view/modify/modifycancel.html"
    },

    // OP 初审节点
    CRM_OP_CHECK_NODE_PRE: '0',

    // op 复审节点
    CRM_OP_CHECK_NODE_RE: '1',

    // OP 查看节点
    CRM_OP_CHECK_NODE_VIEW: '2',

    // OP 修改节点
    CRM_OP_CHECK_NODE_MODIFY: '3',

    // 驳回
    OP_CRM_CHECK_REJECT:'0',
    // 通过
    OP_CRM_CHECK_FINISH:'1',

    // 初审结果
    CHECK_SUCC_MAP:{"0":"7", "1":"9","3":"7"},

    // 复审结果
    RE_CHECK_MAP:{"0":"8", "1":"9"},

    // 退回结果
    REJECT_MAP:{'0':"6", '1':'8','3':"6"},

    REJECT_STATUS:[6,7],

    MODIFY_CHECK_TIP_MAP: {
        "1": "柜台资料待销助审核，期间请勿下单",
        "2": "柜台资料待销助审核，期间请勿下单",
        "3": "柜台资料待销助审核，期间请勿下单",
        "4": "柜台资料待销助审核，期间请勿下单",
        "5": "succ",
        "6": "柜台资料正在重新提交中，OP初审退回",
        "7": "柜台材料状态异常,已OP初审通过",
        "8": "柜台资料正在重新提交中，OP复审退回",
        "9": "柜台材料状态异常,已通过",
        "10": "没有查询到柜台资料信息"
    },

    PRE_CHECK_TIP_MAP: {
        "1": "柜台资料待销助审核，期间请勿下单",
        "2": "柜台资料待销助审核，期间请勿下单",
        "3": "柜台资料待销助审核，期间请勿下单",
        "4": "柜台资料待销助审核，期间请勿下单",
        "5": "succ",
        "6": "柜台资料正在重新提交中，OP初审退回",
        "7": "柜台资料已初审完成，请勿重复下单",
        "8": "柜台资料正在重新提交中，OP复审退回",
        "9": "柜台材料状态异常,已通过",
        "10": "没有查询到柜台资料信息"
    },

    crmTradeTypeToTmsTradeTypeMap: {
       "13": '0',
       "14": '0',
       "15": '0',
       '16': '1',
       '12': '2',
       '17': '3'
    },
    //OP 0-初审节点 5-op初审
    // op 1- 复审节点 6-op复审
    // OP 2-修改节点 5-p初审
    checkNodeAndCheckLevelMap:{
        "0":"5",
        "1":"6",
        "2":"5",
        "3":"5"
    },

    selfLogin:function(urlParams){
        if(OnLineOrderFile.isCrm()){
            OnLineOrderFile.loginStatus = "0";
            OnLineOrderFile.loginRst = '登录失败';

            urlParams = urlParams || {};
            var  uri= TmsCounterConfig.HIGH_SELF_LOGIN_URL ||  '';
            var reqparamters = {};
            reqparamters['operatorNo'] = urlParams['operatorNo'] || '';
            var paramters = {"uri":uri, "reqparamters":reqparamters};
            CommonUtil.ajaxAndCallBack(paramters, OnLineOrderFile.loginCallBack);
        }

    },
    loginCallBack:function(data){
        var code = data.code;
        var desc = data.desc;
        if(!CommonUtil.isSucc(code)){
            OnLineOrderFile.loginStatus = "0";
            OnLineOrderFile.loginRst = desc;
        }else{
            OnLineOrderFile.loginStatus = "1";
            OnLineOrderFile.loginRst = desc;
        }
    },
    initCrmUrl:function(urlParams, custInfoBind,crmTradeType){
        var outSource = urlParams['source'];
        if('crm' == outSource){
            var dealAppNo = urlParams['forId'];
            var crmTradeType = urlParams['busiId'];
            var tradeType = OnLineOrderFile.crmTradeTypeToTmsTradeTypeMap[crmTradeType];
             if(OnLineOrderFile.isModify(dealAppNo, tradeType)){
                 urlParams['dealAppNo'] = dealAppNo;
                 OnLineOrderFile.redirectModifyUrl(urlParams, tradeType);
                 return true;
             }
            OnLineOrderFile.selfLogin(urlParams);

            if(OnLineOrderFile.loginStatus != "1"){
                CommonUtil.layer_alert("登录失败"+OnLineOrderFile.loginRst);
                return;
            }

            var hbOneNo = urlParams['hboneNo'];
            if(CommonUtil.isEmpty(hbOneNo)){
                CommonUtil.layer_tip("请求参数没有一帐通号")

            }
            var preId = urlParams['preId'];
            if(CommonUtil.isEmpty(preId)){
                CommonUtil.layer_tip("请求参数没有预约ID")

            }

            // 根据客户类型和产品代码，获取默认分销代码
            var fundCode = urlParams['fundCode'];
            HighCustInfo.getDefaultDisCode(hbOneNo,fundCode);

            if($.isFunction(custInfoBind)){
                // 查询客户基本信息
                custInfoBind(hbOneNo)
            }

            // 默认选中第一条客户信息记录
            HighCustInfo.selectFirstCust();

            // 选中预约信息
            Appoint.selectAppointByPreId(preId);
        }
    },

    redirectModifyUrl:function(urlParams, crmTradeType){
        window.location.href = OnLineOrderFile.VIEW_URL_MAP[crmTradeType]+ "?" + OnLineOrderFile.buildUrlParams(urlParams) + "&viewType=2";
    },

    isModify:function(dealAppNo, crmTradeType){
        if(!CommonUtil.isEmpty(dealAppNo) && !CommonUtil.isEmpty(crmTradeType)){
            return true;
        }
        return false;
    },

    buildUrlParams:function(urlParamsJson){
       return  Object.keys(urlParamsJson).map(function(k) {
            return encodeURIComponent(k) + "=" + encodeURIComponent(urlParamsJson[k]);
        }).join('&');
    },
    initCrmUrlWithOutAppoint:function(urlParams,custInfoBind, crmTradeType){
        var outSource = urlParams['source'];
        if('crm' == outSource){
            OnLineOrderFile.selfLogin(urlParams);
            if(OnLineOrderFile.loginStatus != "1"){
                CommonUtil.layer_alert("登录失败"+OnLineOrderFile.loginRst);
                return;
            }
            var dealAppNo = urlParams['forId'];
            var crmTradeType = urlParams['busiId'];
            var tradeType = OnLineOrderFile.crmTradeTypeToTmsTradeTypeMap[crmTradeType];
            if(OnLineOrderFile.isModify(dealAppNo, tradeType)){
                urlParams['dealAppNo'] = dealAppNo;
                OnLineOrderFile.redirectModifyUrl(urlParams, tradeType);
                return true;
            }
            var hbOneNo = urlParams['hboneNo'];
            if(CommonUtil.isEmpty(hbOneNo)){
                CommonUtil.layer_tip("请求参数没有一帐通号")

            }

            // 根据客户类型和产品代码，获取默认分销代码
            var fundCode = urlParams['fundCode'];
            HighCustInfo.getDefaultDisCode(hbOneNo,fundCode);

            if($.isFunction(custInfoBind)){
                // 查询客户基本信息
                custInfoBind(hbOneNo)
            }

            // 默认选中第一条客户信息记录
            HighCustInfo.selectFirstCust();
        }
    },

    initCrmUrlWithOutAppointAndSelectCustInfo:function(urlParams,custInfoBind){
        var outSource = urlParams['source'];
        if('crm' == outSource){
            OnLineOrderFile.selfLogin(urlParams);
            if(OnLineOrderFile.loginStatus != "1"){
                CommonUtil.layer_alert("登录失败"+OnLineOrderFile.loginRst);
                return;
            }
            var dealAppNo = urlParams['forId'];
            var crmTradeType = urlParams['busiId'];
            var tradeType = OnLineOrderFile.crmTradeTypeToTmsTradeTypeMap[crmTradeType];
            if(OnLineOrderFile.isModify(dealAppNo, tradeType)){
                urlParams['dealAppNo'] = dealAppNo;
                OnLineOrderFile.redirectModifyUrl(urlParams, tradeType);
                return true;
            }
            var hbOneNo = urlParams['hboneNo'];
            if(CommonUtil.isEmpty(hbOneNo)){
                CommonUtil.layer_tip("请求参数没有一帐通号")

            }

            if($.isFunction(custInfoBind)){
                // 查询客户基本信息
                custInfoBind(hbOneNo)
            }

        }
    },

    /**
     *
     * @Description  初始化材料
     *
     * @param null
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 17:30
     **/
    initMaterial:function(urlParams, custSelectOrder, checkNode){
        // 初始化资料信息
        var source = urlParams['source'];
        if("crm" == source){
            OnLineOrderFile.query(urlParams, null, checkNode);
        }else{
            OnLineOrderFile.query(null, custSelectOrder, checkNode);
        }
    },

    isCrm:function(){
        var urlParams = CommonUtil.getParamJson() || {};
        var source = urlParams['source'];
        if("crm" == source){
            return true;
        }else{
            return false;
        }
    },
    
    query:function (urlParams, custSelect,checkNode) {
        var queryCondition = {};
        if(!(urlParams == null)){
            queryCondition["orderid"] =  urlParams['orderId'];// 材料ID
            queryCondition["hboneno"] =  urlParams['hboneNo'];// 一账通帐号
            queryCondition["pcode"] =  urlParams['fundCode'];// 产品代码
            queryCondition["preid"] =  urlParams['preId'];// 预约ID
            queryCondition["busiid"] =  urlParams['busiId'];// 业务类型ID
        }else{
            queryCondition = custSelect || {};
        }

        var  uri= TmsCounterConfig.HIGH_QUERY_ORDERFILE_URL ||  '';
        var reqparamters = {};
        reqparamters["condition"] = JSON.stringify(queryCondition);
        reqparamters['checkNode'] = checkNode || '';
        var paramters = {"uri":uri, "reqparamters":reqparamters};
        CommonUtil.ajaxAndCallBack(paramters, OnLineOrderFile.queryCallBack);
    },

    queryCallBack:function (data) {
        var bodyData = data.body || {};
        var orderFile = bodyData.orderFile || {};
        OnLineOrderFile.buildOrderFileHtml(orderFile)
    },

    clearOrder:function(){
        $("#onLineMaterial").html("");
    },

    getMsgTip: function (orderFile) {
        var msg = "succ";
        if (OnLineOrderFile.CRM_OP_CHECK_NODE_PRE == orderFile.checkNode) {
            // 初审
            msg = OnLineOrderFile.PRE_CHECK_TIP_MAP[orderFile.curstat];
        } else if (OnLineOrderFile.CRM_OP_CHECK_NODE_RE == orderFile.checkNode) {
            // 复审
            if (!(OnLineOrderFile.CRM_CHECK_STATUS_OP_PRE_CHECK_FINISH == orderFile.curstat)) {
                msg = "资料审核状态异常，请联系管理员";
            } else {
                msg = "succ";
            }
        } else if (OnLineOrderFile.CRM_OP_CHECK_NODE_MODIFY == orderFile.checkNode) {
            msg = OnLineOrderFile.MODIFY_CHECK_TIP_MAP[orderFile.curstat];
        } else if (OnLineOrderFile.CRM_OP_CHECK_NODE_VIEW = orderFile.checkNode) {
            msg = "succ";
        }
        return msg;
    }, buildBodyItems: function (orderinfolist) {
        var trBodyList = [];
        $(orderinfolist).each(function (index, element) {
            var trItems = [];
            var fileTyleItem = CommonUtil.formatData(element.filetypename);
            // 是否必填
            if('1' == element.isrequire){
                fileTyleItem += '<span class="required">*</span>';
            }
            trItems.push(fileTyleItem);// 材料名称
            trItems.push(OnLineOrderFile.buildFilePath(element.orderFileList));// 文件路径
            var isqualItem = '<span class="select-box inline"><select fid= "' + element.fid + '" name="isqual" class="select selectIsqual" isallowopt="'+element.isallowopt +'"></select></span>';
            var returndesItem = '<input class="returndes" fid= "' + element.fid + '" name="returndes"/>';// 退回原因
            var lastreturndesItem = '<input class="lastreturndes" fid= "' + element.fid + '" name="lastreturndes" value="'+CommonUtil.formatData(element.lastreturndes)+'"'+' disabled="none"/>';// 最新退回原因

            trItems.push(isqualItem);// 文件路径
            trItems.push(returndesItem);
            trItems.push(lastreturndesItem);// 上次退回原因
            trBodyList.push('<tr fid = "+element.fid+"><td style="width:20%;white-space:normal;word-wrap:break-word;">' + trItems.join("</td><td style=\"width:20%;white-space:normal;word-wrap:break-word;\">") + '</td></tr>');
        });
        return trBodyList;
    }, buildOrderFileHtml:function(orderFile){
        OnLineOrderFile.hideMaterial(CommonUtil.getParam("source"), orderFile);
        var orderinfolist = orderFile.orderinfolist || [];

        var tableList = [];
        tableList.push('<table class="table table-border table-bordered table-hover table-bg table-sort" style="table-layout:fixed;">');
        tableList.push('<form id="onLineOrderForm">');

        var headItems = [];
        headItems.push("材料名称");
        headItems.push("上传材料");
        headItems.push("审核结果");
        headItems.push("退回原因");
        headItems.push("上次退回原因");
        var headHtml = '<tr><td>'+ headItems.join('</td><td>')+ '</td></tr>';

        tableList.push(headHtml);
        if(orderinfolist.length <= 0){
            var msg = "没有查询到柜台资料信息";
            var trHtml ='<tr><td colspan="'+headItems.length+'">'+msg+'</td></tr>';
            tableList.push(trHtml);
            tableList.push('<table>');
        }else{
            var msg = OnLineOrderFile.getMsgTip(orderFile);
            if(msg == "succ"){
                var trBodyList = OnLineOrderFile.buildBodyItems(orderinfolist);
                var trBodyHtml=trBodyList.join('');
                tableList.push(trBodyHtml);
                tableList.push('</form>');
                tableList.push('</table>');
                var textHtml = '<div id="curcheckdesDiv" style="display: none; overflow:auto;height:95%;width:95%;"><textarea style="overflow:auto;height:100%;width:100%;" id="curcheckdes"></textarea></td></div>';
                tableList.push(textHtml);
            }else{
                var trHtml ='<tr><td colspan="4">'+msg+'</td></tr>';
                tableList.push(trHtml);
                tableList.push('</form>');
                tableList.push('</table>');
            }
        }

        var rejectBtn = '<p class="mt30">'+
            '<a href="javascript:void(0)" id ="rejectOrderBtn"  class="btn radius btn-secondary">材料退回</a>'+
            '</p>';
        var onlineOrderHtml = tableList.join("") + rejectBtn;
        $("#onLineMaterial").html(onlineOrderHtml);

        $("#curcheckdes").attr("check-node", orderFile.checkNode);
        var dealAppNo = CommonUtil.getParam("forId") || CommonUtil.getParam("dealAppNo");
        $("#curcheckdes").attr("forder-id", dealAppNo);
        $("#curcheckdes").attr("check-node", orderFile.checkNode);
        $("#curcheckdes").attr("order-id",orderFile.orderid);

        //初始化
        $(".selectIsqual").html(CommonUtil.selectOptionsHtml(CONSTANTS.ON_LINE_ORDER_ISQUAL_MAP, "1", null, null, "1"));

        // 绑定驳回事件
        $("#rejectOrderBtn").on("click", function () {
            $("#curcheckdes").val("");
            CommonUtil.layerConfrim("请输入驳回意见", $("#curcheckdesDiv"),null, OnLineOrderFile.rejectOrder);
        });

        // 绑定选择合格事件
        $(".selectIsqual").on("click", function () {
            var isallowopt = $(this).attr("isallowopt");
            if("false" === isallowopt){
                CommonUtil.layer_tip("上级审核员已审核通过，不允许修改为不合格");
            }
        });
        
        // 是否禁用驳回按钮
        var canCheck = orderFile.canCheck || '';
        if( !("1" == canCheck)){
             CommonUtil.disabledBtn("rejectOrderBtn")
        }

        // 隐藏退回按钮
        if(OnLineOrderFile.CRM_OP_CHECK_NODE_VIEW == orderFile.checkNode){
              $("#rejectOrderBtn").hide();
        }
    },

    buildFilePath:function (orderFileList) {
        var filePaths = [];
        $(orderFileList).each(function(index, element){
            var fileUrl = '<a href="'+element.filepathurl+'" target="_blank" >'+OnLineOrderFile.buildFileNameWithSuffix(element)+'</a>';
            filePaths.push(fileUrl);
        });

        return '<ul><li>'+ filePaths.join('</li><li>')+"</li></ul>";
    },

    buildFileNameWithSuffix:function(file){
       return  file.filename+'.'+file.filesuffix;
    },

    getRejectUri: function (checkNode) {
        var uri = TmsCounterConfig.HIGH_ORDERFILE_URL || '';
        if (OnLineOrderFile.CRM_OP_CHECK_NODE_RE == checkNode) {
            // 复审退回
            uri = TmsCounterConfig.HIGH_RECHECK_REJECTFILE_URL;
        }
        return uri;
    }, rejectOrder:function () {
        var checkNode = $("#curcheckdes").attr("check-node");
        var checkOrder = OnLineOrderFile.buildOrderCheckFile();
        checkOrder['curstat'] = OnLineOrderFile.REJECT_MAP[checkNode];
        if(OnLineOrderFile.valilateNotCanReject(checkOrder)){
            CommonUtil.layer_tip("请填写驳回意见或者选择要退回的资料");
            return false;
        }

        if(OnLineOrderFile.validateRejectDescIsEmpty(checkOrder.fileinfolist)){
            CommonUtil.layer_tip("存在不合格的资料，没有填写退回原因， 请检查");
            return false;
        }

        if(OnLineOrderFile.isCanNotReject(checkOrder.fileinfolist)){
            CommonUtil.layer_tip("上级审核员已审核通过，不允许修改为不合格");
            return false;
        }

        var uri = OnLineOrderFile.getRejectUri(checkNode);
        var reqparamters = {};
        reqparamters["condition"] = JSON.stringify(checkOrder);
        var paramters = {"uri":uri, "reqparamters":reqparamters};
        CommonUtil.ajaxAndCallBack(paramters, OnLineOrderFile.rejectOrderCallback);
    },
    valilateNotCanReject:function(checkOrder){
        if(CommonUtil.isEmpty(checkOrder.curcheckdes) && OnLineOrderFile.isAllPass(checkOrder.fileinfolist)){
             return true;
        }
        return false;
    },
    isAllPass:function(fileinfolist){
        fileinfolist = fileinfolist || [];
        var allPass = true;
        $(fileinfolist).each(function (index, element) {
             if("1" != element.isqual){
                 allPass = false;
                 return allPass;
             }
        });

        return allPass;
},

    isCanNotReject(fileinfolist){
        fileinfolist = fileinfolist || [];
        var allPass = false;
        $(fileinfolist).each(function (index, element) {
            // isqual 是否合格 0-不合格
            if("false" === element.isallowopt && "0" == element.isqual){
                allPass = true;
                return allPass;
            }
        });

        return allPass;
    },

    validateRejectDescIsEmpty:function(fileinfolist){
        var flag = false;
        $(fileinfolist).each(function (index, element) {
            if("1" != element.isqual && CommonUtil.isEmpty(element.returndes)){
                flag = true;
                return flag;
            }
        });

        return flag;
    },
    rejectOrderCallback:function(data){
        $("#curcheckdes").val("");
        data = data || {};
        if(CommonUtil.isSucc(data.code)){
            layer.confirm('驳回成功', {
                btn: ['确定'] //按钮
            }, function(){
                layer.closeAll();
                if(OnLineOrderFile.isCrm()){
                    // 关闭当前页面
                    CommonUtil.closeCurrentUrl();
                }else{
                    CommonUtil.reloadUrl();
                }
            });

        }else{
            CommonUtil.layer_tip("驳回失败," + data.desc);
        }
    },
    /**
     * 
     * @Description 
     * 
     * @param checkStatus 0-驳回 1-通过
     * @return 
     * <AUTHOR>
     * @Date 2019/6/5 13:50
     **/
    buildOrderCheckFile:function () {
        var fileOrders = {};
        $(".returndes").each(function (index, element) {
            var fid = $(element).attr("fid");
            var returndes = $(element).val();
            var fileOrder = {};
            fileOrder["fid"] = fid;
            fileOrder["returndes"] = returndes;
            fileOrders[fid] = fileOrder;
        });

        $(".selectIsqual").each(function (index, element) {
            var fid = $(element).attr("fid");
            var isallowopt =  $(element).attr("isallowopt");
            var isquals = $(element).val();

            var fileOrder = fileOrders[fid];
            fileOrder["isqual"] = isquals;
            fileOrder["isallowopt"] = isallowopt;
            fileOrders[fid] = fileOrder;
        });

        var fileinfolist = [];
        $.each(fileOrders, function (key, value) {
            fileinfolist.push(value);
        });


        var checkOrder = {};
        var curcheckdes = $("#curcheckdes").val();
        var orderId =  $("#curcheckdes").attr("order-id");
        var checkNode = $("#curcheckdes").attr("check-node");
        var forderid = $("#curcheckdes").attr("forder-id");
        checkOrder['orderid'] = orderId;
        checkOrder['curcheckdes'] = curcheckdes;// 审核意见
        checkOrder['fileinfolist'] = fileinfolist;// 文件状态
        checkOrder['curstat'] = OnLineOrderFile.getCheckStatus(checkNode);// 审核状态
        checkOrder['forderid'] = forderid || '';// 审核状态
        checkOrder['checklevel'] = OnLineOrderFile.checkNodeAndCheckLevelMap[checkNode] || '';// 审核级别

        return checkOrder;
    },

    getCheckStatus:function(checkNode){
        return OnLineOrderFile.CHECK_SUCC_MAP[checkNode];
    },
    
    /**
     * 
     * @Description 隐藏材料
     * 
     * @param null
     * @return 
     * <AUTHOR>
     * @Date 2020/7/29 14:37
     **/
    hideMaterial:function (source, orderFile) {
        if("crm" != source && (orderFile == null || JSON.stringify(orderFile) === '{}')){
             $("#showMaterial").hide();
            $("#onLineMaterial").hide();
        }
    }

};