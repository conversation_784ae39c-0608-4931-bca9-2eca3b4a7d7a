/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:()
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年7月18日 上午9:21:06
 * @since JDK 1.6
 */
public class FeeReqDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 6197288127734368929L;

    private String fundCode;

    private String businessCode;

    private String invstType;

    private String shareClass;
    /**
     * 实缴金额或者申请净金额
     */
    private BigDecimal netBuyAmt;
    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;

    private BigDecimal appointmentAmt;

    private BigDecimal discountRate;

    private String feeCalMode;

    private String disCode;

    private String txAcctNo;

    private String bankCode;

    /**
     * 预约订单号
     */
    private String appointmentDealNo;

    /**
     * 交易IP
     */
    private String operIp;

    /**
     * 当前日期
     */
    private Date queryDate;

    public Date getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(Date queryDate) {
        this.queryDate = queryDate;
    }

    public String getAppointmentDealNo() {
        return appointmentDealNo;
    }

    public void setAppointmentDealNo(String appointmentDealNo) {
        this.appointmentDealNo = appointmentDealNo;
    }

    public String getOperIp() {
        return operIp;
    }

    public void setOperIp(String operIp) {
        this.operIp = operIp;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public BigDecimal getSubsAmt() {
        return subsAmt;
    }

    public void setSubsAmt(BigDecimal subsAmt) {
        this.subsAmt = subsAmt;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getShareClass() {
        return shareClass;
    }

    public void setShareClass(String shareClass) {
        this.shareClass = shareClass;
    }

    public BigDecimal getNetBuyAmt() {
        return netBuyAmt;
    }

    public void setNetBuyAmt(BigDecimal netBuyAmt) {
        this.netBuyAmt = netBuyAmt;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public BigDecimal getAppointmentAmt() {
        return appointmentAmt;
    }

    public void setAppointmentAmt(BigDecimal appointmentAmt) {
        this.appointmentAmt = appointmentAmt;
    }

}
