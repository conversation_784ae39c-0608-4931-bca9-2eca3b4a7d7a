/**
 *Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlResponse;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(查询预约信息) 
 * @reason:
 * <AUTHOR>
 * @date 2018年2月11日 下午2:47:02
 * @since JDK 1.6
 */

@Controller
public class QueryPreBookController {
    
    @Autowired
    private QueryPreBookDtlFacade queryPreBookDtlFacade;
    
    @RequestMapping("/tmscounter/high/queryprebook.htm")
    public ModelAndView queryPreBook(HttpServletRequest request, HttpServletResponse response) throws Exception {
        
        //预约信息
        String appointmentDealNo = request.getParameter("appointmentDealNo");
        String disCode = request.getParameter("disCode");
        QueryPreBookDtlRequest queryPreBookDtlRequest = new QueryPreBookDtlRequest();
        queryPreBookDtlRequest.setPreId(appointmentDealNo);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        TmsFacadeUtil.setCommonParameters(queryPreBookDtlRequest, disInfoDto);
        QueryPreBookDtlResponse queryPreBookDtlResponse = queryPreBookDtlFacade.execute(queryPreBookDtlRequest);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("prebook", queryPreBookDtlResponse);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
}

