/**
*撤单
*<AUTHOR>
*@date 2017-04-01 14:56
*/

CONSTANTS.PAYMENT_TYPE_CANCEL_MAP = {"04": "银行卡", "06":"储蓄罐"};
CONSTANTS.PAYMENT_TYPE_CANCEL_CXG = {"06":"储蓄罐"};
CONSTANTS.PAYMENT_TYPE_CANCEL_NONE = {"NONE":"-"};

$(function(){
	Init.init();
	Cancel.init();
	Agent.init();
});

var Cancel = {
	init:function(){
		$("#confimCancelBtn").on('click',function(){
			Cancel.confirm();
		});
		
		$("#queryCustInfoBtn").on('click',function(){
			QueryCustInfo.queryCustInfoByCancel(null, null, null, null, Cancel.selectCustClick);

		});
		
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});

		var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PAYMENT_TYPE_CANCEL_MAP);
		$("#withdrawDirection").html(withdrawDirectionHtml);
	},

	selectCustClick:function(){
		$(".selectcust").click(
			function () {
				$(this).attr('checked', 'checked').siblings().removeAttr('checked');
				var selectIndex = $(this).attr("index");

				QueryCustInfo.custInfo = QueryCustInfo.custInfoDtoList[selectIndex] || {};
				QueryCanCancel.queryCanCancelForApply(QueryCustInfo.custInfo.custNo, null,  Cancel.selectCancelOrderClick);

				if (isEmpty($("#custNo").val())) {
					$("#custNo").val(QueryCustInfo.custInfo.custNo)
				}

				// 判断选择的是个人客户还是机构客户，个人时"是否经办"默认选中否, 机构默认选中是
				Agent.setAgentDivByInvstType(QueryCustInfo.custInfo.invstType);
			});
		$('input[name="checkCust"][index="0"]').click();
	},

	selectCancelOrderClick : function(){
		$(".selectCancleOrder").click(
			function() {
				$(this).attr('checked','checked').siblings().removeAttr('checked');
				var selectIndex = $(this).attr("index");

				var cancelOrder = QueryCanCancel.canCancelOrders[selectIndex] || {};

				console.info("===select Cancel order==> " + JSON.stringify(cancelOrder))

				Cancel.selectWithdrawDirectionClick(cancelOrder);

			});

		var cancelOrderNum = QueryCanCancel.canCancelOrders.length;
		if (cancelOrderNum > 0) {// 默认选择第一个
			$('input[name="orderIndex"][index="0"]').click();
		}
	},

	selectWithdrawDirectionClick : function(data){
		var paymentType = data.paymentType;

		if ('06' == paymentType){
			var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PAYMENT_TYPE_CANCEL_CXG, '06', '', '', true);
			$("#withdrawDirection").html(withdrawDirectionHtml);
		}else if ('09' == paymentType) {
			var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PAYMENT_TYPE_CANCEL_MAP, '06', '', '', true);
			$("#withdrawDirection").html(withdrawDirectionHtml);
		}else if ('01' == paymentType || '04' == paymentType) {
			var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PAYMENT_TYPE_CANCEL_MAP, '04', '', '', true);
			$("#withdrawDirection").html(withdrawDirectionHtml);
		}else {
			var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PAYMENT_TYPE_CANCEL_NONE, 'NONE', '', '', true);
			$("#withdrawDirection").html(withdrawDirectionHtml);
		}
	},

	/***
	 * 确认撤单
	 */	
	confirm : function(){
		var  uri= TmsCounterConfig.CANCEL_FUND_CONFIRM_URL ||  {};
		var selectedOrderIndex = $("input[name='orderIndex'][type='radio']:checked").val();

		CommonUtil.disabledBtn("confimCancelBtn");

		if(CommonUtil.isEmpty(selectedOrderIndex)){
			CommonUtil.layer_tip("请选择要撤单的订单");
			return ;
		}
		
		var selectCancelType = $("#transactorInfoForm #selectCancelType").val();
		var cancelMemo = $("#transactorInfoForm #cancelMemo").val();
		if('2' == selectCancelType){
			if(CommonUtil.isEmpty(cancelMemo)){
				CommonUtil.layer_tip("强制撤单，撤单原因必填");
				return ;
			}
		}
		

		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		var cancelConfirmForm = QueryCanCancel.canCancelOrders[selectedOrderIndex];
		
		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimCancelBtn");
			return false;
		}
		
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		transactorInfoForm.appDtm = transactorInfoForm.appDt +'' + transactorInfoForm.appTm;
		if(!Valid.valiadTradeTime(transactorInfoForm.appTm) && transactorInfoForm.cancelType == '1'){
			CommonUtil.layer_tip("自行撤单申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimCancelBtn");
			return false;
		}

		var withdrawDirection = $("#transactorInfoForm #withdrawDirection").val();
		if(CommonUtil.isEmpty(withdrawDirection) || 'NONE' == withdrawDirection){
			transactorInfoForm.withdrawDirection = null;
		}
		
		var dealAppNo ="";
		if(!(typeof ApplyCancel == "undefined")){
			dealAppNo = ApplyCancel.checkOrder.dealAppNo;
		}
		
		var reqparamters ={"dealAppNo":dealAppNo,
				"transactorInfoForm":JSON.stringify(transactorInfoForm),
				"cancelConfirmForm": JSON.stringify(cancelConfirmForm),
				"custInfoForm":JSON.stringify(QueryCustInfo.custInfo)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Cancel.callBack);
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			if($(".confimBtn").length > 0){
				CommonUtil.disabledBtnWithClass("confimBtn");
				CommonUtil.disabledBtn("abolishBtn");
			}
			CommonUtil.layer_tip("撤单提交成功");
			QueryCanCancel.queryCanCancelForApply(QueryCustInfo.custInfo.custNo);
		}else{
			CommonUtil.layer_tip("撤单提交失败,"+respDesc);
		}
		
		if(!$(".confimBtn").length > 0){
			CommonUtil.enabledBtn("confimCancelBtn");
		}
	}	
};


