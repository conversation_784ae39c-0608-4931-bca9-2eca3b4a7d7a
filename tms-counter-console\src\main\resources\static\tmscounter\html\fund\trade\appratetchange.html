<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>人工干预折扣率</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 特殊业务 <span class="c-gray en">&gt;</span> 人工干预折扣率 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box" id="searchForm">
                <p class="mainTitle mt10">人工干预折扣率</p>
                <div class="cp_top mt30">
                    <span class="normal_span ml30">基金代码：</span>
                    <input type="text" name="fundCode" id="fundCode" placeholder="基金代码">
                    <span class="normal_span ml30">标准业务名称：</span>
                    <span class="select-box inline">
                       <select name="selectCheckFlag" id="selectCheckFlag" class="select">
                       </select>
                    </span>
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryAppRateChangeInfoBtn">查询</a>
                    <a href="javascript:void(0)" class="btn radius btn-success ml30" id="reset">重置</a>
                </div>
                <div class="cp_top mt30">
                    <span class="normal_span ml30">支付方式：</span>
                    <span class="select-box inline">
                       <select name="selectPayFlag" id="selectPayFlag" class="select">
                       </select>
                    </span>
                    
                    <span class="normal_span ml30">申请TA日期：</span>
                    <input id="beginDtm" name="beginDtm" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYY-MM-DD'})">
                    <span>至</span>
                    <input id="endDtm" name="endDtm" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYY-MM-DD'})">
                </div>
            </div>
        </div>
	</div>
	
    <div class="page-container w1000">
        <p class="main_title mt10">基金基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>基金代码</th>
                        <th>基金名称</th>
                        <th>基金类型</th>
                    </tr>
               </thead>
                <tbody id="fundInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                	 	<td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
     </div> 
     
     <div class="page-container w1000">
        <p class="main_title mt10">订单信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                  		<th>上报订单号</th>
                        <th>基金代码</th>
                        <th>基金名称</th>
                        <th>当前折扣率</th>
                        <th>标准业务名称</th>
                        <th>申请金额</th>
                        <th>支付方式</th>
                        <th>申请时间</th>
                        <th>申请TA日</th>
                    </tr>
               </thead>
                <tbody id="rsList">
                </tbody>
            </table>
        </div>
        <div class="clear page_all">
            <div class="fy_part fr mt10" id="pageView"></div>
        </div>
    </div>
    
    <div class="page-container w1000">
        <p class="main_title mt10">修改折扣</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>修改的TA日期</th>
                        <th>修改折扣</th>
                    </tr>
               </thead>
                <tbody>
                	 <tr class="text-c">
                	 	<td id="rateModifyId">--</td>
                	 	<td>
                   			<input type="text" placeholder="请输入修改后的折扣"  name="modifyRateId" id="modifyRateId"  datatype="s"></input>
                   		</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div style="margin:0 auto;width:600px;">
       	<a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="modifyRateBtn">确认提交</a>
    </div>
    
    <div class="page-container w1000">
    </div>
    <div class="page-container w1000">
    </div>
    <div class="page-container w1000">
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20180604"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
	<script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
	<script type="text/javascript" src="../../../js/fund/common/bodyview.js?v=20181011"></script>
    <script type="text/javascript" src="../../../js/fund/trade/appratechange.js?v=20200730"></script>
</body>

</html>