<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.howbuy.tms</groupId>
        <artifactId>tms-counter</artifactId>
        <version>4.8.50-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>tms-counter-common</artifactId>
    <packaging>jar</packaging>
    <name>tms-counter-common</name>
    <dependencies>
		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-service</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.dfile</groupId>
			<artifactId>howbuy-dfile-impl-webdav</artifactId>
		</dependency>
		<!-- servlet -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
	<!-- spring -->
	<dependency>
   			<groupId>org.springframework</groupId>
    		 <artifactId>spring-web</artifactId>
    		 <exclusions>
    		 	<exclusion>
    		 		<artifactId>spring-core</artifactId>
    		 		<groupId>org.springframework</groupId>
    		 	</exclusion>
    		 </exclusions>
		</dependency>
		 <!-- log4j2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
		</dependency>
		
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-slf4j-impl</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-1.2-api</artifactId>
		</dependency>
    </dependencies>

</project>