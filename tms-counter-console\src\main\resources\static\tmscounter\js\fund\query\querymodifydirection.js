/**
 * 修改回款方向
 * <AUTHOR>
 * @date 2023-05-23 14:02
 */
$(function() {

});

var QueryModifyDirection = {
	/**
	 * 查询修改回款方向订单
	 */
	queryModifyDeal: function (custNo, dealNo, appBeginDt, appEndDt, disCode, idNo, hboneNo) {

		var userUri = TmsCounterConfig.QUERY_CUST_INFO_URL || {};
		var dealUri = TmsCounterConfig.QUERY_CAN_MODIFY_DIRECTION_DEAL || {};

		if (!custNo) {
			custNo = $("#custNo").val();
		}

		if (!dealNo) {
			dealNo = $("#dealNo").val();
		}

		if (!appBeginDt) {
			appBeginDt = $("#appBeginDt").val();
		}

		if (!appEndDt) {
			appEndDt = $("#appEndDt").val();
		}

		if (!disCode) {
			disCode = $("#selectDisCode").val();
		}

		if (!idNo) {
			idNo = $("#idNo").val();
		}


		if (!hboneNo) {
			hboneNo = $("#hboneNo").val();
		}

		var userparamters = {};
		var dealparamters = {};

		if (!CommonUtil.isEmpty(custNo)) {
			userparamters.custNo = custNo;
			dealparamters.custNo = custNo;
		}

		if (!CommonUtil.isEmpty(disCode)) {
			userparamters.disCode = disCode;
			dealparamters.disCode = disCode;
		}

		if (!CommonUtil.isEmpty(dealNo)) {
			dealparamters.dealNo = dealNo;
		}
		if (!CommonUtil.isEmpty(appBeginDt)) {
			dealparamters.appBeginDt = appBeginDt;
		}

		if (!CommonUtil.isEmpty(appEndDt)) {
			dealparamters.appEndDt = appEndDt;
		}

		if (!CommonUtil.isEmpty(idNo)) {
			userparamters.idNo = idNo;
			dealparamters.idNo = idNo;
		}

		if (!CommonUtil.isEmpty(hboneNo)) {
			userparamters.hboneNo = hboneNo;
		}

		if (isEmpty(custNo) && isEmpty(idNo) && isEmpty(hboneNo)) {
			showMsg("客户号,证件号必须输入一项");
			return false;
		}
		//时间
		var appBeginDt = $("#appBeginDt").val();
		var appEndDt = $("#appEndDt").val();
		if(isEmpty(appBeginDt)){
			layer_tip("请选择开始时间");
			enableBtn($("#disCountGen"));
			return false;
		}
		if(isEmpty(appEndDt)){
			layer_tip("请选择结束时间");
			return false;
		}

		//时间比较
		if(Number(appEndDt) - Number(appBeginDt) >= 300){
			layer_tip("只能查询最近三个月内的交易，请重新输入开始日期");
			return false;
		}
		var userParam = CommonUtil.buildReqParams(userUri, userparamters, null,
			null, null);

		CommonUtil.ajaxAndCallBack(userParam,
			QueryCustInfo.queryCustInfocallBack);

		var dealParam = CommonUtil.buildReqParams(dealUri, dealparamters, null,
			null, null);
		CommonUtil.ajaxAndCallBack(dealParam,
			QueryModifyDirection.queryCanModifyDeal);
	},

	queryCanModifyDeal : function(data) {

		$("#dealInfoId").empty();
		var dealList = data.body || {};
		if (dealList == null || dealList.length == 0) {
			CommonUtil.layer_tip("未查询到可修改回款方向的交易");
			return false;
		}

		var directionList = {};
		// 个人用户
		if (QueryCustInfo.custInfo.invstType == '1') {
			directionList = CONSTANTS.PERSON_DIRECTION_TYPE_MAP;
		} else {
			directionList = CONSTANTS.JIGOU_DIRECTION_TYPE_MAP;
		}
		var selectModify = CommonUtil.selectOptionsHtml(directionList);
		$("#afterModify").html(selectModify);

		$(dealList).each(function(index, element) {
			var trList = [];
			trList.push('<input class="selectDeal" name="selectDeal" type="checkbox" index="' + index + '">');
			trList.push(CommonUtil.formatData(element.dealNo, '--'));
			trList.push(CommonUtil.formatData(CommonUtil.getMapValue(CONSTANTS.BUSI_CODE_MAP, element.mBusiCode, ''), '--'));
			trList.push(CommonUtil.formatData(element.fundName, '--'));
			trList.push(CommonUtil.formatData(element.fundCode, '--'));
			trList.push(CommonUtil.formatData(element.appDate, '--'));
			trList.push(CommonUtil.formatData(element.appAmt, '--'));
			trList.push(CommonUtil.formatData(element.appVol, '--'));
			trList.push(CommonUtil.formatData(element.tFundCode, '--'));
			trList.push(CommonUtil.formatData(element.tFundName, '--'));
			trList.push(element.mBusiCode = '1236' ? CommonUtil.formatData(element.transferAmt,'--') : '--');
			trList.push(element.mBusiCode = '1236' ? CommonUtil.formatData(element.transferVol,'--') :'--');
			trList.push(CommonUtil.getMapValue(CONSTANTS.TX_ACK_FLAG, element.txAckFlag, '--'));
			trList.push(CommonUtil.formatData(element.appDate, '--'));
			trList.push(CommonUtil.formatData(element.appTime, '--'));
			var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td><td style="display: none" class ="redeemDirection">'+ element.redeemDirection +'</td>' +
				'<td style="display: none" class ="uncheckedCount">'+ element.uncheckedCount +'</td></tr>';

			$("#dealInfoId").append(trHtml);
			//绑定选择事件
			$(".selectDeal").off();
			$(".selectDeal").click(function(){
				$("#projectTable").find(".selectDeal").prop('checked',false);
				$(this).prop('checked',true);
				$("#beforeModify").html('--');
				var redeemDirection = $("#dealInfoId").find("input[type='checkbox']:checked").parent().parent().find(".redeemDirection").html();
				var uncheckCount = $("#dealInfoId").find("input[type='checkbox']:checked").parent().parent().find(".uncheckedCount").html();
				$("#beforeModifyId").val(redeemDirection);
				$("#uncheckCount").val(uncheckCount);
				$.each(CONSTANTS.JIGOU_DIRECTION_TYPE_SHOW_MAP, function (key, value) {
					if (key == redeemDirection) {
						$("#beforeModify").html(value);
					}
				});
			});
		});

	}
}