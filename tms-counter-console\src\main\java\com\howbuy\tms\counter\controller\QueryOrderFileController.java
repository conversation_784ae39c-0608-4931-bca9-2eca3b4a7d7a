/**
 *Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.counter.cmd.QueryOrderFileCmd;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.QueryOrderFileDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @description:(查询CRM线上资料文件)
 * @reason:
 * <AUTHOR>
 * @date 2019年5月24日 下午1:27:55
 * @since JDK 1.6
 */
@Controller
public class QueryOrderFileController {
    //private static Logger logger = LogManager.getLogger(QueryOrderFileController.class);

    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    
    @RequestMapping("/tmscounter/queryorderfile.htm")
    public ModelAndView queryHighWorkDay(HttpServletRequest request, HttpServletResponse response) throws Exception {
        QueryOrderFileContext queryContext = buildQueryContext(request);
        String checkNode = request.getParameter("checkNode");

        QueryOrderFileDto queryOrderFileDto = tmsCounterOutService.queryOrderFile(queryContext,checkNode);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("orderFile", queryOrderFileDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private QueryOrderFileContext buildQueryContext(HttpServletRequest request){
        String condion = request.getParameter("condition");
        if(condion == null){
            return new QueryOrderFileContext();
        }

        QueryOrderFileCmd cmd = JSON.parseObject(condion, QueryOrderFileCmd.class);
        QueryOrderFileContext context = new QueryOrderFileContext();
        BeanUtils.copyProperties(cmd, context);

        return context;
    }
    public static final int MAX_BUFF_SIZE = 1024*1024;

    private static Set<String> supportFileType = new HashSet<>();
    static {
        supportFileType.add(".pdf");
        supportFileType.add(".xls");
        supportFileType.add(".xlsx");
        supportFileType.add(".jpg");
        supportFileType.add(".bmp");
        supportFileType.add(".gif");
        supportFileType.add(".png");
    }

    private static Set<String> supportOnlineViewSet = new HashSet<>();
    static {
        supportOnlineViewSet.add(".pdf");
        supportOnlineViewSet.add(".jpg");
        supportOnlineViewSet.add(".bmp");
        supportOnlineViewSet.add(".gif");
        supportOnlineViewSet.add(".png");
    }

    @RequestMapping("/tmscounter/showorderfile.htm")
    public void showAssetsCertificate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fileName = request.getParameter("fileName");
        fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.name());
        String suffix = fileName.substring(fileName.toLowerCase().lastIndexOf(".") + 1);
        if(!supportFileType.contains(suffix)){
            TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
            Map<String,Object> body = new HashMap<String, Object>(16);
            body.put("msg", fileName + "文件类型不支持");
            rst.setBody(body);
            WebUtil.write(response, rst);
        }

        if(supportOnlineViewSet.contains(suffix)){
            downLoad(fileName, response, true);
        }else{
            downLoad(fileName, response, false);
        }

    }

    public void downLoad(String filePath, HttpServletResponse response, boolean isOnLine) throws Exception {
        File f = new File(filePath);
        if (!f.exists()) {
            response.sendError(404, "File not found!");
            return;
        }
        BufferedInputStream br = new BufferedInputStream(new FileInputStream(f));
        byte[] buf = new byte[MAX_BUFF_SIZE];
        int len = 0;
        // 非常重要
        response.reset();
        // 在线打开方式
        if (isOnLine) {
            URL u = new URL("file:///" + filePath);
            response.setContentType(u.openConnection().getContentType());
            response.setHeader("Content-Disposition", "inline; filename=" + f.getName());
        // 纯下载方式
        } else {
            // 文件名应该编码成UTF-8
             String filename = URLEncoder.encode(f.getName(),StandardCharsets.UTF_8.name());
             response.setContentType("application/x-msdownload");
             response.setContentType("application/octet-stream;charset=UTF-8;");
             response.setHeader("Content-Disposition", "attachment; filename=" + filename);
        }
        OutputStream out = response.getOutputStream();
        while ((len = br.read(buf)) > 0){
            out.write(buf, 0, len);
        }
        br.close();
        out.close();
    }

}

