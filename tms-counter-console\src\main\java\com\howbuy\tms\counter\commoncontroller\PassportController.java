package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.permission.UserService;
import com.howbuy.tms.counter.auth.EasyTypeToken;
import com.howbuy.tms.counter.auth.LoginTypeEnum;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.exception.BaseException;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 
 * @description:登录服务
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年5月21日 下午7:56:11
 * @since JDK 1.6
 */
@Controller
public class PassportController extends AbstractController {
	
    private final static Logger logger = LoggerFactory.getLogger(PassportController.class);
	
	@Autowired
	private UserService userService;
	
	@Autowired
	org.apache.shiro.mgt.SecurityManager securityManager;

    /**
     * 
     * ajaxDone:(TODO 这里用一句话描述这个方法的作用)
     * 
     * @param statusCode
     * @param message
     * @param forwardUrl
     * @return
     * <AUTHOR>
     * @date 2018年5月21日 下午7:57:08
     */
    protected ModelAndView ajaxDone(int statusCode, String message, String forwardUrl) {
        ModelAndView mav = new ModelAndView("ajaxDone");
        mav.addObject("statusCode", statusCode);
        mav.addObject("message", message);
        mav.addObject("forwardUrl", forwardUrl);
        return mav;
    }

    /**
     * 
     * loginView:请求登录页面
     * 
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @date 2018年5月21日 下午7:57:13
     */
    @RequestMapping("tmscounter/loginView.htm")
    public ModelAndView loginView(HttpServletRequest request, HttpServletResponse response) {
        logger.info("进入登录页面...");
        return new ModelAndView("/html/login");
    }

    /**
     * 
     * login:登录请求
     * 
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @date 2018年5月21日 下午7:57:53
     */
    @RequestMapping("tmscounter/login.htm")
	public ModelAndView login(HttpServletRequest request, HttpServletResponse response) {		
        String username = request.getParameter("userName");
		if(username==null || "".equals(username.trim())){
            request.setAttribute("error", "用户名不能为空，请检查！");
            return new ModelAndView("/html/login");
		}
		String password = request.getParameter("password");
        if (password == null || "".equals(password.trim())) {
            request.setAttribute("error", "密码不能为空，请检查！");
            return new ModelAndView("/html/login");
		}	
			
        Subject currentUser = SecurityUtils.getSubject();
		if(!currentUser.isAuthenticated()){
			EasyTypeToken token = new EasyTypeToken(username, password);
			token.setType(LoginTypeEnum.PASSWORD);
            token.setRememberMe(false);
	        try{
	            currentUser.login(token);
			} catch (UnknownAccountException uae) {
                logger.error("There is no user with username of {} ", token.getPrincipal());
                request.setAttribute("error", "用户认证失败，账户或密码错误，请检查！");
                return new ModelAndView("/html/login");
	        } catch (IncorrectCredentialsException ice) {
                logger.error("Password for account{} was incorrect!", token.getPrincipal());
                request.setAttribute("error", "用户认证失败，账户或密码错误，请检查！");
                return new ModelAndView("/html/login");
	        } catch (LockedAccountException lae) {
                logger.error("The account for username{} is locked.  Please contact your administrator to unlock it.", token.getPrincipal());
                request.setAttribute("error", "用户已被锁定，请检查！");
                return new ModelAndView("/html/login");
            } // ... catch more exceptions here (maybe custom ones specific to
              // your application?
            catch (AuthenticationException ae) {
                logger.error(ae.getMessage(), ae);
                request.setAttribute("error", "用户认证失败，请检查！");
                return new ModelAndView("/html/login");
            } catch (BaseException be) {
                logger.error(be.getMessage(), be);
                request.setAttribute("error", be.getMessage());
                return new ModelAndView("/html/login");
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                request.setAttribute("error", "登陆服务异常，请稍后！");
                return new ModelAndView("/html/login");
            }
		}
		if(currentUser.isAuthenticated()) {
		
            String userId = (String) currentUser.getPrincipal();
            UserAccountModel userAccountModel = userService.getUserByUserId(userId);
            request.getSession().setAttribute(Constants.AUTHENTICATION_KEY, userAccountModel);
            request.getSession().setAttribute(Constants.SESSION_USER, userAccountModel);
            
            // 设置登录时间
            UserAccountModel lastLoginModel = new UserAccountModel();
            lastLoginModel.setUserId(userId);
            lastLoginModel.setLastLogTime(new Date());
            lastLoginModel.setMemo("柜台");
            userService.updateUserInfo(lastLoginModel);
            
		}
		ModelAndView mav = new ModelAndView();
        mav.setViewName("redirect:/tmscounter/index.htm");
		return mav;
	}

    /**
     * 
     * logout:登出请求
     * 
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @date 2018年5月21日 下午7:58:16
     */
    @RequestMapping("tmscounter/logout.htm")
    public ModelAndView logout(HttpServletRequest request, HttpServletResponse response) {
        Subject currentUser = SecurityUtils.getSubject();
        currentUser.logout();
		
		request.getSession().removeAttribute(Constants.AUTHENTICATION_KEY);
        request.getSession().removeAttribute(Constants.SESSION_USER);
        request.getSession().invalidate();

        ModelAndView mav = new ModelAndView();
        mav.setViewName("redirect:/tmscounter/loginView.htm");
        return mav;
	}

}
