/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto.base;

import java.io.Serializable;

/**
 * @description:(公共请求)
 * <AUTHOR>
 * @date 2017年3月28日 下午9:06:57
 * @since JDK 1.7
 */
public class BaseRequestDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 6870122919341388759L;

    /**
     * 页码
     */
    private int pageNo = 1;
    /**
     * 每页记录数
     */
    private int pageSize = 20;

    /**
     * 交易IP
     */
    private String operIp;
    /**
     * 产品类别
     */
    private String productClass;
    /**
     * 投资者类型
     */
    private String invstType;
    /**
     * 分销机构号
     */
    private String disCode;
    /**
     * 柜台号
     */
    private String outletCode;
    /**
     * CRM线上资料ID
     */
    private String materialId;

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOperIp() {
        return operIp;
    }

    public void setOperIp(String operIp) {
        this.operIp = operIp;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }
}
