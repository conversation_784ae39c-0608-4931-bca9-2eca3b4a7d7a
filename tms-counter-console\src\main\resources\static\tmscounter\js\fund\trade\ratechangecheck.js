
$(function(){
	var operatorNo = cookie.get("operatorNo");
    Init.init();
	AppRateChangeCheck.init();
});

var AppRateChangeCheck = {
	init:function(){
		//通过
		AppRateChangeCheck.Succ = '1';
		//驳回
		AppRateChangeCheck.Faild = '2';

		$("#succBtn").on('click',function(){
			AppRateChangeCheck.confirm(AppRateChangeCheck.Succ);
		});

		$("#returnBtn").on('click',function(){
			/*layer.prompt({title: '请填写拒绝原因', formType: 1}, function(pass, index){
				layer.close(index);
				//QueryHighForceDeal.reSerSubmitFlag(dealNo,pass);
			});
*/
			layer.prompt({
				formType: 2,
				title: '请填写审核拒绝原因（注：必填项）',
				area: ['500px', '150px'],
				btnAlign: 'c',
				maxlength:100,
				yes: function(index, layero){
					// 获取文本框输入的值
					var value = layero.find(".layui-layer-input").val();
					if (value) {
						if(value.length >100){
							alert("审核拒绝原因不能超过100个字");
							return false;
						}
						AppRateChangeCheck.confirm(AppRateChangeCheck.Faild,value);
						layer.close(index);
					} else {
						alert("请填写审核拒绝原因！");
					}
				}
			});



		});

		var dealAppNo ;
		var checkNode;
		var url = location.search; //获取url中"?"符后的字串
		if (url.indexOf("?") != -1) {
			var str = url.substr(1);
			var strs = str.split("&");
			dealAppNo = decodeURIComponent(strs[0].replace("dealAppNo=", ""));
			checkNode = decodeURIComponent(strs[1].replace("checkNode=", ""));
		}
		AppRateChangeCheck.checkNode = checkNode;
		AppRateChangeCheck.queryDetail(dealAppNo,checkNode);
	},


	queryDetail : function(dealAppNo,checkNode){

		AppRateChangeCheck.queryChangeDiscountCheckOrderById(dealAppNo, AppRateChangeCheck.queryCheckDiscountOrderByIdBack, checkNode, "");

		// 查询明细
		var uri = TmsCounterConfig.QUERY_SUBMIT_APP_ORDER_TRADE_BY_ID_URL || {};
		var reqparamters = {};
		reqparamters.dealAppNo = dealAppNo;
		reqparamters.page = 1;
		reqparamters.pageSize = 5;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxPaging(uri, paramters, AppRateChangeCheck.queryRateChangeCheckInfoCallBack, "pageView");

	},
	queryChangeDiscountCheckOrderById:function(dealAppNo, callBack, checkNode,txCode){
		var  uri= TmsCounterConfig.QUERY_CHANGE_DISCOUNT_CHECK_ORDER_BY_ID_URL  ||  {};
		var reqparamters = {};
		reqparamters.dealAppNo = dealAppNo;
		reqparamters.pageNum = 1;
		reqparamters.pageSize = 100;
		reqparamters.checkNode = checkNode;
		reqparamters.txCode = txCode;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters, false, null, null);
		CommonUtil.ajaxAndCallBack(paramters, callBack);
	},
	queryCheckDiscountOrderByIdBack: function (data) {
		var bodyData = data.body || {};
		AppRateChangeCheck.appCheckOrder = bodyData.checkOrder || {};
		//AppRateChangeCheck.checkDtlOrder = bodyData.checkDtlOrder || [];

		if (CommonUtil.isEmpty(AppRateChangeCheck.appCheckOrder.dealAppNo)) {
			CommonUtil.layer_tip("无此订单");
			return false;
		}

		if (AppRateChangeCheck.checkNode == '1') {
			$('#succBtn').hide();
			$('#returnBtn').hide();
		} else {
			$('#succBtn').show();
			$('#returnBtn').show();
			CommonUtil.enabledBtn("returnBtn");
			CommonUtil.enabledBtn("succBtn");
		}

		AppRateChangeCheck.queryFundInfo(AppRateChangeCheck.appCheckOrder.fundCode);

		$("#modifyRateId").val(AppRateChangeCheck.appCheckOrder.discountRate);
		$("#rateModifyId").html(AppRateChangeCheck.appCheckOrder.taTradeDt);
	},

	queryRateChangeCheckInfoCallBack:function(data){
		var checkDtlOrder = data.dtlOrderDtoList;
		var appCheckOrder = data.counterOrderDto;
		$("#rsCheckList").empty();
		var trHtml = '';
		if(checkDtlOrder.length <= 0){
			trHtml = '<tr><td colspan="13">无查询记录</td></tr>';
			$("#rsCheckList").append(trHtml);
		}else{
			$(checkDtlOrder).each(function(index, element){
				var trList = [];
				trList.push(element.submitDealNo);
				trList.push(element.fundCode);
				trList.push(element.fundName);
				trList.push(element.afterDiscountRate);//当前折扣率
				// trList.push('申购');//标准业务名称
				trList.push(CommonUtil.getMapValue(CONSTANTS.BUSI_CODES_MAP, element.busiCode));//标准业务名称
				if(element.appVol > 0){
					trList.push(CommonUtil.formatData(element.appVol));
				}else {
					trList.push('--');
				}
				// trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_ALL_TYPE, appCheckOrder.paymentType));//支付方式
				trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_ALL_TYPE, element.paymentType));//支付方式
				trList.push(appCheckOrder.appDt + ' ' +  appCheckOrder.appTm);//申请时间
				trList.push(appCheckOrder.taTradeDt);//申请TA日
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#rsCheckList").append(trAppendHtml);
			});
		}

	},
	/**
	 * 查询基金信息
	 *
	 * fundCode 基金代码毕传
	 */
	queryFundInfo:function(fundCode){
		var uri= TmsCounterConfig.QUERY_FUND_INFO_URL ||  {};
		var reqparamters = {"fundCode":fundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, AppRateChangeCheck.queryFundInfoCallBack);
	},

	/**
	 * 处理基金信息
	 */
	queryFundInfoCallBack:function(data){

		var bodyData = data.body || {};
		var fundInfo = bodyData.fundInfo || {};
		AppRateChangeCheck.fundInfo = fundInfo;

		$("#fundInfoId").empty();
		var trHtml = '<tr class="text-c"><td>' + AppRateChangeCheck.fundInfo.fundCode + '</td><td>' + AppRateChangeCheck.fundInfo.fundAttr + '</td><td>' + CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, AppRateChangeCheck.fundInfo.fundType) + '</td></tr>';
		$("#fundInfoId").append(trHtml);

	},
	/***
	 * 审核确认
	 */
	confirm : function(checkStatus,checkFaildDesc){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';


		var uri= TmsCounterConfig.CHECK_CHANGE_DISCOUNT_CONFIRM_URL ||  {};

		if(AppRateChangeCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty(checkFaildDesc)){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入退回原因");
				return false;
			}
			AppRateChangeCheck.checkFaildDesc = checkFaildDesc;
		}

		var reqparamters ={
			"checkFaildDesc":AppRateChangeCheck.checkFaildDesc || '',
			"checkStatus":checkStatus,
			"checkedOrderForm":JSON.stringify(AppRateChangeCheck.appCheckOrder)


		};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, AppRateChangeCheck.callBack);
		return true;
	},

	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_alert(respDesc);
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_alert(respDesc);
		}
	}
};