/**
 * 
 */
package com.howbuy.tms.counter.common.exception;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础异常定义
 * <AUTHOR>
 * @date 2016-10-14
 *
 */
public class BaseException extends RuntimeException {
	/**
	 * contentDesc的messageFormat参数
	 */
	protected List<String> args = new ArrayList<String>();

	/**
	 * 错误代码
	 */
	private String code;
	/**
	 * 错误描述
	 */
	private String desc;

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public BaseException(String code,String desc) {
		super(code+":"+desc);
		this.code = code;
		this.desc = desc;
	}
	

	public BaseException(String code,String desc,Throwable e) {
		super(code+":"+desc,e);
		this.code = code;
		this.desc = desc;
	}


	public String getCode() {
		return code;
	}


	public String getDesc() {
		return desc;
	}

	public List<String> args() {
        return args;
    }

    public void addParameter(String s){
        this.args.add(s);
    }
	
	
}
