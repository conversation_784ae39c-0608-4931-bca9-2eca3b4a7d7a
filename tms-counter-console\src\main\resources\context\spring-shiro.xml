<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:mvc="http://www.springframework.org/schema/mvc"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <description>Spring Shiro</description>
	
	<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
		<property name="securityManager" ref="securityManager" />
		<property name="loginUrl" value="/loginview" />
		<property name="successUrl" value="/management" />
		<property name="unauthorizedUrl" value="/unauthorized" />

		<property name="filters">
			<!-- 默认的authc对应的FormAuthenticationFilter，会对loginUrl的Post请求自动处理登录，PassThruAuthenticationFilter则不会。 -->
			<map>
				<entry key="authc">
					<bean class="com.howbuy.tms.counter.filter.AccessAuthenticationFilter"/>
				</entry>
			</map>
		</property>

		<property name="filterChainDefinitions">
			<value>
				/tmscounter/login.htm = anon
				/tmscounter/loginView.htm = anon
				/tmscounter/logout.htm = anon
				/tmscounter/selflogin.htm = anon
				/passport/login = anon
				/images/** = anon
				/error/** = anon
				/static/** = anon
				/versionnewbatch/** = anon
				/js/** = anon
				/lib/** = anon
				/styles/** = anon
				/fckeditor/** = anon
				/** = authc
				# more URL-to-FilterChain definitions here
			</value>
		</property>
	</bean>
	
	<bean id="authorizationInfoRealm" class="com.howbuy.tms.counter.auth.AuthorizationRealm">
		<property name="credentialsMatcher" ref="myCredentialsMatcher"/>
	</bean>
	<bean id="myCredentialsMatcher" class="com.howbuy.tms.counter.auth.MyHashedCredentialsMatcher">
		<property name="hashAlgorithmName" value="MD5"/>
		<property name="hashIterations" value="1"/>
	</bean>
	<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
		<property name="cacheManager" ref="shiroEhcacheManager"/>
		<property name="realm" ref="authorizationInfoRealm" />
	</bean>
	
	<bean id="shiroEhcacheManager" class="org.apache.shiro.cache.MemoryConstrainedCacheManager"/>
    
	<bean id="lifecycleBeanPostProcessor" class="org.apache.shiro.spring.LifecycleBeanPostProcessor"/>
	<bean class="org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator" depends-on="lifecycleBeanPostProcessor"/>
	<bean class="org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor">
	    <property name="securityManager" ref="securityManager"/>
	</bean>
</beans>