package com.howbuy.tms.counter.dto;


import java.io.Serializable;
import java.util.List;

/**
 * 
* @Title: QueryAgreementModel.java 
* @Description: 查询协议返回值 
* <AUTHOR>
* @date 2021年7月14日 下午3:12:00 
*
 */
public class QueryAgreementDto implements Serializable{

	private static final long serialVersionUID = -4500974107478167752L;
	/**
	 * 匹配的概要
	 */
	List<ComplianceDto> outlineList;
	/**
	 * 匹配的协议
	 */
	List<ComplianceDto> agreementList;

	/**
	 * 匹配基金合同
	 */
	List<ComplianceDto> fundContractList;

	/**
	 * 匹配招募说明书
	 */
	List<ComplianceDto> introduceList;

	public List<ComplianceDto> getOutlineList() {
		return outlineList;
	}

	public void setOutlineList(List<ComplianceDto> outlineList) {
		this.outlineList = outlineList;
	}

	public List<ComplianceDto> getAgreementList() {
		return agreementList;
	}

	public void setAgreementList(List<ComplianceDto> agreementList) {
		this.agreementList = agreementList;
	}

	public List<ComplianceDto> getFundContractList() {
		return fundContractList;
	}

	public void setFundContractList(List<ComplianceDto> fundContractList) {
		this.fundContractList = fundContractList;
	}

	public List<ComplianceDto> getIntroduceList() {
		return introduceList;
	}

	public void setIntroduceList(List<ComplianceDto> introduceList) {
		this.introduceList = introduceList;
	}
}
