package com.howbuy.tms.counter.common.util;


import com.google.common.collect.Maps;
import com.howbuy.dfile.HFileService;
import com.howbuy.dfile.internal.config.StoreConfig;
import com.howbuy.dfile.internal.config.StoreServer;
import com.howbuy.dfile.internal.config.processor.StoreConfigProcessor;
import com.howbuy.dfile.internal.config.processor.StoreServerProcessor;
import com.howbuy.dfile.internal.dto.FileConfigDTO;
import com.howbuy.dfile.internal.enums.ModeNameEnum;
import com.howbuy.tms.counter.common.enums.WebDevEnum;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

public class WebDevFileUtils {

    private static final Logger logger = LoggerFactory.getLogger(WebDevFileUtils.class);

    private static HFileService hFileService = HFileService.getInstance();

    private static final Map<String, String> storeServerFilePathMap = Maps.newHashMap();
    static {
        storeServerFilePathMap.put("webdav_server_files", "/data/files");
    }

    /**
     * 
     * read2Bytes:文件流读取
     * @param businessCode
     * @param path
     * @param fileName
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:52:35
     */
    public static byte[] read2Bytes(String businessCode, String path, String fileName) {
        byte[] frontBytes = null;
        // 读取身份证获取流对象
        try {
            frontBytes = HFileService.getInstance().read2Bytes(businessCode, path, fileName);
        } catch (IOException e) {
            logger.error("FileUtils>>>read2Bytes error {}", log(businessCode, path, fileName), e);
            logger.error("FileUtils>>>read2Bytes error:", e);
        }
        return frontBytes;
    }

    
    /**
     * 
     * write:写入文件 byte
     * @param businessCode
     * @param path
     * @param fileName
     * @param context
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:02
     */
    public static boolean write(String businessCode, String path, String fileName, byte[] context) {
        try {
            hFileService.write(businessCode, path, fileName, context);
            logger.info("FileUtils>>>write success {}  ", log(businessCode, path, fileName));
            return true;
        } catch (Exception e) {
            logger.error("FileUtils>>>write error {}", log(businessCode, path, fileName), e);
            logger.error("FileUtils>>>write error:", e);
        }
        return false;
    }


    /**
     * 
     * write:写入文件 String
     * @param businessCode
     * @param path
     * @param fileName
     * @param context
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:18
     */
    public static boolean write(String businessCode, String path, String fileName, String context) {
        try {
            hFileService.write(businessCode, path, fileName, context);
            logger.info("FileUtils>>>write success {}  ", log(businessCode, path, fileName));
            return true;
        } catch (Exception e) {
            logger.error("FileUtils>>>write error {}", log(businessCode, path, fileName), e);
            logger.error("FileUtils>>>write error:", e);
        }
        return false;
    }

    public static boolean write(String businessCode, String path, String fileName, InputStream inputStream) {
        try {
            hFileService.write(businessCode, path, fileName, inputStream);

            return true;
        } catch (Exception e) {
            logger.error("FileUtils>>>write>>businessCode={} path={} fileName={} error", businessCode, path, fileName, e);
        }
        return false;
    }
    
    /**
     * 
     * log:路径日志
     * @param businessCode
     * @param path
     * @param fileName
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:41
     */
    private static String log(String businessCode, String path, String fileName) {
        String absolutePath = getAbsolutePath(businessCode, path, fileName);
        return String.format(" businessCode=%s, path=%s, fileName=%s, absolutePath=%s", businessCode, path, fileName, absolutePath);
    }
    
    /**
     * 
     * getAbsolutePath:获取绝对路径
     * @param businessCode
     * @param path
     * @param fileName
     * @return
     * <AUTHOR>
     * @date 2024年2月28日 上午10:46:53
     */
    public static String getAbsolutePath(String businessCode, String path, String fileName) {
        try {
            return hFileService.getAbsolutePath(businessCode, path, fileName);
        } catch (Exception e) {
            logger.error("FileUtils>>>getAbsolutePath>>businessCode={} path={} fileName={} error", businessCode, path, fileName, e);
            logger.error("FileUtils>>>getAbsolutePath>>>write error:", e);
        }
        return "";
    }

    /**
     * 获取文件绝对路径
     * @param path
     * @param fileName
     * @return 最后返回 /data/xxxx/xxxx
     */
    public static String getDataAbsolutePath(WebDevEnum webDevEnum, String path, String fileName) {
        String businessCode = webDevEnum.getCode();
        try {

            String absolutePath = hFileService.getAbsolutePath(businessCode, path, fileName);

            StoreConfig storeConfig = StoreConfigProcessor.getInstance().getStoreConfig(businessCode);
            StoreServer storeServer = StoreServerProcessor.getInstance().getStoreServer(storeConfig.getStoreServer());
            if (ModeNameEnum.WEBDAV.name().equalsIgnoreCase(storeServer.getType())) {
                String nfsPathPrefix = storeServerFilePathMap.get(storeConfig.getStoreServer());
                if (StringUtils.isNotBlank(nfsPathPrefix) && absolutePath.startsWith(storeServer.getServerPath())) {

                    absolutePath = absolutePath.replace(storeServer.getServerPath(), nfsPathPrefix);

                    String relativeDir = storeConfig.getRelativeDir();
                    if (StringUtils.isNotBlank(relativeDir) && !StringUtils.equals(File.separator, relativeDir)) {
                        absolutePath = absolutePath.replace(relativeDir, webDevEnum.getName());
                    }

                }
            }

            return absolutePath;
        } catch (Exception e) {
            logger.error("FileUtils>>>getAbsolutePath>>businessCode={} path={} fileName={} error", businessCode, path, fileName, e);
        }

        return "";
    }

    /**
     * @param filePath 全路径 path+文件名
     * @return String
     * @Decription:获取文件名称相对路径
     * <AUTHOR>
     * @date 2023年6月19日 上午15:23:12
     */
    public static FileConfigDTO getFileConfigByKey(String storeConfigKey, String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return null;
        }

        filePath = getWebdavFilePath(storeConfigKey, filePath);

        FileConfigDTO fileConfigDTO = null;
        try {
            fileConfigDTO = hFileService.findDFilePath(storeConfigKey, filePath);
        } catch (Exception e) {
            logger.error("FileUtils>>>findDFilePath error {}", log(storeConfigKey, filePath, ""), e);
        }
        return fileConfigDTO;
    }


    /**
     * nfs的全路径  替换 成webdav 的全路径
     * @param businessCode
     * @param filePath
     * @return  给路径 头加上webdav的serverPath
     */
    public static String getWebdavFilePath(String businessCode, String filePath) {
        StoreConfig storeConfig = StoreConfigProcessor.getInstance().getStoreConfig(businessCode);
        StoreServer storeServer = StoreServerProcessor.getInstance().getStoreServer(storeConfig.getStoreServer());
        String serverPath = storeServer.getServerPath();

        if (ModeNameEnum.WEBDAV.name().equalsIgnoreCase(storeServer.getType())) {
            String nfsPathPrefix = storeServerFilePathMap.get(storeConfig.getStoreServer());
            if (StringUtils.isNotBlank(nfsPathPrefix) && filePath.startsWith(nfsPathPrefix)) {
                filePath = filePath.replace(nfsPathPrefix, serverPath);
            }
        }

        return filePath;
    }
    
}
