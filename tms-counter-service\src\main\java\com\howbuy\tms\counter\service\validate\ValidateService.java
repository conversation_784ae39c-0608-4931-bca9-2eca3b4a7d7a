/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.service.validate;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:(校验服务) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年8月31日 上午10:46:07
 * @since JDK 1.6
 */
@Service("validateService")
public class ValidateService {
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    public void  validateProductInfo(String fundCode){
        HighProductBaseInfoBean highProduct =  queryHighProductOuterService.getHighProductBaseInfo(fundCode);
        if(highProduct  == null){
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }
    }
}

