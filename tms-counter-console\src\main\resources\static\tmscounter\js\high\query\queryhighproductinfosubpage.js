/**
*查询高端产品信息
*
**/

/**
 * 初始化页面事件
 */

$(function(){
	// 初始化下拉框
	QueryHighProdInfoSubPage.initSelect();
	
	// 初始化按钮时间
	QueryHighProdInfoSubPage.initBtn();	
});

var QueryHighProdInfoSubPage = {
		
		/**
		 * 初始化按钮绑定事件
		 */
		initBtn:function(){
			$("#queryProductBtn").off();
		    $("#queryProductBtn").on('click',function(){
		    	QueryHighProdInfoSubPage.queryProductInfo();
		    });
		    
		    $("#resetProductLayerBtn").off();
		    $("#resetProductLayerBtn").on('click',function(){
		    	QueryHighProdInfoSubPage.reset('searchProductFormId');
		    });
		    
		    //给父页面传值
		    $('#submitProdBtn').on('click', function(){
		        var checkedInputs = $("input[class='selectProduct'][type='checkbox']:checked");
		    	if(checkedInputs.length <= 0){
		    		showMsg("必须选中一个产品");
		    		return false ;
		    	}else if(checkedInputs.length > 1){
		    		showMsg("不能多选，只能选择一个产品");
		    		return false;
		    	}else{
		    		var productCode = $(checkedInputs[0]).val();
		    		var targetBackId = parent.$('#parentTargetFundId').val();
		    		parent.$('#'+targetBackId).val(productCode);
		    		
		    		$("#layerrs").empty();
		    		QueryHighProdInfoSubPage.reset('searchProductFormId');
		    		parent.layer.closeAll();		
		    	}
		     });
		},
		
		/**
		 * 初始化下拉框
		 */
		initSelect:function(){
			var selectProductChannelHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PRODUCT_CHANNEL_MAP);
			$("#selectProductChannel").html(selectProductChannelHtml);
		},
		/**
		 * 产品信息查询弹出
		 */
		selectProductCode:function(targetobj , targetBackId){
			
			//父页面目标回写对象
			targetBackId = targetBackId ||  'fundCode';
			$("#parentTargetFundId").val(targetBackId);
			
			layer.open({
				  type: 2,
				  shade: [0.1 ,'#fff'],
				  title:'产品信息查询',
				  area: ['800px', '400px'],
				  offset: 'auto',
				  content: '../../../html/high/query/queryhighproductinfosubpage.html',
				});
		},
		/**
		 * 定位产品信息
		 */
		queryProductInfo:function(){
			var searchProductFormId = $("#searchProductFormId").serializeObject();
			var productCode = searchProductFormId['productCode'];
			var productName = searchProductFormId['productName'];
			var productChannel = searchProductFormId['productChannel'];
			
			if(isEmpty(productCode) && isEmpty(productName)){
				showMsg("产品代码或者产品名称必须输入一项");
				return false;
			}
			
			var uri= TmsCounterConfig.HIGH_QUERY_PRODUCT_SUB_PAGE  ||  "";
			var reqparamters = {};
			if(!isEmpty(productCode)){
				reqparamters.productCode = productCode;
			}else if(!isEmpty(productName) ){
				reqparamters.productName = encodeURIComponent(productName);
			}
			
			if(!isEmpty(productChannel)){
				reqparamters.productChannel = productChannel;
			}
			
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);

			CommonUtil.ajaxAndCallBack(paramters, QueryHighProdInfoSubPage.processProdInfoView);
			
		},
		/**
		 * 重置查询条件
		 * @param formId
		 */
		reset:function(formId){
			$("#"+formId).find("input").each(function(index,element){
				$(element).val('');
			});
			
			$("#"+formId).find("select").each(function(index,element){
				$(element).val('');
			});
		},
		
		/**
		 *渲染产品信息查询结果
		 */
		processProdInfoView:function(data){
			var bodyData = data.body || {};
			var productList = bodyData.productList || [];
			var len = productList.length;
			var appendHtml = '';
			$("#layerrs").empty();
			if(len <= 0){
				appendHtml = '<tr><td colspan="5">没有查询结果</td><tr>';
				$("#layerrs").append(appendHtml);
			}
			else{
				$(productList).each(function(index,element){
					var appendHtml = '<tr class="text-c">'+
									'<td><input type="checkbox" class="selectProduct" value="'+element.fundCode+'"/></td>'+
									 '<td>'+formatData(element.fundCode)+'</td>'+
									 '<td>'+formatData(element.fundAttr)+'</td>'+
									 '<td>'+getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, formatData(element.productChannel))+'</td>'+
									 '<td>'+formatData(element.fundRiskLevel)+'</td>'+
									 '<td>'+getMapValue(CONSTANTS.PRODUCT_TYPE_MAP,formatData(element.productType))+'</td>'+
									'<tr>';
					$("#layerrs").append(appendHtml);
				});		
			}
		}
};