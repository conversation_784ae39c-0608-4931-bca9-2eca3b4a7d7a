/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.common;

/**
 * (请在此添加描述)
 * <AUTHOR>
 * @date 2023/6/5 17:05
 * @since JDK 1.8
 */
public class Constants {

    public static final String NEXT_LINE = "<br/>";
    public static final String TRANS_IN_FAIL =  "转托管转入失败<br/>失败原因:";
    public static final String TRANS_IN_SUC =  "转托管转入成功";
    public static final String FUND_STR =  "基金: ";
    public static final String NUM_STR =  "条";
    public static final String FAIL_STR =  "失败";
    public static final String PENSION_CARD_ATTR =  "6";
    public static final String SESSION_USER = "user";
}
