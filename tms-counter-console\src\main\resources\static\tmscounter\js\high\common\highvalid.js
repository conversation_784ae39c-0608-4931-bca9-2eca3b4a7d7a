var HighValid = {
	     /**
		 * 校验成功后下单
		 * @param custNo
		 * @param fundCode
		 * @param btn 按钮
		 * @param callBack
		 * @returns
		 */
		orderValid:function(custNo,fundCode,btn,callBack){
			var uri = TmsCounterConfig.HIGH_VALID_RISK_URL;
			var paramters = CommonUtil.buildReqParams(uri,{"custNo":custNo,"fundCode":fundCode},false);
			mathRiskFlag = false;
			var rst = {"mathRiskFlag":mathRiskFlag,"status":false};
			
			CommonUtil.disabledBtn(btn);
			CommonUtil.ajaxAndCallBack(paramters, function(data){
				CommonUtil.enabledBtn(btn);
				
				var respCode = data.code || '';
				var desc =data.desc || '';
				var body = data.body || {};
				
				if(CommonUtil.isSucc(respCode)){
					rst.mathRiskFlag = body.mathRiskFlag;
					rst.isDefautCxg = body.isDefautCxg;
					rst.status = true;
					
				}else{
					CommonUtil.layer_tip(respCode+"("+desc+")");
				}
			});
			
			return rst;
			
		},
		
		/**
		 * 
		 * @param txAcctNo
		 * @param fundCode
		 * @param disCode
		 * @param appDt
		 * @param appTm
		 * @param busiType
		 * @param btn
		 */
		repeatValid:function(txAcctNo, fundCode, disCode, appDt,appTm, busiType, btn){
			var uri = TmsCounterConfig.HIGH_REPEAT_APP_VALID_URL ;
			var reqParamter = {};
			reqParamter.txAcctNo = txAcctNo;
			reqParamter.fundCode = fundCode;
			reqParamter.disCode = disCode;
			reqParamter.appDt = appDt;
			reqParamter.appTm = appTm;
			reqParamter.busiType = busiType;
			
			var paramters = CommonUtil.buildReqParams(uri,reqParamter,false);
			mathRiskFlag = false;
			var rst = {"status":false};
			
			CommonUtil.disabledBtn(btn);
			CommonUtil.ajaxAndCallBack(paramters, function(data){
				CommonUtil.enabledBtn(btn);
				
				var respCode = data.code || '';
				var desc =data.desc || '';
				var body = data.body || {};
				
				if(CommonUtil.isSucc(respCode)){
					rst.repeateFlag = body.repeateFlag;
					rst.status = true;
					
				}else{
					CommonUtil.layer_tip(respCode+"("+desc+")");
				}
			});
			
			return rst;
			
		}
};

