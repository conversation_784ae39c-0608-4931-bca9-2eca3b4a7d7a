/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(中台柜台首页) 
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */

public class IndexController {
    
    @RequestMapping("tmscounter/index.htm")
    public ModelAndView index(HttpServletRequest request,HttpServletResponse response) throws IOException{
        //操作员代码
        String operatorNo = request.getParameter("operatorNo");
        //操作员名称
        String operName = request.getParameter("operName");
        //权限
        String role = request.getParameter("role");
        OperatorInfoCmd operatorInfoCmd = new OperatorInfoCmd();
        operatorInfoCmd.setOperatorNo(operatorNo);
        operatorInfoCmd.setOperName(operName);
        operatorInfoCmd.setRole(role);
        SessionUtil.setValue(TmsCounterConstant.SESSION_OPERATORINFO, operatorInfoCmd,request);
        Map<String,Object> rst = new HashMap<String,Object>(16);
        rst.put("code", TmsCounterResultEnum.SUCC.getCode());
        rst.put("desc", TmsCounterResultEnum.SUCC.getDesc());
        WebUtil.write(response, rst);
        return null;
    }
}

