<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/style.css" />
    <title>认申购(高端)</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 我的交易申请（高端）<span class="c-gray en">&gt;</span> 认申购 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container w1000">
        <p class="main_title mt30 cust_info" >客户基本信息</p>
        <div class="result2_tab cust_info" >
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>风险测评结果</th>
                        <th>开户分销机构</th>
                       <th>私募合格投资者认证</th>
                       <th>资管合格投资者认证</th>
                        <th>客户状态</th>
                        <th>投资者类型</th>
                        <th>协议回款方式</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                </tbody>
            </table>
        </div>
        
        <p class="main_title mt30">客户预约信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>预约单号</th>
                        <th>预约产品代码</th>
                        <th>预约产品名称</th>
                        <th>预约业务</th>
                        <th>预约日期</th>
                        <th>预约时间</th>
                        <th>预约金额(不含费)</th>
                        <th>预约份额</th>
                        <th>预约折扣</th>
                        <th>预约单状态</th>
                    </tr>
               </thead>
                <tbody class="text-c" id="rsList">
                		<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                </tbody>
            </table>
             <div class="clear page_all">
            <div class="fy_part fr mt20" id="pageView"></div>
        </div>
        </div>
        <p class="main_title mt30" id="showMaterial">柜台材料信息</p>
        <div class="result2_tab" id="onLineMaterial">
        </div>

        <p class="main_title mt30">录入订单信息</p>
        <form action="" id="orderFormId">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                
                	<tr class="text-c">
                        <td>TA代码</td>
                        <td id="taCodeId">
                        <td>业务名称</td>
                        <td id="buyBusiTypeId">
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>基金代码</td>
                        <td>
                            <div class="searchIn">
                            <input type ="hidden" id="dealAppNo" name="dealAppNo">
                            <input type="text" name="fundCode" id="fundCode"  placeholder="双击查询产品代码">
                            <a href="javascript:void(0)" class="searchIcon"></a>
                            </div>
                        </td>
                        
                        <td>基金简称</td>
                        <td id="fundName" >--</td>
                        
                    </tr>
                    
                     <tr class="text-c">
                        <td>产品通道</td>
                        <td id="productChannelId" >--</td>
                        <td>产品风险等级</td>
                        <td id="fundRiskLevel" >--</td>
                     </tr>
                    
                    <tr class="text-c">
                        <td>产品类型</td>
                        <td id="productTypeId">
                        </td>
                        <td>基金状态</td>
                        <td id="fundStatus">--</td>
                        
                    </tr>
                    
                    <tr class="text-c">                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
                        <td>收费方式</td>
                        <td id="shareClassId" >--</td>
                        <td>银行账号</td>
                        <td id="bankAcctNoId">
                             <span class="select-box inline">
                                <select name="cpAcctNo" class="select" id="selectCustBank" isnull="false" datatype="s" errormsg="银行卡">
                                    <option value="">请选择</option>
                                </select>
                            </span>
                        </td>
                    </tr>
                    
                    <tr class="text-c">
                        <td>净申请金额</td>
                        <td>
                           <div class="convertCon">
                               <input type="text" placeholder="请输入" id="applyAmount" class="applyAmount" name="appAmt" isnull="false" datatype="s" errormsg="净申请金额">
                           </div>
                        </td>
                        <td>大写净金额</td>
                        <td id = "convertAmtId">
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>缴款金额</td>
                        <td>
                            <div class="convertCon">
                                <input type="text" readonly="readonly" id="payRatioAmount" datatype="s" errormsg="缴款金额">
                                <input type="hidden"  id="payRatioId" />
                            </div>
                        </td>
                        <td></td>
                        <td id = "" class="readText">
                        </td>
                    </tr>
                    <tr class="text-c">                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
                        <td>首次最低购买净金额</td>
                        <td id="netMinAppAmtId" >--</td>
                        <td>最低追加申请净金额</td>
                        <td id="netMinSuppleAmtId">--</td>
                    </tr>
                    
                    
                    <tr class="text-c">
                        <td>申请折扣率</td>
                        <td>
                            <input type="hidden" name="fee">
                            <input type="hidden" name="feeRate">
                            <input type="hidden" name="applyAmountIncluFee">
                            <input id ="discountRate" class="discountRate" name="discountRate" type="text" datatype="s" errormsg="申请折扣率"  readonly="readonly">
                        </td>
                        <td>申请总金额</td>
                        <td id="applyAmountIncluFee"></td>
                        
                    </tr>
                    <tr class="text-c">
                   		 <td>标准费率</td>
                        <td>             
							<input id="originalFeeRate" class="originalFeeRate" name="originalFeeRate" type="text" datatype="s" errormsg="原始费率" readonly="readonly" >
						</td>
                        <td>费用</td>
                        <td id="feeId">
                        </td>
                    </tr>
                   
                     <tr class="text-c">
                        <td>预约开始日期</td>
                        <td id="appointStartDtId">
                        </td>
                       <td>开放开始日期</td>
                        <td id="openStartDtId">
                        </td>
                    </tr>
                    
                     <tr class="text-c">
                        <td>预约结束日期</td>
                        <td id="apponitEndDtId"></td>
                        
                        <td>开放结束日期</td>
                        <td id="openEndDtId"></td>
                     </tr>
                    
                     <tr class="text-c">
                     	<td>下单日期</td>
                        <td>
                            <input class="input-text laydate-icon" onclick="laydate({istime: false, format: 'YYYYMMDD'})"  id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" />
                        </td>
                        
                        <td>手续费计算方式</td>
                        <td id="feeCalModeId"></td>
                     </tr>   
                     <tr class="text-c">
                    	 <td>下单时间</td>
                         <td>
                            <input class="" type="text" id="appTm" name="appTm" isnull="false" datatype="time" errormsg="下单时间" maxlength="6"  />
                         </td>
                         <td>支付方式</td>
                          <td>自划款<input type="hidden" name="paymentType" value="01"/></td>
                     </tr>
                     
                      <tr class="text-c">
                        <td>币种</td>
                        <td id="currencyId">
                       		 人民币
                        </td>
                          <td>认缴金额</td>
                          <td>
                              <div class="convertCon">
                                  <input type="text" placeholder="请输入" id="subsAmt" class="subsAmt" name="subsAmt" datatype="s" errormsg="认缴金额">
                              </div>
                          </td>
                          <td id="redeemExpireDescId"></td>
                          <td id="redeemExpireTd">

                          </td>
                    </tr>
                </tbody>
            </table>
        </div>
        </form>
        <p class="main_title mt30">其他信息</p>
        <form id="otherFrom">
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                 <tr class="text-c">
                        <td> 网点：</td>
                        <td>
                        	柜台
                       </td>
                        <td>投资顾问代码：</td>
              			<td>
              			  <span class="select-box inline">
                   			 <select name="consCode" class="select selectconsCode">
                    		</select>
                		</span>
                		</td>
             <tr class="text-c">
              <td>是否代理：</td>
               <td> <span class="select-box inline">
                   <select name="agentFlag" class="select selectAgened">
                      <option value="0">否</option>
                      <option value="1">是</option>
                   </select>
                </span></td>
                <td></td>
                 <td></td>
                 <tr class="text-c">
                     <td>审核人</td>
                     <td id="checker"></td>
                     <td>驳回原因</td>
                     <td id="memo"></td>
                 </tr>
            </tbody>
          </table>
        </div>
        </form>
        
         <form action="" id="transactorFormId">
         <p class="main_title mt30">经办人信息</p>
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                 <tr class="text-c">
                        <td>经办人姓名：</td>
                        <td>
                       <input type="text" placeholder="请输入" id="transactorName" name="transactorName" isnull="false" datatype="s" errormsg="经办人姓名">
                       </td>
                        <td>经办人证件类型：</td>
                   <td>
                  <span class="select-box inline">
                    <select id="transactorIdType" name="transactorIdType" id="transactorIdType" class="select selectTransactorIdType"  isnull="false" datatype="s" errormsg="经办人证件类型" >
                    </select>
                </span>
                </td>
             <tr class="text-c">
              <td>经办人证件号：</td>
               <td> <input type="text" placeholder="请输入" id="transactorIdNo" name="transactorIdNo" isnull="false" datatype="s" errormsg="经办人证件号" ></td>
                <td></td>
                 <td></td>
            </tbody>
          </table>
        </div>
         </form>
         
         <p class="mt30 text-c" id="submitDiv">
            <a href="javascript:void(0)" id ="modifyBtn" class="btn radius btn-warning ml30">提交</a>
            <a href="javascript:void(0)" id ="cancelBtn" class="btn radius btn-secondary ml30">作废</a>
            <a href="javascript:void(0)" id ="backBtn" class="btn radius btn-success ml30">返回</a>
        </p>
    </div>
    
    <input type="hidden" id="parentTargetFundId" />
     
    <script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../../lib/math/Math.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../../js/common.js"></script>
    <script type="text/javascript" src="../../../../js/config.js"></script>
    <script type="text/javascript" src="../../../../js/commonutil.js"></script>
    <script type="text/javascript" src="../../../../js/valid.js"></script>
    <script type="text/javascript" src="../../../../js/high/conscode.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/custinfo.js"></script>
    <script type="text/javascript" src="../../../../js/high/query/queryhighproductinfosubpage.js"></script>
    <script type="text/javascript" src="../../../../js/high/query/queryhighproduct.js"></script>
    <script type="text/javascript" src="../../../../js/high/check/viewcounterdeal.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/onlineorderfile.js"></script>
    <script type="text/javascript" src="../../../../js/high/modify/modify.js"></script>
    <script type="text/javascript" src="../../../../js/high/modify/buymodify.js?v=3.9.54"></script>
    <script type="text/javascript" src="../../../../js/high/common/init.js?v=3.9.54"></script>

</body>

</html>