/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;

import com.howbuy.interlayer.product.model.appointment.ProductAppointmentInfoModel;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.dto.CounterPortfolioProductDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.CustomerAppointmentInfoDto;
import com.howbuy.tms.counter.dto.FundInfoAndNavDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.OtherInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;

import java.util.Date;

/**
 * @className TradeCommonContext
 * @description
 * <AUTHOR>
 * @date 2019/6/10 14:35
 */
public class TradeCommonContext {
    /**
     * 其他信息
     */
    private OtherInfoDto otherInfoDto;
    /**
     * 客户信息
     */
    private CustInfoDto custInfoDto ;
    /**
     * 投顾预约信息
     */
    private CustomerAppointmentInfoDto customerAppointmentInfoDto;
    /**
     * 经办人信息
     */
    private TransactorInfoDto transactorInfoDto;
    /**
     * 产品信息
     */
    private FundInfoAndNavDto fundInfo ;
    /**
     * 产品信息
     */
    private CounterPortfolioProductDto productInfo;
    /**
     * 线上资料信息
     */
    private AuditingOrderFileCmd auditingOrderFileCmd;
    /**
     * 预约开放日信息
     */
    private ProductAppointmentInfoModel productAppointmentInfoModel;
    /**
     * 操作员信息
     */
    private OperatorInfoCmd operatorInfoCmd;
    /**
     * 高端产品信息
     */
    private HighProductBaseInfoBean highProduct;
    /**
     * 下单时间
     */
    private Date appDtm;
    /**
     * 分销信息
     */
    private DisInfoDto disInfoDto;

    public OtherInfoDto getOtherInfoDto() {
        return otherInfoDto;
    }

    public void setOtherInfoDto(OtherInfoDto otherInfoDto) {
        this.otherInfoDto = otherInfoDto;
    }

    public CustInfoDto getCustInfoDto() {
        return custInfoDto;
    }

    public void setCustInfoDto(CustInfoDto custInfoDto) {
        this.custInfoDto = custInfoDto;
    }

    public CustomerAppointmentInfoDto getCustomerAppointmentInfoDto() {
        return customerAppointmentInfoDto;
    }

    public void setCustomerAppointmentInfoDto(CustomerAppointmentInfoDto customerAppointmentInfoDto) {
        this.customerAppointmentInfoDto = customerAppointmentInfoDto;
    }

    public TransactorInfoDto getTransactorInfoDto() {
        return transactorInfoDto;
    }

    public void setTransactorInfoDto(TransactorInfoDto transactorInfoDto) {
        this.transactorInfoDto = transactorInfoDto;
    }

    public FundInfoAndNavDto getFundInfo() {
        return fundInfo;
    }

    public void setFundInfo(FundInfoAndNavDto fundInfo) {
        this.fundInfo = fundInfo;
    }

    public AuditingOrderFileCmd getAuditingOrderFileCmd() {
        return auditingOrderFileCmd;
    }

    public void setAuditingOrderFileCmd(AuditingOrderFileCmd auditingOrderFileCmd) {
        this.auditingOrderFileCmd = auditingOrderFileCmd;
    }

    public ProductAppointmentInfoModel getProductAppointmentInfoModel() {
        return productAppointmentInfoModel;
    }

    public void setProductAppointmentInfoModel(ProductAppointmentInfoModel productAppointmentInfoModel) {
        this.productAppointmentInfoModel = productAppointmentInfoModel;
    }

    public OperatorInfoCmd getOperatorInfoCmd() {
        return operatorInfoCmd;
    }

    public void setOperatorInfoCmd(OperatorInfoCmd operatorInfoCmd) {
        this.operatorInfoCmd = operatorInfoCmd;
    }

    public HighProductBaseInfoBean getHighProduct() {
        return highProduct;
    }

    public void setHighProduct(HighProductBaseInfoBean highProduct) {
        this.highProduct = highProduct;
    }

    public Date getAppDtm() {
        return appDtm;
    }

    public void setAppDtm(Date appDtm) {
        this.appDtm = appDtm;
    }

    public DisInfoDto getDisInfoDto() {
        return disInfoDto;
    }

    public void setDisInfoDto(DisInfoDto disInfoDto) {
        this.disInfoDto = disInfoDto;
    }

    public CounterPortfolioProductDto getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(CounterPortfolioProductDto productInfo) {
        this.productInfo = productInfo;
    }
}
