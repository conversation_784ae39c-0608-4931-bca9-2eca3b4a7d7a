package com.howbuy.tms.counter.controller;

import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.service.trade.SubsAmtService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description:认缴金额相关
 * @Author: yun.lu
 * Date: 2024/7/15 13:37
 */
@Controller
public class SubsAmtController {
    private Logger logger = LogManager.getLogger(SubsAmtController.class);
    @Autowired
    private SubsAmtService subsAmtService;


    /**
     * @api {GET} /tmscounter/high/querySubsAmtList.htm querySubsAmtList
     * @apiVersion 1.0.0
     * @apiGroup SubsAmtController
     * @apiName querySubsAmtList
     * @apiDescription 分页查询认缴金额列表信息
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,TOO_EARLY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"IM_USED"}
     */
    @RequestMapping("tmscounter/high/querySubsAmtList.htm")
    public ModelAndView querySubsAmtList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建请求入参
        CustomerSubsAmtPageReqDto customerSubsAmtPageReqDto = buildQueryRequest(request);
        // 查询认缴金额信息列表
        CustomerSubsAmtResultDto responseDto = subsAmtService.querySubsAmtPage(customerSubsAmtPageReqDto);
        // 返回认缴金额信息
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("rsList", responseDto.getCustomerSubsAmtInfoDtoList());
        rst.setBody(body);
        rst.setCode(responseDto.getResultCode());
        rst.setDesc(responseDto.getResultMsg());
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * @api {GET} /tmscounter/customerSubsAmtDetail.htm customerSubsAmtDetail
     * @apiVersion 1.0.0
     * @apiGroup SubsAmtController
     * @apiName customerSubsAmtDetail
     * @apiDescription 查询用户认缴详情
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,TOO_EARLY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"IM_USED"}
     */
    @RequestMapping("tmscounter/customerSubsAmtDetail.htm")
    public ModelAndView customerSubsAmtDetail(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建请求入参
        CustomerSubsAmtDetailReqDto customerSubsAmtDetailReqDto = buildQueryDetailRequest(request);
        // 查询认缴金额信息列表
        CustomerSubsAmtInfoDto customerSubsAmtInfoDto = subsAmtService.querySubsAmtDetail(customerSubsAmtDetailReqDto);
        String currDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        // 返回认缴金额信息
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(2);
        body.put("detail", customerSubsAmtInfoDto);
        body.put("currDt", currDt);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }


    @RequestMapping("tmscounter/querySubsAmtChangeDetail.htm")
    public ModelAndView querySubsAmtChangeDetail(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建请求入参
        String dealAppNo = request.getParameter("dealAppNo");
        if(StringUtils.isBlank(dealAppNo)){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "申请单号不能为空");
        }
        // 查询认缴金额信息列表
        CustomerSubsAmtChangeDetailDto customerSubsAmtChangeDetailDto = subsAmtService.querySubsAmtChangeDetail(dealAppNo);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("detail", customerSubsAmtChangeDetailDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }


    /**
     * @api {GET} /tmscounter/subsAmtDetailApply.htm subsAmtDetailApply
     * @apiVersion 1.0.0
     * @apiGroup SubsAmtController
     * @apiName subsAmtDetailApply
     * @apiDescription 用户认缴金额信息修改提交
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,TOO_EARLY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"FOUND"}
     */
    @RequestMapping("tmscounter/subsAmtDetailApply.htm")
    public ModelAndView subsAmtDetailApply(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建请求入参
        CustomerSubsAmtApplyReqDto customerSubsAmtApplyReqDto = buildQueryDetailChangeApplyRequest(request);
        // 查询认缴金额信息列表
        CustomerSubsAmtApplyResultDto customerSubsAmtApplyResultDto = subsAmtService.subsAmtDetailApply(customerSubsAmtApplyReqDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if (YesOrNoEnum.NO.getCode().equals(customerSubsAmtApplyResultDto.getApplySuccess())) {
            rst.setCode(TmsCounterResultEnum.FAILD.getCode());
        }
        rst.setDesc(customerSubsAmtApplyResultDto.getMsg());
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * @api {GET} /tmscounter/updateSubsAmtDetailApply.htm updateSubsAmtDetailApply
     * @apiVersion 1.0.0
     * @apiGroup SubsAmtController
     * @apiName subsAmtDetailApply
     * @apiDescription 用户认缴金额信息修改提交
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,TOO_EARLY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"FOUND"}
     */
    @RequestMapping("tmscounter/updateSubsAmtDetailApply.htm")
    public ModelAndView updateSubsAmtDetailApply(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建请求入参
        UpdateSubsAmtDetailApplyDto updateSubsAmtDetailApplyDto = buildUpdateSubsAmtDetailApplyDto(request);
        // 查询认缴金额信息列表
        CustomerSubsAmtApplyResultDto customerSubsAmtApplyResultDto = subsAmtService.updateApply(updateSubsAmtDetailApplyDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if (YesOrNoEnum.NO.getCode().equals(customerSubsAmtApplyResultDto.getApplySuccess())) {
            rst.setCode(TmsCounterResultEnum.FAILD.getCode());
        }
        rst.setDesc(customerSubsAmtApplyResultDto.getMsg());
        WebUtil.write(response, rst);
        return null;
    }
    /**
     * 构建修改认缴金额申请单入参
     */
    private UpdateSubsAmtDetailApplyDto buildUpdateSubsAmtDetailApplyDto(HttpServletRequest request) {
        String dealAppNo = request.getParameter("dealAppNo");
        String newSubsAmtStr = request.getParameter("subsAmt");
        String cancel = request.getParameter("cancel");
        BigDecimal newSubsAmt = null;
        if (StringUtils.isNotBlank(newSubsAmtStr)) {
            newSubsAmt = new BigDecimal(newSubsAmtStr);
        }
        String appDt = request.getParameter("appDt");
        String appTime = request.getParameter("appTime");
        UpdateSubsAmtDetailApplyDto updateSubsAmtDetailApplyDto = new UpdateSubsAmtDetailApplyDto();
        updateSubsAmtDetailApplyDto.setSubsAmt(newSubsAmt);
        updateSubsAmtDetailApplyDto.setDealAppNo(dealAppNo);
        updateSubsAmtDetailApplyDto.setCancel(cancel);
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        updateSubsAmtDetailApplyDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        updateSubsAmtDetailApplyDto.setCreator(operatorInfoCmd.getOperName());
        return updateSubsAmtDetailApplyDto;
    }

    /**
     * 构建修改认缴金额申请单入参
     */
    private CustomerSubsAmtApplyReqDto buildQueryDetailChangeApplyRequest(HttpServletRequest request) {
        String txAcctNo = request.getParameter("txAcctNo");
        String fundCode = request.getParameter("fundCode");
        String newSubsAmtStr = request.getParameter("subsAmt");
        BigDecimal newSubsAmt = null;
        if (StringUtils.isNotBlank(newSubsAmtStr)) {
            newSubsAmt = new BigDecimal(newSubsAmtStr);
        }
        String appDt = request.getParameter("appDt");
        String appTime = request.getParameter("appTime");
        CustomerSubsAmtApplyReqDto customerSubsAmtApplyReqDto = new CustomerSubsAmtApplyReqDto();
        customerSubsAmtApplyReqDto.setFundCode(fundCode);
        customerSubsAmtApplyReqDto.setTxAcctNo(txAcctNo);
        customerSubsAmtApplyReqDto.setNewSubsAmt(newSubsAmt);
        customerSubsAmtApplyReqDto.setAppDt(appDt);
        customerSubsAmtApplyReqDto.setAppTime(appTime);
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        customerSubsAmtApplyReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        customerSubsAmtApplyReqDto.setCreator(operatorInfoCmd.getOperName());
        return customerSubsAmtApplyReqDto;
    }

    private CustomerSubsAmtDetailReqDto buildQueryDetailRequest(HttpServletRequest request) {
        String txAcctNo = request.getParameter("txAcctNo");
        String fundCode = request.getParameter("fundCode");
        CustomerSubsAmtDetailReqDto customerSubsAmtDetailReqDto = new CustomerSubsAmtDetailReqDto();
        customerSubsAmtDetailReqDto.setFundCode(fundCode);
        customerSubsAmtDetailReqDto.setTxAcctNo(txAcctNo);
        return customerSubsAmtDetailReqDto;
    }

    /**
     * 构建查询入参
     */
    private CustomerSubsAmtPageReqDto buildQueryRequest(HttpServletRequest request) {
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        CustomerSubsAmtPageReqDto customerSubsAmtPageReqDto = new CustomerSubsAmtPageReqDto();
        customerSubsAmtPageReqDto.setDisCode(disCode);
        customerSubsAmtPageReqDto.setTxAcctNo(custNo);
        return customerSubsAmtPageReqDto;
    }
}
