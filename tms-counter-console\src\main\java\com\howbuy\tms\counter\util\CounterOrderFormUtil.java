/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.howbuy.interlayer.product.model.appointment.ProductAppointmentInfoModel;
import com.howbuy.tms.common.enums.busi.FundStatusEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.counter.dto.CounterOrderFormDto;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.CounterCustBalanceVolDtlBean;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.CounterHighProductBaseBean;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.CounterProductAppointmentInfoBean;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.FundLimitBean;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.SourceDealOrderBean;
import com.howbuy.tms.counter.dto.ModifyRepurchaseProtocolDto;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * @description:(柜台订单快照创建工具) 
 * @reason:
 * <AUTHOR>
 * @date 2018年3月13日 上午10:21:26
 * @since JDK 1.6
 */
public class CounterOrderFormUtil {
    
    /**
     * 
     * createCounterOrderForm:(创建柜台订单FormJson)
     * @param sourceDealOrderBean
     * @return
     * <AUTHOR>
     * @date 2018年3月17日 上午9:41:42
     */
    public static String createCounterOrderForm(SourceDealOrderBean sourceDealOrderBean, CounterOrderFormDto.RefundBean refundBean){
        
        return createCounterOrderForm(null, null, null, null, sourceDealOrderBean, refundBean);
    }
    
    /**
     * 
     * createCounterOrderForm:(创建柜台订单快照)
     * @param highProductBaseBean
     * @param highProductStatInfoBean
     * @param productAppointmentInfoModel
     * @param dtlBeanList
     * @return
     * <AUTHOR>
     * @date 2018年3月17日 上午9:55:23
     */
    public static String createCounterOrderForm(HighProductBaseInfoBean highProductBaseBean,HighProductStatInfoBean highProductStatInfoBean, 
            ProductAppointmentInfoModel productAppointmentInfoModel,
                                                List<CounterCustBalanceVolDtlBean> dtlBeanList){
        
        return createCounterOrderForm(highProductBaseBean, highProductStatInfoBean, 
                 productAppointmentInfoModel, dtlBeanList, null, null);
    }
    
    public static String createCounterOrderFormNew(HighProductBaseInfoBean highProductBaseBean,HighProductStatInfoBean highProductStatInfoBean, 
            ProductAppointmentInfoModel productAppointmentInfoModel,
            FundLimitBean fundLimitBean){
            CounterOrderFormDto counterOrderFormDto =  new CounterOrderFormDto();
                    
            //预约开放日历信息
            if(productAppointmentInfoModel != null){
                CounterProductAppointmentInfoBean counterProductAppointmentInfoBean = new CounterProductAppointmentInfoBean();
                BeanUtils.copyProperties(productAppointmentInfoModel, counterProductAppointmentInfoBean);
                counterOrderFormDto.setCounterProductAppointmentInfoBean(counterProductAppointmentInfoBean);
            }
            
            //产品基本信息
            CounterHighProductBaseBean counterHighProductBaseBean =  buildCounterHighProductBaseBeanNew(highProductBaseBean, highProductStatInfoBean, fundLimitBean);
            counterOrderFormDto.setCounterHighProductBaseBean(counterHighProductBaseBean);
            String orderMemo = JSON.toJSONString(counterOrderFormDto,SerializerFeature.DisableCircularReferenceDetect);
            return orderMemo;
    }

    public static String createCounterOrderFormForRepurchase(ModifyRepurchaseProtocolDto modifyRepurchaseProtocolDto){
        CounterOrderFormDto.CounterRepurChaseInfoBean counterRepurChaseInfoBean = new CounterOrderFormDto.CounterRepurChaseInfoBean();
        BeanUtils.copyProperties(modifyRepurchaseProtocolDto, counterRepurChaseInfoBean);
        CounterOrderFormDto counterOrderFormDto =  new CounterOrderFormDto();

        counterOrderFormDto.setCounterRepurChaseInfoBean(counterRepurChaseInfoBean);
        String orderMemo = JSON.toJSONString(counterOrderFormDto,SerializerFeature.DisableCircularReferenceDetect);
        return orderMemo;
    }
    /**
     * 
     * createCounterOrderForm:(创建柜台订单form)
     * @return
     * <AUTHOR>
     * @date 2018年3月12日 下午5:29:20
     */
    public static String createCounterOrderForm(
            HighProductBaseInfoBean highProductBaseBean, HighProductStatInfoBean highProductStatInfoBean,
            ProductAppointmentInfoModel productAppointmentInfoModel,
            List<CounterCustBalanceVolDtlBean> dtlBeanList , SourceDealOrderBean sourceDealOrderBean,
            CounterOrderFormDto.RefundBean refundBean){
        
        CounterOrderFormDto counterOrderFormDto =  new CounterOrderFormDto();
        
        if(sourceDealOrderBean != null){
            counterOrderFormDto.setSourceDealOrderBean(sourceDealOrderBean);
        }
        //预约开放日历信息
        if(productAppointmentInfoModel != null){
            CounterProductAppointmentInfoBean counterProductAppointmentInfoBean = new CounterProductAppointmentInfoBean();
            BeanUtils.copyProperties(productAppointmentInfoModel, counterProductAppointmentInfoBean);
            counterOrderFormDto.setCounterProductAppointmentInfoBean(counterProductAppointmentInfoBean);
        }
        
        //用户持有份额新
        if(dtlBeanList != null){
            counterOrderFormDto.setDtlBeanList(dtlBeanList);
        }
        
        //产品基本信息
        CounterHighProductBaseBean counterHighProductBaseBean =  buildCounterHighProductBaseBean(highProductBaseBean, highProductStatInfoBean);
        counterOrderFormDto.setCounterHighProductBaseBean(counterHighProductBaseBean);

        // 回款
        counterOrderFormDto.setRefundBean(refundBean);

        String orderMemo = JSON.toJSONString(counterOrderFormDto,SerializerFeature.DisableCircularReferenceDetect);
        return orderMemo;
    }
    
    private static CounterHighProductBaseBean buildCounterHighProductBaseBean(HighProductBaseInfoBean highProductBaseBean,HighProductStatInfoBean highProductStatInfoBean){
        
        if(highProductBaseBean == null){
            return null;
        }
        CounterHighProductBaseBean counterHighProductBaseBean = new CounterHighProductBaseBean();
        BeanUtils.copyProperties(highProductBaseBean, counterHighProductBaseBean);
        
        if(highProductStatInfoBean != null ){
            counterHighProductBaseBean.setFundStat(highProductStatInfoBean.getFundStat());
            counterHighProductBaseBean.setBuyBusiType(convertBuyBusiType(highProductStatInfoBean.getFundStat()));
        }
        
        return counterHighProductBaseBean;
    }
    
    private static CounterHighProductBaseBean buildCounterHighProductBaseBeanNew(HighProductBaseInfoBean highProductBaseBean,HighProductStatInfoBean highProductStatInfoBean,FundLimitBean fundLimitBean){
       
        if(highProductBaseBean == null){
            return null;
        }
        CounterHighProductBaseBean counterHighProductBaseBean = new CounterHighProductBaseBean();
        BeanUtils.copyProperties(highProductBaseBean, counterHighProductBaseBean);
        
        if(highProductStatInfoBean != null ){
            counterHighProductBaseBean.setFundStat(highProductStatInfoBean.getFundStat());
            counterHighProductBaseBean.setBuyBusiType(convertBuyBusiType(highProductStatInfoBean.getFundStat()));
        }
        
        if(fundLimitBean != null ){
            counterHighProductBaseBean.setNetMinAppAmt(fundLimitBean.getNetMinAppAmt());
            counterHighProductBaseBean.setNetMinSuppleAmt(fundLimitBean.getNetMinSuppleAmt());
        }
        
        return counterHighProductBaseBean;
    }
    
    /**
     * 
     * convertBusiType:(基金状态转换)
     * @param fundStatus
     * @return 0-认购 1-申购
     * <AUTHOR>
     * @date 2018年2月6日 下午6:02:37
     */
    public static  String convertBuyBusiType(String fundStatus){
        //1-发行
        if(FundStatusEnum.IPO.getCode().equals(fundStatus)){
            return "0";
        }else{
            return "1";
        }
    }
    
    /**
     * 
     * getSubmitTradeDt:(获取上报TA工作日)
     * @return
     * <AUTHOR>
     * @date 2018年4月12日 上午8:43:38
     */
    public static String getSubmitTradeDt(String appDt, String openStartDt){
        
        if(openStartDt == null){
            return appDt;
        }
        
        if(appDt.compareTo(openStartDt) < 0){
            return openStartDt;
        }
        
        return appDt;
        
    }
    
    
}

