<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/css/video.css" />
    <title>认申购复核</title>
</head>

<body>
	<div class="page-container">
        <div class="containner_all">
              <p class="mainTitle mt10">认申购审核</p>
        </div>
    </div>
    <div class="page-container w1000">
        <p class="main_title mt30">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>                   
                        <th>风险等级</th>
                        <th>分销机构</th>                        
                    </tr>
               </thead>
                <tbody id="custInfoId">
                	 <tr class="text-c">
                	 	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <p class="main_title mt30">录入订单信息</p>
        <form action="" id="buyConfirmForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                    <tr class="text-c">
                        <td>产品代码</td>
                        <td>
                            <div class="searchIn"><input type="text" name="fundCode"  id="fundCode" ><a href="javascript:void(0)" class="searchIcon"></a></div>
                        </td>
                        <td>风险等级</td>
                        <td id="fundRiskLevel" >--</td>
                    </tr>
                    <tr class="text-c">
                        <td>产品名称</td>
                        <td id="fundName" >--</td>
                        <td>产品状态</td>
                        <td id="fundStatus">--</td>
                    </tr>
                    <tr class="text-c">
                        <td>申请金额（元）</td>
                        <td>
                           <div class="convertCon">
                               <input type="text" placeholder="请输入" id="applyAmount" class="applyAmount" name="appAmt" isnull="false" datatype="s" errormsg="申请金额">
                           </div>
                        </td>
                        <td>申请金额（大写）</td>
                        <td>
                        	<input id="applyAmountCapital" class="applyAmountCapital" name="applyAmountCapital" type="text" datatype="s" errormsg="申请金额（元）含费" readonly="readonly">
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>申请折扣率</td>
                        <td>
                            <input id ="discountRate" class="discountRate" name="discountRate" type="text" datatype="s" errormsg="申请折扣率" readonly="readonly">
                        </td>
                        <td>原始费率</td>
                        <td id="originalFeeRate">             
						</td>
                    </tr>
                    <tr class="text-c">
                        <td>银行账号</td>
                        <td>
                            <span class="select-box inline">
                                <select name="cpAcctNo" class="select" id="selectBank" isnull="false" datatype="s" errormsg="银行卡">
                                    <option value="">请选择</option>
                                </select>
                            </span>
                        </td>
                        <td>支付方式</td>
                        <td>自划款<input type="hidden" name="paymentType" value="01"/></td>
                       
                    </tr>
                     <tr class="text-c">
                     	<td>下单日期</td>
                        <td id="appDt" >
                        </td>
                        <td>下单时间</td>
                        <td id="appTm">
                        </td>
                     </tr>
                     <tr class="text-c">
                     	<td>二次确认标识</td>
                        <td id=riskFlag ></td>
                         <td>双录材料</td>
                         <td id="filePath" data="">
                             <input type="button" value="下载" id="downloadFile" class="btn radius btn-secondary" style="display: none;" />
                             <input type="button" value="查看" id="openFile" class="btn radius btn-secondary" style="display: none; background-color: #4F9F8F" />
                         </td>
                     </tr>

                    <tr class="text-c" id="questionAnswerDiv" style="display: none;">
                        <td>投顾问卷答案</td>
                        <td>
                            <input id="questionAnswer" class="questionAnswer" name="questionAnswer"  type="text" datatype="s" errormsg="投顾问卷答案" readonly="readonly">
                        </td>
                        <td></td>
                        <td>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
       </form>
       <p class="main_title mt30">其他信息</p>
       <form id="transactorInfoForm" >
         <div class="result2_tab">
         <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
         		<tr class="text-c">
                   	<td>网点：</td>
                   	<td>
                   		柜台<input type="hidden" name="outletCode" value="01"/>
                   	</td>
                   	<td>投资顾问代码：</td>
              		<td id="consCode" class ="selectconsCode">
                	</td>
                </tr>
            	<tr class="text-c">
                   	<td>经办人姓名：</td>
                   	<td id="transactorName">
                   	</td>
                   	<td>经办人证件类型：</td>
              		<td id="transactorIdType">
                	</td>
                </tr>
             	<tr class="text-c">
              		<td>经办人证件号：</td>
              		<td id="transactorIdNo"> 
              		</td>
              		<td>失败原因：</td>
              		<td>
                        <input type="text" placeholder='请输入失败原因' id="checkFaildDesc" name="checkFaildDesc">
                    </td>
               	</tr>
            </tbody>
          </table>
        </div>
         </form>
         <p class="mt10 text-c">
            <a href="javascript:void(0)" id ="returnBtn" class="btn btn-default radius">退回</a> 
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <a href="javascript:void(0)" id ="succBtn" class="btn radius btn-secondary">审核通过</a>
        </p>
        <div class="clear page_all">
            	<div class="fy_part fr mt20" style="display: none" id="pageView"></div>
        </div>
    </div>

    <div class="cover2 hide2" ></div>
    <div class="modal2 hide2" >
        <span class="close2" id="closeVideo">×</span>
        <video src="" id="videoUrl"  width="320" height="240" controls="controls" autoplay="autoplay">
            Your browser does not support the video tag.
        </video>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/check/checkbuy.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/queryfundinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycheckorder.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/check/countercheck.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
</body>

</html>