/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.enums;
/**
 * @description:(赎回去向) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年10月10日 下午6:33:28
 * @since JDK 1.6
 */
public enum RedeemCapitalFlagEnum {
    /**
     * 储蓄罐
     */
    PIGGY("1","储蓄罐"),
    /**
     * 银行卡
     */
    BANK("0","银行卡");
    
    
    private String code;
    private String desc;
    
    RedeemCapitalFlagEnum(String code,String desc){
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    
}

