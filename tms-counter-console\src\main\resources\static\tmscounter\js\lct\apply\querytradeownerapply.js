/**
*理财通柜台---我的交易申请查询
*<AUTHOR>
*@date 2017-09-18 15:23
*/
$(function(){
	Init.init();
	QueryTradeOwnerApply.init();
	QueryTradeOwnerApply.custInfo = {};
	QueryTradeOwnerApply.checkOrders = [];
	QueryTradeOwnerApply.checkedOrder = {};

	QueryTradeOwnerApply.queryOrderInfo();
});
 var  QueryTradeOwnerApply = {
	init:function(){
		$("#queryBtn").on('click',function(){
			QueryTradeOwnerApply.queryOrderInfo();
		});
	},
	
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.LCT_QUERY_CHECK_ORDER_URL  ||  {};
		var reqparamters  = {};
		var queryOrderConditionForm =  $("#queryConditonForm").serializeObject();
		var queryOrderCondition = {};
		$.each(queryOrderConditionForm,function(name,value){
			if(!CommonUtil.isEmpty(value)){
				queryOrderCondition[name] = value;
			}
		});
		
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 20;
		var currWorkDay = CommonUtil.getWorkDay();
		reqparamters.tradeDt = currWorkDay;
		reqparamters.owner = "owner";
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, QueryTradeOwnerApply.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		QueryTradeOwnerApply.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];
		$("#rsList").empty();
		if(QueryTradeOwnerApply.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
			$("#rsList").append(trHtml);
		}
		
		var staticData = bodyData.counterQueryOrderRespDto || {};
		$("#staticId").html("当页小计：申请笔数【"+QueryTradeOwnerApply.checkOrders.length+"】申请金额【"+CommonUtil.formatAmount(staticData.pageAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.pageAppVol)+"】 合计：申请笔数【"+staticData.totalCount+"】申请金额【"+CommonUtil.formatAmount(staticData.totalAppAmt)+"】申请份额【"+CommonUtil.formatAmount(staticData.totalAppVol)+"】");
		
		var i = 1;
		$(QueryTradeOwnerApply.checkOrders).each(function(index,element){
			var trList = [];
			trList.push(i++);
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custName));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, element.txCode, ''));
			trList.push(CommonUtil.formatData(element.fundCode));
			trList.push(CommonUtil.formatData(element.fundName));
			if(element.appAmt > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appAmt)));
			}else {
				trList.push('--');
			}
			if(element.appVol > 0){
				trList.push(CommonUtil.formatData(CommonUtil.formatAmount(element.appVol)));
			}else {
				trList.push('--');
			}
			trList.push(CommonUtil.formatData(element.appDt));
			trList.push(CommonUtil.formatData(element.appTm));
			trList.push('柜台');
			trList.push(CommonUtil.formatData(element.creator,''));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, element.checkFlag, ''));
			if(element.checkFlag == '3'){
				trList.push('<a class="read reCheck" href="javascript:void(0);" indexvalue = '+index+'>查看</a>');
				trList.push('<a class="update reCheck" href="javascript:void(0);" indexvalue = '+index+'>修改</a>');
			} else {
				trList.push('<a class="read reCheck" href="javascript:void(0);" indexvalue = '+index+'>查看</a>');
				trList.push('--');
			}
			
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
		});
		

		// 绑定修改
		$(".update").off();
		$(".update").on('click',function(){			
			var indexValue = $(this).attr("indexvalue");
			var checkOrder = QueryTradeOwnerApply.checkOrders[indexValue] || {};
			QueryTradeOwnerApply.checkedOrder = checkOrder;
			
			var txCode = QueryTradeOwnerApply.checkedOrder.txCode;
			var dealAppNo = QueryTradeOwnerApply.checkedOrder.dealAppNo;
			var txAcctNo = QueryTradeOwnerApply.checkedOrder.txAcctNo;
			var disCode = QueryTradeOwnerApply.checkedOrder.disCode;
			var idNo = QueryTradeOwnerApply.checkedOrder.idNo;
			
			var param = "checkId="+dealAppNo+"&custNo="+txAcctNo+"&disCode="+disCode+"&idNo="+idNo;
			if('Z910097' == txCode){// 转托管转出
				window.open("../apply/applytransfertubeout.html?"+param,"_blank");
				return;
			}
		});
		
		// 绑定查看
		$(".read").off();
		$(".read").on('click',function(){	
			var indexValue = $(this).attr("indexvalue");
			var checkOrder = QueryTradeOwnerApply.checkOrders[indexValue] || {};
			QueryTradeOwnerApply.checkedOrder = checkOrder;
			
			var dealAppNo = checkOrder.dealAppNo;
			if(!CommonUtil.isEmpty(dealAppNo)){
				
				if(checkOrder.txCode == 'Z910097'){// 转托管转出
                    QueryLctCheckOrder.queryCheckOrderById(dealAppNo, BodyView.queryShowLctCounterTransferTubeOrderInfo);
					
				} else{// 认申购，赎回，修改分红方式，撤单，基金转换
					BodyView.showCounterOrderInfo(checkOrder);
				}
			}else {
				CommonUtil.layer_tip("无预申请单号无法显示订单详情！");
			}
		});
		
	}
};
