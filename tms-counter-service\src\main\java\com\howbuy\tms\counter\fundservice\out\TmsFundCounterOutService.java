/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundservice.out;

import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.ConsultantBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.DisChannelBean;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundProductFeeRateBean;
import com.howbuy.tms.counter.dto.CounterModifyDivRespDto;
import com.howbuy.tms.counter.dto.ValidatorRetailRiskLevelDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:(中台柜台引用外部服务)
 * <AUTHOR>
 * @date 2017年4月12日 上午10:33:53
 * @since JDK 1.6
 */
public interface TmsFundCounterOutService {

    /***
     * 
     * getFundFeeRateByAmt:(TODO 这里用一句话描述这个方法的作用)
     * 
     * @param midProductId
     * @param busiCode
     * @param invstType
     * @param shareClass
     * @param appAmt
     * @return
     * <AUTHOR>
     * @date 2017年9月15日 下午3:12:09
     */
    public FundProductFeeRateBean getFundFeeRateByAmt(String midProductId, String busiCode, String invstType, String shareClass, BigDecimal appAmt);

    /**
     * 
     * getConsInfo:(查询投顾)
     * 
     * @param outletCode
     * @return
     * <AUTHOR>
     * @date 2017年9月18日 下午4:51:39
     */
    public List<ConsultantBean> getConsInfo(String outletCode);

    /**
     * 
     * selectAllDisChannel:(查询所有有效分销机构)
     * 
     * @return
     * <AUTHOR>
     * @date 2017年10月17日 下午5:41:54
     */
    public List<DisChannelBean> selectAllDisChannel();

    /**
     * 
     * validatorRetailRiskLevel:(校验风险等级)
     * 
     * @param txAcctNo
     * @param invstType
     * @param qualificationType
     * @param riskFlag
     * @param productRiskLevel
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年11月28日 上午10:24:01
     */
    public ValidatorRetailRiskLevelDto validatorRetailRiskLevel(String txAcctNo, String invstType, String qualificationType, String riskFlag,
            String productRiskLevel) throws Exception;

    /**
     * 
     * queryFundDiv:(零售查询分红方式)
     * 
     * @param custNo
     * @param fundCode
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年11月27日 下午1:54:05
     */
    public CounterModifyDivRespDto queryFundDiv(String custNo, String fundCode, DisInfoDto disInfoDto, String fundTxAcctNo) throws Exception;

    /**
     * 
     * getActiRateDiscount:(查询活动折扣)
     * 
     * @param productId
     * @param shareClass
     * @param paymentType
     * @param invstType
     * @param busiCode
     * @param bankCode
     * @param disCode
     * @param appAmt
     * @return
     * @return BigDecimal
     * <AUTHOR>
     * @date 2018年5月4日 上午10:28:00
     */
    public BigDecimal getActiRateDiscount(String productId, String shareClass, String paymentType, String invstType, String busiCode, String bankCode,
            String disCode, BigDecimal appAmt);
}
