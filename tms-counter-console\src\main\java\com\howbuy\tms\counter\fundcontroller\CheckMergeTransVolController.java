/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.CustBankInfoBean;
import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.QueryBindCustBankCardListFacade;
import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.QueryBindCustBankCardListRequest;
import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.QueryBindCustBankCardListResponse;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.OrderFileEnum;
import com.howbuy.paycommon.model.enums.ProdTypeEnum;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.DeleteVoucherFileFacade;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.DeleteVoucherFileRequest;
import com.howbuy.tms.batch.facade.trade.exchangecarduploadfile.DeleteVoucherFileResponse;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.ShareTypeEnum;
import com.howbuy.tms.common.enums.database.MaterialTypeEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankCardSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.QueryAllBankCardSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankacctsensitiveinfo.bean.CustAllBankSensitiveModel;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.QueryAllBankCardInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallbankcardinfo.bean.CustBankModel;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryidcardinfo.QueryIdAndFaceInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryidcardinfo.QueryIdCardInfoOuterService;
import com.howbuy.tms.common.outerservice.common.TradeFacadeUtils;
import com.howbuy.tms.common.outerservice.crm.td.queryorderfile.QueryOrderFileContext;
import com.howbuy.tms.common.outerservice.finonline.querylatestpaycust.QueryLatestPayCustResult;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.QueryFundInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.DisChannelBean;
import com.howbuy.tms.counter.config.TmsCounterNacosConfig;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.*;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.AssetScopeEnum;
import com.howbuy.tms.counter.enums.CheckTypeEnum;
import com.howbuy.tms.counter.service.orderplan.api.query.QuerySchePlanCounterRequest;
import com.howbuy.tms.counter.service.orderplan.api.query.QuerySchePlanCounterResponse;
import com.howbuy.tms.counter.service.orderplan.api.query.SchePlanCounterDto;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.counter.util.NfsToWebDavUtils;
import com.howbuy.tms.counter.util.WebDevFileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * @description:(柜台份额合并或迁移复核)
 * <AUTHOR>
 * @date 2018年5月9日 上午10:23:49
 * @since JDK 1.6
 */
@Controller
public class CheckMergeTransVolController extends AbstractController {
    private static Logger logger = LogManager.getLogger(CheckMergeTransVolController.class);
    @Autowired
    @Qualifier("queryAllCustInfoOuterService")
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    
    @Autowired
    @Qualifier("queryAllBankAcctSensitiveInfoOuterService")
    private QueryAllBankAcctSensitiveInfoOuterService queryAllBankAcctSensitiveInfoOuterService;
    
    @Autowired
    private TmsCounterNacosConfig tmsCounterNacosConfig;

    @Autowired
    private DeleteVoucherFileFacade deleteVoucherFileFacade;

    @Autowired
    @Qualifier("tmscounter.queryBindCustBankCardListFacade")
    private QueryBindCustBankCardListFacade queryBindCustBankCardListFacade;

    @Autowired
    private QueryIdCardInfoOuterService queryIdCardInfoOuterService;

    @Autowired
    private TradeValidateService tradeValidateService;

    @Autowired
    private QueryFundInfoOuterService queryFundInfoOuterService;

    /**
     * 转入份额卡不能被解绑
     * @param txAcctNo 客户号
     * @param disCode 分销号
     * @param cpAcctNo 资金账号
     * @return void
     * @author: houling.lei
     * @date: 2022/10/18 16:01
     * @since JDK 1.8
     */
    private void validVolInFundBankAcctInfo(String txAcctNo, String disCode, final String cpAcctNo){
        QueryBindCustBankCardListRequest request = new QueryBindCustBankCardListRequest();
        request.setTxAcctNo(txAcctNo);
        request.setDisCode(disCode);
        request.setProdType(ProdTypeEnum.ALL.toString());
        //查询用户绑定的银行卡
        QueryBindCustBankCardListResponse resp = queryBindCustBankCardListFacade.execute(request);
        TradeFacadeUtils.isSuccessWithThrowException(resp);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(resp.getBindCustBankCardInfoList())) {
            throw new TmsCounterException(TmsCounterResultEnum.IN_VOL_FUND_BANK_ACCT_IS_OUT_BIND);
        }
        //该银行卡已解绑则抛出异常
        List<CustBankInfoBean> list =  resp.getBindCustBankCardInfoList().stream().filter(item-> Objects.equals(cpAcctNo,item.getCpAcctNo())).collect(Collectors.toList());
        if(org.apache.commons.collections.CollectionUtils.isEmpty(list)){
            throw new TmsCounterException(TmsCounterResultEnum.IN_VOL_FUND_BANK_ACCT_IS_OUT_BIND);
        }
    }
    /**
     * 
     * checkMergeTransConfirm:(审核确认)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月9日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/checkMergeTransConfirm.htm")
    public ModelAndView checkMergeTransConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        
        String checkStatus = request.getParameter("checkStatus");
        String checkFaildDesc = request.getParameter("checkFaildDesc");
        String checkedOrderForm = request.getParameter("checkedOrderForm");
        String intransitAssetMemo = request.getParameter("intransitAssetMemo");
        String operation = request.getParameter("operation");
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");
        logger.debug("CheckMergeTransVolController|checkMergeTransConfirm: checkedOrderForm:{}, checkStatus:{}", checkedOrderForm, checkStatus);
        
        CounterOrderDto checkOrderDto = JSON.parseObject(checkedOrderForm, CounterOrderDto.class);
        String dealAppNo = checkOrderDto != null ? checkOrderDto.getDealAppNo() : null;
        // 校验
        check(operatorInfoCmd, checkStatus, operation, checkOrderDto, dealAppNo);

        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        // 明细申请单
        List<SubmitUncheckOrderDtlDto> checkDtlOrders = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
        if(CollectionUtils.isEmpty(checkDtlOrders)){
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_NOT_QUERY_ORDER);
        }

        // 材料
        AuditingOrderFileCmd auditingOrderFileCmd =  null;
        if(!StringUtils.isEmpty(materialinfoForm)){
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
            tmsCounterOutService.validateOrderFileStatus(auditingOrderFileCmd);
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(checkOrderDto.getDisCode());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, checkOrderDto);
        // 获取审核确认结果
        TmsCounterResult rst = getTmsCounterResult(operatorInfoCmd, checkStatus, checkFaildDesc, intransitAssetMemo, checkOrderDto, checkDtlOrders, auditingOrderFileCmd, disInfoDto);

        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 获取审核确认结果
     */
    private TmsCounterResult getTmsCounterResult(OperatorInfoCmd operatorInfoCmd, String checkStatus, String checkFaildDesc, String intransitAssetMemo, CounterOrderDto checkOrderDto, List<SubmitUncheckOrderDtlDto> checkDtlOrders, AuditingOrderFileCmd auditingOrderFileCmd, DisInfoDto disInfoDto) throws Exception {
        SubmitUncheckOrderDto submitUncheckOrderDto = getSubmitUncheckOrderDto(operatorInfoCmd, checkStatus, checkFaildDesc, intransitAssetMemo, checkOrderDto);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        // 份额合并审核
        if(TxCodes.COUNTER_MERGE_VOL.equals(checkOrderDto.getTxCode())){
            checkOrderDto.setShareType(ShareTypeEnum.FUND_SHARE_MERGE.getCode());
            // 根据产品交易通道进行审核落单
            String productChannel = checkDtlOrders.get(0).getProductChannel();

            if(ProductChannelEnum.FUND.getCode().equals(productChannel)) {
                // 零售审核
                tmsFundCounterService.checkOrder(submitUncheckOrderDto, null, checkDtlOrders, disInfoDto);

            } else if (ProductChannelEnum.HIGH_FUND.getCode().equals(productChannel)) {
                // 专户审核
                //tmsCounterService.checkVolMergeTransOrder(submitUncheckOrderDto, checkDtlOrders, disInfoDto);
            }
        }
        // 份额迁移审核
        else if(TxCodes.COUNTER_TRANS_VOL.equals(checkOrderDto.getTxCode())){
            checkOrderDto.setShareType(ShareTypeEnum.FUND_SHARE_TRANSFER.getCode());

            checkBindCard(submitUncheckOrderDto, checkDtlOrders);

            BaseResponse rs = tmsCounterService.checkVolShareTransOrder(submitUncheckOrderDto, checkDtlOrders, disInfoDto);
            rst.setCode(rs.getReturnCode());
            rst.setDesc(rs.getDescription());
        }

        if(auditingOrderFileCmd != null && !StringUtils.isEmpty(auditingOrderFileCmd.getOrderid())){
            if(CheckTypeEnum.CHECK_SUCC.getCode().equals(checkStatus)){
                // 复审通过
                auditingOrderFileCmd.setCurstat(OrderFileEnum.CURSTAT_SUCCESS.getCode());
                tmsCounterOutService.auditingFile(operatorInfoCmd, auditingOrderFileCmd, submitUncheckOrderDto.getDealAppNo());
            }else if(CheckTypeEnum.CHECK_REJECT.getCode().equals(checkStatus)){
                //总部已审核
                auditingOrderFileCmd.setCurstat(OrderFileEnum.CURSTAT_OPWAIT.getCode());
                tmsCounterOutService.auditingFile(operatorInfoCmd, auditingOrderFileCmd, submitUncheckOrderDto.getDealAppNo());
            }
        }
        return rst;
    }

    private void checkBindCard(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> checkDtlOrders) {
        if(CollectionUtils.isEmpty(checkDtlOrders)){
            throw new TmsCounterException(TmsCounterResultEnum.CUST_BANK_CARD_DATA_ERROR);
        }
        if(submitUncheckOrderDto == null || StringUtil.isEmpty(submitUncheckOrderDto.getCpAcctNo())){
            throw new TmsCounterException(TmsCounterResultEnum.CUST_BANK_CARD_DATA_ERROR);
        }

        List<String> disCodes = checkDtlOrders.stream().filter(e ->  !StringUtils.isEmpty(e.getDisCode())).map(SubmitUncheckOrderDtlDto::getDisCode).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(disCodes)){
            throw new TmsCounterException(TmsCounterResultEnum.CUST_BANK_CARD_DATA_ERROR);
        }
        String txAcctNo = submitUncheckOrderDto.getTxAcctNo();
        String cpAcctNo = submitUncheckOrderDto.getCpAcctNo();
        for(String disCodeStr : disCodes){
            boolean isValid = tradeValidateService.validateCustBankCardByCpAcctNo(txAcctNo, cpAcctNo, disCodeStr);
            if(!isValid){
                String disCodeName = disCodeStr;
                DisChannelBean disChannelBean = queryFundInfoOuterService.getDisChannel(disCodeStr);
                if(disChannelBean != null){
                    disCodeName = disChannelBean.getDisName();
                }
                throw new TmsCounterException(TmsCounterResultEnum.CUST_BANK_CARD_UNBIND.getCode(), "转入卡在" + disCodeName + "下已解绑，请联系用户绑定后再审核");
            }
        }
    }

    private SubmitUncheckOrderDto getSubmitUncheckOrderDto(OperatorInfoCmd operatorInfoCmd, String checkStatus, String checkFaildDesc, String intransitAssetMemo, CounterOrderDto checkOrderDto) {
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        BeanUtils.copyProperties(checkOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setCheckFlag(checkStatus);
        submitUncheckOrderDto.setMemo(checkFaildDesc);
        submitUncheckOrderDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setIntransitAssetMemo(intransitAssetMemo);
        return submitUncheckOrderDto;
    }

    private void check(OperatorInfoCmd operatorInfoCmd, String checkStatus, String operation, CounterOrderDto checkOrderDto, String dealAppNo) {
        // 审核成功需要检查转入卡用户是否已经解绑
        if(CheckTypeEnum.CHECK_SUCC.getCode().equals(checkStatus)){
            validVolInFundBankAcctInfo(checkOrderDto.getTxAcctNo(), checkOrderDto.getDisCode(), checkOrderDto.getCpAcctNo());
        }
        if(StringUtils.isEmpty(dealAppNo) || StringUtils.isEmpty(checkStatus)){
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_PARAMS_ERROR);
        }

        // 核人与录入人是同一人
        if (!Constants.OPERATION_ABOLISH.equals(operation)) {
            String creator = StringUtils.isEmpty(checkOrderDto.getCreator()) ? "" : checkOrderDto.getCreator();
            if (creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_REPLY);
            }
        }
    }

    @RequestMapping("/tmscounter/fund/uploadVoucherFilePic.htm")
    public ModelAndView uploadVoucherFilePic(MultipartFile file, String dealAppNo, HttpServletResponse response) throws Exception {
        String materialType = null;
        logger.info("uploadVoucherFile.htm dealAppNo:{}, materialType:{}", dealAppNo, materialType);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if(StringUtil.isEmpty(dealAppNo)){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("参数错误");
            WebUtil.write(response, rst);
            return null;
        }
        if(file.getBytes().length ==0){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("文件未选择");
            WebUtil.write(response, rst);
            return null;
        }


        byte[] fileBytes = file.getBytes();
        String fileName = file.getOriginalFilename();

        if(fileBytes.length > Constants.MAX_FILE_BYTE_LENGTH){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("文件大小超过限制，最大为8M");
            WebUtil.write(response, rst);
            return null;
        }
        String[] splitStrs = fileName.split("\\.");
        if(!TmsCounterConstant.ONLINE_CHANGE_CARD_FILE_TYPE.contains(splitStrs[splitStrs.length-1].toLowerCase())){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("文件格式不支持，请重新上传");
            WebUtil.write(response, rst);
            return null;
        }

        BaseResponse rs = tmsCounterService.saveVoucherFile(dealAppNo, "1", fileName, fileBytes);
        rst.setCode(rs.getReturnCode());
        rst.setDesc(rs.getDescription());
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 删除换卡资料
     *
     * @param dealDtlAppNo 订单明细号
     * @param response HTTP响应
     * @return ModelAndView
     * @throws Exception 异常
     * <AUTHOR>
     * @date 2025-04-01 17:29:50
     */
    @RequestMapping("/tmscounter/fund/delVoucherFile.htm")
    public ModelAndView delVoucherFile(String dealDtlAppNo, HttpServletResponse response) throws Exception {
        logger.info("delVoucherFile.htm dealDtlAppNo:{}", dealDtlAppNo);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if(StringUtil.isEmpty(dealDtlAppNo)){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("参数错误");
            WebUtil.write(response, rst);
            return null;
        }

        DeleteVoucherFileRequest deleteVoucherFileRequest = new DeleteVoucherFileRequest();
        deleteVoucherFileRequest.setDealDtlAppNo(dealDtlAppNo);
        DeleteVoucherFileResponse execute = deleteVoucherFileFacade.execute(deleteVoucherFileRequest);
        rst.setCode(execute.getReturnCode());
        rst.setDesc(execute.getDescription());
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/fund/uploadVoucherFile.htm")
    public ModelAndView uploadVoucherFile(MultipartFile file, String dealAppNo, String materialType, HttpServletResponse response) throws Exception {
        logger.info("uploadVoucherFile.htm dealAppNo:{}, materialType:{}", dealAppNo, materialType);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        if(StringUtil.isEmpty(dealAppNo)){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("参数错误");
            WebUtil.write(response, rst);
            return null;
        }
        if(file.getBytes().length ==0){
            rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
            rst.setDesc("文件未选择");
            WebUtil.write(response, rst);
            return null;
        }


        byte[] fileBytes = file.getBytes();
        String fileName = file.getOriginalFilename();

        if (MaterialTypeEnum.MATERIAL_TYPE_VOUCHER.getCode().equals(materialType)){
            if(fileBytes.length > Constants.MAX_FILE_BYTE_LENGTH){
                rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
                rst.setDesc("文件大小超过限制，最大为8M");
                WebUtil.write(response, rst);
                return null;
            }
            String[] splitStrs = fileName.split("\\.");
            if(!TmsCounterConstant.ONLINE_CHANGE_CARD_FILE_TYPE.contains(splitStrs[splitStrs.length-1].toLowerCase())){
                rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
                rst.setDesc("文件格式不支持，请重新上传");
                WebUtil.write(response, rst);
                return null;
            }
        }

        if (MaterialTypeEnum.MATERIAL_TYPE_VIDEO_PROMISE.getCode().equals(materialType)
                ||MaterialTypeEnum.MATERIAL_TYPE_VIDEO_ID_CARD.getCode().equals(materialType)
                ||MaterialTypeEnum.MATERIAL_TYPE_VIDEO_HOLD_BANK_CARD.getCode().equals(materialType)){
            if(fileBytes.length > Constants.MAX_VIDEO_FILE_SIZE){
                rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
                rst.setDesc("视频文件大小超过限制，最大为50M");
                WebUtil.write(response, rst);
                return null;
            }
            String[] splitStrs = fileName.split("\\.");
            if(!TmsCounterConstant.ONLINE_CHANGE_CARD_FILE_VIDEO_TYPE.contains(splitStrs[splitStrs.length-1].toLowerCase())){
                rst.setCode(TmsCounterResultEnum.PARAM_IS_ERROR.getCode());
                rst.setDesc("视频格式仅支持avi、mp4、mov、wmv、flv、mpeg、webm格式，请重新上传");
                WebUtil.write(response, rst);
                return null;
            }
        }


        BaseResponse rs = tmsCounterService.saveVoucherFile(dealAppNo, materialType, fileName, fileBytes);
        rst.setCode(rs.getReturnCode());
        rst.setDesc(rs.getDescription());
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/fund/downloadPic.htm")
    public void downloadPic(@RequestParam("path") String path, @RequestParam("fileName")  String fileName, @RequestParam("type") String type, HttpServletResponse response) throws Exception {
        logger.info("downloadPic.htm path:{}, fileName:{}, type:{}", path, fileName, type);

        // 根据不同的 type 下载不同类型的图片
        byte[] fileData = null;

        if ("1".equals(type)){
            // 人脸识别照片
            String webDavFrontPicturePath = NfsToWebDavUtils.processFaceAccIdCardUpFile(path, WebDevEnum.FACE_PIC_UP_FILE.getName());
            fileData = WebDevFileUtils.read2Bytes(WebDevEnum.FACE_PIC_UP_FILE.getCode(), webDavFrontPicturePath, fileName);
        } else if ("2".equals(type)) {
            // 身份证照片
            String webDavFrontPicturePath = NfsToWebDavUtils.processFaceAccIdCardUpFile(path, WebDevEnum.ID_CARD_UP_FILE.getName());
            fileData = WebDevFileUtils.read2Bytes(WebDevEnum.ID_CARD_UP_FILE_MAPPING.getCode(), webDavFrontPicturePath, fileName);
        }

        if (fileData != null) {
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + encodedFileName);
            response.setContentType("application/octet-stream");
            response.getOutputStream().write(fileData);
            response.getOutputStream().flush();
        }

    }

    /**
     *
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月9日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/onlineTransConfirm.htm")
    public ModelAndView onlineTransConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);

        String checkStatus = request.getParameter("checkStatus");
        String checkFaildDesc = request.getParameter("checkFaildDesc");
        String checkedOrderForm = request.getParameter("checkedOrderForm");
        String operation = request.getParameter("operation");
        // CRM材料ID
        String materialCheckFlagList = request.getParameter("materialCheckFlagList");
        logger.debug("CheckMergeTransVolController|checkMergeTransConfirm: checkedOrderForm:{}, checkStatus:{}", checkedOrderForm, checkStatus);

        CounterOrderDto checkOrderDto = JSON.parseObject(checkedOrderForm, CounterOrderDto.class);
        List<ExchangeCardMaterialDtlDto> materialDtlDto = JSON.parseArray(materialCheckFlagList, ExchangeCardMaterialDtlDto.class);
        String dealAppNo = checkOrderDto != null ? checkOrderDto.getDealAppNo() : null;
        if (StringUtils.isEmpty(dealAppNo) || StringUtils.isEmpty(checkStatus)) {
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_PARAMS_ERROR);
        }

        // 核人与录入人是同一人
        if (!Constants.OPERATION_ABOLISH.equals(operation)) {
            String creator = StringUtils.isEmpty(checkOrderDto.getCreator()) ? "" : checkOrderDto.getCreator();
            if (creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_REPLY);
            }
        }
        // 审核成功需要检查转入卡用户是否已经解绑
        if(CheckTypeEnum.CHECK_SUCC.getCode().equals(checkStatus)){
            validVolInFundBankAcctInfo(checkOrderDto.getTxAcctNo(),checkOrderDto.getDisCode(),checkOrderDto.getCpAcctNo());
        }
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        // 份额迁移审核
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        BeanUtils.copyProperties(checkOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setCheckFlag(checkStatus);
        submitUncheckOrderDto.setMemo(checkFaildDesc);
        submitUncheckOrderDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setDealNo(submitUncheckOrderDto.getDealAppNo());
        submitUncheckOrderDto.setReturnCode(ExceptionCodes.SUCCESS);
        submitUncheckOrderDto.setDescription("成功");

        BaseResponse rs = tmsCounterService.checkVolOnlineTransOrder(submitUncheckOrderDto,materialDtlDto);
        rst.setCode(rs.getReturnCode());
        rst.setDesc(rs.getDescription());


        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 
     * queryMergeTransCheckOrder:(查询申请订单列表)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月9日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/queryMergeTransCheckOrder.htm")
    public ModelAndView queryMergeTransCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String queryOrderCmd = request.getParameter("queryConditonForm");
        String pageNum = StringUtils.isEmpty(request.getParameter("page")) ? "1" : request.getParameter("page");
        String pageSize = StringUtils.isEmpty(request.getParameter("pageSize")) ? "20" : request.getParameter("pageSize");
        String owner = request.getParameter("owner");
        String checkFlag = request.getParameter("checkFlag");
        String tradeDt = request.getParameter("tradeDt");
        logger.debug("queryOrderCmd:{}, checkFlag:{}, tradeDt:{}", queryOrderCmd, checkFlag, tradeDt);

        CounterQueryOrderReqDto counterQueryOrderReqDto = JSON.parseObject(queryOrderCmd, CounterQueryOrderReqDto.class);
        counterQueryOrderReqDto.setPageNo(Integer.parseInt(pageNum));
        counterQueryOrderReqDto.setPageSize(Integer.parseInt(pageSize));
        if (StringUtils.isNotEmpty(checkFlag)) {
            counterQueryOrderReqDto.setCheckFlag(checkFlag);
        }
        
        // 业务码为空时, 查合并或迁移待审核单
        if(StringUtils.isEmpty(counterQueryOrderReqDto.getTxCode()) && CollectionUtils.isEmpty(counterQueryOrderReqDto.getTxCodeList())){
            List<String> txCodes = new ArrayList<String>();
            txCodes.add(TxCodes.COUNTER_MERGE_VOL);
            txCodes.add(TxCodes.COUNTER_TRANS_VOL);
            txCodes.add(TxCodes.ONLINE_CHANGE_CARD);
            txCodes.add(TxCodes.COUNTER_MODIFY_DISCOUNT_INFO);
            counterQueryOrderReqDto.setTxCodeList(txCodes);
        }

        // 我的交易申请查询需要绑定操作员号
        if (Constants.ROLE_OWNER.equals(owner)) {
            OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
            counterQueryOrderReqDto.setCreator(operatorInfoCmd.getOperatorNo());

            // 我的交易申请查询默认查询当前工作日
            if (Constants.ORDER_CMD_BRACKETS.equals(queryOrderCmd)) {
                counterQueryOrderReqDto.setTradeDt(tradeDt);
            }
        }

        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        if (StringUtils.isEmpty(counterQueryOrderReqDto.getTxAcctNo())
                && StringUtils.isNotEmpty(counterQueryOrderReqDto.getIdNo())) {
            queryCustBaseInfoReqDto.setIdNo(counterQueryOrderReqDto.getIdNo());
            QueryCustBaseInfoRespDto qeryCustBaseInfoRespDto = tmsCounterService.queryCustBaseInfo(queryCustBaseInfoReqDto, null);
            counterQueryOrderReqDto.setTxAcctNo(qeryCustBaseInfoRespDto.getTxAcctNo());
        }
        logger.debug("queryMergeTransCheckOrder|counterQueryOrderReqDto:{}", JSON.toJSONString(counterQueryOrderReqDto));

        CounterQueryOrderRespDto counterQueryOrderRespDto = tmsFundCounterService.counterQueryOrder(counterQueryOrderReqDto, null);
         if(counterQueryOrderRespDto == null){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD);
        }
        // 脱敏
        if (!CollectionUtils.isEmpty(counterQueryOrderRespDto.getCounterOrderList())) {
            for (CounterOrderDto dto : counterQueryOrderRespDto.getCounterOrderList()) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(4);
        this.chooseAsset(counterQueryOrderReqDto.getAssetScope(), counterQueryOrderRespDto);
        body.put("counterQueryOrderRespDto", counterQueryOrderRespDto);
        this.formateDt(counterQueryOrderRespDto);
        body.put("totalPage", counterQueryOrderRespDto.getTotalPage());
        body.put("pageNum", counterQueryOrderRespDto.getPageNo());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }


    private void formateDt(CounterQueryOrderRespDto counterQueryOrderRespDto){
        if (counterQueryOrderRespDto != null) {
            List<CounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();
            if (!CollectionUtils.isEmpty(counterOrderList)) {
                for (CounterOrderDto counterOrderDto : counterOrderList) {
                    if (counterOrderDto!= null) {
                       counterOrderDto.setAppDt(DateUtil.format(counterOrderDto.getAppDt() + counterOrderDto.getAppTm(), "yyyyMMddHHmmss", "yyyy-MM-dd HH:mm:ss"));
                    }
                }
            }
        }
    }


    @RequestMapping("/tmscounter/fund/querySubmitAppOrderById.htm")
    public ModelAndView querySubmitAppOrderById(HttpServletRequest request, HttpServletResponse response) throws Exception {
    	TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
    	String dealAppNo = request.getParameter("dealAppNo");
        String pageNum = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
    	
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        // 主申请单
        CounterOrderDto counterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);
        
        // 明细申请单
        SubmitUncheckOrderDtlAllDto dtlOrderDto = tmsFundCounterService.querySubmitUnCheckDtlOrderPage(queryReqDto, null);
       
        long totalPage = 0L;
        long page = 0L;
        long totalCount = 0L;
        List<SubmitUncheckOrderDtlDto> submitUncheckOrderDtlList = null;
        if(dtlOrderDto != null){
        	totalPage = dtlOrderDto.getTotalPage();
        	page = dtlOrderDto.getPageNo();
        	totalCount = dtlOrderDto.getTotalCount();
        	submitUncheckOrderDtlList = dtlOrderDto.getSubmitUncheckOrderDtlList();
        }
        
        
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("counterOrderDto", counterOrderDto);
        body.put("dtlOrderDtoList", submitUncheckOrderDtlList);
        body.put("totalPage", totalPage);
        body.put("pageNum", page);
        body.put("totalCount", totalCount);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 根据资产范围取值
     * @param assetScope
     * @param counterQueryOrderRespDto
     * @return
     */
    private void chooseAsset(String assetScope, CounterQueryOrderRespDto counterQueryOrderRespDto){
        List<CounterOrderDto> counterOrderList = counterQueryOrderRespDto.getCounterOrderList();
        if (CollectionUtils.isEmpty(counterOrderList) || StringUtils.isEmpty(assetScope)) {
            return;
        }
        List<CounterOrderDto> counterOrderDtos = new ArrayList<>();
        if (AssetScopeEnum.SCOPE_BELOW_2000.CODE.equals(assetScope)) {
            for (CounterOrderDto counterOrderDto : counterOrderList) {
                if (this.isBelow2000(counterOrderDto.getAsset())) {
                    counterOrderDtos.add(counterOrderDto);
                }
            }
            counterQueryOrderRespDto.setCounterOrderList(counterOrderDtos);
            return;
        }

        if (AssetScopeEnum.SCOPE_2000_TO_20000.CODE.equals(assetScope)) {
            for (CounterOrderDto counterOrderDto : counterOrderList) {
                if (this.is2000To20000(counterOrderDto.getAsset())) {
                    counterOrderDtos.add(counterOrderDto);
                }
            }
            counterQueryOrderRespDto.setCounterOrderList(counterOrderDtos);
            return;
        }

        if (AssetScopeEnum.SCOPE_ABOVE_20000.CODE.equals(assetScope)) {
            for (CounterOrderDto counterOrderDto : counterOrderList) {
                if (this.isAbove20000(counterOrderDto.getAsset())) {
                    counterOrderDtos.add(counterOrderDto);
                }
            }
            counterQueryOrderRespDto.setCounterOrderList(counterOrderDtos);
            return;
        }

    }

    /**
     * 资产小于2000
     * @param asset
     * @return
     */
    private boolean isBelow2000(BigDecimal asset) {
        if (asset == null) {
            asset = BigDecimal.ZERO;
        }
        return asset.compareTo(new BigDecimal("2000")) < 0;
    }

    /**
     * 2000-20000
     * @param asset
     * @return
     */
    private boolean is2000To20000(BigDecimal asset){
        if (asset == null) {
            asset = BigDecimal.ZERO;
        }
        return asset.compareTo(new BigDecimal("2000")) >= 0 && asset.compareTo(new BigDecimal("20000")) < 0;
    }


    /**
     * 大于20000
     * @param asset
     * @return
     */
    private boolean isAbove20000(BigDecimal asset){
        if (asset == null) {
            asset = BigDecimal.ZERO;
        }
        return asset.compareTo(new BigDecimal("20000")) >=0;
    }

    /**
     * 
     * queryMergeTransCheckOrderbyId:(查询申请订单明细信息)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月9日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/queryMergeTransCheckOrderbyId.htm")
    public ModelAndView queryMergeTransCheckOrderbyId(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String pageNum = request.getParameter("pageNum");
        String pageSize = request.getParameter("pageSize");
        String checkNode = request.getParameter("checkNode");
        String txCode = request.getParameter("txCode");


        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        // 主申请单
        CounterOrderDto counterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);
        // 查询卡信息
        QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
        queryAllCustInfoContext.setDisCode(counterOrderDto.getDisCode());
        queryAllCustInfoContext.setTxAcctNo(counterOrderDto.getTxAcctNo());
        queryAllCustInfoContext.setCpAcctNo(counterOrderDto.getCpAcctNo());
        QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);
        if (queryAllCustInfoResult != null) {
            if (queryAllCustInfoResult.getCustBankCardInfo() != null) {
                counterOrderDto.setBankAcct(queryAllCustInfoResult.getCustBankCardInfo().getBankAcct());
            }
            if (queryAllCustInfoResult.getCustInfo() != null) {
                counterOrderDto.setIdNo(queryAllCustInfoResult.getCustInfo().getIdNo());
            }
        }
        
        QueryAllBankCardSensitiveInfoContext ctx = new QueryAllBankCardSensitiveInfoContext();
        ctx.setTxAcctNo(counterOrderDto.getTxAcctNo());
        ctx.setDisCode(counterOrderDto.getDisCode());
        QueryAllBankCardSensitiveInfoResult allBankResult = queryAllBankAcctSensitiveInfoOuterService.queryAllBankAcctSensitiveInfo(ctx);
        Map<String, CustAllBankSensitiveModel> bankMap = new HashMap<String, CustAllBankSensitiveModel>(16);
        if(allBankResult.getCustBankModelList() != null && allBankResult.getCustBankModelList().size() > 0){
        	for(CustAllBankSensitiveModel model : allBankResult.getCustBankModelList()){
        		bankMap.put(model.getCpAcctNo(), model);
        	}
        }
        
        String bankAcct = null;
        // 明细申请单
        List<SubmitUncheckOrderDtlDto> dtlOrderDto = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
        if (!CollectionUtils.isEmpty(dtlOrderDto)) {
            // 查询卡信息
            for (SubmitUncheckOrderDtlDto dto : dtlOrderDto) {
            	CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(dto.getCpAcctNo());
            	if(custAllBankSensitiveModel != null){
            		dto.setBankAcct(custAllBankSensitiveModel.getBankAcct());
            		bankAcct = custAllBankSensitiveModel.getBankAcct();
            	}
            }
        }

        List<ExchangeCardMaterialDtlDto> materialDtlDtoList = null;
        List<SchePlanCounterDto> schePlanList = null;
        QueryLatestPayCustResult payCustResult = null;
        if(TxCodes.ONLINE_CHANGE_CARD.equals(txCode)){
            // 查询线上换卡上传资料
            materialDtlDtoList = tmsFundCounterService.queryMaterialDtl(queryReqDto,null);

            if(!CollectionUtils.isEmpty(materialDtlDtoList)){
                for(ExchangeCardMaterialDtlDto dto : materialDtlDtoList){
                    dto.setFilePath(convertFilePath(dto.getFilePath()));
                }
            }
            // 人脸识别是证件照照片
            buildIdInfoAndFaceInfo(counterOrderDto, materialDtlDtoList);

            payCustResult = tmsFundCounterService.queryLatestPayRecord(counterOrderDto.getTxAcctNo(), counterOrderDto.getDisCode(), null, DigestUtil.digest(bankAcct));

            List<String> dtlCpAcctNos = getSubmitUncheckOrderDtlDto(dtlOrderDto);
            // 查询合约
            QuerySchePlanCounterRequest req = new QuerySchePlanCounterRequest();
            req.setTxAcctNo(counterOrderDto.getTxAcctNo());
            req.setCpAcctNos(dtlCpAcctNos);
            QuerySchePlanCounterResponse res = orderPlanService.querySchePlanCounter(req);
            schePlanList = res.getResults();
            if(schePlanList != null && schePlanList.size() > 0){
            	for(SchePlanCounterDto dto : schePlanList){
            		CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(dto.getCpAcctNo());
            		if(custAllBankSensitiveModel != null){
            			dto.setBankAcct(custAllBankSensitiveModel.getBankAcct());
            		}
            	}
            }
        }
        
        List<String> cpAcctNos = getInTransitCpAcctNos(counterOrderDto.getTxAcctNo(), counterOrderDto.getCpAcctNo());

        //  份额迁移：查出转入银行卡资产信息
        QueryCustBankBalVolRespDto respData = null;
        if(TxCodes.COUNTER_TRANS_VOL.equals(counterOrderDto.getTxCode()) || TxCodes.ONLINE_CHANGE_CARD.equals(counterOrderDto.getTxCode())){
            QueryCustBankBalVolReqDto reqDto = new QueryCustBankBalVolReqDto();
            reqDto.setCustNo(counterOrderDto.getTxAcctNo());
            reqDto.setIdNo(counterOrderDto.getIdNo());
            reqDto.setDisCode(counterOrderDto.getDisCode());
            reqDto.setFundCode(counterOrderDto.getFundCode());
            reqDto.setShareType(ShareTypeEnum.FUND_SHARE_TRANSFER.getCode());
            reqDto.setBankAcct(counterOrderDto.getBankAcct());
            reqDto.setCpAcctNos(cpAcctNos);

            DisInfoDto disDto = new DisInfoDto();
            disDto.setDisCode(counterOrderDto.getDisCode());
            respData = tmsFundCounterService.queryCustBankBalVol(reqDto, disDto);
            
            if(respData != null && respData.getCustBalDtlList() != null && respData.getCustBalDtlList().size() > 0){
            	for(CustBalDtlDto balDto : respData.getCustBalDtlList()){
            		CustAllBankSensitiveModel custAllBankSensitiveModel = bankMap.get(balDto.getCpAcctNo());
            		if(custAllBankSensitiveModel != null){
            			balDto.setBankAcct(custAllBankSensitiveModel.getBankAcct());
            		}
            	}
            }
        }

        // 查询CRM线上化资料
        QueryOrderFileDto queryOrderFileDto = new QueryOrderFileDto();
        if(StringUtils.isNotEmpty(counterOrderDto.getMaterialId())){
            QueryOrderFileContext queryContext = new QueryOrderFileContext();
            queryContext.setOrderid(counterOrderDto.getMaterialId());
            queryOrderFileDto = tmsCounterOutService.queryOrderFile(queryContext, checkNode);
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("checkOrder", counterOrderDto);
        body.put("checkDtlOrder", dtlOrderDto);
        body.put("orderFile", queryOrderFileDto);
        body.put("respData", respData);
        body.put("materialDtl", materialDtlDtoList);
        body.put("schePlanList", schePlanList);
        body.put("capitalPayRecord", payCustResult);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    private void buildIdInfoAndFaceInfo(CounterOrderDto counterOrderDto, List<ExchangeCardMaterialDtlDto> materialDtlDtoList) {
        // 查询人脸识别
        QueryIdAndFaceInfoResult result = queryIdCardInfoOuterService.queryIdCardInfoAndFaceInfo(counterOrderDto.getTxAcctNo(), null);
        logger.info("QueryIdAndFaceInfoResult : {}",JSON.toJSONString(result));
        if (Objects.nonNull(result)){
            if (StringUtils.isNotEmpty(result.getPicturePath())){
                // 身份证正反面
//                String backPath = tmsCounterNacosConfig.getIdCardPathUrl() + processAccIdCardUpFile(result.getPicturePath()) + result.getBackPictureName();
                ExchangeCardMaterialDtlDto backDto = new ExchangeCardMaterialDtlDto();
                backDto.setFilePath(result.getPicturePath());
                backDto.setMaterialType(MaterialTypeEnum.MATERIAL_TYPE_CERTIFICATE.getCode());
                backDto.setFileName(result.getBackPictureName());
                materialDtlDtoList.add(backDto);

//                String frontPathPath = tmsCounterNacosConfig.getIdCardPathUrl() + processAccIdCardUpFile(result.getPicturePath()) + result.getFrontPictureName();
                ExchangeCardMaterialDtlDto frontDto = new ExchangeCardMaterialDtlDto();
                frontDto.setFilePath(result.getPicturePath());
                frontDto.setMaterialType(MaterialTypeEnum.MATERIAL_TYPE_CERTIFICATE.getCode());
                frontDto.setFileName(result.getFrontPictureName());
                materialDtlDtoList.add(frontDto);
            }

            if (StringUtils.isNotBlank(result.getFaceImagePath())){
//                String facePath = tmsCounterNacosConfig.getIdCardPathUrl() + result.getFaceImagePath() + "/" + result.getFaceImageName();
                ExchangeCardMaterialDtlDto faceDto = new ExchangeCardMaterialDtlDto();
                faceDto.setFilePath( result.getFaceImagePath());
                // 人脸识别
                faceDto.setMaterialType("99");
                faceDto.setFileName(result.getFaceImageName());
                materialDtlDtoList.add(faceDto);
            }
        }
    }

    public static String processAccIdCardUpFile(String originalPath) {

        String keyword = "/cim_web_server/center_feature";
        int startIndex = originalPath.lastIndexOf(keyword);

        // 如果找到关键字，返回截取后的字符串
        if (startIndex != -1) {
            return originalPath.substring(startIndex + keyword.length());
        }
        // 如果没有找到，返回原始字符串或处理为其他逻辑
        return originalPath;

    }

    /**
     * 迁移的资金账号
     * getSubmitUncheckOrderDtlDto:迁移的资金账号
     * @param dtlOrderDtos
     * @return
     * <AUTHOR>
     * @date 2022年4月21日 下午6:33:06
     */
    private List<String> getSubmitUncheckOrderDtlDto(List<SubmitUncheckOrderDtlDto> dtlOrderDtos) {
        List<String> cpAcctNos = new ArrayList<String>();
        if(CollectionUtils.isEmpty(dtlOrderDtos)) {
            return cpAcctNos;
        }
        for(SubmitUncheckOrderDtlDto dto : dtlOrderDtos) {
            if(!cpAcctNos.contains(dto.getCpAcctNo())) {
                cpAcctNos.add(dto.getCpAcctNo());
            }
        }
        return cpAcctNos;
    }

    /**
     * @description:转换文件路径
     * @param filePath
     * @return java.lang.String
     * @author: dejun.gu
     * @date: 2020/11/23 18:25
     * @since JDK 1.8
     */
    private String convertFilePath(String filePath) {
        if(!StringUtil.isEmpty(filePath)){
            return tmsCounterNacosConfig.getOnlineChangeCardUrl() +filePath.replace(tmsCounterNacosConfig.getOnlineChangeCardFilePath(),"");
        }
       return "";
    }

    /**
     * 
     * queryMergeTransTradeOrderById:(查询交易订单信息)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月9日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/queryMergeTransTradeOrderById.htm")
    public ModelAndView queryMergeTransTradeOrderById(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealNo = request.getParameter("dealNo");
        String dealAppNo = request.getParameter("dealAppNo");
        String disCode = request.getParameter("disCode");
        String pageNum = request.getParameter("pageNum");
        String pageSize = request.getParameter("pageSize");
        
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealNo(dealNo);
        queryReqDto.setDealAppNo(dealAppNo);
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        DisInfoDto disDto = new DisInfoDto();
        disDto.setDisCode(disCode);
        
        // 主申请单
        CounterOrderDto counterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);
        // 明细申请单
        List<SubmitUncheckOrderDtlDto> dtlOrderDto = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
        
        // 查询交易订单
        List<CounterShareMergeTradeOrderDto> tradeOrders = new ArrayList<CounterShareMergeTradeOrderDto>();
        
        // 零售订单
        List<CounterShareMergeTradeOrderDto> fundOrders = tmsFundCounterService.queryShareMergeTradeOrder(queryReqDto, disDto);
        if(!CollectionUtils.isEmpty(fundOrders)) {
        	tradeOrders.addAll(fundOrders);
        }
        
        // 专户订单
        List<CounterShareMergeTradeOrderDto> highOrders = tmsCounterService.queryHighShareMergeTradeOrder(queryReqDto, disDto);
        if(!CollectionUtils.isEmpty(highOrders)) {
        	tradeOrders.addAll(highOrders);
        }
        
        // 储蓄罐订单
        List<CounterShareMergeTradeOrderDto> piggyOrders = tmsCounterService.queryPiggyShareMergeTradeOrder(queryReqDto, disDto);
        if(!CollectionUtils.isEmpty(piggyOrders)) {
        	for(CounterShareMergeTradeOrderDto o : piggyOrders) {
        		o.setCustName(counterOrderDto.getCustName());
        	}
        	tradeOrders.addAll(piggyOrders);
        }

        
        List<String> dtlCpAcctNos = getSubmitUncheckOrderDtlDto(dtlOrderDto);
        QuerySchePlanCounterRequest req = new QuerySchePlanCounterRequest();
        req.setTxAcctNo(counterOrderDto.getTxAcctNo());
        req.setCpAcctNos(dtlCpAcctNos);
        QuerySchePlanCounterResponse res = orderPlanService.querySchePlanCounter(req);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("tradeOrders", tradeOrders);
        body.put("schedulePlans", res.getResults());
        body.put("dtlOrderDtos", dtlOrderDto);
        body.put("counterOrderDto", counterOrderDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     *
     * queryMergeTransCheckOrderbyId:(查询申请订单明细信息)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2021年2月25日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/queryChangeDiscountCheckOrderById.htm")
    public ModelAndView queryChangeDiscountCheckOrderById(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");

        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        // 主申请单
        CounterOrderDto counterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);

        // 明细申请单
        List<SubmitUncheckOrderDtlDto> dtlOrderDto = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("checkOrder", counterOrderDto);
        body.put("checkDtlOrder", dtlOrderDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     *
     * checkChangeDiscountConfirm:(审核确认)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2021年2月25日 上午10:28:53
     */
    @RequestMapping("/tmscounter/fund/checkChangeDiscountConfirm.htm")
    public ModelAndView checkChangeDiscountConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);

        String checkStatus = request.getParameter("checkStatus");
        String checkFaildDesc = request.getParameter("checkFaildDesc");
        String checkedOrderForm = request.getParameter("checkedOrderForm");
        String operation = request.getParameter("operation");
        logger.debug("CheckMergeTransVolController|checkMergeTransConfirm: checkedOrderForm:{}, checkStatus:{}", checkedOrderForm, checkStatus);

        CounterOrderDto checkOrderDto = JSON.parseObject(checkedOrderForm, CounterOrderDto.class);
        String dealAppNo = checkOrderDto != null ? checkOrderDto.getDealAppNo() : null;
        if(StringUtils.isEmpty(dealAppNo) || StringUtils.isEmpty(checkStatus)){
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_PARAMS_ERROR);
        }

        // 核人与录入人是同一人
        if (!Constants.OPERATION_ABOLISH.equals(operation)) {
            String creator = StringUtils.isEmpty(checkOrderDto.getCreator()) ? "" : checkOrderDto.getCreator();
            if (creator.equals(operatorInfoCmd.getOperatorNo())) {
                throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECKER_REPLY);
            }
        }

        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        // 明细申请单
        List<SubmitUncheckOrderDtlDto> checkDtlOrders = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
        if(CollectionUtils.isEmpty(checkDtlOrders)){
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_CHECK_NOT_QUERY_ORDER);
        }

//        DisInfoDto disInfoDto = new DisInfoDto();
//        disInfoDto.setDisCode(checkOrderDto.getDisCode());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, checkOrderDto);
        SubmitUncheckOrderDto submitUncheckOrderDto = new SubmitUncheckOrderDto();
        BeanUtils.copyProperties(checkOrderDto, submitUncheckOrderDto);
        submitUncheckOrderDto.setChecker(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setModifier(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setCheckDtm(new Date());
        submitUncheckOrderDto.setCheckFlag(checkStatus);
        submitUncheckOrderDto.setMemo(checkFaildDesc);
        submitUncheckOrderDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        submitUncheckOrderDto.setReturnCode(ExceptionCodes.SUCCESS);
        submitUncheckOrderDto.setDescription("成功");
        submitUncheckOrderDto.setDealNo(submitUncheckOrderDto.getDealAppNo());
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        BaseResponse rs = tmsCounterService.checkChangeDiscountOrder(submitUncheckOrderDto);
        rst.setCode(rs.getReturnCode());
        rst.setDesc(rs.getDescription());


        WebUtil.write(response, rst);
        return null;
    }

    
    /**
     * 获取换卡的银行卡id
     * getInTransitCpAcctNos
     * @Title: getInTransitCpAcctNos
     * @Description: 获取换卡的银行卡id
     * @param @param cpAcctNo
     * @param @param bankCardAllList
     * @param @return 参数
     * @return List<String> 返回类型
     * <AUTHOR>
     * @date 2022年4月13日 下午2:13:32
     *
     */
    private List<String> getInTransitCpAcctNos(String custNo, String cpAcctNo) {
        QueryAllBankCardInfoResult queryAllBankCardInfoResult = tmsCounterOutService.queryAllBankCardInfo(custNo, DefaultParamsConstant.DEFULT_DIS_CODE);
        List<CustBankModel> custBankModelList = queryAllBankCardInfoResult.getCustBankModelList();
        
        List<String> cpAcctNos = new ArrayList<String>();
        if(StringUtil.isEmpty(cpAcctNo) || CollectionUtils.isEmpty(custBankModelList)) {
            return cpAcctNos;
        }
        String bankAcctDigest = null; 
        for(CustBankModel model : custBankModelList) {
            if(cpAcctNo.equals(model.getCpAcctNo())) {
                bankAcctDigest = model.getBankAcctDigest();
                break;
            }
        }
        if(StringUtil.isEmpty(bankAcctDigest)) {
            return cpAcctNos;
        }
        
        for(CustBankModel model : custBankModelList) {
            if(bankAcctDigest.equals(model.getBankAcctDigest())) {
                cpAcctNos.add(model.getCpAcctNo());
            }
        }
        return cpAcctNos;
    }
    
}
