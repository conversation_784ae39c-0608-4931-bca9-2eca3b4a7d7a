/**
*查询柜台复核订单
*<AUTHOR>
*@date 2017-04-11 16:17
*/

var RegularQueryCheckOrder ={
		queryCheckOrderById:function(dealAppNo, callBack){
			var  uri= TmsCounterConfig.QUERY_REGULAR_CHECK_ORDER_BY_ID_URL  ||  {};
			var reqparamters = {};
			reqparamters.dealAppNo = dealAppNo;
			reqparamters.pageNum = 1;
			reqparamters.pageSize = 50;
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,"post",null);
			CommonUtil.ajaxAndCallBack(paramters, callBack);
		},	
		
		queryCheckOrderList:function(custNo){
			
		},

		queryCheckOrderListBack:function(data){
			
		}
};
