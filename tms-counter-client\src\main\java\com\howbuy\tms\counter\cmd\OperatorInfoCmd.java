/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.cmd;

import java.io.Serializable;

/**
 * @description:(操作员信息) 
 * <AUTHOR>
 * @date 2017年4月6日 下午5:31:04
 * @since JDK 1.6
 */
public class OperatorInfoCmd implements  Serializable{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -6578036736594039292L;
    /**
     * 操作员代码
     */
    private String operatorNo ;
    /**
     * 操作员名称
     */
    private String operName ;
    /**
     * 权限
     */
    private String role;
    public String getOperatorNo() {
        return operatorNo;
    }
    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }
    public String getOperName() {
        return operName;
    }
    public void setOperName(String operName) {
        this.operName = operName;
    }
    public String getRole() {
        return role;
    }
    public void setRole(String role) {
        this.role = role;
    }
    
}

