/**
*零售柜台废单
*<AUTHOR>
*@date 2017-09-18 15:23
*/
$(function(){
	//废单
	CounterAbolish.Abolish = '4';
});
var CounterAbolish = {
	 abolish : function (uri, checkStatus, checkedOrder){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';
		
		if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
			window.checkedClick = '0';
			CommonUtil.layer_tip("请输入废单原因");
			return false;
		}
		
		checkFaildDesc = $("#checkFaildDesc").val();
		
		var reqparamters ={operation:"abolish","checkFaildDesc":checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(checkedOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CounterAbolish.callBack);
		return true;
	},
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("abolishBtn");
			CommonUtil.disabledBtnWithClass("confimBtn");
			CommonUtil.layer_tip("成功");
		}else{
			window.checkedClick = '0';
			CommonUtil.layer_tip(respDesc);
		}
	}
};
