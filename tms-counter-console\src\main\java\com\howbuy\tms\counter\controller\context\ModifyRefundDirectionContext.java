/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.controller.context;
import com.howbuy.tms.counter.dto.OrderDto;
import com.howbuy.tms.counter.dto.RefundDto;

/**
 * @description:修改回款方向
 * @author: chuanguang.tang
 * @date: 2021/8/4 16:43
 * @since JDK 1.8
 */
public class ModifyRefundDirectionContext extends TradeCommonContext {

    /**
     * 订单详情
     */
    private OrderDto orderDto;
    /**
     * 回款方向
     */
    private RefundDto refundDto;
    /**
     * 数据操作类型 1：新增 2：修改
     */
    private String dbFlag;

    private String dealAppNo;

    public OrderDto getOrderDto() {
        return orderDto;
    }

    public void setOrderDto(OrderDto orderDto) {
        this.orderDto = orderDto;
    }

    public RefundDto getRefundDto() {
        return refundDto;
    }

    public void setRefundDto(RefundDto refundDto) {
        this.refundDto = refundDto;
    }

    public String getDbFlag() {
        return dbFlag;
    }

    public void setDbFlag(String dbFlag) {
        this.dbFlag = dbFlag;
    }

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }
}
