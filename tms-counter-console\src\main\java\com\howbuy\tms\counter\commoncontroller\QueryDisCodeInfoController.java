/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.DisChannelBean;
import com.howbuy.tms.counter.common.TmsCounterConstants;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description:(好买支持分销查询)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年9月18日 下午4:41:20
 * @since JDK 1.6
 */
@Controller
public class QueryDisCodeInfoController extends AbstractController {
    
    Logger logger = LoggerFactory.getLogger(QueryDisCodeInfoController.class);

    @RequestMapping("tmscounter/fund/querydiscodeinfo.htm")
    public void queryConsInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<DisChannelBean> disChannelBeanList = tmsFundCounterOutService.selectAllDisChannel();
        // 分销排序
        List<DisChannelBean> sortedDisChannelBeanList = disChannelSort(disChannelBeanList);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("disCodes", sortedDisChannelBeanList);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }
    
    /**
     * 
     * disChannelSort:(分销机构排序:默认好买和FOF分销)
     * @param disChannelBeanList
     * @return
     * <AUTHOR>
     * @date 2018年4月9日 下午3:06:42
     */
    private List<DisChannelBean> disChannelSort(List<DisChannelBean> disChannelBeanList){
        List<DisChannelBean> sortedDisChannelBeanList = new ArrayList<DisChannelBean>();
        if(CollectionUtils.isEmpty(disChannelBeanList)){
            return sortedDisChannelBeanList;
        }
        Iterator<DisChannelBean> iterator = disChannelBeanList.iterator();
        while(iterator.hasNext()){
            DisChannelBean disChannelBean = iterator.next();
            logger.info("QueryDisCodeInfoController|disChannelSort|disCode:{} is howbuy {} or isFOF:{}", new Object[]{ disChannelBean.getDisCode(), TmsCounterConstants.HB_DIS_CODE.equals(disChannelBean.getDisCode()),
                    TmsCounterConstants.FOF_DIS_CODE.equals(disChannelBean.getDisCode())});
            if(TmsCounterConstants.HB_DIS_CODE.equals(disChannelBean.getDisCode())){
                // 好买分销
                sortedDisChannelBeanList.add(disChannelBean);
                iterator.remove();
            }
            
            if(TmsCounterConstants.FOF_DIS_CODE.equals(disChannelBean.getDisCode())){
                // FOF机构分销
                sortedDisChannelBeanList.add(disChannelBean);
                iterator.remove();
            }
            
        }
        
        sortedDisChannelBeanList.addAll(disChannelBeanList);
        return sortedDisChannelBeanList;
    }
}
