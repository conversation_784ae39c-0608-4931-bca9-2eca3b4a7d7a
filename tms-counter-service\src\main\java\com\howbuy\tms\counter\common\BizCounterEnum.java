/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;
/**
 * 参数问题
 * @description:参数问题
 * @reason:参数问题
 * <AUTHOR>
 * @date 2022年4月22日 上午10:24:57
 * @since JDK 1.8
 */
public enum BizCounterEnum {
    
    /**
     * 资金账号为空
     */
    CPACCTNO_IS_EMPTY("C000001", "资金账号为空"),
    
    /**
     * 成功
     */
    TRANSIT_EXITS("C000002", "存在在途交易"),
    
    /**
     * 银行卡已被更换
     */
    BANK_IS_CHANGE("C000003", "银行卡已被更换"),
    
    /**
     * 换卡失败
     */
    BANK_CHANGE_ERROR("C000004", "换卡失败"),
    
    /**
     * 高端校验失败
     */
    HIGH_VAL_ERROR("C000005", "高端校验失败"),
    
    /**
     * 公募校验失败
     */
    GONGMU_VAL_ERROR("C000006", "公募校验失败");
    
    
    private String code;
    
    private String desc;
    
    BizCounterEnum(String code, String desc){
        this.code = code;
        this.desc =desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}

