package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class QueryAppRateChangeDto implements Serializable{

	private static final long serialVersionUID = 3818249648841325241L;
	
	/**
     * 订单号
     */
    private String dealNo;
    
    /**
     * 订单明细号
     */
    private String dealDtlNo;
    
    /**
     * 上报订单号
     */
    private String submitDealNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 基金名称
     */
    private String fundName;
    
    /**
     * 分销机构
     */
    private String disCode;
    
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 客户姓名
     */
    private String custName;
    
    /**
     * 证件号码
     */
    private String idNo;
    
    /**
     * 中台业务代码
     */
    private String mBusiCode;
    
    /**
     * 标准业务名称
     */
    private String busiCode;
    
    /**
     * 中台业务码
     */
    private String zBusiCode;

    /**
     * 主订单-银行卡号
     */
    private String bankAcct;
    
    /**
     * 支付方式
     */
    private String paymentType;
    
    /**
     * 主订单-银行编号
     */
    private String bankCode;
    
    /**
     * 主订单-资金帐号
     */
    private String cpAcctNo;
    
    /**
     * 主订单-协议号
     */
    private String protocolNo;
    
    /**
     * 主订单-协议类型
     */
    private String protocolType;

    /**
     * 订单明细申请标记
     */
    private String txAppFlag;
    
    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    /**
     * 申请份额
     */
    private BigDecimal appVol;
    /**
     * 确认金额
     */
    private BigDecimal ackAmt;
    /**
     * 确认份额
     */
    private BigDecimal ackVol;
    /**
     * 基金净值
     */
    private BigDecimal nav;
    /**
     * 申请日期
     */
    private String appDate;
    /**
     * 申请时间
     */
    private String appTime;
    /**
     * TA交易日期
     */
    private String taTradeDt;
    /**
     * 折扣率
     */
    private BigDecimal discountRate;
    
	public String getBusiCode() {
		return busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getSubmitDealNo() {
		return submitDealNo;
	}

	public void setSubmitDealNo(String submitDealNo) {
		this.submitDealNo = submitDealNo;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public String getzBusiCode() {
		return zBusiCode;
	}

	public void setzBusiCode(String zBusiCode) {
		this.zBusiCode = zBusiCode;
	}

	public BigDecimal getAppAmt() {
		return appAmt;
	}

	public void setAppAmt(BigDecimal appAmt) {
		this.appAmt = appAmt;
	}

	public BigDecimal getAppVol() {
		return appVol;
	}

	public void setAppVol(BigDecimal appVol) {
		this.appVol = appVol;
	}

	public BigDecimal getAckAmt() {
		return ackAmt;
	}

	public void setAckAmt(BigDecimal ackAmt) {
		this.ackAmt = ackAmt;
	}

	public BigDecimal getAckVol() {
		return ackVol;
	}

	public void setAckVol(BigDecimal ackVol) {
		this.ackVol = ackVol;
	}

	public BigDecimal getNav() {
		return nav;
	}

	public void setNav(BigDecimal nav) {
		this.nav = nav;
	}

	public String getAppDate() {
		return appDate;
	}

	public void setAppDate(String appDate) {
		this.appDate = appDate;
	}

	public String getAppTime() {
		return appTime;
	}

	public void setAppTime(String appTime) {
		this.appTime = appTime;
	}

	public String getTaTradeDt() {
		return taTradeDt;
	}

	public void setTaTradeDt(String taTradeDt) {
		this.taTradeDt = taTradeDt;
	}

	public BigDecimal getDiscountRate() {
		return discountRate;
	}

	public void setDiscountRate(BigDecimal discountRate) {
		this.discountRate = discountRate;
	}

	public String getTxAppFlag() {
		return txAppFlag;
	}

	public void setTxAppFlag(String txAppFlag) {
		this.txAppFlag = txAppFlag;
	}

	public String getDealNo() {
		return dealNo;
	}

	public void setDealNo(String dealNo) {
		this.dealNo = dealNo;
	}

	public String getDealDtlNo() {
		return dealDtlNo;
	}

	public void setDealDtlNo(String dealDtlNo) {
		this.dealDtlNo = dealDtlNo;
	}

	public String getFundCode() {
		return fundCode;
	}

	public void setFundCode(String fundCode) {
		this.fundCode = fundCode;
	}

	public String getFundName() {
		return fundName;
	}

	public void setFundName(String fundName) {
		this.fundName = fundName;
	}

	public String getDisCode() {
		return disCode;
	}

	public void setDisCode(String disCode) {
		this.disCode = disCode;
	}

	public String getTxAcctNo() {
		return txAcctNo;
	}

	public void setTxAcctNo(String txAcctNo) {
		this.txAcctNo = txAcctNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	public String getmBusiCode() {
		return mBusiCode;
	}

	public void setmBusiCode(String mBusiCode) {
		this.mBusiCode = mBusiCode;
	}

	public String getBankAcct() {
		return bankAcct;
	}

	public void setBankAcct(String bankAcct) {
		this.bankAcct = bankAcct;
	}

	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}

	public String getCpAcctNo() {
		return cpAcctNo;
	}

	public void setCpAcctNo(String cpAcctNo) {
		this.cpAcctNo = cpAcctNo;
	}

	public String getProtocolNo() {
		return protocolNo;
	}

	public void setProtocolNo(String protocolNo) {
		this.protocolNo = protocolNo;
	}

	public String getProtocolType() {
		return protocolType;
	}

	public void setProtocolType(String protocolType) {
		this.protocolType = protocolType;
	}
	
}
