/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.common.exception;

import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(中台柜台异常处理类)
 * <AUTHOR>
 * @date 2017年3月27日 下午4:03:34
 * @since JDK 1.7
 */
@Component
public class ExceptionHandler implements HandlerExceptionResolver {
    private static final Logger logger = LogManager.getLogger(ExceptionHandler.class);

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            logger.error("error:", ex);
            TmsCounterResult rst = null;
            if (ex instanceof TmsCounterException) {
                rst = new TmsCounterResult(((TmsCounterException) ex).getCode(), ((TmsCounterException) ex).getDesc());

            } else {
                logger.error("系统异常：" + ex.getMessage(),ex);
                rst = new TmsCounterResult(TmsCounterResultEnum.FAILD);
            }
            WebUtil.write(response, rst);
        } catch (Exception e) {
            logger.error("系统异常:", e);
        }
        return new ModelAndView();

    }
}
