/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.freemarker;

import com.jagregory.shiro.freemarker.ShiroTags;
import freemarker.template.Configuration;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModelException;
import java.io.IOException;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

/**
 * @description:freeMarker 输出 <PERSON><PERSON><PERSON>ag
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年5月11日 下午3:10:05
 * @since JDK 1.6
 */
public class ShiroTagFreeMarkerConfigurer extends FreeMarkerConfigurer {
    @Override
    public void afterPropertiesSet() throws IOException, TemplateException {
        super.afterPropertiesSet();
        Configuration cfg = this.getConfiguration();
        putInitShared(cfg);
    }

    public static void put(Configuration cfg, String k, Object v) throws TemplateModelException {
        cfg.setSharedVariable(k, v);
        // 防止页面输出数字,变成2,000
        cfg.setNumberFormat("#");
    }

    public static void putInitShared(Configuration cfg) throws TemplateModelException {
        // shiro tag
        put(cfg, "shiro", new ShiroTags());
    }
}
