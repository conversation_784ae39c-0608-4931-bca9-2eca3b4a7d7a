/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.cmd;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description:(修改柜台订单请求) 
 * <AUTHOR>
 * @date 2018年3月19日 上午10:00:44
 * @since JDK 1.6
 */
@Data
public class ModifyCounterOrderCmd implements Serializable{


    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -1007151992735991702L;
    
    /**
     * 柜台申请订单号
     */
    private String dealAppNo;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;
    
    /**
     * 含费申请金额
     */
    private BigDecimal applyAmountIncluFee;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;
    
    /**
     * 手续费折扣
     */
    private BigDecimal discountRate;
    
    /**
     * 异常赎回标识
     */
    private String unusualTransType;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    
    /**
     * 赎回去向 0-储蓄罐（人工选择），1-回银行卡（人工选择），2-回银行卡（协议默认）,3-回储蓄罐（协议默认）
     */
    private String redeemCapitalFlag;

    /**
     * 回可用余额
     */
    private BigDecimal refundFinaAvailAmt;

    /**
     * 回可用备注
     */
    private String refundFinaAvailMemo;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    /**
     * 巨额赎回顺延
     */
    private String largeRedmFlag;
    
    /**
     * 银行卡号
     */
    private String bankAcct;

    /**
     * 银行卡号
     */
    private String bankCode;
    
    
    /**
     * 是否强制撤单 0-强制 1-非强制
     */
    private String forceCancelFlag;
    
    /**
     * 撤单原因
     */
    private String  cancelMemo;
    /**
     * 是否到期赎回
     */
    private String isRedeemExpire;
    /**
     * 预计到期日期
     */
    private String preExpireDate;
    /**
     * 复购类型
     */
    private String repurchaseType;
    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 认缴金额
     */
    private BigDecimal subsAmt;
    /**
     * 子订单列表
     */
    private List<SubOrder> subOrders;

    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 是否匹配非交易
     */
    private String isNoTradeTransfer;


    public static class SubOrder implements Serializable{
        private static final long serialVersionUID = 1L;
        private String cpAcctNo;
        private BigDecimal appVol;

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }
    }


}

