/**
*公募柜台收市
*<AUTHOR>
*/

$(function(){
	FundCounterEnd.taList = [];
	FundCounterEnd.notEndTaList = [];
	FundCounterEnd.fundTaList = [];
	FundCounterEnd.init();
});

var FundCounterEnd ={

	/**
	 * 初始化
	 */
	init:function(){
		
		// 初始加载查询
		FundCounterEnd.queryWorkDay();
		FundCounterEnd.queryFundCounterEndTA();
		
		// 公募柜台收市
		$("#fundCounterEndBtn").on('click',function(){
			FundCounterEnd.fundCounterEndPreCheck();
		});
		
		// 添加不收市TA
		$("#addCounterNotEndTaBtn").on('click',function(){
			FundCounterEnd.addNotEndTa();
		});
		
		// 筛选基金添加不收市TA
		$("#addFundFilNotEndTaBtn").on('click',function(){
			FundCounterEnd.popupSelFundTaPage();
		});
		
		// 查询基金TA
		$("#SelectFundTaPage #queryFundTaInfoBtn").on('click',function(){
			FundCounterEnd.queryFundTaInfo();
		});
		
		// 添加基金TA
		$("#SelectFundTaPage #addSelFundTaBtn").on('click',function(){
			FundCounterEnd.addSelFundTa();
		});
		
		// 重置
		$("#SelectFundTaPage #clearnTaInfoBtn").on('click',function(){
			$("#SelectFundTaPage #inputfundCode").val('');
			$("#SelectFundTaPage #inputfundName").val('');
		});
		
		// 关闭
		$("#SelectFundTaPage #closeSelFundTaPage").on('click',function(){
			layerall_close();
		});
		
	},
	
	queryWorkDay:function(){
		$("#workDay").html(CommonUtil.getWorkDay());
	},
	
	/**
	 * 查询统计柜台不收市TA和列表
	 * @param uri
	 */
	queryFundCounterEndTA:function(){
		var uri = TmsCounterConfig.QUERY_FUND_COUNTER_END_TA_URL;
		var paramters = CommonUtil.buildReqParams(uri, null, null,
				null, null);
		CommonUtil.ajaxAndCallBack(paramters, FundCounterEnd.queryCounterTACallBack);
		
	},
	queryCounterTACallBack:function(data){
		var bodyData = data.body || {};
		if(bodyData.length <= 0){
			return;
		}
		// 选择TA
		if(bodyData.respDto != null){
			FundCounterEnd.taList = bodyData.respDto.notProcessTaList ||[];
			FundCounterEnd.notEndTaList = bodyData.respDto.notEndTaList ||[];
		}
		TaInputSelBox.buildTaInputSelBox("fundCounterEndTaSelBox", FundCounterEnd.taList);
		
		// 不收市TA列表
		$("#notEndTaList").empty();
		if(FundCounterEnd.notEndTaList.length <= 0){
			var trHtml = '<tr class="text-c"><td colspan="6">暂无不收市TA</td></tr>';
	    	 $("#notEndTaList").append(trHtml);
		} else{
			 $(FundCounterEnd.notEndTaList).each(function(index,element){
		    	 var trList = [];
		    	 trList.push('<td>'+formatData(element.fundCode, "--")+'</td>');
		    	 trList.push('<td>'+formatData(element.fundAttr, "--")+'</td>');
		    	 trList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP,element.fundType,'--')+'</td>');
		    	 trList.push('<td>'+formatData(element.taCode, "--")+'</td>');
		    	 trList.push('<td>'+formatData(element.taName, "--")+'</td>');
		    	 trList.push('<td><a class="btn btn-link radius delNotEndTa" dataTa="'+element.taCode+'">删除</a></td>');

		    	 var trHtml = '<tr class="text-c">'+trList.join()+'</tr>';
		    	 $("#notEndTaList").append(trHtml);
		     }); 
		}
		
		// 汇总信息
		$("#counterTaEndSum").empty();
		var appendHtml = '';
		appendHtml += '<tr class="text-c">';
		appendHtml += '<td><span>'+formatData(bodyData.respDto.totalTaNum, "--")+'</span></td>';
		appendHtml += '<td><span>'+formatData(bodyData.respDto.succEndTaNum, "--")+'</span></td>';
		appendHtml += '<td><span>'+formatData(bodyData.respDto.nonEndTaNum, "--")+'</span></td>';
		appendHtml += '</tr>';
		$("#counterTaEndSum").append(appendHtml);

		 // 删除
		 $(".delNotEndTa").off();
		 $(".delNotEndTa").on('click',function(){
			 var taCode = $(this).attr("dataTa");
			 FundCounterEnd.delNotEndTa(taCode);
		 });
	},
	
	/**
	 * 删除不收市TA(公募)
	 */
	delNotEndTa:function(taCode){
		if(isEmpty(taCode)){
			CommonUtil.layer_tip("请选择TA");
		}
		
		layer.confirm('确定删除不收市TA('+taCode+')吗？', {
            btn: ['确定', '取消']
        }, function (index) {
        	CommonUtil.disabledBtn("delNotEndTa");
        	// 提交
			var uri = TmsCounterConfig.COUNTER_SAVE_OR_DEL_END_TA_URL ||  {};
			var taFunds = [{"taCode":taCode}];
			var reqparamters = {"taFunds": JSON.stringify(taFunds), "actionType":CONSTANTS.OPERATION_TYPE_DELETE};
			
			//console.log(reqparamters);
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
    		CommonUtil.ajaxAndCallBack(paramters, function(data){
    			
    			var respCode = data.code || '';
    			var respDesc = data.desc || '';
    			
    			if(CommonUtil.isSucc(respCode)){
    				CommonUtil.layer_tip("删除成功");
    				FundCounterEnd.queryFundCounterEndTA();
    			}else{
    				CommonUtil.layer_alert("删除失败，"+respDesc);
    			}
    			
    		});
    		
        }, function(){  
 			layerall_close();
 			CommonUtil.enabledBtn("delNotEndTa");
 		});
	},
	
	/**
	 * 添加不收市TA(公募)
	 */
	addNotEndTa:function(){
		
		 var taCodeList = $("#fundCounterEndTaSelBox #selCheckedTA").val();
		 if(taCodeList == null || taCodeList.length<=0){
			 CommonUtil.layer_tip("请选择TA");
			 return;
		 }
		 
		 var taFunds = [];
		 $("#fundCounterEndTaSelBox input[type='checkbox']").each(function(index,element){
			 	var value = $(element).val();
			 	if(value != 'all' && $(element).is(":checked")){
				    var taInfo = {};
				    taInfo.taCode = value;
				    taInfo.taName = $(element).attr("taName");
				    taFunds.push(taInfo);
				}
			});

		 if(taFunds.length <= 0){
			 CommonUtil.layer_tip("请选择TA");
			 return;
		 }
		 
		
		 layer.confirm('确定添加不收市TA('+taCodeList+')吗？', {
	            btn: ['确定', '取消']
	        }, function (index) {
	        	CommonUtil.disabledBtn("addCounterNotEndTaBtn");
	        	// 提交
				var uri = TmsCounterConfig.COUNTER_SAVE_OR_DEL_END_TA_URL ||  {};
				var reqparamters = {"taFunds": JSON.stringify(taFunds), "actionType":CONSTANTS.OPERATION_TYPE_ADD};
				
				//console.log(reqparamters);
				var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
	    		CommonUtil.ajaxAndCallBack(paramters, function(data){
	    			CommonUtil.enabledBtn("addCounterNotEndTaBtn");
	    			
	    			var respCode = data.code || '';
	    			var respDesc = data.desc || '';
	    			
	    			if(CommonUtil.isSucc(respCode)){
	    				CommonUtil.layer_tip("添加成功");
	    				FundCounterEnd.queryFundCounterEndTA();
	    			}else{
	    				CommonUtil.layer_alert("添加失败，"+respDesc);
	    			}
	    			
	    		});
	    		
	        }, function(){  
	 			layerall_close();
	 			CommonUtil.enabledBtn("addCounterNotEndTaBtn");
	 		});
	},


	fundCounterEndPreCheck : function () {

        layer.confirm('确定公募柜台收市吗？', {
            btn: ['确定', '取消']
        }, function (index) {
            var uri = TmsCounterConfig.FUND_END_PRE_CHECK;
            var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
            CommonUtil.ajaxAndCallBack(paramters, function(data){
                var code = data.code;
                var desc = data.desc;
                if(CommonUtil.isSucc(code)){
                    var rsList = data.body.rsList || []
                    if(!CommonUtil.isEmpty(rsList) && rsList.length > 0){
                        var memoList = [];
                        $(rsList).each(function (index, element) {
							memoList.push(element.desc);
                        });

                        var content = memoList.join("<br>");
                        layer.confirm(content, {
                            btn: ['确定收市', '取消']
                        }, function (index) {
                            FundCounterEnd.fundCounterEnd();
                        }, function(){
                            layerall_close();
                        });
                    }else{
                        FundCounterEnd.fundCounterEnd();
                    }
                }else{
                    CommonUtil.layer_tip("收市失败:"+desc+"("+code+")");
                }
            });
        }, function(){
            layerall_close();
        });
    },
	
	/**
	 * 柜台收市(公募)
	 */
	fundCounterEnd:function(){

        var uri = TmsCounterConfig.QUERY_FUND_COUNTER_END_URL;
        var paramters = CommonUtil.buildReqParams(uri, null,null,null,null);
        CommonUtil.ajaxAndCallBack(paramters, function(data){
            var code = data.code;
            var desc = data.desc;

            FundCounterEnd.queryFundCounterEndTA();
            if(CommonUtil.isSucc(code)){
                CommonUtil.layer_tip("收市成功");
            }else{
                CommonUtil.layer_alert("收市失败:"+desc+"("+code+")");
            }
        });
	},

	/**
	 * 查询基金TA信息
	 */
	queryFundTaInfo:function(){
		var fundCode = $("#inputfundCode").val();
		var fundName = $("#inputfundName").val();
		if(CommonUtil.isEmpty(fundCode) && CommonUtil.isEmpty(fundName) ){
			CommonUtil.layer_tip("请输入基金代码或基金名称查询");
			return ;
		}
		
		var  uri= TmsCounterConfig.QUERY_FUND_TA_INFO_URL  ||  {};
		var reqparamters  = {};
		reqparamters.fundCode = fundCode;
		reqparamters.fundName = fundName;
		reqparamters.page = 1;
		reqparamters.pageSize = 10;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, FundCounterEnd.queryFundTaInfoCallBack,"pageView");
	},
	
	queryFundTaInfoCallBack:function(data){
		
		FundCounterEnd.fundTaList = data.fundTaList || [];
		$("#fundTaList").empty();
		if(FundCounterEnd.fundTaList.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="5">暂无数据</td></tr>';
			$("#fundTaList").append(trHtml);
		} else{
			$(FundCounterEnd.fundTaList).each(function(index,element){
				var tdList = [];
				tdList.push('<td><input class="selectNotEndFundTa" name="selectNotEndFundTa" type="checkbox" value="'+index+'"></input></td>');
				tdList.push('<td>'+element.fundCode+'</td>');
				tdList.push('<td>'+element.fundAttr+'</td>');
				tdList.push('<td>'+element.taCode+'</td>');
				tdList.push('<td>'+element.taName+'</td>');
				
				var trAppendHtml = '<tr class="text-c">'+tdList.join() +'</tr>';
				$("#fundTaList").append(trAppendHtml);
			});
		}
	},
	
	/**
	 * 添加不收市TA(公募)
	 */
	addSelFundTa:function(){
		
		var selectedCheckboxs = $("#SelectFundTaPage input[name='selectNotEndFundTa'][type='checkbox']:checked");
		if(CommonUtil.isEmpty(selectedCheckboxs) || selectedCheckboxs.length == 0){
			CommonUtil.layer_tip("请选择不收市基金TA");
			return ;
		}

		 var taFunds = [];	 
		 var taCodeList = "";
		 var existTaCodeList = "";
		 $("#SelectFundTaPage input[name='selectNotEndFundTa'][type='checkbox']").each(function(index,element){
				if($(element).is(":checked")){
					var selectedIndex = $(element).val();
					var selRowFundTa = FundCounterEnd.fundTaList[selectedIndex];
					 // 校验TA是否已添加
					 var checkFlag = false;
					 $(FundCounterEnd.notEndTaList).each(function(index,element){
						 if(element.taCode == selRowFundTa.taCode){
							 checkFlag = true;
							 existTaCodeList +=(selRowFundTa.taCode+",");
							 return;
						 }
					 });
					 
					 if(!checkFlag){
						 taCodeList +=(selRowFundTa.taCode+",");
						 taFunds.push(selRowFundTa);
					 }
				}
			});
		  taCodeList = taCodeList.substring(0, taCodeList.length-1);
		  existTaCodeList = existTaCodeList.substring(0, existTaCodeList.length-1);
		  if(!CommonUtil.isEmpty(existTaCodeList)){
			  CommonUtil.layer_alert("选择的TA("+existTaCodeList+")已经存在不收市TA列表中，不能重复添加，谢谢！");
				return ;
		  }
		  if(CommonUtil.isEmpty(taCodeList)){
				CommonUtil.layer_tip("请选择不收市基金TA");
				return ;
		  }
			
		 // 询问框
		 layer.confirm('确定添加不收市TA('+taCodeList+')吗？', {
	            btn: ['确定', '取消']
	        }, function (index) {
	        	
	        	CommonUtil.disabledBtn("addSelFundTaBtn");
	        	// 提交
				var uri = TmsCounterConfig.COUNTER_SAVE_OR_DEL_END_TA_URL ||  {};
				var reqparamters = {"taFunds": JSON.stringify(taFunds), "actionType":CONSTANTS.OPERATION_TYPE_ADD};
				
				//console.log(reqparamters);
				var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
	    		CommonUtil.ajaxAndCallBack(paramters, function(data){
	    			CommonUtil.enabledBtn("addSelFundTaBtn");
	    			
	    			var respCode = data.code || '';
	    			var respDesc = data.desc || '';
	    			
	    			if(CommonUtil.isSucc(respCode)){
	    				layerall_close();
	    				FundCounterEnd.queryFundCounterEndTA();
	    				CommonUtil.layer_tip("添加成功");
	    				
	    			}else{
	    				CommonUtil.enabledBtn("addSelFundTaBtn");
	    				CommonUtil.layer_alert("添加失败，"+respDesc);
	    			}
	    			
	    		});
	    		
	        }, function(){  
	 			layerall_close();
	 			CommonUtil.enabledBtn("addSelFundTaBtn");
	 		});
	},
	
	popupSelFundTaPage:function(){
		$("#SelectFundTaPage #inputfundCode").val('');
		$("#SelectFundTaPage #inputfundName").val('');
		$("#SelectFundTaPage #fundTaList").empty();
		var trHtml = '<tr class="text-c" ><td colspan="5">暂无数据</td></tr>';
		$("#SelectFundTaPage #fundTaList").append(trHtml);
		// POPUP
	    layer.open({
	        title: ['筛选基金添加不收市TA', false],
	        type: 1,
	        area: ['850px', '500px'],
	        skin: 'layui-layer-rim', //加上边框
	        btnAlign: 'l',
	        content: $('#SelectFundTaPage'),
	        cancel: function(){
	        	FundCounterEnd.queryFundCounterEndTA();
	        }
	    });	
	}
	
};
