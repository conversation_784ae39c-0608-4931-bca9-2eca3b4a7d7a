package com.howbuy.tms.counter.config.web;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        //图片、文件资源
        registry.addResourceHandler("/data/files/middle/exchangecard/**")
                .addResourceLocations("file:/data/files/middle/exchangecard/");
        //js、css、html资源
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
    }
}
