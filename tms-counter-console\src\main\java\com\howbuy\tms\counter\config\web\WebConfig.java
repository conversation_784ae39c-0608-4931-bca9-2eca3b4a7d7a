package com.howbuy.tms.counter.config.web;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {

        //js、css、html资源
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
    }
}
