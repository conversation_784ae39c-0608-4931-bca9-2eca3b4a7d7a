/**
 * 购买修改
 * <AUTHOR>
 * @date 2018-03-19 10:33
 */
$(function () {
    BuyModify.init();
});

var BuyModify = {

    /**
     * 初始化参数
     */
    initData: function () {
        BuyModify.custBanks = [];// 客户绑定银行卡
        BuyModify.appointment = {};// 投顾预约信息
        BuyModify.highproductAppointinfo = {};//预约开放日历信息
        BuyModify.checkOrder = {};// 订单信息
        BuyModify.custInfo = {};// 客户信息
        BuyModify.modifyDeal = {};// 修改订单
        BuyModify.fundInfo = {};

    },

    /**
     * 初始化下拉框
     */
    init: function () {

        /**
         * 初始化参数
         */
        BuyModify.initData();

        /**
         * 初始化按钮事件
         */
        BuyModify.initBtn();

        Modify.queryCounterDealOrder(this.queryCounterDealOrderCallBack, null);
        document.getElementById("subsAmt").addEventListener("change", BuyModify.calFee);
    },

    /**
     * 初始化按钮事件
     */
    initBtn: function () {

        /**
         * 双击客户号查询客户信息
         */
        $("#custNo").on('dblclick', function () {
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        $("#applyAmount").change(function () {
            $("#applyAmount").val(CommonUtil.formatAmount($("#applyAmount").val(), ''));
            // 申请金额
            var applyAmount = BuyModify.getAppAmt();

            // 计算手续费
            BuyModify.calFee();
        });
        var lastValue = '';
        //大小写转换
        $('.applyAmount').on('keyup focus', function () {

            var str = $(this).val() || '';
            if (!CommonUtil.validFloat(str)) {
                $(this).val(lastValue);
                return false;
            }
            ;

            lastValue = str;
            str = str.replace(/\,/g, '');
            var re = /([0-9]+\.[0-9]{2})[0-9]*/;
            str = str.replace(re, "$1");
            var convertStr = CommonUtil.digit_uppercase(str);
            $('#convertAmtId').html(convertStr);
        });
        // 认缴金额格式化
        $("#subsAmt").on("blur", function () {
            var subsAmt = $("#subsAmt").val();
            if (!CommonUtil.isEmpty(subsAmt)) {
                $("#subsAmt").val(CommonUtil.formatAmount(subsAmt));
                BuyModify.calculatePayRatioAmount(subsAmt);
                BuyModify.calFee();
            }
            CommonUtil.appAmtUpper(str);
        });


        //银行卡选择事件
        $('#selectCustBank').change(function () {
            var applyAmount = BuyModify.getAppAmt();

            if (CommonUtil.isEmpty(applyAmount)) {
                return false;
            }
            // 计算手续费
            BuyModify.calFee();
        });

        //修改折扣，计算手续费
        var lastDiscountRate = '';
        $("#discountRate").on('keyup blur', function () {

            var discountRate = $("#discountRate").val();

            if (lastDiscountRate === discountRate || BuyModify.checkOrder.discountRate === discountRate) {
                return;
            }

            var reg = new RegExp("^(([0]{1}\\.[0-9]{0,2})|([0-1]{1}))$");
            if (CommonUtil.isEmpty(discountRate) || reg.test(discountRate)) {
                lastDiscountRate = discountRate;
            } else {
                $("#discountRate").val(lastDiscountRate);
                return false;
            }
            BuyModify.calFee();
        });
    },

    /**
     * 计算缴款金额
     */
    calculatePayRatioAmount: function (value) {
        if ("HZ000N001" === BuyModify.custInfo.disCode && BuyModify.fundInfo.peDivideCallFlag !== '1') {
            $("#payRatioAmount").val(CommonUtil.formatAmount(value));
            $('#applyAmount').val(CommonUtil.formatAmount(value));
            return;
        }
        if (BuyModify.fundInfo.peDivideCallFlag !== '1') {
            return;
        }
        var payRatioAmount = CommonUtil.unFormatAmount(value);

        var payRatio = $("#payRatioId").val();
        if (payRatio) {
            payRatioAmount = math.multiply(math.bignumber(payRatioAmount), math.bignumber(payRatio)).toString();
        }
        $("#payRatioAmount").val(CommonUtil.formatAmount(payRatioAmount))
    },
    /**
     * 计算手续费
     */
    calFee: function () {

        var uri = TmsCounterConfig.CAL_FUND_BUY_FEE_URL || {};
        var fundCode = $("#fundCode").val();
        if (CommonUtil.isEmpty(fundCode)) {
            $("#applyAmount").val();
            return false;
        }

        //选中银行卡
        var bankCode = $('#selectBank').find('option:selected').attr('bankCode');
        if (CommonUtil.isEmpty(bankCode) && BuyModify.custBanks.length > 0) {
            bankCode = BuyModify.custBanks[0].bankCode;
        }

        var applyAmount = $("#applyAmount").val() || '';
        applyAmount = CommonUtil.unFormatAmount(applyAmount);
        if (CommonUtil.isEmpty(applyAmount)) {
            CommonUtil.layer_tip("请填写净申请金额");
            return false;
        }
        if (applyAmount === "--") {
            CommonUtil.layer_tip("请填写净申请金额");
            return false;
        }
        var custInfoForm = JSON.stringify(BuyModify.custInfo);
        if (CommonUtil.isEmpty(custInfoForm)) {
            showMsg("请先选择客户信息");
            return false;
        }

        var subsAmt = $("#subsAmt").val() || '';
        subsAmt = CommonUtil.unFormatAmount(subsAmt);
        if (subsAmt === "--") {
            subsAmt = '';
        }
        var discountRate = $("#discountRate").val();
        var reqparamters = {};
        reqparamters.custInfoForm = custInfoForm;
        reqparamters.fundCode = fundCode;
        reqparamters.appointmentDealNo = fundCode;
        reqparamters.bankCode = bankCode;
        reqparamters.disCode = BuyModify.custInfo.disCode;
        reqparamters.appointmentDealNo = BuyModify.appointment.appointId
        reqparamters.applyAmount = applyAmount;
        reqparamters.subsAmt = subsAmt;
        reqparamters.discountRate = discountRate || '';
        reqparamters.queryDateStr = BuyModify.checkOrder.appDt + '' + BuyModify.checkOrder.appTm;
        var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
        CommonUtil.ajaxAndCallBack(paramters, BuyModify.calFundBuyFeeCallBack);
    },

    calFundBuyFeeCallBack: function (data) {
        var bodyData = data.body || {};
        var respCode = data.code || '';
        var respDesc = data.desc || '';
        if (CommonUtil.isSucc(respCode)) {
            var respData = bodyData.respData || [];
            $("#applyAmountIncluFeeId").html(CommonUtil.formatAmount(respData.payAmt));
            $("#applyAmountIncluFee").val(respData.payAmt);
            $("#originalFeeRate").val(respData.feeRate);
            $("#discountRate").val(respData.discountRate);
            $("#feeRate").val(respData.feeRate);
            $("#feeId").html(CommonUtil.formatAmount(respData.fundBuyFee));
            BuyModify.fundInfo.fundBuyFee = respData.fundBuyFee;
        } else {
            CommonUtil.layer_tip(respDesc);
        }
    },

    /**
     * 查询产品预约开放日历信息
     */
    queryHighproductAppointinfo: function (fundCode, appDt, appTm, busyType) {

        QueryHighProduct.queryHighproductAppointinfo(fundCode, appDt, appTm, busyType);
        BuyModify.highproductAppointinfo = QueryHighProduct.highproductAppointinfo || {};
        // 构建预约开放日历信息
        ViewCounterDeal.buildProductAppointmentInfo(BuyModify.highproductAppointinfo);
    },
    /**
     * 查询高端产品基本信息
     * @param fundCode 产品代码
     */
    queryFundInfo: function (fundCode) {

        QueryHighProduct.queryFundInfo(fundCode, null, null, '0');
        BuyModify.fundInfo = QueryHighProduct.fundInfo || {};

        if (CommonUtil.isEmpty(BuyModify.fundInfo.fundCode)) {
            CommonUtil.layer_tip("没有查询到此产品");
            return false;
        }
        //构建产品基本信息
        ViewCounterDeal.buildFundInfo(BuyModify.fundInfo);
    },

    /**
     * 查询客户银行卡信息
     */
    queryCustBankInfo: function (custNo, disCode) {
        var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
        var reqparamters = {"custNo": custNo, "disCode": disCode};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                BuyModify.custBanks = body.custBanks || [];
                var selectBankHtml = '';
                $(BuyModify.custBanks).each(function (index, element) {
                    selectBankHtml += '<option bankcode="' + element.bankCode + '" bankacct= "' + element.bankAcct + '" value="' + element.cpAcctNo + '">' + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                });
                $("#selectBank").empty();
                $(".selectBank").attr("style", "width:300px");
                $("#selectBank").attr("style", "width:280px");
                $("#selectBank").html(selectBankHtml);
            }
        });
    },


    queryCounterDealOrderCallBack: function (data) {

        var bodyData = data.body || {};
        BuyModify.counterOrderDto = bodyData.counterOrderDto || {};//订单信息
        var checkOrder = BuyModify.counterOrderDto || {};//订单信息

        var appointList = bodyData.appointList || [];//投顾预约信息
        var custInfofiList = bodyData.custInfofiList || [];//客户信息
        BuyModify.checkOrder = checkOrder;//柜台订单信息


        ViewCounterDeal.buildAppointmentInfo(appointList);//预约信息
        if (custInfofiList.length > 0) {
            BuyModify.custInfo = custInfofiList[0] || {};//客户信息
        }

        if (appointList.length > 0) {
            BuyModify.appointment = appointList[0] || {};// 投顾预约信息
            /**认缴金额 有预约时已预约为准，不允许修改*/
            if ("HZ000N001" !== BuyModify.custInfo.disCode) {
                $('#subsAmt').attr("readonly", "readonly");
            }
        }
        var fundCode = checkOrder.fundCode;// 产品代码
        var appDt = checkOrder.appDt;// 申请日期
        var appTm = checkOrder.appTm;// 申请时间
        var busiType = '0';// 业务类型 0-购买 1-赎回
        BuyModify.queryFundInfo(fundCode);// 查询产品信息
        BuyModify.queryHighproductAppointinfo(fundCode, appDt, appTm, busiType);// 查询预约开放日历信息
        BuyModify.buildBuyDealInfo(checkOrder);//购买订单信息
        ViewCounterDeal.buildCustInfo(custInfofiList);//客户信息
        ViewCounterDeal.buildOtherInfo(checkOrder);//其他信息
        ViewCounterDeal.buildTransactor(checkOrder);//经办人信息
        ViewCounterDeal.buildCheckInfo(checkOrder);//审核信息

        var viewType = CommonUtil.getParam("viewType");
        if ('0' !== viewType) {
            OnLineOrderFile.query(null, Modify.getSelectCustMaterial(bodyData, OnLineOrderFile.CRM_BUY), Modify.getCheckNode());// CRM线上资料
        } else {
            var orderFile = bodyData.orderFile || {};
            OnLineOrderFile.buildOrderFileHtml(orderFile);
        }
        //查询银行卡信息
        BuyModify.queryCustBankInfo(checkOrder.txAcctNo, checkOrder.bankAcct, checkOrder.disCode);
    },

    /**
     * 查询客户银行卡信息
     * @param custNo  客户号
     * @param bankAcct 默认选中银行卡
     * @param disCode  分销机构号
     */
    queryCustBankInfo: function (custNo, bankAcct, disCode) {
        var uri = TmsCounterConfig.QUERY_CUST_BANKINFO_URL;
        var reqparamters = {"custNo": custNo, "disCode": disCode};

        var paramters = CommonUtil.buildReqParams(uri, reqparamters);
        CommonUtil.ajaxAndCallBack(paramters, function (data) {
            var respCode = data.code || '';
            var body = data.body || {};

            if (CommonUtil.isSucc(respCode)) {
                var custBanks = body.custBanks || [];
                var selectBankHtml = '<option value="">请选择</option>';
                $(custBanks).each(function (index, element) {
                    selectBankHtml += '<option bankcode = "' + element.bankCode + '" bankacct= "' + element.bankAcct + '" name= "cpAcctNo"' + ' value="' + element.cpAcctNo + '">'
                        + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                });
                $("#selectBank").empty();
                $(".selectBank").attr("style", "width:300px");
                $("#selectBank").attr("style", "width:280px");
                $("#selectBank").html(selectBankHtml);

                //下单所属银行卡
                var selectCustBankHtml = '';
                $(custBanks).each(function (index, element) {
                    if (element.bankAcct == bankAcct) {
                        selectCustBankHtml += '<option bankcode = "' + element.bankCode + '" bankacct= "' + element.bankAcct + '" value="' + element.cpAcctNo + '" selected>'
                            + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                    } else {
                        selectCustBankHtml += '<option bankcode = "' + element.bankCode + '" bankacct= "' + element.bankAcct + '" value="' + element.cpAcctNo + '">'
                            + CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode) + '' + element.bankAcct + ' </option>';
                    }

                });
                $("#selectCustBank").empty();
                $("#selectCustBank").attr("style", "width:300px");
                $("#selectCustBank").attr("style", "width:280px");
                $("#selectCustBank").html(selectCustBankHtml);
                $("#selectCustBank").attr("style", "width:280px");

            }
        });
    },

    /**
     * 购买订单信息
     * @param checkOrder
     */
    buildBuyDealInfo: function (checkOrder) {
        $("#fundCode").val(checkOrder.fundCode);// 基金代码
        var appAmt = checkOrder.appAmt - checkOrder.esitmateFee; // 净申请金额
        $("#applyAmount").val(CommonUtil.formatAmount(appAmt));// 申请金额
        var convertStr = CommonUtil.digit_uppercase(appAmt);// 申请金额转大写
        $("#convertAmtId").html(convertStr);// 大写金额
        $("#discountRate").val(checkOrder.discountRate);// 折扣
        $("#originalFeeRate").val(checkOrder.feeRate);// 费率
        $("#feeId").html(CommonUtil.formatAmount(checkOrder.esitmateFee));// 手续费
        $("input[name='fee']").val(checkOrder.esitmateFee);// 手续费
        $("#applyAmountIncluFee").html(CommonUtil.formatAmount(checkOrder.appAmt));// 总金额
        $("input[name='applyAmountIncluFee']").val(checkOrder.appAmt);// 含费总金额
        $("input[name='feeRate']").val(checkOrder.feeRate);// 费率
        $("#appDt").val(checkOrder.appDt);// 申请日期
        $("#appTm").val(checkOrder.appTm);// 申请时间
        $("#isRedeemExpire").val(checkOrder.isRedeemExpire);//是否到期赎回
        $("#preExpireDate").val(checkOrder.preExpireDate);//预计到期日期
        $("#subsAmt").val(checkOrder.subsAmt);
        BuyModify.initInputStatus();//初始化可修改输入框
        Modify.modifyDealOrder = checkOrder;// 原订单信息
        BuyModify.calculatePayRatioAmount(checkOrder.subsAmt)
    },


    /**
     * 计算缴款金额
     */
    calculatePayRatioAmount: function (value) {
        if ("HZ000N001" === BuyModify.custInfo.disCode && BuyModify.fundInfo.peDivideCallFlag !== '1') {
            $("#payRatioAmount").val(CommonUtil.formatAmount(value));
            $('#applyAmount').val(CommonUtil.formatAmount(value));
            return;
        }
        if (BuyModify.fundInfo.peDivideCallFlag !== '1') {
            return;
        }
        var payRatioAmount = CommonUtil.unFormatAmount(value);

        var payRatio = $("#payRatioId").val();
        if (payRatio) {
            payRatioAmount = math.multiply(math.bignumber(payRatioAmount), math.bignumber(payRatio)).toString();
        }
        $("#payRatioAmount").val(CommonUtil.formatAmount(payRatioAmount))
    },

    /**
     * 初始化输入框状态
     * @param checkOrder
     */
    initInputStatus: function (checkOrder) {
        var viewType = CommonUtil.getParam("viewType");
        if ('2' === viewType) {
            // 可修改输入框
            var enableList = [];
            enableList.push($("#applyAmount"));// 申请金额
            enableList.push($("#discountRate"));// 折扣
            enableList.push($("#originalFeeRate"));// 费率
            enableList.push($("input[name='fee']"));// 手续费
            enableList.push($("#applyAmountIncluFee"));// 总金额
            enableList.push($("input[name='applyAmountIncluFee']"));// 含费总金额
            enableList.push($("input[name='feeRate']"));// 费率
            enableList.push($("#appTm"));// 申请时间
            enableList.push($("#selectCustBank"));// 客户银行卡
            enableList.push($("#isRedeemExpire"));// 申请时间
            enableList.push($("#preExpireDate"));// 客户银行卡
            CommonUtil.enabledList(enableList);
            // 标准费率只读不可修改
            $("#originalFeeRate").attr("readonly", "readonly");
            if (!CommonUtil.isEmpty(BuyModify.appointment.discountRate)) {
                // 有预约折扣，折扣不可修改
                $("#discountRate").attr("readonly", "readonly");
            } else {
                // 没有预约折扣，折扣可以修改
                $("#discountRate").removeAttr("readonly");
            }

        }

    },
    /**
     * 获取反格式化申请金额
     * @returns
     */
    getAppAmt: function () {
        var appAmt = $("#applyAmount").val();
        appAmt = CommonUtil.unFormatAmount(appAmt);
        return appAmt;
    }

};

