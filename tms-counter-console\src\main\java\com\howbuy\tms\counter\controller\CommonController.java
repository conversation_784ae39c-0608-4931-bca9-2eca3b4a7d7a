/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.date.DateUtil;
import com.howbuy.common.page.Page;
import com.howbuy.interlayer.product.model.HighActiDiscountRatioModel;
import com.howbuy.interlayer.product.model.HighProductControlModel;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService;
import com.howbuy.interlayer.product.service.high.request.QueryNotHBJGFundListRequest;
import com.howbuy.interlayer.product.service.high.response.QueryNotHBJGFundListResponse;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoResponse;
import com.howbuy.web.util.IpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(公共控制器)
 * @date 2017年3月28日 下午5:28:03
 * @since JDK 1.7
 */
@Controller
public class CommonController {
    private final static Logger logger = LogManager.getLogger(CommonController.class);
    @Autowired
    private TmsCounterService tmsCounterService;
    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;


    @Autowired
    @Qualifier("tmscounter.highProductService")
    private HighProductService highProductService;

    @Autowired
    @Qualifier("tmscounter.queryNotHBJGFundListService")
    private QueryNotHBJGFundListService queryNotHBJGFundListService;

    /***
     *
     * calDiscountRate:(计算折扣率)
     *
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月18日 下午8:41:52
     */
    @RequestMapping("tmscounter/caldiscountrate.htm")
    public void calDiscountRate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("CommonController|calDiscountRate|nowTime:{},ipAddr:{}", DateUtil.getSystemDate("YYYYMMDDHHMMSS"), IpUtil.getIpAddr(request));
        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);

        String fundCode = request.getParameter("fundCode");
        // 预约折扣
        String discountRate = request.getParameter("discountRate");
        String bankCode = request.getParameter("bankCode");
        // 净申请金额
        String applyAmount = request.getParameter("applyAmount");
        // 预约金额
        String appAmt = request.getParameter("appAmt");

        FeeReqDto feeReqDto = new FeeReqDto();
        if (!StringUtils.isEmpty(applyAmount)) {
            feeReqDto.setNetBuyAmt(new BigDecimal(applyAmount));
        }
        if (!StringUtils.isEmpty(appAmt)) {
            feeReqDto.setAppointmentAmt(new BigDecimal(appAmt));
        }
        if (!StringUtils.isEmpty(discountRate)) {
            feeReqDto.setDiscountRate(new BigDecimal(discountRate));
        }

        String workDay = tmsCounterService.getHighSystemWorkDay();
        logger.debug("tmsCounterService|getCounterWorkDay|workDay:{}", workDay);
        String mBusiCode = tmsCounterOutService.getMBusiCode(custInfoDto, fundCode, workDay);

        if (StringUtils.isEmpty(mBusiCode)) {
            throw new TmsCounterException("", "参数错误，业务类型为空");
        }
        logger.debug("tmsCounterOutService|getMBusiCode|mBusiCode:{}", mBusiCode);

        logger.debug("tmsCounterService|calDiscountRate|req|feeReqDto:{},custInfoDto:{},bankCode:{},mBusiCode:{}", JSON.toJSONString(feeReqDto), custInfoDto,
                bankCode, mBusiCode);
        FeeDto feeDto = tmsCounterService.calDiscountRate(feeReqDto, custInfoDto, fundCode, bankCode, mBusiCode);
        logger.debug("tmsCounterService|calDiscountRate|resp|feeDto:{}", JSON.toJSONString(feeDto));

        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respData", feeDto);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }


    /**
     * @api {GET} /tmscounter/queryHzBuyInfo.htm queryHzBuyInfo
     * @apiVersion 1.0.0
     * @apiGroup CommonController
     * @apiName queryHzBuyInfo
     * @apiDescription 查询好臻信息
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping("tmscounter/queryHzBuyInfo.htm")
    public void queryHzBuyInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String fundCode = request.getParameter("fundCode");
        String txAcctNo = request.getParameter("txAcctNo");
        String queryDateStr = request.getParameter("queryDateStr");
        if (StringUtils.isBlank(fundCode) || StringUtils.isBlank(txAcctNo)) {
            logger.info("queryHzBuyInfo-查询好臻订单信息,没有产品编码或者交易账号,fundCode={},txAcctNo={}", fundCode, txAcctNo);
            Map<String, Object> bodyResult = new HashMap<String, Object>(1);
            TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
            tmsCounterResult.setBody(bodyResult);
            WebUtil.write(response, tmsCounterResult);
            return;
        }
        QueryHzBuyOrderInfoResponse queryHzBuyOrderInfoResponse = tmsCounterOutService.queryHzBuyInfo(fundCode.toUpperCase(Locale.ROOT), txAcctNo, com.howbuy.web.util.WebUtil.getCustIP(request),queryDateStr);
        Map<String, Object> bodyResult = new HashMap<String, Object>(1);
        bodyResult.put("data", queryHzBuyOrderInfoResponse);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    /**
     * queryCurrDate:(查询当前日期)
     *
     * @param request
     * @param response
     * @throws IOException
     * <AUTHOR>
     * @date 2017年4月21日 下午11:41:03
     */
    @RequestMapping("tmscounter/querycurrdate.htm")
    public void queryCurrDate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String currDate = DateUtil.formateDate(new Date(), DateUtil.STANDARDDATE);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("currDate", currDate);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    /***
     *
     * queryAppointmentInfo:(查询客户预约信息)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月3日 下午5:16:07
     */
    @RequestMapping("/tmscounter/queryappointmentInfo.htm")
    public void queryAppointmentInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("CommonController|queryAppointmentInfo|nowTime:{},ipAddr:{}", DateUtil.getSystemDate("YYYYMMDDHHMMSS"), IpUtil.getIpAddr(request));
        String custNo = request.getParameter("custNo");
        String tradeType = request.getParameter("tradeType");
        String disCode = request.getParameter("disCode");
        String[] tradeTypes = {};
        if (!StringUtils.isEmpty(tradeType)) {
            tradeTypes = tradeType.split(",");
        }
        List<String> statusList = new ArrayList<String>();
        statusList.add(PreBookStateEnum.CONFIRM.getCode());
        Map<String, Object> parameter = new HashMap<String, Object>(16);
        parameter.put("custNo", custNo);
        // 1是柜台异常流程、4柜台
        parameter.put("preType", Collections.singletonList("1"));
        // 1是购买 2是追加 3是赎回
        parameter.put("tradeType", java.util.Arrays.asList(tradeTypes));
        parameter.put("statusList", statusList);

        Page cpage = new Page();
        String pageSizeStr = StringUtils.isEmpty(request.getParameter("pageSize")) ? "10" : request.getParameter("pageSize");
        String pageStr = StringUtils.isEmpty(request.getParameter("page")) ? "10" : request.getParameter("page");
        cpage.setPerPage(Integer.parseInt(pageSizeStr));
        cpage.setPage(Integer.parseInt(pageStr));

        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        List<String> filterFundCodeList = new ArrayList<>();
        if (user.getInHBJG()) {
            QueryNotHBJGFundListResponse queryNotHBJGFundListResponse = queryNotHBJGFundListService.query(new QueryNotHBJGFundListRequest());
            filterFundCodeList = queryNotHBJGFundListResponse.getFundCodes();
        }

        logger.info("CommonController|queryAppointmentInfo|tmsCounterOutService|queryAppointmentInfo|parameter:{},", JSON.toJSONString(parameter));
        CustomerAppointmentInfoRespDto customerAppointmentInfoRespDto = tmsCounterOutService.queryAppointmentInfo(parameter, cpage);

        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        if (customerAppointmentInfoRespDto != null && !CollectionUtils.isEmpty(customerAppointmentInfoRespDto.getCustomerAppointmentInfoDtoList())) {
            if (disCode != null) {
                // 根据分销代码进行筛选
                List<CustomerAppointmentInfoDto> customerAppointmentInfoDtoList = new ArrayList<>();
                for (CustomerAppointmentInfoDto dto : customerAppointmentInfoRespDto.getCustomerAppointmentInfoDtoList()) {
                    HighProductControlModel highProductControlModel = highProductService.getHighProductControlInfo(dto.getProductCode());
                    if (highProductControlModel != null) {
                        if (filterFundCodeList.contains(dto.getProductCode())) {
                            continue;
                        }
                        if (highProductControlModel.getDisCode() != null && highProductControlModel.getDisCode().contains(disCode)) {
                            customerAppointmentInfoDtoList.add(dto);
                        } else if (DisCodeEnum.HM.getCode().equals(disCode) && DisCodeEnum.ALL.getCode().equals(highProductControlModel.getDisCode())) {
                            customerAppointmentInfoDtoList.add(dto);
                        } else if (DisCodeEnum.OTC_MIDDLE.getCode().equals(disCode) &&
                                (DisCodeEnum.ALL.getCode().equals(highProductControlModel.getDisCode()) || DisCodeEnum.HM.getCode().equals(highProductControlModel.getDisCode()))) {
                            customerAppointmentInfoDtoList.add(dto);
                        }
                    }
                }

                bodyResult.put("pageNum", customerAppointmentInfoRespDto.getPageNo());
                bodyResult.put("totalPage", customerAppointmentInfoRespDto.getTotalPage());
                bodyResult.put("appointmentInfo", customerAppointmentInfoDtoList);
            } else {
                List<CustomerAppointmentInfoDto> customerAppointmentInfoDtoList = new ArrayList<>();
                for (CustomerAppointmentInfoDto dto : customerAppointmentInfoRespDto.getCustomerAppointmentInfoDtoList()) {
                    if (filterFundCodeList.contains(dto.getProductCode())) {
                        continue;
                    }
                    customerAppointmentInfoDtoList.add(dto);
                }
                bodyResult.put("pageNum", customerAppointmentInfoRespDto.getPageNo());
                bodyResult.put("totalPage", customerAppointmentInfoRespDto.getTotalPage());
                bodyResult.put("appointmentInfo", customerAppointmentInfoDtoList);
            }
        }

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    /***
     *
     * queryPayAmt:(查询购买金额（含费）和原始手续费)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月18日 上午9:59:31
     */
    @RequestMapping("tmscounter/calfundbuyfee.htm")
    public ModelAndView calPayAmt(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info("CommonController|calPayAmt|nowTime:{},ipAddr:{}", DateUtil.getSystemDate("YYYYMMDDHHMMSS"), IpUtil.getIpAddr(request));
        String netBuyAmt = request.getParameter("applyAmount");
        String discountRate = request.getParameter("discountRate");
        String fundCode = request.getParameter("fundCode");
        String disCode = request.getParameter("disCode");
        String bankCode = request.getParameter("bankCode");
        String appointmentDealNo = request.getParameter("appointmentDealNo");
        String queryDateStr = request.getParameter("queryDateStr");
        String subsAmt = request.getParameter("subsAmt");
        String custInfoForm = request.getParameter("custInfoForm");
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        logger.info("CommonController|calPayAmt|netBuyAmt:{},discountRate:{},fundCode:{},custInfoForm:{}", netBuyAmt, discountRate, fundCode,
                JSON.toJSONString(custInfoDto));
        if (custInfoDto == null) {
            throw new TmsCounterException("", "参数错误，客户信息为空。");
        }

        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(fundCode);
        if (highProductBaseBean == null) {
            logger.info("highProductBaseBean is null.");
            throw new TmsCounterException("", "参数错误，产品信息为空。");
        }
        String workDay =  tmsCounterService.getHighSystemWorkDay();
        logger.info("CommonController|calPayAmt|tmsCounterOutService|getCounterWorkDay|productChannel:{}, workDay:{}", highProductBaseBean.getProductChannel(), workDay);

        String mBusiCode = tmsCounterOutService.getMBusiCode(custInfoDto, fundCode, workDay);
        logger.info("CommonController|calPayAmt|tmsCounterOutService|getMBusiCode|mBusiCode:{}", mBusiCode);
        if (StringUtils.isEmpty(mBusiCode)) {
            throw new TmsCounterException("", "参数错误，业务类型为空");
        }
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(mBusiCode);
        String businessCode = null;
        if (null != businessCodeEnum) {
            businessCode = businessCodeEnum.getCode();
        }

        // 查询活动折扣
        HighActiDiscountRatioModel actiDiscountRatioModel = tmsCounterService.getHighActiDiscountRatio(fundCode, highProductBaseBean.getShareClass(),
                PaySourceEnum.SELF_DRAWING.getCode(), custInfoDto.getInvstType(), businessCode, bankCode, custInfoDto.getDisCode());
        // 配置活动折扣校验
        if (actiDiscountRatioModel == null
                || !FundActiFlagEnum.JOIN.getCode().equals(actiDiscountRatioModel.getActiFlag())
                || actiDiscountRatioModel.getActiDiscountRatio() == null) {
            logger.info("calDiscountRate|actiDiscountRatioBean:{}", JSON.toJSONString(actiDiscountRatioModel));
            throw new TmsCounterException(TmsCounterResultEnum.COUNTER_ACTIDISCOUNT_ERROR);
        }

        // 页面输入代销折扣和活动折扣比较
        if (!StringUtils.isEmpty(discountRate)) {
            BigDecimal inputRate = new BigDecimal(discountRate);
            if (inputRate.compareTo(actiDiscountRatioModel.getActiDiscountRatio()) < 0) {
                TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.COUNTER_AGENT_DISCOUNT_ERROR);
                Map<String, Object> bodyResult = new HashMap<String, Object>(16);
                tmsCounterResult.setBody(bodyResult);
                tmsCounterResult.setDesc("活动折扣为" + actiDiscountRatioModel.getActiDiscountRatio() + "折，申请折扣率不能低于活动折扣");
                WebUtil.write(response, tmsCounterResult);
                return null;
            }
        }
        FeeReqDto feeReqDto = new FeeReqDto();
        feeReqDto.setBusinessCode(mBusiCode);
        feeReqDto.setInvstType(custInfoDto.getInvstType());
        feeReqDto.setDiscountRate(new BigDecimal(StringUtils.isEmpty(discountRate) ? "1" : discountRate));
        feeReqDto.setFeeCalMode(highProductBaseBean.getFeeCalMode());
        feeReqDto.setDisCode(disCode);
        if (StringUtils.isNotEmpty(subsAmt)) {
            feeReqDto.setSubsAmt(new BigDecimal(subsAmt));
        }
        feeReqDto.setOperIp(com.howbuy.web.util.WebUtil.getCustIP(request));
        feeReqDto.setAppointmentDealNo(appointmentDealNo);
        feeReqDto.setBankCode(bankCode);
        feeReqDto.setTxAcctNo(custInfoDto.getCustNo());
        feeReqDto.setNetBuyAmt(new BigDecimal(netBuyAmt));
        feeReqDto.setShareClass(highProductBaseBean.getShareClass());
        if(StringUtils.isNotBlank(queryDateStr)){
            feeReqDto.setQueryDate(DateUtil.formatToDate(queryDateStr,DateUtil.YYYYMMDDHHMMSS));
        }
        feeReqDto.setFundCode(fundCode.toUpperCase(Locale.ROOT));
        logger.info("CommonController|calPayAmt|tmsCounterOutService|calFundBuyFee|feeReqDto:{}", JSON.toJSONString(feeReqDto));
        FeeDto feeDto = tmsCounterOutService.calFundBuyFee(feeReqDto);

        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("respData", feeDto);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
        return null;
    }

    /***
     *
     * queryCurrDate:(查询当前工作日)
     *
     * @param request
     * @param response
     * <AUTHOR>
     * @throws Exception
     * @date 2017年9月15日 上午9:45:11
     */
    @RequestMapping("tmscounter/fund/querycurrworkday.htm")
    public void queryCurrWorkDay(HttpServletRequest request, HttpServletResponse response) throws Exception {
        DisInfoDto disInfoDto = new DisInfoDto();
        String currDate = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("currDate", currDate);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    @RequestMapping("tmscounter/high/queryhighcurrworkday.htm")
    public void queryHighCurrWorkDay(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String currDate = tmsCounterService.getHighSystemWorkDay();
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("workDay", currDate);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

    /**
     * getWorkDay:(获取当前工作日)
     *
     * @param productChannel 3-群济私募 5-好买公募 6-好买高端公募
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年3月17日 下午1:44:35
     */
    private String getWorkDay(String productChannel) throws Exception {

        if (StringUtils.isEmpty(productChannel)) {
            logger.info("QueryHighWorkDay|getWorkDay|productChannel:{}", productChannel);
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_IS_NULL);
        }

        String sysCode = TmsCounterConstant.PRODUCT_CHANNEL_AND_SYS_CODE_MAP.get(productChannel);
        logger.info("QueryHighWorkDay|getWorkDay|productChannel:{}, sysCode:{}", productChannel, sysCode);

        if (StringUtils.isEmpty(sysCode)) {
            logger.info("QueryHighWorkDay|getWorkDay|not find sysCode of  productChannel:{} ", productChannel);
            throw new TmsCounterException(TmsCounterResultEnum.PROUDCT_CHANNEL_WITH_NULL_SYSCODE);
        }

        String workDay = tmsCounterService.getSystemWorkDay(sysCode, null);

        return workDay;
    }

}
