<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" 
    "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>
	<bean id="hsbClient" class="com.howbuy.hsb.hsbclient.HSBClient" />
	<bean id="hsbServer" class="com.howbuy.hsb.hsbserver.HSBServer"/>
	
	<bean id="fdNetConfig" class="com.howbuy.hsb.txio.NetConfig">
		<property name="recognizers"><value>20</value></property>
		<property name="communMode"><value>3</value></property>
		<property name="timeout"><value>0</value></property>
		<property name="ctxPath"><value>context/fds/fds-service.xml</value></property>
		<property name="local"><value>false</value></property>
		<property name="ip"><value>***************</value></property>
		<property name="port"><value>13242</value></property>
		<property name="communPort"><value>13244</value></property>
	</bean>
	
</beans>