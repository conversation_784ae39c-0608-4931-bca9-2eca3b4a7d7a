package com.howbuy.tms.counter.common.util;



import java.io.IOException;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import javax.servlet.ServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.alibaba.fastjson.JSON;
import org.springframework.http.MediaType;

/**
 * 
 * @description:(web 工具类)
 * <AUTHOR>
 * @date 2017年3月27日 下午3:56:59
 * @since JDK 1.7
 */
public class WebUtil {
    private static final Logger logger = LogManager.getLogger(WebUtil.class);

    /**
     * 写出响应内容
     * @param writer
     * @param bs 业务结果对象
     * @throws IOException
     */
    public static void write(Writer writer, Object bs) throws IOException {
        if(writer == null || bs == null){
            return;
        }
        String content = JSON.toJSONString(bs);
        String jsonStr = String.format("%s(%s)", "callback",content);
        writer.write(jsonStr);
        writer.close();
    }

    /**
     * 写出响应内容
     * @param response
     * @param bs 业务结果对象
     * @throws IOException
     */
    public static void write(ServletResponse response, Object bs) throws IOException {
        if(response == null || bs == null ){
            return;
        }
        String jsonStr = JSON.toJSONString(bs);
        
        String jsonpCallback = RequestUtil.getHttpParameter("callbackfun");
        if (!StringUtils.isEmpty(jsonpCallback)) {
            jsonStr = String.format("%s(%s)", jsonpCallback, jsonStr);
        }
        write(response, jsonStr);
    }

    /**
     * 写出响应内容
     * @param response
     * @param content 业务结果数据
     * @throws IOException
     */
    public static void write(ServletResponse response, String content) throws IOException {
        if(StringUtils.isEmpty(content)){
            return;
        }
        logger.info("response:{}",content);
        try{
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.getWriter().write(content);
            response.getWriter().close();
        }catch(Exception e){
            logger.error("error:",e);
        }
        
    }      

}
