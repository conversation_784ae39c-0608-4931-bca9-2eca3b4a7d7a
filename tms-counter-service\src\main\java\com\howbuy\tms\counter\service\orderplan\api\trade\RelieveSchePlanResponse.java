/**
 *Copyright (c) 2020, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.service.orderplan.api.trade;

import com.howbuy.tms.counter.service.orderplan.api.BaseResponse;

/**
 * 
 * @description:解除定投合约（销户||换卡）
 * <AUTHOR>
 * @date 2020年5月18日 下午2:09:35
 * @since JDK 1.6
 */
public class RelieveSchePlanResponse extends BaseResponse {

    private static final long serialVersionUID = -1989898703002495560L;
}
