$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplyModifydiv.checkOrder = {};	 
	ApplyModifydiv.init(checkId,custNo,disCode,idNo);
});

var ApplyModifydiv = {	
		init:function(checkId, custNo, disCode,idNo){
			QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
			QueryCheckOrder.queryCheckOrderById(checkId,ApplyModifydiv.queryCheckOrderByIdBack);
			
			$("#abolishBtn").on('click',function(){
				CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyModifydiv.checkOrder);
			});
			
		},
		
		
		queryCheckOrderByIdBack:function(data){
			var bodyData = data.body || {};
			ApplyModifydiv.checkOrder = bodyData.checkOrder || {};
					
			if(CommonUtil.isEmpty(ApplyModifydiv.checkOrder.dealAppNo)){
				CommonUtil.layer_tip("无此订单");
				return false;
			}
			
			if(ApplyModifydiv.checkOrder.checkFlag != 3){
				CommonUtil.layer_tip("该订单不处于驳回状态");
				return false;
			}
			
			QueryFundInfo.queryFundInfo(ApplyModifydiv.checkOrder.fundCode);
			
			QueryFundDivInfo.queryFundDivInfo(QueryCustInfo.custInfo.disCode,QueryCustInfo.custInfo.custNo,ApplyModifydiv.checkOrder.fundCode);
			
			if($(".selectAgened").length > 0){
				$(".selectAgened").val(ApplyModifydiv.checkOrder.agentFlag);
			}
			
			if($("#fundCode").length > 0){
				$("#fundCode").val(ApplyModifydiv.checkOrder.fundCode);
			}
			
			/**other*/
			if($("#appDt").length > 0){
				$("#appDt").val(ApplyModifydiv.checkOrder.appDt);
			}
			
			if($("#appTm").length > 0){
				$("#appTm").val(ApplyModifydiv.checkOrder.appTm);
			}
			
			if($("#consCode").length > 0){
				$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, ApplyModifydiv.checkOrder.consCode, ''));
			}
			
			if($("#transactorName").length > 0){
				$("#transactorName").val(ApplyModifydiv.checkOrder.transactorName);
			}
			
			if($("#transactorIdNo").length > 0){
				$("#transactorIdNo").val(ApplyModifydiv.checkOrder.transactorIdNo);
			}
			
			if($("#transactorIdType").length > 0){
				$("#transactorIdType").val(ApplyModifydiv.checkOrder.transactorIdType);
			}
			
			if($("#checkFaildDesc").length > 0){
				$("#checkFaildDesc").val(ApplyModifydiv.checkOrder.memo);
			}
		},
	}
