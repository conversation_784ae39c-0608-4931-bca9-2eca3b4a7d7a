/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.counter.dto;

import java.io.Serializable;

import java.math.BigDecimal;

/**
 * @description:(客户份额明细)
 * @reason:
 * <AUTHOR>
 * @date 2018年2月7日 下午5:54:35
 * @since JDK 1.6
 */
public class CustVolDtlDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -8431343439549865528L;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 分销机构号
     */
    private String disCode;

    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 份额类型
     */
    private String fundShareClass;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品类型
     */
    private String productType;

    /**
     * 净值
     */
    private BigDecimal nav;
    /**
     * 产品状态：0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记； 8-红利发放；
     * 9-基金封闭； a-基金终止
     */
    private String productStatus;
    /**
     * 净值日期
     */
    private String navDt;
    /**
     * 购买状态：1-认购，2-申购，3-不可购买
     */
    private String buyStatus;
    /**
     * 赎回状态 1-可赎回，2-不可赎回
     */
    private String redeemStatus;

    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 协议类型
     */
    private String protocolType;
    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行卡号
     */
    private String bankAcctNo;
    /**
     * 总份额
     */
    private BigDecimal balanceVol;
    /**
     * 可用份额
     */
    private BigDecimal availVol;
    /**
     * 待确认份额
     */
    private BigDecimal unconfirmedVol;
    /**
     * 待确认金额
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 市值
     */
    private BigDecimal marketValue;

    /**
     * 锁定份额
     */
    private BigDecimal lockVol;

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getProductStatus() {
        return productStatus;
    }

    public void setProductStatus(String productStatus) {
        this.productStatus = productStatus;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public String getBuyStatus() {
        return buyStatus;
    }

    public void setBuyStatus(String buyStatus) {
        this.buyStatus = buyStatus;
    }

    public String getRedeemStatus() {
        return redeemStatus;
    }

    public void setRedeemStatus(String redeemStatus) {
        this.redeemStatus = redeemStatus;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAcctNo() {
        return bankAcctNo;
    }

    public void setBankAcctNo(String bankAcctNo) {
        this.bankAcctNo = bankAcctNo;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getAvailVol() {
        return availVol;
    }

    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }

    public BigDecimal getUnconfirmedVol() {
        return unconfirmedVol;
    }

    public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
        this.unconfirmedVol = unconfirmedVol;
    }

    public BigDecimal getUnconfirmedAmt() {
        return unconfirmedAmt;
    }

    public void setUnconfirmedAmt(BigDecimal unconfirmedAmt) {
        this.unconfirmedAmt = unconfirmedAmt;
    }

    public BigDecimal getMarketValue() {
        return marketValue;
    }

    public void setMarketValue(BigDecimal marketValue) {
        this.marketValue = marketValue;
    }

    public BigDecimal getLockVol() {
        return lockVol;
    }

    public void setLockVol(BigDecimal lockVol) {
        this.lockVol = lockVol;
    }

}
