/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * 
 * @description:(客户银行卡信息)
 * <AUTHOR>
 * @date 2018年5月4日 下午2:10:55
 * @since JDK 1.6
 */
public class CustBankCardDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -4143982837388293925L;
    /**
     * 银行卡号
     */
    private String bankAcct;
    /**
     * 银行编码
     */
    private String bankCode;
    /**
     * 银行全称
     */
    private String bankRegionName;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    /**
     * 银行账户状态 0:正常;1:待审核;2:注销;3:冻结;4:销户待确认;5:冻结待确认
     */
    private String bankAcctStatus;
    /**
     * 卡验证状态 2-验证通过，3-验证失败
     */
    private String vrfyCardStat;
    
    public String getBankAcctStatus() {
		return bankAcctStatus;
	}

	public void setBankAcctStatus(String bankAcctStatus) {
		this.bankAcctStatus = bankAcctStatus;
	}

	public String getVrfyCardStat() {
		return vrfyCardStat;
	}

	public void setVrfyCardStat(String vrfyCardStat) {
		this.vrfyCardStat = vrfyCardStat;
	}
	
    public String getBankAcct() {
        return bankAcct;
    }
    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }
    public String getBankCode() {
        return bankCode;
    }
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }
    public String getBankRegionName() {
        return bankRegionName;
    }
    public void setBankRegionName(String bankRegionName) {
        this.bankRegionName = bankRegionName;
    }
    public String getCpAcctNo() {
        return cpAcctNo;
    }
    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }
    
}

