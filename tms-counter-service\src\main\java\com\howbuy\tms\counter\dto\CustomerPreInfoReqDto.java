/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseRequestDto;

/**
 * @description:(查询crm预约列表)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月4日 上午9:20:01
 * @since JDK 1.6
 */
public class CustomerPreInfoReqDto extends BaseRequestDto {

    /**
     * serialVersionUID:
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 2071625239691832058L;
    /**
     * 客户号
     */
    private String custNo;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 客户号
     */
    private String idNo;
    /**
     * 一账通号
     */
    private String hbOneNo;
    /***
     * 预约单号
     */
    private String preId;
    /**
     * 开始时间
     */
    private String startDt;
    /***
     * 结束时间
     */
    private String endDt;
    /***
     * 产品名称
     */
    private String fundName;
    /***
     * 产品代码
     */
    private String fundCode;
    /***
     * 成单方式
     */
    private String preType;
    /***
     * 预约业务类型
     */
    private String tradeType;
    /***
     * 确认状态
     */
    private String tradeState;
    /**
     * 预约状态
     */
    private String preBookState;
    /**
     * 无纸化预约状态
     */
    private String noPaperState;

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getHbOneNo() {
        return hbOneNo;
    }

    public void setHbOneNo(String hbOneNo) {
        this.hbOneNo = hbOneNo;
    }

    public String getPreId() {
        return preId;
    }

    public void setPreId(String preId) {
        this.preId = preId;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getPreBookState() {
        return preBookState;
    }

    public void setPreBookState(String preBookState) {
        this.preBookState = preBookState;
    }

    public String getNoPaperState() {
        return noPaperState;
    }

    public void setNoPaperState(String noPaperState) {
        this.noPaperState = noPaperState;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

}
