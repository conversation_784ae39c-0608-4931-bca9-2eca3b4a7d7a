/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common.util;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.net.InetAddress;
import java.net.UnknownHostException;

import javax.servlet.http.HttpServletRequest;

/**
 * @description:(Http 工具类) 
 * <AUTHOR>
 * @date 2017年3月29日 下午2:17:16
 * @since JDK 1.7
 */
public class HttpUtil {
    private static final Logger LOGGER = LogManager.getLogger(HttpUtil.class);

    public static final String X_FORWARD_FOR = "x-forwarded-for";
    public static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    public static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    public static final String SELF_IP_ADDRESS_1 = "127.0.0.1";
    public static final String SELF_IP_ADDRESS_2 = "0:0:0:0:0:0:0:1";
    public static final String UNKNOWN = "unknown";
    public static final int IP_ADDRESS_LENGTH = 15;
    public static final String INDEX_SIGN_1 = ",";

    public static String getIpAddr(HttpServletRequest request){  
        String ipAddress = request.getHeader(X_FORWARD_FOR);
            if(ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader(PROXY_CLIENT_IP);
            }  
            if(ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader(WL_PROXY_CLIENT_IP);
            }  
            if(ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = getString(request);
            }  
            //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            //"***.***.***.***".length() = 15
            if(ipAddress!=null && ipAddress.length()>IP_ADDRESS_LENGTH){
                if(ipAddress.indexOf(INDEX_SIGN_1) != -1){
                    ipAddress = ipAddress.substring(0,ipAddress.indexOf(INDEX_SIGN_1));
                }  
            }  
            return ipAddress;   
    }

    private static String getString(HttpServletRequest request) {
        String ipAddress;
        ipAddress = request.getRemoteAddr();
        if(SELF_IP_ADDRESS_1.equals(ipAddress) || SELF_IP_ADDRESS_2.equals(ipAddress)){
            //根据网卡取本机配置的IP
            InetAddress inet=null;
            try {
                inet = InetAddress.getLocalHost();
            } catch (UnknownHostException e) {
                LOGGER.error("", e);
            }
            if(inet !=null){
                ipAddress= inet.getHostAddress();
            }
        }
        return ipAddress;
    }
}

