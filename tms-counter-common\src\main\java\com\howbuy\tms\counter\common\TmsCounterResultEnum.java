/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common;

import com.howbuy.tms.counter.common.util.StringUtils;

/**
 * @description:(中台柜台结果代码枚举) 
 * <AUTHOR>
 * @date 2017年3月27日 下午4:13:08
 * @since JDK 1.7
 */
public enum TmsCounterResultEnum {
    SUCC("0000","成功"),
    UNLOGINED("0001","未登录"),
    SESSION_TIME_OUT("0002","session 过期,操作员信息丢失"),
    FUND_NAV_ERR("0003","未查询到基金净值"),
    COUNTER_END("0004","中台柜台已收市"),
    COUNTER_END_EXCEPTION("0005","柜台收市异常"),
    COUNTER_CHECKER_REPLY("0006","审核人与录入人是同一人"),
    COUNTER_NOT_ALLOW_SIMU_TRADE("0007","中台柜台不可以下私募订单"),
    PRODUCT_NOT_EXIST("0008","产品不存在"),
    COUNTER_END_TIME("0009","未到15点收市时间"),
    SUBSCRIPTION_NOT_EXIST("0010","预约信息不存在"),
    FEE_RATE_IS_NULL("0011","未配置基金费率"),
    APP_AMT_IS_NULL("0012","申请金额为空"),
    PRODUCT_IS_NULL("0013","产品不存在"),
    BANK_INFO_PARAMS_ERROR("0014","银行卡参数信息错误"),
    TXACCT_IS_EXIT("0015","交易账号已存在"),
    IS_NOT_INDIVIDUAL("0016","不是个人投资者用户"),
    OPEN_ACCOUT_ERROR("0017","开户失败"),
    TXACCT_IS_NOT_EXIT("0018","交易账户不存在"),
    OVER_RISK_TEST("0019","风险测评已过期"),
    NO_RISK_TEST("0020","未做风险测评"),
    BIND_ERROR("0021", "绑卡失败"),
    REJECT_ERROR("0022", "无纸化驳回失败"),
    NOT_FIND_CUST_INFO("0023", "客户信息不存在"),
    CURRENT_ASSET_CERTIFICATE_STATUS_VALID("0024", "资产证明无效"),
    OVER_BUY_PALACE("0025", "超过购买总人数"),
    PARAMS_ERROR("0026","参数错误"),
    COUNTER_DEAL_NOT_EXIT("0027", "柜台订单不存在"),
    HBONENO_IS_NOT_EXIT("0028", "一帐通号不存在"),
    ASSETCERTIFICATE_FILE_NOT_FIND("0029", "未查询到生成的好买资产证明文件"),
    COUNTER_DEAL_NOT_EXIST("0030", "柜台订单不存在"),
    UN_KNOW_CHECK_ACTION("0031", "未知的审核操作"),
    COUNTER_CHECKER_MODIFY_ERROR("0032", "修改人和录入人不是同一人"),
    PREID_NOT_EXIT("0033", "预约id不存在"),
    PROUDCT_CHANNEL_IS_NULL("0034", "产品通道为空"),
    PROUDCT_CHANNEL_WITH_NULL_SYSCODE("0035", "产品通道对应的批处理系统码不存在"),
    NOT_MODIFY("0036", "没有修改信息，不能修改"),
    EXIT_UNCHECK_CANCEL_DEAL("0037", "存在未审核的柜台订单"),
    PRODUCT_CAN_NOT_BUY("0038", "产品不可购买"),
    CUST_NOT_EXIST("0039","客户信息不存在"),
    ID_NO_ERROR("0040", "证件号不正确"),
    CP_ACCT_IS_EXIT("0041","资金账号已存在"),
    REDEEM_ORDER_NOT_SELECTED("0042","赎回订单未选择"),
    EXCHANGE_ORDER_NOT_SELECTED("0043","基金转换订单未选择"),
    EXCHANGE_APP_VOL_IS_NULLORZERO("0044","基金转换申请转出份额不能为空或0"),
    EXCHANGE_FUNDCODE_IS_NULL("0045","基金转换转出基金代码不能为空"),
    EXCHANGE_T_FUNDCODE_IS_NULL("0046","基金转换转入基金代码不能为空"),
    CANCEL_MEMO_CAN_NOT_NULL("0047", "撤单原因不能为空"),
    CANCEL_APPOINT_DATE_CAN_NOT_NULL("0048", "支持预约的产品撤单，预约开放日历不能为空"),
    EXIT_UN_FINISH_COUNTER_DEAL("0049","存在未完成柜台订单"),
    ACK_DAY_END_IS_COMP("0050","批处理确认处理日终已经执行完成, 不可以提交全赎申请单"),
    NOT_SIGN_FLAG("0051","未签署合格投资者认证"),
    MERGE_OUT_ORDER_NOT_SELECTED("0052","份额合并转出订单未选择"),
    MERGE_IN_ORDER_NOT_SELECTED("0053","份额合并转入订单未选择"),
    MERGE_VOL_EXIT_UNCFMVOL("0054","存在冻结份额， 不可以份额合并"),
    TRANS_OUT_ORDER_IS_EMPTY("0055","份额迁移转出订单传入为空"),
    TRANS_IN_ORDER_NOT_SELECTED("0056","份额迁移转入订单未选择"),
    TRANS_VOL_EXIT_UNCFMVOL("0057","存在冻结份额， 不可以份额迁移"),
    CUST_OUT_BALS_IS_EMPTY("0058","转出银行卡资产信息为空，不能提交！"),
    CUST_BANK_BALS_IS_QUNJI_PRODUCT("0059", "存在群济私募持仓, 请先迁移私募群济持仓！"),
    CUST_EXIST_UNCONFIRMED_VOL("0060", "可用份额与总份额不相等，存在在途！"),
    COUNTER_CHECK_PARAMS_ERROR("0061", "审核失败，参数异常！"),
    COUNTER_CHECK_NOT_QUERY_ORDER("0062", "审核失败，未查到审核订单！"),
    COUNTER_ACTIDISCOUNT_ERROR("0063", "活动折扣配置错误！"),
    COUNTER_DISCOUNT_ERROR("0064", "活动折扣小于预约折扣！"),
    COUNTER_AGENT_DISCOUNT_ERROR("0065", "活动折扣小于代销折扣！"),
    COUNTER_HIGH_APPOINT_DAY_IS_NULL("0066", " 没有该产品该业务类型匹配的开放日！"),
    TRANSFER_TUBE_OUT_ORDER_NOT_SELECTED("0067","转托管转出持仓订单未选择"),
    COUNTER_SAVE_OR_DEL_PARAM_IS_NULL("0068","柜台添加或删除不收市TA参数为空！"),
    YL_FOF_NOT_SUPPORT_TRANSFER_OUT("0069","养老FOF不允许转托管转出"),
    PARAM_IS_ERROR("9997","参数错误"),
    CRM_PARAMS_ERROR("0069", "CRM接口异常请联系技术人员"),
    CRM_DOUBLE_DEAL_UN_GENERATE_ERROR("0070", "crm预约单的双录任务还未生成，请稍后再试"),
    EXPIRED_REDEEM_PARAMS_ERROR("0071", "预约赎回参数错误，没有选择赎回记录"),
    HOGH_APPOINT_IS_NOT_EXIST_ERROR("0072", "预约单不存在，请与投顾核实！若该订单已撤单，请将该申请单驳回并废单"),
    UPLOAD_DOUBLE_RECORD_FILE_ERROR("0073", "上传双录文件失败"),
    COUNTER_CHECK_MISS_INFO("0074", "资料未上传，不能审核"),
    COUNTER_MERGE_SUBMIT_SUBORDER_ERROR("0075", "合并上报子订单参数错误"),
    DISCODE_ERROR("0075", "合并上报子订单参数错误"),
    MODIFY_DATETIME_ERROR("0076", "请修改下单时间。"),
    DATETIME_BIG_ERROR("0077", "修改的下单时间不能超过150000"),
    UNKNOW("9998","未知异常"),
    FAILD("9999","系统异常"),
    TMSSUCC("Z0000000","成功"),
    CHANGE_BANK_ONLY("0078","换卡中存在投顾份额，只能选择注销原卡的换卡。请重新操作。"),
    CHANGE_BANK_NULL("0079","客户转入卡在选择的分销下未绑定，请让用户完成绑定后份额迁移。"),
    IN_VOL_FUND_BANK_ACCT_IS_OUT_BIND("0080","转入卡已解绑，请联系用户绑定转入卡再审核"),
    EXIT_UN_CHECK_COUNTER_DEAL("0081","存在待审核记录，强控不允许收市"),
    PENSION_FORBID_BUY("0082","柜台不支持Y份额基金交易"),
    PENSION_BANKCARD_FORBID_BUY("0083","个人养老金账户绑定的银行不支持该业务"),
    SEND_PENSION_RETRY("0084","发送中登查询账户信息失败，请重新提交"),
    NO_TRADE_TRANSFER_NEED_TRANSFER_PRICE("0085","股权产品非交易过户需维护转让价格"),
    
    CUST_ORDER_INFO_NULL("0086","反洗钱中数据缺失，无法下单"),

    MESSAGE_INFO_NULL("0087","信息错误"),

    ORDER_ALEARY_SH("0088","订单已被审核"),

    CUST_BANK_CARD_DATA_ERROR("0089","份额迁移数据异常"),
    CUST_BANK_CARD_UNBIND("0090","转入卡在【分销名称】下已解绑，请联系用户绑定后再审核"),
    
    ;
    
    private String code;
    private String desc;

    TmsCounterResultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDesc(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (TmsCounterResultEnum p : values()) {
            if (code.equals(p.getCode())) {
                return p.getDesc();
            }
        }
        return null;
    }

    public String getCode(){
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

