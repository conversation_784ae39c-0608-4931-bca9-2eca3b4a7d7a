<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 高端产品参数配置 -->
	<dubbo:reference id="tmscounter.highProductParamConfService" registry="interlayer"
					 interface="com.howbuy.interlayer.product.service.HighProductParamConfService" check="false"/>

	<!-- 查询ta信息 -->
	<dubbo:reference id="tmscounter.queryTaInfoService" registry="interlayer"
					 interface="com.howbuy.interlayer.product.service.QueryTaInfoService" check="false"/>
	<!-- 高端产品服务 -->
	<dubbo:reference id="tmscounter.highProductService" interface="com.howbuy.interlayer.product.service.HighProductService" registry="interlayer" check="false" />

	<!-- 查询代销场检产品代码 -->
	<dubbo:reference id="tmscounter.queryNotHBJGFundListService" interface="com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService" registry="interlayer" check="false" />

</beans>