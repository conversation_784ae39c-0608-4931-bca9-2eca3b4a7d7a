/**
*修改分红方式
*<AUTHOR>
*@date 2017-04-01 15:16
*/
$(function(){
	Init.init();
	ModifyDivFund.init();
	ModifyDivFund.custInfo = {};
	ModifyDivFund.fundDivList = [];
	ModifyDivFund.currDate = '';
});
var ModifyDivFund = {
	init:function(){
		$("#confimModifyDivBtn").on('click',function(){
			ModifyDivFund.confirm();
		}); 
		
		$("#queryCustInfoBtn").on('click',function(){
			QueryCustInfo.queryCustInfo();
		});
		
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询基金分红方式
		 */
		$(".searchIcon").on('click',function(){
			ModifyDivFund.queryFundDivInfo(QueryCustInfo.custInfo.disCode,QueryCustInfo.custInfo.custNo);
		});

		$("#fundCode").on('blur',function(){
			ModifyDivFund.queryFundDivInfo(QueryCustInfo.custInfo.disCode,QueryCustInfo.custInfo.custNo);
		});
	},


	/***
	 * 确认修改分红方式
	 */
	confirm : function(dealAppNo){
		CommonUtil.disabledBtn("confimModifyDivBtn");
		var  uri= TmsCounterConfig.MULTI_MODIFYDIV_FUND_CONFIRM_URL ||  '';

		var fundTypeValidate = true;
		var allowModifyDivModeValidate = true;
		var checkedList = [];
		$("input[name ='selectFundDiv']:checked").each(function (){
			var id = $(this).attr("id");

			var fundDiv = QueryFundDivInfo.fundDivList[id];
			fundDiv.fundDivMode = $("#targetDivMode_"+id).val();
			checkedList.push(fundDiv);

			if(fundDiv.fundType == '3'){
				fundTypeValidate = false;
				return false;
			}

			if(fundDiv.allowModifyDivMode == '0'){
				allowModifyDivModeValidate = false;
				return false;
			}


		});

		if(checkedList.length == 0){
			CommonUtil.layer_tip("请选择修改记录");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}

		if(!fundTypeValidate){
			CommonUtil.layer_tip("货币型基金不支持修改分红方式");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}
		if(!allowModifyDivModeValidate){
			CommonUtil.layer_tip("暂不支持修改分红方式");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#modifydivConfirmForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}

		var modifydivConfirmForm = $("#modifydivConfirmForm").serializeObject();

		var validRst = Valid.valiadateFrom($("#transactorInfoForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}

		var transactorInfoForm = $("#transactorInfoForm").serializeObject();

		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}

		var divMode = CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_DIS_MAP, $("#fundDivMode").text());
		if(divMode == $("#targetDivMode").val()){
			CommonUtil.layer_tip("目标分红方式不能与原分红方式一致");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}

		modifydivConfirmForm.appDtm = modifydivConfirmForm.appDt + '' +modifydivConfirmForm.appTm;
		// if(!Valid.valiadTradeTime(modifydivConfirmForm.appTm)){
		// 	CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
		// 	CommonUtil.enabledBtn("confimModifyDivBtn");
		// 	return false;
		// }

		var dealAppNo ="";
		if(!(typeof ApplyModifydiv == "undefined")){
			dealAppNo = ApplyModifydiv.checkOrder.dealAppNo;
		}

		var reqparamters ={"dealAppNo":dealAppNo,"modifydivConfirmForm":JSON.stringify(checkedList),"fundDiv": JSON.stringify(QueryFundDivInfo.fundDiv),"custInfoForm":JSON.stringify(QueryCustInfo.custInfo),"transactorInfoForm":JSON.stringify(transactorInfoForm)};
		reqparamters.transactorInfoForm = JSON.stringify(transactorInfoForm);

		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ModifyDivFund.callBack);

	},
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			if($(".confimBtn").length > 0){
				CommonUtil.disabledBtnWithClass("confimBtn");
				CommonUtil.disabledBtn("abolishBtn");
			}
			CommonUtil.layer_tip("修改分红方式提交成功");
		}else{
			CommonUtil.layer_tip("修改分红方式提交失败,"+respDesc);
		}

		if(!$(".confimBtn").length > 0){
			CommonUtil.enabledBtn("confimModifyDivBtn");
		}
	},
	/**
	 * 查询基金分红方式
	 */
	queryFundDivInfo:function(disCode,custNo,fundCode){
		var  uri= TmsCounterConfig.QUERY_FUND_MODIFY_DIV_URL ||  {};
		if(!fundCode){
			fundCode = $("#fundCode").val();
		}
		var reqparamters = {"disCode":disCode,"custNo":custNo,"fundCode":fundCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ModifyDivFund.queryFundDivInfoCallBack);
	},

	/**
	 * 处理基金分红方式信息
	 */
	queryFundDivInfoCallBack:function(data){
		var bodyData = data.body || {};
		var fundDivList = bodyData.fundDivList || {};
		QueryFundDivInfo.fundDivList = fundDivList;


		if(QueryFundDivInfo.fundDivList.length >0){
			var fundDiv = QueryFundDivInfo.fundDivList[0];
			/**
			 * 券商
			 */
			if("7" == fundDiv.fundType  || '9' == fundDiv.fundType){
				CommonUtil.layer_tip("该基金不是普通公募基金");
				return false;
			}

			/**
			 * 理财
			 */
			if("2" == fundDiv.fundType &&  '21'== fundDiv.fundSubType){
				CommonUtil.layer_tip("该基金不是普通公募基金");
				return false;
			}
			$("#modifydivConfirmFormId").empty();
			$(QueryFundDivInfo.fundDivList).each(function(index,element){

				var trList = [];
				trList.push('<input class="selectFundDiv" id="'+index+'" name="selectFundDiv" type="checkbox" data-index="' + index + '"></input>');
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.fundAttr));
				trList.push(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, fundDiv.fundStat));
				trList.push(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,fundDiv.divMode, '--'));
				trList.push("<span class='select-box inline'><select name='fundDivMode' id='targetDivMode_"+index+"' class='select'  isnull='false' datatype='s' errormsg='目标分红方式' ><option  value='0'>红利再投</option><option  value='1'>现金红利</option></select></span>");
				trList.push(CommonUtil.formatData(element.fundTxAcctNo));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP,element.protocolType));

				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#modifydivConfirmFormId").append(trAppendHtml);

			});
		}

	}
};

