/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:回款
 * @author: chuanguang.tang
 * @date: 2021/7/28 17:34
 * @since JDK 1.8
 */
public class RefundDto implements Serializable {

	private static final long serialVersionUID = -6865728139154824769L;
	/**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 回款方向
     */
    private String refundDirection;
    /**
     * 回款到可用金额
     */
    private BigDecimal refundFinaAvailAmt;
    /**
     * 回款备注
     */
    private String refundFinaAvailMemo;
    /**
     * 撤单方向
     */
    private String withdrawDirection;

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getRefundDirection() {
        return refundDirection;
    }

    public void setRefundDirection(String refundDirection) {
        this.refundDirection = refundDirection;
    }

    public BigDecimal getRefundFinaAvailAmt() {
        return refundFinaAvailAmt;
    }

    public void setRefundFinaAvailAmt(BigDecimal refundFinaAvailAmt) {
        this.refundFinaAvailAmt = refundFinaAvailAmt;
    }

    public String getRefundFinaAvailMemo() {
        return refundFinaAvailMemo;
    }

    public void setRefundFinaAvailMemo(String refundFinaAvailMemo) {
        this.refundFinaAvailMemo = refundFinaAvailMemo;
    }

    public String getWithdrawDirection() {
        return withdrawDirection;
    }

    public void setWithdrawDirection(String withdrawDirection) {
        this.withdrawDirection = withdrawDirection;
    }
}
