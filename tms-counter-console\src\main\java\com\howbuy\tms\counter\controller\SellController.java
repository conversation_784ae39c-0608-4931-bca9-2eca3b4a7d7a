/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.prosale.request.QueryCurrentPreInfoRequest;
import com.howbuy.crm.prosale.response.GetCurrentPreInfoResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.model.appointment.ProductAppointmentInfoModel;
import com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService;
import com.howbuy.interlayer.product.service.high.request.QueryNotHBJGFundListRequest;
import com.howbuy.interlayer.product.service.high.response.QueryNotHBJGFundListResponse;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.AuditingOrderFileCmd;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.controller.context.ContextUtils;
import com.howbuy.tms.counter.controller.context.SellContext;
import com.howbuy.tms.counter.controller.validate.ValidateUtils;
import com.howbuy.tms.counter.dto.*;
import com.howbuy.tms.counter.dto.CounterOrderFormDto.CounterCustBalanceVolDtlBean;
import com.howbuy.tms.counter.dto.QueryAcctBalanceDtlRespDto.DtlBean;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.OtherInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.counter.service.validate.ValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.counter.util.CounterOrderFormUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 
 * @description:(赎回控制器)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年4月1日 下午3:01:30
 * @since JDK 1.6
 */
@Controller
public class SellController {
    private Logger logger = LogManager.getLogger(SellController.class);
    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    private TmsCounterOutService tmsCounterOutService;
    
    @Autowired
    private ValidateService validateService;

    @Autowired
    @Qualifier("tmscounter.preBookService")
    private PreBookService preBookService;

    @Autowired
    @Qualifier("tmscounter.queryNotHBJGFundListService")
    private QueryNotHBJGFundListService queryNotHBJGFundListService;
    
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    /**
     * 
     * redeemConfirm:(赎回确认)
     * 
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/sellconfirm.htm")
    public ModelAndView sellConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 构建赎回内容上下文
       SellContext sellContext = buildSellContext(request);

        //校验产品信息
        String productCode = sellContext.getDtlList().get(0).getProductCode();
        validateService.validateProductInfo(productCode);
        //查询高端产品信息
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
        sellContext.setHighProduct(highProductBaseBean);

        if (null == sellContext.getCustomerAppointmentInfoDto() || StringUtils.isEmpty(sellContext.getCustomerAppointmentInfoDto().getAppointId())) {
            GetCurrentPreInfoResponse currentPreInfoResponse = getGetCurrentPreInfoResponse(sellContext.getCustInfoDto(), productCode);
            if(currentPreInfoResponse != null){
                sellContext.getCustomerAppointmentInfoDto().setAppointId(currentPreInfoResponse.getPreId());
            }
        }

        // 校验线上资料
        if(sellContext.getAuditingOrderFileCmd() != null
                && !org.springframework.util.StringUtils.isEmpty(sellContext.getAuditingOrderFileCmd().getOrderid())) {
            tmsCounterOutService.validateOrderFileStatus(sellContext.getAuditingOrderFileCmd());
        }else{
            tmsCounterOutService.validateOrderFileExist(ContextUtils.buildQueryCotext(sellContext, TmsCounterConstant.CRM_TRADE_TYPE_SELL),
                    OpCheckNode.PRE_CHECK.getCode());
        }

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(sellContext.getCustInfoDto().getDisCode());
        disInfoDto.setOutletCode(TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode());

        CounterOrderDto condition = ContextUtils.getValidateReplyCondition(sellContext, TxCodes.HIGH_COUNTER_REDEEM);
        tmsCounterService.validateReplyOrder(condition, disInfoDto);

        Date appDtm = DateUtil.string2Date(sellContext.getCounterRedeemReqDto().getAppDtm(), DateUtils.YYYYMMDDHHMMSS);
        sellContext.setAppDtm(appDtm);

        // 校验申请日期
        ValidateUtils.validateAppDtm(appDtm);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        // 赎回下单信息
        fillSellOrder(sellContext);

        CounterRedeemRespDto responseDto = tmsCounterService.counterRedeem(sellContext.getCounterRedeemReqDto(), disInfoDto);

        // 初审通过
        tmsCounterOutService.auditingFile(sellContext.getOperatorInfoCmd(), sellContext.getAuditingOrderFileCmd(), responseDto.getDealAppNo());

        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    private void fillSellOrder(SellContext sellContext) {
        String appDt = DateUtils.formatToString(sellContext.getAppDtm(), DateUtils.YYYYMMDD);
        String appTm = DateUtils.formatToString(sellContext.getAppDtm(), DateUtils.HHMMSS);
        String productCode = sellContext.getDtlList().get(0).getProductCode();

        CounterRedeemReqDto counterRedeemReqDto = sellContext.getCounterRedeemReqDto();
        // 申请日期
        counterRedeemReqDto.setAppDt(appDt);
        // 申请时间
        counterRedeemReqDto.setAppTm(appTm);

        counterRedeemReqDto.setChecker(sellContext.getOperatorInfoCmd().getOperatorNo());
        counterRedeemReqDto.setTxAcctNo(sellContext.getCustInfoDto().getCustNo());
        counterRedeemReqDto.setFundCode(productCode);
        counterRedeemReqDto.setFundName(sellContext.getHighProduct().getFundAttr());

        counterRedeemReqDto.setCustName(sellContext.getCustInfoDto().getCustName());
        counterRedeemReqDto.setIdNo(PrivacyUtil.encryptIdCard(sellContext.getCustInfoDto().getIdNo()));
        counterRedeemReqDto.setDisCode(sellContext.getCustInfoDto().getDisCode());
        counterRedeemReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        counterRedeemReqDto.setAgentFlag(sellContext.getOtherInfoDto().getAgentFlag());
        counterRedeemReqDto.setConsCode(sellContext.getOtherInfoDto().getConsCode());
//        counterRedeemReqDto.setLargeRedmFlag(counterRedeemReqDto.getLargeRedmFlag());// 巨额赎回顺延0-不顺延
        counterRedeemReqDto.setBankAcct(PrivacyUtil.encryptBankAcct(counterRedeemReqDto.getBankAcct()));

        counterRedeemReqDto.setTransactorIdNo(sellContext.getTransactorInfoDto().getTransactorIdNo());
        counterRedeemReqDto.setTransactorIdType(sellContext.getTransactorInfoDto().getTransactorIdType());
        counterRedeemReqDto.setTransactorName(sellContext.getTransactorInfoDto().getTransactorName());
        counterRedeemReqDto.setAppointmentDealNo(sellContext.getCustomerAppointmentInfoDto().getAppointId());
        //纸质成单
        counterRedeemReqDto.setOrderFormType(OrderFormTypeEnum.PAPER.getCode());
        // 产品通道
        counterRedeemReqDto.setProductChannel(sellContext.getHighProduct().getProductChannel());
        counterRedeemReqDto.setTaCode(sellContext.getHighProduct().getTaCode());
        CommonUtil.setCommonOperInfo(sellContext.getOperatorInfoCmd(), counterRedeemReqDto);

        if(sellContext.getAuditingOrderFileCmd() != null){
            counterRedeemReqDto.setMaterialId(sellContext.getAuditingOrderFileCmd().getOrderid());
        }

        FundInfoAndNavBean fundInfoAndNavBean = null;
        try {
            fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(productCode, appDt, appTm);
        } catch (Exception e) {
            logger.error("query fundInfoAndNavBean erro :", e);
        }

        if(fundInfoAndNavBean != null){
            counterRedeemReqDto.setNav(fundInfoAndNavBean.getNav());
        }

        // 转换赎回申请份额map
        List<CounterRedeemReqDto.AppDetail> appList = counterRedeemReqDto.getAppList();
        Map<String, CounterRedeemReqDto.AppDetail> appMap = new HashMap<>(appList.size());
        for (CounterRedeemReqDto.AppDetail app : appList) {
            appMap.put(app.getCpAcctNo(), app);
        }

        //客户持有产品份额快照
        List<CounterCustBalanceVolDtlBean> dtlBeanList = new ArrayList<>(sellContext.getDtlList().size());
        for (DtlBean dtl : sellContext.getDtlList()) {
            CounterCustBalanceVolDtlBean bean = new CounterCustBalanceVolDtlBean();
            //可用份额
            bean.setAvailVol(dtl.getAvailVol());
            //持有份额
            bean.setBalanceVol(dtl.getBalanceVol());
            //市值
            bean.setMarketValue(dtl.getMarketValue());
            //冻结份额
            bean.setUnconfirmedVol(dtl.getUnconfirmedVol());
            //开放日
            bean.setOpenRedeDt(dtl.getOpenRedeDt());
            bean.setCpAcctNo(dtl.getCpAcctNo());
            bean.setBankAcctNo(PrivacyUtil.encryptBankAcct(dtl.getBankAcctNo()));
            CounterRedeemReqDto.AppDetail app = appMap.get(dtl.getCpAcctNo());
            if (app != null) {
                // 申请份额
                bean.setAppVol(app.getAppVol());
                bean.setRefundFinaAvailAmt(app.getRefundFinaAvailAmt());
                bean.setRefundFinaAvailMemo(app.getRefundFinaAvailMemo());
                bean.setRedeemCapitalFlag(app.getRedeemCapitalFlag());
            }

            dtlBeanList.add(bean);
        }

        //查询基金状态
        String openStartDt = null;
        if(sellContext.getProductAppointmentInfoModel() != null){
            openStartDt = sellContext.getProductAppointmentInfoModel().getOpenStartDt();
        }
        String submitTaDt = CounterOrderFormUtil.getSubmitTradeDt(counterRedeemReqDto.getAppDt(), openStartDt );
        HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(productCode, submitTaDt);
        String counterOrderFormMemo = CounterOrderFormUtil.createCounterOrderForm(sellContext.getHighProduct(), highProductStatInfoBean,
                sellContext.getProductAppointmentInfoModel(), dtlBeanList);
        counterRedeemReqDto.setOrderFormMemo(counterOrderFormMemo);
    }

    private SellContext buildSellContext(HttpServletRequest request){
        //赎回表单信息
        String redeemConfirmCmd = request.getParameter("sellConfirmForm");
        //客户信息
        String custInfoForm = request.getParameter("custInfoForm");
        //份额信息
        String fundInfoForm = request.getParameter("fundDtlForm");
        //基金信息
        //String fundName = request.getParameter("fundName");
        //CRM预约信息
        String appointmentForm = request.getParameter("appointmentForm");
        //预约开放日历
        String productAppointmentInfoForm= request.getParameter("productAppointmentInfoForm");
        // CRM材料ID
        String materialinfoForm = request.getParameter("materialinfoForm");
        String othetInfoForm = request.getParameter("othetInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        AuditingOrderFileCmd auditingOrderFileCmd =  null;
        if(!org.springframework.util.StringUtils.isEmpty(materialinfoForm)){
            auditingOrderFileCmd = JSON.parseObject(materialinfoForm, AuditingOrderFileCmd.class);
        }
        // 操作员信息
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        OtherInfoDto otherInfoDto = JSON.parseObject(othetInfoForm, OtherInfoDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        logger.info("SESSION_OPERATORINFO : {}", JSON.toJSONString(operatorInfoCmd));
        CounterRedeemReqDto counterRedeemReqDto = JSON.parseObject(redeemConfirmCmd, CounterRedeemReqDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        List<DtlBean> dtlList = JSON.parseArray(fundInfoForm, DtlBean.class);
        CustomerAppointmentInfoDto customerAppointmentInfoDto = JSON.parseObject(appointmentForm, CustomerAppointmentInfoDto.class);
        ProductAppointmentInfoModel productAppointmentInfoModel = JSON.parseObject(productAppointmentInfoForm, ProductAppointmentInfoModel.class);

        SellContext context = new SellContext();
        context.setDtlList(dtlList);
        //赎回表单信息
        context.setCounterRedeemReqDto(counterRedeemReqDto);
        context.setCustInfoDto(custInfoDto);
        context.setProductAppointmentInfoModel(productAppointmentInfoModel);
        context.setCustomerAppointmentInfoDto(customerAppointmentInfoDto);
        context.setAuditingOrderFileCmd(auditingOrderFileCmd);
        context.setOtherInfoDto(otherInfoDto);
        context.setTransactorInfoDto(transactorInfoDto);
        context.setOperatorInfoCmd(operatorInfoCmd);

        return context;
    }

    private GetCurrentPreInfoResponse getGetCurrentPreInfoResponse(CustInfoDto custInfoDto, String fundCode) {
        QueryCurrentPreInfoRequest queryCurrentPreInfoRequest = new QueryCurrentPreInfoRequest();
        List<String> prebookStateList = new ArrayList<String>();
        prebookStateList.add(PreBookStateEnum.CONFIRM.getCode());
        queryCurrentPreInfoRequest.setPrebookState(prebookStateList);
        queryCurrentPreInfoRequest.setFundCode(fundCode);
        List<String> preTypeList = new ArrayList<String>();
        preTypeList.add(OrderFormTypeEnum.PAPER.getCode());
        queryCurrentPreInfoRequest.setPreType(preTypeList);
        List<String> tradeTypeList = new ArrayList<String>();
        tradeTypeList.add(PreTradeTypeEnum.SELL.getCode());
        queryCurrentPreInfoRequest.setTradeType(tradeTypeList);
        String hbOneNo = tmsCounterOutService.queryHboneNoByTxAccountNo(custInfoDto.getCustNo());
        queryCurrentPreInfoRequest.setHboneNo(hbOneNo);
        queryCurrentPreInfoRequest.setUseFlag(PreBookUseFlagEnum.NOT_USED.getCode());
        return preBookService.getCurrentPreInfo(queryCurrentPreInfoRequest);
    }

    /**
     * 
     * queryFundRedeemInfo:(查询用户赎回基金信息)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月14日 下午7:07:48
     */
    @RequestMapping("/tmscounter/queryfundredeeminfo.htm")
    public ModelAndView queryFundRedeemInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        logger.info("fundCode:{},custNo:{},disCode:{}",fundCode,custNo,disCode);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        QueryAcctBalanceDtlReqDto queryAcctBalanceDtlReqDto = new QueryAcctBalanceDtlReqDto();
        queryAcctBalanceDtlReqDto.setCustNo(custNo);
        queryAcctBalanceDtlReqDto.setFundCode(fundCode);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        QueryAcctBalanceDtlRespDto responseDto = tmsCounterService.queryAcctBalanceDtl(queryAcctBalanceDtlReqDto,disInfoDto);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }
    
  /**
   * 
   * queryCustBal:(查询客户高端产品持仓明细)
   * @param request
   * @param response
   * @return
   * @throws Exception
   * <AUTHOR>
   * @date 2018年2月7日 下午1:51:14
   */
    @RequestMapping("/tmscounter/high/querycustbaldtl.htm")
    public ModelAndView queryCustBal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //客户号
        String custNo = request.getParameter("custNo");
        // 分销机构号
        String disCode = request.getParameter("disCode");
        // 申请日期
        String appDt = request.getParameter("appDt");
        // 申请时间
        String appTm = request.getParameter("appTm");
        // 产品代码
        String productCode = request.getParameter("productCode");
        // 资金账号
        String cpAcctNo = request.getParameter("cpAcctNo");

        logger.info("SellController|queryCustBal|custNo:{}, productCode:{}, cpAcctNo:{}, disCode:{}, "
                + "appDt:{}, appTm:{}",new Object[] {custNo, productCode, cpAcctNo, disCode, appDt, appTm});
        
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        QueryAcctBalanceDtlReqDto reqDto = new QueryAcctBalanceDtlReqDto();
        reqDto.setCustNo(custNo);
        if(StringUtils.isNotEmpty(productCode)){
            reqDto.setFundCode(productCode);
        }
        
        if(StringUtils.isNotEmpty(cpAcctNo)){
            reqDto.setCpAcctNo(cpAcctNo);
        }
        
        if(StringUtils.isNotEmpty(appDt)){
            reqDto.setAppDt(appDt);
        }
        
        if(StringUtils.isNotEmpty(appTm)){
            reqDto.setAppTm(appTm);
        }

        if(StringUtils.isNotEmpty(disCode)){
            reqDto.setDisCode(disCode);
        }
        QueryAcctBalanceDtlRespDto queryAcctBalanceRespDto = tmsCounterService.querySubBalanceList(reqDto, disInfoDto);

        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        List<DtlBean> dtlList = new ArrayList<>();
        if(queryAcctBalanceRespDto != null && !CollectionUtils.isEmpty(queryAcctBalanceRespDto.getBalanceDtlList())){
            if(user.getInHBJG()) {
                QueryNotHBJGFundListResponse queryNotHBJGFundListResponse = queryNotHBJGFundListService.query(new QueryNotHBJGFundListRequest());
                for(DtlBean dtlBean : queryAcctBalanceRespDto.getBalanceDtlList()){
                    if(!queryNotHBJGFundListResponse.getFundCodes().contains(dtlBean.getProductCode())){
                        dtlList.add(dtlBean);
                    }
                }
            }else {
                dtlList.addAll(queryAcctBalanceRespDto.getBalanceDtlList());
            }
        }
       
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("dtlList",  dtlList);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    /**
     * 
     * viewCustBal:(查询客户份额明细)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月7日 下午5:52:39
     */
    @RequestMapping("/tmscounter/high/viewbaldtl.htm")
    public ModelAndView viewCustBal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String productCode= request.getParameter("productCode");
        String cpAcctNo = request.getParameter("cpAcctNo");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        logger.info("SellController|queryCustBal|custNo:{}, disCode:{}, cpAcctNo:{}",custNo, disCode, cpAcctNo);
        
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        QueryAcctBalanceDtlReqDto reqDto = new QueryAcctBalanceDtlReqDto();
        reqDto.setCustNo(custNo);
        reqDto.setCpAcctNo(cpAcctNo);
        reqDto.setFundCode(productCode);
        reqDto.setAppDt(appDt);
        reqDto.setAppTm(appTm);
        QueryAcctBalanceDtlRespDto queryAcctBalanceRespDto = tmsCounterService.querySubBalanceDtl(reqDto, disInfoDto);
        
        List<DtlBean> dtlList = null;
        if(queryAcctBalanceRespDto != null){
           dtlList = queryAcctBalanceRespDto.getBalanceDtlList();
        }
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("dtlList", dtlList);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    

    
}
