/**
 *Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;

/**
 * @description:(客户基本信息) 
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2017年3月29日 下午1:29:06
 * @since JDK 1.7
 */
public class CustBaseInfoDto implements Serializable{

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -7067613225087744704L;
    
    
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 投资者名称
     */
    private String custName;
    /**
     * 投资者类型
     */
    private String invstType;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号码
     */
    private String idNo;
    /**
     * 客户状态
     */
    private String custStat;
    public String getTxAcctNo() {
        return txAcctNo;
    }
    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }
    public String getCustName() {
        return custName;
    }
    public void setCustName(String custName) {
        this.custName = custName;
    }
    public String getInvstType() {
        return invstType;
    }
    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }
    public String getIdType() {
        return idType;
    }
    public void setIdType(String idType) {
        this.idType = idType;
    }
    public String getIdNo() {
        return idNo;
    }
    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }
    public String getCustStat() {
        return custStat;
    }
    public void setCustStat(String custStat) {
        this.custStat = custStat;
    }

}

