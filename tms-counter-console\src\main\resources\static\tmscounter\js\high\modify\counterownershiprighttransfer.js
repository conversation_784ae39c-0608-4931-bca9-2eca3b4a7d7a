/**
 * 初始化页面事件
 */

$(function () {
    //viewType 0-查看；1-审核；2-修改
    var viewType = CommonUtil.getParam("viewType");

    CounterCheck.viewType = viewType;
    CounterCheck.initBtn(viewType);
    // 查询订单
    CounterOwnerShipRightTransferPage.queryOwnershipRightTransferDtl();

    // 查询订单
    CounterCheck.queryCounterDealOrder(viewType, CounterOwnerShipRightTransferPage.initCounterOrder, null);
});

var CounterOwnerShipRightTransferPage = {

        initCounterOrder: function (data) {
            var bodyData = data.body || {};
            CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};
        },

        queryOwnershipRightTransferDtl: function () {
            var dealAppNo = CommonUtil.getParam("dealAppNo");
            if (isEmpty(dealAppNo)) {
                showMsg("申请单号不存在");
                return false;
            }
            var uri = TmsCounterConfig.QUERY_OWNERSHIP_RIGHT_TRANSFER_DTL || "";
            var reqparamters = {};
            reqparamters.dealAppNo = dealAppNo;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
            CommonUtil.ajaxAndCallBack(paramters, CounterOwnerShipRightTransferPage.buildOrderInfo);
        },


        /**
         *渲染产品信息查询结果
         */
        buildOrderInfo: function (data) {
            var bodyData = data.body || {};
            var orderInfo = bodyData.orderDtl || [];

            var appendHtml = '';
            var changeHtml = '';
            $("#layerrs").empty();
            $("#changeInfoStr").empty();
            if (orderInfo.mBusinessCode === '1142' || orderInfo.mBusinessCode === '1144' || orderInfo.mBusinessCode === '1145') {
                appendHtml =
                    '<tr className="text-c">' +
                    '<td>客户号:</td>' +
                    '<td id="txAcctNo">' + formatData(orderInfo.txAcctNo) + '</td>' +
                    '<td>客户姓名:</td>' +
                    '<td id="custName">' + formatData(orderInfo.custName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>基金代码:</td>' +
                    '<td id="fundCode">' + formatData(orderInfo.fundCode) + '</td>' +
                    '<td>基金名称:</td>' +
                    '<td id="fundName">' + formatData(orderInfo.fundName) + '</td>' +
                    '</tr>' +
                    '<tr className="text-c">' +
                    '<td>业务名称:</td>' +
                    '<td id="busiName">' + CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, orderInfo.mBusinessCode, "--") + '</td>' +
                    '<td>确认日期:</td>' +
                    '<td id="ackDt">' + formatData(orderInfo.ackDt) + '</td>' +
                    '</tr>';


                // 变更对比信息
                if (CounterCheck.viewType === '1') {
                    appendHtml += '<tr className="text-c">' +
                        '<td>转让份额:</td>' +
                        '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                        '</tr>';
                    changeHtml =
                        '<tr className="text-c">' +
                        '<td>字段:</td>' +
                        '<td>原值:</td>' +
                        '<td>修改后值:</td>' +
                        '</tr>';
                    if (orderInfo.transferPrice !== orderInfo.oldTransferPrice) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>转让价格:</td>' +
                            '<td id="oldTransferPrice">' + formatData(orderInfo.oldTransferPrice, "--") + '</td>' +
                            '<td id="transferPrice">' + formatData(orderInfo.transferPrice, "--") + '</td>' +
                            '</tr>';

                    }
                    if (orderInfo.subsAmt !== orderInfo.oldSubsAmt) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>过户份额对应的认缴金额:</td>' +
                            '<td id="oldSubsAmt">' + formatData(orderInfo.oldSubsAmt, "--") + '</td>' +
                            '<td id="subsAmt">' + formatData(orderInfo.subsAmt, "--") + '</td>' +
                            '</tr>';

                    }
                    if (orderInfo.totalSubsAmt !== orderInfo.oldTotalSubsAmt) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>过户的总认缴金额:</td>' +
                            '<td id="oldTotalSubsAmt">' + formatData(orderInfo.oldTotalSubsAmt, "--") + '</td>' +
                            '<td id="totalSubsAmt">' + formatData(orderInfo.totalSubsAmt, "--") + '</td>' +
                            '</tr>';

                    }
                    if (orderInfo.isNoTradeTransfer !== orderInfo.oldIsNoTradeTransfer) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>是否非交易过户:</td>' +
                            '<td id="oldIsNoTradeTransfer">' + CommonUtil.getMapValue(CONSTANTS.IS_NOTRADE_TRANSFER, orderInfo.oldIsNoTradeTransfer, "--") + '</td>' +
                            '<td id="isNoTradeTransfer">' + CommonUtil.getMapValue(CONSTANTS.IS_NOTRADE_TRANSFER, orderInfo.isNoTradeTransfer, "--") + '</td>' +
                            '</tr>';
                    }
                } else {
                    appendHtml +=
                        '<tr className="text-c">' +
                        '<td>基金类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUNDTYPE_MAP, orderInfo.fundType, "--") + '</td>' +
                        '<td>基金二级类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_SUB_TYPE_MAP, orderInfo.fundSubType, "--") + '</td>' +
                        '</tr>' +
                        '<tr className="text-c">' +
                        '<td>确认金额:</td>' +
                        '<td id="ackAmt">' + formatData(orderInfo.ackAmt, "--") + '</td>' +
                        '<td>确认份额:</td>' +
                        '<td id="ackVol">' + formatData(orderInfo.ackVol, "--") + '</td>' +
                        '</tr>' +

                        '<tr className="text-c">' +
                        '<td>是否非交易过户:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.IS_NOTRADE_TRANSFER, orderInfo.isNoTradeTransfer, "--") + '</td>' +
                        '<td>转让价格:</td>' +
                        '<td>' + orderInfo.transferPrice + '</td>' +
                        '</tr>' +

                        '<tr className="text-c">' +
                        '<td>过户份额对应的认缴金额:</td>' +
                        '<td>' + orderInfo.subsAmt + '</td>' +
                        '<td>过户的总认缴金额:</td>' +
                        '<td>' + orderInfo.totalSubsAmt + '</td>' +
                        '</tr>'

                }

            } else {
                // 这里是非交易转让/转入的
                appendHtml =
                    '<tr className="text-c">' +
                    '<td>转让人客户号:</td>' +
                    '<td id="outTxAcctNo">' + formatData(orderInfo.outTxAcctNo) + '</td>' +
                    '<td>转让人客户姓名:</td>' +
                    '<td id="outCustName">' + formatData(orderInfo.outCustName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>受让人客户号:</td>' +
                    '<td id="inTxAcctNo">' + formatData(orderInfo.inTxAcctNo) + '</td>' +
                    '<td>受让人客户姓名:</td>' +
                    '<td id="inCustName">' + formatData(orderInfo.inCustName) + '</td>' +
                    '</tr>' +

                    '<tr className="text-c">' +
                    '<td>基金代码:</td>' +
                    '<td id="fundCode">' + formatData(orderInfo.fundCode) + '</td>' +
                    '<td>基金名称:</td>' +
                    '<td id="fundName">' + formatData(orderInfo.fundName) + '</td>' +
                    '</tr>' +
                    '<tr className="text-c">' +
                    '<td>业务名称:</td>' +
                    '<td id="busiName">' + CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, orderInfo.mBusinessCode, "--") + '</td>' +
                    '<td>确认日期:</td>' +
                    '<td id="ackDt">' + formatData(orderInfo.ackDt) + '</td>' +
                    '</tr>';


                // 变更对比信息
                if (CounterCheck.viewType === '1') {
                    changeHtml =
                        '<tr className="text-c">' +
                        '<td>转让份额:</td>' +
                        '<td id="transferVol">' + formatData(orderInfo.transferVol) + '</td>' +
                        '</tr>'
                        + '<tr className="text-c">' +
                        '<td>字段:</td>' +
                        '<td>原值:</td>' +
                        '<td>修改后值:</td>' +
                        '</tr>';
                    if (orderInfo.transferPrice !== orderInfo.oldTransferPrice) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>转让价格:</td>' +
                            '<td id="transferPrice">' + formatData(orderInfo.oldTransferPrice, "--") + '</td>' +
                            '<td id="oldTransferPrice">' + formatData(orderInfo.transferPrice, "--") + '</td>' +
                            '</tr>';
                    }
                    if (orderInfo.subsAmt !== orderInfo.oldSubsAmt) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>过户份额对应的认缴金额:</td>' +
                            '<td id="oldSubsAmt">' + formatData(orderInfo.oldSubsAmt, "--") + '</td>' +
                            '<td id="subsAmt">' + formatData(orderInfo.subsAmt, "--") + '</td>' +
                            '</tr>';

                    }
                    if (orderInfo.totalSubsAmt !== orderInfo.oldTotalSubsAmt) {
                        changeHtml += '<tr className="text-c">' +
                            '<td>过户的总认缴金额:</td>' +
                            '<td id="oldTotalSubsAmt">' + formatData(orderInfo.oldTotalSubsAmt, "--") + '</td>' +
                            '<td id="totalSubsAmt">' + formatData(orderInfo.totalSubsAmt, "--") + '</td>' +
                            '</tr>';

                    }

                } else {
                    appendHtml +=
                        '<tr className="text-c">' +
                        '<td>基金类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUNDTYPE_MAP, orderInfo.fundType, "--") + '</td>' +
                        '<td>基金二级类型:</td>' +
                        '<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_SUB_TYPE_MAP, orderInfo.fundSubType, "--") + '</td>' +
                        '</tr>' +
                        '<tr className="text-c">' +
                        '<td>确认金额:</td>' +
                        '<td id="ackAmt">' + formatData(orderInfo.ackAmt, "--") + '</td>' +
                        '<td>确认份额:</td>' +
                        '<td id="ackVol">' + formatData(orderInfo.ackVol, "--") + '</td>' +
                        '</tr>' +

                        '<tr className="text-c">' +
                        '<td>转让价格:</td>' +
                        '<td>' + orderInfo.transferPrice + '</td>' +
                        '</tr>'+

                        '<tr className="text-c">' +
                        '<td>过户份额对应的认缴金额:</td>' +
                        '<td>' + orderInfo.subsAmt + '</td>' +
                        '<td>过户的总认缴金额:</td>' +
                        '<td>' + orderInfo.totalSubsAmt + '</td>' +
                        '</tr>'
                }

            }
            $("#layerrs").append(appendHtml);
            $("#changeInfoStr").append(changeHtml);
        }
    }
;