/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.ConsultantBean;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description:(查询投顾信息)
 * <AUTHOR>
 * @date 2017年9月18日 下午4:41:20
 * @since JDK 1.6
 */
@Controller
public class QueryConsInfoController extends AbstractController {
    private static final Logger logger = LoggerFactory.getLogger(QueryConsInfoController.class);

    @RequestMapping("tmscounter/fund/queryconscodeinfo.htm")
    public void queryConsInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String outletCode = request.getParameter("outletCode");
        logger.debug("QueryConsInfoController|queryConsInfo: outletCode:{}", outletCode);
        
        List<ConsultantBean> consultantBeanList = tmsFundCounterOutService.getConsInfo(outletCode);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("consCodes", consultantBeanList);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }
}
