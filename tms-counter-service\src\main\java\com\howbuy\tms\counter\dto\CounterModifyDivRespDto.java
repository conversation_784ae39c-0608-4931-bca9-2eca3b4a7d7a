/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;

import java.util.List;

/**
 * @description:(柜台修改分红方式响应)
 * <AUTHOR>
 * @date 2017年3月29日 下午8:26:06
 * @since JDK 1.6
 */
public class CounterModifyDivRespDto extends BaseResponseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = -4255354885718030003L;

    private FundDivDto fundDivDto;

    private List<FundDivDto> fundDivDtoList;

    public FundDivDto getFundDivDto() {
        return fundDivDto;
    }

    public void setFundDivDto(FundDivDto fundDivDto) {
        this.fundDivDto = fundDivDto;
    }

    public List<FundDivDto> getFundDivDtoList() {
        return fundDivDtoList;
    }

    public void setFundDivDtoList(List<FundDivDto> fundDivDtoList) {
        this.fundDivDtoList = fundDivDtoList;
    }
}
