/**
 *Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.howbuy.tms.common.client.TxCodes;

import com.howbuy.tms.common.enums.database.CounterCheckFlagEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.cmd.RepeatAppValidCmd;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.TradeConstant;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CounterOrderDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderReqDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderRespDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse.DealOrderBean;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description:(重复下单申请校验) 
 * @reason:
 * <AUTHOR>
 * @date 2018年2月9日 下午4:28:49
 * @since JDK 1.6
 */
@Controller
public class RepeatAppValidController {
    
    private Logger logger = LoggerFactory.getLogger(RepeatAppValidController.class);
    
    @Autowired
    private TmsCounterService tmsCounterService;
    
    @Autowired
    private QueryDealOrderListFacade queryDealOrderListFacade;
    
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    
    /**
     * 
     * calDiscountRate:(重复申请校验)
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月9日 下午4:30:19
     */
    @RequestMapping("tmscounter/high/repeatappvalid.htm")
    public void repeatAppValid(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String txAcctNo = request.getParameter("txAcctNo");
        // 产品代码
        String productCode = request.getParameter("fundCode");
        String disCode = request.getParameter("disCode");
        String appDt = request.getParameter("appDt");
        String appTm = request.getParameter("appTm");
        // 业务类型 0-购买 1-赎回 3-修改分红方式
        String busiType = request.getParameter("busiType");
        
        logger.info("RepeatAppValidController|repeatAppValid|txAcctNo:{},productCode:{}, disCode:{}, "
                + "appDt:{}, appTm:{}, busiType:{}",
                new Object[]{txAcctNo,productCode,disCode,appDt,appTm,busiType});
       
        Date appDtm = new Date();
        if(!StringUtils.isEmpty(appDt) && !StringUtils.isEmpty(appTm)){
            String appDtmStr = new StringBuffer(appDt).append(appTm).toString();
            appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
        }
        
        Date appStartDtm = DateUtils.formatToDate(appDt, DateUtils.YYYYMMDD);
        Date appEndDtm =  DateUtils.addDay(appStartDtm, 1);
        if(BusiTypeEnum.BUY.getCode().equals(busiType) || BusiTypeEnum.SELL.getCode().equals(busiType)){
            ProductAppointmentInfoBean productAppointmentInfoBean =  queryHighProductOuterService.queryAppointmentInfoByAppointDate(productCode, busiType, "A", disCode, appDtm);
             if(productAppointmentInfoBean != null){
                 appStartDtm = DateUtils.formatToDate(productAppointmentInfoBean.getAppointStartDt(), DateUtils.YYYYMMDD);
                 appEndDtm =  DateUtils.formatToDate(productAppointmentInfoBean.getApponitEndDt(), DateUtils.YYYYMMDD);
             }
        }
        
        String txCode = getTxCodeByBusiType(busiType);
        
        //获取申请成功的柜台订单
        List<CounterOrderDto> counterDealList = getAppSuccCouterDeal(txAcctNo, productCode, txCode, disCode, appDt, appTm);
        
        //获取申请成功订单
        List<DealOrderBean>  dealOrderList = getAppSucc(txAcctNo, productCode, busiType, disCode, appDt, appTm, appStartDtm, appEndDtm);
      
        String repeateFlag = "0";
        if(!CollectionUtils.isEmpty(counterDealList) || !CollectionUtils.isEmpty(dealOrderList)){
            repeateFlag = "1";
        }
        
        RepeatAppValidCmd repeatAppValidCmd = new RepeatAppValidCmd();
        repeatAppValidCmd.setRepeateFlag(repeateFlag);
        
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(repeatAppValidCmd);
        WebUtil.write(response, tmsCounterResult);
    }
    
    /**
     * 
     * getAppSucc:(申请成功订单)
     * @param txAcctNo
     * @param productCode
     * @param busiType 0-购买 1-赎回2-修改分红方式
     * @param txCode
     * @param disCode
     * @param appDt
     * @param appTm
     * @return
     * <AUTHOR>
     * @date 2018年2月9日 下午5:44:32
     */
    private List<DealOrderBean> getAppSucc(String txAcctNo,  String productCode, String busiType,
            String disCode, String appDt, String appTm, Date appStartDtm, Date appEndDtm){
        
        String[] mBusiCodeArr = getBusiCode(busiType);
        QueryDealOrderListRequest queryDealOrderListRequest = new QueryDealOrderListRequest();
        queryDealOrderListRequest.setDisCode(disCode);
        queryDealOrderListRequest.setProductCode(productCode);
        queryDealOrderListRequest.setmBusiCodeArr(mBusiCodeArr);
        queryDealOrderListRequest.setAppBeginDtm(appStartDtm);
        queryDealOrderListRequest.setAppEndDtm(appEndDtm);
        queryDealOrderListRequest.setTxAcctNo(txAcctNo);
        
        QueryDealOrderListResponse queryDealOrderListResponse = queryDealOrderListFacade.execute(queryDealOrderListRequest);
        if(queryDealOrderListResponse != null && TradeConstant.TMS_TRADE_SUCC_CODE.equals(queryDealOrderListResponse.getReturnCode())){
            queryDealOrderListResponse.getDealOrderList();
        }
        return null;
    }
    
    /**
     * 
     * getAppSuccCouterDeal:(柜台申请成功订单)
     * @param txAcctNo
     * @param productCode
     * @param txCode
     * @param disCode
     * @param appDt
     * @param appTm
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月9日 下午5:44:40
     */
    private List<CounterOrderDto> getAppSuccCouterDeal(String txAcctNo,  String productCode,
            String txCode, String disCode, String appDt, String appTm) throws Exception{
        
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        
        CounterQueryOrderReqDto counterQueryOrderReqDto = new CounterQueryOrderReqDto();
        counterQueryOrderReqDto.setTxAcctNo(txAcctNo);
        counterQueryOrderReqDto.setTxCode(txCode);
        counterQueryOrderReqDto.setFundCode(productCode);
        counterQueryOrderReqDto.setCheckFlag(CounterCheckFlagEnum.NO_CHECKED.getKey());
        
        CounterQueryOrderRespDto counterQueryOrderRespDto =  tmsCounterService.counterQueryOrder(counterQueryOrderReqDto, disInfoDto);
        if(counterQueryOrderRespDto != null){
            List<CounterOrderDto> counterDealList = counterQueryOrderRespDto.getCounterOrderList();
            return counterDealList;
        }
        return null;
    }

    private String[] getBusiCode(String busiType){
        List<String> busiCodeList = new ArrayList<String>();
        
        if(BusiTypeEnum.BUY.getCode().equals(busiType)){
            // 购买
            busiCodeList.add("1120");
            busiCodeList.add("1122");
        }else if( BusiTypeEnum.SELL.getCode().equals(busiType)){
            busiCodeList.add("1124");
        }else if("2".equals(busiType)){
            busiCodeList.add("1129");
        }
        
        if(CollectionUtils.isEmpty(busiCodeList)){
            return null;
        }
        
        String[] busiCodeArr = new String[busiCodeList.size()];
        int i = 0;
        for(String busiCode : busiCodeList){
            busiCodeArr[i++] = busiCode;
        }
        return busiCodeArr;
    }
    
    private String getTxCodeByBusiType(String busiType){
        if(BusiTypeEnum.BUY.getCode().equals(busiType)){
           return TxCodes.HIGH_COUNTER_PURCHASE;
        }else if(BusiTypeEnum.SELL.getCode().equals(busiType)){
           return TxCodes.HIGH_COUNTER_REDEEM;
        }else if("2".equals(busiType)){
            return TxCodes.HIGH_COUNTER_MODIFYDIV;
        }
        
        return null;
    }
}

