$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	ApplyTransferTubeIn.checkOrder = {};	 
	ApplyTransferTubeIn.init(checkId,custNo,disCode,idNo);
});

var ApplyTransferTubeIn = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,ApplyTransferTubeIn.queryCheckOrderByIdBack);
		
		// 废单
		$("#abolishBtn").on('click',function(){
			CounterAbolish.abolish(TmsCounterConfig.CHECK_FUND_CONFIRM_URL, CounterCheck.Abolish, ApplyTransferTubeIn.checkOrder);
		});
	}, 
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		ApplyTransferTubeIn.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(ApplyTransferTubeIn.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(ApplyTransferTubeIn.checkOrder.checkFlag != 3){
			CommonUtil.layer_tip("该订单不处于驳回状态");
			return false;
		}
		QueryFundInfo.queryFundInfo(ApplyTransferTubeIn.checkOrder.fundCode,false);
		/** 录入订单信息*/
		if($("#fundCode").length > 0){
			$("#fundCode").val(ApplyTransferTubeIn.checkOrder.fundCode);
		}
		
		if($("#transferTubeBusiType").length > 0){
			$("#transferTubeBusiType").val(ApplyTransferTubeIn.checkOrder.transferTubeBusiType);
		}
		if($("#originalAppDealNo").length > 0){
			$("#originalAppDealNo").val(ApplyTransferTubeIn.checkOrder.originalAppDealNo);
		}
		
		if($("#tSellerTD").length > 0){
			Init.setTSellerCodeTD(ApplyTransferTubeIn.checkOrder.transferTubeBusiType);
			$("#tSellerCode").val(ApplyTransferTubeIn.checkOrder.tSellerCode);
		}
		if($("#selectBank").length > 0){
			$("#selectBank").val(ApplyTransferTubeIn.checkOrder.cpAcctNo);
		}
		
		if($("#inAppVol").length > 0){
			$("#inAppVol").val(ApplyTransferTubeIn.checkOrder.appVol);
		}
		
		/**other*/
		BodyView.setShowOperInfo(ApplyTransferTubeIn.checkOrder);
		
		// 修改提交
		$("#confimTransInBtn").on('click',function(){
			ApplyTransferTubeIn.confirm("update", ApplyTransferTubeIn.checkOrder.dealAppNo);
		});

	},
	
	/***
	 * 确认转托管转入
	 */	
	confirm : function(action, dealAppNo){
		CommonUtil.disabledBtn("confimTransInBtn");
		if(CommonUtil.isEmpty(dealAppNo)){
			CommonUtil.layer_tip("原始订单不存在不可修改！");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		

		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			$("#inAppVol").val();
			CommonUtil.layer_tip("请输入转入基金代码");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		 /**
	     * 转托管方式,1-一次转托管；2-两次转托管
	     */
		var chgTrusteeMode = QueryFundInfo.fundInfo.chgTrusteeMode;
		if(chgTrusteeMode == "1"){
			CommonUtil.layer_tip("转入基金为一次转托管，不可转托管转入！");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}

		var inAppVol = $("#inAppVol").val() || '';
		if(CommonUtil.isEmpty(inAppVol)){
			CommonUtil.layer_tip("请填写转托管转入份额");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		
		
		var cpAcctNo = $("#selectBank").val();
		var bankAcct = $('#selectBank').find('option:selected').attr('bankacct');
		var bankCode = $('#selectBank').find('option:selected').attr('bankcode');
		if(CommonUtil.isEmpty(cpAcctNo)){
			CommonUtil.layer_tip("请选择银行卡");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		
		var transferTubeBusiType = $("#transferTubeBusiType").val();
		var tSellerCode = $("#tSellerCode").val();
		if(transferTubeBusiType == '2' && tSellerCode == '304'){
			CommonUtil.layer_tip("场外跨销售机构, 对方销售人代码不能输入好买304！");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#transfertubeInForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		
		// 转入录入信息
		var transfertubeInForm = $("#transfertubeInForm").serializeObject();
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		var originalAppDealNo = $("#originalAppDealNo").val();
		var transInVolCmd = {"appVol": CommonUtil.unFormatAmount(inAppVol), "cpAcctNo":cpAcctNo, "bankAcct":bankAcct, "bankCode":bankCode, "originalAppDealNo":originalAppDealNo};

		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		
		transfertubeInForm.appDtm = transfertubeInForm.appDt +'' + transfertubeInForm.appTm;
		if(CommonUtil.isEmpty(transfertubeInForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		if(!Valid.valiadTradeTime(transfertubeInForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimTransInBtn");
			return false;
		}
		
		// 提交
		var uri = TmsCounterConfig.TRANSFERTUBE_IN_FUND_CONFIRM_URL ||  {};
		var reqparamters = {"dealAppNo": CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
				"fundInfoForm": JSON.stringify(QueryFundInfo.fundInfo),
				"custInfoForm": JSON.stringify(QueryCustInfo.custInfo),
				"transInVolCmd": JSON.stringify(transInVolCmd),
				"transfertubeInForm": JSON.stringify(transfertubeInForm),
				"transactorInfoForm": JSON.stringify(transactorInfoForm)
			};
		//console.log(reqparamters);
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ApplyTransferTubeIn.callBack);
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confimTransInBtn");
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.enabledBtn("confimTransInBtn");
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	}
}
