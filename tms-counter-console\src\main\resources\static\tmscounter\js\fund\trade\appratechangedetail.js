

$(function(){
	AppRateChangeDetail.operatorNo = CommonUtil.getParam("operatorNo");
	AppRateChangeDetail.dealAppNo = CommonUtil.getParam("dealAppNo");
	Init.init();
    AppRateChangeDetail.initSelect();
    AppRateChangeDetail.dtlOrderDtoList = [];
    AppRateChangeDetail.counterOrderDto = {};
});

var AppRateChangeDetail = {
		
	initSelect:function(){
		//console.info("AppRateChangeDetail.operatorNo:" + AppRateChangeDetail.operatorNo);
		//console.info("AppRateChangeDetail.dealAppNo:" + AppRateChangeDetail.dealAppNo);
		AppRateChangeDetail.querySubmitAppOrderById(AppRateChangeDetail.dealAppNo);
	},
	
	querySubmitAppOrderById:function(dealAppNo){
		var  uri= TmsCounterConfig.QUERY_SUBMIT_APP_ORDER_TRADE_BY_ID_URL || {};
		var reqparamters = {};
		reqparamters.dealAppNo = AppRateChangeDetail.dealAppNo;
		reqparamters.page = 1;
		reqparamters.pageSize = 3;

		var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
		CommonUtil.ajaxPaging(uri, paramters, AppRateChangeDetail.querySubmitAppOrderByIdBack, "pageView");
	},
	
	
	querySubmitAppOrderByIdBack: function (data) {
        var bodyData = data.body || {};
        AppRateChangeDetail.dtlOrderDtoList = data.dtlOrderDtoList  || [];
        AppRateChangeDetail.counterOrderDto = data.counterOrderDto  || {};
        
        $("#memoId").show();
        //console.info(AppRateChangeDetail.counterOrderDto.memo);
        $("#memoText").val(AppRateChangeDetail.counterOrderDto.memo);
        
        $("#querySubmitAppOrderInfoIdrsList").empty();
		var trHtml = '';
		if(AppRateChangeDetail.dtlOrderDtoList.length <= 0){
			trHtml = '<tr><td colspan="13">无查询记录</td></tr>';
			$("#rsList").append(trHtml);
		}else{
			$(AppRateChangeDetail.dtlOrderDtoList).each(function(index, element){
                var trList = [];
                trList.push(element.submitDealNo);
                trList.push(element.fundCode);
                trList.push(element.fundName);
                trList.push(element.afterDiscountRate);//修改前折扣率
                trList.push(AppRateChangeDetail.counterOrderDto.discountRate);//修改后折扣率
                trList.push(AppRateChangeDetail.counterOrderDto.appDt);//申请日期
                trList.push(AppRateChangeDetail.counterOrderDto.appTm);//申请时间
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_APP_FLAG_MAP, element.appFlag, ''));
                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#querySubmitAppOrderInfoIdrsList").append(trAppendHtml);
			});
		}
        
    }
    

};