/**
 *柜台审核
 *<AUTHOR>
 *@date 2017-04-01 15:23
 */
$(function () {

    // 查询
    queryOwnershipRightTransfer.init();

    //查询待审核订单
    queryOwnershipRightTransfer.queryOrderInfo();

});

var queryOwnershipRightTransfer = {
    init: function () {
        $("#queryBtn").on('click', function () {
            queryOwnershipRightTransfer.queryOrderInfo();
        });
        $("#resetBth").on("click", function () {
            CommonUtil.clearForm("searchCheckForm");
        });

        /**
         * 导出查询结果
         */
        $("#exportBtn").on('click', function (index, element) {
            queryOwnershipRightTransfer.exportOrderList();
        });
        // 确认日期
        $("#beginDtm").val(CommonUtil.getHighWorkDay())
        $("#endDtm").val(CommonUtil.getHighWorkDay())
        // 初始化下拉框
        queryOwnershipRightTransfer.initSelect();
    },

    checkParam: function () {
        // 参数校验
        // 1.基金类型不能为空
        var searchForm = $("#searchCheckForm").serializeObject();
        var fundType = searchForm.fundType;
        if (CommonUtil.isEmpty(fundType)) {
            CommonUtil.layer_tip("基金类型不能为空");
            return false;
        }
        // 2.二级基金类型不能为空
        var fundSubType = searchForm.fundSubType;
        if (CommonUtil.isEmpty(fundSubType)) {
            CommonUtil.layer_tip("二级基金类型不能为空");
            return false;
        }
        // 3.确认起始日期不能为空
        var beginDtm = searchForm.beginDtm;
        if (CommonUtil.isEmpty(beginDtm)) {
            CommonUtil.layer_tip("确认起始日期不能为空");
            return false;
        }
        // 4.确认截止日期不能为空
        var endDtm = searchForm.endDtm;
        if (CommonUtil.isEmpty(endDtm)) {
            CommonUtil.layer_tip("确认截止日期不能为空");
            return false;
        }

    },

    // 导出查询出的订单内容
    exportOrderList: function () {
        var uri = TmsCounterConfig.DOWNLOAD_OWNERSHIP_RIGHT_TRANSFER_URL || {};
        var searchForm = $("#searchCheckForm").serializeObject();
        // 1.入参校验
        queryOwnershipRightTransfer.checkParam();
        // 2.构建查询参数
        var reqparamters = {};
        for (name in searchForm) {
            if (!CommonUtil.isEmpty(searchForm[name])) {
                // 判断如果是mBusiCode多选框
                if (name != "mBusiCode") {
                    reqparamters[name] = searchForm[name];
                }
            }
        }
        if ($("#mBusiCode").length > 0) {
            var mBusiCodes = $("#mBusiCode").val().join(',')
            if (!CommonUtil.isEmpty(mBusiCodes)) {
                reqparamters.mBusiCodes = mBusiCodes;
            }
        }
        window.location.href = uri + "?conditions=" + encodeURIComponent(JSON.stringify(reqparamters), 'utf-8');
    },

    /**
     * 初始化下拉框
     */
    initSelect: function () {
        //初始化审核下拉框,默认 查询未审核订单
        var selectCheckFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.OWNERSHIP_CHECK_FLAG_MAP, '0');
        $("#selectCheckFlag").html(selectCheckFlagHtml);

        var selectFundTypeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.FUNDTYPE_MAP, '11');
        $("#fundType").html(selectFundTypeHtml);

        var selectFundSubTypeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.FUND_SUB_TYPE_MAP, 'C');
        $("#fundSubType").html(selectFundSubTypeHtml);
    },
    /**
     * 构建查询入参
     */
    buildParam: function (uri) {
        var searchForm = $("#searchCheckForm").serializeObject();
        // 1.入参校验
        queryOwnershipRightTransfer.checkParam();
        // 2.构建查询参数
        var reqparamters = {};
        for (name in searchForm) {
            if (!CommonUtil.isEmpty(searchForm[name])) {
                // 判断如果是mBusiCode多选框
                if (name != "mBusiCode") {
                    reqparamters[name] = searchForm[name];
                }
            }
        }
        if ($("#mBusiCode").length > 0) {
            var mBusiCodes = $("#mBusiCode").val().join(',')
            if (!CommonUtil.isEmpty(mBusiCodes)) {
                reqparamters.mBusiCodes = mBusiCodes;
            }
        }
        reqparamters.reqparamters = JSON.stringify(reqparamters);
        // 2.3.参数构建
        return CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
    },

    /**
     * 查询股权份额转让订单
     */
    queryOrderInfo: function () {
        var uri = TmsCounterConfig.QUERY_OWNERSHIP_RIGHT_TRANSFER_URL || {};
        var paramters = queryOwnershipRightTransfer.buildParam(uri);
        // 3.调用后端
        CommonUtil.ajaxAndCallBack(paramters, queryOwnershipRightTransfer.queryOrderInfoCallBack);
    },

    queryOrderInfoCallBack: function (data) {
        queryOwnershipRightTransfer.orderList = data.body.orderList || [];
        $("#rsList").empty();
        if (queryOwnershipRightTransfer.orderList.length <= 0) {
            var trHtml = '<tr class="text-c" ><td colspan="9">暂无维护记录</td></tr>';
            $("#rsList").append(trHtml);
        }

        $(queryOwnershipRightTransfer.orderList).each(function (index, element) {
            var trList = [];
            var modifyBtn = '<a id="modify' + element.dealDtlNo + '" class="modifyBtn btn btn-success radius" href="javascript:void(0);" indexvalue = ' + index + '>修改</a>';
            var forceModifyBtn = '<a id="forceModify' + element.dealDtlNo + '" class="forceModifyBtn btn btn-success radius" href="javascript:void(0);" indexvalue = ' + index + '>强制修改</a>';
            var viewBtn = '<a id="view' + element.dealDtlNo + '" class="viewBtn btn btn-secondary radius ml5" href="javascript:void(0);" indexvalue = ' + index + '>查看</a>';


            var btn = '';
            if ("0" === element.checkFlag) {
                btn = modifyBtn;
            } else if ("2" === element.checkFlag) {
                btn = forceModifyBtn;
            }
            btn += viewBtn;
            trList.push(btn);
            trList.push(CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_RIGHT_TRANSFER_CHECK_FALG_MAP, element.checkFlag, '--'));
            trList.push(CommonUtil.formatData(element.custNo));
            trList.push(CommonUtil.formatData(element.custName));
            trList.push(CommonUtil.getMapValue(CONSTANTS.OWNERSHIP_TX_CODES_MAP, element.mBusinessCode, ''));
            trList.push(CommonUtil.formatData(element.fundCode));
            trList.push(CommonUtil.formatData(element.fundName));
            trList.push(CommonUtil.formatData(element.fundType));
            trList.push(CommonUtil.formatAmount(element.fundSubType));
            // 转让价格
            trList.push(CommonUtil.formatAmount(element.transferPrice, '--'));
            trList.push(CommonUtil.formatAmount(element.ackVol));
            trList.push(CommonUtil.formatData(element.ackAmt));
            trList.push(CommonUtil.formatData(element.ackDtm));
            trList.push(CommonUtil.getMapValue(CONSTANTS.FUND_UNUSUAL_TRANS_TYPE_MAP, element.isNoTradeTransfer));

            trList.push(CommonUtil.formatDateToStr(element.updateDtm, "yyyy-MM-dd hh:mm:ss"));
            trList.push(CommonUtil.formatData(element.modifier));
            trList.push(CommonUtil.formatDateToStr(element.modifyDtm, "yyyy-MM-dd hh:mm:ss"));
            trList.push(CommonUtil.formatData(element.checker));

            var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
            $("#rsList").append(trHtml);
            $("#modify" + element.dealDtlNo).attr("dealDtlNo", element.dealDtlNo);
            $("#forceModify" + element.dealDtlNo).attr("dealDtlNo", element.dealDtlNo);
            $("#view" + element.dealDtlNo).attr("dealDtlNo", element.dealDtlNo);
        });

        function ownershipRightTransferUrl(params) {
            return "../trade/ownershiprighttransfer.html" + "?" + params;
            ;
        }

        //viewType 0-查看；1-审核；2-修改
        //修改
        $(".modifyBtn").off();
        $(".modifyBtn").on('click', function () {
            var dealDtlNo = $(this).attr("dealDtlNo");
            var params = [];
            params.push('dealDtlNo=' + dealDtlNo);
            params.push('viewType=2');
            var urlParams = ViewDealCommon.buildParams(params);
            var viewUrl = ownershipRightTransferUrl(urlParams);
            ViewDealCommon.showDeal(viewUrl);
        });

        //强制修改
        $(".forceModifyBtn").off();
        $(".forceModifyBtn").on('click', function () {
            var dealDtlNo = $(this).attr("dealDtlNo");
            var params = [];
            params.push('dealDtlNo=' + dealDtlNo);
            params.push('viewType=2');
            var urlParams = ViewDealCommon.buildParams(params);
            var viewUrl = ownershipRightTransferUrl(urlParams);
            ViewDealCommon.showDeal(viewUrl);
        });

        //查看
        $(".viewBtn").off();
        $(".viewBtn").on('click', function () {
            var dealDtlNo = $(this).attr("dealDtlNo");
            var params = [];
            params.push('dealDtlNo=' + dealDtlNo);
            params.push('viewType=0');
            var urlParams = ViewDealCommon.buildParams(params);
            var viewUrl = ownershipRightTransferUrl(urlParams);
            console.log("viewUrl:" + viewUrl);
            ViewDealCommon.showDeal(viewUrl);

        });

    },

    /**
     * 是否已审核
     * @param checkFlag
     *  0-待审核
     *  1-审核通过
     *  2-审核失败
     *  3-审核驳回
     *  4-作废
     */
    isChecked: function (checkFlag) {
        if ('0' == checkFlag) {
            return false;
        }
        return true;
    }
};
