/**
*修改复购协议审核查询页面
*
**/

/**
 * 初始化
 */
$(function(){
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
	if('crm' == CommonUtil.getParam("source")){
		viewType = '1';
	}

	CounterCheck.viewType = viewType;

	CounterCheck.initBtn(viewType);
	
	var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
	$(".selectconsCode").html(selectConsCodesHtml);

	// 查询订单
	CounterCheck.queryCounterDealOrder(viewType, ModifyRepurchaseCheck.queryCounterDealOrderCallBack, null);

});

var ModifyRepurchaseCheck = {
	queryCounterDealOrderCallBack:function(data){
		var bodyData = data.body || {};
		CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};
		var counterOrder = bodyData.counterOrder || {};//订单信息
        var repurchaseInfo = counterOrder.counterRepurChaseInfoBean || {};// 复购信息
		
		ModifyRepurchaseCheck.buildDealInfo(CounterCheck.counterOrderDto);// 订单信息
        ModifyRepurchaseCheck.buildRepurchaseInfo(repurchaseInfo);// 复购信息
	},
	
	/**
     * 订单信息
     * @param checkOrder
     */
    buildDealInfo:function(checkOrder){
    	$("#productCode").val(checkOrder.fundCode);//基金代码
        $("#taCode").html(checkOrder.taCode);//基金代码
        $("#fundName").html(checkOrder.fundName);//基金代码
        $("#idNo").html(checkOrder.idNo);// 证件号
    	$("#custName").html(checkOrder.custName);// 客户姓名
        $("#txAcctNo").val(checkOrder.txAcctNo);//客户号
    	$("#appVol").val(CommonUtil.formatAmount(checkOrder.appVol));//复购份额
        $("#repurchaseType").val(checkOrder.repurchaseType);//复购类型
    },

    /**
     * 复购信息
     * @param checkOrder
     */
    buildRepurchaseInfo:function(repurchaseInfo){
        $("#balanceVol").html(CommonUtil.formatAmount(repurchaseInfo.balanceVol));// 持有份额
        $("#redeemVol").html(CommonUtil.formatAmount(repurchaseInfo.redeemVol));// 赎回份额
        $("#frznVol").html(CommonUtil.formatAmount(repurchaseInfo.frznVol));//
        $("#expectedDueDt").html(repurchaseInfo.expectedDueDt);// 预计到期日
    }

};
