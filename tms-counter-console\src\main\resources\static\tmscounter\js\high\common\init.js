$(function(){
	Init.init();
});

var Init = {
		init:function(){
			/**
			 * 分销机构列表
			 */
			DisCode.getDisCodes();
			
			/**
			 * 投顾列表
			 */
//			if($(".selectconsCode").length > 0){
//				var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
//			}
			
			
			/**
			 * 证件类型
			 */
			if($(".selectTransactorIdType").length > 0){
				var selectIdTypeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.ID_TYPE_MAP);
				$(".selectTransactorIdType").html(selectIdTypeHtml);
			}
			
			
			/**
			 * 分销机构
			 */
			if($("#selectDisCode").length > 0){
				var selectDiscodeHtm = CommonUtil.selectOptionsHtml(DisCode.disCodesMap,CONSTANTS.HB_DISCODE);
				$("#selectDisCode").html(selectDiscodeHtm);
			}

			/**
			 * 分销机构多选框
			 */
			if($("#multiSelectDisCode").length > 0){

				var optionList = [];
				$.each(DisCode.disCodesMap,function(key,value){
					if(!isEmpty(key) && !isEmpty(value)){
						var optionValue = key || '';
						var optionDesc = key + '-' + value || '';
						optionList.push('<option value="'+optionValue+'">'+optionDesc+'</option>');
					}
				});

				var optionHtml = optionList.join('');
				$("#multiSelectDisCode").html(optionHtml);

				// 初始化多选框插件
				$("#multiSelectDisCode").multiselect({
					checkAllText: '全选',
					uncheckAllText: '取消',
					noneSelectedText:"请选择"
				}).multiselectfilter({label:"过滤",
					placeholder:"代码或简称"});

				var initDisCodes =["HB000A001","HZ000N001"];
				$('#multiSelectDisCode option').each(function(){
					if(initDisCodes.indexOf(this.value)!=-1){
						this.selected=true;
					}
				});
				$("#multiSelectDisCode").multiselect('refresh');
			}
			
			
			if($("#selectTxCode").length>0){
				var selectTxCodeHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_TXCODE_MAP,function(name,value){
					selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectTxCode").empty();
				$("#selectTxCode").html(selectTxCodeHtml);
			}
			
			if($("#selectCheckFlag").length > 0){
				var selectCheckFlagHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP,function(name,value){
					selectCheckFlagHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectCheckFlag").empty();
				$("#selectCheckFlag").html(selectCheckFlagHtml);
			}
			
			/**
			 * 获取工作日
			 */	
			if($("#appDt").length > 0){
				CommonUtil.getHighWorkDay();
			}
			
			/**
			 * 初始化异常赎回
			 */
			if($(".selectUnusualTransType").length > 0){
				var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.FUND_UNUSUAL_TRANS_TYPE_MAP,'0');
				$(".selectUnusualTransType").html(selectTransTypeHtml);
			}
			
		}	
};

var DisCode = {
		
};

DisCode.disCodes =[];
DisCode.disCodesMap ={};

DisCode.getDisCodes=function(){
	var uri = TmsCounterConfig.QUERY_DIS_CODE_INFO_URL;
	var reqparamters = {};
	var paramters = CommonUtil.buildReqParams(uri,reqparamters);
	CommonUtil.ajaxAndCallBack(paramters, function(data){
		var respCode = data.code || '';
		var body = data.body || {};
		if(CommonUtil.isSucc(respCode)){
			DisCode.disCodes = body.disCodes || [];
			$(DisCode.disCodes).each(function(index,element){
				DisCode.disCodesMap[element.disCode] = element.disName;
			});
		}		
	});
};