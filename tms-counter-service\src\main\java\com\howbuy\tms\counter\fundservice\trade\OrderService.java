/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundservice.trade;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.interlayer.product.enums.FundSubTypeEnum;
import com.howbuy.interlayer.product.enums.FundTypeEnum;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionFacade;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionRequest;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.ModifyRedeemDirectionResponse;
import com.howbuy.tms.batch.facade.trade.modifyredeemdirection.bean.RedeemDirectionBean;
import com.howbuy.tms.batch.facade.trade.submitcheckorder.SubmitCheckOrderFacade;
import com.howbuy.tms.batch.facade.trade.submitcheckorder.SubmitCheckOrderRequest;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.ForceRedeemFlagEnum;
import com.howbuy.tms.common.enums.busi.RiskRevealBookFlagEnum;
import com.howbuy.tms.common.enums.busi.TxPasswordValidateFlagEnum;
import com.howbuy.tms.common.enums.database.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.database.CounterCheckFlagEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.enums.database.ZBusiCodeEnum;
import com.howbuy.tms.common.enums.database.ZDtlBusiCodeEnum;
import com.howbuy.tms.common.outerservice.fund.infoackfile.InfoAckFileNameBean;
import com.howbuy.tms.common.outerservice.fund.infoackfile.QueryInfoAckFileOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.QueryFundInfoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.common.outerservice.lctonline.xferagentout.XferAgentOutContext;
import com.howbuy.tms.common.outerservice.lctonline.xferagentout.XferAgentOutResult;
import com.howbuy.tms.common.outerservice.lctonline.xferagentout.XferAgentOutService;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.counter.enums.RedeemCapitalFlagEnum;
import com.howbuy.tms.counter.utils.TmsFacadeUtil;
import com.howbuy.tms.counter.common.BooleanFlagReverseEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.CounterPortfolioProductDto;
import com.howbuy.tms.counter.dto.SubmitUncheckOrderDtlDto;
import com.howbuy.tms.counter.dto.SubmitUncheckOrderDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.CancelOrderFacade;
import com.howbuy.tms.orders.facade.trade.fund.cancelorder.CancelOrderRequest;
import com.howbuy.tms.orders.facade.trade.fund.exchange.exchangecounter.ExchangeCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.exchange.exchangecounter.ExchangeCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.exchange.exchangecounter.ExchangeCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivcounter.ModifyDivCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivcounter.ModifyDivCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.modifydiv.modifydivcounter.ModifyDivCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.redeem.redeemcounter.RedeemCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.redeem.redeemcounter.RedeemCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.redeem.redeemcounter.RedeemCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.BaseShareMergeRequest.ShareMergeOutDetail;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergecounter.ShareMergeCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergecounter.ShareMergeCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.sharemerge.sharemergecounter.ShareMergeCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.sharetransfer.sharetransfercounter.ShareTransferCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurcounter.SubsOrPurCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurcounter.SubsOrPurCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.subsorpur.subsorpurcounter.SubsOrPurCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.transfertubein.counter.TransManageInCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.transfertubein.counter.TransManageInCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.transfertubein.counter.TransManageInCounterResponse;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.BaseTransManageOutRequest.OutDetail;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.counter.TransManageOutCounterFacade;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.counter.TransManageOutCounterRequest;
import com.howbuy.tms.orders.facade.trade.fund.transfertubeout.counter.TransManageOutCounterResponse;
import com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.CounterPurchaseAdviserFacadeFacade;
import com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.CounterPurchaseAdviserRequest;
import com.howbuy.tms.robot.orders.facade.trade.adviser.purchase.PurchasePortfolioAdviserResponse;
import com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.CounterRedeemAdviserFacade;
import com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.CounterRedeemAdviserRequest;
import com.howbuy.tms.robot.orders.facade.trade.adviser.redeem.RedeemPortfolioAdviserResponse;
import com.howbuy.tms.robot.orders.facade.trade.redeemportfolio.CounterRedeemPortfolioFacade;
import com.howbuy.tms.robot.orders.facade.trade.redeemportfolio.RedeemPortfolioRequest;
import com.howbuy.tms.robot.orders.facade.trade.redeemportfolio.RedeemPortfolioResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:(零售公募订单服务类)
 * <AUTHOR>
 * @date 2017年9月18日 下午5:24:49
 * @since JDK 1.6
 */
@Service
public class OrderService {

    private static final Logger logger = LogManager.getLogger(OrderService.class);

    @Autowired
    private SubsOrPurCounterFacade subsOrPurCounterFacade;

    @Autowired
    private RedeemCounterFacade redeemCounterFacade;

    @Autowired
    private CounterRedeemAdviserFacade counterRedeemAdviserFacade;
    @Autowired
    private CancelOrderFacade cancelOrderFacade;

    @Autowired
    private ModifyDivCounterFacade modifyDivCounterFacade;

    @Autowired
    private SubmitCheckOrderFacade submitCheckOrderFacade;

    @Autowired
    private ExchangeCounterFacade exchangeCounterFacade;
    
    @Autowired
    private ShareMergeCounterFacade fundShareMergeCounterFacade;
    
    @Autowired
    private ShareTransferCounterFacade fundShareTransferCounterFacade;
    
    @Autowired
    private QueryFundInfoOuterService queryFundInfoOuterService;
    
    @Autowired
    private TransManageInCounterFacade transManageInCounterFacade;
    
    @Autowired
    private TransManageOutCounterFacade transManageOutCounterFacade;

    @Autowired
    private QueryInfoAckFileOuterService queryInfoAckFileOuterService;

    @Autowired
    private ModifyRedeemDirectionFacade modifyRedeemDirectionFacade;

    @Autowired
    private CounterPurchaseAdviserFacadeFacade counterPurchaseAdviserFacadeFacade;

    @Autowired
    private CancelOrderService cancelOrderService;

    @Autowired
    private CounterRedeemPortfolioFacade counterRedeemPortfolioFacade;


    /**
     * 
     * subsOrPurCounter:(柜台审核购买)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:28:28
     */
    public boolean subsOrPurCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        SubsOrPurCounterRequest request = new SubsOrPurCounterRequest();
        if (submitUncheckOrderDto != null) {
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            request.setProtocolType(submitUncheckOrderDto.getProtocolType());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            // 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
            request.setPaymentType(submitUncheckOrderDto.getPaymentType());
            // 申请金额
            request.setAppAmt(submitUncheckOrderDto.getAppAmt());
            // 风险确认标记：1-确认，0-未确认
            request.setRiskFlag(submitUncheckOrderDto.getRiskFlag());
            // 基金代码
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            // 份额类型：A-前收费；B-后收费
            request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            // 协议号
            request.setProtocolNo(null);
            request.setDiscount(submitUncheckOrderDto.getDiscountRate());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            
            String tradeDt = new StringBuilder(request.getAppDt()).append(request.getAppTm()).toString();
            if(checkFundType(request.getFundCode(), tradeDt)){
                // 柜台默认风险揭示书1-确认
                request.setRiskRevealBookAckFlag(RiskRevealBookFlagEnum.CONFIRM.getCode());
                request.setRiskRevealBookAckDtm(tradeDt);
            }

            InfoAckFileNameBean bean = queryInfoAckFileOuterService.getInfoAckFileNameByFundCode(submitUncheckOrderDto.getFundCode());
            if(bean != null && !StringUtil.isEmpty(bean.getFileName())){
                request.setInfoAckFileName(bean.getFileName());
            }
        }
        logger.debug("Fund | subsOrPurCounter request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(subsOrPurCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                SubsOrPurCounterResponse subsOrPurCounterResponse = (SubsOrPurCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(subsOrPurCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }


    public boolean adviserPurCounter(SubmitUncheckOrderDto submitUncheckOrderDto, CounterPortfolioProductDto productDto, DisInfoDto disInfoDto) throws Exception {
        CounterPurchaseAdviserRequest request = new CounterPurchaseAdviserRequest();
        if (submitUncheckOrderDto != null) {
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            request.setProtocolType(submitUncheckOrderDto.getProtocolType());
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            // 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
            request.setPaymentType(submitUncheckOrderDto.getPaymentType());
            // 申请金额
            request.setAppAmt(submitUncheckOrderDto.getAppAmt());
            // 风险确认标记：1-确认，0-未确认
            request.setRiskFlag(submitUncheckOrderDto.getRiskFlag());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            // 协议号
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            request.setzBusiCode(ZBusiCodeEnum.BUY.getCode());
            request.setZdtlBusiCode(ZDtlBusiCodeEnum.PURCHASE.getMCode());
            request.setProductCode(submitUncheckOrderDto.getFundCode());
            request.setTxPasswordValidateFlag(TxPasswordValidateFlagEnum.NOT_REQUIRED_VALIDATE.getCode());
            request.setPartnerCode(productDto.getPartnerCode());
            if (StringUtil.isNotBlank(submitUncheckOrderDto.getSurveyAnswer())) {
                String surveyAnswer = String.join(",", submitUncheckOrderDto.getSurveyAnswer().split(""));
                request.setSurveyAnswer(surveyAnswer);
            }

        }
        logger.debug("Fund | subsOrPurCounter request:{}", JSON.toJSONString(request));

        BaseResponse baseResp = TmsFacadeUtil.execute(counterPurchaseAdviserFacadeFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                PurchasePortfolioAdviserResponse subsOrPurCounterResponse = (PurchasePortfolioAdviserResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(subsOrPurCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }
    

    /**
     * 
     * redeemCounter:(柜台基金审核赎回)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:28:18
     */
    public boolean redeemCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        RedeemCounterResponse resp = null;
        RedeemCounterRequest request = new RedeemCounterRequest();
        if (submitUncheckOrderDto != null) {
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            request.setProtocolType(submitUncheckOrderDto.getProtocolType());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            // 申请份额
            request.setAppVol(submitUncheckOrderDto.getAppVol());
            // 基金代码
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            // 份额类型：A-前收费；B-后收费
            request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            request.setLargeRedeemFlag(submitUncheckOrderDto.getLargeRedmFlag());
            //request.setRedeemCapitalFlag("0");// 赎回资金去向：0-赎回到银行卡；1-赎回到储蓄罐；默认0
            // 20180227 update 柜台赎回资金去向：0-储蓄罐（人工选择）；1-回银行卡（人工选择）；2-回银行卡（协议默认）；3-回储蓄罐（协议默认）；
            request.setRedeemCapitalFlag(submitUncheckOrderDto.getRedeemCapitalFlag());
            request.setAllRedeemFlag(submitUncheckOrderDto.getAllRedeemFlag());
            // 协议号
            request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());
            request.setUnusualTransType(submitUncheckOrderDto.getUnusualTransType());
            // 可赎回日期
            request.setAllowRedeemDt(submitUncheckOrderDto.getAllowDt());
            request.setAdvanceStatus(submitUncheckOrderDto.getAdvanceStatus());
        }
        logger.debug("Fund | redeemCounter request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(redeemCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                resp = (RedeemCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo((resp.getDealNo()));
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }

        }
        return true;
    }


    public boolean redeemCounterAdviser(SubmitUncheckOrderDto submitUncheckOrderDto, CounterPortfolioProductDto productDto, DisInfoDto disInfoDto) throws Exception {
        CounterRedeemAdviserRequest request = new CounterRedeemAdviserRequest();
        if (submitUncheckOrderDto != null) {
            request.setProductCode(submitUncheckOrderDto.getFundCode());
            request.setPartnerCode(productDto.getPartnerCode());
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            // 申请赎回比例
            request.setAppRatio(MathUtils.bigDecimalToString(submitUncheckOrderDto.getAppRatio()));
            request.setRedeemDirection(submitUncheckOrderDto.getRedeemCapitalFlag());
            // 协议号
            request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());
//            request.setAllRedeemFlag(submitUncheckOrderDto.getAllRedeemFlag());
//            request.setLargeRedeemFlag(submitUncheckOrderDto.getLargeRedmFlag());
//            request.setUnusualTransType(submitUncheckOrderDto.getUnusualTransType());
            request.setAdvanceStatus(submitUncheckOrderDto.getAdvanceStatus());
            request.setTxPasswordValidateFlag(YesOrNoEnum.YES.getCode());
        }
        logger.debug("adviser | redeemCounterAdviser request:{}", JSON.toJSONString(request));

        BaseResponse baseResp = TmsFacadeUtil.execute(counterRedeemAdviserFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                RedeemPortfolioAdviserResponse resp = (RedeemPortfolioAdviserResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo((resp.getDealNo()));
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }

        }
        return true;
    }
    /**
     * 
     * exchangeCounter:(柜台基金转换)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:45:20
     */
    public boolean exchangeCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        ExchangeCounterRequest request = new ExchangeCounterRequest();
        if (submitUncheckOrderDto != null) {
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            request.setProtocolType(submitUncheckOrderDto.getProtocolType());
            request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 资金账号
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            // 申请份额
            request.setAppVol(submitUncheckOrderDto.getAppVol());
            request.setOutFundCode(submitUncheckOrderDto.getFundCode());
            request.setOutShareClass(submitUncheckOrderDto.getFundShareClass());
            request.setInFundCode(submitUncheckOrderDto.gettFundCode());
            request.setInShareClass(submitUncheckOrderDto.gettFundShareClass());
            request.setRiskFlag(submitUncheckOrderDto.getRiskFlag());
            request.setAllowRedeemDt(submitUncheckOrderDto.getAllowDt());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            
            String tradeDt = new StringBuilder(request.getAppDt()).append(request.getAppTm()).toString();
            if(checkFundType(request.getInFundCode(), tradeDt)){
                // 柜台默认风险揭示书1-确认
                request.setRiskRevealBookAckFlag(RiskRevealBookFlagEnum.CONFIRM.getCode());
                request.setRiskRevealBookAckDtm(tradeDt);
            }
            
            if(!StringUtil.isEmpty(submitUncheckOrderDto.gettFundCode())){
            	InfoAckFileNameBean bean = queryInfoAckFileOuterService.getInfoAckFileNameByFundCode(submitUncheckOrderDto.gettFundCode());
                if(bean != null && !StringUtil.isEmpty(bean.getFileName())){
                    request.setInfoAckFileName(bean.getFileName());
                }
            }
            
        }
        logger.debug("Fund | exchangeCounter request:{}", JSON.toJSONString(request));
        
        ExchangeCounterResponse baseResp = (ExchangeCounterResponse) TmsFacadeUtil.execute(exchangeCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo((baseResp.getDealNo()));
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    /**
     * 
     * cancelOrder:(柜台撤单)
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @param cancelFlag
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年7月18日 上午10:55:39
     */
    public boolean cancelOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String cancelFlag) throws Exception {
        BaseResponse baseResp = null;

        String protocolType = submitUncheckOrderDto.getProtocolType();
        String zBusiCode = submitUncheckOrderDto.getzBusiCode();
        if (ProtocolTypeEnum.DEFAULT_FUND.getCode().equals(protocolType) || ProtocolTypeEnum.CYCLE_LOCK_FUND.getCode().equals(protocolType)) {
            if (ZBusiCodeEnum.BATCH_BUY.getCode().equals(zBusiCode)) {
                baseResp = cancelOrderService.cancelBatchFundOrder(submitUncheckOrderDto, disInfoDto, cancelFlag);
            }else {
                baseResp = cancelOrderService.cancelFundOrder(submitUncheckOrderDto, disInfoDto, cancelFlag);
            }
        }else {
            baseResp = cancelOrderService.cancelPortfolioOrder(submitUncheckOrderDto, disInfoDto, cancelFlag);
        }

        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }



    /**
     * 
     * modifyDivCounter:(柜台修改分红方式)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:28:06
     */
    public boolean modifyDivCounter(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        ModifyDivCounterRequest request = new ModifyDivCounterRequest();
        if (submitUncheckOrderDto != null) {
            request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
            request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
            request.setTransactorName(submitUncheckOrderDto.getTransactorName());
            request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
            request.setConsCode(submitUncheckOrderDto.getConsCode());
            request.setOutletCode(submitUncheckOrderDto.getOutletCode());
            request.setDisCode(submitUncheckOrderDto.getDisCode());
            // 外部订单号
            request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
            request.setAppDt(submitUncheckOrderDto.getAppDt());
            request.setAppTm(submitUncheckOrderDto.getAppTm());
            // 基金代码
            request.setFundCode(submitUncheckOrderDto.getFundCode());
            // request
            request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
            // 目标基金分红方式
            request.setDivMode(submitUncheckOrderDto.getFundDivMode());
            // 交易账号
            request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
            request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
            request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());

        }
        logger.debug("Fund | modifyDivCounter request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.executeThrowException(modifyDivCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                ModifyDivCounterResponse modifyDivCounterResponse = (ModifyDivCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(modifyDivCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    /**
     * 
     * checkCounterOrder:(更新订单审核状态)
     * 
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:30:17
     */
    public boolean checkCounterOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto, String checkFlag) throws Exception {
        logger.info("submitUncheckOrderDto:{}", JSON.toJSONString(submitUncheckOrderDto));
        SubmitCheckOrderRequest request = new SubmitCheckOrderRequest();
        request.setDealNo(submitUncheckOrderDto.getDealNo());
        request.setCheckFlag(checkFlag);
        request.setOrderReturnCode(submitUncheckOrderDto.getReturnCode());
        request.setOrderReturnMsg(submitUncheckOrderDto.getDescription());
        request.setDealAppNo(submitUncheckOrderDto.getDealAppNo());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setMemo(submitUncheckOrderDto.getMemo());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setChecker(submitUncheckOrderDto.getChecker());
        request.setCheckDtm(submitUncheckOrderDto.getCheckDtm());
        request.setBeforeModifyDirection(submitUncheckOrderDto.getBeforeModifyDirection());
        request.setOrderedDealNos(submitUncheckOrderDto.getOrderedDealNos());
        logger.debug("Fund | checkCounterOrder request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(submitCheckOrderFacade, request, disInfoDto);
        if (TmsFacadeUtil.isSuccess(baseResp)) {
            return true;
        }else{
            if(baseResp != null){
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            } else{
                return false; 
            }
        }
    }

    /**
     * 
     * shareMergeVolCounter:(柜台审核份额合并落单)
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月10日 下午3:15:59
     */
    public boolean checkCounterMergeVolOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {
        
        if(submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)){
            return false;
        }
        
        ShareMergeCounterRequest request = new ShareMergeCounterRequest();
        request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
        request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
        request.setTransactorName(submitUncheckOrderDto.getTransactorName());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setConsCode(submitUncheckOrderDto.getConsCode());
        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        request.setInCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        request.setInProtocolNo(submitUncheckOrderDto.getProtocolNo());
        request.setInProtocolType(submitUncheckOrderDto.getProtocolType());
        request.setFundCode(submitUncheckOrderDto.getFundCode());
        request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
        
        List<ShareMergeOutDetail> shareMergeOutDetail = new ArrayList<ShareMergeOutDetail>();
        if(CollectionUtils.isNotEmpty(dtlOrderDtoList)){
            ShareMergeOutDetail outDetail = null;
            for(SubmitUncheckOrderDtlDto dtlDto : dtlOrderDtoList){
                outDetail = new ShareMergeOutDetail();
                outDetail.setCpAcctNo(dtlDto.getCpAcctNo());
                outDetail.setBankAcct(dtlDto.getBankAcct());
                outDetail.setBankCode(dtlDto.getBankCode());
                outDetail.setAppVol(dtlDto.getAppVol());
                outDetail.setProtocolNo(dtlDto.getProtocolNo());
                outDetail.setProtocolType(dtlDto.getProtocolType());
                
                shareMergeOutDetail.add(outDetail);
            }
        }
        request.setShareMergeOutDetail(shareMergeOutDetail);
        logger.debug("Fund | checkCounterMergeVolOrder request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(fundShareMergeCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                ShareMergeCounterResponse shareMergeCounterResponse = (ShareMergeCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(shareMergeCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }
    
    /**
     * 
     * shareTransferVolCounter:(柜台审核份额迁移落单)
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年5月10日 下午3:15:59
     */
    public boolean checkCounterTransVolOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {
        
        if(submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)){
            return false;
        }

        ShareTransferCounterRequest request = new ShareTransferCounterRequest();
        request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
        request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
        request.setTransactorName(submitUncheckOrderDto.getTransactorName());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setConsCode(submitUncheckOrderDto.getConsCode());
        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        
        request.setInCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        //份额迁移，转出卡
        List<String> outCpAcctNos = getOutCpAcctNo(dtlOrderDtoList);
        request.setOutCpAcctNos(outCpAcctNos);
        logger.info("Fund | checkCounterTransVolOrder request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(fundShareTransferCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                ShareTransferCounterResponse shareMergeCounterResponse = (ShareTransferCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(shareMergeCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }
    
    /**
     * 资金账号列表
     * getOutCpAcctNo:资金账号列表
     * @param outDtoList
     * @return
     * <AUTHOR>
     * @date 2022年4月17日 下午12:01:21
     */
    private List<String> getOutCpAcctNo(List<SubmitUncheckOrderDtlDto> outDtoList) {
        List<String> outCpAcctNos = new ArrayList<String>();
        if(CollectionUtils.isEmpty(outDtoList)) {
            return outCpAcctNos;
        }
        for(SubmitUncheckOrderDtlDto dto : outDtoList) {
            outCpAcctNos.add(dto.getCpAcctNo());
        }
        return outCpAcctNos;
    }
    
    /**
     * 
     * checkFundType: 校验基金类型
     * @param fundCode
     * @param tradeDt
     * @return
     * <AUTHOR>
     * @date 2018年7月17日 下午2:19:06
     */
    private boolean checkFundType(String fundCode, String tradeDt){
        // 查询产品信息
        FundInfoAndNavBean fundInfo = queryFundInfoOuterService.getFundInfoAndNav(fundCode, tradeDt);
        
        // 养老FOF基金
        if(FundTypeEnum.FOF.getCode().equals(fundInfo.getFundType()) 
                && (FundSubTypeEnum.PENSION_TARGET_RISK.getCode().equals(fundInfo.getFundSubType())  
                        || FundSubTypeEnum.PENSION_TARGET_DATE.getCode().equals(fundInfo.getFundSubType()))){
            return true;
        }
        return false;
    }
    
    /**
     * 
     * checkCounterTransferTubeInOrder:(柜台审核转托管转入落单)
     * @param submitUncheckOrderDto
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月11日 上午11:28:09
     */
    public boolean checkCounterTransferTubeInOrder(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {
        
        if(submitUncheckOrderDto == null){
            return false;
        }
        
        TransManageInCounterRequest request = new TransManageInCounterRequest();
        request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
        request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
        request.setTransactorName(submitUncheckOrderDto.getTransactorName());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setConsCode(submitUncheckOrderDto.getConsCode());
        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        //request.setInProtocolNo(submitUncheckOrderDto.getProtocolNo());
        //request.setInProtocolType(submitUncheckOrderDto.getProtocolType());
        request.setFundCode(submitUncheckOrderDto.getFundCode());
        request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
        request.setOriginalAppDealNo(submitUncheckOrderDto.getOriginalAppDealNo());
        request.setTransferTubeBusiType(submitUncheckOrderDto.getTransferTubeBusiType());
        request.setTSellerCode(submitUncheckOrderDto.gettSellerCode());
        request.setInCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        request.setBankAcct(submitUncheckOrderDto.getBankAcct());
        request.setBankCode(submitUncheckOrderDto.getBankCode());
        request.setAppVol(submitUncheckOrderDto.getAppVol());
        logger.debug("Fund | checkCounterTransferTubeInOrder request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(transManageInCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                TransManageInCounterResponse transManageInCounterResponse = (TransManageInCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(transManageInCounterResponse.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }
    
    /**
     * 
     * checkCounterTransferTubeOutOrder:(柜台审核转托管转出落单)
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月11日 下午1:32:50
     */
    public boolean checkCounterTransferTubeOutOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {
        
        if(submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)){
            return false;
        }
        
        TransManageOutCounterRequest request = new TransManageOutCounterRequest();
        request.setTransactorIdNo(submitUncheckOrderDto.getTransactorIdNo());
        request.setTransactorIdType(submitUncheckOrderDto.getTransactorIdType());
        request.setTransactorName(submitUncheckOrderDto.getTransactorName());
        request.setOperatorNo(submitUncheckOrderDto.getOperatorNo());
        request.setConsCode(submitUncheckOrderDto.getConsCode());
        request.setOutletCode(submitUncheckOrderDto.getOutletCode());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setExternalDealNo(submitUncheckOrderDto.getDealAppNo());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        request.setFundCode(submitUncheckOrderDto.getFundCode());
        request.setFundShareClass(submitUncheckOrderDto.getFundShareClass());
        request.setTransferTubeBusiType(submitUncheckOrderDto.getTransferTubeBusiType());
        request.setTSellerCode(submitUncheckOrderDto.gettSellerCode());
        request.setTOutletCode(submitUncheckOrderDto.gettOutletCode());
        request.setTSellerTxAcctNo(submitUncheckOrderDto.gettSellerTxAcctNo());
        
        List<OutDetail> outDetailList = new ArrayList<OutDetail>();
        if(CollectionUtils.isNotEmpty(dtlOrderDtoList)){
            OutDetail outDetail = null;
            for(SubmitUncheckOrderDtlDto dtlDto : dtlOrderDtoList){
                outDetail = new OutDetail();
                outDetail.setExternalDealDtlNo(dtlDto.getDealDtlAppNo());
                outDetail.setCpAcctNo(dtlDto.getCpAcctNo());
                outDetail.setBankAcct(dtlDto.getBankAcct());
                outDetail.setBankCode(dtlDto.getBankCode());
                outDetail.setAppVol(dtlDto.getAppVol());
                outDetail.setProtocolNo(dtlDto.getProtocolNo());
                outDetail.setProtocolType(dtlDto.getProtocolType());
                
                outDetailList.add(outDetail);
            }
        }
        request.setOutDetailList(outDetailList);
        logger.debug("Fund | checkCounterTransferTubeOutOrder request:{}", JSON.toJSONString(request));
        
        BaseResponse baseResp = TmsFacadeUtil.execute(transManageOutCounterFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                TransManageOutCounterResponse transManageOutCounterResponse = (TransManageOutCounterResponse) baseResp;
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(transManageOutCounterResponse.getDealNo());
                submitUncheckOrderDto.setOrderedDealNos(transManageOutCounterResponse.getOrderedDealNos());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

    @Autowired
    private XferAgentOutService xferAgentOutService;
    /**
     *
     * checkCounterTransferTubeOutOrder:(柜台审核转托管转出落单)
     * @param submitUncheckOrderDto
     * @param dtlOrderDtoList
     * @param disInfoDto
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年10月11日 下午1:32:50
     */
    public boolean lctCheckCounterTransferTubeOutOrder(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {

        if(submitUncheckOrderDto == null || CollectionUtils.isEmpty(dtlOrderDtoList)){
            return false;
        }
        XferAgentOutContext xferAgentOutContext = new XferAgentOutContext();

        List<XferAgentOutContext.XferAgentDtl> appDtls = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(dtlOrderDtoList)){
            XferAgentOutContext.XferAgentDtl outDetail = null;
            for(SubmitUncheckOrderDtlDto dtlDto : dtlOrderDtoList){
                outDetail = new XferAgentOutContext.XferAgentDtl();
                outDetail.setCpAcctNo(dtlDto.getCpAcctNo());
                outDetail.setTfundTxAcctNo(submitUncheckOrderDto.gettSellerTxAcctNo());
                outDetail.setToutletCode(submitUncheckOrderDto.gettOutletCode());
                outDetail.setTagentNo(submitUncheckOrderDto.gettSellerCode());
                outDetail.setFundCode(dtlDto.getFundCode());
                outDetail.setProtocolNo(dtlDto.getProtocolNo());
                outDetail.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
                outDetail.setShareClass(submitUncheckOrderDto.getFundShareClass());
                outDetail.setAppVol(dtlDto.getAppVol());
                outDetail.setAppVol(dtlDto.getAppVol());
                outDetail.setProtocolNo(dtlDto.getProtocolNo());
                outDetail.setLctAcctNo(dtlDto.getBankAcct());
                outDetail.setCounterDealNo(dtlDto.getDealDtlAppNo());

                appDtls.add(outDetail);
            }
        }
        xferAgentOutContext.setAppDtls(appDtls);
        xferAgentOutContext.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        xferAgentOutContext.setFundCode(submitUncheckOrderDto.getFundCode());
        xferAgentOutContext.setAppDt(submitUncheckOrderDto.getAppDt());
        xferAgentOutContext.setAppTime(submitUncheckOrderDto.getAppTm());

        logger.debug("Fund |lctCheckCounterTransferTubeOutOrder request:{}", JSON.toJSONString(xferAgentOutContext));

        XferAgentOutResult  result = xferAgentOutService.xferAgentOut(xferAgentOutContext);
        if (result != null) {
            if (result.isSuccess()) {
                submitUncheckOrderDto.setReturnCode(result.getReturnCode());
                submitUncheckOrderDto.setDescription(result.getDescription());
                submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                submitUncheckOrderDto.setDealNo(result.getDealNo());
                checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
            } else {
                throw new TmsCounterException(result.getReturnCode(), result.getDescription());
            }
        }

        return true;
    }

    public boolean redeemPortfolioCounter(SubmitUncheckOrderDto submitUncheckOrderDto, List<SubmitUncheckOrderDtlDto> dtlOrderDtoList, DisInfoDto disInfoDto) throws Exception {
        RedeemPortfolioRequest request = new RedeemPortfolioRequest();
        request.setTxCode(TxCodes.REDEEM);
        request.setTxPasswordValidateFlag(TxPasswordValidateFlagEnum.NOT_REQUIRED_VALIDATE.getCode());
        request.setForceRedeemFlag(ForceRedeemFlagEnum.YES.getCode());
        request.setAppDt(submitUncheckOrderDto.getAppDt());
        request.setAppTm(submitUncheckOrderDto.getAppTm());
        request.setTxAcctNo(submitUncheckOrderDto.getTxAcctNo());
        request.setDisCode(submitUncheckOrderDto.getDisCode());
        request.setProtocolNo(submitUncheckOrderDto.getProtocolNo());
        request.setCpAcctNo(submitUncheckOrderDto.getCpAcctNo());
        if(RedeemCapitalFlagEnum.PIGGY.getCode().equals(submitUncheckOrderDto.getRedeemCapitalFlag())){
            request.setRedeemDirection(RedeemCapitalFlagEnum.BANK.getCode());
        }
        if(RedeemCapitalFlagEnum.BANK.getCode().equals(submitUncheckOrderDto.getRedeemCapitalFlag())){
            request.setRedeemDirection(RedeemCapitalFlagEnum.PIGGY.getCode());
        }
        request.setAppRatio(String.valueOf(submitUncheckOrderDto.getAppRatio()));

        List<RedeemPortfolioRequest.RedeemFundInfo> redeemFundInfos = new ArrayList<>();
        if("2".equals(submitUncheckOrderDto.getCustomRatioType()) && !CollectionUtils.isEmpty(dtlOrderDtoList)){
            RedeemPortfolioRequest.RedeemFundInfo redeemFundInfo = null;
            for(SubmitUncheckOrderDtlDto dtlDto : dtlOrderDtoList){
                if(dtlDto.getAppVol() != null && dtlDto.getAppVol().compareTo(BigDecimal.ZERO) > 0){
                    redeemFundInfo = new RedeemPortfolioRequest.RedeemFundInfo();
                    redeemFundInfo.setFundCode(dtlDto.getFundCode());
                    redeemFundInfo.setSellVol(dtlDto.getAppVol());
                    redeemFundInfos.add(redeemFundInfo);
                }
            }
            request.setRedeemFundInfos(redeemFundInfos);
        }
        BaseResponse baseResp = TmsFacadeUtil.execute(counterRedeemPortfolioFacade, request, disInfoDto);
        String dealNo = null;
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                RedeemPortfolioResponse redeemPortfolioResponse = (RedeemPortfolioResponse) baseResp;
                dealNo = redeemPortfolioResponse.getDealNo();
                if (TmsFacadeUtil.isSuccess(baseResp) && !StringUtils.isEmpty(dealNo)) {
                    submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                    submitUncheckOrderDto.setDealNo(redeemPortfolioResponse.getDealNo());
                    checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
                } else {
                    throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
                }

            }
        }
        return !StringUtils.isEmpty(dealNo)?true:false;
    }

    public boolean checkModifyRedeemDirection(SubmitUncheckOrderDto submitUncheckOrderDto, DisInfoDto disInfoDto) throws Exception {

        if(submitUncheckOrderDto == null){
            return false;
        }
        ModifyRedeemDirectionRequest request = new ModifyRedeemDirectionRequest();
        RedeemDirectionBean bean = new RedeemDirectionBean();
        bean.setDealNo(submitUncheckOrderDto.getDealNo());
        bean.setRedeemDirection(submitUncheckOrderDto.getWithdrawDirection());
        request.setRedeemDirectionList(Lists.newArrayList(bean));
        logger.debug("Fund | checkModifyRedeemDirection request:{}", JSON.toJSONString(request));

        BaseResponse baseResp = TmsFacadeUtil.execute(modifyRedeemDirectionFacade, request, disInfoDto);
        if (baseResp != null) {
            submitUncheckOrderDto.setReturnCode(baseResp.getReturnCode());
            submitUncheckOrderDto.setDescription(baseResp.getDescription());
            if (TmsFacadeUtil.isSuccess(baseResp)) {
                ModifyRedeemDirectionResponse modifyRedeemDirectionResponse = (ModifyRedeemDirectionResponse) baseResp;
                if(modifyRedeemDirectionResponse.getFailList().size() == 0 || (modifyRedeemDirectionResponse.getFailList().size() > 0
                        && modifyRedeemDirectionResponse.getFailList().get(0).isProcessFlag())){
                    submitUncheckOrderDto.setAppFlag(CounterAppFlagEnum.APP_SUCC.getKey());
                    if(!CollectionUtils.isEmpty(modifyRedeemDirectionResponse.getFailList())){
                        submitUncheckOrderDto.setDealNo(modifyRedeemDirectionResponse.getFailList().get(0).getDealNo());
                    }
                    checkCounterOrder(submitUncheckOrderDto, disInfoDto, CounterCheckFlagEnum.CHECKED_SUCC.getKey());
                } else {
                    throw new TmsCounterException("", modifyRedeemDirectionResponse.getFailList().get(0).getFailDesc());
                }

            } else {
                throw new TmsCounterException(baseResp.getReturnCode(), baseResp.getDescription());
            }
        }
        return true;
    }

}
