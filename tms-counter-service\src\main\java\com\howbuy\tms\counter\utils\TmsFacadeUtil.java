package com.howbuy.tms.counter.utils;

import com.howbuy.tms.counter.common.ReturnCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.enums.TmsCounterOutCodeEnum;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.common.client.BaseFacade;
import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.client.DefaultParamsConstant;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.HttpUtil;
import com.howbuy.tms.counter.common.util.RequestUtil;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 中台dubbo服务调用工具类
 * 
 * <AUTHOR>
 * @date 2017-03-29 13:55
 *
 */
public class TmsFacadeUtil {
    private final static Logger logger = LoggerFactory.getLogger(TmsFacadeUtil.class);

    /**
     * 获取分销
     * @param disCode
     * @return
     */
    public static String getAllDisCode(String disCode) {
        if(StringUtil.isEmpty(disCode)) {
            return DefaultParamsConstant.DEFULT_DIS_CODE;
        }
        return disCode;
    }
    
    /**
     * 执行facade
     * 
     * @param facade
     * @param request
     * @param disInfoDto
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
	public static BaseResponse execute(BaseFacade facade, BaseRequest request, DisInfoDto disInfoDto) {
        doFillBaseRequest(request, disInfoDto);
        Map<String, Object> map = printRequestLog(request);
        BaseResponse response = facade.execute(request);
        printResponseLog(response, map);
        return response;
    }

    /**
     * 执行facade返回失败抛异常处理
     * 
     * @param facade
     * @param request
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
	public static BaseResponse executeThrowException(BaseFacade facade, BaseRequest request, DisInfoDto disInfoDto) throws Exception {
        BaseResponse response = execute(facade, request, disInfoDto);
        if (response == null){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD);
        }
        String code = response.getReturnCode();
        String desc = response.getDescription();
        if (isSucc(ReturnCodeEnum.SUCC_TMS.getCode(), code) || isSucc(ReturnCodeEnum.SUCC_NEW.getCode(), code)) {
            return response;
        } else{
            throw new TmsCounterException(code, desc);
        }
    }

    /**
     * 执行facade返回失败抛异常处理
     *
     * @param facade
     * @param request
     * @param disInfoDto
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
	public static BaseResponse executeThrowsException(BaseFacade facade, BaseRequest request, DisInfoDto disInfoDto) throws TmsCounterException {
        BaseResponse response = execute(facade, request, disInfoDto);
        if (response == null){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD);
        }
        String code = response.getReturnCode();
        String desc = response.getDescription();
        if (isSucc(ReturnCodeEnum.SUCC_TMS.getCode(), code) || isSucc(ReturnCodeEnum.SUCC_NEW.getCode(), code)) {
            return response;
        } else{
            throw new TmsCounterException(code, desc);
        }
    }

    public static boolean isSucc(String succCode, String returnCode) {
        return succCode.equals(returnCode);
    }

    /**
     * 
     * @param request
     *            公共输入对象
     * @param disInfoDto
     *            登录信息

     */
    public static void doFillBaseRequest(BaseRequest request, DisInfoDto disInfoDto) {
        String operIp = HttpUtil.getIpAddr(RequestUtil.getHttpRequest());
        request.setOperIp(operIp);
        setCommonParameters(request, disInfoDto);
    }

    public static void setCommonParameters(BaseRequest request, DisInfoDto disInfoDto) {
        String uuid = UUID.randomUUID().toString();
        request.setDataTrack(uuid);
        
        if(disInfoDto != null && DefaultParamsConstant.DEFULT_DIS_CODE.equals(disInfoDto.getDisCode())) {
            disInfoDto.setDisCode(DisCodeEnum.HM.getCode());
        }

        String disCode = disInfoDto != null ? disInfoDto.getDisCode() : DisCodeEnum.HM.getCode();
        String outletCode = disInfoDto != null ? disInfoDto.getOutletCode() : TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode(); 
        if (StringUtils.isEmpty(request.getOutletCode())) {
            request.setOutletCode(StringUtils.isEmpty(outletCode) ? TmsCounterOutCodeEnum.HOWBUY_COUNTE_OUT_CODE.getCode() : outletCode);
        }
        TradeParamLocalUtils.setOutletCode(request.getOutletCode());

        if (StringUtils.isEmpty(request.getDisCode())) {
            request.setDisCode(StringUtils.isNotEmpty(disCode) ? disCode : DisCodeEnum.HM.getCode());
        }
        
        TradeParamLocalUtils.setDisCode(request.getDisCode());

        if (StringUtil.isEmpty(request.getAppDt())) {
            // 申请日期
            request.setAppDt(DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN));
        }
        TradeParamLocalUtils.setAppDt(request.getAppDt());

        if (StringUtil.isEmpty(request.getAppTm())) {
            // 申请时间
            request.setAppTm(DateUtil.formatNowDate(DateUtil.StR_PATTERN_HHMMSS));
        }
        TradeParamLocalUtils.setAppTm(request.getAppTm());

        if (StringUtil.isEmpty(request.getTxChannel())) {
            request.setTxChannel("1");
        }
    }

    /**
     * 打印请求日志
     * 
     * @param request
     */
    private static Map<String, Object> printRequestLog(BaseRequest request) {
        if (null == request) {
            return null;
        }

        String uuid = UUID.randomUUID().toString();
        long start = System.currentTimeMillis();
        Map<String, Object> map = new HashMap<String, Object>(16);
        map.put("uuid", uuid);
        map.put("start", start);
        logger.info("###### request:[UUID:{},txName:{}]{}", new Object[] { uuid, request.getClass().getSimpleName(), JSON.toJSONString(request) });
        return map;
    }

    /**
     * 打印返回日志，不抛异常
     * 
     * @param response
     */
    private static void printResponseLog(BaseResponse response, Map<String, Object> map) {
        if (null == response) {
            return;
        }
        String uuid = (String) map.get("uuid");
        long start = (Long) map.get("start");
        long cost = System.currentTimeMillis() - start;
        logger.info("###### response:[UUID:{},responseName:{},code:{},desc:{},cost:{}]{}", new Object[] { uuid, response.getClass().getName(),
                response.getReturnCode(), response.getDescription(), String.valueOf(cost), JSON.toJSONString(response) });

    }

    /**
     * 是否处理成功
     * 
     * @param response
     * @return
     */
    public static boolean isSuccess(BaseResponse response) {
        if (response == null) {
            return false;
        }
        String code = response.getReturnCode();
        if (!StringUtil.isEmpty(code) && ReturnCodeEnum.SUCC_TMS.getCode().equals(code)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 是否处理成功
     *
     * @param response
     * @return
     */
    public static boolean isSuccessForRegular(BaseResponse response) {
        if (response == null) {
            return false;
        }
        String code = response.getReturnCode();
        if (!StringUtil.isEmpty(code) && ReturnCodeEnum.SUCC_TMS.getCode().equals(code)) {
            return true;
        } else {
            return false;
        }
    }


    public static boolean isSuccess(String returnCode) {
        return ReturnCodeEnum.SUCC.getCode().equals(returnCode) || ReturnCodeEnum.SUCC_NEW.getCode().equals(returnCode)  || ReturnCodeEnum.SUCC_TMS.getCode().equals(returnCode);
    }

}
