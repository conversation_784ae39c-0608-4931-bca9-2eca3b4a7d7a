/**
 * Copyright (c) 2020, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 非交易过户请求
 *
 * <AUTHOR>
 * @date 2020/9/24 15:47
 * @since JDK 1.8
 */
@Getter
@Setter
public class CounterNoTradeOverAccountReqDto extends OperInfoBaseDto {

    private static final long serialVersionUID = 5257943668655903259L;

    private String dealNo;
    private String txAcctNo;
    private String cpAcctNo;
    private String custName;
    private String idNo;
    private String idType;
    private String invstType;
    private String bankAcct;
    private String inTxAcctNo;
    private String inCpAcctNo;
    private String inCustName;
    private String inIdNo;
    private String inIdType;
    private String inInvstType;
    private String inBankAcct;
    private String inDisCode;
    private String fundCode;
    private String fundName;
    private BigDecimal appVol;
    private BigDecimal subsAmt;
    /**
     * 转让价格
     */
    private BigDecimal transferPrice;
    private String appDt;
    private String appTm;
    private String protocolNo;
    private String protocolType;
    private String orderFormMemo;
    private String productChannel;
    private String taCode;

    /**
     * 业务类型
     */
    private String busiType;
    /**
     * 过户的总认缴金额
     */
    private BigDecimal totalSubsAmt;
}