/**
\*购买
*<AUTHOR>
*@date 2017-09-15 10:39
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();
	BuyFund.init();
	BuyFund.currDate = '';
});

var BuyFund = {
	init:function(){
		/**
		 * 确认购买
		 */
		$("#confimBuyBtn").on('click',function(){
			BuyFund.confirm();
		});
		
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询客户基本信息
		 */
		$("#queryCustInfoBtn").on('click',function(){
			QueryCustInfo.queryCustInfo();
		});
		
		/**
		 * 查询产品基本信息
		 */
		$(".searchIcon").on('click blur',function(){
			QueryProductInfo.queryProductInfo($("#productId").val());
		});
		

	},
	

	
	validatorRetailRiskLevel:function(productInfoForm,custInfoForm){
		var uri = TmsCounterConfig.VALIDATOR_REGULAR_RISK_LEVEL_URL ||  {};
		var reqparamters ={"productInfoForm":productInfoForm,"custInfoForm":custInfoForm};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,"post",null);
		CommonUtil.ajaxAndCallBack(paramters, BuyFund.validatorRetailRiskLevelCallBack);
		
	},
	
	validatorRetailRiskLevelCallBack:function(data){
		var uri = TmsCounterConfig.BUY_REGULAR_CONFIRM_URL ||  {};
		
		/**风险确认标记：1-确认，0-未确认*/
		var riskFlag = '0'; 
		
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var productInfoForm = JSON.stringify(QueryProductInfo.productInfo);
		
		var buyConfirmForm = $("#buyConfirmForm").serializeObject();
		var bankAcct = $('#selectBank').find('option:selected').attr('bankacct');
		buyConfirmForm.bankAcct = bankAcct || '';
		buyConfirmForm.appAmt = CommonUtil.unFormatAmount(buyConfirmForm.appAmt);
		
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		var dealAppNo ="";
		if(!(typeof ApplyBuy == "undefined")){
			dealAppNo = ApplyBuy.checkOrder.dealAppNo;
		}
		
		if(CommonUtil.isSucc(respCode)){
			buyConfirmForm.riskFlag = riskFlag; 
			var reqparamters ={"dealAppNo":dealAppNo,"productInfoForm":productInfoForm,"buyConfirmForm": JSON.stringify(buyConfirmForm),"custInfoForm":custInfoForm,"transactorInfoForm":JSON.stringify(transactorInfoForm)};
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);
			CommonUtil.ajaxAndCallBack(paramters, BuyFund.callBack);
		}else if ('Z3000023' == respCode){
			CommonUtil.layer_tip(respDesc);
			if('0' == QueryCustInfo.custInfo.custRiskLevel ){
			//2、客户风险等级为极低，不能购买除货币基金以外的基金
				CommonUtil.layer_tip("客户风险等级为极低，不能购买除货币基金之外的基金！");
				CommonUtil.enabledBtn("confimBuyBtn");
				return false;
			}
			
			layer.open({
	            title: ['风险提示', true],
	            type: 1,
	            area: ['320px', 'auto'],
	            btn: ['确定', '取消'],
	            skin: 'layui-layer-rim', 
	            btnAlign: 'l',
	            content: "基金风险高于户风险等级承受能力，确认继续吗",
	            yes: function (index, layero) { //或者使用btn1
	            	//layer.close(index);
	            	layer.closeAll();
	            	riskFlag = '1';
	            	buyConfirmForm.riskFlag = riskFlag; 
	        		var reqparamters ={"dealAppNo":dealAppNo,"productInfoForm":productInfoForm,"buyConfirmForm": JSON.stringify(buyConfirmForm),"custInfoForm":custInfoForm,"transactorInfoForm":JSON.stringify(transactorInfoForm)};
	        		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);
	        		CommonUtil.ajaxAndCallBack(paramters, BuyFund.callBack);
	            },
	            cancel: function (index) { 
	            	CommonUtil.enabledBtn("confimBuyBtn");
	            }
	        }); 
		} else if('Z9999999' == respCode){
			CommonUtil.layer_tip("系统异常");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		} else if('Z3000005' == respCode){
			CommonUtil.layer_tip("客户被要求的风险评测未完成");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}else if('Z3000006' == respCode){
			CommonUtil.layer_tip("客户风险承受能力评测已过期");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}else {
			CommonUtil.layer_tip(respDesc);
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
	},
	
	/***
	 * 确认购买
	 */	
	confirm : function(dealAppNo){
		CommonUtil.disabledBtn("confimBuyBtn");
		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}

		var validRst = Valid.valiadateFrom($("#buyConfirmForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
		var buyConfirmForm = $("#buyConfirmForm").serializeObject();
		
		var bankAcct = $('#selectBank').find('option:selected').attr('bankacct');
		buyConfirmForm.bankAcct = bankAcct || '';
		buyConfirmForm.appAmt = CommonUtil.unFormatAmount(buyConfirmForm.appAmt);
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var productInfoForm = JSON.stringify(QueryProductInfo.productInfo);
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();
		
		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}

		
		buyConfirmForm.appDtm = buyConfirmForm.appDt +'' + buyConfirmForm.appTm;
		if(!Valid.valiadTradeTime(buyConfirmForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimBuyBtn");
			return false;
		}
		
		BuyFund.validatorRetailRiskLevel(productInfoForm,custInfoForm);

	},
		
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			if($(".confimBtn").length > 0){
				CommonUtil.disabledBtnWithClass("confimBtn");
				CommonUtil.disabledBtn("abolishBtn");
			}
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.layer_tip("提交失败，"+respDesc);
		}
		
		if(!$(".confimBtn").length > 0){
			CommonUtil.enabledBtn("confimBuyBtn");
		}
	},

};



