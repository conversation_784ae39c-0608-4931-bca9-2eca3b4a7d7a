/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.interlayer.product.enums.ProductClassEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.busi.BusiProcessProtocolTypeEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CancelDealDto;
import com.howbuy.tms.counter.dto.CounterCancelReqDto;
import com.howbuy.tms.counter.dto.CounterCancelRespDto;
import com.howbuy.tms.counter.dto.CounterOrderDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderReqDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderRespDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.enums.CheckFlagEnum;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 
 * @description:(撤单控制器)
 * <AUTHOR>
 * @date 2017年9月16日 下午7:11:27
 * @since JDK 1.6
 */
@Controller
public class CanceFundController extends AbstractController {
    private Logger logger = LogManager.getLogger(CanceFundController.class);

    @Autowired
    private TradeValidateService tradeValidateService;

    /**
     * 
     * cancelConfirm:(零售柜台撤单)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月16日 下午7:11:38
     */
    @RequestMapping("/tmscounter/fund/cancelconfirm.htm")
    public ModelAndView cancelConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String cancelConfirmCmd = request.getParameter("cancelConfirmForm");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.info("CanceFundController|cancelconfirm: cancelConfirmCmd:{}, custInfoForm:{}, transactorInfoForm:{}",
                cancelConfirmCmd, custInfoForm, transactorInfoForm);

        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        CancelDealDto orderDto = JSON.parseObject(cancelConfirmCmd, CancelDealDto.class);

        CounterCancelReqDto counterCancelReqDto = new CounterCancelReqDto();
        counterCancelReqDto.setDealAppNo(dealAppNo);
        counterCancelReqDto.setFundCode(orderDto.getProductCode());
        counterCancelReqDto.setDealNo(orderDto.getDealNo());
        counterCancelReqDto.setFundName(orderDto.getProductName());
        counterCancelReqDto.setAppAmt(orderDto.getAppAmt());
        counterCancelReqDto.setAppVol(orderDto.getAppVol());
        counterCancelReqDto.setPartnerCode(orderDto.getPartnerCode());

        FundInfoAndNavBean fundInfo = tmsCounterOutService.getFundNavInfo(orderDto.getFundCode(), orderDto.getTaTradeDt());
        if(fundInfo != null){
            counterCancelReqDto.setTaCode(fundInfo.getTaCode());
            counterCancelReqDto.setFundName(StringUtils.defaultIfEmpty(fundInfo.getFundName(), fundInfo.getFundAttr()));
            counterCancelReqDto.setFundShareClass(fundInfo.getFundShareClass());
        }
        // 柜台不支持Y份额基金交易
        tradeValidateService.tradeFundValidate(fundInfo);

        counterCancelReqDto.setDisCode(custInfoDto.getDisCode());
        counterCancelReqDto.setCustName(custInfoDto.getCustName());
        counterCancelReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterCancelReqDto.setIdNo(custInfoDto.getIdNo());
        counterCancelReqDto.setIdType(custInfoDto.getIdType());
        counterCancelReqDto.setInvstType(custInfoDto.getInvstType());

        counterCancelReqDto.setAppDt(transactorInfoDto.getAppDt());
        counterCancelReqDto.setAppTm(transactorInfoDto.getAppTm());
        counterCancelReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        counterCancelReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterCancelReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterCancelReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterCancelReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterCancelReqDto.setCancelType(transactorInfoDto.getCancelType());
        counterCancelReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterCancelReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());
        counterCancelReqDto.setCancelMemo(transactorInfoDto.getCancelMemo());
        counterCancelReqDto.setWithdrawDirection(transactorInfoDto.getWithdrawDirection());

        counterCancelReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        counterCancelReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        String protocolType = orderDto.getProtocolType();
        // 普通公募协议
        counterCancelReqDto.setProtocolType(protocolType);
        counterCancelReqDto.setProtocolNo(orderDto.getProtocolNo());
        // 普通公募产品
        if (BusiProcessProtocolTypeEnum.getAllGmProtocolTypes().contains(protocolType)){
            counterCancelReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
        }else {
            counterCancelReqDto.setProductClass(ProductClassEnum.PORTFOLIO.getCode());
        }
        counterCancelReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECK.getCode());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterCancelReqDto);
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(orderDto.getDisCode());
        CounterCancelRespDto responseDto = tmsFundCounterService.counterCancel(counterCancelReqDto, disInfoDto);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/fund/querycancancelforapply.htm")
    public ModelAndView queryCanCancelForApply(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String dealNo = request.getParameter("dealNo");
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        
        // 查询可撤单订单
        List<CancelDealDto> canCancelOrders = tmsFundCounterService.queryCanCancelOrder(dealNo, custNo, null);

        Map<String, CancelDealDto> mapData = convertMap(canCancelOrders);

        List<CounterOrderDto> counterOrderList = getCounterAppCancelOrder(custNo, dealNo, null);
        // 过滤掉已经申请过的撤单订单
        if (!CollectionUtils.isEmpty(counterOrderList)) {
            for (CounterOrderDto counterOrderDto : counterOrderList) {
                if (mapData.containsKey(counterOrderDto.getDealNo())) {
                    canCancelOrders.remove(mapData.get(counterOrderDto.getDealNo()));
                }
            }
        }
        
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("canCancelOrders", canCancelOrders);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    @RequestMapping("/tmscounter/fund/querycancancel.htm")
    public ModelAndView queryCanCancel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String dealNo = request.getParameter("dealNo");
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        
        // 查询可撤单订单
        List<CancelDealDto> canCancelOrders = tmsFundCounterService.queryCanCancelOrder(dealNo, custNo, null);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("canCancelOrders", canCancelOrders);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    
    /**
     * 
     * getCounterAppCancelOrder:(查询柜台申请的撤单订单)
     * @param txAcctNo
     * @param dealNo
     * @param operatorNo
     * @return
     * <AUTHOR>
     * @date 2018年11月2日 下午5:41:03
     */
    private List<CounterOrderDto> getCounterAppCancelOrder(String txAcctNo, String dealNo, String operatorNo){
        CounterQueryOrderReqDto counterQueryOrderReqDto = new CounterQueryOrderReqDto();
        counterQueryOrderReqDto.setTxAcctNo(txAcctNo);
        counterQueryOrderReqDto.setDealNo(dealNo);
        List<String> txCodeList = new ArrayList<String>();
        txCodeList.add(TxCodes.COUNTER_CANCEL);
        txCodeList.add(TxCodes.COUNTER_FORCE_CANCEL);
        counterQueryOrderReqDto.setTxCodeList(txCodeList);

        List<String> checkFlagLsit = new ArrayList<String>();
        checkFlagLsit.add(CheckFlagEnum.NOT_CHECK.getCode());
        checkFlagLsit.add(CheckFlagEnum.CHECK_REJECT.getCode());
        checkFlagLsit.add(CheckFlagEnum.CHECK_SUCC.getCode());
        counterQueryOrderReqDto.setCheckFlagLsit(checkFlagLsit);
        counterQueryOrderReqDto.setProductClassList(Lists.newArrayList(ProductClassEnum.RETAIL.getCode(), ProductClassEnum.PORTFOLIO.getCode()));
        counterQueryOrderReqDto.setOperatorNo(operatorNo);

        CounterQueryOrderRespDto counterQueryOrderRespDto = null;
        try {
            counterQueryOrderRespDto = tmsFundCounterService.counterQueryOrder(counterQueryOrderReqDto, null);
        } catch (Exception e) {
            logger.error("Get Counter app cancel order exception.", e);
        }
        if(counterQueryOrderRespDto != null){
            return counterQueryOrderRespDto.getCounterOrderList();
        }
        return null;
    }

    /**
     * 
     * convertMap:(list to map)
     * 
     * @param canCancelOrders
     * @return
     * <AUTHOR>
     * @date 2017年11月15日 下午1:45:40
     */
    private Map<String, CancelDealDto> convertMap(List<CancelDealDto> canCancelOrders) {
        Map<String, CancelDealDto> map = new HashMap<String, CancelDealDto>(16);

        if (CollectionUtils.isEmpty(canCancelOrders)) {
            return map;
        }

        for (CancelDealDto cancelDealDto : canCancelOrders) {
            map.put(cancelDealDto.getDealNo(), cancelDealDto);
        }
        return map;
    }
}
