/**
 * 初始化
 */
$(function(){
	ModifyRefundDirCheck.refundInfo = {};
	ModifyRefundDirCheck.checkOrder = {};
	//viewType 0-查看；1-审核；2-修改
	var viewType = CommonUtil.getParam("viewType");
    if('crm' == CommonUtil.getParam("source")){
        viewType = '1';
    }

	var checkFlag = CommonUtil.getParam("checkFlag");
	$('#refundTableId').html(ModifyRefundDirCheck.generateCancelFormTable(checkFlag));

	//初始化按钮
	CounterCheck.initBtn(viewType);

    // 查询订单
	CounterCheck.queryCounterDealOrder(viewType, ModifyRefundDirCheck.queryCounterDealOrderCallBack, null);
});

var ModifyRefundDirCheck = {
	queryCounterDealOrderCallBack:function(data){
		
		var bodyData = data.body || {};

		CounterCheck.counterOrderDto = bodyData.counterOrderDto || {};//柜台订单
		
		var checkOrder = CounterCheck.counterOrderDto || {};//柜台订单信息
		ModifyRefundDirCheck.checkOrder = checkOrder;
		var refundInfo = JSON.parse(checkOrder.orderFormMemo);
		ModifyRefundDirCheck.refundInfo = refundInfo;
		ModifyRefundDirCheck.ModifyRefundDirCheck(checkOrder);//订单信息
	},

	/**
	 * 柜台交易查询
	 */
	ModifyRefundDirCheck : function(checkOrder){
		var  uri= TmsCounterConfig.QUERY_COUNTER_MODIFY_REFUND_DIRECTION_URL ||  {};
		var reqparamters  = {};
		var queryOrderCondition = {"dealNo":checkOrder.dealNo,"txAcctNo":checkOrder.txAcctNo};
		reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
		reqparamters.page = 1;
		reqparamters.pageSize = 50;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxPaging(uri, paramters,  ModifyRefundDirCheck.callBack, "pageView");
	},

	callBack:function(data){
		var orders = data.orders || [];
		var element = orders[0];
		ModifyRefundDirCheck.order = element;
		$("#rsList").empty();
		var trList = [];
		trList.push(CommonUtil.formatData(element.taTradeDt));
		trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
		trList.push(CommonUtil.formatData(element.custName));
		trList.push(CommonUtil.formatData(element.productCode));
		trList.push(CommonUtil.formatData(element.productName));
		trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME, element.mBusiCode, ''));
		trList.push(CommonUtil.formatData(element.dealNo));
		trList.push(CommonUtil.formatData(element.appAmt));
		trList.push(CommonUtil.formatData(element.appVol));
		trList.push(CommonUtil.formatData(element.ackAmt));
		trList.push(CommonUtil.formatData(element.ackVol));
		trList.push(CommonUtil.formatData(element.appDate));
		trList.push(CommonUtil.formatData(element.appTime));
		trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP, element.orderStatus, ''));
		var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>')+'</td></tr>';
		$("#rsList").append(trHtml);

		$("#orderStatusId").html(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP, element.orderStatus, ''));
		$("#refundAmtId").html(element.refundAmt);
		$("#refundMemoId").html(element.refundMemo);

		if ((element.mBusiCode == '1122' || element.mBusiCode == '1120') && (element.orderStatus == '5' || element.orderStatus == '6')) {
			$("#refundDirectionId").html(CommonUtil.getMapValue(CONSTANTS.WITHDRAW_DIR_ALL_MAP, element.withdrawDirection, ''));
		} else {
			$("#refundDirectionId").html(CommonUtil.getMapValue(CONSTANTS.REDEEM_DIRECTION_MAP, element.redeemDirection, ''));
		}

		// 显示回可用配置项
		var payType = element.paymentType || '';
		ModifyRefundDirCheck.refundDirectionOnChange(element.mBusiCode, payType);
	},

	/**
	 * 回可用备注选择
	 * @param redeemDirection
	 */
	refundDirectionOnChange: function(mBusiCode, payType) {
		if ((mBusiCode == '1122' || mBusiCode == '1120') && "01" == payType || mBusiCode == '1124') {
			var dir;
			var order = ModifyRefundDirCheck.order;
			var checkOrder = ModifyRefundDirCheck.checkOrder;
			if ((order.mBusiCode == '1122' || order.mBusiCode == '1120') && (order.orderStatus == '5' || order.orderStatus == '6')) {
				dir = checkOrder.withdrawDirection;
				$("#newRefundDirectionId").html(CommonUtil.getMapValue(CONSTANTS.WITHDRAW_DIR_ALL_MAP, checkOrder.withdrawDirection, ''));
			} else {
				dir = checkOrder.redeemCapitalFlag;
				$("#newRefundDirectionId").html(CommonUtil.getMapValue(CONSTANTS.REDEEM_DIRECTION_MAP, checkOrder.redeemCapitalFlag, ''));
			}
			if (dir == '5' || dir == '6' || dir == '7') {
				$("#newRefundAmtId").html(ModifyRefundDirCheck.refundInfo.refundFinaAvailAmt);
				$("#newRefundMemoId").html(ModifyRefundDirCheck.refundInfo.refundFinaAvailMemo);
			}else {
				$("#newRefundAmtId").html("");
				$("#newRefundMemoId").html("");
			}
		}
	},

	generateCancelFormTable:function(checkFlag) {
		if(checkFlag == 1){
			var table = '<tr class="text-c">' +
				'<tr class="text-c">' +
				'<td>当前订单状态</td>' +
				'<td id="orderStatusId"></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>修改后的回款方向</td>' +
				'<td id="newRefundDirectionId"></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>回可用余额的金额</td>' +
				'<td id="newRefundAmtId"></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>回可用余额的备注</td>' +
				'<td id="newRefundMemoId"></td>' +
				'</tr>';
		}else{
			var table ='<tr class="text-c">' +
				'<td>修改前</td>' +
				'<td></td>' +
				'<td>修改后</td>' +
				'<td></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>当前订单状态</td>' +
				'<td id="orderStatusId"></td>' +
				'<td></td>' +
				'<td></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>当前回款方向</td>' +
				'<td id="refundDirectionId"></td>' +
				'<td>修改后的回款方向</td>' +
				'<td id="newRefundDirectionId"></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>回可用余额的金额</td>' +
				'<td id="refundAmtId"></td>' +
				'<td>回可用余额的金额</td>' +
				'<td id="newRefundAmtId"></td>' +
				'</tr>' +
				'<tr class="text-c">' +
				'<td>回可用余额的备注</td>' +
				'<td id="refundMemoId"></td>' +
				'<td>回可用余额的备注</td>' +
				'<td id="newRefundMemoId"></td>' +
				'</tr>';
		}

		return table;
	},

};
