/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.lctcontroller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.client.TxCodes;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:(柜台审核控制)
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class LctCheckFundController extends AbstractController {
    private static Logger logger = LogManager.getLogger(LctCheckFundController.class);

    /**
     * 
     * queryCheckOrder:(查询待审核订单列表)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:16:03
     */
    @RequestMapping("/tmscounter/lct/querycheckorder.htm")
    public ModelAndView queryCheckOrder(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String queryOrderCmd = request.getParameter("queryConditonForm");
        String pageNum = StringUtils.isEmpty(request.getParameter("page")) ? "1" : request.getParameter("page");
        String pageSize = StringUtils.isEmpty(request.getParameter("pageSize")) ? "20" : request.getParameter("pageSize");
        String owner = request.getParameter("owner");
        String checkFlag = request.getParameter("checkFlag");
        String tradeDt = request.getParameter("tradeDt");
        logger.debug("queryOrderCmd:{}, checkFlag:{}, tradeDt:{}", queryOrderCmd, checkFlag, tradeDt);
        
        CounterQueryOrderReqDto counterQueryOrderReqDto = JSON.parseObject(queryOrderCmd, CounterQueryOrderReqDto.class);
        counterQueryOrderReqDto.setPageNo(Integer.parseInt(pageNum));
        counterQueryOrderReqDto.setPageSize(Integer.parseInt(pageSize));
        // 理财通
        counterQueryOrderReqDto.setProductClass(ProductClassEnum.LCT.getCode());
        if (!StringUtils.isEmpty(checkFlag)) {
            counterQueryOrderReqDto.setCheckFlag(checkFlag);
        }
        
        // 我的交易申请查询需要绑定操作员号
        if (Constants.ROLE_OWNER.equals(owner)) {
            OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
            counterQueryOrderReqDto.setCreator(operatorInfoCmd.getOperatorNo());

            // 我的交易申请查询默认查询当前工作日
            if (Constants.ORDER_CMD_BRACKETS.equals(queryOrderCmd)) {
                counterQueryOrderReqDto.setTradeDt(tradeDt);
            }
        }
        
        QueryCustBaseInfoReqDto queryCustBaseInfoReqDto = new QueryCustBaseInfoReqDto();
        if (StringUtils.isEmpty(counterQueryOrderReqDto.getTxAcctNo())) {
            if (!StringUtils.isEmpty(counterQueryOrderReqDto.getIdNo())) {
                queryCustBaseInfoReqDto.setIdNo(counterQueryOrderReqDto.getIdNo());
                QueryCustBaseInfoRespDto qeryCustBaseInfoRespDto = tmsCounterService.queryCustBaseInfo(queryCustBaseInfoReqDto, null);
                counterQueryOrderReqDto.setTxAcctNo(qeryCustBaseInfoRespDto.getTxAcctNo());
            }
        }
        logger.debug("counterQueryOrderReqDto:{}", JSON.toJSONString(counterQueryOrderReqDto));

        // 主申请单
        CounterQueryOrderRespDto counterQueryOrderRespDto = tmsFundCounterService.counterQueryOrder(counterQueryOrderReqDto, null);
        if(counterQueryOrderRespDto == null){
            throw new TmsCounterException(TmsCounterResultEnum.FAILD);
        }
        // 脱敏
        if (!CollectionUtils.isEmpty(counterQueryOrderRespDto.getCounterOrderList())) {
            for (CounterOrderDto dto : counterQueryOrderRespDto.getCounterOrderList()) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("counterOrderList", counterQueryOrderRespDto.getCounterOrderList());
        body.put("totalPage", counterQueryOrderRespDto.getTotalPage());
        body.put("pageNum", counterQueryOrderRespDto.getPageNo());
        body.put("counterQueryOrderRespDto", counterQueryOrderRespDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 
     * queryCheckOrderById:(查询单条未审核订单)
     * 
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月18日 下午5:10:22
     */
    @RequestMapping("/tmscounter/lct/querycheckorderbyid.htm")
    public ModelAndView queryCheckOrderById(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String dealAppNo = request.getParameter("dealAppNo");
        String pageNum = request.getParameter("pageNum");
        String pageSize = request.getParameter("pageSize");

        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setDealAppNo(dealAppNo);
        //理财通
        queryReqDto.setProductClass(ProductClassEnum.LCT.getCode());
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        // 主申请单
        CounterOrderDto counterOrderDto = tmsFundCounterService.counterQueryOrderById(queryReqDto, null);
        
        // 明细申请单
        List<SubmitUncheckOrderDtlDto> dtlOrderDto = null;
        if(TxCodes.COUNTER_LCT_TRANSFER_TUBE_OUT.equals(counterOrderDto.getTxCode())){
            dtlOrderDto = tmsFundCounterService.querySubmitUnCheckDtlOrder(queryReqDto, null);
        }
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("checkOrder", counterOrderDto);
        body.put("checkDtlOrder", dtlOrderDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

}
