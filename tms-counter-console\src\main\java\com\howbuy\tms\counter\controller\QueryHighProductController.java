/**
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.counter.controller;

import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.enums.InvstTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService;
import com.howbuy.interlayer.product.service.high.request.QueryNotHBJGFundListRequest;
import com.howbuy.interlayer.product.service.high.response.QueryNotHBJGFundListResponse;
import com.howbuy.tms.common.enums.busi.FundStatusEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.HighProductDto;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.service.trade.TmsCounterService;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @description:(查询个高端产品新)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年2月6日 下午2:42:46
 * @since JDK 1.6
 */
@Controller
public class QueryHighProductController {

    private Logger logger = LoggerFactory.getLogger(QueryHighProductController.class);

    @Autowired
    private HighProductService highProductService;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Autowired
    @Qualifier("tmscounter.queryNotHBJGFundListService")
    private QueryNotHBJGFundListService queryNotHBJGFundListService;

    @RequestMapping("tmscounter/queryhighproductinfo.htm")
    public void queryhighproductinfo(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String productCode = request.getParameter("productCode");
        String busiType = request.getParameter("busyType");
        String invstType = request.getParameter("invstType");
        logger.info("QueryHighProductController|queryhighproductinfo|productCode:{}, busiType:{},invstType:{}", productCode, busiType, invstType);

        if (StringUtils.isEmpty(invstType)) {
            // 默认客户类型1-个人
            logger.info("QueryHighProductController|queryhighproductinfo|invstType from null to 1");
            invstType = InvstTypeEnum.INDI.getCode();
        }
        //查询产品基本信息
        HighProductInfoModel highProductBaseModel = highProductService.getHighProductInfo(productCode);
        if (highProductBaseModel == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }

        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        if (user.getInHBJG()) {
            QueryNotHBJGFundListResponse queryNotHBJGFundListResponse = queryNotHBJGFundListService.query(new QueryNotHBJGFundListRequest());
            if (queryNotHBJGFundListResponse.getFundCodes().contains(productCode)) {
                logger.info("productCode{} is HBJG", productCode);
                throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
            }
        }

        //构建高端产品信息
        HighProductDto highProductDto = buildHighProductInfo(highProductBaseModel);
        highProductDto.setTaTradeDt(getSubmitDt(productCode, busiType));

        //查询基金状态
        HighProductStatInfoModel highProductStatInfoModel = highProductService.getProductStatInfo(productCode, highProductDto.getTaTradeDt());


        if (highProductStatInfoModel != null) {
            highProductDto.setFundStat(highProductStatInfoModel.getFundStat());
            highProductDto.setBuyBusiType(convertBuyBusiType(highProductStatInfoModel.getFundStat()));
        }

        // 业务码
        String busiCode = getBusiCode(busiType, highProductStatInfoModel);
        HighProductLimitModel highProductLimitModel = queryHighProductLimit(highProductBaseModel.getFundCode(), highProductBaseModel.getShareClass(), busiCode, invstType);

        // 构建产品限额
        buildProductLimit(highProductDto, highProductLimitModel);


        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("fundInfo", highProductDto);
        rst.setBody(body);
        WebUtil.write(response, rst);
    }

    /**
     *
     * queryHighProductLimit:(产品限额查询)
     * @throws Exception
     * <AUTHOR>
     * @date 2018年6月4日 下午4:06:30
     */
    public HighProductLimitModel queryHighProductLimit(String fundCode, String shareClass, String busiCode, String invstType) throws Exception {

        logger.info("QueryHighProductController|queryHighProductLimit|params, fundCode:{}, shareClass:{}, busiCode:{}, invstType:{}", new Object[]{fundCode, shareClass, busiCode, invstType});

        // 产品限额查询
        HighProductLimitModel highProductLimitModel = highProductService.getHighProductLimitInfo(fundCode, shareClass, busiCode, invstType);

        return highProductLimitModel;
    }

    /**
     *
     * buildHighProductInfo:(构建高端产品信息)
     * @param highProductBaseModel
     * <AUTHOR>
     * @date 2018年2月6日 下午2:54:44
     */
    private HighProductDto buildHighProductInfo(HighProductBaseInfoModel highProductBaseModel) {

        HighProductDto highProductDto = new HighProductDto();
        BeanUtils.copyProperties(highProductBaseModel, highProductDto);

        return highProductDto;
    }


    public String getSubmitDt(String productCode, String busiType) throws Exception {

        if (StringUtils.isEmpty(busiType)) {
            return null;
        }

        logger.info("QueryHighProductController|getSubmitDt|productCode:{}, busiType:{}", new Object[]{productCode, busiType});

        //查询产品基本信息
        HighProductBaseInfoModel highProductBaseModel = highProductService.getHighProductBaseInfo(productCode);
        if (highProductBaseModel == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }

        //系统工作日
        String workDay = tmsCounterService.getHighSystemWorkDay();
        String appDt = workDay;
        if (StringUtils.isEmpty(appDt)) {
            appDt = workDay;
        }

        String appTm = "145959";
        boolean isSupportAdvance = isSupportAdvance(highProductBaseModel.getIsScheduledTrade(), busiType);
        logger.info("QueryHighProductAppointInfoController|isSupportAdvance:{}, productCode:{}, supportAdvanceFlag:{}, busiType:{}", new Object[]{isSupportAdvance, productCode, highProductBaseModel.getIsScheduledTrade(), busiType});

        HighProductAppointmentInfoModel productAppointmentInfoModel = null;
        if (isSupportAdvance) {
            // 查询预约开放日历
            String appDateStr = appDt + appTm;
            Date appDtm = DateUtils.formatToDate(appDateStr, DateUtils.YYYYMMDDHHMMSS);
            productAppointmentInfoModel = highProductService.getAppointmentInfoByAppointDate(productCode, busiType, highProductBaseModel.getShareClass(), TmsCounterConstant.HOWBUY_DISCODE, appDtm);
        }
        logger.info("QueryHighProductController|getSubmitDt|productCode:{}, isSupportAdvance:{}, workDay:{}", new Object[]{productCode, isSupportAdvance, workDay});

        String openDay = null;
        if (productAppointmentInfoModel != null) {
            openDay = productAppointmentInfoModel.getOpenEndDt();
        }

        if (StringUtils.isNotEmpty(openDay) && workDay.compareTo(openDay) < 0) {
            return openDay;
        }

        return workDay;

    }

    /**
     *
     * isSupportAdvance:(是否支持提前下单)
     * @param supportAdvanceFlag 0-不支持提前购买赎回 1-支持提前购买 2-支持提前赎回 3-支持提前购买赎回
     * @param busiType 业务类型 0-购买 1-赎回 
     * @return
     * <AUTHOR>
     * @date 2018年3月9日 下午3:58:49
     */
    private boolean isSupportAdvance(String supportAdvanceFlag, String busiType) {

        if (StringUtils.isEmpty(supportAdvanceFlag)) {
            return false;
        }

        // 是否支持购买提前下单
        if (BusiTypeEnum.BUY.getCode().equals(busiType)) {
            if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(supportAdvanceFlag)) {
                return true;
            }
        }

        // 是否支持赎回提前下单
        if (BusiTypeEnum.SELL.getCode().equals(busiType)) {
            if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag)) {
                return true;
            }
        }

        return false;

    }

    /**
     *
     * convertBusiType:(基金状态转换)
     * @param fundStatus
     * @return 0-认购 1-申购
     * <AUTHOR>
     * @date 2018年2月6日 下午6:02:37
     */
    private String convertBuyBusiType(String fundStatus) {
        //1-发行
        if (FundStatusEnum.IPO.getCode().equals(fundStatus)) {
            return "0";
        } else {
            return "1";
        }
    }

    /**
     *
     * getBusiCode:(获取业务码)
     * @param busiType 0-购买 1-赎回
     * @param highProductStatInfoModel
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:20:00
     */
    private String getBusiCode(String busiType, HighProductStatInfoModel highProductStatInfoModel) {

        String busiCode = null;
        if (BusiTypeEnum.BUY.getCode().equals(busiType)) {
            // 购买业务码默认申购
            busiCode = "022";
        } else {
            busiCode = "024";
        }

        if (highProductStatInfoModel == null) {
            return busiCode;
        }

        if (FundStatusEnum.IPO.getCode().equals(highProductStatInfoModel.getFundStat())) {
            // 1-发行
            busiCode = "020";
        }

        return busiCode;
    }

    private void buildProductLimit(HighProductDto highProductDto, HighProductLimitModel highProductLimitModel) {

        if (highProductLimitModel == null) {
            return;
        }
        // 首次最低申请金额（净购买金额）
        highProductDto.setNetMinAppAmt(highProductLimitModel.getMinPurchaseAmt());
        // 最低追加申请金额（净追加金额）
        highProductDto.setNetMinSuppleAmt(highProductLimitModel.getMinAddPurchaseAmt());
        highProductDto.setMaxAppAmt(highProductLimitModel.getMaxAppAmt());
    }
}

