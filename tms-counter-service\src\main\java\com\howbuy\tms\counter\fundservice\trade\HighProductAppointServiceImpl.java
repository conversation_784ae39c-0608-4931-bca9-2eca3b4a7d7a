package com.howbuy.tms.counter.fundservice.trade;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.enums.BusiTypeEnum;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description:高端产品预约日历信息查询
 * @Author: yun.lu
 * Date: 2025/6/3 15:02
 */
@Service
@Slf4j
public class HighProductAppointServiceImpl implements HighProductAppointService {
    @Autowired
    private HighProductService highProductService;

    @Autowired
    private TmsCounterService tmsCounterService;

    @Override
    public HighProductAppointmentInfoModel queryTradeProductAppointInfo(String productCode, String appDt, String appTm, String busiType) throws Exception {
        //查询产品基本信息
        HighProductBaseInfoModel highProductBaseModel = highProductService.getHighProductBaseInfo(productCode);
        if (highProductBaseModel == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_NOT_EXIST);
        }
        //系统工作日
        String workDay = tmsCounterService.getHighSystemWorkDay();
        if (StringUtils.isEmpty(appDt)) {
            appDt = workDay;
        }
        if (StringUtils.isEmpty(appTm)) {
            appTm = "145959";
        }
        boolean isSupportAdvance = isSupportAdvance(highProductBaseModel.getIsScheduledTrade(), busiType);
        HighProductAppointmentInfoModel productAppointmentInfoModel = null;
        if (isSupportAdvance) {
            //查询预约开放日历
            if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade()) || SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade())) {
                String appDateStr = appDt + appTm;
                Date appDtm = DateUtils.formatToDate(appDateStr, DateUtils.YYYYMMDDHHMMSS);
                productAppointmentInfoModel = highProductService.getAppointmentInfoByAppointDate(productCode, busiType, highProductBaseModel.getShareClass(), highProductBaseModel.getDisCode(), appDtm);
            }
        }
        return productAppointmentInfoModel;
    }

    @Override
    public String getBuySubmitTaDt(HighProductBaseInfoModel highProductBaseModel, String busiType, String appDt, String appTm, String disCode) throws Exception {
        log.info("getSubmitTaDt-查询上报日,highProductBaseModel:{}, appDt:{}, appTm:{}", JSON.toJSONString(highProductBaseModel), appDt, appTm);
        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade()) ||
                SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade())) {
            // 支持提前购买
            HighProductAppointmentInfoModel productAppointmentInfoModel = queryTradeProductAppointInfo(highProductBaseModel.getFundCode(), appDt, appTm, busiType);
            if (productAppointmentInfoModel != null) {
                if (appDt.compareTo(productAppointmentInfoModel.getOpenStartDt()) < 0) {
                    return productAppointmentInfoModel.getOpenStartDt();
                }
            }
        }
        return appDt;
    }

    @Override
    public String getBuySubmitTaDt(HighProductBaseInfoModel highProductBaseModel, String openStartDt, String appDt) {
        log.info("getSubmitTaDt-查询上报日,highProductBaseModel:{}, productAppointmentInfoModel:{}", JSON.toJSONString(highProductBaseModel), openStartDt);
        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade()) ||
                SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(highProductBaseModel.getIsScheduledTrade())) {
            // 支持提前购买
            if (openStartDt != null) {
                if (appDt.compareTo(openStartDt) < 0) {
                    return openStartDt;
                }
            }
        }
        return appDt;
    }

    /**
     * isSupportAdvance
     *
     * @param supportAdvanceFlag 0-不支持提前购买赎回 1-支持提前购买 2-支持提前赎回 3-支持提前购买赎回
     * @param busiType           业务类型 0-购买 1-赎回
     * @return
     * <AUTHOR>
     * @date 2018年3月9日 下午3:58:49
     */
    private boolean isSupportAdvance(String supportAdvanceFlag, String busiType) {
        if (StringUtils.isEmpty(supportAdvanceFlag)) {
            return false;
        }
        // 是否支持购买提前下单
        if (BusiTypeEnum.BUY.getCode().equals(busiType)) {
            if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)
                    || SupportAdvanceFlagEnum.SupportBuyAdvance.getCode().equals(supportAdvanceFlag)) {
                return true;
            }
        }
        // 是否支持赎回提前下单
        if (BusiTypeEnum.SELL.getCode().equals(busiType)) {
            return SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag) || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag);
        }
        return false;

    }
}
