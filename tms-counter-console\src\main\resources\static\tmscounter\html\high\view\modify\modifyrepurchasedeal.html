<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../../static/h-ui.admin/css/style.css" />
    <title>修改复购协议审核(高端)</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 交易审核<span class="c-gray en">&gt;</span> 修改复购协议审核（高端） <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
        <p class="main_title mt30">录入订单信息</p>
        <form action="" id="orderFormId">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tbody>
                <tr class="text-c">
                    <td>TA代码</td>
                    <td id="taCode">
                    </td>
                    <td>基金代码</td>
                    <td class="readText"> <input id ="productCode"  name="fundCode" readonly="readonly" datatype="s" errormsg=""/> </td>
                </tr>

                <tr class="text-c">
                    <td>基金名称</td>
                    <td id ="fundName">
                    </td>
                    <td>客户号</td>
                    <td class="readText"> <input id ="txAcctNo"  readonly="readonly" name="txAcctNo"/></td>
                </tr>

                <tr class="text-c">
                    <td>客户姓名</td>
                    <td id ="custName">
                    </td>
                    <td>证件号</td>
                    <td class="readText" id="idNo"></td>
                </tr>

                <tr class="text-c">
                    <td>总份额</td>
                    <td id ="balanceVol" >
                    </td>
                    <td>赎回份额</td>
                    <td class="readText" id="redeemVol"></td>
                </tr>

                <tr class="text-c">
                    <td>冻结份额</td>
                    <td class="readText" id="frznVol"></td>

                    <td>预计到期日</td>
                    <td class="readText" id="expectedDueDt"></td>
                </tr>

                <tr class="text-c">
                    <td>复购份额</td>
                    <td>
                        <input id ="appVol" name="appVol" type="text" datatype="s" errormsg=""/>
                        <input id ="repurchaseProtocolNo" name="repurchaseProtocolNo" type="hidden" datatype="s" errormsg=""/>
                        <input id ="repurchaseType" name="repurchaseType" type="hidden" datatype="s" errormsg=""/>
                    </td>

                    <td></td>
                    <td></td>


                </tr>
                <tr class="text-c">
                    <td>审核人</td>
                    <td id="checker"></td>
                    <td>驳回原因</td>
                    <td id="memo"></td>
                </tr>

                </tbody>
            </table>
        </div>
        </form>

        <p class="mt30 text-c" id="submitDiv">
            <a href="javascript:void(0)" id ="modifyBtn" class="btn radius btn-warning ml30">提交</a>
            <a href="javascript:void(0)" id ="cancelBtn" class="btn radius btn-secondary ml30">作废</a>
            <a href="javascript:void(0)" id ="backBtn" class="btn radius btn-success ml30">返回</a>
        </p>
    </div>
    
    <script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../../js/baseconfig.js"></script>
    <script type="text/javascript" src="../../../../js/common.js"></script>
    <script type="text/javascript" src="../../../../js/config.js"></script>
    <script type="text/javascript" src="../../../../js/commonutil.js"></script>
    <script type="text/javascript" src="../../../../js/valid.js"></script>
    <script type="text/javascript" src="../../../../js/high/conscode.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/custinfo.js"></script>
    <script type="text/javascript" src="../../../../js/high/query/queryhighproductinfosubpage.js"></script>
    <script type="text/javascript" src="../../../../js/high/query/queryhighproduct.js"></script>
    <script type="text/javascript" src="../../../../js/high/check/viewcounterdeal.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/onlineorderfile.js"></script>
    <script type="text/javascript" src="../../../../js/high/modify/modify.js"></script>
    <script type="text/javascript" src="../../../../js/high/modify/modifyrepurchase.js"></script>
    <script type="text/javascript" src="../../../../js/high/common/init.js"></script>

</body>

</html>