/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto.common;

import java.io.Serializable;

/**
 * @description:(其他信息) 
 * <AUTHOR>
 * @String 2017年5月2日 下午6:08:45
 * @since JDK 1.6
 */
public class OtherInfoDto implements Serializable{
    
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = -491110424801651938L;

    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是)
     */
    private String agentFlag;
    
    /**
     * 投顾代码
     */
    private String consCode;
    
    String appDtm ;
    
    /**
     * 强制取消
     */
    private String cancelMemo;

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getAppDtm() {
        return appDtm;
    }

    public void setAppDtm(String appDtm) {
        this.appDtm = appDtm;
    }

    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }
    
}

