package com.howbuy.tms.counter.dto;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/30 19:50
 * @since JDK 1.8
 */
public class QueryRedmProductListReqDto implements Serializable {

    private String txAcctNo;

    private String disCode;

    private String protocolNo;

    private String productCode;

    private String appRatio;

    private String cpAcctNo;

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAppRatio() {
        return appRatio;
    }

    public void setAppRatio(String appRatio) {
        this.appRatio = appRatio;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }
}