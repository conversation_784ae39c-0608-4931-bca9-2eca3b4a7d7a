/**
*修改审核订单
*<AUTHOR>
*@date 2018-03-26 17:22
**/

$(function(){
	
	//禁用所有input,不含复核信息输入框
	CommonUtil.disableAllInput();
	
	var viewType = CommonUtil.getParam("viewType");

	OnLineOrderFile.selfLogin(CommonUtil.getParamJson());
	
	// 初始化按钮
	Modify.initBtn(viewType);
	
	// 初始化下拉框
	Modify.initSelect();
	
});
 var Modify = {
     //viewType 0-查看；1-审核；2-修改
     viewTypeAndCheckNodeMap:{
         "0":OnLineOrderFile.CRM_OP_CHECK_NODE_VIEW,
         "1":OnLineOrderFile.CRM_OP_CHECK_NODE_RE,
         "2":OnLineOrderFile.CRM_OP_CHECK_NODE_MODIFY
     },
	 getCheckNode:function(){
         var viewType = CommonUtil.getParam("viewType");
         var checkNode = Modify.viewTypeAndCheckNodeMap[viewType] || OnLineOrderFile.CRM_OP_CHECK_NODE_MODIFY;
         return checkNode;
	 },

     /**
      * 查询柜台订单
      * @param dealAppNo
      */
     queryCounterDealOrder: function (succFun, completeFun) {
         var dealAppNo = CommonUtil.getParam("dealAppNo");
         var viewType = CommonUtil.getParam("viewType") || '';
         var uri = TmsCounterConfig.HIGH_VIEW_CHECKORDER;

         var reqparamters = {};
         reqparamters.dealAppNo = dealAppNo;
         reqparamters.checkNode = this.viewTypeAndCheckNodeMap[viewType];
         var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
         CommonUtil.ajaxAndCallBack(paramters, succFun, completeFun);
     },
    
	initBtn:function(viewType){
		
		//隐藏按钮
		CommonUtil.higdeAllBtnOfDiv("submitDiv");
		
		
		if("2" == viewType){
			
			//显示修改按钮
			$("#modifyBtn").show();
			//显示作废按钮
			$("#cancelBtn").show();
			
			//修改
			$("#modifyBtn").on('click', function(){
				Modify.modifyConfirm("5");  // 修改 checkType 1-审核通过 3-审核驳回 5-修改4-作废
			});
			
			//作废
			$("#cancelBtn").on('click', function(){
				Modify.modifyConfirm("4");// 作废
			});
		}
		
		//查看
		if("0" == viewType){
			
			// 隐藏修改按钮
			$("#modifyBtn").hide();
			//显示作废按钮
			$("#cancelBtn").hide();
			
			// 显示返回按钮
			$("#backBtn").show();
			
			// 返回
			$("#backBtn").on("click", function(){
				parent.layer.closeAll();
			});
		}
		
	},
	
	/***
	 * checkType  审核状态 1-审核通过 2-审核驳回
	 * checkedOrder 待审核订单
	 * 修改确认
	 */	
	modifyConfirm : function(checkType){
		CommonUtil.disabledBtn("modifyBtn");
		if(Modify.checkedClick == '1'){
			return false;
		}
		
		//防止重复点击
		Modify.checkedClick = '1';
		
		//购买表单参数校验
		var validRst = Valid.valiadateFrom($("#orderFormId"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("modifyBtn");
			Modify.checkedClick = '0';
			return false;
		}
		
		
		var modifyForm = Modify.buildDealForm("orderFormId");// 修改表单
		modifyForm.dealAppNo = Modify.modifyDealOrder.dealAppNo;// 柜台订单号
		
		var  uri= TmsCounterConfig.MODIFY_CHECK_ORDER_URL||  '';
		var reqparamters ={"checkType":checkType,
				            "modifyForm":JSON.stringify(modifyForm),
                             "materialinfoForm":JSON.stringify(OnLineOrderFile.buildOrderCheckFile())
				           };
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Modify.callBack);
		return true;
	},
	callBack:function(data){
		CommonUtil.enabledBtn("modifyBtn");
		Modify.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
            layer.confirm('成功', {
                btn: ['确定'] //按钮
            }, function(){
            	layer.closeAll();
            	if(OnLineOrderFile.isCrm()){
            		CommonUtil.closeCurrentUrl();// 关闭当前页面
            	}else{
            		// 刷新父页面
					window.parent.location.reload();
					// 获取窗口索引
					var index = parent.layer.getFrameIndex(window.name);
					// 关闭弹窗
					parent.layer.close(index);
            	}

            });

		}else{
			if("Z3000116" == respCode){
				CommonUtil.layer_tip("产品极差为零，请确认");
			}else{
				CommonUtil.layer_tip("提交失败，"+respDesc);
			}
		}
	},
	/**
	 * 初始化投顾列表
	 */
	initSelect:function(){
		//初始化投顾列表
		var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
		$(".selectconsCode").html(selectConsCodesHtml);
	},
	/**
	 * 构建修改订单信息
	 * @param formId
	 */
	buildDealForm:function(formId){
		var orderForm = {};
		var subOrders = [];// 合并上报子订单
		$("#"+formId).find('input').each(function(index, element){
			 var elementName = $(element).attr('name');
			 var elementValue = $(element).val();
			var cpAcctNo = $(element).attr('cpacctno');

			 if('appAmt' == elementName){
				 orderForm[elementName] = CommonUtil.unFormatAmount(elementValue);
			 }
			else if('subsAmt' == elementName){
				orderForm[elementName] = CommonUtil.unFormatAmount(elementValue);
			}
			 else if('transferPrice' == elementName){
				 orderForm[elementName] = CommonUtil.unFormatAmount(elementValue);
			 }


			 else if('appVol' == elementName){
			 	let value = CommonUtil.unFormatAmount(elementValue);
				 orderForm[elementName] = value;
				 if (!isEmpty(cpAcctNo)) {
				 	subOrders.push({"cpAcctNo":cpAcctNo,"appVol":value});
				 }
			 }else{
				 orderForm[elementName] = elementValue;
			 }
		});
		orderForm["subOrders"] = subOrders;
		
		$("#"+formId).find('select').each(function(index, element){
			 var elementName = $(element).attr('name');
			 var elementValue = $(element).val();
			 if('cpAcctNo' == elementName){
				 orderForm[elementName] = elementValue;
				 orderForm['bankCode'] = $(element).find("option:selected").attr('bankcode');
				 orderForm['bankAcct'] = $(element).find("option:selected").attr('bankacct');
			 }else{
				 orderForm[elementName] = elementValue;
			 }
			 
		});
		
		$("#"+formId).find('textarea').each(function(index, element){
			var elementName = $(element).attr('name');
			var elementValue = $(element).val();
			orderForm[elementName] = elementValue;
		});
		
		
		return orderForm;
	},
     /**
      *
      * @Description  初始化材料
      *
      * @param null
      * @return
      * <AUTHOR>
      * @Date 2019/5/31 17:30
      **/
     getSelectCustMaterial:function(checkOrder, crmBusiType){
         var appointList = checkOrder.appointList || [];//投顾预约信息
         var custInfofiList = checkOrder.custInfofiList || [];//客户信息
         var order = checkOrder.counterOrderDto;//柜台订单信息


         var custInfo = {};
         if(custInfofiList.length > 0){
             custInfo = custInfofiList[0] || {};//客户信息
         }

         var appointment = {};
         if(appointList.length > 0 ){
             appointment = appointList[0] || {};// 投顾预约信息
         }
         var custSelectOrder = {};
         custSelectOrder["hboneno"] = custInfo.hboneNo;// 一账通帐号
         custSelectOrder["pcode"] =  order.fundCode;// 产品代码
         custSelectOrder["preid"] =  appointment.appointId;// 预约ID
         custSelectOrder["busiid"] = crmBusiType;// 业务类型ID // CRM业务类型 购买
         return custSelectOrder;
     }
	
};
