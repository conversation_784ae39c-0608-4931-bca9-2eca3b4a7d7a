/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import com.howbuy.tms.counter.dto.base.BaseResponseDto;

/**
 * @description:(查询基金持有明细) 
 * <AUTHOR>
 * @date 2017年4月12日 下午8:01:23
 * @since JDK 1.6
 */
public class QueryAcctBalanceDtlRespDto extends BaseResponseDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 1L;

    private String taTradeDt;
    private List<DtlBean> balanceDtlList = new ArrayList<DtlBean>();

    /**
     * 产品是否支持赎回到银行卡 0-不支持 1-支持
     */
    private String redeemDirectionIsSupCardFlag;
    /**
     * 产品是否支持赎回到储蓄罐 0-不支持 1-支持
     */
    private String redeemDirectionIsSupCxgFlag;

    public String getRedeemDirectionIsSupCardFlag() {
        return redeemDirectionIsSupCardFlag;
    }

    public void setRedeemDirectionIsSupCardFlag(String redeemDirectionIsSupCardFlag) {
        this.redeemDirectionIsSupCardFlag = redeemDirectionIsSupCardFlag;
    }

    public String getRedeemDirectionIsSupCxgFlag() {
        return redeemDirectionIsSupCxgFlag;
    }

    public void setRedeemDirectionIsSupCxgFlag(String redeemDirectionIsSupCxgFlag) {
        this.redeemDirectionIsSupCxgFlag = redeemDirectionIsSupCxgFlag;
    }

    public List<DtlBean> getBalanceDtlList() {
        return balanceDtlList;
    }

    public void setBalanceDtlList(List<DtlBean> balanceDtlList) {
        this.balanceDtlList = balanceDtlList;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public static class DtlBean implements Serializable {

        /**
         * serialVersionUID:TODO（用一句话描述这个变量表示什么）
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = -6776763701201056826L;
        /**
         * 交易账号
         */
        private String txAcctNo;
        /**
         * 分销机构号
         */
        private String disCode;

        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 份额类型
         */
        private String fundShareClass;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 产品类型
         */
        private String productType;

        /**
         * 净值
         */
        private BigDecimal nav;
        /**
         * 产品状态：0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记； 8-红利发放；
         * 9-基金封闭； a-基金终止
         */
        private String productStatus;

        /**
         * 净值日期
         */
        private String navDt;
        /**
         * 购买状态：1-认购，2-申购，3-不可购买
         */
        private String buyStatus;
        /**
         * 赎回状态 1-可赎回，2-不可赎回
         */
        private String redeemStatus;
        
        /**
         * 协议号
         */
        private String protocolNo;
        /**
         * 协议类型
         */
        private String protocolType;
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 银行代码
         */
        private String bankCode;
        /**
         * 银行名称
         */
        private String bankName;
        /**
         * 银行卡号
         */
        private String bankAcctNo;
        /**
         * 总份额
         */
        private BigDecimal balanceVol;
        /**
         * 可用份额
         */
        private BigDecimal availVol;
        /**
         * 冻结份额(待确认份额+申请份额)
         */
        private BigDecimal unconfirmedVol;
        /**
         * 待确认金额
         */
        private BigDecimal unconfirmedAmt;
        /**
         * 市值
         */
        private BigDecimal marketValue;
        
        /**
         * 开放赎回日期
         */
        private String openRedeDt;
        
        /**
         * 是否可以全赎：1:是(可以);0:否(不可以);
         */
        private String allRedeemFlag;
        /**
         * 锁定份额
         */
        private BigDecimal lockVol;

        private String productChannel;
        /**
         * 认缴金额
         */
        private BigDecimal subscribeAmt;

        /**
         * 转托管方式,1-一次转托管；2-两次转托管
         */
        private String chgTrusteeMode;

        /**
         * 基金类型，0-股票型1-混合型2-债券型3-货币型4-QDII5-封闭式6-结构型7-一对多专户8-券商资管产品_大集合9-券商资管产品_小集合
         */
        private String fundType;
        /**
         * 基金二级类型，01-指数型；21-理财型
         */
        private String fundSubType;

        /**
         * 税延基金标识，0或空-否，1-是
         */
        private String taxDelayFlag;

        /**
         * 产品类别
         */
        private String productClass;

        /**
         * TA代码
         */
        private String taCode;

        public String getProductClass() {
            return productClass;
        }

        public void setProductClass(String productClass) {
            this.productClass = productClass;
        }

        public String getTaCode() {
            return taCode;
        }

        public void setTaCode(String taCode) {
            this.taCode = taCode;
        }

        public String getTaxDelayFlag() {
            return taxDelayFlag;
        }

        public void setTaxDelayFlag(String taxDelayFlag) {
            this.taxDelayFlag = taxDelayFlag;
        }

        public String getChgTrusteeMode() {
            return chgTrusteeMode;
        }

        public void setChgTrusteeMode(String chgTrusteeMode) {
            this.chgTrusteeMode = chgTrusteeMode;
        }

        public String getFundType() {
            return fundType;
        }

        public void setFundType(String fundType) {
            this.fundType = fundType;
        }

        public String getFundSubType() {
            return fundSubType;
        }

        public void setFundSubType(String fundSubType) {
            this.fundSubType = fundSubType;
        }

        public BigDecimal getSubscribeAmt() {
            return subscribeAmt;
        }

        public void setSubscribeAmt(BigDecimal subscribeAmt) {
            this.subscribeAmt = subscribeAmt;
        }

        public String getTxAcctNo() {
            return txAcctNo;
        }
        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }
        public String getDisCode() {
            return disCode;
        }
        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }
        public String getProductCode() {
            return productCode;
        }
        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }
        public String getFundShareClass() {
            return fundShareClass;
        }
        public void setFundShareClass(String fundShareClass) {
            this.fundShareClass = fundShareClass;
        }
        public String getProductName() {
            return productName;
        }
        public void setProductName(String productName) {
            this.productName = productName;
        }
        public String getProductType() {
            return productType;
        }
        public void setProductType(String productType) {
            this.productType = productType;
        }
        public BigDecimal getNav() {
            return nav;
        }
        public void setNav(BigDecimal nav) {
            this.nav = nav;
        }
        public String getProductStatus() {
            return productStatus;
        }
        public void setProductStatus(String productStatus) {
            this.productStatus = productStatus;
        }
        public String getNavDt() {
            return navDt;
        }
        public void setNavDt(String navDt) {
            this.navDt = navDt;
        }
        public String getBuyStatus() {
            return buyStatus;
        }
        public void setBuyStatus(String buyStatus) {
            this.buyStatus = buyStatus;
        }
        public String getRedeemStatus() {
            return redeemStatus;
        }
        public void setRedeemStatus(String redeemStatus) {
            this.redeemStatus = redeemStatus;
        }
        public String getProtocolNo() {
            return protocolNo;
        }
        public void setProtocolNo(String protocolNo) {
            this.protocolNo = protocolNo;
        }
        public String getProtocolType() {
            return protocolType;
        }
        public void setProtocolType(String protocolType) {
            this.protocolType = protocolType;
        }
        public String getCpAcctNo() {
            return cpAcctNo;
        }
        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }
        public String getBankCode() {
            return bankCode;
        }
        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }
        public String getBankName() {
            return bankName;
        }
        public void setBankName(String bankName) {
            this.bankName = bankName;
        }
        public String getBankAcctNo() {
            return bankAcctNo;
        }
        public void setBankAcctNo(String bankAcctNo) {
            this.bankAcctNo = bankAcctNo;
        }
        public BigDecimal getBalanceVol() {
            return balanceVol;
        }
        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }
        public BigDecimal getAvailVol() {
            return availVol;
        }
        public void setAvailVol(BigDecimal availVol) {
            this.availVol = availVol;
        }
        public BigDecimal getUnconfirmedVol() {
            return unconfirmedVol;
        }
        public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
            this.unconfirmedVol = unconfirmedVol;
        }
        public BigDecimal getUnconfirmedAmt() {
            return unconfirmedAmt;
        }
        public void setUnconfirmedAmt(BigDecimal unconfirmedAmt) {
            this.unconfirmedAmt = unconfirmedAmt;
        }
        public BigDecimal getMarketValue() {
            return marketValue;
        }
        public void setMarketValue(BigDecimal marketValue) {
            this.marketValue = marketValue;
        }
        public String getOpenRedeDt() {
            return openRedeDt;
        }
        public void setOpenRedeDt(String openRedeDt) {
            this.openRedeDt = openRedeDt;
        }
        public String getAllRedeemFlag() {
            return allRedeemFlag;
        }
        public void setAllRedeemFlag(String allRedeemFlag) {
            this.allRedeemFlag = allRedeemFlag;
        }

        public BigDecimal getLockVol() {
            return lockVol;
        }

        public void setLockVol(BigDecimal lockVol) {
            this.lockVol = lockVol;
        }

        public String getProductChannel() {
            return productChannel;
        }

        public void setProductChannel(String productChannel) {
            this.productChannel = productChannel;
        }


    }
    
}

