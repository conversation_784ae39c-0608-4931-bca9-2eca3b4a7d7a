/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.google.common.collect.Lists;
import com.howbuy.tms.batch.facade.enums.WorkdayTypeEnum;
import com.howbuy.tms.batch.facade.query.queryworkday.QueryWorkdayResponse;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterQueryOrderReqDto;
import com.howbuy.tms.counter.dto.QueryAppRateChangeResDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Controller
public class AppRateChangeController extends AbstractController {
	
    private Logger logger = LogManager.getLogger(AppRateChangeController.class);
    
	@RequestMapping("/tmscounter/queryAppRateChange.htm")
    public ModelAndView queryAppRateChange(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String busiCodes = request.getParameter("selectCheckFlag");
        String paymemtTypes = request.getParameter("selectPayFlag");
        String beginDtm = request.getParameter("beginDtm");
        String endDtm = request.getParameter("endDtm");
        String pageNum = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        
        logger.info("AppRateChangeController|queryAppRateChange|fundCode:{}, busiCodes:{}, paymemtTypes:{}, beginDtm:{}, endDtm:{}", 
        		fundCode, busiCodes, paymemtTypes, beginDtm, endDtm);
        
        String [] busiCodesArrays = busiCodes.split("-");
        String [] paymemtTypesArrays = paymemtTypes.split("-");
        
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setFundCode(fundCode);
        queryReqDto.setBusiCodes(Lists.newArrayList(busiCodesArrays));
        queryReqDto.setPaymemtTypes(Lists.newArrayList(paymemtTypesArrays));
        queryReqDto.setTaTradeDtStart(beginDtm);
        queryReqDto.setTaTradeDtEnd(endDtm);
        queryReqDto.setPageNo(Integer.parseInt(pageNum));
        queryReqDto.setPageSize(Integer.parseInt(pageSize));
        DisInfoDto disDto = new DisInfoDto();
        disDto.setDisCode(DisCodeEnum.HM.getCode());
        
        QueryAppRateChangeResDto resDto = tmsFundCounterService.queryAppRateChange(queryReqDto, disDto);
        
        QueryWorkdayResponse workdayResponse =
                tmsCounterOutService.querySysWorkday(WorkdayTypeEnum.SYS_TYPE, SysCodeEnum.BATCH_GM, null);

        String workday = workdayResponse.getWoryday();
        String nextWorkday = workdayResponse.getNextWorkDay();
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("queryAppRateChangeList", resDto.getQueryAppRateChangeList());
        body.put("totalPage", resDto.getTotalPage());
        body.put("pageNum", resDto.getPageNo());
        body.put("totalCount", resDto.getTotalCount());
        body.put("workday", workday);
        body.put("nextWorkday", nextWorkday);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    
	@RequestMapping("/tmscounter/modifyAppRateChange.htm")
    public ModelAndView modifyAppRateChange(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = request.getParameter("fundCode");
        String busiCodes = request.getParameter("selectCheckFlag");
        String paymemtTypes = request.getParameter("selectPayFlag");
        String beginDtm = request.getParameter("beginDtm");
        String endDtm = request.getParameter("endDtm");
        String afterDiscountRate = request.getParameter("afterDiscountRate");
        
        logger.info("AppRateChangeController|modifyAppRateChange|fundCode:{}, mBusiCode:{}, paymemtType:{}, beginDtm:{}, endDtm:{}, afterDiscountRate:{}", 
        		fundCode, busiCodes, paymemtTypes, beginDtm, endDtm, afterDiscountRate);
        
        String [] busiCodesArrays = busiCodes.split("-");
        String [] paymemtTypesArrays = paymemtTypes.split("-");
        
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        
        CounterQueryOrderReqDto queryReqDto = new CounterQueryOrderReqDto();
        queryReqDto.setFundCode(fundCode);
        queryReqDto.setBusiCodes(Lists.newArrayList(busiCodesArrays));
        queryReqDto.setPaymemtTypes(Lists.newArrayList(paymemtTypesArrays));
        queryReqDto.setTaTradeDtStart(beginDtm);
        queryReqDto.setTaTradeDtEnd(endDtm);
        queryReqDto.setAfterDiscountRate(new BigDecimal(afterDiscountRate));
        queryReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        DisInfoDto disDto = new DisInfoDto();
        disDto.setDisCode(DisCodeEnum.HM.getCode());
        
        boolean modifyFlag = tmsFundCounterService.counterModifyDicount(queryReqDto, disDto);
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("modifyFlag", modifyFlag?YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
}