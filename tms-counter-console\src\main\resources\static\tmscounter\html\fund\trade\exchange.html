<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>基金转换</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 零售业务 <span class="c-gray en">&gt;</span> 投资交易类<span class="c-gray en">&gt;</span> 交易申请<span class="c-gray en">&gt;</span> 基金转换 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <input name="是否包含预约信息;默认false" id="isContainAppointmentFlag" type="hidden" value = "false">
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
                <p class="mainTitle mt10">基金转换</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="custNo" id="custNo"  placeholder="双击查询客户号">
                    <span class="normal_span ml30">证件号：</span>
                    <input name="idNo" id="idNo" type="text" placeholder='请输入'>
                    <span class="normal_span ml30">分销机构：</span>
                    <span class="select-box inline">
                       <select name="disCode" class="select" id="selectDisCode">
                       </select>
                    </span>
                    <a href="javascript:void(0)" id="queryCustInfoBtn" class="btn radius btn-secondary ml30">查询</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="page-container w1000">
        <p class="main_title">客户基本信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>选择</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>客户类型</th>
                        <th>客户状态</th>
                        <th>证件类型</th>
                        <th>证件号</th>
                        <th>投资者类型</th>                   
                        <th>风险等级</th>
                        <th>分销机构</th>
                    </tr>
               </thead>
                <tbody id="custInfoId">
                    <tr class="text-c">
                    	<td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                        <td>--</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <form action="" id="exchangeConfirmForm">
        <p class="main_title mt30">录入订单信息</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
             	<thead>
                   <tr class="text-c">
                  		<th>选择</th>
                   		<th>转出基金代码</th>
                   		<th>转出基金简称</th>
                   		<th>转出基金状态</th>
                        <th>银行账户</th>
                        <th>当前可用份额</th>
                        <th>可赎回日期</th>
                        <th>申请转出份额（份）</th>
                        <th>申请转出份额（大写）</th>
                        <th>协议号</th>
                        <th>协议类型</th>
                        <th>转入基金代码</th>
                        <th>转入基金简称</th>
                        <th>转入基金代码</th>
                        <th>转入风险等级</th>
                        <th>巨额赎回顺延标记</th>
                    </tr>
               	</thead>
                <tbody id="exchangeOrderInfoId">
                	<!-- exchange order -->
                </tbody>
            </table>
        </div>
        </form>

        <p class="main_title mt30" id="uploadFileText" style="display: none;">录入双录材料</p>
        <form id="uploadFileForm" name="uploadFileForm" action="" method="post" enctype="multipart/form-data" style="display: none;">
            <div class="info">
                <span>音频/视频材料：</span>
                <input type="text" id="fileNameShow" filePath="" style="height: 28px;font-size: 14px;border: 1px solid #ccc;padding: 0 4px;border-radius: 3px;" />
                <span style="color:red;margin-left: 10px;margin-right: 30px;">*视频大小限制300M</span>

                <input type="file" name="videoFile" id="videoFile" multiple="multiple" style="display:none;" />
                <input type="button" value="浏览" class="btn radius btn-secondary" onclick="document.uploadFileForm.videoFile.click()" />
                <input type="text" name="fileName" id="fileName" style="border: none;width: 10px;color: #CCCCCC" readonly />

                <input type="button" value="上传" id="fileSubmit" class="btn radius btn-secondary" style="background-color: #4F9F8F" />
            </div>
        </form>

       <p class="main_title mt30">其他信息</p>
       <form id="transactorInfoForm" >
       <div class="result2_tab">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
         		<tr class="text-c">
         			<td>下单日期</td>
                    <td>
                        <input class="input-text laydate-icon" id="appDt" name="appDt" isnull="false" datatype="s" errormsg="下单日期" maxlength = "8" readonly="true">
                    </td>
                    <td>下单时间</td>
                    <td>
                        <input class="input-text laydate-icon"  type="text" id="appTm" name="appTm" isnull="false" datatype="s" errormsg="下单时间" maxlength = "6">
                    </td>
                    <td>&nbsp;</td>
               		<td>&nbsp;</td>
         		</tr>
         		<tr class="text-c">
         			<td>网点：</td>
         			<td>中台柜台<input type="hidden" name="outletCode" value="W20170215"/></td>
         			<td>投资顾问代码：</td>
         			<td>
         			 	<span class="select-box inline">
                			<select name="consCode" class="select selectconsCode"></select>
            			</span>
         			</td>
         			<td>是否经办：</td>
         			<td>
         				 <span class="select-box inline">
		           			<select name="agentFlag" class="select selectAgened">
		              			<option value="0">否</option>
		             		 	<option value="1">是</option>
		           			</select>
		           		</span>
         			</td>
         		</tr>
         	</tbody>
         </table>
        </div>
        
       <!-- 是否经办: 个人用户默认为否, 机构客户默认为是, 为是时显示经办人信息 -->
       <div class="result2_tab" id="agentInfoDiv" style="display: none;">
        <p class="main_title mt20">经办人信息</p>
         <table class="table table-border table-bordered table-hover table-bg table-sort">
         	<tbody>
            	<tr class="text-c">
                   	<td>经办人姓名：</td>
                   	<td>
                   		<input type="text" placeholder="请输入"  name="transactorName"  datatype="s" errormsg="经办人姓名">
                   	</td>
                   	<td>经办人证件类型：</td>
              		<td>
                  		<span class="select-box inline">
                    		<select name="transactorIdType" class="select selectTransactorIdType"   datatype="s" errormsg="经办人证件类型" >
                    		</select>
                		</span>
                	</td>
                </tr>
             	<tr class="text-c">
              		<td>经办人证件号：</td>
              		<td> <input type="text" placeholder="请输入"  name="transactorIdNo"  datatype="s" errormsg="经办人证件号" ></td>
               		<td>&nbsp;</td>
               		<td>&nbsp;</td>
               	</tr>
            </tbody>
          </table>
        </div>
        </form>

        <div id="queryAgreementMainId" class="cp_top mt10" style="display: none;">
            <span class="normal_span red">请注意该基金需要签署：</span>
            <span id="queryAgreementId"></span>
        </div>

        <p class="mt30">
            <a href="javascript:void(0)" class="btn radius btn-secondary" id="confimExchangeBtn">确认提交</a>
        </p>
    </div>

    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
   <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfosubpage.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/queryfundinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/query/queryfunddivinfo.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/common/validate.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/trade/exchange.js?v=20240511"></script>
    <script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
</body>

</html>