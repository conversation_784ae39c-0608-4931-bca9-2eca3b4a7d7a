/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.util.List;

import com.howbuy.tms.counter.dto.base.BaseRequestDto;

/**
 * 
 * @description:(查询客户基金银行卡持有持仓份额)
 * <AUTHOR>
 * @date 2018年5月4日 上午10:44:25
 * @since JDK 1.6
 */
public class QueryCustBankBalVolReqDto extends BaseRequestDto {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = 3707631714663064961L;
    
    /**
     * 份额业务类型
     */
    private String shareType;

    /**
     * 客户号
     */
    private String custNo;
    
    /**
     * 证件号
     */
    private String idNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    /**
     * 资金账号列表
     */
    private List<String> cpAcctNos;
    
    /**
     * 银行卡号
     */
    private String bankAcct;
    
    public List<String> getCpAcctNos() {
        return cpAcctNos;
    }

    public void setCpAcctNos(List<String> cpAcctNos) {
        this.cpAcctNos = cpAcctNos;
    }

    public String getCpAcctNo() {
		return cpAcctNo;
	}

	public void setCpAcctNo(String cpAcctNo) {
		this.cpAcctNo = cpAcctNo;
	}

	public String getShareType() {
        return shareType;
    }

    public void setShareType(String shareType) {
        this.shareType = shareType;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }
}
