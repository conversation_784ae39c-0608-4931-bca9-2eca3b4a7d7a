/**
*配置信息
*<AUTHOR>
*@date 2017-03-28 10:45
*/



/**
 * 首页
 */
TmsCounterConfig.INDEX_URL = TmsCounterConfig.BASEURL+'tmscounter/indexparam.htm';
/**
 * 查看个人信息
 */
TmsCounterConfig.QUERY_LOGIN_USER_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/queryLoginUserInfo.htm';
/**
 * 修改密码
 */
TmsCounterConfig.MODIFY_PWD_INDEX_URL = TmsCounterConfig.BASEURL+'tmscounter/modifyPwd.htm';
/**
 *校验风险等级 
 */
TmsCounterConfig.VALIDATOR_RETAIL_RISK_LEVEL_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/validatorretailrisklevel.htm';
/**
 *校验风险等级
 */
TmsCounterConfig.VALIDATOR_REGULAR_RISK_LEVEL_URL = TmsCounterConfig.BASEURL+'tmscounter/regular/validatorretailrisklevel.htm';



/**
 * 购买
 */
TmsCounterConfig.BUY_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/buyconfirm.htm';
/**
 * 零售购买
 */
TmsCounterConfig.BUY_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/buyconfirm.htm';
/**
 * 定期购买
 */
TmsCounterConfig.BUY_REGULAR_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/regular/buyconfirm.htm';
/**
 * 赎回
 */
TmsCounterConfig.SELL_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/sellconfirm.htm';
/**
 * 非交易过户
 */
TmsCounterConfig.NO_TRADE_OVER_ACCOUNT_URL = TmsCounterConfig.BASEURL+'tmscounter/notradeoveraccountconfirm.htm';

/**
 * 零售赎回
 */
TmsCounterConfig.SELL_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/sellconfirm.htm';

/**
 * 投顾赎回
 */
TmsCounterConfig.SELL_ADVISER_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/adviser/sell.htm';


/**
 * 自建组合赎回
 */
TmsCounterConfig.SELL_SELF_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/self/sell.htm';


/**
 * 零售赎回-当日确认份额校验
 */
TmsCounterConfig.SELL_FUND_TADTACKVOL_VALIDATE_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/validateCurTaDtAckVol.htm';
/**
 * 基金转换
 */
TmsCounterConfig.EXCHANGE_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/exchangeconfirm.htm';
/**
 * 基金转换-当日确认份额校验
 */
TmsCounterConfig.EXCHANGE_FUND_TADTACKVOL_VALIDATE_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/validateExgCurTaDtAckVol.htm';
/**
 * 高端查询客户持有基金赎回信息
 */
TmsCounterConfig.QUERY_FUND_REDEEM_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/queryfundredeeminfo.htm';
/**
 * 零售查询客户持有赎回基金订单信息
 */
TmsCounterConfig.FUND_QUERY_FUND_REDEEM_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/queryfundredeeminfo.htm';
/**
 * 零售查询客户持有赎回投顾订单信息
 */
TmsCounterConfig.QUERY_ADVISER_REDEEM_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/adviser/queryadviserredeeminfo.htm';

/**
 * 自建组合赎回试算
 */
TmsCounterConfig.QUERY_SELF_REDEEM_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/self/queryselfredeeminfo.htm';

/**
 * 自建组合赎回比例限额
 */
TmsCounterConfig.QUERY_SELF_REDEEM_LIMIT_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/self/querycustomsellratio.htm';




/**
 * 零售查询客户持有可转换基金订单信息
 */
TmsCounterConfig.FUND_QUERY_EXCHANGE_FUND_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/queryExchangeFundInfo.htm';

/**
 * 零售查询客户基金持仓信息
 */
TmsCounterConfig.FUND_QUERY_CUST_HODL_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/queryCustHodlInfo.htm';

/**
 * 上传双录文件
 */
TmsCounterConfig.UPLOAD_DOUBLE_RECORD_FILE_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/uploadDoubleRecordFile.htm';

/**
 *  获取双录文件
 */
TmsCounterConfig.GET_DOUBLE_RECORD_FILE_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/getDoubleRecordFile.htm';

/**
 * 查询可撤单订单
 */
TmsCounterConfig.QUERY_CAN_CANCEL_URL = TmsCounterConfig.BASEURL+'tmscounter/querycancancel.htm';

/**
 * 查询零售可撤单订单
 */
TmsCounterConfig.QUERY_FUND_CAN_CANCEL_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/querycancancel.htm';

TmsCounterConfig.QUERY_REGULAR_CAN_CANCEL_URL = TmsCounterConfig.BASEURL+'tmscounter/regular/querycancancel.htm';

TmsCounterConfig.QUERY_FUND_CAN_CANCEL_FOR_APPLY_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/querycancancelforapply.htm';

TmsCounterConfig.QUERY_REGULAR_CAN_CANCEL_FOR_APPLY_URL = TmsCounterConfig.BASEURL+'tmscounter/regular/querycancancelforapply.htm';
/**
 * 查询可修改回款方向订单
 */
TmsCounterConfig.QUERY_CAN_MODIFY_DIRECTION_DEAL = TmsCounterConfig.BASEURL+'tmscounter/fund/querymodifyredeemdirection.htm';
/**
 * 查询回款方向
 */
TmsCounterConfig.QUERY_MODIFY_DIRECTION = TmsCounterConfig.BASEURL+'tmscounter/fund/querydealorderdtl.htm';
/**
 * 查询可强撤订单
 */
TmsCounterConfig.QUERY_CAN_FORCE_CANCEL_URL = TmsCounterConfig.BASEURL+'tmscounter/querycanforcecancel.htm';

/**
 * 撤单
 */
TmsCounterConfig.CANCEL_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/cancelconfirm.htm';
/**
 * 零售撤单
 */
TmsCounterConfig.CANCEL_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/cancelconfirm.htm';
/**
 * 定期撤单
 */
TmsCounterConfig.CANCEL_REGULAR_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/regular/cancelconfirm.htm';
/**
 * 强制撤单
 */
TmsCounterConfig.FORCE_CANCEL_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/forcecancelconfirm.htm';

/**
 * 查询用户基金分红方式
 */
TmsCounterConfig.QUERY_MODIFY_DIV_URL = TmsCounterConfig.BASEURL+'tmscounter/querymodifydiv.htm';
/**
 * 查询用户基金分红方式
 */
TmsCounterConfig.QUERY_FUND_MODIFY_DIV_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/querymodifydiv.htm';
/**
 * 修改分红方式确认
 */
TmsCounterConfig.MODIFYDIV_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/modifydivconfirm.htm';
/**
 * 零售修改分红方式确认
 */
TmsCounterConfig.MODIFYDIV_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/modifydivconfirm.htm';
/**
 * 零售多交易账号修改分红方式确认
 */
TmsCounterConfig.MULTI_MODIFYDIV_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/modifymultidivconfirm.htm';
/**
 * 零售转托管转入
 */
TmsCounterConfig.TRANSFERTUBE_IN_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/transfertubeinconfirm.htm';

/**
 * 修改回款方向
 */
TmsCounterConfig.MODIFY_REDEEM_DIRECTION = TmsCounterConfig.BASEURL+'tmscounter/fund/modifyredeemdirection.htm';

/**
 * 修改回款方向申请
 */
TmsCounterConfig.APPLY_MODIFY_REDEEM_DIRECTION = TmsCounterConfig.BASEURL+'tmscounter/fund/applymodifyredeemdirection.htm';

/**
 * 零售转托管转出
 */
TmsCounterConfig.TRANSFERTUBE_OUT_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/transfertubeoutconfirm.htm';

/**
 * 零售转托管转出---查询客户基金持仓信息
 */
TmsCounterConfig.QUERY_TRANS_OUT_FUND_CUST_HODL_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/queryTransOutFundCustHodlInfo.htm';

/**
 * 零售转托管转出---税延基金调用中登查询账户对应关系
 */
TmsCounterConfig.QUERY_ACCOUNT_RELATION_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/queryaccountrelation.htm';

/**
 * 零售转托管转出---税延基金调用中登查询账户对应关系结果
 */
TmsCounterConfig.QUERY_ACCOUNT_RELATION_RESULT_URL = TmsCounterConfig.BASEURL+'tmscounter/fund/queryaccountrelationresult.htm';


/**
 * 查询待审核订单
 */
TmsCounterConfig.QUERY_CHECK_ORDER_URL= TmsCounterConfig.BASEURL+'tmscounter/querycheckorder.htm';

/**
 * 查询股权份额转让订单
 */
TmsCounterConfig.QUERY_OWNERSHIP_RIGHT_TRANSFER_URL= TmsCounterConfig.BASEURL+'tmscounter/queryOwnershipRightTransfer.htm';

/**
 * 下载股权份额转让订单
 */
TmsCounterConfig.DOWNLOAD_OWNERSHIP_RIGHT_TRANSFER_URL= TmsCounterConfig.BASEURL+'tmscounter/downloadOwnershipRightTransfer.htm';


TmsCounterConfig.QUERY_FUND_CHECK_ORDER_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/querycheckorder.htm';

TmsCounterConfig.QUERY_REGULAR_CHECK_ORDER_URL= TmsCounterConfig.BASEURL+'tmscounter/regular/querycheckorder.htm';

TmsCounterConfig.QUERY_CHECK_ORDER_BY_ID_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/querycheckorderbyid.htm';

TmsCounterConfig.QUERY_REGULAR_CHECK_ORDER_BY_ID_URL= TmsCounterConfig.BASEURL+'tmscounter/regular/querycheckorderbyid.htm';

/**
 * 审核确认
 */
TmsCounterConfig.CHECK_CONFIRM_URL= TmsCounterConfig.BASEURL+'tmscounter/checkconfirm.htm';

/**
 * 购买下单校验
 */
TmsCounterConfig.HIGH_CHECK_BUY_ORDER_INFO= TmsCounterConfig.BASEURL+'tmscounter/checkBuyOrderInfo.htm';


/**
 * 股份份额转让修改申请
 */
TmsCounterConfig.COUNTER_OWNERSHIP_RIGHT_TRANSFET= TmsCounterConfig.BASEURL+'tmscounter/counterownershipRightTransfer.htm';
/**
 * 用户认缴金额信息提交
 */
TmsCounterConfig.SUBS_AMT_DETAIL_APPLY= TmsCounterConfig.BASEURL+'tmscounter/subsAmtDetailApply.htm';

/**
 * 股份份额转让详情
 */
TmsCounterConfig.OWNERSHIP_RIGHT_TRANSFER_DTL= TmsCounterConfig.BASEURL+'tmscounter/ownershipRightTransferDtlByDtlNo.htm';
/**
 * 查询用户认缴金额详情
 */
TmsCounterConfig.CUSTOMER_SUBS_AMT_DEAIL= TmsCounterConfig.BASEURL+'tmscounter/customerSubsAmtDetail.htm';
/**
 * 股份份额转让详情
 */
TmsCounterConfig.QUERY_OWNERSHIP_RIGHT_TRANSFER_DTL= TmsCounterConfig.BASEURL+'tmscounter/queryOwnershipRightTransferDtl.htm';

TmsCounterConfig.QUERY_SUBS_AMT_CHANGE_DETAIL= TmsCounterConfig.BASEURL+'tmscounter/querySubsAmtChangeDetail.htm';

TmsCounterConfig.CHECK_FUND_CONFIRM_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/checkconfirm.htm';

TmsCounterConfig.CHECK_REGULAR_CONFIRM_URL= TmsCounterConfig.BASEURL+'tmscounter/regular/checkconfirm.htm';

TmsCounterConfig.CHECK_FUND_ABOLISH_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/abolishconfirm.htm';

/**
 * 查询客户预约信息
 */
TmsCounterConfig.QUERY_APPOINTMENT_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/queryappointmentInfo.htm';

/**
 * 客户定位子页面查询客户信息
 */
TmsCounterConfig.QUERY_CUST_INFO_SUB_PAGE_URL =TmsCounterConfig.BASEURL + 'tmscounter/querycustinfosubpage.htm';
/**
 * 查询客户信息
 */
TmsCounterConfig.QUERY_CUST_INFO_URL =TmsCounterConfig.BASEURL + 'tmscounter/querycustinfo.htm';
/**
 * 查询客户默认分销代码
 */
TmsCounterConfig.QUERY_DEFAULT_DISCODE_URL =TmsCounterConfig.BASEURL + 'tmscounter/getdefaultdiscode.htm';
/**
 * 查询客户信息-多分销
 */
TmsCounterConfig.QUERY_CUST_INFO_URL_BY_DISCODELIST =TmsCounterConfig.BASEURL + 'tmscounter/querycustinfoByDisCodeList.htm';
/**
 * 查询基金TA信息
 */
TmsCounterConfig.QUERY_FUND_TA_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryFundTaInfo.htm';
/**
 * 查询基金TA信息
 */
TmsCounterConfig.QUERY_FUND_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryfundinfo.htm';
TmsCounterConfig.QUERY_PRODUCT_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryproductinfo.htm';
TmsCounterConfig.QUERY_SELF_PRODUCT_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryselfproductinfo.htm';
/**
 * 查询默认基金交易账号
 */
TmsCounterConfig.QUERY_FUND_TX_ACCT_NO = TmsCounterConfig.BASEURL + 'tmscounter/qeuryfundtxacctno.htm';
/**
 * 查询定期产品信息
 */
TmsCounterConfig.QUERY_REGULAR_PRODUCT_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryregularproductinfo.htm';
/**
 * 计算支付金额
 */
TmsCounterConfig.CAL_FUND_BUY_FEE_URL = TmsCounterConfig.BASEURL + 'tmscounter/calfundbuyfee.htm';

/**
 * 好臻订单信息
 */
TmsCounterConfig.QUERY_HZ_BUY_INFO = TmsCounterConfig.BASEURL + 'tmscounter/queryHzBuyInfo.htm';
/**
 * 计算基础费率
 */
TmsCounterConfig.CAL_BASE_FATE_URL = TmsCounterConfig.BASEURL + 'tmscounter/calbasefate.htm';
/**
 * 计算折扣率
 */
TmsCounterConfig.CAL_DISCOUNT_RATE_URL = TmsCounterConfig.BASEURL + 'tmscounter/caldiscountrate.htm';
/**
 * 零售计算折扣率
 */
TmsCounterConfig.FUND_CAL_DISCOUNT_RATE_URL = TmsCounterConfig.BASEURL + 'tmscounter/fund/caldiscountrate.htm';
/**
 * 柜台交易查询
 */
TmsCounterConfig.QUERY_COUNTER_TRADE_URL = TmsCounterConfig.BASEURL + 'tmscounter/querycountertrade.htm';

/**
 * 柜台回款方向
 */
TmsCounterConfig.QUERY_COUNTER_MODIFY_REFUND_DIRECTION_URL = TmsCounterConfig.BASEURL + 'tmscounter/modifyrefund/queryorder.htm';
/**
 * 柜台回款方向
 */
TmsCounterConfig.COUNTER_MODIFY_REFUND_DIRECTION_URL = TmsCounterConfig.BASEURL + 'tmscounter/modifyrefund/modifyrefundconfirm.htm';
/**
 * 修改认缴金额申请单
 */
TmsCounterConfig.UPDATE_SUBS_AMT_DETAIL_APPLY = TmsCounterConfig.BASEURL + 'tmscounter/updateSubsAmtDetailApply.htm';
/**
 * 柜台交易记录下载
 */
TmsCounterConfig.QUERY_COUNTER_TRADE_DOWN_URL = TmsCounterConfig.BASEURL + 'tmscounter/querycountertradedown.htm';
/**
 * 柜台交易统计
 */
TmsCounterConfig.QUERY_COUNTER_REPORT_URL = TmsCounterConfig.BASEURL + 'tmscounter/querycounterreport.htm';

/**
 * 柜台文件下载
 */
TmsCounterConfig.QUERY_COUNTER_DOWN_URL = TmsCounterConfig.BASEURL + 'tmscounter/counterdown.htm';

/**
 * 柜台收市
 */
TmsCounterConfig.QUERY_COUNTER_END_URL = TmsCounterConfig.BASEURL + 'tmscounter/counterend.htm';
/**
 * 高端公募柜台收市
 */
TmsCounterConfig.HIGH_FUND_COUNTER_END_URL = TmsCounterConfig.BASEURL + 'tmscounter/simucounterend.htm';
/**
 * 高端公募柜台收市
 */
TmsCounterConfig.QUERY_HIGH_PRODUCT_COUNTER_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/queryhighprodcounter.htm';
/**
 * 不收市TA维护
 */
TmsCounterConfig.HIGH_COUNTER_SAVE_OR_DEL_END_TA_URL = TmsCounterConfig.BASEURL + 'tmscounter/saveOrDelCounterNotEndTA.htm';
/**
 * 柜台收市(公募)
 */
TmsCounterConfig.QUERY_FUND_COUNTER_END_URL = TmsCounterConfig.BASEURL + 'tmscounter/fund/counterend.htm';
/**
 * 查询柜台不收市TA列表和统计(公募)
 */
TmsCounterConfig.QUERY_FUND_COUNTER_END_TA_URL = TmsCounterConfig.BASEURL + 'tmscounter/fund/queryCounterEndTA.htm';
/**
 * 柜台添加和删除不收市TA
 */
TmsCounterConfig.COUNTER_SAVE_OR_DEL_END_TA_URL = TmsCounterConfig.BASEURL + 'tmscounter/fund/saveOrDelCounterNotEndTA.htm';

/**
 * 查询当前服务器端日期时间
 */
TmsCounterConfig.QUERY_CURR_DATE_URL = TmsCounterConfig.BASEURL + 'tmscounter/querycurrdate.htm';

TmsCounterConfig.QUERY_CURR_WORK_DAY_URL = TmsCounterConfig.BASEURL + 'tmscounter/fund/querycurrworkday.htm';

TmsCounterConfig.QUERY_HIGH_CURR_WORK_DAY_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/queryhighcurrworkday.htm';
/**
 * 查询客户绑定银行卡信息
 */
TmsCounterConfig.QUERY_CUST_BANKINFO_URL =TmsCounterConfig.BASEURL + 'tmscounter/querycustbankinfo.htm';

/***查询投顾*/
TmsCounterConfig.QUERY_CONS_CODE_INFO_URL =TmsCounterConfig.BASEURL + 'tmscounter/fund/queryconscodeinfo.htm';

/***查询分销机构*/
TmsCounterConfig.QUERY_DIS_CODE_INFO_URL =TmsCounterConfig.BASEURL + 'tmscounter/fund/querydiscodeinfo.htm';

/***
 * 查询产品基础信息
 */
TmsCounterConfig.QUERY_PRODUCT_BASE_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryproductbaseinfo.htm';

/**
 * 查询高端产品基本信息
 */
TmsCounterConfig.QUERY_HIGH_PRODUCT_INFO_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryhighproductinfo.htm';

/**
 * 高端-校验客户
 */
TmsCounterConfig.HIGH_VALID_RISK_URL = TmsCounterConfig.BASEURL+ 'tmscounter/high/validrisk.htm';

/**
 * 高端-查询客户基本信息
 */
TmsCounterConfig.HIGH_QUERY_CUST_INFO_URL = TmsCounterConfig.BASEURL+ 'tmscounter/high/querycustinfo.htm';

/**
 * 高端产品子页面查询
 */
TmsCounterConfig.HIGH_QUERY_PRODUCT_SUB_PAGE = TmsCounterConfig.BASEURL+ 'tmscounter/high/queryhighprodsubpage.htm';


/**
 * 查询认缴信息
 */
TmsCounterConfig.HIGH_QUERY_SUBS_AMT_LIST = TmsCounterConfig.BASEURL+ 'tmscounter/high/querySubsAmtList.htm';

/**
 * 高端产品购买预校验
 */
TmsCounterConfig.HIGH_BUY_PRE_VALID = TmsCounterConfig.BASEURL +'tmscounter/high/buyprevalid.htm';


/**
 * 查询客户高端产品持仓明细
 */
TmsCounterConfig.HIGH_QUERY_CUST_BAL_DTL = TmsCounterConfig.BASEURL + 'tmscounter/high/querycustbaldtl.htm';

/**
 * 查看用户份额明细
 */
TmsCounterConfig.HIGH_VIEW_BAL_DTL =TmsCounterConfig.BASEURL+ 'tmscounter/high/viewbaldtl.htm';

/**
 * 查看高端柜台订单
 */
TmsCounterConfig.HIGH_VIEW_CHECKORDER =TmsCounterConfig.BASEURL+ 'tmscounter/high/viewcheckorder.htm';

/**
 * 查询客户资产(资产证明)
 */
TmsCounterConfig.HIGH_QUERY_CUST_ASSERT = TmsCounterConfig.BASEURL+ 'tmscounter/high/querycustassert.htm';

/**
 * 生成资产证明
 */
TmsCounterConfig.HIGH_GENERATE_CUST_ASSERT_URL = TmsCounterConfig.BASEURL+ 'tmscounter/high/generatecustassert.htm';
/**
 * 导出客户资产证明
 */
TmsCounterConfig.HIGH_EXPORT_CUST_ASSERT_URL = TmsCounterConfig.BASEURL+ 'tmscounter/high/exportcustassert.htm';

/**
 * 查询高端产品预约开放日历信息
 */
TmsCounterConfig.HIGH_QUERY_HIGHPRODUCT_APPOINTINFO_URL =TmsCounterConfig.BASEURL+  'tmscounter/queryhighproductappointinfo.htm';

/**
 * 查询好臻认缴金额信息
 */
TmsCounterConfig.QUERY_HZ_SUBSCRIBE_AMT_INFO =TmsCounterConfig.BASEURL+  'tmscounter/queryHzSubscribeAmtInfo.htm';

/**
 * 查询高端工作日
 */
TmsCounterConfig.QUERY_HIGH_WORKDAY_URL =TmsCounterConfig.BASEURL+ 'tmscounter/queryhighworkday.htm';

/**
 * 修改柜台订单
 */
TmsCounterConfig.MODIFY_CHECK_ORDER_URL = TmsCounterConfig.BASEURL+'tmscounter/modifycheckorder.htm';

/**
 * 高端撤单校验
 */
TmsCounterConfig.HIGH_CANCEL_VALID_URL = TmsCounterConfig.BASEURL+'tmscounter/high/cancelvalid.htm';


/**
 * 查询客户经办人信息
 */
TmsCounterConfig.QUERY_CUST_TRANSINFO_URL = TmsCounterConfig.BASEURL+ 'tmscounter/querycusttransinfo.htm';

/**
 * 查询客户份额持仓信息
 */
TmsCounterConfig.QUERY_CUST_VOL_BAL_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryCustVolBal.htm';

/**
 * 费率数据查询
 */
TmsCounterConfig.QUERY_APP_RATE_CHANGE_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryAppRateChange.htm';

/**
 * 修改折扣费率
 */
TmsCounterConfig.MODIFY_APP_RATE_CHANGE_URL = TmsCounterConfig.BASEURL + 'tmscounter/modifyAppRateChange.htm';

/**
 * 份额合并申请提交
 */
TmsCounterConfig.CUST_VOL_MERGE_SUBMIT_URL = TmsCounterConfig.BASEURL + 'tmscounter/custVolMergeSubmit.htm';
/**
 * 份额迁移申请提交
 */
TmsCounterConfig.CUST_VOL_TRANSFER_SUBMIT_URL = TmsCounterConfig.BASEURL + 'tmscounter/custVolTransferSubmit.htm';
/**
 * 份额合并或迁移审核提交
 */
TmsCounterConfig.CHECK_MERGE_TRANS_CONFIRM_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/checkMergeTransConfirm.htm';
/**
 * 份额合并或迁移审核提交
 */
TmsCounterConfig.CHECK_ONLINE_TRANS_CONFIRM_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/onlineTransConfirm.htm';
/**
 * 上传凭证图片、视频
 */
TmsCounterConfig.UPLOAD_VOUCHER_FILE= TmsCounterConfig.BASEURL+'tmscounter/fund/uploadVoucherFile.htm';

/**
 * 删除凭证图片、视频
 */
TmsCounterConfig.DELETE_VOUCHER_FILE= TmsCounterConfig.BASEURL+'tmscounter/fund/delVoucherFile.htm';

/**
 * 下载身份证正反面和人脸识别
 */
TmsCounterConfig.DOWNLOAD_PIC= TmsCounterConfig.BASEURL+'tmscounter/fund/downloadPic.htm';

/**
 * 上传身份证图片
 */
TmsCounterConfig.UPLOAD_VOUCHER_PIC_FILE= TmsCounterConfig.BASEURL+'tmscounter/fund/uploadVoucherFilePic.htm';

/**
 * 查询份额合并或迁移待审核单列表
 */
TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/queryMergeTransCheckOrder.htm';
/**
 * 查询份额合并或迁移明细订单
 */
TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_BY_ID_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/queryMergeTransCheckOrderbyId.htm';
/**
 * 查询修改费率折扣信息
 */ 
TmsCounterConfig.QUERY_SUBMIT_APP_ORDER_TRADE_BY_ID_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/querySubmitAppOrderById.htm';

/**
 * 查询份额合并或迁移交易订单明信息
 */
TmsCounterConfig.QUERY_MERGE_TRANS_TRADE_ORDER_BY_ID_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/queryMergeTransTradeOrderById.htm';


/**
 * 高端柜台收市
 */
TmsCounterConfig.High_COUNTER_END_URL = TmsCounterConfig.BASEURL + 'tmscounter/simucounterend.htm';

/**
 * 高端重复单检验
 */
TmsCounterConfig.HIGH_REPEAT_APP_VALID_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/repeatappvalid.htm';

/**
 *
 * 查询批处理状态
 */
TmsCounterConfig.QUERY_BATCH_STAT_URL = TmsCounterConfig.BASEURL + 'tmscounter/querybatchstat.htm';
/**
 * 查询批处理状态(new)
 */
TmsCounterConfig.QUERY_BATCH_STEP_STAT_URL = TmsCounterConfig.BASEURL + 'tmscounter/querybatchstepstat.htm';
TmsCounterConfig.QUERY_HIGH_COUNTER_END_TA_LIST_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryCounterEndTAList.htm';

TmsCounterConfig.QUERY_HIGH_TA_BUSINESS_COUNT_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryTaBusinessCount.htm';

TmsCounterConfig.QUERY_HIGH_TA_COUNTER_NOT_END_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryTaCounterNotEnd.htm';

TmsCounterConfig.HIGH_TA_NOT_END_PRO_URL = TmsCounterConfig.BASEURL + 'tmscounter/saveOrDelCounterNotEndTA.htm';

/**
 * 固收到期赎回
 */
TmsCounterConfig.HIGH_EXPIRED_REDEEM_URL = TmsCounterConfig.BASEURL + 'tmscounter/expiredRedeem.htm';

/**
 * 修改客户复购协议
 */
TmsCounterConfig.HIGH_MODIFY_REPURCHASE_PROTOCOL_URL = TmsCounterConfig.BASEURL + 'tmscounter/modifyrepurchaseprotocol.htm';

/**
 * 查询固收赎回报表
 */
TmsCounterConfig.HIGH_QUERY_FIXED_REDEEM_REPORT_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryfixedredeemreport.htm';

/**
 * 查询高端产品信息coedeAndName
 */
TmsCounterConfig.HIGH_QUERY_PRODUCT_CODEANDNAME_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/query/queryhighproductcodeandname.htm';


/**
 * 查询高端管理人信息coedeAndName
 */
TmsCounterConfig.HIGH_QUERYFUNDMAN_CODEANDNAME_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/query/queryfundmancodeandname.htm';

/**
 * 查询高端TA信息coedeAndName
 */
TmsCounterConfig.HIGH_QUERY_TAINFO_CODEANDNAME_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/query/querytainfocodeandname.htm';
/**
 *
 * 线上资料审核
 */
TmsCounterConfig.HIGH_ORDERFILE_URL = TmsCounterConfig.BASEURL + 'tmscounter/orderfile.htm';

/**
 *
 * 线上资料查询
 */
TmsCounterConfig.HIGH_QUERY_ORDERFILE_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryorderfile.htm';
/**
 *
 * 免密登录
 */
TmsCounterConfig.HIGH_SELF_LOGIN_URL = TmsCounterConfig.BASEURL + 'tmscounter/selflogin.htm';
/**
 *
 * 查看资料文件
 */
TmsCounterConfig.HIGH_SHOW_ORDERFILE_URL = TmsCounterConfig.BASEURL + 'tmscounter/showorderfile.htm';

/**
 *
 * 复审退回资料文件
 */
TmsCounterConfig.HIGH_RECHECK_REJECTFILE_URL = TmsCounterConfig.BASEURL + 'tmscounter/recheckrejectfile.htm';

/**
 *
 * 查询未完成复购协议
 */
TmsCounterConfig.HIGH_QUERY_UNFINISH_REPURCHASE_URL = TmsCounterConfig.BASEURL + 'tmscounter/high/queryunfinishrepurchase.htm';

/**
 * 查询在途资产，调用资金系统
 */
TmsCounterConfig.QUERY_INTRANSIT_ASSET_URL = TmsCounterConfig.BASEURL + 'tmscounter/queryintransasset.htm';

/**
 * 查询客户银行卡状态
 */
TmsCounterConfig.QUERY_BANK_ACCT_STAT = TmsCounterConfig.BASEURL + 'tmscounter/querycustbankacctstat.htm';

/**
 *
 * 收市预检查
 */
TmsCounterConfig.FUND_END_PRE_CHECK = TmsCounterConfig.BASEURL + 'tmscounter/fund/endprecheck.htm';




/**
 * 理财通转托管转出
 */
TmsCounterConfig.LCT_TRANSFERTUBE_OUT_FUND_CONFIRM_URL = TmsCounterConfig.BASEURL+'tmscounter/lct/lcttransfertubeoutconfirm.htm';

/**
 * 理财通转托管转出---查询客户基金持仓信息
 */
TmsCounterConfig.LCT_QUERY_TRANS_OUT_FUND_CUST_HODL_INFO_URL = TmsCounterConfig.BASEURL+'tmscounter/lct/lctqueryTransOutFundCustHodlInfo.htm';

/**
 * 理财通-查询理财通待审核订单
 */
TmsCounterConfig.LCT_QUERY_CHECK_ORDER_URL = TmsCounterConfig.BASEURL+'tmscounter/lct/querycheckorder.htm';

/**
 * 理财通-查询理财通单条待审核订单
 */
TmsCounterConfig.LCT_QUERY_CHECK_ORDER_BY_ID_URL = TmsCounterConfig.BASEURL+'tmscounter/lct/querycheckorderbyid.htm';

/**
 * 查询修改折扣率明细订单
 */
TmsCounterConfig.QUERY_CHANGE_DISCOUNT_CHECK_ORDER_BY_ID_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/queryChangeDiscountCheckOrderById.htm';

/**
 * 修改折扣率审核
 */
TmsCounterConfig.CHECK_CHANGE_DISCOUNT_CONFIRM_URL= TmsCounterConfig.BASEURL+'tmscounter/fund/checkChangeDiscountConfirm.htm';


