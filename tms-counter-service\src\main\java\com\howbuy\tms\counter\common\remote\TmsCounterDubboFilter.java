/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.common.remote;

import com.alibaba.fastjson.JSON;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.Filter;
import com.alibaba.dubbo.rpc.Invocation;
import com.alibaba.dubbo.rpc.Invoker;
import com.alibaba.dubbo.rpc.Result;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.dubbo.rpc.RpcException;
import com.howbuy.common.facade.BaseRequest;
import com.howbuy.common.facade.BaseResponse;
import com.howbuy.payonline.facade.base.model.Request;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.howbuy.tms.counter.common.RemoteConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @description:(柜台调用外部dubbo过滤器) 
 * @reason:
 * <AUTHOR>
 * @date 2018年2月9日 下午3:48:25
 * @since JDK 1.6
 */
@Activate(group=Constants.CONSUMER)
public class TmsCounterDubboFilter implements Filter {
   
    private static final Logger log = LoggerFactory.getLogger(TmsCounterDubboFilter.class);

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // 关联request、response
        String txKeyTime = UUID.randomUUID().toString().replaceAll("-", "");
        String interfaceName = invoker.getInterface().getSimpleName();
        String methodName = invocation.getMethodName();

        List<Object> args = new ArrayList<Object>();

        if (invocation.getArguments() != null) {
            for (Object obj : invocation.getArguments()) {
                if (obj == null) {
                    continue;
                }
                if (obj instanceof Request) {
                    TradeInvoker.setCommonParameters((Request) obj);
                } else if (obj instanceof BaseRequest) {
                    TradeInvoker.setCommonParameters((BaseRequest) obj);
                } else if (obj instanceof com.howbuy.tms.common.client.BaseRequest) {
                    com.howbuy.tms.common.client.BaseRequest req = (com.howbuy.tms.common.client.BaseRequest) obj;
                    TradeInvoker.setCommonParameters(req);
                    req.setDataTrack(txKeyTime);
                } else if (obj instanceof com.howbuy.cc.center.feature.base.BaseRequest) {
                    TradeInvoker.setKycParameters((com.howbuy.cc.center.feature.base.BaseRequest) obj);
                }
                args.add(obj);
            }

        }

        String remoteAddressString = RpcContext.getContext().getRemoteAddressString();
        if(!RemoteConstants.UploadVoucherFileFacade.equals(interfaceName)){
            log.info("DUBBO request:[txKeyTime:{}, txInterface:{}.{},address:{}]{}", new Object[] { txKeyTime, interfaceName, methodName,
                    remoteAddressString, JSON.toJSONString(args) });
        }


        long start = System.currentTimeMillis();
        Result invoke = invoker.invoke(invocation);
        long cost = System.currentTimeMillis() - start;
        Object obj = invoke.getValue();
        if (obj != null) {
            String code = "--";
            String desc = "--";
            if (obj instanceof BaseResponse) {
                code = ((BaseResponse) obj).getReturnCode();
                desc = ((BaseResponse) obj).getDescription();
                log.warn("DUBBO response:[txKeyTime:{}, txInterface:{}.{}, code:{}, desc:{}, cost:{}]{}", new Object[] { txKeyTime, interfaceName, methodName,
                        code, desc, cost, JSON.toJSONString(invoke.getValue()) });
            } else if (obj instanceof com.howbuy.tms.common.client.BaseResponse) {
                code = ((com.howbuy.tms.common.client.BaseResponse) obj).getReturnCode();
                desc = ((com.howbuy.tms.common.client.BaseResponse) obj).getDescription();
                log.warn("DUBBO response:[txKeyTime:{}, txInterface:{}.{}, code:{}, desc:{}, cost:{}]{}", new Object[] { txKeyTime, interfaceName, methodName,
                        code, desc, cost, JSON.toJSONString(invoke.getValue()) });
            } else {
                if (log.isInfoEnabled()) {
                    log.info("DUBBO response:[txKeyTime:{}, txInterface:{}.{}, code:{}, desc:{}, cost:{}]{}", new Object[] { txKeyTime, interfaceName,
                            methodName, code, desc, cost, JSON.toJSONString(invoke.getValue()) });
                }
            }
        }
        return invoke;
    }

}

