/**
 * 线上份额迁移
 */
$(function () {
    Init.init();
    OnlineTransferVol.init();
    OnlineTransferVol.custInfo = {};
    OnlineTransferVol.checkOrders = [];
    OnlineTransferVol.checkOrders = [];
    OnlineTransferVol.dtlOrderDtoList = [];
    OnlineTransferVol.counterOrderDto = {};
    OnlineTransferVol.subcount = 0;
    
    //通过
    OnlineTransferVol.Succ = '1';
    //驳回
    OnlineTransferVol.Faild = '2';
    var selectTxCodeHtml = '<option value="">全部</option>';
    $.each(CONSTANTS.COUNTER_MERGE_TRANS_TXCODE_MAP, function (name, value) {
        selectTxCodeHtml += '<option value="' + name + '">' + value + ' </option>';
    });
    $("#selectTxCode").empty();
    $("#selectTxCode").html(selectTxCodeHtml);

    $("#returnBtn").on('click', function () {
        OnlineTransferVol.confirm(OnlineTransferVol.Faild);
    });

    $("#succBtn").on('click', function () {
        OnlineTransferVol.confirm(OnlineTransferVol.Succ);
    });

    let selectAssetScopeHtml = '<option value="">全部</ooption>';
    $.each(CONSTANTS.ASSET_SCOPE, function (name, value) {
        selectAssetScopeHtml += '<option value="' + name + '">' + value + ' </option>';
    });
    $("#selectAssetScope").empty();
    $("#selectAssetScope").html(selectAssetScopeHtml);


});

function downloadImage(path, fileName, type) {
    var url = TmsCounterConfig.DOWNLOAD_PIC || {};
    url += '?path=' + encodeURIComponent(path) + '&fileName=' + encodeURIComponent(fileName) + '&type=' + type;
    // 使用 window.location.href 触发下载
    window.location.href = url;
}

var OnlineTransferVol = {
        init: function () {
            $("#queryOnlineBtn").on('click', function () {
                OnlineTransferVol.queryOrderInfo();
            });
        },

        /***
         * 审核确认
         */
        confirm: function (checkStatus) {
            if (window.checkedClick == '1') {
                return false;
            }
            //防止重复点击
            window.checkedClick = '1';
            var checkFaildDesc;
            var uri = TmsCounterConfig.CHECK_ONLINE_TRANS_CONFIRM_URL || {};

            if (OnlineTransferVol.Faild == checkStatus) {
                if (CommonUtil.isEmpty($("#checkFaildDesc").val())) {
                    window.checkedClick = '0';
                    CommonUtil.layer_tip("请输入退回原因");
                    return false;
                }
                checkFaildDesc = $("#checkFaildDesc").val();
            }

            var materialCheckFlagList = [];
            var validateFlag = true;
            var existsNotSelect = false;
            var existFile = false;
            $('#onlineMaterialBody').find("tr").each(function () {

                var dtlCheckFlag = $(this).find('td:eq(3)').find("select").val();
                if (dtlCheckFlag == '0') {
                    existsNotSelect = true;
                    return;
                }
                if (OnlineTransferVol.Succ == checkStatus && '2' == dtlCheckFlag) {
                    validateFlag = false;
                    return;
                }

                if (OnlineTransferVol.Succ === checkStatus && OnlineTransferVol.appCheckOrder.asset > 20000) {
                    $(this).find('td:eq(1)').find("input[name='needFile']").each(function () {
                        if ($(this).val() !== '') {
                            existFile = true;
                        }
                    });
                }

                $(this).find('td:eq(1)').find("input[name='dealDtlAppNo']").each(function () {
                    var dtlCheckFlagJson = {
                        "dealDtlAppNo": $(this).val(),
                        "checkFlag": dtlCheckFlag
                    };
                    materialCheckFlagList.push(dtlCheckFlagJson);
                });
            });

            if (!existFile && OnlineTransferVol.Succ === checkStatus && OnlineTransferVol.appCheckOrder.asset > 20000 ) {
                window.checkedClick = '0';
                CommonUtil.layer_tip("资料未上传，不能审核! 或者 未选择审核结果！");
                return false;
            }

            if (existsNotSelect) {
                window.checkedClick = '0';
                CommonUtil.layer_tip("未选择审核结果!");
                return false;
            }

            if (!validateFlag) {
                window.checkedClick = '0';
                CommonUtil.layer_tip("存在资料不合格的明细");
                return false;
            }

            var reqparamters = {
                "checkFaildDesc": checkFaildDesc,
                "checkStatus": checkStatus,
                "checkedOrderForm": JSON.stringify(OnlineTransferVol.appCheckOrder),
                "materialCheckFlagList": JSON.stringify(materialCheckFlagList)

                //"checkDtlOrderForm":JSON.stringify(checkDtlOrderForm)
            };
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, OnlineTransferVol.callBack);
            return true;
        },
        callBack: function (data) {
            window.checkedClick = '0';
            var respCode = data.code || '';
            var respDesc = data.desc || '';

            if (CommonUtil.isSucc(respCode)) {
                CommonUtil.layer_alert(respDesc);
                CommonUtil.disabledBtn("returnBtn");
                CommonUtil.disabledBtn("succBtn");
            } else {
                CommonUtil.layer_alert(respDesc);
            }
        },

        /**
         * 查询待审核订单信息
         */
        queryOrderInfo: function () {
            var uri = TmsCounterConfig.QUERY_MERGE_TRANS_CHECK_ORDER_URL || {};
            var reqparamters = {};
            var queryOrderConditionForm = $("#queryConditonForm").serializeObject();
            var queryOrderCondition = {};
            $.each(queryOrderConditionForm, function (name, value) {
                if (!CommonUtil.isEmpty(value)) {
                    queryOrderCondition[name] = value;
                }
            });
            reqparamters.queryConditonForm = JSON.stringify(queryOrderCondition);
            reqparamters.page = 1;
            reqparamters.pageSize = 20;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxPaging(uri, paramters, OnlineTransferVol.queryOrderInfoCallBack, "pageView");
        },

        queryOrderInfoCallBack: function (data) {
            var bodyData = data;
            OnlineTransferVol.checkOrders = bodyData.counterQueryOrderRespDto.counterOrderList || [];

            var staticData = bodyData.counterQueryOrderRespDto || {};
            $("#staticId").html("当页小计：申请笔数【" + OnlineTransferVol.checkOrders.length + "】申请份额【" + CommonUtil.formatAmount(staticData.pageAppVol) + "】 合计：申请笔数【" + staticData.totalCount + "】申请份额【" + CommonUtil.formatAmount(staticData.totalAppVol) + "】");

            $("#rsList").empty();
            if (OnlineTransferVol.checkOrders.length <= 0) {
                var trHtml = '<tr class="text-c" ><td colspan="14">暂无记录</td></tr>';
                $("#rsList").append(trHtml);
            }

            var i = 1;
            $(OnlineTransferVol.checkOrders).each(function (index, element) {
                var trList = [];
                if (element.checkFlag == '0') {
                    trList.push('<a class="reCheck" href="javascript:void(0);" indexvalue = ' + index + ' checknode=' + '2' + '  style="color: #06c;">审核</a> <a class="reOnlineQuery" href="javascript:void(0);" indexvalue = ' + index + ' checknode=' + '2' + '  style="color: #06c;">查看</a>');

                } else {
                    trList.push('<a class="reOnlineQuery" href="javascript:void(0);" indexvalue = ' + index + ' checknode=' + '2' + '  style="color: #06c;">查看</a>');

                }
                trList.push(CommonUtil.formatData(element.appDt));
                trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
                trList.push(CommonUtil.formatData(element.custName));
                trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP, element.invstType, ''));
                trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
                trList.push(CommonUtil.formatData(element.idNo));
                trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP, element.checkFlag, ''));

                trList.push(CommonUtil.formatData(element.outBankAcct, '--'));
                trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.outBankCode, ''));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode, ''));

                var trHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                $("#rsList").append(trHtml);
            });

            // 查订单详情
            $(".reOnlineQuery").off();
            $(".reOnlineQuery").on('click', function () {
                var indexValue = $(this).attr("indexvalue");
                var checkedOrder = OnlineTransferVol.checkOrders[indexValue] || {};
                OnlineTransferVol.checkedOrder = checkedOrder;
                OnlineTransferVol.operationType = "query";
                var dealAppNo = checkedOrder.dealAppNo;
                var checkNode = $(this).attr('checkNode');

                // 复审
                var checkNode = "1";

                // 设置转出和转入信息
                QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, OnlineTransferVol.queryCheckVolOrderByIdBack, checkNode, "Z910105");
            });


            $(".reCheck").off();
            $(".reCheck").on('click', function () {
                var indexValue = $(this).attr("indexvalue");
                var checkedOrder = OnlineTransferVol.checkOrders[indexValue] || {};
                OnlineTransferVol.checkedOrder = checkedOrder;
                OnlineTransferVol.operationType = "check";
                var dealAppNo = checkedOrder.dealAppNo;
                var checkNode = $(this).attr('checkNode');

                // 复审
                var checkNode = "1";

                // 设置转出和转入信息
                QueryCheckOrder.queryMergeTransCheckOrderById(dealAppNo, OnlineTransferVol.queryCheckVolOrderByIdBack, checkNode, "Z910105");
            });
        },
        
        queryCheckVolOrderByIdBack: function (data) {
            var bodyData = data.body || {};
            OnlineTransferVol.appCheckOrder = bodyData.checkOrder || {};
            OnlineTransferVol.checkDtlOrder = bodyData.checkDtlOrder || [];
            OnlineTransferVol.materialDtl = bodyData.materialDtl || [];
            OnlineTransferVol.schePlanList = bodyData.schePlanList || [];
            OnlineTransferVol.capitalPayRecord = bodyData.capitalPayRecord || [];
            // 转入持仓
            var respData = bodyData.respData;
            OnlineTransferVol.transInDisCode = OnlineTransferVol.appCheckOrder.disCode;
            OnlineTransferVol.transInBalDtl = respData.custBalDtlList || [];


            if (CommonUtil.isEmpty(OnlineTransferVol.appCheckOrder.dealAppNo)) {
                CommonUtil.layer_tip("无此订单");
                return false;
            }

            if (OnlineTransferVol.operationType == 'query') {
                $('#succBtn').hide();
                $('#returnBtn').hide();
            } else {
                $('#succBtn').show();
                $('#returnBtn').show();
                CommonUtil.enabledBtn("returnBtn");
                CommonUtil.enabledBtn("succBtn");
            }

            if (OnlineTransferVol.operationType == 'check' && OnlineTransferVol.appCheckOrder.checkFlag != 0) {
                CommonUtil.layer_tip("该订单已审核完成");
                return false;
            }
            // POPUP
            layer.open({
                title: ['详情', true],
                type: 1,
                area: ['95%', '90%'],
                skin: 'layui-layer-rim', //加上边框
                btnAlign: 'l',
                content: $('#popupOnlineTransVolInfo')

            });
            // 客户信息
            BodyView.setCustInfo("queryOnlineTransCustInfoId", OnlineTransferVol.appCheckOrder.txAcctNo,
                OnlineTransferVol.appCheckOrder.idNo, OnlineTransferVol.appCheckOrder.disCode, OnlineTransferVol.setMergeCustInfoTable);
            // 用户材料信息
            OnlineTransferVol.setMaterailTableView("onlineMaterialBody", OnlineTransferVol.materialDtl, OnlineTransferVol.operationType, OnlineTransferVol.appCheckOrder.asset, OnlineTransferVol.appCheckOrder.dealAppNo);
            // 定投合约信息
            BodyView.setScheduleAllInfoTableView("onlineSchedulePlanInfo", OnlineTransferVol.schePlanList);

            // 转出银行卡资产信息
            BodyView.setTransOutTableViewNew("onlineHighTransOutCustBals", "onlineTransOutCustBals", OnlineTransferVol.checkDtlOrder, OnlineTransferVol.appCheckOrder.disCode, "onlineAssetBody");
            // 设置转入银行卡信息
            QueryCustInfo.getCustBankInfos(OnlineTransferVol.appCheckOrder.txAcctNo, OnlineTransferVol.appCheckOrder.disCode, OnlineTransferVol.setTransInBankInfo);
            // 转入银行卡资产信息
            BodyView.setTransInCustBalsTableView("onlineTransInCustBals", OnlineTransferVol.transInBalDtl, OnlineTransferVol.transInDisCode);
            // 打款记录
            BodyView.setLastRemitBodyTableView("lastRemitBody", OnlineTransferVol.capitalPayRecord);
            /**other*/
            BodyView.setCheckOperInfoView(OnlineTransferVol.appCheckOrder);
            // 在途资产
            //OnlineTransferVol.queryAssetInfo();


        },

        refreshMaterailDtlBack: function (data) {
            var bodyData = data.body || {};
            OnlineTransferVol.appCheckOrder = bodyData.checkOrder || {};
            OnlineTransferVol.checkDtlOrder = bodyData.checkDtlOrder || [];
            OnlineTransferVol.materialDtl = bodyData.materialDtl || [];


            // 用户材料信息
            OnlineTransferVol.setMaterailTableView("onlineMaterialBody", OnlineTransferVol.materialDtl, OnlineTransferVol.operationType, OnlineTransferVol.appCheckOrder.asset, OnlineTransferVol.appCheckOrder.dealAppNo);

        },
        queryAssetInfo: function () {
            // 转入银行卡资产信息
            $("#onlineAssetBody").empty();

            // 查询客户银行卡持仓
            var uri = TmsCounterConfig.QUERY_INTRANSIT_ASSET_URL || {};
            var reqparamters = {};

            reqparamters.cpAcctNo = OnlineTransferVol.transInBalDtl[0].cpAcctNo;


            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, OnlineTransferVol.queryAssetInfoCallBack);
        },

        queryAssetInfoCallBack: function (data) {
            var bodyData = data.body || {};
            var respData = bodyData.batchStatList || [];

            OnlineTransferVol.assetDetailList = respData.detailList || [];


            if (OnlineTransferVol.assetDetailList.length <= 0) {
                var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
                $("#assetRealBody").append(trHtml);
                return false;

            } else {
                $(OnlineTransferVol.assetDetailList).each(function (index, element) {
                    var trList = [];
                    trList.push('');
                    trList.push(CommonUtil.formatData(element.prodCode));
                    trList.push(CommonUtil.formatData(element.fundName));
                    trList.push(CommonUtil.formatData(element.busiCode));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                    trList.push(CommonUtil.formatData(element.bankAcct));
                    trList.push(CommonUtil.formatData(element.bankAcctName));
                    trList.push(CommonUtil.formatAmount(element.occurBalance));
                    var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                    $("#assetRealBody").append(trAppendHtml);
                });
            }
        }

        ,

        setMergeCustInfoTable: function (data) {
            var id = "queryOnlineTransCustInfoId";
            var bodyData = data.body || {};
            var custInfoList = bodyData.custInfoList || [];

            $("#" + id).empty();
            if (custInfoList <= 0) {
                var trHtml = '<tr><td colspan="9">没有查询到客户信息</td></tr>';
                $("#" + id).append(trHtml);
                return false;

            } else {
                $(custInfoList).each(function (index, element) {
                    var trList = [];
                    trList.push(CommonUtil.formatData(element.custNo, '--'));
                    trList.push(CommonUtil.formatData(element.custName, '--'));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP, element.invstType, ''));
                    if (element.invstType == '0') {//属于机构
                        trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
                    }
                    if (element.invstType == '1') {
                        trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
                    }
                    if (element.invstType == '2') {
                        trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
                    }
                    trList.push(CommonUtil.formatData(element.idNo, '--'));
                    trList.push(CommonUtil.formatData(OnlineTransferVol.appCheckOrder.asset, '--'));

                    trList.push(CommonUtil.getMapValue(DisCode.disCodesMap, element.disCode, '--'));

                    var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                    $("#" + id).append(trAppendHtml);
                });
            }
        }
        ,

        setTransInBankInfo: function (data) {
            var body = data.body || {};
            var custBanks = body.custBanks || [];

            var transInBanks = {};
            transInBanks.bankAcct = OnlineTransferVol.appCheckOrder.bankAcct;
            transInBanks.bankCode = OnlineTransferVol.appCheckOrder.bankCode;

            $(custBanks).each(function (index, element) {
                if (element.cpAcctNo == OnlineTransferVol.appCheckOrder.cpAcctNo) {
                    transInBanks.bankRegionName = element.bankRegionName;
                    transInBanks.bankAcctStatus = element.bankAcctStatus;
                    return;
                }
            });

            // 转入银行卡
            OnlineTransferVol.setTransInBankTableView("onlineTransInBanks", transInBanks);
        },

        /**
         *  设置转入银行信息
         */
        setTransInBankTableView: function (id, data) {
            //console.log(data);

            $("#" + id).empty();
            if (data == null) {
                var trHtml = '<tr><td colspan="3">没有查询到转入的银行卡信息</td></tr>';
                $("#" + id).append(trHtml);
                return false;

            } else {
                var trList = [];
                trList.push(CommonUtil.formatData(data.bankAcct));
                trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, data.bankCode));
                trList.push(CommonUtil.formatData(data.bankRegionName));
                trList.push(CommonUtil.getMapValue(CONSTANTS.BANKACCT_STATUS_MAP, data.bankAcctStatus));
                var trAppendHtml = '<tr class="text-c"><td>' + trList.join('</td><td>') + '</td></tr>';
                $("#" + id).append(trAppendHtml);
            }
        },

        /**
         *  设置材料信息
         */
        setMaterailTableView: function (fundId, data, operationType, asset, dealAppNo) {
            $("#" + fundId).empty();

            if (data.length <= 0) {
                var trHtml = '<tr><td colspan="11">没有查询到材料信息</td></tr>';
                $("#" + fundId).append(trHtml);
                return false;

            } else {
                var size = data.length;
                var voucherTr = [];
                var videoPromiseTr = [];
                var certificateTr = [];
                var faceTr = [];
                var notProvideVoucherTr = [];
                var voucherMaterial = "";
                var voucherTrAppendHtml = "";
                var videoPromiseHtml = "";
                var notProvideTrAppendHtml = "";
                // 2000元以上档位一直展示证明原卡不可用凭证  有不可用凭证原因展示原因
                if (asset > 2000) {
                    // 凭证和视频承诺是否有资料标识
                    var uploadVoucherFlag = false;
                    var notVoucherFlag = false;
                    var videoFlag = false;
                    // 驳回原因合并单元格数量
                    var rowspanNum = 0;
                    // 是否合并过驳回原因单元格
                    var mergeRowSpan = false;

                    // 视频承诺审核状态
                    var videoPromiseCheckFlag = "0";//
                    // 手持身份证审核状态
                    var videoHoldIdCardCheckFlag = "0";
                    // 手持银行卡审核状态
                    var videoBankCardCheckFlag = "0";
                    var checkFlag = "0";
                    // materialType:1-身份证；2-银行凭证；3-银行凭证不可用原因；4-视频承诺；5-手持身份证视频；6-手持新银行卡视频
                    $(data).each(function (index, element) {
                        if (element.materialType === '2') {
                            voucherMaterial += '<input type="hidden" name="needFile" value="' + element.filePath + '"/> ' +
                                '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/>' +
                                '<a class="read reCheck" href="' + element.filePath + '" target="_blank" >' + element.fileName + '</a>' + 
                                (operationType != 'query' ? '<i class="Hui-iconfont Hui-iconfont-del3 deleteVoucher" style="margin-left:5px;cursor:pointer;color:red;" data-filepath="' + element.filePath + '" data-filename="' + element.fileName + '" data-dealdtlappno="' + element.dealDtlAppNo + '"></i>' : '') + '</br>';
                            if (!uploadVoucherFlag){
                                rowspanNum = rowspanNum + 1;
                            }
                            uploadVoucherFlag = true;
                            checkFlag = element.checkFlag;
                        } else if (element.materialType === '3') {
                            if (!notVoucherFlag){
                                rowspanNum = rowspanNum + 1;
                            }
                            notVoucherFlag = true;
                        }else if (element.materialType === '4') {
                            if (!videoFlag && asset > 20000 && OnlineTransferVol.appCheckOrder.existWhiteList === '1'){
                                rowspanNum = rowspanNum + 1;
                            }
                            videoPromiseHtml += '<input type="hidden" name="needFile" value="' + element.filePath + '"/> ' +
                                '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/>' +
                                '<a class="read reCheck" href="' + element.filePath + '" target="_blank" >' + element.fileName + '</a>' + 
                                (operationType != 'query' ? '<i class="Hui-iconfont" style="margin-left:5px;cursor:pointer;color:red;" data-filepath="' + element.filePath + '" data-filename="' + element.fileName + '" data-dealdtlappno="' + element.dealDtlAppNo + '"></i>' : '') + '</br>';
                            videoFlag = true;
                            videoPromiseCheckFlag = element.checkFlag;
                        }
                    });

                    if (uploadVoucherFlag) {
                        voucherTr.push('证明原卡不可用凭证');
                        if (operationType != 'query') {
                            var uploadButton = '<form action="" enctype="multipart/form-data" method="post" name="uploadForm" id="uploadForm"><input type="file" name="file" style="width: 180px" accept=".jpg,.png,.jpeg,.gif"/><input type="hidden" name="dealAppNo" value="' + dealAppNo + '"/><input type="hidden" name="materialType" value="2"/><a class="read reCheck" href="javascript:void(0)" id ="uploadFile" class="btn radius btn-secondary">上传</a></form>';
                            voucherMaterial += uploadButton;
                        }
                        voucherTr.push(voucherMaterial);
                        voucherTr.push("需要原件");
                        voucherTr.push('<select name="materialCheckFlag" id="materialCheckFlag" class="select"  ' + (operationType == "query" ? "disabled" : "") + ' ><option value="0">请选择</option><option value="1" ' + (checkFlag == 1 ? "selected" : "") + '>合格</option><option value="2" ' + (checkFlag == 2 ? "selected" : "") + '>不合格</option></select>');

                        voucherTrAppendHtml = '<tr class="text-c"><td>' + voucherTr.join('</td><td>') + '</td><td rowspan="' + rowspanNum + '"><textarea style="overflow:auto;height:100%;width:100%;" id="checkFaildDesc"   ' + (operationType == "query" ? "disabled" : "") + ' ></textarea></td></tr>';
                        mergeRowSpan = true;
                    }
                    // 用户没上传原卡不可用凭证，资产大于2000，长显证明原卡不可用凭证这个功能
                    if (!uploadVoucherFlag && asset >= 2000) {
                        voucherTr.push('证明原卡不可用凭证');
                        if (operationType != 'query') {
                            var uploadButton = '<form action="" enctype="multipart/form-data" method="post" name="uploadForm" id="uploadForm"><input type="file" name="file" style="width: 180px" accept=".jpg,.png,.jpeg,.gif"/><input type="hidden" name="dealAppNo" value="' + dealAppNo + '"/><input type="hidden" name="materialType" value="2"/><a class="read reCheck" href="javascript:void(0)" id ="uploadFile" class="btn radius btn-secondary">上传</a></form>';
                            voucherMaterial += uploadButton;
                        }
                        voucherTr.push(voucherMaterial);
                        voucherTr.push("需要原件");
                        voucherTr.push('<select name="materialCheckFlag" id="materialCheckFlag" class="select"  ' + (operationType == "query" ? "disabled" : "") + ' ><option value="0">请选择</option><option value="1" ' + (checkFlag == 1 ? "selected" : "") + '>合格</option><option value="2" ' + (checkFlag == 2 ? "selected" : "") + '>不合格</option></select>');
                        rowspanNum = rowspanNum + 1;
                        voucherTrAppendHtml = '<tr class="text-c"><td>' + voucherTr.join('</td><td>') + '</td><td rowspan="' + rowspanNum + '"><textarea style="overflow:auto;height:100%;width:100%;" id="checkFaildDesc"   ' + (operationType == "query" ? "disabled" : "") + ' ></textarea></td></tr>';
                        mergeRowSpan = true;
                    }
                    if (notVoucherFlag) {
                        notProvideVoucherTr.push('无法提供不可用凭证原因');
                        var notProvideVoucher = "";
                        materialCheckFlag = "";
                        $(data).each(function (index, element) {
                            if (element.materialType == '3') {
                                notProvideVoucher += '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/> ' + element.notProvideReason;
                                materialCheckFlag = '<select name="materialCheckFlag" id="materialCheckFlag" class="select"  ' + (operationType == "query" ? "disabled" : "") + ' ><option value="0">请选择</option><option value="1" ' + (element.checkFlag == 1 ? "selected" : "") + '>合格</option><option value="2" ' + (element.checkFlag == 2 ? "selected" : "") + '>不合格</option></select>';
                            }
                        });

                        notProvideVoucherTr.push(notProvideVoucher);
                        notProvideVoucherTr.push('需要核实原因的真实性');
                        notProvideVoucherTr.push(materialCheckFlag);
                        if (mergeRowSpan){
                            notProvideTrAppendHtml = '<tr class="text-c"><td>' + notProvideVoucherTr.join('</td><td>') + '</td></tr>';
                        }else{
                            notProvideTrAppendHtml = '<tr class="text-c"><td>' + notProvideVoucherTr.join('</td><td>') + '</td><td rowspan="' + rowspanNum + '"><textarea style="overflow:auto;height:100%;width:100%;" id="checkFaildDesc"   ' + (operationType == "query" ? "disabled" : "") + ' ></textarea></td></tr>';
                        }
                    }

                    // 20000元以上档位并且在crm白名单中展示视频承诺
                    if (asset > 20000 && OnlineTransferVol.appCheckOrder.existWhiteList === '1') {
                        videoPromiseTr.push('视频承诺');
                        if (operationType != 'query') {
                            var uploadButton = '<form action="" enctype="multipart/form-data" method="post" name="uploadVideoForm" id="uploadVideoForm"><input type="file" name="file" style="width: 180px" accept=".avi,.mp4,.mov,.wmv,.flv,.mpeg,.webm"/><input type="hidden" name="dealAppNo" value="' + dealAppNo + '"/><input type="hidden" name="materialType" value="4"/><a class="read reCheck" href="javascript:void(0)" id ="uploadVideoFile" class="btn radius btn-secondary">上传</a></form>';
                            videoPromiseHtml += uploadButton;
                        }
                        videoPromiseTr.push(videoPromiseHtml);
                        videoPromiseTr.push("需要核实视频");
                        videoPromiseTr.push('<select name="videoCheckFlag" id="videoCheckFlag" class="select"  ' + (operationType == "query" ? "disabled" : "") + ' ><option value="0">请选择</option><option value="1" ' + (videoPromiseCheckFlag === 1 ? "selected" : "") + '>合格</option><option value="2" ' + (videoPromiseCheckFlag === 2 ? "selected" : "") + '>不合格</option></select>');

                        if (mergeRowSpan){
                            videoPromiseHtml = '<tr class="text-c"><td>' + videoPromiseTr.join('</td><td>') + '</td></tr>';
                        }else{
                            videoPromiseHtml = '<tr class="text-c"><td>' + videoPromiseTr.join('</td><td>') + '</td><td rowspan="' + rowspanNum + '"><textarea style="overflow:auto;height:100%;width:100%;" id="checkFaildDesc"   ' + (operationType == "query" ? "disabled" : "") + ' ></textarea></td></tr>';
                        }
                        
                        // 新增手持身份证视频行
                        var videoHoldIdCardTr = [];
                        var videoHoldIdCardHtml = "";
                        videoHoldIdCardTr.push('手持身份证视频');
                        if (operationType != 'query') {
                            var uploadButton = '<form action="" enctype="multipart/form-data" method="post" name="uploadHoldIdCardForm" id="uploadHoldIdCardForm"><input type="file" name="file" style="width: 180px" accept=".avi,.mp4,.mov,.wmv,.flv,.mpeg,.webm"/><input type="hidden" name="dealAppNo" value="' + dealAppNo + '"/><input type="hidden" name="materialType" value="5"/><a class="read reCheck" href="javascript:void(0)" id ="uploadHoldIdCardFile" class="btn radius btn-secondary">上传</a></form>';
                            videoHoldIdCardHtml = uploadButton;
                            $(data).each(function (index, element) {
                                if (element.materialType === '5') {
                                    videoHoldIdCardHtml = '<input type="hidden" name="needFile" value="' + element.filePath + '"/> ' +
                                        '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/>' +
                                        '<a class="read reCheck" href="' + element.filePath + '" target="_blank" >' + element.fileName + '</a>' + 
                                        (operationType != 'query' ? '<i class="Hui-iconfont" style="margin-left:5px;cursor:pointer;color:red;" data-filepath="' + element.filePath + '" data-filename="' + element.fileName + '" data-dealdtlappno="' + element.dealDtlAppNo + '"></i>' : '') + '</br>' + uploadButton;
                                    videoHoldIdCardCheckFlag = element.checkFlag;
                                }
                            });
                        } else {
                            $(data).each(function (index, element) {
                                if (element.materialType === '5') {
                                    videoHoldIdCardHtml += '<input type="hidden" name="needFile" value="' + element.filePath + '"/> ' +
                                        '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/>' +
                                        '<a class="read reCheck" href="' + element.filePath + '" target="_blank" >' + element.fileName + '</a>' + 
                                        (operationType != 'query' ? '<i class="Hui-iconfont " style="margin-left:5px;cursor:pointer;color:red;" data-filepath="' + element.filePath + '" data-filename="' + element.fileName + '" data-dealdtlappno="' + element.dealDtlAppNo + '"></i>' : '') + '</br>';
                                    videoHoldIdCardCheckFlag = element.checkFlag;
                                }
                            });
                        }
                        videoHoldIdCardTr.push(videoHoldIdCardHtml);
                        videoHoldIdCardTr.push("需要核实视频");
                        videoHoldIdCardTr.push('<select name="videoHoldIdCardCheckFlag" id="videoHoldIdCardCheckFlag" class="select"  ' + (operationType == "query" ? "disabled" : "") + ' ><option value="0">请选择</option><option value="1" ' + (videoHoldIdCardCheckFlag == 1 ? "selected" : "") + '>合格</option><option value="2" ' + (videoHoldIdCardCheckFlag == 2 ? "selected" : "") + '>不合格</option></select>');
                        var videoHoldIdCardTrHtml = '';
                        if (mergeRowSpan){
                            videoHoldIdCardTrHtml = '<tr class="text-c"><td>' + videoHoldIdCardTr.join('</td><td>') + '</td></tr>';
                        }else{
                            videoHoldIdCardTrHtml = '<tr class="text-c"><td>' + videoHoldIdCardTr.join('</td><td>') + '</td><td rowspan="' + rowspanNum + '"><textarea style="overflow:auto;height:100%;width:100%;" id="checkFaildDesc"   ' + (operationType == "query" ? "disabled" : "") + ' ></textarea></td></tr>';
                        }

                        // 新增手持新银行卡视频行
                        var videoBankCardTr = [];
                        var videoBankCardHtml = "";
                        videoBankCardTr.push('手持新银行卡视频');
                        if (operationType != 'query') {
                            var uploadButton = '<form action="" enctype="multipart/form-data" method="post" name="uploadBankCardForm" id="uploadBankCardForm"><input type="file" name="file" style="width: 180px" accept=".avi,.mp4,.mov,.wmv,.flv,.mpeg,.webm"/><input type="hidden" name="dealAppNo" value="' + dealAppNo + '"/><input type="hidden" name="materialType" value="6"/><a class="read reCheck" href="javascript:void(0)" id ="uploadBankCardFile" class="btn radius btn-secondary">上传</a></form>';
                            videoBankCardHtml = uploadButton;
                            $(data).each(function (index, element) {
                                if (element.materialType === '6') {
                                    videoBankCardHtml = '<input type="hidden" name="needFile" value="' + element.filePath + '"/> ' +
                                        '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/>' +
                                        '<a class="read reCheck" href="' + element.filePath + '" target="_blank" >' + element.fileName + '</a>' + 
                                        (operationType != 'query' ? '<i class="Hui-iconfont Hui-iconfont-del3 deleteVoucher" style="margin-left:5px;cursor:pointer;color:red;" data-filepath="' + element.filePath + '" data-filename="' + element.fileName + '" data-dealdtlappno="' + element.dealDtlAppNo + '"></i>' : '') + '</br>' + uploadButton;
                                    videoBankCardCheckFlag = element.checkFlag;
                                }
                            });
                        } else {
                            $(data).each(function (index, element) {
                                if (element.materialType === '6') {
                                    videoBankCardHtml += '<input type="hidden" name="needFile" value="' + element.filePath + '"/> ' +
                                        '<input type="hidden" name="dealDtlAppNo" value="' + element.dealDtlAppNo + '"/>' +
                                        '<a class="read reCheck" href="' + element.filePath + '" target="_blank" >' + element.fileName + '</a>' + 
                                        (operationType != 'query' ? '<i class="Hui-iconfont Hui-iconfont-del3 deleteVoucher" style="margin-left:5px;cursor:pointer;color:red;" data-filepath="' + element.filePath + '" data-filename="' + element.fileName + '" data-dealdtlappno="' + element.dealDtlAppNo + '"></i>' : '') + '</br>';
                                    videoBankCardCheckFlag = element.checkFlag;
                                }
                            });
                        }
                        videoBankCardTr.push(videoBankCardHtml);
                        videoBankCardTr.push("需要核实视频");
                        videoBankCardTr.push('<select name="videoBankCardCheckFlag" id="videoBankCardCheckFlag" class="select"  ' + (operationType == "query" ? "disabled" : "") + ' ><option value="0">请选择</option><option value="1" ' + (videoBankCardCheckFlag == 1 ? "selected" : "") + '>合格</option><option value="2" ' + (videoBankCardCheckFlag == 2 ? "selected" : "") + '>不合格</option></select>');
                        var videoBankCardTrHtml = '';
                        if (mergeRowSpan){
                            videoBankCardTrHtml = '<tr class="text-c"><td>' + videoBankCardTr.join('</td><td>') + '</td></tr>';
                        }else{
                            videoBankCardTrHtml = '<tr class="text-c"><td>' + videoBankCardTr.join('</td><td>') + '</td><td rowspan="' + rowspanNum + '"><textarea style="overflow:auto;height:100%;width:100%;" id="checkFaildDesc"   ' + (operationType == "query" ? "disabled" : "") + ' ></textarea></td></tr>';
                        }
                        // 合并视频承诺相关行
                        videoPromiseHtml = videoPromiseHtml + videoHoldIdCardTrHtml + videoBankCardTrHtml;
                    }
                }

                faceTr.push('人脸识别结果照片');
                var face = "";
                $(data).each(function (index, element) {
                    if (element.materialType == '99') {
                        face += '<a href="javascript:void(0);" onclick="downloadImage(\'' + element.filePath + '\', \'' + element.fileName + '\', 1)">' +  element.fileName + '</a></br>';
                    }
                });

                faceTr.push(face);
                faceTr.push("");
                faceTr.push("");
                var faceHtml = '<tr class="text-c"><td>' + faceTr.join('</td><td>') + '</td><td></td></tr>';

                certificateTr.push('客户OCR长传照片（非换卡上传）');
                var certificate = "";
                $(data).each(function (index, element) {
                    if (element.materialType == '1') {
                        certificate += '<a href="javascript:void(0);" onclick="downloadImage(\'' + element.filePath + '\', \'' + element.fileName + '\', 2)">' +  element.fileName + '</a>';
                    }
                });

                certificateTr.push(certificate);
                certificateTr.push("");
                certificateTr.push("");
                var certificateTrAppendHtml = '<tr class="text-c"><td>' + certificateTr.join('</td><td>') + '</td><td></td></tr>';


                $("#" + fundId).append(voucherTrAppendHtml + notProvideTrAppendHtml + videoPromiseHtml + faceHtml + certificateTrAppendHtml);

            }
            // 上传按钮
            $("#uploadFile").off();
            $("#uploadFile").on('click', function () {
                var uploadForm = $("#uploadForm")[0];
                var formData = new FormData(uploadForm);
                var uri = TmsCounterConfig.UPLOAD_VOUCHER_FILE || {};
                $.ajax({
                    url: uri,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var respCode = data.code || '';
                        var respDesc = data.desc || '';
                        if (CommonUtil.isSucc(respCode)) {
                            CommonUtil.layer_alert(respDesc);
                            QueryCheckOrder.queryMergeTransCheckOrderById(OnlineTransferVol.checkedOrder.dealAppNo, OnlineTransferVol.refreshMaterailDtlBack, "1", "Z910105");
                        } else {
                            CommonUtil.layer_alert(respDesc);
                        }
                    },
                    error: function (returndata) {
                        CommonUtil.layer_alert(returndata);
                    }
                });
            });

            $("#uploadVideoFile").on('click', function () {
                var uploadVideoForm = $("#uploadVideoForm")[0];
                var formData = new FormData(uploadVideoForm);
                var uri = TmsCounterConfig.UPLOAD_VOUCHER_FILE || {};
                $.ajax({
                    url: uri,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var respCode = data.code || '';
                        var respDesc = data.desc || '';
                        if (CommonUtil.isSucc(respCode)) {
                            CommonUtil.layer_alert(respDesc);
                            QueryCheckOrder.queryMergeTransCheckOrderById(OnlineTransferVol.checkedOrder.dealAppNo, OnlineTransferVol.refreshMaterailDtlBack, "1", "Z910105");
                        } else {
                            CommonUtil.layer_alert(respDesc);
                        }
                    },
                    error: function (returndata) {
                        CommonUtil.layer_alert(returndata);
                    }
                });
            });

            // 添加手持身份证视频上传事件
            $("#uploadHoldIdCardFile").off();
            $("#uploadHoldIdCardFile").on('click', function () {
                var uploadHoldIdCardForm = $("#uploadHoldIdCardForm")[0];
                var formData = new FormData(uploadHoldIdCardForm);
                var uri = TmsCounterConfig.UPLOAD_VOUCHER_FILE || {};
                $.ajax({
                    url: uri,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var respCode = data.code || '';
                        var respDesc = data.desc || '';
                        if (CommonUtil.isSucc(respCode)) {
                            CommonUtil.layer_alert(respDesc);
                            QueryCheckOrder.queryMergeTransCheckOrderById(OnlineTransferVol.checkedOrder.dealAppNo, OnlineTransferVol.refreshMaterailDtlBack, "1", "Z910105");
                        } else {
                            CommonUtil.layer_alert(respDesc);
                        }
                    },
                    error: function (returndata) {
                        CommonUtil.layer_alert(returndata);
                    }
                });
            });
            
            // 添加手持新银行卡视频上传事件
            $("#uploadBankCardFile").off();
            $("#uploadBankCardFile").on('click', function () {
                var uploadBankCardForm = $("#uploadBankCardForm")[0];
                var formData = new FormData(uploadBankCardForm);
                var uri = TmsCounterConfig.UPLOAD_VOUCHER_FILE || {};
                $.ajax({
                    url: uri,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (data) {
                        var respCode = data.code || '';
                        var respDesc = data.desc || '';
                        if (CommonUtil.isSucc(respCode)) {
                            CommonUtil.layer_alert(respDesc);
                            QueryCheckOrder.queryMergeTransCheckOrderById(OnlineTransferVol.checkedOrder.dealAppNo, OnlineTransferVol.refreshMaterailDtlBack, "1", "Z910105");
                        } else {
                            CommonUtil.layer_alert(respDesc);
                        }
                    },
                    error: function (returndata) {
                        CommonUtil.layer_alert(returndata);
                    }
                });
            });

            // 添加图片删除事件
            $(document).on('click', '.deleteVoucher', function() {
                var dealDtlAppNo = $(this).data('dealdtlappno');

                layer.confirm('确认要删除此图片吗？', function(index){
                    var uri = TmsCounterConfig.DELETE_VOUCHER_FILE || {};
                    var reqparamters = {
                        "dealDtlAppNo": dealDtlAppNo
                    };

                    var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
                    CommonUtil.ajaxAndCallBack(paramters, OnlineTransferVol.delCardBack);

                    layer.close(index);
                });
            });

        },
    delCardBack:function(data){
        var respCode = data.code || '';
        var respDesc = data.desc || '';

        if (CommonUtil.isSucc(respCode)) {
            CommonUtil.layer_alert('删除成功');
            QueryCheckOrder.queryMergeTransCheckOrderById(OnlineTransferVol.checkedOrder.dealAppNo, OnlineTransferVol.refreshMaterailDtlBack, "1", "Z910105");
        } else {
            CommonUtil.layer_alert(respDesc || '删除失败，请稍后重试');
        }
    }



    }
;
