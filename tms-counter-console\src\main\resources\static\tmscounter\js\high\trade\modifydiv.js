/**
*修改分红方式
*<AUTHOR>
*@date 2017-04-01 15:16
*/
$(function(){
	ModifyDiv.custInfo = {};
    ModifyDiv.fundDivList = [];
    ModifyDiv.currDate = '';
    ModifyDiv.init();
});
var ModifyDiv = {
	init:function(){
        ModifyDiv.initBind();
		var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
		$(".selectconsCode").html(selectConsCodesHtml);
        ModifyDiv.urlParams = CommonUtil.getParamJson() || {};
        // 初始化CRM参数数据
        OnLineOrderFile.initCrmUrlWithOutAppoint(ModifyDiv.urlParams,ModifyDiv.initCustQuery);
	},
    initCustQuery:function(hboneNo){
        /**
         * 查询客户基本信息
         */
        HighCustInfo.queryCustInfo(hboneNo);

        /**
         * 绑定客户选择事件
         */
        setTimeout(ModifyDiv.custInfoBind(), 2000);
    },
    initBind:function(){
        $("#confimModifyDivBtn").on('click',function(){
            ModifyDiv.confirm();
        });
        $("#queryCustInfoBtn").on('click',function(){
            ModifyDiv.initCustQuery("");
        });

        $("#custNo").on('dblclick',function(){
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        $(".selectAgened").change(function(){
            var selectAgened = $(this).val();
            if("1" == selectAgened){
                $("#transactorInfoForm").show();
            }else{
                $("#transactorInfoForm").hide();
            }
        });
    },

    initCrmModifyDivOrder:function(urlParams){
        var outSource = urlParams['source'];
        if('crm' == outSource){
             var deleteElement = [];
            // 初始化选择分红方式
            var crmFundCode = urlParams['fundCode'];
            $(".selectModifyDiv").each(function (index, element) {
                var selectedOrderIndex = $(element).val();
                var orderForm = ModifyDiv.fundDivList[selectedOrderIndex];
                var fundCode = orderForm.fundCode;

                if(fundCode == crmFundCode){
                    $(element).click();
                }

            });
            $("#rsList").find("tr").each(function(index, element){
                var orderForm = ModifyDiv.fundDivList[index];
                var fundCode = orderForm.fundCode;
                if(fundCode != crmFundCode){
                    deleteElement.push(element);
                }
            });

            $(deleteElement).each(function (index, element) {
				$(element).remove();
            });
        }
    },
	/**
	 * 购买客户信息绑定事件
	 */
	custInfoBind:function(){
		
		 $(".selectcust").click(function(){
	    	 $(this).attr('checked','checked').siblings().removeAttr('checked');
	    	 var selectIndex = $(this).attr("index");
	    	 ModifyDiv.custInfo = HighCustInfo.custList[selectIndex] || {};
	    	 // 机构用户
	    	 if('0' == ModifyDiv.custInfo.invstType || '2' == ModifyDiv.custInfo.invstType){
	    		// 显示经办人信息
		    	 HighCustInfo.queryCustTransInfo(ModifyDiv.custInfo.custNo, ModifyDiv.custInfo.disCode);
	    		 // 代理
	    		 $(".selectAgened").val('1');
	    		 $("#transactorInfoForm").show();
	    	 }
	    	 
	    	 // 查询用户分红方式列表
	    	 ModifyDiv.queryFundDivInfo(ModifyDiv.custInfo.custNo, ModifyDiv.custInfo.disCode);
	    });
	},
	
	/***
	 * 确认修改分红方式
	 */	
	confirm : function(){
		CommonUtil.disabledBtn("confimModifyDivBtn");
		var  uri= TmsCounterConfig.MODIFYDIV_CONFIRM_URL ||  '';
		var fundDiv = ModifyDiv.modifyDiv || {};
		
		if(fundDiv.allowModifyDivMode == '0'){
			CommonUtil.layer_tip("暂不支持修改分红方式");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}
		
		var validRst = Valid.valiadateFrom($("#othetInfoForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}
		
		var validRst = Valid.valiadateFrom($("#modifyDivConfirmForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}
		
		var othetInfoForm = $("#othetInfoForm").serializeObject();
		var modifyDivConfirmForm = $("#modifyDivConfirmForm").serializeObject();
		//代理人OR经办人信息
		var transactorInfoForm = {};
		if('0' == ModifyDiv.custInfo.invstType || '2' == ModifyDiv.custInfo.invstType){
			transactorInfoForm = $("#transactorInfoForm").serializeObject();
		}else{
			transactorInfoForm = $("#agenInfoForm").serializeObject();
		}
		if(othetInfoForm.agentFlag == '1'){
			var validTransactorRst = Valid.valiadateFrom($("#transactorInfoForm"));
			if(!validTransactorRst.status){
				CommonUtil.layer_tip(validTransactorRst.msg);
				CommonUtil.enabledBtn("confimModifyDivBtn");
				return false;
			}
			if('0' == transactorInfoForm.transactorIdType && !Valid.id_rule(transactorInfoForm.transactorIdNo)){
				CommonUtil.layer_tip("经办人证件号格式不正确");
				CommonUtil.enabledBtn("confimModifyDivBtn");
				return false;
			}
		}
		
		othetInfoForm.appDtm = modifyDivConfirmForm.appDt + '' +modifyDivConfirmForm.appTm;
		if(!Valid.valiadTradeTime(modifyDivConfirmForm.appTm)){
			CommonUtil.layer_tip("请重新输入下单时间，时间范围为9:00:00-14:59:59");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}
		
		if(CommonUtil.formatDateToStr(ModifyDiv.currDate, 'yyyyMMdd') > othetInfoForm.appDt){
			CommonUtil.layer_tip("申请日期不能在当前日期之前");
			CommonUtil.enabledBtn("confimModifyDivBtn");
			return false;
		}
		
		var reqparamters ={"targetDiv":fundDiv.targetDiv || '',
			"fundDiv": JSON.stringify(fundDiv),
			"custInfoForm":JSON.stringify(ModifyDiv.custInfo),
			"othetInfoForm":JSON.stringify(othetInfoForm),
            "materialinfoForm":JSON.stringify(OnLineOrderFile.buildOrderCheckFile())};
		reqparamters.transactorInfoForm = JSON.stringify(transactorInfoForm);
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ModifyDiv.callBack);
		
	},
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
            layer.confirm('修改分红方式提交成功', {
                btn: ['确定'] //按钮
            }, function(){
                layer.closeAll();
                // 刷新页面
                if(OnLineOrderFile.isCrm()){
                    CommonUtil.closeCurrentUrl();
                }else{
                    // 刷新页面
                    CommonUtil.reloadUrl();
                }
            });
		}else{
			CommonUtil.layer_tip("修改分红方式提交失败,"+respDesc);
		}
		CommonUtil.enabledBtn("confimModifyDivBtn");
	},
	
	/**
	 * 查询基金分红方式
	 * @param custNo 
	 * @param disCode
	 */
	queryFundDivInfo:function(custNo, disCode){
		var  uri= TmsCounterConfig.QUERY_MODIFY_DIV_URL ||  {};
		var reqparamters = {"custNo":custNo, "disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, ModifyDiv.queryFundDivInfoCallBack);
	},
	/**
	 * 处理基金分红方式信息
	 */
	queryFundDivInfoCallBack:function(data){
		var bodyData = data.body || {};
		ModifyDiv.fundDivList = bodyData.fundDivList || [];
		if(ModifyDiv.fundDivList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
		}
		$("#rsList").empty();
		$(ModifyDiv.fundDivList).each(function(index,element){
			var trList = [];
			trList.push(CommonUtil.formatData(element.fundCode));
			trList.push(CommonUtil.formatData(element.fundAttr));
			trList.push(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,element.divMode,'--'));
			element.targetDiv = '';
			
			if(element.divMode == '0'){
				element.targetDiv = '1';
			}else if(element.divMode == '1'){
				element.targetDiv = '0';
			}
			trList.push(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,element.targetDiv,'--'));
			var trHtml = '<tr class="text-c"><td><input  class="selectModifyDiv" type="radio" name="modifydiv" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td></tr>';
			$("#rsList").append(trHtml);
			
			//选择修改分红方式
			$(".selectModifyDiv").off();
			$(".selectModifyDiv").on('click', function(){

				$(this).attr('checked','checked').siblings().removeAttr('checked');
				var selectIndex = $(this).attr("value");
				var modifyDiv = ModifyDiv.fundDivList[selectIndex];
			    ModifyDiv.buildDeal(modifyDiv);
			    ModifyDiv.modifyDiv = modifyDiv;
			    //查询产品信息
			    ModifyDiv.queryFundInfo(modifyDiv.fundCode);

			    OnLineOrderFile.initMaterial(ModifyDiv.urlParams, ModifyDiv.getSelectCustMaterial(), OnLineOrderFile.CRM_OP_CHECK_NODE_PRE);
			});
		});

        ModifyDiv.initCrmModifyDivOrder(ModifyDiv.urlParams, ModifyDiv.initCustQuery);
		
	},

    /**
     *
     * @Description  初始化材料
     *
     * @param null
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 17:30
     **/
    getSelectCustMaterial:function(){
        var custSelectOrder = {};
        var modifyDiv = ModifyDiv.modifyDiv || {};
        custSelectOrder["hboneno"] = ModifyDiv.custInfo.hboneNo;// 一账通帐号
        custSelectOrder["pcode"] =  modifyDiv.fundCode;// 产品代码
        custSelectOrder["busiid"] =  OnLineOrderFile.CRM_MODIFY_DIV;// 业务类型ID // CRM业务类型 购买
        return custSelectOrder;
    },
	/**
	 * 构建修改分红方式订单
	 * @param modifyDiv
	 */
	buildDeal:function(modifyDiv){
		modifyDiv = modifyDiv || {};
		$("#fundCodeId").html(CommonUtil.formatData(modifyDiv.fundCode));//基金代码
		$("#divModeId").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,modifyDiv.divMode,'--'));//当前修改分红方式
		
		if(modifyDiv.divMode == '0'){
			modifyDiv.targetDiv = '1';
		}else if(modifyDiv.divMode == '1'){
			modifyDiv.targetDiv = '0';
		}else if(modifyDiv.divMode == '9'){
			modifyDiv.targetDiv = '9';//不支持修改分红方式
		}
		$("#targetDivId").html(CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP,modifyDiv.targetDiv,'--'));//目标分红方式
		$("#productChannelId").html(CommonUtil.formatData(modifyDiv.productChannel));//产品通道
	},
	/**
	 * 查询高端产品基本信息
	 * @param fundCode 产品代码
	 * @param appDt 申请日期
	 * @param appTm 申请时间
	 * @param busyType 业务类型 0-购买 1-赎回
	 */
	queryFundInfo:function(fundCode){
		
		QueryHighProduct.queryFundInfo(fundCode);
	    ModifyDiv.fundInfo = QueryHighProduct.fundInfo || {};
		if(CommonUtil.isEmpty(ModifyDiv.fundInfo.fundCode)){
			CommonUtil.layer_tip("没有查询到此产品");
			return false;
		}
		
		// 构建产品基本信息
		ModifyDiv.buildFundInfo(ModifyDiv.fundInfo);
		
		// 查询工作日
		QueryHighProduct.queryHighproductWorkDay(fundCode);
	},
	
	/**
	 * 构建产品信息
	 */
	buildFundInfo:function(fundInfo){
		$("#fundName").html(fundInfo.fundAttr || '');
		$("#taCodeId").html(fundInfo.taCode);
		$("#productTypeId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_TYPE_MAP, fundInfo.fundType));
		$("#shareClassId").html(CommonUtil.getMapValue(CONSTANTS.FUND_SHARECLASS_MAP, fundInfo.shareClass));
		$("#productChannelId").html(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, fundInfo.productChannel));
	}
	
};

