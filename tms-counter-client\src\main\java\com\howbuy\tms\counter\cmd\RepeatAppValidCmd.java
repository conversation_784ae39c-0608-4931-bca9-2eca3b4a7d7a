/**
 *Copyright (c) 2018, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.cmd;
/**
 * @description:(重复单校验结果) 
 * @reason:
 * <AUTHOR>
 * @date 2018年7月9日 上午10:32:26
 * @since JDK 1.6
 */
public class RepeatAppValidCmd {
    
    /**
     * 是否重复订单 0-否 1-是
     */
    private String repeateFlag;

    public String getRepeateFlag() {
        return repeateFlag;
    }

    public void setRepeateFlag(String repeateFlag) {
        this.repeateFlag = repeateFlag;
    }
    

}

