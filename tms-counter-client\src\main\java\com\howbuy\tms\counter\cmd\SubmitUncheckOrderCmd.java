package com.howbuy.tms.counter.cmd;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description:(柜台提交审核订单)
 * <AUTHOR>
 * @date 2017年3月28日 下午6:24:53
 * @since JDK 1.7
 */
public class SubmitUncheckOrderCmd implements Serializable{
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    
    private static final long serialVersionUID = 5769630336011397623L;

    /**
     * 审核申请流水号
     */
    private String dealAppNo;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 子交易账号
     */
    private String subTxAcctNo;

    /**
     * 分销机构号
     */
    private String disCode;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金份额类型
     */
    private String fundShareClass;
    
    /**
     * 目标基金代码
     */
    private String tFundCode;

    /**
     * 目标基金份额类型
     */
    private String tFundShareClass;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 交易码
     */
    private String txCode;

    /**
     * 支付方式
     *01-自划款
     *04-代扣款
     *06-储蓄罐
     */
    private String paymentType;

    /**
     * 手续费折扣
     */
    private BigDecimal discountRate;

    /**
     * 协议类型
     *1-普通公募基金投资
     *2-组合公募基金投资
     *3-暴力定投
     */
    private String protocolType;

    /**
     * 协议号
     */
    private String protocolNo;
    
    /**
     * 审核状态，0-未审核，1-审核通过，2-审核不通过
     */
    private String checkFlag;

    /**
     * 基金分红方式
     *0-红利再投；
     *1-现金红利
     */
    private String fundDivMode;

    /**
     * 客户订单号
     */
    private String dealNo;

    /**
     * 经办人证件号
     */
    private String transactorIdNo;

    /**
     * 经办人证件类型
     */
    private String transactorIdType;

    /**
     * 经办人姓名
     */
    private String transactorName;

    /**
     * 操作员编号
     */
    private String operatorNo;

    /**
     * 投资顾问代码
     */
    private String consCode;
    
    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是)
     */
    private String isAgened;
    
    /**
     * 是否顺延  0-取消；1-顺延
     */
    private String largeRedmFlag;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回描述
     */
    private String description;

    /**
     * 备注
     */
    private String memo;

    /**
     * 创建人
     */
    private String creator;

    /**
     *审核人
     */
    private String checker;

    /**
     *修改人
     */
    private String modifier;

    /**
     * 创建日期时间
     */
    private Date createDtm;

    /**
     *审核日期时间
     */
    private Date checkDtm;

    /**
     *更新日期时间
     */
    private Date updateDtm;

    public String getDealAppNo() {
        return dealAppNo;
    }

    public void setDealAppNo(String dealAppNo) {
        this.dealAppNo = dealAppNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getSubTxAcctNo() {
        return subTxAcctNo;
    }

    public void setSubTxAcctNo(String subTxAcctNo) {
        this.subTxAcctNo = subTxAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String gettFundCode() {
        return tFundCode;
    }

    public void settFundCode(String tFundCode) {
        this.tFundCode = tFundCode;
    }

    public String gettFundShareClass() {
        return tFundShareClass;
    }

    public void settFundShareClass(String tFundShareClass) {
        this.tFundShareClass = tFundShareClass;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

    public String getOperatorNo() {
        return operatorNo;
    }

    public void setOperatorNo(String operatorNo) {
        this.operatorNo = operatorNo;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getCheckDtm() {
        return checkDtm;
    }

    public void setCheckDtm(Date checkDtm) {
        this.checkDtm = checkDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getIsAgened() {
        return isAgened;
    }

    public void setIsAgened(String isAgened) {
        this.isAgened = isAgened;
    }

    public String getLargeRedmFlag() {
        return largeRedmFlag;
    }

    public void setLargeRedmFlag(String largeRedmFlag) {
        this.largeRedmFlag = largeRedmFlag;
    }
    
    

}