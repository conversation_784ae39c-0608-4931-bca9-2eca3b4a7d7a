$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckSell.checkOrder = {};	 
	CheckSell.init(checkId,custNo,disCode,idNo);
	CheckSell.isAdviser = false;
	CheckSell.productInfo = {};

});

var CheckSell = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,CheckSell.queryCheckOrderByIdBack);
		
		$("#returnBtn").on('click',function(){
			CheckSell.confirm(CounterCheck.Faild);
		});
		
		$("#succBtn").on('click',function(){
			CheckSell.confirm(CounterCheck.Succ);
		});
		
		$("#fundCode").on('blur',function(){
			var productCode = $("#fundCode").val();
			QueryFundInfo.queryAdviserOrFundProduct(productCode, CheckSell.queryProductInfoCallBack);
		});

		$("#appRatio").on('keyup',function(){
			CheckSell.validatorRedeemRatio(this);
		});
		
		$("#appVol").blur(function(){
			$("#appVol").val(CommonUtil.formatAmount($("#appVol").val()));
		});
	}, 
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';
		
		var sellConfirmForm = $("#sellConfirmForm").serializeObject();
		
		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckSell.checkFaildDesc = $("#checkFaildDesc").val();
		}else{
			var validRst = Valid.valiadateFrom($("#sellConfirmForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}

			var checkResultReply = CheckSell.checkSellValid(sellConfirmForm,CheckSell.checkOrder);
			if(!checkResultReply.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(checkResultReply.tip);
				return false;
			}
		}
		var reqparamters ={"checkFaildDesc":CheckSell.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckSell.checkOrder), "productInfoForm":JSON.stringify(CheckSell.productInfo)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckSell.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	
	/**
	 * 审核赎回校验
	 */
	checkSellValid:function(checkForm , orderForm){
		var fundCode = checkForm.fundCode || '';
		var largeRedmFlag = checkForm.largeRedmFlag || '';
		var cpAcctNo = checkForm.cpAcctNo || '';
		
		var result = {"status":true,"tip":''};
		
		if(fundCode != (orderForm.fundCode || '')){
			result.status = false;
			result.tip = "基金/产品代码不匹配，请重新确认";
			return result;
		}
		
		if(largeRedmFlag != (orderForm.largeRedmFlag || '')){
			result.status = false;
			result.tip = "巨额赎回不匹配，请重新确认";
			return result;
		}

		if (CheckSell.isAdviser) {
			var appRatio = checkForm.appRatio || '';
			if(appRatio != CommonUtil.multiply(orderForm.appRatio, 100) || ''){
				result.status = false;
				result.tip = "赎回比例不匹配，请重新确认";
				return result;
			}
		}else {
			var appVol = CommonUtil.unFormatAmount(checkForm.appVol) || '';
			if(appVol != (orderForm.appVol || '')){
				result.status = false;
				result.tip = "申请份额不匹配，请重新确认";
				return result;
			}
		}

		
		var orderCpAcctNo = orderForm.cpAcctNo || '';
		if(cpAcctNo != orderCpAcctNo){
			result.status = false;
			result.tip = "银行卡不匹配，请重新确认";
			return result;
		}
		
		return result;
	},

	/**
	 * 校验赎回比例(事件:onkeyup)
	 */
	validatorRedeemRatio: function (thisObj) {
		var redeemRatio = thisObj.value;
		//if (!/^[0-9]+$/.test(redeemRatio)) {
		//if (!/^[0-9]+(\.[0-9]{1,2})?$/.test(redeemRatio)) {
		if (!/^[0-9]+\.?[0-9]{0,2}$/.test(redeemRatio)) {
			CommonUtil.layer_tip('只能输入数字且小数点后两位');
			thisObj.value = '';
		}

		if (!(redeemRatio <= 100 && redeemRatio >= 0)) {
			CommonUtil.layer_tip('请输入赎回比例1~100');
			thisObj.value = '';
		}
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckSell.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(CheckSell.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckSell.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}

		QueryFundInfo.queryAdviserOrFundProduct(CheckSell.checkOrder.fundCode, CheckSell.queryProductInfoCallBack);

		if($("#unusualTransType").length > 0){
			$("#unusualTransType").html(CommonUtil.getMapValue(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP, CheckSell.checkOrder.unusualTransType, ''));
		}

		// 交易回款方式
		if($("#redeemCapitalFlag").length > 0){
			$("#redeemCapitalFlag").html(CommonUtil.getMapValue(CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG, CheckSell.checkOrder.redeemCapitalFlag, ''));
		}
		// 是否全赎
		if($("#allRedeemFlag").length > 0){
			$("#allRedeemFlag").html(CommonUtil.getMapValue(CONSTANTS.ALL_REDEEM_FLAG_MAP, CheckSell.checkOrder.allRedeemFlag, ''));
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(CheckSell.checkOrder.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").html(CheckSell.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckSell.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(CheckSell.checkOrder.transactorName);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckSell.checkOrder.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(CheckSell.checkOrder.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(CheckSell.checkOrder.memo);
		}
	},




	queryCustAdviserHodlInfo:function(order){
		var uri= TmsCounterConfig.QUERY_ADVISER_REDEEM_INFO_URL ||  {};
		var custNo = QueryCustInfo.custInfo.custNo || '';
		var disCode = QueryCustInfo.custInfo.disCode || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		var fundCode ='';
		var appDt ='';
		var appTm ='';
		var protocolNo ='';
		if(!order){
			fundCode = $("#fundCode").val();
			appDt = $("#appDt").val();
			appTm = $("#appTm").val();
		}else {
			appDt = order.appDt;
			appTm = order.appTm;
			fundCode = order.fundCode;
			protocolNo = order.protocolNo;
		}
		var reqparamters = {'protocolNo':protocolNo,'appDt':appDt,'appTm':appTm,"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckSell.queryCustAdviserHoldFundInfoCallBack);
	},

	/**
	 * 处理基金持仓信息
	 */
	queryCustAdviserHoldFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		QueryFundInfo.dtlList = bodyData.balanceDtlList || [];

		if(QueryFundInfo.dtlList == null || QueryFundInfo.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
		}

		var selectHtml ='';
		$(QueryFundInfo.dtlList).each(function(index,element){
			selectHtml +='<option indexnum ="'+index+'" value="'+element.cpAcctNo+'">'+element.bankName+''+element.bankAcctNo+' </option>';
		});

		$("#selectBank").html(selectHtml);
		$("#selectBank").change(function(){
			var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
			var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
			if($("#bankCode").length > 0){
				$("#bankCode").val(selectDtl.bankCode);
			}
		});

		$("#selectBank").change();

		if(QueryFundInfo.dtlList.length >0){
			var selectDtl = QueryFundInfo.dtlList[0] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
		}
	},


	queryProductInfoCallBack:function(productInfo){
		var isAdviser = productInfo.adviserFlag || false;
		CheckSell.isAdviser = isAdviser;
		CheckSell.productInfo = productInfo;
		console.info("====>CheckSell.isAdviser" +  CheckSell.isAdviser);

		CheckSell.productChangeStyle(isAdviser);


		if (CheckSell.isAdviser) {
			// 使用 输入框中的 接口返回的 产品代码 做后续查询操作
			CheckSell.checkOrder.fundCode = productInfo.productCode;
			CheckSell.queryCustAdviserHodlInfo(CheckSell.checkOrder);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}
			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}
			if($("#fundStatus").length > 0){
				$("#fundStatus").html(productInfo.isSoldOpen == '1' ? "可赎回" : "暂停赎回");
			}

		}else {
			// 使用 输入框中的 接口返回的 产品代码 做后续查询操作
			CheckSell.checkOrder.fundCode = productInfo.fundCode;
			QueryFundInfo.queryCustHodlInfo(CheckSell.checkOrder);

			var isCommonFund = QueryFundInfo.checkFundInfo(productInfo);

			if($("#fundName").length > 0){
				$("#fundName").html(productInfo.productName || '');
			}

			if($("#fundRiskLevel").length > 0){
				$("#fundRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.FUND_RISK_LEVELS_MAP, productInfo.riskLevel, ''));
			}

			if($("#fundStatus").length > 0){
				$("#fundStatus").html(CommonUtil.getMapValue(CONSTANTS.FUND_STATE, productInfo.fundStat));
			}

			if(!isCommonFund){
				return false;
			}
		}

		if($("#selectBank").length > 0){
			$("#selectBank").val(CheckSell.checkOrder.cpAcctNo);
		}

		var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
		var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
		$("#availVol").html(selectDtl.availVol);

	},

	productChangeStyle:function(isAdviser){
		if(isAdviser){
			$("#appRatio").val("");
			$("#appVol").val("--");
			$("#appVolCapitalForSell").val("--");

			$("#appRatio").attr("isnull","false");
			$("#appVol").removeAttr("isnull");
			$("#appVolCapitalForSell").removeAttr("isnull");

			$("#appRatio").attr('readonly', false);
			$("#appVol").attr('readonly', true);
			$("#appVolCapitalForSell").attr('readonly', true);
			$("#largeRedmFlag").find("option[value='0']").attr("selected",true);
			$("#largeRedmFlag").find("option[value='1']").remove();
		}else {
			$("#appRatio").val("--");
			$("#appVol").val("");
			$("#appVolCapitalForSell").val("");

			$("#appRatio").removeAttr("isnull");
			$("#appVol").attr("isnull","false");
			$("#appVolCapitalForSell").attr("isnull","false");

			$("#appRatio").attr('readonly', true);
			$("#appVol").attr('readonly', false);
			$("#appVolCapitalForSell").attr('readonly', false);
			$("#largeRedmFlag").append('<option value="1">顺延</option>')
			$("#largeRedmFlag").find("option[value='1']").attr("selected",true);

		}
	},
}
