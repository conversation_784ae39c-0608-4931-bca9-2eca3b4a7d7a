/**
 *Copyright (c) 2018, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.howbuy.common.page.PageResult;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.model.HighProductBaseInfoPageModel;
import com.howbuy.interlayer.product.model.fund.queryfundinfo.FundTaInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(子页面查询高端产品信息) 
 * @reason:
 * <AUTHOR>
 * @date 2018年2月5日 下午6:04:03
 * @since JDK 1.6
 */
@Controller
public class QueryHighProdSubPage {
    private Logger logger = LogManager.getLogger(QueryHighProdSubPage.class);
    @Autowired
    private HighProductService  highProductService;

    /**
     * 
     * queryHighProdSubPage:(子页面查询高端产品信息)
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月6日 上午11:38:48
     */
    @RequestMapping("/tmscounter/high/queryhighprodsubpage.htm")
    public ModelAndView queryHighProdSubPage(HttpServletRequest request,HttpServletResponse response) throws Exception{
        
        String productCode = request.getParameter("productCode");
        String productName = request.getParameter("productName");
        String productChannel = request.getParameter("productChannel");
        if(StringUtils.isNotEmpty(productName)){
            // 中文名称解码
            productName = URLDecoder.decode(productName, StandardCharsets.UTF_8.name());
        }
        
        int pageNum = 1;
        int pageSize = 1000;
        logger.info("productCode:{}, productName:{}, productChannel:{}, pageNum:{}, pageSize:{}", productCode, productName, productChannel, pageNum, pageSize);
        
        String productNameOrCode = null;
        if(StringUtils.isNotEmpty(productCode)){
            productNameOrCode = productCode;
        }else if(StringUtils.isNotEmpty(productName)){
            productNameOrCode = productName;
        }
        HighProductBaseInfoPageModel highProductBaseInfoPageModel = highProductService.getHighProductBaseInfo(productChannel, productNameOrCode, null, null,
                null, pageNum, pageSize);
        List<HighProductBaseInfoModel> productList = new ArrayList<HighProductBaseInfoModel>();
        if(highProductBaseInfoPageModel != null){
             productList = highProductBaseInfoPageModel.getHighProductBaseInfoModelList(); 
        }
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("productList", productList);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    @RequestMapping("/tmscounter/high/queryhighprodcounter.htm")
    public void queryHighProdCounterPage(HttpServletRequest request,HttpServletResponse response) throws Exception{
        
        String fundCode = request.getParameter("fundCode");
        String fundAttr = request.getParameter("fundName");
        if(StringUtils.isNotEmpty(fundAttr)){
            // 中文名称解码
            fundAttr = URLDecoder.decode(fundAttr, StandardCharsets.UTF_8.name());
        }
        
        int pageNum = 1;
        int pageSize = 1000;
        PageResult<FundTaInfoModel> pageResult = highProductService.getFundTaInfoListByPage(fundCode, fundAttr, pageNum, pageSize);
        if (pageResult == null){
            pageResult = new PageResult<FundTaInfoModel>(pageNum, pageSize, 0);
        }

        Map<String, Object> rst = new HashMap<String, Object>(16);
        rst.put("fundTaList", pageResult.getPageList());
        rst.put("pageNum", pageResult.getPageNo());
        rst.put("totalPage", pageResult.getTotalPage());
        rst.put("totalCount", pageResult.getTotalCount());
    
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(rst);
        WebUtil.write(response, tmsCounterResult);
    }
}

