$(function(){
	Init.init();
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckSell.checkOrder = {};	 
	CheckSell.init(checkId,custNo,disCode,idNo);
	CheckSell.isSelfFlag = false;
	CheckSell.productInfo = {};

});

var CheckSell = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
		QueryCheckOrder.queryCheckOrderById(checkId,CheckSell.queryCheckOrderByIdBack);
		
		$("#returnBtn").on('click',function(){
			CheckSell.confirm(CounterCheck.Faild);
		});

		$("#feiDanBtn").on('click',function(){
			CheckSell.confirm(CounterCheck.Abolish);
		});

		$("#succBtn").on('click',function(){
			CheckSell.confirm(CounterCheck.Succ);
		});
		
		$("#appRatio").on('keyup',function(){
			CheckSell.validatorRedeemRatio(this);
		});
		
	},
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';
		
		var sellConfirmForm = $("#sellConfirmForm").serializeObject();
		
		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};
		
		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckSell.checkFaildDesc = $("#checkFaildDesc").val();
		} else if(CounterCheck.Abolish == checkStatus){
			CheckSell.checkFaildDesc = "废单操作";
		} else {
			var validRst = Valid.valiadateFrom($("#sellConfirmForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}
		}
		var reqparamters ={"checkFaildDesc":CheckSell.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckSell.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckSell.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
			CommonUtil.disabledBtn("feiDanBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	/**
	 * 审核赎回校验
	 */
	checkSellValid:function(checkForm , orderForm){
		// var fundCode = checkForm.fundCode || '';
		// var largeRedmFlag = checkForm.largeRedmFlag || '';
		// var cpAcctNo = checkForm.cpAcctNo || '';
		
		// var result = {"status":true,"tip":''};
		
		// if(fundCode != (orderForm.fundCode || '')){
		// 	result.status = false;
		// 	result.tip = "基金/产品代码不匹配，请重新确认";
		// 	return result;
		// }
		//
		// if(largeRedmFlag != (orderForm.largeRedmFlag || '')){
		// 	result.status = false;
		// 	result.tip = "巨额赎回不匹配，请重新确认";
		// 	return result;
		// }

		// if (CheckSell.isSelfFlag) {
		// 	var appRatio = checkForm.appRatio || '';
		// 	if(appRatio != CommonUtil.multiply(orderForm.appRatio, 100) || ''){
		// 		result.status = false;
		// 		result.tip = "赎回比例不匹配，请重新确认";
		// 		return result;
		// 	}
		// }else {
		// 	var appVol = CommonUtil.unFormatAmount(checkForm.appVol) || '';
		// 	if(appVol != (orderForm.appVol || '')){
		// 		result.status = false;
		// 		result.tip = "申请份额不匹配，请重新确认";
		// 		return result;
		// 	}
		// }

		
		// var orderCpAcctNo = orderForm.cpAcctNo || '';
		// if(cpAcctNo != orderCpAcctNo){
		// 	result.status = false;
		// 	result.tip = "银行卡不匹配，请重新确认";
		// 	return result;
		// }
		
		// return result;
	},

	/**
	 * 校验赎回比例(事件:onkeyup)
	 */
	validatorRedeemRatio: function (thisObj) {
		var redeemRatio = thisObj.value;
		if (!/^[0-9]+$/.test(redeemRatio)) {
			CommonUtil.layer_tip('请输入整数');
			thisObj.value = '';
		}

		if (!(redeemRatio <= 100 && redeemRatio >= 0)) {
			CommonUtil.layer_tip('请输入赎回比例1~100');
			thisObj.value = '';
		}
	},
	
	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckSell.checkOrder = bodyData.checkOrder || {};
		if(CheckSell.checkOrder.protocolType == '6'){
			CheckSell.isSelfFlag = true;
		}

		if(CommonUtil.isEmpty(CheckSell.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckSell.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		$("#fundCode").html(CheckSell.checkOrder.fundCode);
		$("#fundName").html(CheckSell.checkOrder.fundName);
		$("#protocolNo").html(CheckSell.checkOrder.protocolNo);
		$("#protocolType").html(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, CheckSell.checkOrder.protocolType));
		$("#customOrRatio").html(CommonUtil.getMapValue(CONSTANTS.GM_COUNTEE_SUSTOM_RATIO_TYPE, CheckSell.checkOrder.customRatioType));
		$("#largeRedmFlag").html(CommonUtil.getMapValue(CONSTANTS.LARGE_REDM_FLAG_MAP, CheckSell.checkOrder.largeRedmFlag));
		$("#openFlag").html(CommonUtil.getMapValue(CONSTANTS.PROTOCOL_OPEN_FLAG_MAP, CheckSell.checkOrder.openFlag));

		// var appRatio = CheckSell.checkOrder.appRatio * 100;
		appRatio = CommonUtil.formatData(CommonUtil.formatPercent(CheckSell.checkOrder.appRatio, "", 2));
		$("#appRatio").val(appRatio);

		if($("#unusualTransType").length > 0){
			$("#unusualTransType").html(CommonUtil.getMapValue(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP, CheckSell.checkOrder.unusualTransType, ''));
		}

		// 交易回款方式
		if($("#redeemCapitalFlag").length > 0){
			$("#redeemCapitalFlag").html(CommonUtil.getMapValue(CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG, CheckSell.checkOrder.redeemCapitalFlag, ''));
		}

		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(CheckSell.checkOrder.appDt);
		}
		if($("#appTm").length > 0){
			$("#appTm").html(CheckSell.checkOrder.appTm);
		}
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckSell.checkOrder.consCode, ''));
		}
		if($("#transactorName").length > 0){
			$("#transactorName").html(CheckSell.checkOrder.transactorName);
		}
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckSell.checkOrder.transactorIdType, ''));
		}
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(CheckSell.checkOrder.transactorIdNo);
		}
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(CheckSell.checkOrder.memo);
		}

		var selectBankHtml = '<option bankacct= "'+ CheckSell.checkOrder.bankAcct + '" bankcode= "'+ CheckSell.checkOrder.bankCode + '" value="' + CheckSell.checkOrder.cpAcctNo + '">'+ CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP,CheckSell.checkOrder.bankCode) + ''+ CheckSell.checkOrder.bankAcct + ' </option>';
		$("#selectBank").empty();
		$("#selectBank").html(selectBankHtml);

		$("#selectBank").change(function(){
			if($("#selectBank").find("option:selected")[0]){
				var indexNum = $("#selectBank").find("option:selected")[0].index;
				//var custBank = SellFund.custBanks[indexNum] || {} ;
				if($("#bankCode").length > 0){
					$("#bankCode").val(CheckSell.checkOrder.bankCode);
				}
			}
		});
		$("#selectBank").change();

		var customRatioType = CheckSell.checkOrder.customRatioType;
		if(customRatioType == '1'){
			var bodyData = data.body || {};
			CheckSell.showSelfRedeemRatioTableList(bodyData);
		} else {
			CheckSell.showSelfRedeemVolTableList(bodyData);
		}
	},

	showSelfRedeemVolTableList:function (bodyData) {
		if (CheckSell.isSelfFlag) {
			var trAppendTableHtml =
				'<tr className="text-c">' +
				'<th>基金代码</th>'+
				'<th>基金名称</th>'+
				'<th>赎回比例</th>'+
				'<th>可用份额</th>'+
				'<th>不可赎回原因</th>'+
				'<th>基金状态</th>'+
				'<th>自定义赎回份额</th>'+
				' </tr>' ;
			$("#sellRedeemInfoId").empty();
			$("#sellRedeemInfoId").append(trAppendTableHtml);

			var redeemMemoStr = "可赎";
			var querySellVolResult = bodyData.querySellVolResult;
			var customFundInfoResDtos = querySellVolResult.customFundInfoResDtos;
			if (customFundInfoResDtos == null || customFundInfoResDtos.length <= 0) {
				return;
			} else{
				// 重新渲染列表
				$(customFundInfoResDtos).each(function (index, element) {
					if (CheckSell.isSelfFlag) {
						var redeemMemo = CheckSell.showSelfRedeemVolTable(index, element, null);
						if(!isEmpty(redeemMemo)){
							redeemMemoStr = redeemMemo;
						}
					}
				});
			}
			$("#canRedeemFlag").html(redeemMemoStr);
		}

	},

	showSelfRedeemRatioTableList:function (bodyData) {
		if (CheckSell.isSelfFlag) {
			var trAppendTableHtml =
				'<tr className="text-c">' +
				'<th>基金代码</th>'+
				'<th>基金名称</th>'+
				'<th>赎回比例</th>'+
				'<th>是否可赎回</th>'+
				'<th>不可赎回原因</th>'+
				'<th>基金状态</th>'+
				'<th>自定义赎回份额</th>'+
				' </tr>' ;
			$("#sellRedeemInfoId").empty();
			$("#sellRedeemInfoId").append(trAppendTableHtml);

			var canRedeem = bodyData.queryTrialResult.canRedeem||'0';
			var reason = bodyData.queryTrialResult.redeemReason ||'';
			var showCanReddeemMemo = CommonUtil.getMapValue(CONSTANTS.FUND_CAN_REDEEM_MAP, canRedeem);
			if(!isEmpty(reason) && canRedeem == '0'){
				showCanReddeemMemo =  showCanReddeemMemo + "(" + reason + ")"
			}
			$("#canRedeemFlag").html(showCanReddeemMemo);

			var queryTrialResult = bodyData.queryTrialResult;
			var redeemProductResDtos = queryTrialResult.redeemProductResDtos;
			if (redeemProductResDtos == null || redeemProductResDtos.length <= 0) {
				return;
			} else{
				// 重新渲染列表
				$(redeemProductResDtos).each(function (index, element) {
					if (CheckSell.isSelfFlag) {
						CheckSell.showSelfRedeemRatioTable(index, element, null);
					}
				});
			}
		}

	},

	showSelfRedeemVolTable:function (index, element, bodyData) {
		var fundCode = element.fundCode;
		var redeemVol = element.sellVol;
		var sellRatio = element.sellRatio * 100;
		var availableVol = element.availableVol;

		var redeemMemo = "";
		var minAcctVol = element.minAcctVol;
		var limitMax = element.limitMax;
		var limitMin = element.limitMin;

		if(redeemVol > availableVol){
			redeemMemo = fundCode + "基金超过可用份额" + "份";
		}
		if(redeemVol < limitMin){
			redeemMemo = fundCode + "基金小于最低限额:" + limitMin + "份";
		}
		if(redeemVol > limitMax){
			redeemMemo = fundCode + "基金大于最高限额:" + limitMax + "份";
		}
		var lastVol = availableVol - redeemVol;
		if(lastVol < minAcctVol && lastVol > 0){
			redeemMemo = fundCode + "基金小于最低持有:" + minAcctVol + "份";
		}
		var redeemMemoStr = "可赎";
		if(!isEmpty(redeemMemo)){
			redeemMemoStr = redeemMemo;
		}
		var tdList = [];
		tdList.push('<td><input id="fundCode_' + index + '" name="fundCode_' + index + '" type="text" value="' + fundCode + '" readonly="true"></td>');
		tdList.push('<td>' + element.fundName + '</td>');
		// tdList.push('<td>' + element.holdRatio + '%' + '</td>');
		tdList.push('<td>' + sellRatio + '%' + '</td>');
		tdList.push('<td>' + availableVol + '</td>');
		tdList.push('<td>' + redeemMemoStr + '</td>');
		tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_STATE, element.fundStat) + '</td>');
		tdList.push('<td><input type="text" class="redeemRatioClass" name="redeemVol_' + index + '" id="redeemVol_' + index + '" isnull="false" datatype="s" value="' + redeemVol + '" errormsg="赎回份额" placeholder="请输入" readonly="true" onkeyup="SellFund.validatorRedeemVol(' + index + ');"></td>');

		var trAppendHtml = '<tr class="text-c" id="redeemInfo_tr_' + index + '">' + tdList.join() + '</tr>';
		$("#sellRedeemInfoId").append(trAppendHtml);

		return redeemMemo;
	},

	showSelfRedeemRatioTable:function (index, element, bodyData) {
		var fundCode = element.fundCode;
		var redeemVol = element.redeemVol;

		var tdList = [];
		tdList.push('<td><input id="fundCode_' + index + '" name="fundCode_' + index + '" type="text" value="' + fundCode + '" readonly="true"></td>');
		tdList.push('<td>' + element.fundName + '</td>');
		// tdList.push('<td>' + element.holdRatio + '%' + '</td>');
		tdList.push('<td>' + element.sellRatio + '%' + '</td>');
		tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_CAN_REDEEM_MAP, element.fundCanRedeem) + '</td>');
		tdList.push('<td>' + element.fundReason + '</td>');
		tdList.push('<td>' + CommonUtil.getMapValue(CONSTANTS.FUND_STATE, element.fundStat) + '</td>');
		tdList.push('<td><input type="text" class="redeemRatioClass" name="redeemVol_' + index + '" id="redeemVol_' + index + '" isnull="false" datatype="s" value="' + redeemVol + '" errormsg="赎回份额" placeholder="请输入" readonly="true" onkeyup="SellFund.validatorRedeemVol(' + index + ');"></td>');

		var trAppendHtml = '<tr class="text-c" id="redeemInfo_tr_' + index + '">' + tdList.join() + '</tr>';
		$("#sellRedeemInfoId").append(trAppendHtml);
	},

	queryCustAdviserHodlInfo:function(order){
		var uri= TmsCounterConfig.QUERY_ADVISER_REDEEM_INFO_URL ||  {};
		var custNo = QueryCustInfo.custInfo.custNo || '';
		var disCode = QueryCustInfo.custInfo.disCode || '';
		if(isEmpty(custNo)){
			CommonUtil.layer_tip("请先选择用户");
			return false;
		}
		var fundCode ='';
		var appDt ='';
		var appTm ='';
		var protocolNo ='';
		if(!order){
			fundCode = $("#fundCode").val();
			appDt = $("#appDt").val();
			appTm = $("#appTm").val();
		}else {
			appDt = order.appDt;
			appTm = order.appTm;
			fundCode = order.fundCode;
			protocolNo = order.protocolNo;
		}
		var reqparamters = {'protocolNo':protocolNo,'appDt':appDt,'appTm':appTm,"fundCode":fundCode,"custNo":custNo,"disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,null,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckSell.queryCustAdviserHoldFundInfoCallBack);
	},

	/**
	 * 处理基金持仓信息
	 */
	queryCustAdviserHoldFundInfoCallBack:function(data){
		var bodyData = data.body || {};
		QueryFundInfo.dtlList = bodyData.balanceDtlList || [];

		if(QueryFundInfo.dtlList == null || QueryFundInfo.dtlList.length <=0){
			CommonUtil.layer_tip("没有查询到持仓信息");
		}

		var selectHtml ='';
		$(QueryFundInfo.dtlList).each(function(index,element){
			selectHtml +='<option indexnum ="'+index+'" value="'+element.cpAcctNo+'">'+element.bankName+''+element.bankAcctNo+' </option>';
		});

		$("#selectBank").html(selectHtml);
		$("#selectBank").change(function(){
			var indexNum = $("#selectBank").find("option:selected").attr("indexnum");
			var selectDtl = QueryFundInfo.dtlList[indexNum] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
			if($("#bankCode").length > 0){
				$("#bankCode").val(selectDtl.bankCode);
			}
		});

		$("#selectBank").change();

		if(QueryFundInfo.dtlList.length >0){
			var selectDtl = QueryFundInfo.dtlList[0] || {} ;
			$("#availVol").html(CommonUtil.formatAmount(selectDtl.availVol));
		}
	}


}
