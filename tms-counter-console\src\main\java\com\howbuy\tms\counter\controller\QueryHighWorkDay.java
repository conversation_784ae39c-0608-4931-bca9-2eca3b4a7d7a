/**
 *Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;


import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(查询高端工作日) 
 * @reason:
 * <AUTHOR>
 * @date 2018年3月17日 下午1:27:55
 * @since JDK 1.6
 */
@Controller
public class QueryHighWorkDay {
    
    private static final Logger LOGGER = LogManager.getLogger(QueryHighWorkDay.class);
    
    @Autowired
    private TmsCounterService tmsCounterService;
    
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    
    @RequestMapping("/tmscounter/queryhighworkday.htm")
    public ModelAndView queryHighWorkDay(HttpServletRequest request, 
            HttpServletResponse response) throws Exception {
        
        String productCode = request.getParameter("productCode");
        LOGGER.info("QueryHighWorkDay|productCode:{}", productCode);
        
        if(StringUtils.isEmpty(productCode)){
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR);
        }
        
        HighProductBaseInfoBean  highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
        if(highProductBaseBean == null ){
            LOGGER.info("product :{} not exit", productCode);
            throw new TmsCounterException(TmsCounterResultEnum.PRODUCT_IS_NULL);
        }
        
        String workDay = getWorkDay();
        
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String, Object>(16);
        body.put("workDay", workDay);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }
    
    
    /**
     * 
     * getWorkDay:(获取高端系统当前工作日)
     * @return
     * <AUTHOR>
     * @throws Exception 
     * @date 2018年3月17日 下午1:44:35
     */
    private String getWorkDay() throws Exception{
        return tmsCounterService.getHighSystemWorkDay();
    }
    
    
}

