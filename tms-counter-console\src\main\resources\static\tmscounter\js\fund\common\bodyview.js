/**
*视图共通显示-模块
*<AUTHOR>
*@date 2018-05-10 10:26
*/
var BodyView = {
	
		
		
	/**
	 *  份额合并或迁移列表显示共通----------------------------------------------start-------
	 */
		
	/**
	 *  设置转出信息
	 */
	setTransOutTableView : function(id, data, disCode){
		//console.log(data);
		
		// 转出信息:SubmitUncheckOrderDtlDto
		$("#"+id).empty();
		if(data.length <=0){
			var trHtml = '<tr><td colspan="11">没有查询到转出信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			$(data).each(function(index,element){
				var trList = [];
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.fundName));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
				trList.push(CommonUtil.formatData(element.protocolNo));
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
				trList.push(CommonUtil.formatAmount(element.appVol));
				trList.push(CommonUtil.formatAmount(element.appVol));
				trList.push("0");
				trList.push("0");
				trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCode));
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#"+id).append(trAppendHtml);
			});
		}
	},

   
	
	/**
	 *  设置转出信息
	 */
	setTransOutTableViewNew : function(highId,fundId,data, disCode,capitalId){
		$("#"+fundId).empty();
		$("#"+highId).empty();
		$("#"+capitalId).empty();

		if(data.length <=0){
			var trHtml = '<tr><td colspan="11">没有查询到转出信息</td></tr>';
			$("#"+fundId).append(trHtml);
			$("#"+highId).append(trHtml);
			return false;
			
		}else{
			$(data).each(function(index,element){
				if (element.protocolType == "4") {
					var highTrList = [];
					highTrList.push(CommonUtil.formatData(element.taCode));
					highTrList.push(CommonUtil.formatData(element.fundCode));
					highTrList.push(CommonUtil.formatData(element.fundName));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
					highTrList.push(CommonUtil.formatData(element.bankAcct));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
					highTrList.push(CommonUtil.formatAmount(element.appVol));
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
					highTrList.push(CommonUtil.formatData(element.protocolNo));
					
					var disCodeShow = element.disCode;
					if(CommonUtil.isEmpty(disCodeShow)){
						disCodeShow = disCode;
					}
					
					highTrList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCodeShow));
					
					var highTrAppendHtml = '<tr class="text-c"><td>'+ highTrList.join('</td><td>') + '</td></tr>';
					$("#"+highId).append(highTrAppendHtml);
				}else if(element.protocolType == "91"){

                    var trList = [];
                    trList.push('');
                    trList.push(CommonUtil.formatData(element.fundCode));
                    trList.push(CommonUtil.formatData(element.fundName));
                    trList.push(CommonUtil.formatData(element.protocolNo));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                    trList.push(CommonUtil.formatData(element.bankAcct));
                    trList.push(CommonUtil.formatData(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode)));
                    trList.push(CommonUtil.formatAmount(element.appVol));
                    var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                    $("#"+capitalId).append(trAppendHtml);

                } else {
					var trList = [];
					trList.push(CommonUtil.formatData(element.fundCode));
					trList.push(CommonUtil.formatData(element.fundName));
					trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
					trList.push(CommonUtil.formatData(element.bankAcct));
					trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
					trList.push(CommonUtil.formatAmount(element.appVol));
					trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
					trList.push(CommonUtil.formatData(element.protocolNo));
					
					var disCodeShow = element.disCode;
					if(CommonUtil.isEmpty(disCodeShow)){
						disCodeShow = disCode;
					}
					trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCodeShow));
					
					var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
					$("#"+fundId).append(trAppendHtml);
				}
			});
		}
	},
	
	/**
	 *  设置转入信息
	 */
	setTransInTableView: function(id, data){
		//console.log(data);
		
		// 转入信息:CounterOrderDto
		$("#"+id).empty();
		if(data == null){
			var trHtml = '<tr><td colspan="7">没有查询到转入信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			var trList = [];
			trList.push(CommonUtil.formatData(data.fundCode));
			trList.push(CommonUtil.formatData(data.bankAcct));
			trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP,data.disCode));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, data.protocolType));
			trList.push(CommonUtil.formatData(data.protocolNo));
			trList.push(CommonUtil.formatAmount(data.appVol));// 并入后份额

			var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
			$("#"+id).append(trAppendHtml);
		}
	},
	
	/**
	 *  设置转入银行信息
	 */
	setTransInBankTableView : function(id, data){
		//console.log(data);
		
		$("#"+id).empty();
		if(data == null){
			var trHtml = '<tr><td colspan="3">没有查询到转入的银行卡信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			var trList = [];
			trList.push(CommonUtil.formatData(data.bankAcct));
			trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, data.bankCode));
			trList.push(CommonUtil.formatData(data.bankRegionName));
			var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
			$("#"+id).append(trAppendHtml);
		}
	},
	
	/**
	 *  设置转入银行卡资产信息
	 */
	setTransInCustBalsTableView : function(id, data, disCode){
		//console.log(data);
		
		$("#"+id).empty();
		if(data.length <=0){
			var trHtml = '<tr><td colspan="11">没有查询到转入银行卡资产信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			$(data).each(function(index,element){
				var trList = [];
				trList.push(CommonUtil.formatData(element.fundCode));
				trList.push(CommonUtil.formatData(element.fundAttr));
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
				trList.push(CommonUtil.formatAmount(element.balanceVol));
				trList.push(CommonUtil.formatAmount(element.availVol));
				trList.push(CommonUtil.formatAmount(element.unconfirmedVol));
				trList.push(CommonUtil.formatAmount(element.justFrznVol));
				trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, disCode));
				trList.push(CommonUtil.formatData(element.protocolNo));
				trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType));
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#"+id).append(trAppendHtml);
			});
		}
	},

    /**
     *  设置最近打款记录
     */
    setLastRemitBodyTableView : function(id, data, disCode){
        //console.log(data);

        $("#"+id).empty();
        if(data.length <=0){
            var trHtml = '<tr><td colspan="11">没有查询到最近一次打款记录</td></tr>';
            $("#"+id).append(trHtml);
            return false;


        }else{
            $(data).each(function(index,element){
                var trList = [];
                trList.push(CommonUtil.formatData(element.realSettleDate));
                trList.push(CommonUtil.formatAmount(element.occurBalance));
                trList.push(CommonUtil.getMapValue(CONSTANTS.CAPTIAL_ORDER_PAY_STATUS_MAP, element.orderPayStatus));
                trList.push(CommonUtil.formatData(element.statusDesc));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode));
                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#"+id).append(trAppendHtml);
            });
        }
    },
	/**
	 * 设置订单信息
	 */
	setAppOrderInfoTableView : function(id, data){
		//console.log(data);
		
		$("#"+id).empty();
		if(data == null){
			var trHtml = '<tr><td colspan="7">没有查询到订单信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			var trList = [];
			trList.push(CommonUtil.formatData(data.dealAppNo, '--'));
			trList.push(CommonUtil.formatData(data.memo, '--'));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, data.checkFlag, ''));
			trList.push(CommonUtil.formatData(data.operatorNo,''));
			trList.push('柜台');
			trList.push(CommonUtil.formatData(data.appDt,''));
			trList.push(CommonUtil.formatData(data.appTm,''));

			var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
			$("#"+id).append(trAppendHtml);
		}
	},
	
	/**
	 * 设置订单详情信息
	 */
	setTradeOrderInfoTableView : function(id, data){
		//console.log(data);
		
		$("#"+id).empty();
		if(data.length <=0){
			var trHtml = '<tr><td colspan="17">没有查询到订单信息</td></tr>';
			$("#"+id).append(trHtml);

		}else{
			$(data).each(function(index,element){

                    var trList = [];
                    trList.push(CommonUtil.formatData(element.dealNo));
                    trList.push(CommonUtil.formatData(element.txAcctNo));//custNo
                    trList.push(CommonUtil.formatData(element.custName));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.Z_BUSICODE_MAP, element.zBusiCode));
                    trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME, element.mBusiCode));
                    trList.push(CommonUtil.formatData(element.fundCode));
                    trList.push(CommonUtil.formatData(element.fundName));

                    trList.push(CommonUtil.formatData(element.outBankAcct));//转出银行卡
                    trList.push(CommonUtil.formatData(element.outProtocolNo));//转出协议号

                    trList.push(CommonUtil.formatAmount(element.appVol));//申请份额

                    trList.push(CommonUtil.formatData(element.inBankAcct));//转入银行卡
                    trList.push(CommonUtil.formatData(element.inProtocolNo));//转入协议号

                    trList.push(CommonUtil.formatAmount(element.beforeInAvailVol));//转入卡合并前可用份额
                    trList.push(CommonUtil.formatAmount(element.afterInAvailVol));//转入卡合并后可用份额

                    trList.push(CommonUtil.getMapValue(CONSTANTS.TX_APP_FLAG, element.txAppFlag, '--'));//订单明细申请状态

                    trList.push(CommonUtil.getMapValue(CONSTANTS.TX_ACK_FLAG, element.txAckFlag, '--'));//订单明细确认状态

                    trList.push(CommonUtil.formatData(element.retDesc, '--'));//处理结果

                    var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                    $("#"+id).append(trAppendHtml);





			});
		}


	},

    /**
     * 设置资金在途信息
     */
    setIntrasitAssetTableView : function(id, data){
        //console.log(data);

        $("#"+id).empty();


        if(data.length <=0){
            var trHtml = '<tr><td colspan="10">没有查询到在途资产信息</td></tr>';
            $("#"+id).append(trHtml);
            return false;

        }else{
            $(data).each(function(index,element){
                var trList = [];
                trList.push('');
                trList.push(CommonUtil.formatData(element.fundCode));
                trList.push(CommonUtil.formatData(element.fundAttr));
                trList.push(CommonUtil.formatData(element.protocolNo));
                trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.formatData(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode)));
                trList.push(CommonUtil.formatAmount(element.balanceVol));
                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#"+id).append(trAppendHtml);
            });
        }

    },
	/**
	 * 设置定投合约信息
	 */
	setScheduleInfoTableView : function(id, data){
		
		$("#"+id).empty();
		if(data.length <=0){
			var trHtml = '<tr><td colspan="17">没有查询到定投合约信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			$(data).each(function(index,element){
                if(element.scheStatus == '3'){
                    return true;
                }
				var trList = [];
				trList.push(CommonUtil.formatData(element.scheId));
				trList.push(CommonUtil.formatData(element.scheName));
				trList.push(CommonUtil.getMapValue(CONSTANTS.SCHEDULE_TYPE_MAP, element.scheType, ''));
				trList.push(CommonUtil.formatData(element.txAcctNo));
				trList.push(CommonUtil.formatData(element.custName));
				trList.push(CommonUtil.formatData(element.bankAcct));
				trList.push(CommonUtil.getMapValue(CONSTANTS.SCHE_PLAN_STATUS_MAP, element.scheStatus, ''));
				
				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#"+id).append(trAppendHtml);
			});
		}
	},

    /**
     * 设置定投合约信息
     */
    setScheduleAllInfoTableView : function(id, data){
		data.aa;
        $("#"+id).empty();
        if(data.length <=0){
            var trHtml = '<tr><td colspan="17">没有查询到定投合约信息</td></tr>';
            $("#"+id).append(trHtml);
            return false;

        }else{
            $(data).each(function(index,element){
            	if(element.scheStatus == '3'){
            		return true;
				}
                var trList = [];
                trList.push(CommonUtil.formatData(element.scheId));
                trList.push(CommonUtil.formatData(element.scheName));
                trList.push(CommonUtil.getMapValue(CONSTANTS.SCHEDULE_TYPE_MAP, element.scheType, ''));
                trList.push(CommonUtil.getMapValue(CONSTANTS.SCHE_PLAN_STATUS_MAP, element.scheStatus, ''));
                trList.push(CommonUtil.formatData(element.scheActualDate));
                trList.push(CommonUtil.formatData(element.scheAmt));
                trList.push(CommonUtil.formatData(element.scheVol));
                trList.push(CommonUtil.formatData(element.productCode));
                trList.push(CommonUtil.formatData(element.productName));
                trList.push(CommonUtil.formatData('好买通道'));
                trList.push(CommonUtil.formatData(element.bankAcct));
                trList.push(CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode, ''));
                trList.push(CommonUtil.getMapValue(CONSTANTS.DISCODE_MAP, element.disCode, ''));

                var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
                $("#"+id).append(trAppendHtml);
            });
        }
    },

    /**
     * 设置客户信息
     * @param id
     * @param data
     * @returns {Boolean}
     */
	setCustInfoTableView : function(id, data){
		//console.log(data);
		var bodyData = data.body || {};
		var custInfoList = bodyData.custInfoList || [];
		
		$("#"+id).empty();
		if(custInfoList <=0){
			var trHtml = '<tr><td colspan="9">没有查询到客户信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			$(custInfoList).each(function(index,element){
				var trList = [];
				trList.push(CommonUtil.formatData(element.custNo,'--'));
				trList.push(CommonUtil.formatData(element.custName,'--'));
				trList.push(CommonUtil.getMapValue(CONSTANTS.INVST_TYPE_MAP,element.invstType, ''));
				trList.push(CommonUtil.getMapValue(CONSTANTS.CUST_STAT_MAP, element.custStat,'--'));
				if(element.invstType == '0'){//属于机构
					trList.push(CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, element.idType, ''));
				}
				if(element.invstType == '1'){
					trList.push(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, element.idType, ''));
				}
				if(element.invstType == '2'){
					trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, element.idType, ''));
				}
				trList.push(CommonUtil.formatData(element.idNo,'--'));
				trList.push(CommonUtil.getMapValue(CONSTANTS.QUALIFICATION_TYPE_MAP,element.investorType, '--'));
				trList.push(CommonUtil.getMapValue(CONSTANTS.RISK_LEVEL_MAP,element.custRiskLevel, '--'));
				trList.push(CommonUtil.getMapValue(DisCode.disCodesMap, element.disCode,'--'));

				var trAppendHtml = '<tr class="text-c"><td>'+ trList.join('</td><td>') + '</td></tr>';
				$("#"+id).append(trAppendHtml);
			});
		}
	},

	setCustInfo : function(id, custNo, idNo, disCode, callBack){
		$("#"+id).empty();
		QueryCustInfo.getCustInfos(custNo, idNo, disCode, callBack);
	},

	
	/**
	 *  份额合并或迁移列表显示共通----------------------------------------------end-------
	 */
	
	
	/**
	 * 设置表格操作信息
	 */
	setCheckOperInfoView : function(data){
		//console.log(data);
		
		if($("#appDt").length > 0){
			$("#appDt").html(data.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").html(data.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, data.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(data.transactorName);
		}
		
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, data.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(data.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(data.memo);
		}
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(data.agentFlag);
		}
		
		if($("#selectCancelCard").length > 0){
			$("#selectCancelCard").val(data.cancelCard)
		}
	},
	
	/**
	 * 设置控件操作信息
	 */
	setShowOperInfo : function(data){
		//console.log(data);
		
		if($("#appDt").length > 0){
			$("#appDt").val(data.appDt);
		}
		
		if($("#appTm").length > 0){
			$("#appTm").val(data.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").val(CommonUtil.getMapValue(ConsCode.consCodesMap, data.consCode, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").val(data.transactorIdNo);
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").val(data.transactorName);
		}
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").val(parseInt(data.transactorIdType));
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(data.memo);
		}
		
		if($(".selectAgened").length > 0){
			$(".selectAgened").val(data.agentFlag);
		}
	},
	
	/**
	 * 交易申请详情
	 * @param checkOrder
	 */
	showCounterOrderInfo:function(checkOrder){
		
		var idTypeValue ='';
		if(checkOrder.invstType == '0'){//属于机构
			idTypeValue = CommonUtil.getMapValue(CONSTANTS.JIGOU_ID_TYPE_MAP, checkOrder.idType, '');
		}
		if(checkOrder.invstType == '1'){
			idTypeValue = CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, checkOrder.idType, '');
		}
		if(checkOrder.invstType == '2'){
			idTypeValue = CommonUtil.getMapValue(CONSTANTS.PRODUCT_ID_TYPE_MAP, checkOrder.idType, '');
		}

		var detailList = [];
		detailList.push('<tr>'+
		     			'<td class="type">预申请单号</td>'+
		     			'<td>'+CommonUtil.formatData(checkOrder.dealAppNo,'')+'</td>'+
		     			'<td class="type">客户账号</td>'+
		     			'<td>'+CommonUtil.formatData(checkOrder.txAcctNo,'')+'</td>'+
		 	 			'</tr>');

		detailList.push('<tr>'+
		 				'<td class="type">客户名称</td>'+
		 				'<td>'+CommonUtil.formatData(checkOrder.custName,'')+'</td>'+
		 				'<td class="type">证件类型</td>'+
		 				'<td>'+idTypeValue+'</td>'+
						'</tr>');
		
		detailList.push('<tr>'+
				         '<td class="type">证件号码</td>'+
				         '<td>'+CommonUtil.formatData(checkOrder.idNo,'')+'</td>'+
				         '<td class="type" style="background-color: #dcdcdc;">业务类型</td>'+
				         '<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, checkOrder.txCode, '')+'</td>'+
		     	 	'</tr>');
		
		detailList.push('<tr>'+
				         '<td class="type">产品代码</td>'+
				         '<td>'+CommonUtil.formatData(checkOrder.fundCode,'')+'</td>'+
				         '<td class="type">产品名称</td>'+
				         '<td>'+CommonUtil.formatData(checkOrder.fundName,'')+'</td>'+
		    	 	'</tr>');
		
		
		detailList.push('<tr>'+
				         '<td class="type">赎回比例</td>'+
				         '<td>'+CommonUtil.formatAmount(CommonUtil.formatPercent(checkOrder.appRatio,'--'))+'</td>'+
						 '<td class="type">申请金额（元）</td>'+
						 '<td>'+CommonUtil.formatAmount(CommonUtil.formatData(checkOrder.appAmt,''))+'</td>'+
						'</tr>');

		var lineCol1 = '<tr>'+
			'<td class="type">申请份额（份）</td>'+
			'<td>'+CommonUtil.formatAmount(CommonUtil.formatData(checkOrder.appVol,''))+'</td>';
		var lineCol2 = '<td class="type"></td>'+
						'<td></td>'+
						'</tr>';
		// 赎回
		if(checkOrder.txCode == 'Z910043'){
			lineCol2 =  '<td class="type">是否全赎</td>'+
						'<td>'+CommonUtil.getMapValue(CONSTANTS.ALL_REDEEM_FLAG_MAP, checkOrder.allRedeemFlag, '')+'</td>'+
						'</tr>';
		}
		detailList.push(lineCol1 + lineCol2);
		
		// 转托管转入
		if(checkOrder.txCode == 'Z910076'){
			detailList.push('<tr>'+
				         '<td class="type">转托管业务类型</td>'+
				         '<td>'+CommonUtil.getMapValue(CONSTANTS.TRANSFER_TUBE_TYPE_MAP, checkOrder.transferTubeBusiType, '')+'</td>'+
				         '<td class="type">对方销售人代码</td>'+
				         '<td>'+CommonUtil.getMapValue(CONSTANTS.T_SELLER_CODE_MAP, checkOrder.tSellerCode, checkOrder.tSellerCode)+'</td>'+
		   	 			'</tr>');
			
			detailList.push('<tr>'+
				         '<td class="type">原申请单号</td>'+
				         '<td>'+CommonUtil.formatData(checkOrder.originalAppDealNo,'')+'</td>'+
				         '<td class="type"></td>'+
				         '<td>&nbsp;</td>'+
		   	 			'</tr>');
		}

		detailList.push('<tr>'+
		             	'<td class="type">银行名称</td>'+
		             	'<td>'+ CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, checkOrder.bankCode, '')+'</td>'+
		             	'<td class="type">申请折扣率</td>'+
		             	'<td>'+CommonUtil.formatData(checkOrder.discountRate,'')+'</td>'+
						'</tr>');
		
		detailList.push('<tr>'+
		             	'<td class="type">银行账号</td>'+
		             	'<td>'+ CommonUtil.formatData(checkOrder.bankAcct,'')+'</td>'+
		             	'<td class="type">支付方式</td>'+
		             	'<td> 自划款 </td>'+
						'</tr>');
		
		detailList.push('<tr>'+
		             	'<td class="type">目标分红方式</td>'+
		             	'<td>'+ CommonUtil.getMapValue(CONSTANTS.FUND_DIV_MODE_MAP, checkOrder.fundDivMode, '')+'</td>'+
		             	'<td class="type">转入基金代码</td>'+
		             	'<td>'+CommonUtil.formatData(checkOrder.tFundCode,'')+' </td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
		 				'<td class="type">转入基金简称</td>'+
		 				'<td>'+ CommonUtil.formatData(checkOrder.tFundName,'')+'</td>'+
		             	'<td class="type">赎回不出款标记</td>'+
		             	'<td>'+CommonUtil.getMapValue(CONSTANTS.UNUSUAL_TRANS_TYPE_MAP, checkOrder.unusualTransType, '')+' </td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
		 				'<td class="type" style="background-color: #dcdcdc;">订单编号</td>'+
		 				'<td>'+ CommonUtil.formatData(checkOrder.dealNo,'')+'</td>'+
		             	'<td class="type">巨额赎回顺延标记</td>'+
		             	'<td>'+CommonUtil.getMapValue(CONSTANTS.LARGE_REDM_FLAG_MAP, checkOrder.largeRedmFlag, '')+'</td>'+
						'</tr>');

		// 修改资金回款
		if(checkOrder.txCode == 'Z930023'){
			detailList.push('<tr>'+
				'<td class="type">修改前回款方向</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.JIGOU_DIRECTION_TYPE_SHOW_MAP, checkOrder.beforeModifyDirection, '')+'</td>'+
				'<td class="type">修改后回款方向</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.JIGOU_DIRECTION_TYPE_SHOW_MAP, checkOrder.withdrawDirection, '')+'</td>'+
				'</tr>');
		}else if (checkOrder.txCode == 'Z910045' || checkOrder.txCode == 'Z910046') {//撤单展示 回款方向
			detailList.push('<tr>'+
				'<td class="type">撤单回款方向</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.PAYMENT_ALL_TYPE, checkOrder.withdrawDirection, '')+'</td>'+
				'<td class="type"></td>'+
				'<td></td>'+
				'</tr>');
		}

		if (CommonUtil.isEmpty(checkOrder.surveyAnswer)) {
			detailList.push('<tr>'+
				'<td class="type">二次确认标识</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, checkOrder.riskFlag, '')+'</td>'+
				'<td class="type">交易回款方式</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG, checkOrder.redeemCapitalFlag, '')+'</td>'+
				'</tr>');
		}else {
			detailList.push('<tr>'+
				'<td class="type">二次确认标识</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, checkOrder.riskFlag, '')+'</td>'+
				'<td class="type">投顾问卷答案</td>'+
				'<td>'+checkOrder.surveyAnswer+'</td>'+
				'</tr>');

			detailList.push('<tr>'+
				'<td class="type">交易回款方式</td>'+
				'<td>'+CommonUtil.getMapValue(CONSTANTS.GM_COUNTEE_REDEEM_CAPITAL_FLAG, checkOrder.redeemCapitalFlag, '')+'</td>'+
				'<td class="type"></td>'+
				'<td></td>'+
				'</tr>');
		}



		
		detailList.push('<tr>'+ 
		 				'<td class="type">失败原因</td>'+
		 				'<td>'+ CommonUtil.formatData(checkOrder.memo,'')+'</td>'+
		 				'<td class="type" style="background-color: #dcdcdc;">审核状态</td>'+
		             	'<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, checkOrder.checkFlag, '')+'</td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
		 				'<td class="type">录入操作员</td>'+
		 				'<td>'+ CommonUtil.formatData(checkOrder.operatorNo,'')+'</td>'+
		 				'<td class="type">网点</td>'+
		             	'<td> 柜台  </td>'+
						'</tr>');
		
		detailList.push('<tr>'+ 
						'<td class="type">申请日期</td>'+
		             	'<td>'+CommonUtil.formatData(checkOrder.appDt,'')+'</td>'+
		 				'<td class="type">申请时间</td>'+
		 				'<td>'+ CommonUtil.formatData(checkOrder.appTm,'')+'</td>'+
						'</tr>');

			
			var bodyHtml = detailList.join('');
			$(".tabPop").html(bodyHtml);

			// POPUP
		    layer.open({
		        title: ['订单详情', true],
		        type: 1,
		        area: ['820px', 'auto'],
		        skin: 'layui-layer-rim', //加上边框
		        maxmin: true, //允许全屏最小化
		        btnAlign: 'l',
		        content: $('.reCheckInfo')
		    });
	},
	
	/**
	 * 转托管转出申请订单详情
	 * @param id
	 * @param checkOrder
	 */
	setTransferTubeOrderInfo : function(id, checkOrder){
		var detailList = [];
		detailList.push('<tr>'+
		         '<td class="type">业务类型</td>'+
		         '<td>'+CommonUtil.getMapValue(CONSTANTS.TRANSFER_TUBE_TYPE_MAP, checkOrder.transferTubeBusiType, '')+'</td>'+
		         '<td class="type" style="background-color: #dcdcdc;">交易业务类型</td>'+
		         '<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_TXCODE_MAP, checkOrder.txCode, '')+'</td>'+
  	 			'</tr>');
		
		detailList.push('<tr>'+
				 '<td class="type">基金代码</td>'+
		         '<td>'+CommonUtil.formatData(checkOrder.fundCode,'')+'</td>'+
		         '<td class="type">基金简称</td>'+
		         '<td>'+CommonUtil.formatData(checkOrder.fundName,'')+'</td>'+
   	 			'</tr>');
		
		detailList.push('<tr>'+
		         '<td class="type">总份额（份）</td>'+
		         '<td><span id="showTotalVol"></span></td>'+
		         '<td class="type">对方销售人代码</td>'+
		         '<td>'+CommonUtil.getMapValue(CONSTANTS.T_SELLER_CODE_MAP, checkOrder.tSellerCode, checkOrder.tSellerCode)+'</td>'+
  	 			'</tr>');
		
		detailList.push('<tr>'+
		         '<td class="type">对方销售人处投资者基金交易账号</td>'+
		         '<td>'+CommonUtil.formatData(checkOrder.tSellerTxAcctNo,'')+'</td>'+
		         '<td class="type">对方网点</td>'+
		         '<td>'+CommonUtil.formatData(checkOrder.tOutletCode,'')+'</td>'+
  	 			'</tr>');
		
		detailList.push('<tr>'+ 
				'<td class="type">申请日期</td>'+
             	'<td>'+CommonUtil.formatData(checkOrder.appDt,'')+'</td>'+
 				'<td class="type">申请时间</td>'+
 				'<td>'+ CommonUtil.formatData(checkOrder.appTm,'')+'</td>'+
			'</tr>');
		
		detailList.push('<tr>'+ 
 				'<td class="type">录入操作员</td>'+
 				'<td>'+ CommonUtil.formatData(checkOrder.operatorNo,'')+'</td>'+
 				'<td class="type">网点</td>'+
             	'<td> 柜台  </td>'+
			'</tr>');
		
		detailList.push('<tr>'+
     			'<td class="type">预申请单号</td>'+
     			'<td>'+CommonUtil.formatData(checkOrder.dealAppNo,'')+'</td>'+
     			'<td class="type">订单编号</td>'+
     			'<td>'+CommonUtil.formatData(checkOrder.dealNo,'')+'</td>'+
 	 		'</tr>');
		
		detailList.push('<tr>'+ 
 				'<td class="type">失败原因</td>'+
 				'<td>'+ CommonUtil.formatData(checkOrder.memo,'')+'</td>'+
 				'<td class="type" style="background-color: #dcdcdc;">审核状态</td>'+
             	'<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP, checkOrder.checkFlag, '')+'</td>'+
			'</tr>');
		
		var bodyHtml = detailList.join('');
		$("#"+id).html(bodyHtml);
	},
	
	/**
	 * 转托管转出详情
	 * @param data
	 */
	queryShowCounterTransferTubeOrderInfo:function(data){
		var bodyData = data.body || {};
		var checkOrder = bodyData.checkOrder;
		var checkDtlOrder = bodyData.checkDtlOrder;
		
		// 客户信息
		BodyView.setCustInfo("tubeCustInfo", checkOrder.txAcctNo, checkOrder.idNo, checkOrder.disCode, BodyView.setTransferTubeOutCustInfoTable);
		// 录入订单信息
		BodyView.setTransferTubeOrderInfo("tubeOrderInfo", checkOrder);
		// 转出持仓份额信息
		BodyView.setTransferTubeOutTableView("tubeOutVolInfo", checkDtlOrder, "show");
		
		// POPUP
	    layer.open({
	        title: ['订单详情', true],
	        type: 1,
	        area: ['80%', '90%'],
	        skin: 'layui-layer-rim', //加上边框
	        maxmin: true, //允许全屏最小化
	        btnAlign: 'l',
	        content: $('#popupTransferTubeOutInfo')
	    });
	},

    /**
     * 理财通转托管转出详情
     * @param data
     */
    queryShowLctCounterTransferTubeOrderInfo:function(data){
        var bodyData = data.body || {};
        var checkOrder = bodyData.checkOrder;
        var checkDtlOrder = bodyData.checkDtlOrder;

        // 客户信息
        BodyView.setCustInfo("tubeCustInfo", checkOrder.txAcctNo, checkOrder.idNo, checkOrder.disCode, BodyView.setTransferTubeOutCustInfoTable);
        // 录入订单信息
        BodyView.setTransferTubeOrderInfo("tubeOrderInfo", checkOrder);
        // 转出持仓份额信息
       BodyView.setLctTransferTubeOutTableView("tubeOutVolInfo", checkDtlOrder, "show");

        // POPUP
        layer.open({
            title: ['订单详情', true],
            type: 1,
            area: ['80%', '90%'],
            skin: 'layui-layer-rim', //加上边框
            maxmin: true, //允许全屏最小化
            btnAlign: 'l',
            content: $('#popupTransferTubeOutInfo')
        });
    },


    setTransferTubeOutCustInfoTable : function(data){
		BodyView.setCustInfoTableView("tubeCustInfo", data);
	},
	
	/**
	 *  转托管转出审核份额信息
	 */
	setTransferTubeOutTableView : function(id, data, action){
		//console.log(data);
		
		var totalVol = 0;
		var selTotalOutVol = 0;
		// 转出信息:SubmitUncheckOrderDtlDto
		$("#"+id).empty();
		if(data.length <=0){
			var trHtml = '<tr><td colspan="8">没有查询到转出份额信息</td></tr>';
			$("#"+id).append(trHtml);
			return false;
			
		}else{
			$(data).each(function(index,element){
				totalVol = Number(totalVol) + Number(element.preAppVol);
				selTotalOutVol = Number(selTotalOutVol) + Number(element.appVol);
				
				var tdList = [];
				tdList.push('<td class="hide"><input class="selectTransOutCustBal" id="selectTransOutCustBal_'+index+'" name="checkTransOutBal" type="checkbox" data-index="' + index + '" checked="true"></input></td>');
				tdList.push('<td>'+CommonUtil.formatData(element.fundCode)+'</td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.preAppVol, '0')+'</td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.preFrznVol, '0')+'</td>');
				
				// 转出份额
				if(action == "show"){
					tdList.push('<td><input type="text" name="outVol_'+index+'" id="outVol_'+index+'" value="'+CommonUtil.formatAmount(element.appVol)+'" readonly="readonly"></td>');
				
				} else if(action == "check"){
					tdList.push('<td><input type="text" class="outVolClass" name="outVol_'+index+'" id="outVol_'+index+'" isnull="false" datatype="s" errormsg="转出份额" placeholder="请输入" value=""></td>');
				
				} else if(action == "update"){
					tdList.push('<td><input type="text" class="outVolClass" name="outVol_'+index+'" id="outVol_'+index+'" isnull="false" datatype="s" errormsg="转出份额" placeholder="请输入" onkeyup="TransfertubeOut.validatorOutVol('+index+','+element.availVol+', this);" value="'+CommonUtil.formatAmount(element.appVol)+'"></td>');
				}
				
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType)+'</td>');
				tdList.push('<td>'+CommonUtil.formatData(element.protocolNo)+'</td>');
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode)+'</td>');
				tdList.push('<td>'+CommonUtil.formatData(element.bankAcct)+'</td>');
			
				var trAppendHtml = '<tr class="text-c">'+tdList.join() +'</tr>';
				$("#"+id).append(trAppendHtml);
			});
		}
		// 设置录入中的总份额
		if($("#totalVol").length > 0){
			$("#totalVol").val(CommonUtil.formatAmount(totalVol));
		}
		if($("#showTotalVol").length > 0){
			$("#showTotalVol").html(CommonUtil.formatAmount(totalVol));
		}
		if($("#statics").length > 0){
			var statisHtml = '转出笔数：'+data.length+'笔；总转出份额：'+CommonUtil.formatAmount(selTotalOutVol)+'份，大写：'+CommonUtil.digit_uppercase(selTotalOutVol).replace('元', '份');
			$("#statics").html(statisHtml);
		}
		
	},

    /**
     * 理财通转托管转出审核份额信息
     */
    setLctTransferTubeOutTableView : function(id, data, action){
        //console.log(data);

        var totalVol = 0;
        var selTotalOutVol = 0;
        // 转出信息:SubmitUncheckOrderDtlDto
        $("#"+id).empty();
        if(data.length <=0){
            var trHtml = '<tr><td colspan="8">没有查询到转出份额信息</td></tr>';
            $("#"+id).append(trHtml);
            return false;

        }else{
            $(data).each(function(index,element){
                totalVol = Number(totalVol) + Number(element.preAppVol);
                selTotalOutVol = Number(selTotalOutVol) + Number(element.appVol);

                var tdList = [];
                tdList.push('<td class="hide"><input class="selectTransOutCustBal" id="selectTransOutCustBal_'+index+'" name="checkTransOutBal" type="checkbox" data-index="' + index + '" checked="true"></input></td>');
                tdList.push('<td>'+CommonUtil.formatData(element.fundCode)+'</td>');
                tdList.push('<td>'+CommonUtil.formatAmount(element.preAppVol, '0')+'</td>');
                tdList.push('<td>'+CommonUtil.formatAmount(element.preFrznVol, '0')+'</td>');

                // 转出份额
                if(action == "show"){
                    tdList.push('<td><input type="text" name="outVol_'+index+'" id="outVol_'+index+'" value="'+CommonUtil.formatAmount(element.appVol)+'" readonly="readonly"></td>');

                } else if(action == "check"){
                    tdList.push('<td><input type="text" class="outVolClass" name="outVol_'+index+'" id="outVol_'+index+'" isnull="false" datatype="s" errormsg="转出份额" placeholder="请输入" value=""></td>');

                } else if(action == "update"){
                    tdList.push('<td><input type="text" class="outVolClass" name="outVol_'+index+'" id="outVol_'+index+'" isnull="false" datatype="s" errormsg="转出份额" placeholder="请输入" onkeyup="TransfertubeOut.validatorOutVol('+index+','+element.availVol+', this);" value="'+CommonUtil.formatAmount(element.appVol)+'"></td>');
                }

                tdList.push('<td>'+CommonUtil.formatData(element.protocolNo)+'</td>');
                tdList.push('<td>'+'理财通虚拟卡'+'</td>');
                tdList.push('<td>'+CommonUtil.formatData(element.bankAcct)+'</td>');

                var trAppendHtml = '<tr class="text-c">'+tdList.join() +'</tr>';
                $("#"+id).append(trAppendHtml);
            });
        }
        // 设置录入中的总份额
        if($("#totalVol").length > 0){
            $("#totalVol").val(CommonUtil.formatAmount(totalVol));
        }
        if($("#showTotalVol").length > 0){
            $("#showTotalVol").html(CommonUtil.formatAmount(totalVol));
        }
        if($("#statics").length > 0){
            var statisHtml = '转出笔数：'+data.length+'笔；总转出份额：'+CommonUtil.formatAmount(selTotalOutVol)+'份，大写：'+CommonUtil.digit_uppercase(selTotalOutVol).replace('元', '份');
            $("#statics").html(statisHtml);
        }

    }
} 