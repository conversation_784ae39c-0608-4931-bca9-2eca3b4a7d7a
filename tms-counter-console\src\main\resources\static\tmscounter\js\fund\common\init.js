var Init = {
		init:function(){
			/**
			 * 分销机构列表
			 */
			DisCode.getDisCodes();
			
			/**
			 * 投顾列表
			 */
			if($(".selectconsCode").length > 0 && CommonUtil.isEmpty($(".selectconsCode").val())){
				var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
			}

			/**
			 * 证件类型
			 */
			if($(".selectTransactorIdType").length > 0 && CommonUtil.isEmpty($(".selectTransactorIdType").val())){
				var selectIdTypeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.ID_TYPE_MAP);
				$(".selectTransactorIdType").html(selectIdTypeHtml);
			}
			
			/**
			 * 分销机构
			 */
			if($("#selectDisCode").length > 0 && CommonUtil.isEmpty($("#selectDisCode").val())){
				var selectDiscodeHtm = CommonUtil.selectOptionsHtml(DisCode.disCodesMap,CONSTANTS.HB_DISCODE);
				$("#selectDisCode").html(selectDiscodeHtm);
			}
			
			if($("#selectTxCode").length>0 && CommonUtil.isEmpty($("#selectTxCode").val())){
				var selectTxCodeHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_TXCODE_MAP,function(name,value){
					selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectTxCode").empty();
				$("#selectTxCode").html(selectTxCodeHtml);
			}
			
			if($("#selectCheckFlag").length > 0 && CommonUtil.isEmpty($("#selectCheckFlag").val())){
				var selectCheckFlagHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP,function(name,value){
					selectCheckFlagHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectCheckFlag").empty();
				$("#selectCheckFlag").html(selectCheckFlagHtml);
			}
			
			/**
			 * 获取工作日
			 */	
			if($("#appDt").length > 0 && CommonUtil.isEmpty($("#appDt").val())){
				CommonUtil.getWorkDay();
			}
			
			/**
			 * 初始化异常赎回
			 */
			if($(".selectUnusualTransType").length > 0 && CommonUtil.isEmpty($(".selectUnusualTransType").val())){
				var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.FUND_UNUSUAL_TRANS_TYPE_MAP,'0');
				$(".selectUnusualTransType").html(selectTransTypeHtml);
			}
		},
		
		
		initDisCodeAll:function(){
			/**
			 * 分销机构列表
			 */
			DisCode.getDisCodes();
			
			/**
			 * 投顾列表
			 */
			if($(".selectconsCode").length > 0 && CommonUtil.isEmpty($(".selectconsCode").val())){
				var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
			}

			/**
			 * 证件类型
			 */
			if($(".selectTransactorIdType").length > 0 && CommonUtil.isEmpty($(".selectTransactorIdType").val())){
				var selectIdTypeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.ID_TYPE_MAP);
				$(".selectTransactorIdType").html(selectIdTypeHtml);
			}
			
			/**
			 * 分销机构
			 */
			if($("#selectDisCode").length > 0 && CommonUtil.isEmpty($("#selectDisCode").val())){
				var selectDiscodeHtm = CommonUtil.selectOptionsHtml(DisCode.disCodesMap, "ALL", "ALL", "全部");
				$("#selectDisCode").html(selectDiscodeHtm);
			}
			
			if($("#selectTxCode").length>0 && CommonUtil.isEmpty($("#selectTxCode").val())){
				var selectTxCodeHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_TXCODE_MAP,function(name,value){
					selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectTxCode").empty();
				$("#selectTxCode").html(selectTxCodeHtml);
			}
			
			if($("#selectCheckFlag").length > 0 && CommonUtil.isEmpty($("#selectCheckFlag").val())){
				var selectCheckFlagHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP,function(name,value){
					selectCheckFlagHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectCheckFlag").empty();
				$("#selectCheckFlag").html(selectCheckFlagHtml);
			}
			
			/**
			 * 获取工作日
			 */	
			if($("#appDt").length > 0 && CommonUtil.isEmpty($("#appDt").val())){
				CommonUtil.getWorkDay();
			}
			
			/**
			 * 初始化异常赎回
			 */
			if($(".selectUnusualTransType").length > 0 && CommonUtil.isEmpty($(".selectUnusualTransType").val())){
				var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.FUND_UNUSUAL_TRANS_TYPE_MAP,'0');
				$(".selectUnusualTransType").html(selectTransTypeHtml);
			}
		},


		initDisCodeTrans:function(){
			/**
			 * 分销机构列表
			 */
			DisCode.getDisCodesExcludeLct();

			/**
			 * 投顾列表
			 */
			if($(".selectconsCode").length > 0 && CommonUtil.isEmpty($(".selectconsCode").val())){
				var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
			}

			/**
			 * 证件类型
			 */
			if($(".selectTransactorIdType").length > 0 && CommonUtil.isEmpty($(".selectTransactorIdType").val())){
				var selectIdTypeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.ID_TYPE_MAP);
				$(".selectTransactorIdType").html(selectIdTypeHtml);
			}

			/**
			 * 分销机构
			 */
			if($("#selectDisCode").length > 0 && CommonUtil.isEmpty($("#selectDisCode").val())){
				var selectDiscodeHtm = CommonUtil.selectOptionsHtml(DisCode.disCodesMap, "ALL", "ALL", "全部");
				$("#selectDisCode").html(selectDiscodeHtm);
			}

			if($("#selectTxCode").length>0 && CommonUtil.isEmpty($("#selectTxCode").val())){
				var selectTxCodeHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_TXCODE_MAP,function(name,value){
					selectTxCodeHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectTxCode").empty();
				$("#selectTxCode").html(selectTxCodeHtml);
			}

			if($("#selectCheckFlag").length > 0 && CommonUtil.isEmpty($("#selectCheckFlag").val())){
				var selectCheckFlagHtml = '<option value="">全部</option>';
				$.each(CONSTANTS.COUNTER_FUND_CHECK_FLAG_MAP,function(name,value){
					selectCheckFlagHtml +='<option value="'+name+'">'+value+' </option>';
				});
				$("#selectCheckFlag").empty();
				$("#selectCheckFlag").html(selectCheckFlagHtml);
			}

			/**
			 * 获取工作日
			 */
			if($("#appDt").length > 0 && CommonUtil.isEmpty($("#appDt").val())){
				CommonUtil.getWorkDay();
			}

			/**
			 * 初始化异常赎回
			 */
			if($(".selectUnusualTransType").length > 0 && CommonUtil.isEmpty($(".selectUnusualTransType").val())){
				var selectTransTypeHtml =CommonUtil.selectOptionsHtml(CONSTANTS.FUND_UNUSUAL_TRANS_TYPE_MAP,'0');
				$(".selectUnusualTransType").html(selectTransTypeHtml);
			}
		},
		
		/**
		 * 转托管业务类型选择框初始化
		 */
		selectBoxTransferTubeBusiType:function(){
			/**
			 * 业务类型与对方销售人代码联动
			 */
			$(".selectTransferTubeBusiType").change(function(){
				Init.setTSellerCodeTD($(this).val());
			});
		},
		/**
		 * 设置对方销售人
		 * @param transferTubeBusiType
		 */
		setTSellerCodeTD:function(transferTubeBusiType){
			// 当业务类型选择"1"时销售人代码只有两个选项：‘101-上海’、‘102-深圳’ ; "2"时输入框，但不能输入304
			if(transferTubeBusiType == '1'){
				var selectbox = '<span class="select-box inline">';
				selectbox +='<select name="tSellerCode" id="tSellerCode" class="select">';
				selectbox +='<option value="">请选择</option>';
				selectbox +='<option value="101">101-上海</option>';
				selectbox +='<option value="102">102-深圳</option>';
				selectbox +='</select>';
				selectbox +='</span>';
				
				$("#tSellerTD").html(selectbox);
				
			} else{
				var inputText = '<input type="text" placeholder="请输入" id="tSellerCode" name="tSellerCode" isnull="false" datatype="s" errormsg="对方销售人代码">';
				$("#tSellerTD").html(inputText);
			}
		}
}

var DisCode = {
		
};

DisCode.disCodes =[];
DisCode.disCodesMap ={};

DisCode.getDisCodes=function(){
	var uri = TmsCounterConfig.QUERY_DIS_CODE_INFO_URL;
	var reqparamters = {};
	var paramters = CommonUtil.buildReqParams(uri,reqparamters);
	CommonUtil.ajaxAndCallBack(paramters, function(data){
		var respCode = data.code || '';
		var body = data.body || {};
		if(CommonUtil.isSucc(respCode)){
			DisCode.disCodes = body.disCodes || [];
			$(DisCode.disCodes).each(function(index,element){
				DisCode.disCodesMap[element.disCode] = element.disName;
			});
		}		
	});
}


DisCode.getDisCodesExcludeLct=function(){
	var uri = TmsCounterConfig.QUERY_DIS_CODE_INFO_URL;
	var reqparamters = {};
	var paramters = CommonUtil.buildReqParams(uri,reqparamters);
	CommonUtil.ajaxAndCallBack(paramters, function(data){
		var respCode = data.code || '';
		var body = data.body || {};
		if(CommonUtil.isSucc(respCode)){
			DisCode.disCodes = body.disCodes || [];
			$(DisCode.disCodes).each(function(index,element){
				if(element.disCode == 'LCT00K001'){
					return true;
				}
				DisCode.disCodesMap[element.disCode] = element.disName;
			});
		}
	});
}