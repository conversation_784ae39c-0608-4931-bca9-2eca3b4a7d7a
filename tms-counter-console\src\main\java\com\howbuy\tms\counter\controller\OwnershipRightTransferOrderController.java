package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CounterOwnershipRightTransferReqDto;
import com.howbuy.tms.counter.dto.QueryOwnershipRightTransferReqDto;
import com.howbuy.tms.counter.dto.QueryOwnershipRightTransferRespDto;
import com.howbuy.tms.counter.service.trade.OwnershipRightTransferOrderService;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransferdtl.bean.AbstractOwnershipRightTransferDtlBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:股权份额转让订单相关
 * @Author: yun.lu
 * Date: 2023/5/19 14:36
 */
@Controller
@Slf4j
public class OwnershipRightTransferOrderController {
    @Autowired
    private OwnershipRightTransferOrderService ownershipRightTransferOrderService;

    /**
     * 查询股权份转让订单列表
     */
    @RequestMapping("/tmscounter/queryOwnershipRightTransfer.htm")
    public ModelAndView queryOwnershipRightTransfer(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.参数转换
        QueryOwnershipRightTransferReqDto queryOwnershipRightTransferReqDto = new QueryOwnershipRightTransferReqDto(request);
        // 2.参数校验
        queryOwnershipRightTransferReqDto.check();
        // 3.查询
        QueryOwnershipRightTransferRespDto queryOwnershipRightTransfer = ownershipRightTransferOrderService.queryOwnershipRightTransfer(queryOwnershipRightTransferReqDto);
        // 4.返回结果封装
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(3);
        body.put("orderList", queryOwnershipRightTransfer.getOwnershipRightTransferOrderDtoList());
        body.put("totalPage", queryOwnershipRightTransfer.getTotalPage());
        body.put("pageNum", queryOwnershipRightTransfer.getPageNo());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 下载股权份转让订单列表
     */
    @RequestMapping("/tmscounter/downloadOwnershipRightTransfer.htm")
    public ModelAndView downloadOwnershipRightTransfer(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.查询
        ownershipRightTransferOrderService.downloadOwnershipRightTransfer(request, response);
        return null;
    }


    /**
     * 根据订单号查询股权份转让订单信息详情,转让价格+标识以订单为准
     */
    @RequestMapping("/tmscounter/ownershipRightTransferDtlByDtlNo.htm")
    public ModelAndView ownershipRightTransferDtlByDtlNo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.参数转换
        String dealDtlNo = request.getParameter("dealDtlNo");
        log.info("OwnershipRightTransferOrderController-ownershipRightTransferDtl,dealDtlNo={}", dealDtlNo);
        // 2.参数校验
        if (StringUtils.isBlank(dealDtlNo)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "订单号不能为空");
        }
        // 3.查询
        AbstractOwnershipRightTransferDtlBean orderDtl = ownershipRightTransferOrderService.queryOwnershipRightTransferDtlByDealDtlNo(dealDtlNo);
        // 4.返回结果封装
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("orderDtl", orderDtl);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 根据申请单号查询股权转让详情,转让价格+标识以申请单为准
     */
    @RequestMapping("/tmscounter/queryOwnershipRightTransferDtl.htm")
    public ModelAndView queryOwnershipRightTransferDtl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.参数转换
        String dealAppNo = request.getParameter("dealAppNo");
        log.info("OwnershipRightTransferOrderController-queryOwnershipRightTransferDtl,dealAppNo={}", dealAppNo);
        // 2.参数校验
        if (StringUtils.isBlank(dealAppNo)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "申请单号不能为空");
        }
        // 3.查询
        AbstractOwnershipRightTransferDtlBean orderDtl = ownershipRightTransferOrderService.queryOwnershipRightTransferDtlByDealAppNo(dealAppNo);
        // 4.返回结果封装
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("orderDtl", orderDtl);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * 提交修改申请
     */
    @RequestMapping("/tmscounter/counterownershipRightTransfer.htm")
    public ModelAndView counterownershipRightTransfer(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.参数转换
        OperatorInfoCmd operatorInfoCmd = SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        CounterOwnershipRightTransferReqDto queryOwnershipRightTransferReqDto = new CounterOwnershipRightTransferReqDto(request,operatorInfoCmd);
        log.info("counterownershipRightTransfer-股权转让维护提交,queryOwnershipRightTransferReqDto={}", JSON.toJSONString(queryOwnershipRightTransferReqDto));
        // 2.参数校验
        queryOwnershipRightTransferReqDto.check();
        // 3.提交修改申请
        String dealAppNo = ownershipRightTransferOrderService.counterOwnershipRightTransfer(queryOwnershipRightTransferReqDto);
        // 4.返回结果封装
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("dealAppNo", dealAppNo);
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }


}
