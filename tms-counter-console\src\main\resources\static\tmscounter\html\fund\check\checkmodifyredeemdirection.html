<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
    <script type="text/javascript" src="lib/html5.js"></script>
    <script type="text/javascript" src="lib/respond.min.js"></script>
    <script type="text/javascript" src="lib/PIE_IE678.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>修改回款方向审核</title>
</head>

<body>
	<div class="page-container">
        <div class="containner_all">
              <p class="mainTitle mt10">修改回款方向审核</p>
        </div>
    </div>
</div>
<div class="page-container w1000">
    <p class="main_title mt30">客户基本信息</p>
    <div class="result2_tab">
        <table class="table table-border table-bordered table-hover table-bg table-sort">
            <thead>
            <tr class="text-c">
                <th>选择</th>
                <th>客户号</th>
                <th>客户名称</th>
                <th>客户类型</th>
                <th>客户状态</th>
                <th>证件类型</th>
                <th>证件号</th>
                <th>投资者类型</th>
                <th>风险等级</th>
                <th>分销机构</th>
            </tr>
            </thead>
            <tbody id="custInfoId">
            <tr class="text-c">
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
                <td>--</td>
            </tr>
            </tbody>
        </table>
    </div>

    <p class="main_title mt30">可修改回款方向的交易申请</p>
    <form class="w1500">
        <div class="result2_tab">
            <table id = "projectTable" class="table table-border table-bordered table-hover table-bg table-sort">
                <thead>
                <tr class="text-c">
                  <!--  <th>选择</th>-->
                    <th>订单号</th>
                    <th>业务类型</th>
                    <th>产品简称</th>
                    <th>产品代码</th>
                    <th>申请T日</th>
                    <th>申请金额</th>
                    <th>申请份额</th>
                    <th>转入基金代码</th>
                    <th>转入基金简称</th>
                    <th>转入申请金额</th>
                    <th>转入申请份额</th>
                    <th>交易状态</th>
                    <th>申请日期</th>
                    <th>申请时间</th>
                </tr>
                <tbody id="dealInfoId">
                <tr class="text-c">
                    <!--<td><input class="selectDeal" name="selectDeal" type="checkbox"/></td>-->
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                    <td>--</td>
                </tr>
                </tbody>
            </table>
            <br/>
        </div>
    </form>

    <form id="modifyDirection" >
        <!-- 是否经办: 个人用户默认为否, 机构客户默认为是, 为是时显示经办人信息 -->
        <p class="main_title mt30">可修改回款方向的交易申请</p>
        <form class="w1500">
            <div class="result2_tab">
                <table id="redeemDirection" class="table table-border table-bordered table-hover table-bg table-sort">
                    <thead>
                    <tr class="text-c">
                        <input type ="hidden" id ="beforeModifyDirection"/>
                        <td>修改前回款方向</td>
                        <td id="beforeModify">--</td>
                        <td>修改后回款方向</td>
                        <td>
                            <span class="select-box inline">
                                <select name="afterModify" class="select" id="afterModify" isnull="false" datatype="s" errormsg="回款到银行卡">
                                </select>
                            </span>
                        </td>
                    </tr>
                    <tr class="text-c">
                        <td>失败原因</td>
                        <td>
                            <input type="text" placeholder='请输入失败原因' id="checkFaildDesc" name="checkFaildDesc">
                        </td>
                        <td>
                        </td>
                        <td>
                        </td>
                    </tr>
                </table>
            </div>
        </form>
    </form>
        <div class="clear page_all">
            	<div class="fy_part fr mt20" style="display: none" id="pageView"></div>
        </div>
    </div>

    <p class="mt30">
        <a href="javascript:void(0)" id="returnBtn" class="btn radius btn-secondary">退回</a>
        <a href="javascript:void(0)" id="succBtn" class="btn radius btn-secondary">审核通过</a>
    </p>

<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
<script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
<script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
<script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
<script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
<script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
<script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/valid.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/conscode.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/query/querycustinfosubpage.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/query/querycheckorder.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/check/checkmodifyredeemdirection.js?v=20181011"></script>
<script type="text/javascript" src="../../../js/fund/common/main.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/query/querycustinfo.js?v=20181009"></script>
<script type="text/javascript" src="../../../js/fund/query/queryfundinfo.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/check/countercheck.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/common/validate.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/common/agent.js?v=20200301002"></script>
<script type="text/javascript" src="../../../js/fund/common/bodyview.js?v=20181011"></script>
</body>

</html>