/**
 * 初始化页面事件
 */

$(function () {
    // 订单信息查询
    var viewType = CommonUtil.getParam("viewType");
    if (viewType === 0) {
        $("#modifyBtn").hide();
        $("#cancelBtn").hide();
    }
    $("#modifyBtn").on('click', function () {
        ModifySubsAmtChangeDetail.modifyBtn("0");
    });
    $("#cancelBtn").on('click', function () {
        ModifySubsAmtChangeDetail.modifyBtn("1");
    });
    // 返回
    $("#backBtn").on("click", function () {
        parent.layer.closeAll();
    });
    // 订单信息查询
    // 查询订单
    Modify.queryCounterDealOrder(ModifySubsAmtChangeDetail.modifyDataCallBack, null);
});

var ModifySubsAmtChangeDetail = {
        modifyDataCallBack: function (data) {
            console.log(JSON.stringify(data));
            var bodyData = data.body || {};
            Modify.modifyDealOrder = bodyData.counterOrderDto || {};
            ModifySubsAmtChangeDetail.querySubsAmtChangeDetail();
        },

        querySubsAmtChangeDetail: function () {
            var dealAppNo = CommonUtil.getParam("dealAppNo");
            if (isEmpty(dealAppNo)) {
                showMsg("申请单号不存在");
                return false;
            }
            var uri = TmsCounterConfig.QUERY_SUBS_AMT_CHANGE_DETAIL || "";
            var reqparamters = {};
            reqparamters.dealAppNo = dealAppNo;
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, null, null, null);
            CommonUtil.ajaxAndCallBack(paramters, ModifySubsAmtChangeDetail.buildOrderInfo);
        },
        /***
         * 确认修改
         */
        modifyBtn: function (cancel) {
            var uri = TmsCounterConfig.UPDATE_SUBS_AMT_DETAIL_APPLY || {};
            var forceForm = $("#forceForm").serializeObject();
            var dealAppNo = CommonUtil.getParam("dealAppNo");
            // 2.构建查询参数
            var reqparamters = {
                "dealAppNo": dealAppNo,
                "subsAmt": forceForm.newSubsAmt,
                "cancel": cancel
            };
            var paramters = CommonUtil.buildReqParams(uri, reqparamters, true, null, null);
            CommonUtil.ajaxAndCallBack(paramters, ModifySubsAmtChangeDetail.confirmCallBack);
        },

        confirmCallBack: function (data) {
            var respCode = data.code || '';
            var respDesc = data.desc || '';

            if (CommonUtil.isSucc(respCode)) {
                layer.confirm('提交成功', {
                    btn: ['确定'] //按钮
                }, function () {
                    if (OnLineOrderFile.isCrm()) {
                        CommonUtil.closeCurrentUrl();// 关闭当前页面
                    } else {
                        //刷新父页面
                        window.parent.location.reload();

                        //获取窗口索引
                        var index = parent.layer.getFrameIndex(window.name);
                        //关闭弹窗
                        parent.layer.close(index);
                    }
                });
            } else {
                CommonUtil.layer_tip("提交失败," + respDesc);
            }
        },

        /**
         *渲染产品信息查询结果
         */
        buildOrderInfo: function (data) {
            var bodyData = data.body || {};
            var subsAmtInfo = bodyData.detail || [];
            var oldSubsAmt = CommonUtil.formatData(subsAmtInfo.oldSubsAmt, '--');
            var newSubsAmt = CommonUtil.formatData(subsAmtInfo.newSubsAmt, '--');
            var appendHtml = '';
            $("#layerrs").empty();
            appendHtml =
                '<tr className="text-c">' +
                '<td>基金代码:</td>' +
                '<td id="fundCode">' + formatData(subsAmtInfo.fundCode) + '</td>' +
                '<td>基金简称:</td>' +
                '<td id="fundName">' + formatData(subsAmtInfo.fundName) + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>原认缴金额:</td>' +
                '<td id="subsAmt">' + oldSubsAmt + '</td>' +
                '<td>原认缴金额(大写):</td>' +
                '<td id="subsAmtUp">' + CommonUtil.amtUpper(oldSubsAmt) + '</td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>持仓份额:</td>' +
                '<td id="balanceVol">' + formatData(subsAmtInfo.balanceVol) + '</td>' +
                '<td>调整后认缴金额:</td>' +
                '<td> <input type="text" placeholder="请输入" id="newSubsAmt" name="newSubsAmt"  value="' + newSubsAmt + '"/></td>' +
                '</tr>' +

                '<tr className="text-c">' +
                '<td>调整金额:</td>' +
                '<td id="diffSubsAmt">' + ModifySubsAmtChangeDetail.getChangeAmt(oldSubsAmt, newSubsAmt) + '</td>' +
                '<td>调整后认缴金额(大写):</td>' +
                '<td id="newSubsAmtUp">' + CommonUtil.amtUpper(newSubsAmt) + '</td>' +

                '<tr className="text-c">' +
                '<td>下单日期:</td>' +
                '<td id="appDt">' + subsAmtInfo.appDt + '</td>' +
                '<td>下单时间:</td>' +
                '<td id="appTime">' + subsAmtInfo.appTime + '</td>' +
                '<td> </tr>';
            $("#layerrs").append(appendHtml);
            // 添加输入框监听事件
            $('#newSubsAmt').on('input', function () {
                var newAmt = $(this).val();
                $('#diffSubsAmt').text(ModifySubsAmtChangeDetail.getChangeAmt(oldSubsAmt, newAmt));
                $('#newSubsAmtUp').text(CommonUtil.amtUpper(newAmt));
            });
        },

        getChangeAmt: function (oldAmt, newAmt) {
            if (oldAmt === "--" || oldAmt === "") {
                return newAmt;
            }
            if (newAmt === "" || newAmt === "--") {
                return "--"
            }
            return newAmt - oldAmt;
        },
    }
;