/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.service.validate;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoFacade;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.CustBankInfoBean;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustomerBankCardParam;
import com.howbuy.tms.common.outerservice.interlayer.queryfundinfo.bean.FundInfoAndNavBean;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.dto.ShareMergeVolOrderReqDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.service.out.TmsCounterOutService;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 交易税延基金校验
 * <AUTHOR>
 * @date 2023/7/20 14:21
 * @since JDK 1.8
 */
@Service("TradeValidateService")
public class TradeValidateService {
    @Autowired
    private TmsCounterService tmsCounterService;

    private static Logger logger = LogManager.getLogger(TradeValidateService.class);

    @Autowired
    private TmsCounterOutService tmsCounterOutService;

    @Autowired
    @Qualifier("tmscounter.queryBankCardInfoFacade")
    private QueryBankCardInfoFacade queryBankCardInfoFacade;

    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    public boolean validateCustBankCard(String txAcctNo, String bankAcct, String disCode){
        QueryCustomerBankCardParam queryCustomerBankCardParam = new QueryCustomerBankCardParam();
        queryCustomerBankCardParam.setTxAcctNo(txAcctNo);
        queryCustomerBankCardParam.setDisCode(disCode);
        queryCustomerBankCardParam.setOutletCode("*********");
        List<CustBankInfoBean> bindCustBankCardInfoList = queryCustBankCardOuterService.queryBindBankCardList(queryCustomerBankCardParam);
        if(CollectionUtils.isEmpty(bindCustBankCardInfoList)){
            return false;
        }
        String bankAcctDigest = DigestUtil.digest(bankAcct);
        if(StringUtil.isEmpty(bankAcctDigest)){
            return false;
        }
        CustBankInfoBean custBankInfoBean = null;
        for(CustBankInfoBean  bean : bindCustBankCardInfoList){
            if(bankAcctDigest.equals(bean.getBankAcctDigest())){
                custBankInfoBean = bean;
                break;
            }
        }
        if(custBankInfoBean != null &&  "0".equals(custBankInfoBean.getBankAcctStatus())){
            return true;
        }
        return false;
    }


    public boolean validateCustBankCardByCpAcctNo(String txAcctNo, String cpAcctNo, String disCode){
        QueryCustBankCardContext ctx = new QueryCustBankCardContext();
        ctx.setTxAcctNo(txAcctNo);
        ctx.setDisCode(disCode);
        ctx.setCpAcctNo(cpAcctNo);
        QueryCustBankCardResult result = queryCustBankCardOuterService.queryCudtBankCardByCpAcctNo(ctx);
        if(result != null && !StringUtil.isEmpty(result.getBankAcctDigest())){
            return true;
        }
        return false;
    }


    /**
     * 柜台不支持Y份额基金交易
     *
     * @param fundCode 基金代码
     * @author: junkai.du
     * @date: 2023/7/20 14:24
     * @since JDK 1.8
     */
    public void tradeValidate(String fundCode, String taTradeDt) throws Exception {
        if (StringUtils.isBlank(taTradeDt)){
            taTradeDt = tmsCounterService.getSystemWorkDay(SysCodeEnum.BATCH_GM.getCode(), null);
        }
        // 柜台不支持Y份额基金交易
        FundInfoAndNavBean fundInfoAndNavBean = null;
        try {
            fundInfoAndNavBean = tmsCounterOutService.getFundNavInfo(fundCode, taTradeDt);
        } catch (Exception e) {
            logger.error("query fundInfoAndNavBean erro :", e);
        }

        if (null != fundInfoAndNavBean && YesOrNoEnum.YES.getCode().equals(fundInfoAndNavBean.getTaxDelayFlag())){
            throw new TmsCounterException(TmsCounterResultEnum.PENSION_FORBID_BUY);
        }

    }

    /**
     * 柜台不支持Y份额基金交易
     *
     * @author: junkai.du
     * @date: 2023/7/20 14:24
     * @since JDK 1.8
     */
    public void tradeFundValidate(FundInfoAndNavBean fundInfo ){
        if (Objects.isNull(fundInfo)){
            return;
        }
        // 柜台不支持Y份额基金交易
        if (null != fundInfo && YesOrNoEnum.YES.getCode().equals(fundInfo.getTaxDelayFlag())){
            throw new TmsCounterException(TmsCounterResultEnum.PENSION_FORBID_BUY);
        }
    }



    /**
     * 校验银行卡是否是税延卡
     * @param txAcctNo	交易账号
     * @param bankAcct	银行卡号
     * @return void
     * @author: junkai.du
     * @date: 2023/7/20 19:13
     * @since JDK 1.8
     */
    public void tradeCardValidate(String txAcctNo, String bankAcct){
        try {
            QueryBankCardInfoResponse queryBankCardInfoResponse = tmsCounterOutService.queryBankCardInfo(txAcctNo, bankAcct);
            // 柜台不支持Y份额基金交易  6-税延卡
            if (null != queryBankCardInfoResponse && Constants.PENSION_CARD_ATTR.equals(queryBankCardInfoResponse.getBankAcctAttr())){
                throw new TmsCounterException(TmsCounterResultEnum.PENSION_BANKCARD_FORBID_BUY.getCode(),TmsCounterResultEnum.PENSION_BANKCARD_FORBID_BUY.getDesc());
            }
        }catch (Exception e) {
            logger.error("tradeCardValidate is error :", e);
        }

    }

    /**
     * 通过银行摘要校验银行卡是否是税延卡
     * @param txAcctNo	交易账号
     * @param bankAcct	银行卡号
     * @return void
     * @author: junkai.du
     * @date: 2023/7/20 19:13
     * @since JDK 1.8
     */
    public void tradeDigestCardValidate(String txAcctNo, String bankAcct){
        try {
            QueryBankCardInfoResponse queryBankCardInfoResponse = tmsCounterOutService.queryBankCardInfo(txAcctNo, bankAcct);
            // 柜台不支持Y份额基金交易  6-税延卡
            if (null != queryBankCardInfoResponse && Constants.PENSION_CARD_ATTR.equals(queryBankCardInfoResponse.getBankAcctAttr())){
                throw new TmsCounterException(TmsCounterResultEnum.PENSION_BANKCARD_FORBID_BUY.getCode(),TmsCounterResultEnum.PENSION_BANKCARD_FORBID_BUY.getDesc());
            }
        }catch (Exception e) {
            logger.error("tradeCardValidate is error :", e);
        }

    }


    /**
     * 校验银行卡是否是税延卡
     *
     * @param outDtoList 份额合并转出
     * @param inDto      份额合并转入
     * @return void
     * @author: junkai.du
     * @date: 2023/7/21 13:43
     * @since JDK 1.8
     */
    public void validateMergeVolBank(List<ShareMergeVolOrderReqDto> outDtoList, ShareMergeVolOrderReqDto inDto) {
        if (!Objects.isNull(inDto) && StringUtils.isNotEmpty(inDto.getBankAcctDigest())){
            tradeDigestCardValidate(inDto.getCustNo(), inDto.getBankAcctDigest());
        }

        for (ShareMergeVolOrderReqDto outDto : outDtoList) {
            if (!Objects.isNull(outDto) && StringUtils.isNotEmpty(outDto.getBankAcctDigest())){
                tradeDigestCardValidate(outDto.getCustNo(), outDto.getBankAcctDigest());
            }
        }
    }
}
