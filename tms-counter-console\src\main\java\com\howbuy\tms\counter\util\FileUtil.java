/**
 *Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.util;

import com.howbuy.dfile.HInputStream;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @description:(文件工具类) 
 * <AUTHOR>
 * @date 2018年2月24日 上午11:32:24
 * @since JDK 1.6
 */
public class FileUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);
    /**
     * 
     * downLoadFile:(下载文件)
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2018年2月24日 上午11:33:14
     */
    public static void downLoadFile(FileSdkPathInfo fileSdkPathInfo, HttpServletResponse response) throws Exception{
        
        //通过文件路径获得File对象(假如此路径中有一个download.pdf文件)
        //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型  
        response.setContentType("application/octet-stream");
        //2.设置文件头：最后一个参数是设置下载文件名(假如我们叫a.pdf)  
        String fileNameStr =fileSdkPathInfo.getFileName();
         fileNameStr = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.name());
        response.setHeader("Content-Disposition", "attachment;fileName="+fileNameStr);  
        ServletOutputStream out = null;
        HInputStream inputStream = null;
        try {  
            inputStream = FileSdkUtil.buildHInputStream(fileSdkPathInfo);
            //3.通过response获取ServletOutputStream对象(out)  
            out = response.getOutputStream();  
            int b = 0;  
            byte[] buffer = new byte[512];  
            while ((b = inputStream.read(buffer)) >=0 ){  
                //4.写到输出流(out)中  
                out.write(buffer,0,b);  
            }  
            inputStream.close();  
            out.close();  
            out.flush();  
  
        } catch (Exception e) {  
            logger.error("文件下载异常：",e);
            throw new TmsCounterException("0002", "文件下载异常",e);
        }finally{
        	if (inputStream != null){
        		inputStream.close(); 
        	}
            if (out != null){
            	out.close();  
                out.flush();
            }
        }  
    }
}

