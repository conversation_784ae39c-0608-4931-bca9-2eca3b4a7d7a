/**
 *Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto.common;

import java.io.Serializable;

/**
 * @description:(经办人信息)
 * <AUTHOR>
 * @date 2017年5月8日 下午3:58:51
 * @since JDK 1.6
 */
public class TransactorInfoDto implements Serializable {

    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 2136483659815952167L;

    /**
     * 经办人证件号
     */
    private String transactorIdNo;

    /**
     * 经办人证件类型
     */
    private String transactorIdType;

    /**
     * 经办人姓名
     */
    private String transactorName;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * 投顾代码
     */
    private String consCode;
    /**
     * 申请日期
     */
    private String appDt;
    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 撤单类型
     */
    private String cancelType;
    
    /**
     * 撤单原因
     */
    private String cancelMemo;

    /**
     * 是否经办: 0-否；1-是(个人用户默认为否，机构客户默认为是)
     */
    private String agentFlag;
    
    /**
     * 失败原因
     */
    private String checkFaildDesc;

    /**
     * 撤单实时回储蓄罐 04=银行卡 06=储蓄罐 
     */
    private String withdrawDirection;
    
    public String getWithdrawDirection() {
        return withdrawDirection;
    }

    public void setWithdrawDirection(String withdrawDirection) {
        this.withdrawDirection = withdrawDirection;
    }

    public String getCheckFaildDesc() {
        return checkFaildDesc;
    }

    public void setCheckFaildDesc(String checkFaildDesc) {
        this.checkFaildDesc = checkFaildDesc;
    }

    public String getAgentFlag() {
        return agentFlag;
    }

    public void setAgentFlag(String agentFlag) {
        this.agentFlag = agentFlag;
    }

    public String getCancelType() {
        return cancelType;
    }

    public void setCancelType(String cancelType) {
        this.cancelType = cancelType;
    }
    
    public String getCancelMemo() {
        return cancelMemo;
    }

    public void setCancelMemo(String cancelMemo) {
        this.cancelMemo = cancelMemo;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getConsCode() {
        return consCode;
    }

    public void setConsCode(String consCode) {
        this.consCode = consCode;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getTransactorIdNo() {
        return transactorIdNo;
    }

    public void setTransactorIdNo(String transactorIdNo) {
        this.transactorIdNo = transactorIdNo;
    }

    public String getTransactorIdType() {
        return transactorIdType;
    }

    public void setTransactorIdType(String transactorIdType) {
        this.transactorIdType = transactorIdType;
    }

    public String getTransactorName() {
        return transactorName;
    }

    public void setTransactorName(String transactorName) {
        this.transactorName = transactorName;
    }

}
