/**
*撤单
*<AUTHOR>
*@date 2017-04-01 14:56
*/
$(function(){
    Cancel.custInfo = {};
    Cancel.canCancelOrders = [];
    Cancel.chooseOrder = {};
    Cancel.init();

});
var Cancel = {
    initCustQuery:function(hboneNo){
        /**
         * 查询客户基本信息
         */
        HighCustInfo.queryCustInfo(hboneNo);

        /**
         * 绑定客户选择事件
         */
        setTimeout(Cancel.custInfoBind(), 2000);
	},
	initBind:function(){
        $("#confimCancelBtn").on('click',function(){
            Cancel.cancelValidAndSubmit();
        });
        $("#queryCustInfoBtn").on('click',function(){
            Cancel.initCustQuery();
        });

        /**
         * 双击客户号查询客户信息
         */
        $("#custNo").on('dblclick',function(){
            QueryCustInfoSubPage.selectCustNo($(this));
        });

        $(".selectAgened").change(function(){
            var selectAgened = $(this).val();
            if("1" == selectAgened){
                $("#transactorInfoForm").show();

            }else{
                $("#transactorInfoForm").hide();
            }
        });

    },
	init:function(){
    	Cancel.initBind();
		var selectConsCodesHtml = ConsCode.getConsCodeSelectHtml();
		$(".selectconsCode").html(selectConsCodesHtml);

        Cancel.urlParams = CommonUtil.getParamJson() || {};

        // 初始化CRM参数数据
        OnLineOrderFile.initCrmUrlWithOutAppoint(Cancel.urlParams,Cancel.initCustQuery);
	},

	initCrmCancelOrder:function(urlParams){
        var outSource = urlParams['source'];
        if('crm' == outSource){
            // 初始化选择撤单
            var crmDealNo = urlParams['preId'];
            $(".selectCancelOrder").each(function (index, element) {
                var selectedOrderIndex = $(element).val();
                var cancelConfirmForm = Cancel.canCancelOrders[selectedOrderIndex] || {};
                var appointmentDealNo = cancelConfirmForm.appointmentDealNo;

                if(!CommonUtil.isEmpty(appointmentDealNo) &&  appointmentDealNo == crmDealNo){
                    $(element).click();
                }
            });

            var deleteElement = [];
            $("#rsList").find("tr").each(function(index, element){
                var cancelConfirmForm = Cancel.canCancelOrders[index] || {};
                var appointmentDealNo = cancelConfirmForm.appointmentDealNo || '';
                if(appointmentDealNo != crmDealNo){
                	deleteElement.push(deleteElement);
                }
            });

            $(deleteElement).each(function (index, element) {
                $(element).remove();
            });
        }
	},
	
	/**
	 * 客户信息选择绑定处理
	 */
	custInfoBind:function(){
		
		 $(".selectcust").click(function(){
			 $(this).attr('checked','checked').siblings().removeAttr('checked');
			 var selectIndex = $(this).attr("index");
			 Cancel.custInfo = HighCustInfo.custList[selectIndex] || {};
			 Cancel.queryCanCancel(Cancel.custInfo.custNo || '', Cancel.custInfo.disCode);
			 
			 if('0' == Cancel.custInfo.invstType || '2' == Cancel.custInfo.invstType){
				// 显示经办人信息
		    	 HighCustInfo.queryCustTransInfo(Cancel.custInfo.custNo, Cancel.custInfo.disCode);
				 //代理
				 $(".selectAgened").val('1');
				 $("#transactorInfoForm").show();
				 $("#agenInfoForm").hide();
			 }
		 });
	},
	/**
	 * 校验和提交
	 * @returns {Boolean}
	 */
	cancelValidAndSubmit:function(){
		if(Cancel.click == '1'){
			return false;
		}
		Cancel.click = '1';
		var selectObj = $("input[name='orderIndex'][type='radio']:checked");
		var selectedOrderIndex = selectObj.val();
		if(CommonUtil.isEmpty(selectedOrderIndex)){
			CommonUtil.layer_tip("请选择要撤单的订单");
			Cancel.click = '0';
			return false;
		}
		
		var userCancelFlag = $(selectObj).closest("tr").find('.selectUserCancelFlag').val();
		if(CommonUtil.isEmpty(userCancelFlag)){
			CommonUtil.layer_tip("请选择撤单方式", null, null);
			Cancel.click = '0';
			return false;
		}
		var cancelMemo = $("#cancelMemo").val();
		if('0' == userCancelFlag){
			// 强制取消，撤单原因必填
			if(CommonUtil.isEmpty(cancelMemo)){
				Cancel.click = '0';
				CommonUtil.layer_tip("非用户选择撤单，撤单原因必填");
				return false;
			}
		}
		
		//代理人OR经办人信息
		var transactorInfoForm = {};
		transactorInfoForm = $("#transactorInfoForm").serializeObject();
		var othetInfoForm = $("#othetInfoForm").serializeObject();
		if(othetInfoForm.agentFlag == '1'){
			var validTransactorRst = Valid.valiadateFrom($("#transactorInfoForm"));
			if(!validTransactorRst.status){
				CommonUtil.layer_tip(validTransactorRst.msg);
				Cancel.click = '0';
				return false;
			}
			
			if('0' == transactorInfoForm.transactorIdType && !Valid.id_rule(transactorInfoForm.transactorIdNo)){
				CommonUtil.layer_tip("经办人证件号格式不正确");
				Cancel.click = '0';
				return false;
			}
		}
		var cancelConfirmForm = Cancel.chooseOrder;

		var refundValidFlag = Cancel.refundValid();
		if (!refundValidFlag) {
			Cancel.click = '0';
			return false;
		}

		// 撤单校验
		var cancelValidRst = Cancel.canceleValid(cancelConfirmForm.txAcctNo, cancelConfirmForm.productCode, cancelConfirmForm.mBusiCode, CommonUtil.formatDateToStr(cancelConfirmForm.appDtm, 'yyyyMMdd'), CommonUtil.formatDateToStr(cancelConfirmForm.appDtm, 'hhmmss'), cancelConfirmForm.disCode);
		if(!cancelValidRst.status){
			Cancel.click = '0';
			return false;
		}
		
		if(cancelValidRst.status){
			
			if(cancelValidRst.overAppointFlag){
				Cancel.confrimLayer("已过预约截止日，如撤单，需联系基金管理人，确认是否撤单", Cancel.confirm, null);
			}else {
				Cancel.confirm();
			}
		}
		
	},

	
	/***
	 * 确认撤单
	 */	
	confirm : function(){
		var  uri= TmsCounterConfig.CANCEL_CONFIRM_URL ||  {};
		var selectObj = $("input[name='orderIndex'][type='radio']:checked");
		var selectedOrderIndex = selectObj.val();
		var userCancelFlag = $(selectObj).closest("tr").find('.selectUserCancelFlag').val();
		var cancelMemo = $("#cancelMemo").val();
		var othetInfoForm = $("#othetInfoForm").serializeObject();
		var cancelConfirmForm = Cancel.canCancelOrders[selectedOrderIndex];
		var refundForm = $("#refundForm").serializeObject();

		//代理人OR经办人信息
		var transactorInfoForm = {};
		transactorInfoForm = $("#transactorInfoForm").serializeObject();
		
		var reqparamters ={"transactorInfoForm":JSON.stringify(transactorInfoForm),
			"cancelConfirmForm": JSON.stringify(cancelConfirmForm),
			"custInfoForm":JSON.stringify(Cancel.custInfo),
			"othetInfoForm":JSON.stringify(othetInfoForm),
            "materialinfoForm":JSON.stringify(OnLineOrderFile.buildOrderCheckFile()),
			"refundForm":JSON.stringify(refundForm)};
		reqparamters["userCancelFlag"] = userCancelFlag;
		reqparamters["cancelMemo"] = cancelMemo;
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Cancel.callBack);
	},
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
            layer.confirm('撤单提交成功', {
                btn: ['确定'] //按钮
            }, function(){
                layer.closeAll();
                // 下单成功，刷新页面
                if(OnLineOrderFile.isCrm()){
                    CommonUtil.closeCurrentUrl();
                }else{
                    // 刷新页面
                    CommonUtil.reloadUrl();
                }
            });
		}else{
			CommonUtil.layer_tip("撤单提交失败,"+respDesc);
		}
		
		Cancel.click = '0';
		// 刷新结果
		Cancel.queryCanCancel(Cancel.custInfo.custNo || '');
	},
	
	/**
	 * @param custNo
	 * @param disCode
	 * 
	 * 查询可撤单订单
	 */
	queryCanCancel:function(custNo, disCode){
		var  uri= TmsCounterConfig.QUERY_CAN_CANCEL_URL ||  {};
		var reqparamters = {"custNo":custNo, "disCode":disCode};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, Cancel.queryCanCancelBack);
	},
	/**
	 * 可撤单订单信息
	 */
	queryCanCancelBack:function(data){
		var bodyData = data.body || {};
		Cancel.canCancelOrders= bodyData.canCancelOrders || [];
		
		$("#rsList").html('');
		if(Cancel.canCancelOrders.length <=0){
			var trHtml = '<tr><td colspan="11">没有查询到可以撤销交易</td></tr>';
			$("#rsList").append(trHtml);
			return false;
		}
		
		var selectCancelHtml = '<span class="select-box"><select name="userCancelFlag" class="select selectUserCancelFlag" >'+'</select></span>';
		
		$(Cancel.canCancelOrders).each(function(index,element){
			var trList = [];
			trList.push(selectCancelHtml);
			trList.push(CommonUtil.formatData(element.productCode));
			trList.push(CommonUtil.formatData(element.productName));
			trList.push(CommonUtil.getMapValue(CONSTANTS.M_BUSI_CODE_NAME_MAP,element.mBusiCode,'--'));
			trList.push(CommonUtil.getMapValue(CONSTANTS.PAYMENT_TYPE_MAP,element.paymentType,'--'));
			trList.push(CommonUtil.formatData(element.appAmt,''));
			trList.push(CommonUtil.formatData(element.appVol,''));
			trList.push(CommonUtil.getMapValue(CONSTANTS.ORDER_STATUS_MAP,element.orderStatus));
			trList.push(CommonUtil.formatData(element.dealNo));// 中台订单号
			trList.push(CommonUtil.getMapValue(CONSTANTS.PAY_STATUS_MAP,element.payStatus));
			trList.push(CommonUtil.formatDateToStr(element.appDtm, "yyyy-MM-dd hh:mm:ss"));
			trList.push(CommonUtil.formatData(element.submitTaDt));// 上报TA日期
			var trHtml = '<tr class="text-c"><td><input type="radio" class="selectCancelOrder" name="orderIndex" value="'+index+'"></td><td>'+trList.join('</td><td>')+'</td></tr>';
			$("#rsList").append(trHtml);
		});
		 
		var selectUserCancelFlag = CommonUtil.selectOptionsHtml(CONSTANTS.USER_CANCEL_FLAG_MAP);
		$(".selectUserCancelFlag").html(selectUserCancelFlag);

        $(".selectCancelOrder").off();
        $(".selectCancelOrder").on('click', function () {
            var selectedOrderIndex = $(this).val();
            var cancelConfirmForm = Cancel.canCancelOrders[selectedOrderIndex];
            Cancel.chooseOrder = cancelConfirmForm;
            var preId = cancelConfirmForm.appointmentDealNo || '';

            var crmDealNo = Cancel.urlParams['preId'];
            if(!CommonUtil.isEmpty(crmDealNo) && preId == crmDealNo){
                OnLineOrderFile.query(Cancel.urlParams, null, OnLineOrderFile.CRM_OP_CHECK_NODE_PRE);
            }else{
                OnLineOrderFile.query(null, Cancel.getSelectCustMaterial(), OnLineOrderFile.CRM_OP_CHECK_NODE_PRE);
			}
			// 显示回可用配置项
            var payType = cancelConfirmForm.paymentType || '';
			Cancel.refundDirectionOnChange(payType);
        });

        Cancel.initCrmCancelOrder(Cancel.urlParams);
	},

	/**
	 * 回可用备注选择
	 * @param redeemDirection
	 */
	refundDirectionOnChange: function(payType) {
		if ("01" == payType) {
			$("#refundDirection").html(Cancel.refundDirectionOnChangeHtml("refundDirectionId"));
			//回款去向
			var withdrawDirectionHtml = CommonUtil.selectOptionsHtml(CONSTANTS.WITHDRAW_DIR_BUY_SELFDRAWING_MAP);
			$("#withdrawDirection").html(withdrawDirectionHtml);
			$("#withdrawDirection").on('change', function () {
				var withdrawDir = $(this).val();
				$("#refundFormId").html('')
				$("#refundDirectionId").after(Cancel.generateCancelFormTable("refundFormId",withdrawDir));
				// 回可用备注
				$("#selectRedeemMemo").html(CommonUtil.selectOptionsHtml(CONSTANTS.REFUND_FINA_AVAIL_SELECT_MAP));
				$("#selectRedeemMemo").on('change', function () {
					Cancel.refundMemoSelectOnChange();
				});
			});

		}else {
			$("#refundDirection").html("");
		}
	},

	/**
	 * 回可用备注
	 * @param redeemDirection
	 */
	refundMemoSelectOnChange: function() {
		var memoSelect = $("#selectRedeemMemo").val();
		if ("1" == memoSelect) {
			$("#refundMemo").html('<input class="" id="selectRefundMemo" name="refundFinaAvailMemo" isnull="false" datatype="s">');
		}
		if ("0" == memoSelect) {
			$("#refundMemo").html('<select id="selectRefundMemo" name="refundFinaAvailMemo" class="select" isnull="false" datatype="s">');
			// 初始化基金代码多选
			CommonUtil.singleSelectForRefund("selectRefundMemo",TmsCounterConfig.HIGH_QUERY_PRODUCT_CODEANDNAME_URL, Cancel.chooseOrder.productChannel);
		}
	},

	refundDirectionOnChangeHtml:function(sellFormTableId) {
		var table = '<tbody id="'+sellFormTableId+'">' +
			'				<tr class="text-c">' +
			'                <td>回款方向</td>' +
			'                <td>' +
			'					<span class="select-box inline">' +
			'						<select id="withdrawDirection" name="withdrawDirection" class="select" isnull="false" datatype="s"></select>' +
			'					</span>'+
			'				 </td>' +
			'                <td></td>' +
			'                <td></td>' +
			'              </tr>' +
			'        </tbody>';
		return table;
	},

	generateCancelFormTable:function(sellFormTableId, dir) {
		var table = '';
		if(dir != '04' && dir != '06')  {
			table += '<tbody id="' + sellFormTableId + '">' +
				'              <tr class="text-c">' +
				'                <td>回可用余额备注选择</td>' +
				'                <td>' +
				'					<span class="select-box inline">' +
				'						<select id="selectRedeemMemo" class="select" isnull="false" datatype="s"></select>' +
				'					</span>' +
				'				 </td>' +
				'                <td>回可用余额备注</td>' +
				'                <td class="readText availVol"><span class="select-box inline" id="refundMemo"></span></td>' +
				'              </tr>';
				if(dir != '5') {
					table +='        <tr class="text-c">' +
					'                <td>回可用余额金额</td>' +
					'                <td>' +
					'					<input type="text" placeholder="请输入" id="refundFinaAvailAmt" class="refundFinaAvailAmt" name="refundFinaAvailAmt" isnull="false" datatype="s" errormsg="回可用金额">' +
					'				 </td>' +
					'                <td></td>' +
					'                <td></td>' +
					'              </tr>';
				}
			table +='        </tbody>';
		}
		return table;
	},

	refundValid:function(){
		var length = $("#refundFormId").length;
		if (length > 0) {
			var withdrawDirection = $("#withdrawDirection").val();
			if (withdrawDirection == "6" || withdrawDirection == "7") {
				var refundAmtInput = $("#refundFinaAvailAmt").val();
				var refundAmt = CommonUtil.unFormatAmount(refundAmtInput);
				// 检查是否数字
				if (refundAmt == "" || !CommonUtil.validFloat(refundAmt)) {
					layer.alert("回可用余额金额只能输入数字");
					return false;
				}

				var appAmt = Cancel.chooseOrder.appAmt;
				if (refundAmt > appAmt) {
					layer.alert("回可用余额金额不能大于申请金额");
					return false;
				}

				var refundMemo = $("#selectRefundMemo").val();
				if (refundMemo == null || $.trim(refundMemo) == '') {
					layer.alert("回可用余额备注不能为空");
					return false;
				}
			}
		}
		return true;
	},
	/**
	 * 撤单校验
	 * @param txAcctNo 
	 * @param productCode
	 * @param disCode
	 * @param btn
	 */
	canceleValid:function(txAcctNo, productCode, mBusiCode, appDt, appTm, disCode){
		var uri = TmsCounterConfig.HIGH_CANCEL_VALID_URL ;
	
		var reqParams = {"txAcctNo":txAcctNo, 
				"productCode":productCode,
				"mBusiCode":mBusiCode, 
				"appDt":appDt,
				"appTm":appTm,
				"disCode":disCode};
		
		var paramters = CommonUtil.buildReqParams(uri, reqParams, false);
		var rst = {"status":false};
		
		CommonUtil.disabledBtn("confimCancelBtn");
		CommonUtil.ajaxAndCallBack(paramters, function(data){
			CommonUtil.enabledBtn("confimCancelBtn");
			
			var respCode = data.code || '';
			var desc =data.desc || '';
			var body = data.body || {};
			
			if(CommonUtil.isSucc(respCode)){
				rst.overAppointFlag = body.overAppointFlag;
				rst.status = true;
				
			}else{
				CommonUtil.layer_tip(respCode+"("+desc+")");
			}
		});
		
		return rst;
	},
	
   confrimLayer:function(content,ok_cb,cancle_cb){
		
		layer.confirm(content, {
			 btn: ['确定', '取消'] //按钮
			}, function(){
			  if(ok_cb){
				  layer.closeAll();
				  ok_cb(); 
			  }
			 
			}, function(){
			  if(cancle_cb){
				  cancle_cb();
				  Cancel.click = '0';
				  CommonUtil.enabledBtn("confimCancelBtn");
				  layer.closeAll();
			  }else{
				  Cancel.click = '0';
				  CommonUtil.enabledBtn("confimCancelBtn");
				  layer.closeAll();
			  }
			  
			});
	},
    /**
     *
     * @Description  初始化材料
     *
     * @param null
     * @return
     * <AUTHOR>
     * @Date 2019/5/31 17:30
     **/
    getSelectCustMaterial:function(){
        var custSelectOrder = {};

        var selectObj = $("input[name='orderIndex'][type='radio']:checked");
        var selectedOrderIndex = selectObj.val();
        var cancelConfirmForm = Cancel.canCancelOrders[selectedOrderIndex];

        var appointmentDealNo = cancelConfirmForm.appointmentDealNo || '';
        custSelectOrder["hboneno"] = Cancel.custInfo.hboneNo;// 一账通帐号
        custSelectOrder["pcode"] = cancelConfirmForm.productCode;// 产品代码
        custSelectOrder["busiid"] =  OnLineOrderFile.CRM_CANCEL;// 业务类型ID
        custSelectOrder["preid"] =  appointmentDealNo// 业务类型ID
        return custSelectOrder;
    }

};

