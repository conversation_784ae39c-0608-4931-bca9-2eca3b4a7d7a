package com.howbuy.tms.counter.dto;

import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.OwnershipRightTransferCheckFlag;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
public class QueryOwnershipRightTransferReqDto extends BaseDto {
    /**
     * 客户号
     */
    private String custNo;
    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 基金产品号
     */
    private String fundCode;

    /**
     * 中台业务代码
     */
    private List<String> mBusinessCodeList;

    /**
     * 中台业务代码
     */
    private String mBusiCode;
    /**
     * 基金类型
     */
    private String fundType;

    /**
     * 基金二级类型
     */
    private String fundSubType;

    /**
     * 审核状态
     *
     * @see OwnershipRightTransferCheckFlag
     */
    private String checkFlag;

    /**
     * 确认日期起始时间
     */
    private String beginDtm;

    /**
     * 确认日期截止时间
     */
    private String endDtm;

    /**
     * 页码
     */
    private int pageNo = 1;
    /**
     * 每页记录数
     */
    private int pageSize = 20;


    public QueryOwnershipRightTransferReqDto(HttpServletRequest request) {
        this.custNo = request.getParameter("custNo");
        this.hboneNo = request.getParameter("hboneNo");
        this.fundCode = request.getParameter("fundCode");
        if (StringUtils.isNotBlank(request.getParameter("mBusiCodes"))) {
            this.mBusinessCodeList = Arrays.asList(request.getParameter("mBusiCodes").split(","));
        } else {
            this.mBusinessCodeList = new ArrayList<>();
            this.mBusinessCodeList.add(BusinessCodeEnum.FORCE_REDEEM.getMCode());
            this.mBusinessCodeList.add(BusinessCodeEnum.FORCE_ADD.getMCode());
            this.mBusinessCodeList.add(BusinessCodeEnum.FORCE_SUBTRACT.getMCode());
            this.mBusinessCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode());
            this.mBusinessCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode());
        }
        this.fundType = request.getParameter("fundType");
        this.fundSubType = request.getParameter("fundSubType");
        this.checkFlag = request.getParameter("checkFlag");
        this.beginDtm = request.getParameter("beginDtm");
        this.endDtm = request.getParameter("endDtm");
    }

    /**
     * 参数校验
     */
    public void check() {
        if (StringUtils.isBlank(this.fundType)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "基金类型不能为空");
        }
        if (StringUtils.isBlank(this.fundSubType)) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "基金二级类型不能为空");
        }
        if (this.beginDtm == null || this.endDtm == null) {
            throw new TmsCounterException(TmsCounterResultEnum.PARAMS_ERROR.getCode(), "确认日期不能为空");
        }
    }

}
