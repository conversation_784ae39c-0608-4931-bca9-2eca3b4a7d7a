/**
 * 转托管转入审核
 */
$(function(){
	Init.init();
	Init.selectBoxTransferTubeBusiType();
	$("#returnBtn").on('click',function(){
		CheckTransferTubeIn.confirm(CounterCheck.Faild);
	});

	$("#succBtn").on('click',function(){
		CheckTransferTubeIn.confirm(CounterCheck.Succ);
	});

	$("#fundCode").on('blur',function(){
		QueryCurrentFundInfo.queryFundInfo();
	});

	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckTransferTubeIn.checkOrder = {};
	CheckTransferTubeIn.init(checkId,custNo,disCode,idNo);
});

var CheckTransferTubeIn = {
	init:function(checkId, custNo, disCode,idNo){
		// 设置客户信息
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);

		// 设置申请订单信息
		QueryCheckOrder.queryCheckOrderById(checkId,CheckTransferTubeIn.queryCheckOrderByIdBack);
	},

	/***
	 * 审核确认
	 */
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var transfertubeInForm = $("#transfertubeInForm").serializeObject();

		var uri= TmsCounterConfig.CHECK_FUND_CONFIRM_URL ||  {};

		if(CounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckTransferTubeIn.checkFaildDesc = $("#checkFaildDesc").val();
		}else{
			var validRst = Valid.valiadateFrom($("#transfertubeInForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}

			var checkResultReply = CheckTransferTubeIn.checkTransferTubeInValid(transfertubeInForm,CheckTransferTubeIn.checkOrder);
			if(!checkResultReply.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(checkResultReply.tip);
				return false;
			}
		}

		var reqparamters ={"checkFaildDesc":CheckTransferTubeIn.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckTransferTubeIn.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, CheckTransferTubeIn.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';

		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},

	checkTransferTubeInValid:function(checkForm , orderForm){
		var fundCode = checkForm.fundCode || '';
		var inAppVol = CommonUtil.unFormatAmount(checkForm.inAppVol) || '';
		var cpAcctNo = checkForm.cpAcctNo || '';
		var tSellerCode = checkForm.tSellerCode || '';

		var result = {"status":true,"tip":''};

		if(fundCode != (orderForm.fundCode || '')){
			result.status = false;
			result.tip = "基金代码不匹配，请重新确认";
			return result;
		}
		if(inAppVol != (orderForm.appVol || '')){
			result.status = false;
			result.tip = "转入份额不匹配，请重新确认";
			return result;
		}

		var orderCpAcctNo = orderForm.cpAcctNo || '';
		if(cpAcctNo != orderCpAcctNo){
			result.status = false;
			result.tip = "银行卡不匹配，请重新确认";
			return result;
		}

		if(tSellerCode != (orderForm.tSellerCode || '')){
			result.status = false;
			result.tip = "对方销售人代码不匹配，请重新确认";
			return result;
		}

		return result;

	},

	queryCheckOrderByIdBack:function(data){
		var bodyData = data.body || {};
		CheckTransferTubeIn.checkOrder = bodyData.checkOrder || {};

		if(CommonUtil.isEmpty(CheckTransferTubeIn.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}

		if(CheckTransferTubeIn.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}

		QueryCurrentFundInfo.queryFundInfo(CheckTransferTubeIn.checkOrder.fundCode,false);

		if($("#transferTubeBusiType").length > 0){
			$("#transferTubeBusiType").val(CheckTransferTubeIn.checkOrder.transferTubeBusiType);
		}

		if($("#tSellerTD").length > 0){
			Init.setTSellerCodeTD(CheckTransferTubeIn.checkOrder.transferTubeBusiType);
			//$("#tSellerCode").val(CheckTransferTubeIn.checkOrder.tSellerCode);
		}

		if($("#selectBank").length > 0){
			$("#selectBank").val(CheckTransferTubeIn.checkOrder.cpAcctNo);
		}

		if($("#originalAppDealNo").length > 0){
			$("#originalAppDealNo").html(CheckTransferTubeIn.checkOrder.originalAppDealNo);
		}

		/**other*/
		BodyView.setCheckOperInfoView(CheckTransferTubeIn.checkOrder);
	},
}
