<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <title>柜台交易复核</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 业务审核<span class="c-gray en">&gt;</span> 交易审核（零售） <span class="c-gray en">&gt;</span> 柜台交易复核 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
            <div class="container_box">
            <form id="queryConditonForm">
                <p class="mainTitle mt10">柜台交易复核</p>
                <div class="cp_top mt30">
                	 <div class="cp_top mt30">
	                    <span class="normal_span ml30">客户号：</span>
	                    <input type="text"  placeholder="请输入" id="custNo" name="txAcctNo">
	                    <span class="normal_span ml30">证件号：</span>
	                    <input type="text"  placeholder="请输入" id="idNo" name="idNo">
                    </div>
                    <div class="cp_top mt30">
	                    <span class="normal_span ml30">业务类型：</span>
	                    <span class="select-box inline">
	                       <select name="txCode" id="selectTxCode" class="select">
	                       </select>
	                    </span>
	                    <span class="normal_span ml30">预申请单号：</span>
	                    <input type="text"  placeholder="请输入" name="dealAppNo" id="dealAppNo">
                    </div>
                    <div class="cp_top mt30">
                		<span class="normal_span ml30">产品代码：</span>
                    	<input type="text"  placeholder="请输入" id="fundCode" name="fundCode">
                	</div>
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryBtn">查询</a>
                </div>
                </form>
            </div>
        </div>
    </div>
    <div class="clear page_all dataTables_wrapper no-footer">
        <div class="fl page_sj ml20" id="staticId">当页小计：申请笔数【】申请金额【】申请份额【】合计：申请笔数【】申请金额【】申请份额【】</div>
    </div>
    <div class="page-container">
        <p class="main_title">查询结果</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                   		<th>序号</th>
                   		<th>预申请单号</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>证件类型</th>
                        <th>证件号码</th>
                        <th>产品代码</th>
                        <th>产品名称</th>
                        <th>业务类型</th>
                        <th>回款方向</th>
                        <th>赎回比例</th>
                        <th>申请金额（元）</th>
                        <th>申请份额（份）</th>
                        <th>申请日期</th>
                        <th>申请时间</th>
                        <th>操作员</th>
                        <th>操作</th>
                    </tr>
               </thead>
                <tbody id="rsList">
                </tbody>
            </table>
        </div>
        <div class="clear page_all">
            <div class="fy_part fr mt10" id="pageView"></div>
        </div>
    </div>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/fund/check/countercheck.js?v=20200301002"></script>
	<script type="text/javascript" src="../../../js/fund/common/init.js?v=20200301002"></script>
</body>

</html>