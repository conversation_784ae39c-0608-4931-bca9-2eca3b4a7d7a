/**
 *Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.orders.search.facade.query.fund.queryacfundacct.QueryFundTxAcctFacade;
import com.howbuy.tms.orders.search.facade.query.fund.queryacfundacct.QueryFundTxAcctRequest;
import com.howbuy.tms.orders.search.facade.query.fund.queryacfundacct.QueryFundTxAcctResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;


/**
 * @description:查询基金交易账号信息
 * <AUTHOR>
 * @date 2023年5月17日 下午4:28:37
 * @since JDK 1.8
 */
@Controller
public class QueryAcFundTxAcctController extends AbstractController {

    @Autowired
    @Qualifier("queryAcFundTxAcctFacade")
    private QueryFundTxAcctFacade queryFundTxAcctFacade;

    /**
     * @description:查询基金交易账号信息
     * <AUTHOR>
     * @date 2023年5月17日 下午4:28:37
     * @since JDK 1.8
     */
    @RequestMapping("tmscounter/qeuryfundtxacctno.htm")
    public void queryFundInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String txAcctNo = request.getParameter("custNo");
        String disCode = request.getParameter("disCode");
        String cpAcctNo = request.getParameter("cpAcctNo");
        String taCode = request.getParameter("taCode");
        QueryFundTxAcctRequest acctRequest = new QueryFundTxAcctRequest();
        acctRequest.setTxAcctNo(txAcctNo);
        acctRequest.setCpAcctNo(cpAcctNo);
        acctRequest.setDisCode(disCode);
        acctRequest.setTaCode(taCode);
       QueryFundTxAcctResponse acctResponse = queryFundTxAcctFacade.execute(acctRequest);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("fundAcctNo", acctResponse.getFundAcctNo());
        bodyResult.put("fundTxAcctNo", acctResponse.getFundTxAcctNo());
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

}