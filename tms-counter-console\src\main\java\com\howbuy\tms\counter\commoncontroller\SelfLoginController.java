/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.permission.UserService;
import com.howbuy.tms.counter.auth.EasyTypeToken;
import com.howbuy.tms.counter.auth.LoginTypeEnum;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * @className SelfLoginController
 * @description
 * <AUTHOR>
 * @date 2019/6/11 15:35
 */
@Controller
public class SelfLoginController {
    private static Logger LOG = LogManager.getLogger(SelfLoginController.class);

    @Autowired
    private UserService userService;
    @RequestMapping("tmscounter/selflogin.htm")
    public ModelAndView selfLogin(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String username = request.getParameter("operatorNo");
        if(username==null || "".equals(username.trim())){
            throw new TmsCounterException("", username + ", 用户名为空，请检查");
        }
        Subject currentUser = SecurityUtils.getSubject();
        if(!currentUser.isAuthenticated()){
            EasyTypeToken token = new EasyTypeToken(username, "");
            token.setType(LoginTypeEnum.NOPASSWD);
            token.setRememberMe(false);
            try{
                currentUser.login(token);
            } catch (Exception e) {
                LOG.error("", e);
                throw new TmsCounterException("", e.getMessage(),e);
            }
        }
        if(currentUser.isAuthenticated()) {

            UserAccountModel userAccountModel = userService.getUserByUserName(username);
            request.getSession().setAttribute(Constants.AUTHENTICATION_KEY, userAccountModel);
            request.getSession().setAttribute(Constants.SESSION_USER, userAccountModel);

            if(userAccountModel == null){
                throw new TmsCounterException("", username + ", 用户信息不存在，请联系中台管理员添加改用户");
            }

            OperatorInfoCmd operatorInfoCmd = new OperatorInfoCmd();

            operatorInfoCmd.setOperatorNo(userAccountModel.getUserName());
            SessionUtil.setValue(TmsCounterConstant.SESSION_OPERATORINFO, operatorInfoCmd, request);

            // 设置登录时间
            UserAccountModel lastLoginModel = new UserAccountModel();
            lastLoginModel.setUserId(userAccountModel.getUserId());
            lastLoginModel.setLastLogTime(new Date());
            lastLoginModel.setMemo("柜台");
            userService.updateUserInfo(lastLoginModel);

        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        WebUtil.write(response, rst);
        return null;

    }


}
