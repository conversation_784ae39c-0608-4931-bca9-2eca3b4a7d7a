/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @description:(柜台转托管订单)
 * <AUTHOR>
 * @date 2018年10月9日 下午4:22:54
 * @since JDK 1.6
 */
public class TransferTubeOrderReqDto implements Serializable {
    
    /**
     * serialVersionUID:TODO（用一句话描述这个变量表示什么）
     *
     * @since Ver 1.1
     */
    private static final long serialVersionUID = -4287707209513098739L;
    
    /**
     * 预申请订单明细号
     */
    private String dealDtlAppNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行卡号
     */
    private String bankAcct;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 协议号
     */
    private String protocolNo;
    
    /**
     * 协议类型
     */
    private String protocolType;
    
    /**
     * 转托管份额(转入或转出)
     */
    private BigDecimal appVol;
    
    /**
     * 转出前可用份额
     */
    private BigDecimal preAppVol;
    
    /**
     * 转出前冻结份额
     */
    private BigDecimal preFrznVol;
    
    /**
     * 交易通道
     */
    private String productChannel;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金名称
     */
    private String fundCode;
    /**
     * 基金份额类型
     */
    private String fundShareClass;
    /**
     * ta代码
     */
    private String taCode;

    /**
     * 原申请单号
     */
    private String originalAppDealNo;

    /**
     * 对方网点
     */
    private String tOutletCode;

    /**
     * 对方销售人处投资者基金交易账号
     */
    private String tSellerTxAcctNo;

    /**
     * 对方销售人代码
     */
    private String tSellerCode;

    /**
     * 产品类别
     */
    private String productClass;

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public String gettOutletCode() {
        return tOutletCode;
    }

    public void settOutletCode(String tOutletCode) {
        this.tOutletCode = tOutletCode;
    }

    public String gettSellerTxAcctNo() {
        return tSellerTxAcctNo;
    }

    public void settSellerTxAcctNo(String tSellerTxAcctNo) {
        this.tSellerTxAcctNo = tSellerTxAcctNo;
    }

    public String gettSellerCode() {
        return tSellerCode;
    }

    public void settSellerCode(String tSellerCode) {
        this.tSellerCode = tSellerCode;
    }

    public String getDealDtlAppNo() {
        return dealDtlAppNo;
    }

    public void setDealDtlAppNo(String dealDtlAppNo) {
        this.dealDtlAppNo = dealDtlAppNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getPreAppVol() {
        return preAppVol;
    }

    public void setPreAppVol(BigDecimal preAppVol) {
        this.preAppVol = preAppVol;
    }

    public BigDecimal getPreFrznVol() {
        return preFrznVol;
    }

    public void setPreFrznVol(BigDecimal preFrznVol) {
        this.preFrznVol = preFrznVol;
    }

    public String getProductChannel() {
        return productChannel;
    }

    public void setProductChannel(String productChannel) {
        this.productChannel = productChannel;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getOriginalAppDealNo() {
        return originalAppDealNo;
    }

    public void setOriginalAppDealNo(String originalAppDealNo) {
        this.originalAppDealNo = originalAppDealNo;
    }
}
