/**
*我的交易申请查询
*<AUTHOR>
*@date 2018-02-23 14:23
*/
$(function(){
	// 初始化下拉框
	QueryMyCounterApp.initSelect();
	
	// 初始化
	QueryMyCounterApp.init();
	
	//查询待审核订单
	QueryMyCounterApp.queryOrderInfo();
	
});

 var QueryMyCounterApp = {
	init:function(){
		$("#queryBtn").on('click',function(){
			QueryMyCounterApp.queryOrderInfo();
		});
	},
	/**
	 * 初始化下拉框
	 */
	initSelect:function(){
		
		// 初始化交易码下拉框
		var selectTxCodeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.COUNTER_TXCODE_MAP);
		$("#selectTxCode").empty();
		$("#selectTxCode").html(selectTxCodeHtml);
		
		// 初始化审核下拉框
		var selectCheckFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.COUNTER_CHECK_FLAG_MAP);
		$("#selectCheckFlag").html(selectCheckFlagHtml);
		
		// 初始化产品通道下拉框
		var selectProductChannelHtml = CommonUtil.selectOptionsHtml(CONSTANTS.PRODUCT_CHANNEL_MAP);
		$("#selectProductChannel").html(selectProductChannelHtml);
	},
	
	/**
	 *查询我的柜台申请订单
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_CHECK_ORDER_URL  ||  {};
		
		var searchForm = $("#searchCheckForm").serializeObject();
		var reqparamters = {};
		reqparamters.page = 1;
		reqparamters.pageSize = 10;
		
		for(name in searchForm){
			if(!CommonUtil.isEmpty(searchForm[name])){
				reqparamters[name] = searchForm[name];
			}
		}
		reqparamters["owner"] = "owner";
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri,paramters, QueryMyCounterApp.queryOrderInfoCallBack,"pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		QueryMyCounterApp.checkOrders = bodyData.counterOrderList || [];
		$("#rsList").empty();
		if(QueryMyCounterApp.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="9">暂无待审核记录</td></tr>';
			$("#rsList").append(trHtml);
		}
		
		$(QueryMyCounterApp.checkOrders).each(function(index,element){
			var trList = [];
			var checkBtn = '<a id="recheck'+element.dealAppNo+'" class="reCheckBtn btn btn-success radius" href="javascript:void(0);" indexvalue = '+index+'>修改</a>';
			var viewBtn = '<a id="reCheckView'+element.dealAppNo+'" class="reCheckView btn btn-secondary radius ml5" href="javascript:void(0);" indexvalue = '+index+'>查看</a>';
            var btn = '';
            if(!QueryMyCounterApp.isReject(element.checkFlag)){
            	btn = checkBtn;
            }
            btn +=viewBtn;
			trList.push(btn);
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));// 交易账号
			trList.push(CommonUtil.formatData(element.custName));//客户姓名
			trList.push(CommonUtil.formatData(element.idNo));// 证件号
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP, element.txCode, ''));//交易类型
			trList.push(CommonUtil.formatData(element.fundCode));// 产品代码
			trList.push(CommonUtil.formatData(element.fundName));// 产品名称
			trList.push(CommonUtil.formatAmount(element.appAmt));// 申请金额
			trList.push(CommonUtil.formatAmount(element.appVol));// 申请份额
			trList.push(CommonUtil.formatAmount(element.transferPrice, '--'));// 转让价格
			trList.push(CommonUtil.formatData(element.dealAppNo));// 柜台申请订单号
			trList.push(CommonUtil.formatData(element.subsAmt));// 过户份额对应的认缴金额
			trList.push(CommonUtil.formatData(element.totalSubsAmt));// 过户的总认缴金额
			trList.push(CommonUtil.getMapValue(CONSTANTS.PRODUCT_CHANNEL_MAP, element.productChannel));// 产品通道
			trList.push(CommonUtil.formatData(element.appDt) +" "+CommonUtil.formatData(element.appTm));// 申请日期时间
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP, element.checkFlag));// 审核状态
			trList.push(CommonUtil.formatData(element.memo));// 驳回原因
			trList.push(CommonUtil.formatData(element.creator));
			trList.push(CommonUtil.formatData(element.checker));
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
			$("#recheck"+element.dealAppNo).attr("dealAppNo",element.dealAppNo);
			$("#recheck"+element.dealAppNo).attr("txCode",element.txCode);
			$("#reCheckView"+element.dealAppNo).attr("dealAppNo",element.dealAppNo);
			$("#reCheckView"+element.dealAppNo).attr("txCode",element.txCode);
			
		});
		
		
		//viewType 0-查看；1-审核；2-修改
		$(".reCheckBtn").off();
		$(".reCheckBtn").on('click', function(){
		
			var dealAppNo = $(this).attr("dealAppNo");
			var txCode = $(this).attr("txCode");
			
			var params = [];
			params.push('dealAppNo=' + dealAppNo);
			params.push('viewType=2');
			var urlParams = ViewDealCommon.buildParams(params);
			
			var viewUrl = ViewDealCommon.getGetModifyViewUrl(txCode, urlParams);
			ViewDealCommon.showDeal(viewUrl);
			
			
		});
		
		//查看
		$(".reCheckView").off();
		$(".reCheckView").on('click',function(){
			var dealAppNo = $(this).attr("dealAppNo");
			console.log("dealAppNo:"+dealAppNo);
			var txCode = $(this).attr("txCode");
			
			var params = [];
			params.push('dealAppNo=' + dealAppNo);
			params.push('viewType=0');
			var urlParams = ViewDealCommon.buildParams(params);
			
			var viewUrl = ViewDealCommon.getGetModifyViewUrl(txCode, urlParams);
			ViewDealCommon.showDeal(viewUrl);
			
		});
			
	},
	
	/**
	 * 是否是驳回订单
	 * @param checkFlag 
	 *  0-待审核
	 *  1-审核通过
	 *  2-审核失败
	 *  3-审核驳回
	 *  4-作废
	 *  5-材料退回审核驳回
	 */
	isReject:function(checkFlag){
		if('3' == checkFlag || '5' == checkFlag){
			return false;
		}
		return true;
	}
};
