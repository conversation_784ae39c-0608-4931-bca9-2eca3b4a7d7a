/**
*柜台交易申请查询
*<AUTHOR>
*@date 2018-02-23 14:23
*/
$(function(){
	
	//初始化交易码下拉框
	var selectTxCodeHtml = CommonUtil.selectOptionsHtml(CONSTANTS.COUNTER_TXCODE_MAP);
	$("#selectTxCode").empty();
	$("#selectTxCode").html(selectTxCodeHtml);
	
	//初始化审核下拉框
	var selectCheckFlagHtml = CommonUtil.selectOptionsHtml(CONSTANTS.COUNTER_CHECK_FLAG_MAP);
	$("#selectCheckFlag").html(selectCheckFlagHtml);
	//查询待审核订单
	QueryHighCounterApp.queryOrderInfo();
	
	QueryHighCounterApp.init();
	
});

 var QueryHighCounterApp = {
	init:function(){
		$("#queryBtn").on('click',function(){
			QueryHighCounterApp.queryOrderInfo();
		});
	},
	
	/**
	 * 查询待审核订单信息
	 */
	queryOrderInfo:function(){
		var  uri= TmsCounterConfig.QUERY_CHECK_ORDER_URL  ||  {};
		
		var searchForm = $("#searchCheckForm").serializeObject();
		var reqparamters = {};
		reqparamters.page = 1;
		reqparamters.pageSize = 50;
		
		for(name in searchForm){
			if(!CommonUtil.isEmpty(searchForm[name])){
				reqparamters[name] = searchForm[name];
			}
		}
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxPaging(uri, paramters, QueryHighCounterApp.queryOrderInfoCallBack, "pageView");
	},
	
	queryOrderInfoCallBack:function(data){
		var bodyData = data;
		QueryHighCounterApp.checkOrders = bodyData.counterOrderList || [];
		$("#rsList").empty();
		if(QueryHighCounterApp.checkOrders.length <=0){
			var trHtml = '<tr class="text-c" ><td colspan="9">暂无待审核记录</td></tr>';
			$("#rsList").append(trHtml);
		}
		
		$(QueryHighCounterApp.checkOrders).each(function(index,element){
			var trList = [];
			var viewBtn = '<a id="reCheckView'+element.dealAppNo+'" class="reCheckView btn btn-success radius" href="javascript:void(0);" indexvalue = '+index+'>查看</a>';
            var btn = viewBtn;
			trList.push(btn);
			trList.push(CommonUtil.formatData(element.txAcctNo, '--'));
			trList.push(CommonUtil.formatData(element.custName));
			trList.push(CommonUtil.formatData(element.idNo));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_TXCODE_MAP, element.txCode, ''));
			trList.push(CommonUtil.formatData(element.fundCode));
			trList.push(CommonUtil.formatData(element.fundName));
			trList.push(CommonUtil.formatAmount(element.appAmt));
			trList.push(CommonUtil.formatAmount(element.appVol));
			trList.push(CommonUtil.formatAmount(element.transferPrice));
			trList.push(CommonUtil.formatAmount(element.subsAmt));
			trList.push(CommonUtil.formatAmount(element.totalSubsAmt));
			trList.push(CommonUtil.formatData(element.dealAppNo));
			trList.push(CommonUtil.formatData(element.appDt) +" "+CommonUtil.formatData(element.appTm));
			trList.push(CommonUtil.getMapValue(CONSTANTS.COUNTER_CHECK_FLAG_MAP, element.checkFlag));
			
			var trHtml = '<tr class="text-c"><td>'+trList.join('</td><td>') +'</td></tr>';
			$("#rsList").append(trHtml);
			$("#recheck"+element.dealAppNo).attr("dealAppNo",element.dealAppNo);
			$("#recheck"+element.dealAppNo).attr("txCode",element.txCode);
			$("#reCheckView"+element.dealAppNo).attr("dealAppNo",element.dealAppNo);
			$("#reCheckView"+element.dealAppNo).attr("txCode",element.txCode);
			
		});
		
		//viewType 0-查看；1-审核；2-修改
		//查看
		$(".reCheckView").off();
		$(".reCheckView").on('click',function(){
			var dealAppNo = $(this).attr("dealAppNo");
			var txCode = $(this).attr("txCode");
		
			var params = [];
			params.push('dealAppNo=' + dealAppNo);
			params.push('viewType=0');
			var urlParams = ViewDealCommon.buildParams(params);
			
			var viewUrl = ViewDealCommon.getGetViewUrl(txCode, urlParams);
			ViewDealCommon.showDeal(viewUrl);
		});
			
	}
	
};
