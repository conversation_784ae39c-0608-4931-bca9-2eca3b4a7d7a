/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.fundcontroller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.howbuy.tms.batch.facade.enums.CheckFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.common.enums.database.ProductClassEnum;
import com.howbuy.tms.counter.cmd.OperatorInfoCmd;
import com.howbuy.tms.counter.common.TmsCounterConstant;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.SessionUtil;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.commoncontroller.AbstractController;
import com.howbuy.tms.counter.dto.CounterModifyDivReqDto;
import com.howbuy.tms.counter.dto.CounterModifyDivRespDto;
import com.howbuy.tms.counter.dto.CustInfoDto;
import com.howbuy.tms.counter.dto.FundDivDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import com.howbuy.tms.counter.dto.common.TransactorInfoDto;
import com.howbuy.tms.counter.service.validate.TradeValidateService;
import com.howbuy.tms.counter.util.CommonUtil;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 *
 * @description:(零售修改分红方式)
 * <AUTHOR>
 * @date 2017年9月17日 上午10:45:03
 * @since JDK 1.6
 */
@Controller
public class ModifyFundDivController extends AbstractController {
    private Logger logger = LogManager.getLogger(ModifyFundDivController.class);

    @Autowired
    private TradeValidateService tradeValidateService;

    /***
     *
     * modifyDivConfirm:(修改分红方式)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月17日 上午10:44:50
     */
    @RequestMapping("/tmscounter/fund/modifydivconfirm.htm")
    public ModelAndView modifyDivConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String modifydivConfirmForm = request.getParameter("modifydivConfirmForm");
        String fundDivForm = request.getParameter("fundDiv");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.debug("ModifyFundDivController|modifydivconfirm: modifydivConfirmForm:{}, fundDivForm:{}, custInfoForm:{}, transactorInfoForm:{}",
                modifydivConfirmForm, fundDivForm, custInfoForm, transactorInfoForm);

        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        CounterModifyDivReqDto counterModifyDivReqDto = JSON.parseObject(modifydivConfirmForm, CounterModifyDivReqDto.class);
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        FundDivDto fundDivDto = JSON.parseObject(fundDivForm, FundDivDto.class);

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        counterModifyDivReqDto.setDealAppNo(dealAppNo);
        counterModifyDivReqDto.setTxAcctNo(custInfoDto.getCustNo());
        counterModifyDivReqDto.setDisCode(custInfoDto.getDisCode());
        counterModifyDivReqDto.setCustName(custInfoDto.getCustName());
        counterModifyDivReqDto.setIdNo(custInfoDto.getIdNo());
        counterModifyDivReqDto.setIdType(custInfoDto.getIdType());
        counterModifyDivReqDto.setInvstType(custInfoDto.getInvstType());

//        counterModifyDivReqDto.setAppDt(counterModifyDivReqDto.getAppDt());
//        counterModifyDivReqDto.setAppTm(counterModifyDivReqDto.getAppTm());
//        counterModifyDivReqDto.setFundDivMode(counterModifyDivReqDto.getFundDivMode());

        counterModifyDivReqDto.setFundCode(fundDivDto.getFundCode());
        counterModifyDivReqDto.setFundShareClass(fundDivDto.getShareClass());
        counterModifyDivReqDto.setFundName(fundDivDto.getFundAttr());
        counterModifyDivReqDto.setTaCode(fundDivDto.getTaCode());
        counterModifyDivReqDto.setFundTxAcctNo(fundDivDto.getFundTxAcctNo());
        counterModifyDivReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
        // 普通公募协议
        counterModifyDivReqDto.setProtocolType(ProtocolTypeEnum.DEFAULT_FUND.getCode());
        counterModifyDivReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
        // 普通公募产品
        counterModifyDivReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
        counterModifyDivReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());

        counterModifyDivReqDto.setOutletCode(transactorInfoDto.getOutletCode());
        counterModifyDivReqDto.setConsCode(transactorInfoDto.getConsCode());
        counterModifyDivReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
        counterModifyDivReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
        counterModifyDivReqDto.setTransactorName(transactorInfoDto.getTransactorName());
        counterModifyDivReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
        counterModifyDivReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

        CommonUtil.setCommonOperInfo(operatorInfoCmd, counterModifyDivReqDto);

        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(custInfoDto.getDisCode());
        CounterModifyDivRespDto responseDto = tmsFundCounterService.counterModifyDiv(counterModifyDivReqDto, disInfoDto);
        rst.setBody(responseDto);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     *
     * queryModify:(查询基金分红方式)
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月1日 下午4:10:14
     */
    @RequestMapping("/tmscounter/fund/querymodifydiv.htm")
    public ModelAndView queryModify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custNo = request.getParameter("custNo");
        String fundCode = request.getParameter("fundCode");
        String disCode = request.getParameter("disCode");
        String fundTxAcctNo = request.getParameter("fundTxAcctNo");
        DisInfoDto disInfoDto = new DisInfoDto();
        disInfoDto.setDisCode(disCode);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        CounterModifyDivRespDto counterModifyDivRespDto = tmsFundCounterOutService.queryFundDiv(custNo, fundCode, disInfoDto,fundTxAcctNo);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("fundDivList", counterModifyDivRespDto.getFundDivDtoList());

        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /***
     *
     * 修改分红方式多交易账号
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月17日 上午10:44:50
     */
    @RequestMapping("/tmscounter/fund/modifymultidivconfirm.htm")
    public ModelAndView modifyMultiDivConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        OperatorInfoCmd operatorInfoCmd = (OperatorInfoCmd) SessionUtil.getValue(TmsCounterConstant.SESSION_OPERATORINFO, request);
        String modifydivConfirmForm = request.getParameter("modifydivConfirmForm");
        String fundDivForm = request.getParameter("fundDiv");
        String custInfoForm = request.getParameter("custInfoForm");
        String transactorInfoForm = request.getParameter("transactorInfoForm");
        String dealAppNo = request.getParameter("dealAppNo");
        logger.debug("ModifyFundDivController|modifydivconfirm: modifydivConfirmForm:{}, fundDivForm:{}, custInfoForm:{}, transactorInfoForm:{}",
                modifydivConfirmForm, fundDivForm, custInfoForm, transactorInfoForm);

        CustInfoDto custInfoDto = JSON.parseObject(custInfoForm, CustInfoDto.class);
        List<CounterModifyDivReqDto> counterModifyDivReqDtoList = JSON.parseObject(modifydivConfirmForm, new TypeReference<List<CounterModifyDivReqDto>>(){});
        TransactorInfoDto transactorInfoDto = JSON.parseObject(transactorInfoForm, TransactorInfoDto.class);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);

        // 柜台不支持Y份额基金交易
        tradeValidateService.tradeValidate(counterModifyDivReqDtoList.get(0).getFundCode(),null);

        for (CounterModifyDivReqDto counterModifyDivReqDto:counterModifyDivReqDtoList
             ) {
            counterModifyDivReqDto.setDealAppNo(dealAppNo);
            counterModifyDivReqDto.setTxAcctNo(custInfoDto.getCustNo());
            counterModifyDivReqDto.setDisCode(custInfoDto.getDisCode());
            counterModifyDivReqDto.setCustName(custInfoDto.getCustName());
            counterModifyDivReqDto.setIdNo(custInfoDto.getIdNo());
            counterModifyDivReqDto.setIdType(custInfoDto.getIdType());
            counterModifyDivReqDto.setInvstType(custInfoDto.getInvstType());

            counterModifyDivReqDto.setOperatorNo(operatorInfoCmd.getOperatorNo());
            // 普通公募协议
            counterModifyDivReqDto.setProtocolType(ProtocolTypeEnum.DEFAULT_FUND.getCode());
            counterModifyDivReqDto.setAppFlag(CounterAppFlagEnum.NO_APP.getKey());
            // 普通公募产品
            counterModifyDivReqDto.setProductClass(ProductClassEnum.RETAIL.getCode());
            counterModifyDivReqDto.setCheckFlag(CheckFlagEnum.NOT_CHECKED.getKey());

            counterModifyDivReqDto.setAppDt(transactorInfoDto.getAppDt());
            counterModifyDivReqDto.setAppTm(transactorInfoDto.getAppTm());
            counterModifyDivReqDto.setOutletCode(transactorInfoDto.getOutletCode());
            counterModifyDivReqDto.setConsCode(transactorInfoDto.getConsCode());
            counterModifyDivReqDto.setTransactorIdNo(transactorInfoDto.getTransactorIdNo());
            counterModifyDivReqDto.setTransactorIdType(transactorInfoDto.getTransactorIdType());
            counterModifyDivReqDto.setTransactorName(transactorInfoDto.getTransactorName());
            counterModifyDivReqDto.setAgentFlag(transactorInfoDto.getAgentFlag());
            counterModifyDivReqDto.setMemo(transactorInfoDto.getCheckFaildDesc());

            CommonUtil.setCommonOperInfo(operatorInfoCmd, counterModifyDivReqDto);

            DisInfoDto disInfoDto = new DisInfoDto();
            disInfoDto.setDisCode(custInfoDto.getDisCode());
            CounterModifyDivRespDto responseDto = tmsFundCounterService.counterModifyDiv(counterModifyDivReqDto, disInfoDto);

            rst.setBody(responseDto);
        }


        WebUtil.write(response, rst);
        return null;
    }
}
