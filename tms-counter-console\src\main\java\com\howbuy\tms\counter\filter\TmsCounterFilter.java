package com.howbuy.tms.counter.filter;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.tms.counter.common.Constants;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * 
 * @description:(中台柜台公共请求拦截器)
 * <AUTHOR>
 * @date 2017年3月27日 下午3:17:51
 * @since JDK 1.7
 */
public class TmsCounterFilter extends HandlerInterceptorAdapter {
    private Logger logger = LogManager.getLogger(TmsCounterFilter.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        /*日志处理*/
        processForLog(request);
        //fix无法处理ResourceHttpRequestHandler
        if (!(handler instanceof HandlerMethod)){
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        String methodName = handlerMethod.getMethod().getName();
        String handlerName = handlerMethod.getMethod().getDeclaringClass().getName();
        String targetClassName = handlerName.substring(handlerName.lastIndexOf(".")+1, handlerName.length());
        String requestUrl = request.getRequestURI();
        
        logger.info("requestUrl:{},{}.{}() request: {}", requestUrl,targetClassName, methodName, JSON.toJSONString(getRequestParams(request)));

        HttpSession session = request.getSession();
        if (session == null) {
            response.sendRedirect(request.getContextPath() + "/tmscounter/loginView.htm");
            return false;
        }
        UserAccountModel user = session.getAttribute(Constants.SESSION_USER) == null ? null : (UserAccountModel) session.getAttribute("user");
        if (user == null || StringUtils.isEmpty(user.getUserName())) {
            if (Constants.XML_HTTP_REQUEST.equals(request.getHeader(Constants.X_REQUESTED_WITH))) {
                response.setContentType("application/json;charset=UTF-8");
                String json = "{\"code\":\"301\", \"desc\":\"会话超时请重新登录\"}";
                PrintWriter pw = response.getWriter();
                pw.print(json);
                pw.close();
            } else {
                response.sendRedirect(request.getContextPath() + "/tmscounter/loginView.htm");
            }
            return false;
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        //fix无法处理ResourceHttpRequestHandler
        if (!(handler instanceof HandlerMethod)){
            return;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        String methodName = handlerMethod.getMethod().getName();
        String handlerName = handlerMethod.getMethod().getDeclaringClass().getName();
        String targetClassName = handlerName.substring(handlerName.lastIndexOf(".")+1, handlerName.length());
        logger.info("{}.{}()", targetClassName, methodName);
        Long beginTime = request.getAttribute("request_begin_time") != null ? (Long) request.getAttribute("request_begin_time") : System.currentTimeMillis();
        logger.info("cost:{}", System.currentTimeMillis()-beginTime);
    }

    /**
     * 日志处理
     */
    private void processForLog(HttpServletRequest httpReq) {
        if(httpReq == null){
            return;
        }
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        ThreadContext.put("uuid", requestId);
        httpReq.setAttribute("request_id", requestId);
        httpReq.setAttribute("request_begin_time", System.currentTimeMillis());
    }

    /**
     * 获取所有请求参数
     * @return
     */
    public Map<String, String[]> getRequestParams(HttpServletRequest request) {
        Map<String, String[]> paraMap = new HashMap<String, String[]>(16);
        if(request == null){
            return paraMap;
        }
        return request.getParameterMap();
    }
}
