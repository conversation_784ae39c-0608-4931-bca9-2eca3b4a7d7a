<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">

	<properties>
		<property name="logPath">/data/logs/fincenter</property>
		<property name="sqlLevel">info</property>
	</properties>

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %msg%n" />
		</Console>

		<RollingFile name="RollingFile" filename="${logPath}/fincenter.log"
			filepattern="${logPath}/fincenter-%d{yyyyMMdd}.log">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} [%t] %-5p %c{1}:%L - %msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
	</Appenders>
	
	<Loggers>
	
		<Root level="info">
			<AppenderRef ref="Console" />
			<AppenderRef ref="RollingFile" />
		</Root>
		
		<logger name="com.howbuy.fincenter.dao.mapper.schedule" level="${sqlLevel}" additivity="false">
			<AppenderRef ref="RollingFile" />
		</logger>
		
	</Loggers>
	
</Configuration>