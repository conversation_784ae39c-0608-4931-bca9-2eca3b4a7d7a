/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.QueryRegularProductDto;
import com.howbuy.tms.counter.dto.common.DisInfoDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/***
 * 
 * @description:(产品信息控制器)
 * <AUTHOR>
 * @date 2018年6月21日 上午13:15:39
 * @since JDK 1.6
 */
@Controller
public class RegularProductInfoController extends AbstractController {
    /**
     * 
     * queryRegularProductInfo:(查询定期产品信息)
     * 
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2017年9月15日 上午9:44:13
     */
    @RequestMapping("tmscounter/queryregularproductinfo.htm")
    public void queryRegularProductInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String productId = request.getParameter("productId");
        DisInfoDto disInfoDto = new DisInfoDto();
        String workDay = tmsCounterService.getCounterWorkDay(SysCodeEnum.BATCH_GM.getCode(), disInfoDto);
        QueryRegularProductDto productDto = tmsCounterOutService.queryRegularProductInfo(productId, workDay);
        Map<String, Object> bodyResult = new HashMap<String, Object>(16);
        bodyResult.put("productInfo", productDto);
        TmsCounterResult tmsCounterResult = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        tmsCounterResult.setBody(bodyResult);
        WebUtil.write(response, tmsCounterResult);
    }

}
