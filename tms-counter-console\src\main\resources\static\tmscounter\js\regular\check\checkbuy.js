$(function(){
	Init.init();
	$("#returnBtn").on('click',function(){
		CheckBuy.confirm(RegularCounterCheck.Faild);
	});
	
	$("#succBtn").on('click',function(){
		CheckBuy.confirm(RegularCounterCheck.Succ);
	});
	
	$("#productCode").on('blur',function(){
		QueryProductInfo.queryProductInfo();
	});
	
	var checkId = CommonUtil.getParam("checkId");
	var custNo = CommonUtil.getParam("custNo");
	var disCode = CommonUtil.getParam("disCode");
	var idNo = CommonUtil.getParam("idNo");
	CheckBuy.checkOrder = {};	 
	CheckBuy.init(checkId,custNo,disCode,idNo);
});

var CheckBuy = {	
	init:function(checkId, custNo, disCode,idNo){
		QueryCustInfo.queryCustInfo(custNo, idNo, disCode);
        RegularQueryCheckOrder.queryCheckOrderById(checkId,CheckBuy.queryCheckOrderByIdBack);
		
		$("#applyAmount").blur(function(){
			$("#applyAmount").val(CommonUtil.formatAmount($("#applyAmount").val()));
		});
	}, 
	
	/***
	 * 审核确认
	 */	
	confirm : function(checkStatus){
		if(window.checkedClick == '1'){
			return false;
		}
		//防止重复点击
		window.checkedClick = '1';

		var buyConfirmForm = $("#buyConfirmForm").serializeObject();
		
		var uri= TmsCounterConfig.CHECK_REGULAR_CONFIRM_URL ||  {};
		
		if(RegularCounterCheck.Faild == checkStatus){
			if(CommonUtil.isEmpty($("#checkFaildDesc").val())){
				window.checkedClick = '0';
				CommonUtil.layer_tip("请输入失败原因");
				return false;
			}
			CheckBuy.checkFaildDesc = $("#checkFaildDesc").val();
		}else{
			var validRst = Valid.valiadateFrom($("#buyConfirmForm"));
			if(!validRst.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(validRst.msg);
				return false;
			}

			var checkResultReply = CheckBuy.checkBuyValid(buyConfirmForm,CheckBuy.checkOrder);
			if(!checkResultReply.status){
				window.checkedClick = '0';
				CommonUtil.layer_tip(checkResultReply.tip);
				return false;
			}
		}
		
		var reqparamters ={"checkFaildDesc":CheckBuy.checkFaildDesc || '',"checkStatus":checkStatus,"checkedOrderForm":JSON.stringify(CheckBuy.checkOrder)};
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,"post",null);;
		CommonUtil.ajaxAndCallBack(paramters, CheckBuy.callBack);
		return true;
	},
	callBack:function(data){
		window.checkedClick = '0';
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.layer_tip("成功");
			CommonUtil.disabledBtn("returnBtn");
			CommonUtil.disabledBtn("succBtn");
		}else{
			CommonUtil.layer_tip(respDesc);
		}
	},
	
	checkBuyValid:function(checkForm , orderForm){
		var productCode = checkForm.productId || '';
		var appAmt = CommonUtil.unFormatAmount(checkForm.appAmt) || '';
		var cpAcctNo = checkForm.cpAcctNo || '';
		var discountRate = checkForm.discountRate || '';
		
		var result = {"status":true,"tip":''};
		
		if(productCode != (orderForm.productCode || '')){
			result.status = false;
			result.tip = "产品代码不匹配，请重新确认";
			return result;
		}
		if(appAmt != (orderForm.appAmt || '')){
			result.status = false;
			result.tip = "申请金额不匹配，请重新确认";
			return result;
		}

		var orderCpAcctNo = orderForm.cpAcctNo || '';
		if(cpAcctNo != orderCpAcctNo){
			result.status = false;
			result.tip = "银行卡不匹配，请重新确认";
			return result;
		}
		return result;
		
	},
	
	queryCheckOrderByIdBack:function(data){

		var bodyData = data.body || {};
		CheckBuy.checkOrder = bodyData.checkOrder || {};
				
		if(CommonUtil.isEmpty(CheckBuy.checkOrder.dealAppNo)){
			CommonUtil.layer_tip("无此订单");
			return false;
		}
		
		if(CheckBuy.checkOrder.checkFlag != 0){
			CommonUtil.layer_tip("该订单已审核完成");
			return false;
		}
		
		QueryProductInfo.queryProductInfo(CheckBuy.checkOrder.productCode,false);

		if($("#selectBank").length > 0){
			$("#selectBank").val(CheckBuy.checkOrder.cpAcctNo);
		}
		
		if($("#productRiskLevel").length > 0){
			$("#productRiskLevel").html(CommonUtil.getMapValue(CONSTANTS.RISK_FLAG_MAP, CheckBuy.checkOrder.riskFlag, ''));
		}
		
		/**other*/
		if($("#appDt").length > 0){
			$("#appDt").html(CheckBuy.checkOrder.appDt);
		}
		/*设置产品代码*/
        $("#productId").val(CheckBuy.checkOrder.productCode);
		/*设置金额*/
        $("#applyAmount").val(CheckBuy.checkOrder.appAmt);
        $("#applyAmountCapital").val(Main.format(CommonUtil.formatAmount(CheckBuy.checkOrder.appAmt)));

		if($("#appTm").length > 0){
			$("#appTm").html(CheckBuy.checkOrder.appTm);
		}
		
		if($("#consCode").length > 0){
			$("#consCode").html(CommonUtil.getMapValue(ConsCode.consCodesMap, CheckBuy.checkOrder.consCode, ''));
		}
		
		if($("#transactorName").length > 0){
			$("#transactorName").html(CheckBuy.checkOrder.transactorName);
		}
		
		
		if($("#transactorIdType").length > 0){
			$("#transactorIdType").html(CommonUtil.getMapValue(CONSTANTS.ID_TYPE_MAP, CheckBuy.checkOrder.transactorIdType, ''));
		}
		
		if($("#transactorIdNo").length > 0){
			$("#transactorIdNo").html(CheckBuy.checkOrder.transactorIdNo);
		}
		
		if($("#checkFaildDesc").length > 0){
			$("#checkFaildDesc").val(CheckBuy.checkOrder.memo);
		}
	},
}
