.ui-multiselect { padding:2px 0 2px 4px; text-align:left }
.ui-multiselect span.ui-icon { float:right }
.ui-multiselect-single .ui-multiselect-checkboxes input { left:-9999px; position:absolute !important; top: auto !important; }
.ui-multiselect-single .ui-multiselect-checkboxes label { padding:5px !important }

.ui-multiselect-header { margin-bottom:3px; padding:3px 0 3px 4px; }
.ui-multiselect-header ul { font-size:0.9em }
.ui-multiselect-header ul li { float:left; padding:0 10px 0 0; }
.ui-multiselect-header a { text-decoration:none; }
.ui-multiselect-header a:hover { text-decoration:underline; }
.ui-multiselect-header span.ui-icon { float:left; }
.ui-multiselect-header .ui-multiselect-close { float:right; padding-right:0; text-align:right; }

.ui-multiselect-menu { display:none; padding:3px; position:absolute; text-align: left; }
.ui-multiselect-checkboxes { overflow-y:auto; position:relative; }
.ui-multiselect-checkboxes label { border:1px solid transparent; cursor:default; display:block; padding:3px 1px; }
.ui-multiselect-checkboxes label input { position:relative; top:1px }
.ui-multiselect-checkboxes label img { height: 30px; vertical-align: middle; padding-right: 3px;}
.ui-multiselect-checkboxes li { clear:both; font-size:0.9em; list-style: none; padding-right:3px; }
.ui-multiselect-checkboxes .ui-multiselect-optgroup { padding: 3px; }
.ui-multiselect-columns { display: inline-block; vertical-align: top; }
.ui-multiselect-checkboxes .ui-multiselect-optgroup a { border-bottom:1px solid; cursor: pointer; display:block; font-weight:bold; margin:1px 0; padding:3px; text-align:center; text-decoration:none; }

@media print{
    .ui-multiselect-menu {display: none;}
}
