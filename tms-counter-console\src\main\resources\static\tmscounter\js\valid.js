/**
*校验
*<AUTHOR>
*@date 2017-04-12 16:12
*/

BuyValid={
		 /* 校验客户风险等级
		 * @param custRisk
		 * @param fundRisk
		 */
		validRisk:function(custRisk,fundRisk){
			if(0 == custRisk && 0 == fundRisk){
				return false;
			}
			return fundRisk > custRisk;
		},
		/**
		 * 校验折扣率范围0到1
		 * @param disCount
		 * @returns {Boolean}
		 */
		validDisCount:function(disCount){
			if($.isNumeric(disCount)){
				if(disCount<=1 && disCount >=0){
					return true;;
				}
			}
			return false;
		},
		/**
		 * 校验是否是合格的投资用户
		 * @param signFlag
		 */
		validSignFlag:function(signFlag){
			return '1' != signFlag;
		}
}

var Valid = {
		
		/**
		 * 校验时间是否在所选区间
		 * @param timeStr  时间
		 * @param startTimeStr 开始时间
		 * @param emdTimeStr   结束时间
		 */
		valiadTimeInterval:function(timeStr,startTimeStr,endTimeStr){
			return (timeStr>=startTimeStr && timeStr <=endTimeStr);
		},
		
		/**
		 * 校验柜台可校验时间区间
		 * @param timeStr
		 */
		valiadTradeTime:function(timeStr){
			return Valid.valiadTimeInterval(timeStr, CONSTANTS.HIGH_COUNTER_START_TIME , CONSTANTS.HIGH_COUNTER_END_TIME);
		},

		/**
		 * 高端校验柜台可校验时间区间
		 * @param timeStr
		 */
		highValiadTradeTime:function(timeStr){
			return Valid.valiadTimeInterval(timeStr, CONSTANTS.HIGH_COUNTER_START_TIME_NEW , CONSTANTS.HIGH_COUNTER_END_TIME);
		},
		
		valiadateFrom:function(obj){
			var rst = {};
			rst.status = true;
			obj.find("input,select").each(function(index,element){
				var tips = Valid.formElementValid(element);
				if(!isEmpty(tips)){
					rst.status = false;
					rst.msg = tips;
					return false;
				}
			});
			
			return rst;
		},
		
		/**
		 * 表单交易
		 * @returns {Boolean}
		 */
		formElementValid:function(formObj){
			var isnull = $(formObj).attr("isnull");
			var datatype = $(formObj).attr("datatype") || "s";
			var errormsg = $(formObj).attr("errormsg");
			var value = $(formObj).val();
			var tips = '';
			if(isnull == "false"){
				if(isEmpty(value)){
					if($(formObj).is('input')){
						tips = errormsg+"不能为空";
						return tips;
					}else if($(formObj).is('select')){
						tips = "请选择"+errormsg;
						return tips;
					}
				}
			}
			switch(datatype){
			case "s":
				break;
			case "n":
				return tips;
				break;
			case  "time":
			     if(!CommonUtil.timeValid(value)){
			    	 tips = errormsg +"格式不正确";
			    	 return tips;
			     }
			     break;
			default:
				return tips;
				break;
			}
			
		},
		id_rule:function(idcard) {
			var Errors = new Array(true, false, false, false, false);
			var area = {
				11 :"北京",
				12 :"天津",
				13 :"河北",
				14 :"山西",
				15 :"内蒙古",
				21 :"辽宁",
				22 :"吉林",
				23 :"黑龙江",
				31 :"上海",
				32 :"江苏",
				33 :"浙江",
				34 :"安徽",
				35 :"福建",
				36 :"江西",
				37 :"山东",
				41 :"河南",
				42 :"湖北",
				43 :"湖南",
				44 :"广东",
				45 :"广西",
				46 :"海南",
				50 :"重庆",
				51 :"四川",
				52 :"贵州",
				53 :"云南",
				54 :"西藏",
				61 :"陕西",
				62 :"甘肃",
				63 :"青海",
				64 :"宁夏",
				65 :"新疆",
				71 :"台湾",
				81 :"香港",
				82 :"澳门",
				91 :"国外"
			}
			var idcard, Y, JYM;
			var S, M;
			var idcard_array = new Array();
			idcard_array = idcard.split("");
			//地区检验
			if (area[parseInt(idcard.substr(0, 2))] == null)
				return Errors[4];
			//身份号码位数及格式检验
			switch (idcard.length) {
			//case 15:
			//	if ((parseInt(idcard.substr(6, 2)) + 1900) % 4 == 0
			//			|| ((parseInt(idcard.substr(6, 2)) + 1900) % 100 == 0 && (parseInt(idcard
			//					.substr(6, 2)) + 1900) % 4 == 0)) {
			////		ereg = /^[1-9][0-9]{5}[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}$/;//测试出生日期的合法性
			//	} else {
			//		ereg = /^[1-9][0-9]{5}[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}$/;//测试出生日期的合法性
			//	}
			//	if (ereg.test(idcard))
				//	return Errors[0];
			//	else
				//	return Errors[2];
			//	break;
			case 18:
				//18位身份号码检测
				//出生日期的合法性检查
				//闰年月日:((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))
				//平年月日:((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))
				if (parseInt(idcard.substr(6, 4)) % 4 == 0
						|| (parseInt(idcard.substr(6, 4)) % 100 == 0 && parseInt(idcard
								.substr(6, 4)) % 4 == 0)) {
					ereg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}[0-9Xx]$/;//闰年出生日期的合法性正则表达式
				} else {
					ereg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}[0-9Xx]$/;//平年出生日期的合法性正则表达式
				}
				if (ereg.test(idcard)) {//测试出生日期的合法性
					//计算校验位
					S = (parseInt(idcard_array[0]) + parseInt(idcard_array[10]))
							* 7
							+ (parseInt(idcard_array[1]) + parseInt(idcard_array[11]))
							* 9
							+ (parseInt(idcard_array[2]) + parseInt(idcard_array[12]))
							* 10
							+ (parseInt(idcard_array[3]) + parseInt(idcard_array[13]))
							* 5
							+ (parseInt(idcard_array[4]) + parseInt(idcard_array[14]))
							* 8
							+ (parseInt(idcard_array[5]) + parseInt(idcard_array[15]))
							* 4
							+ (parseInt(idcard_array[6]) + parseInt(idcard_array[16]))
							* 2 + parseInt(idcard_array[7]) * 1
							+ parseInt(idcard_array[8]) * 6
							+ parseInt(idcard_array[9]) * 3;
					Y = S % 11;
					M = "F";
					JYM = "10X98765432";
					M = JYM.substr(Y, 1);//判断校验位
					if (M == idcard_array[17]){
						return true; //检测ID的校验位
					}
					else {
						return false;
					}
				} else {
					return false;
				}
				break;
			default:
				return false;
				break;
			}
		}

		
}