/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.counter.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.batch.facade.enums.CounterAppFlagEnum;
import com.howbuy.tms.batch.facade.enums.CounterCheckFlagEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.database.PaymentTypeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.util.WebUtil;
import com.howbuy.tms.counter.dto.CounterOrderDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderReqDto;
import com.howbuy.tms.counter.dto.CounterQueryOrderRespDto;
import com.howbuy.tms.counter.service.trade.TmsCounterService;
import com.howbuy.tms.high.orders.facade.search.queryhzsubscribeamtinfo.QueryHzSubscribeAmtInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryhzsubscribeamtinfo.QueryHzSubscribeAmtInfoRequest;
import com.howbuy.tms.high.orders.facade.search.queryhzsubscribeamtinfo.QueryHzSubscribeAmtInfoResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

/**
 * @description:(柜台订单查询控制器) 
 * <AUTHOR>
 * @date 2017年3月27日 下午4:37:19
 * @since JDK 1.7
 */
@Controller
public class QueryController {
    private Logger logger = LogManager.getLogger(QueryController.class);
    @Autowired
    private TmsCounterService  tmsCounterService;
    @Autowired
    @Qualifier("queryAllCustInfoOuterService")
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    @Autowired
    private QueryHzSubscribeAmtInfoFacade queryHzSubscribeAmtInfoFacade;

    /**
     * 
     * queryCounterTrade:(查询)
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * @throws Exception 
     * @date 2017年3月28日 上午10:26:58
     */
    @RequestMapping("/tmscounter/querycountertrade.htm")
    public ModelAndView queryCounterTrade(HttpServletRequest request,HttpServletResponse response) throws Exception{
        String queryOrderCmd = request.getParameter("queryConditonForm");
        String pageNum = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        String queryType = request.getParameter("queryType");
        logger.info("pageNum:{},pageSize:{}",pageNum,pageSize);
        if(StringUtils.isEmpty(pageNum)){
            pageNum = "1";
        }
        if(StringUtils.isEmpty(pageSize)){
            pageSize = "50";
        }
        CounterQueryOrderReqDto counterQueryOrderReqDto  = JSON.parseObject(queryOrderCmd, CounterQueryOrderReqDto.class);
        counterQueryOrderReqDto.setPageNo(Integer.parseInt(pageNum));
        counterQueryOrderReqDto.setPageSize(Integer.parseInt(pageSize));
        CounterQueryOrderRespDto responseDto = tmsCounterService.counterQueryOrder(counterQueryOrderReqDto,null);
        // 脱敏
        if (!org.springframework.util.CollectionUtils.isEmpty(responseDto.getCounterOrderList())) {
            for (CounterOrderDto dto : responseDto.getCounterOrderList()) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }
        // 明细
        if ("1".equals(queryType)) {
            getCustInfoAndBankAcct(responseDto.getCounterOrderList());
        }
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("pageNum", responseDto.getPageNo());
        body.put("totalPage", responseDto.getTotalPage());
        body.put("rsList",responseDto.getCounterOrderList());
        rst.setBody(body);
        WebUtil.write(response, rst);
        return null;
    }

    /**
     * @api {GET} /tmscounter/queryHzSubscribeAmtInfo.htm queryHzSubscribeAmtInfo
     * @apiVersion 1.0.0
     * @apiGroup QueryController
     * @apiName queryHzSubscribeAmtInfo
     * @apiDescription 查询认缴金额信息
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping("tmscounter/queryHzSubscribeAmtInfo.htm")
    public void queryHzSubscribeAmtInfo(HttpServletRequest request, HttpServletResponse response) throws Exception{
        String fundCode = request.getParameter("fundCode");
        String txAcctNo = request.getParameter("txAcctNo");
        if(StringUtils.isBlank(fundCode)||StringUtils.isBlank(txAcctNo)){
            logger.info("queryHzSubscribeAmtInfo,查询认缴信息,没有产品或者交易账号,就不查了 fundCode:{},txAcctNo={}",fundCode,txAcctNo);
            TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
            Map<String, Object> body = new HashMap<String, Object>(1);
            rst.setBody(body);
            WebUtil.write(response, rst);
            return;
        }
        QueryHzSubscribeAmtInfoRequest queryHzSubscribeAmtInfoRequest = new QueryHzSubscribeAmtInfoRequest();
        queryHzSubscribeAmtInfoRequest.setFundCode(fundCode);
        queryHzSubscribeAmtInfoRequest.setTxAcctNo(txAcctNo);
        queryHzSubscribeAmtInfoRequest.setDisCode(DisCodeEnum.HZ.getCode());
        queryHzSubscribeAmtInfoRequest.setTxChannel(TxChannelEnum.COUNTER.getCode());
        queryHzSubscribeAmtInfoRequest.setOutletCode("counter");
        queryHzSubscribeAmtInfoRequest.setOperIp(com.howbuy.web.util.WebUtil.getCustIP(request));
        QueryHzSubscribeAmtInfoResponse queryHzSubscribeAmtInfoResponse = queryHzSubscribeAmtInfoFacade.execute(queryHzSubscribeAmtInfoRequest);
        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(1);
        body.put("hzSubscribeAmtInfo", queryHzSubscribeAmtInfoResponse);
        rst.setBody(body);
        WebUtil.write(response, rst);
    }

    private void getCustInfoAndBankAcct(List<CounterOrderDto> list){
        if(org.springframework.util.CollectionUtils.isEmpty(list)){
            return;
        }
        //考虑到不要多次查询接口 原有对象的数据 key=txAcctNo
        Map<String,String> cpNoAndBankAcctMap = new HashMap<String,String>(16);
        for(CounterOrderDto bean : list){
            String bankAcct = cpNoAndBankAcctMap.get(bean.getCpAcctNo());
            if(StringUtils.isEmpty(bankAcct)){
                try {
                    QueryAllCustInfoContext queryAllCustInfoContext = new QueryAllCustInfoContext();
                    queryAllCustInfoContext.setDisCode(bean.getDisCode());
                    queryAllCustInfoContext.setTxAcctNo(bean.getTxAcctNo());
                    queryAllCustInfoContext.setCpAcctNo(bean.getCpAcctNo());
                    QueryAllCustInfoResult queryAllCustInfoResult = queryAllCustInfoOuterService.queryCustInfoPlaintext(queryAllCustInfoContext);
                    if(queryAllCustInfoResult != null){
                        bean.setBankAcct(queryAllCustInfoResult.getCustBankCardInfo().getBankAcct());
                        bean.setIdNo(queryAllCustInfoResult.getCustInfo().getIdNo());
                        bean.setCustName(queryAllCustInfoResult.getCustInfo().getCustName());
                        cpNoAndBankAcctMap.put(bean.getCpAcctNo(), queryAllCustInfoResult.getCustBankCardInfo().getBankAcct());
                    }
                } catch (Exception e) {
                    logger.error("QueryBusiController|queryBusiService.queryCustBankCard error.",e);
                }
            }else{
                bean.setBankAcct(bankAcct);
            }
        }
    }
    
    /**
     * 下载柜台交易查询结果
     * queryCounterTradeDown
     * @param request
     * @param response
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年4月13日 下午5:50:04
     */
    @RequestMapping("/tmscounter/querycountertradedown.htm")
    public ModelAndView queryCounterTradeDown(HttpServletRequest request,HttpServletResponse response) throws Exception{
        String queryOrderCmd = request.getParameter("queryConditonForm");
        CounterQueryOrderReqDto counterQueryOrderReqDto  = JSON.parseObject(queryOrderCmd, CounterQueryOrderReqDto.class);
        counterQueryOrderReqDto.setPageSize(0);
        CounterQueryOrderRespDto responseDto = tmsCounterService.counterQueryOrder(counterQueryOrderReqDto,null);
        // 脱敏
        if (!org.springframework.util.CollectionUtils.isEmpty(responseDto.getCounterOrderList())) {
            for (CounterOrderDto dto : responseDto.getCounterOrderList()) {
                PrivacyUtil.resetCustInfoAndBankInfo(dto);
            }
        }

        String fileName =  writeTradeListToFile(TITLES,responseDto.getCounterOrderList());
        logger.info("filename :{}",fileName);
        TmsCounterResult result = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String,Object> body = new HashMap<String,Object>(16);
        body.put("fileName",fileName);
        result.setBody(body);
        WebUtil.write(response, result);
        return null;
    }

    private static final String[] TITLES = {
            "客户号",
            "客户名称",
            "基金代码",
            "基金简称",
            "业务类型",
            "柜台订单号",
            "中台订单号",
            "申请金额",
            "申请份额",
            "银行卡",
            "支付方式",
            "失败原因",
            "审核状态",
            "申请状态",
            "申请时间",
            "操作员"
    };
    
    @SuppressWarnings("resource")
    public  String  writeTradeListToFile(String[] titles ,List<CounterOrderDto> list) throws Exception{  
        String fileName = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD)+"柜台交易详情.xls";
        Workbook workbook = null  ;  
        workbook = new HSSFWorkbook();  
        Sheet sheet = workbook.createSheet();
        int rowIndex = 0;  
        if(titles != null && titles.length >0){
            Row row = sheet.createRow(rowIndex++);  
            for(int i = 0 ;i<titles.length;i++){
                Cell cell = row.createCell(i);
                cell.setCellValue(titles[i]);
            }
        }
        if(!CollectionUtils.isEmpty(list)){
            Iterator<CounterOrderDto> iterator = list.iterator();  
           
            while(iterator.hasNext()){  
                CounterOrderDto order = iterator.next();  
                Row row = sheet.createRow(rowIndex++);  
                Cell cell0 = row.createCell(0);  
                cell0.setCellValue(order.getTxAcctNo());
                Cell cell1 = row.createCell(1);  
                cell1.setCellValue(order.getCustName());
                Cell cell2 = row.createCell(2);  
                cell2.setCellValue(order.getFundCode());
                Cell cell3 = row.createCell(3);  
                cell3.setCellValue(order.getFundName());
                Cell cell4 = row.createCell(4);  
                cell4.setCellValue(order.getTxCode());
                Cell cell5 = row.createCell(5);  
                cell5.setCellValue(order.getDealAppNo());
                Cell cell6 = row.createCell(6);  
                cell6.setCellValue(order.getDealNo());
                
                Cell cell7 = row.createCell(7);
                cell7.setCellValue(order.getAppAmt() == null ? "" : String.valueOf(order.getAppAmt()));
                
                Cell cell8 = row.createCell(8);
                cell8.setCellValue(order.getAppVol() == null ? "" : String.valueOf(order.getAppVol()));
                
                Cell cell9 = row.createCell(9);
                cell9.setCellValue(order.getBankAcct());
                
                Cell cell10 = row.createCell(10);
                String paymentTypeName = PaymentTypeEnum.getPaymentType(order.getPaymentType()) == null ? "" : PaymentTypeEnum.getPaymentType(order.getPaymentType()).getName();
                cell10.setCellValue(paymentTypeName);
                
                Cell cell11 = row.createCell(11);
                cell11.setCellValue(order.getMemo());
                
                Cell cell12 = row.createCell(12);  
                CounterCheckFlagEnum counterCheckFlag =  CounterCheckFlagEnum.getCounterCheckFlag(order.getCheckFlag());
                String checkFlagName = counterCheckFlag == null ? "" : counterCheckFlag.getDesc();
                cell12.setCellValue(checkFlagName);
                
                
                CounterAppFlagEnum counterAppFlag = CounterAppFlagEnum.getCounterAppFlag(order.getAppFlag());
                String counterAppFlagName = counterAppFlag == null ? "" :  counterAppFlag.getDesc();
                
                Cell cell13 = row.createCell(13);  
                cell13.setCellValue(counterAppFlagName);
                Cell cell14= row.createCell(14); 
                StringBuilder appDtm = new StringBuilder();
                if(!StringUtils.isEmpty(order.getAppDt())){
                    appDtm.append(order.getAppDt()).append(" ");
                }
                
                if(!StringUtils.isEmpty(order.getAppTm())){
                    appDtm.append(order.getAppTm());
                }
                cell14.setCellValue(appDtm.toString());
                
                Cell cell15 = row.createCell(15);  
                cell15.setCellValue(order.getCreator());
            }  
        }
        
        File file = new File(fileName);
        FileOutputStream fw = new FileOutputStream(fileName);
        workbook.write(fw);  
        fw.close();  
        
        return file.getAbsolutePath();
    }  
    
 
   
}

