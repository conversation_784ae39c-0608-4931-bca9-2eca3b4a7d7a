/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.dto;

import com.howbuy.tms.counter.dto.base.BaseResponseDto;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @description:(查询预约列表)
 * @reason:TODO ADD REASON(可选)
 * <AUTHOR>
 * @date 2018年1月4日 上午9:22:49
 * @since JDK 1.6
 */
public class CustomerPreInfoRespDto extends BaseResponseDto {

    /**
     * serialVersionUID:
     *
     * @since Ver 1.1
     */

    private static final long serialVersionUID = 4870246973212134585L;

    private List<CustomerPreInfoDto> customerPreInfoDtoList;

    public List<CustomerPreInfoDto> getCustomerPreInfoDtoList() {
        return customerPreInfoDtoList;
    }

    public void setCustomerPreInfoDtoList(List<CustomerPreInfoDto> customerPreInfoDtoList) {
        this.customerPreInfoDtoList = customerPreInfoDtoList;
    }

    public static class CustomerPreInfoDto implements Serializable {
        /**
         * serialVersionUID:
         *
         * @since Ver 1.1
         */

        private static final long serialVersionUID = 1L;
        /**
         * 预约id
         */
        private String appointId;
        /**
         * 投资者类别 1-个人，0-机构
         */
        private String custType;
        /**
         * 一账通账号
         */
        private String hbOneNo;
        /**
         * 客户号
         */
        private String custNo;
        /**
         * 客户名称
         */
        private String custName;
        /**
         * 预约产品代码
         */
        private String productCode;

        /**
         * 预约产品名称
         */
        private String productName;
        /**
         * 银行卡号
         */
        private String bankAcctNo;
        /**
         * 订单号
         */
        private String orderId;
        /**
         * 证件类型
         */
        private String idType;
        /**
         * 证件号
         */
        private String idNo;
        /**
         * 预约单状态
         */
        private String preBookState;
        /**
         * 无纸化状态
         */
        private String noPaperState;
        /**
         * 手续费
         */
        private String fee;
        /***
         * 预约金额
         */
        private BigDecimal appAmt;
        /***
         * 预约份额
         */
        private BigDecimal appVol;
        /***
         * 预约折扣
         */
        private BigDecimal discountRate;
        /***
         * 预约单状态
         */
        private String orderStatus;
        /**
         * 基金风险等级
         */
        private String fundRiskLevel;
        /**
         * 基金简称
         */
        private String fundAttr;
        /**
         * 基金状态
         */
        private String fundStatus;
        /**
         * 预约业务
         */
        private String mBusiCode;
        /***
         * 预约日期
         */
        private String appointStartDt;
        /***
         * 预约时间
         */
        private String appointStartTm;

        /**
         * 持有份额 / 总份额
         */
        private BigDecimal balanceVol;
        /***
         * 可用份额
         */
        private BigDecimal availVol;
        /**
         * 可购买状态
         */
        private String buyStatus;
        /**
         * 预约类型
         */
        private String preType;

        public String getAppointId() {
            return appointId;
        }

        public void setAppointId(String appointId) {
            this.appointId = appointId;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public BigDecimal getAppAmt() {
            return appAmt;
        }

        public void setAppAmt(BigDecimal appAmt) {
            this.appAmt = appAmt;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public BigDecimal getDiscountRate() {
            return discountRate;
        }

        public void setDiscountRate(BigDecimal discountRate) {
            this.discountRate = discountRate;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getFundRiskLevel() {
            return fundRiskLevel;
        }

        public void setFundRiskLevel(String fundRiskLevel) {
            this.fundRiskLevel = fundRiskLevel;
        }

        public String getFundAttr() {
            return fundAttr;
        }

        public void setFundAttr(String fundAttr) {
            this.fundAttr = fundAttr;
        }

        public String getFundStatus() {
            return fundStatus;
        }

        public void setFundStatus(String fundStatus) {
            this.fundStatus = fundStatus;
        }

        public String getmBusiCode() {
            return mBusiCode;
        }

        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }

        public String getAppointStartDt() {
            return appointStartDt;
        }

        public void setAppointStartDt(String appointStartDt) {
            this.appointStartDt = appointStartDt;
        }

        public String getAppointStartTm() {
            return appointStartTm;
        }

        public void setAppointStartTm(String appointStartTm) {
            this.appointStartTm = appointStartTm;
        }

        public BigDecimal getBalanceVol() {
            return balanceVol;
        }

        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }

        public BigDecimal getAvailVol() {
            return availVol;
        }

        public void setAvailVol(BigDecimal availVol) {
            this.availVol = availVol;
        }

        public String getBuyStatus() {
            return buyStatus;
        }

        public void setBuyStatus(String buyStatus) {
            this.buyStatus = buyStatus;
        }

        public String getPreType() {
            return preType;
        }

        public void setPreType(String preType) {
            this.preType = preType;
        }

        public String getCustType() {
            return custType;
        }

        public void setCustType(String custType) {
            this.custType = custType;
        }

        public String getHbOneNo() {
            return hbOneNo;
        }

        public void setHbOneNo(String hbOneNo) {
            this.hbOneNo = hbOneNo;
        }

        public String getCustNo() {
            return custNo;
        }

        public void setCustNo(String custNo) {
            this.custNo = custNo;
        }

        public String getCustName() {
            return custName;
        }

        public void setCustName(String custName) {
            this.custName = custName;
        }

        public String getBankAcctNo() {
            return bankAcctNo;
        }

        public void setBankAcctNo(String bankAcctNo) {
            this.bankAcctNo = bankAcctNo;
        }

        public String getOrderId() {
            return orderId;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public String getIdType() {
            return idType;
        }

        public void setIdType(String idType) {
            this.idType = idType;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getPreBookState() {
            return preBookState;
        }

        public void setPreBookState(String preBookState) {
            this.preBookState = preBookState;
        }

        public String getNoPaperState() {
            return noPaperState;
        }

        public void setNoPaperState(String noPaperState) {
            this.noPaperState = noPaperState;
        }

        public String getFee() {
            return fee;
        }

        public void setFee(String fee) {
            this.fee = fee;
        }
    }
}
