/**
* 转托管转出
* <AUTHOR>
* @date 2018-10-09 14:02
*/
$(function(){
	var operatorNo = cookie.get("operatorNo");
	Init.init();
	TransfertubeOut.init();
	TransfertubeOut.currDate = '';
	// 账户对应关系是否存在
	TransfertubeOut.accountRelationFlag = '';
	// 起始时间
	TransfertubeOut.startTime = '';
	// 终止时间
	TransfertubeOut.endTime = '';
});

var TransfertubeOut = {
	init:function(){
		/**
		 * 确认转托管转出
		 */
		var formAction = $("#transfertubeOutForm").attr("action");
		if(formAction == 'add'){
			$("#confimTransOutBtn").on('click',function(){
				TransfertubeOut.confirm(formAction, null, null);
			});
		}
		
		/**
		 * 双击客户号查询客户信息
		 */
		$("#custNo").on('dblclick',function(){
			QueryCustInfoSubPage.selectCustNo($(this));
		});
		
		/**
		 * 查询客户基本信息
		 */
		$("#queryCustInfoBtn").on('click',function(){
			QueryCustInfo.queryCustInfo();
			// 查询客户基金持仓
			TransfertubeOut.queryAllCustBalInfo();
		});

		// 全选
		$("#selectAll").on('change', function () {
			TransfertubeOut.selectAll();
		});


		/**
		 * 查询基金信息和基金持仓
		 */
		$(".searchIcon").on('click',function(){
			QueryFundInfo.queryFundInfo();

			setTimeout(function(){
				// 查询客户基金持仓
				TransfertubeOut.queryCustBalInfo();
			}, 300);
			
		});	

		/**
		 * 业务类型与对方销售人代码联动
		 */
		$(".selectTransferTubeBusiType").change(function(){
			var transferTubeBusiType = $(this).val();
			var $containers = $(".sellerCodeContainer"); // 使用 class 选择器

			if (transferTubeBusiType === '1') {
				// 显示下拉框
				$containers.html(`
                <select name="tSellerCode" class="select"  id="tSellerCode">
                	<option value="">请选择</option>
                    <option value="101">101-上海</option>
                    <option value="102">102-深圳</option>
                </select>
            `);
			} else {
				// 显示输入框
				$containers.html(`
                <input type="text" placeholder="请输入" name="tSellerCode"   id="tSellerCode" isnull="false" datatype="s" errormsg="对方销售人代码">
            `);
			}
		});
	},
	
	/**
	 * 查询客户基金持仓
	 */
	queryCustBalInfo:function(){
		var custNo = $("#custNo").val();
		var idNo = $("#idNo").val();
		if(CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo)){
			CommonUtil.layer_tip("请先输入客户号或证件号");
			return false;
		}
		
		var fundCode = $("#fundCode").val();
		if(CommonUtil.isEmpty(fundCode)){
			CommonUtil.layer_tip("请输入基金代码");
			return false;
		}
		
		var searchDisCode = $("#selectDisCode").val();
		if(CommonUtil.isEmpty(searchDisCode)){
			CommonUtil.layer_tip("请选择分销机构");
			return false;
		}
		
		// 查询客户基金持仓
		TransfertubeOut.queryTransferOutHodlInfo(custNo, idNo, searchDisCode, fundCode);
	},

	/**
	 * 查询客户基金持仓
	 */
	queryAllCustBalInfo:function(){
		var custNo = $("#custNo").val();
		var idNo = $("#idNo").val();
		if(CommonUtil.isEmpty(custNo) && CommonUtil.isEmpty(idNo)){
			CommonUtil.layer_tip("请先输入客户号或证件号");
			return false;
		}

		var searchDisCode = $("#selectDisCode").val();
		if(CommonUtil.isEmpty(searchDisCode)){
			CommonUtil.layer_tip("请选择分销机构");
			return false;
		}

		// 查询客户基金持仓
		TransfertubeOut.queryTransferOutHodlInfo(custNo, idNo, searchDisCode, null);
	},
	// 全选
	selectAll: function () {
		var selectBox = $("input[class='selectTransOutCustBal'][type='checkbox']");
		if($("input[id='selectAll'][type='checkbox']:checked").length == 1){
			selectBox.each(function(index,element){
				$(element).prop("checked",true);
			});
		}else{
			selectBox.each(function(index,element){
				$(element).prop("checked",false);
			});
		}
	},
	
	/**
	 * 查询客户转托管转出基金持仓
	 */
	queryTransferOutHodlInfo:function(custNo, idNo, disCode, fundCode){
		var  uri= TmsCounterConfig.QUERY_TRANS_OUT_FUND_CUST_HODL_INFO_URL ||  {};
		var reqparamters = {};
		reqparamters.custNo = custNo;
		reqparamters.idNo = idNo;
		reqparamters.disCode = disCode;
		reqparamters.fundCode = fundCode;
		
		var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
		CommonUtil.ajaxAndCallBack(paramters, TransfertubeOut.queryVolBalCallBack);
	},

	queryVolBalCallBack:function(data){
		var bodyData = data.body || {};
		var respData = bodyData.respData || [];
		var fundInfo = bodyData.fundInfo || {};
		// 查询设置基金信息
		//QueryFundInfo.queryFundInfoCallBack(data);
		
		// 持仓信息
		TransfertubeOut.custBalDtlList = respData.balanceDtlList || [];
		
		// 转出信息
		$("#transOutCustBals").empty();
		if(TransfertubeOut.custBalDtlList.length <=0){
			var trHtml = '<tr><td colspan="14">没有查询到可以转出份额信息</td></tr>';
			$("#transOutCustBals").append(trHtml);
			return false;
			
		}else{
			var totalOutVol = 0;// 总份额
			$(TransfertubeOut.custBalDtlList).each(function(index,element){
				totalOutVol = CommonUtil.numAdd(totalOutVol, element.balanceVol);
				
				var tdList = [];
				tdList.push('<td><input class="selectTransOutCustBal" id="selectTransOutCustBal_'+index+'" name="checkTransOutBal"  type="checkbox"  value= ' + JSON.stringify(element) + '  data-index="' + index + '"></input></td>');
				tdList.push('<td>'+CommonUtil.formatData(element.productCode)+'</td>');
				tdList.push('<td>'+CommonUtil.formatData(element.productName)+'</td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.availVol)+'</td>');
				tdList.push('<td>'+CommonUtil.formatAmount(element.unconfirmedVol)+'</td>');
				
				// 转出份额
				tdList.push('<td><input type="text" class="outVolClass" name="outVol_'+index+'" id="outVol_'+index+'" isnull="false" datatype="s" errormsg="转出份额" placeholder="请输入" onkeyup="TransfertubeOut.validatorOutVol('+index+','+element.availVol+', this);"></td>');
				tdList.push('<td><input type="text" name="outVolCapital_'+index+'" id="outVolCapital_'+index+'" isnull="false" datatype="s" errormsg="转出份额" readonly="true"></td>');
				
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.COUNTEE_PROTOCOL_TYPE_MAP, element.protocolType)+'</td>');
				tdList.push('<td>'+CommonUtil.formatData(element.protocolNo)+'</td>');
				tdList.push('<td>'+CommonUtil.getMapValue(CONSTANTS.BANK_NAME_MAP, element.bankCode)+'</td>');
				tdList.push('<td>'+CommonUtil.formatData(element.bankAcctNo)+'</td>');
				var transferTubeBusiType = $("#transferTubeBusiType").val();
				if (transferTubeBusiType == '1'){
					tdList.push(`<td class = "sellerCodeContainer"><select name="tSellerCode" id="tSellerCode" class="select"><option value="101">101-上海</option><option value="102">102-深圳</option></select> </td>`);
				}else {
					tdList.push(`<td class = "sellerCodeContainer"><input type="text" placeholder="请输入" id="tSellerCode" name="tSellerCode" isnull="false" datatype="s" errormsg="对方销售人代码"> </td>`);
				}

				tdList.push('<td><input type="text" class="tSellerTxAcctNo" name="tSellerTxAcctNo" ></td>');
				tdList.push('<td><input type="text" class="tOutletCode" name="tOutletCode" ></td>');


				var trAppendHtml = '<tr class="text-c" id = '+ element.productCode + '_' + element.cpAcctNo + '_' + element.protocolNo + ' >'+tdList.join() +'</tr>';
				$("#transOutCustBals").append(trAppendHtml);
				
				// 绑定点击事件
				$(".selectTransOutCustBal").on('click',function(){
					var selOutIndex = $(this).attr('data-index');
					var selAvailVol = TransfertubeOut.custBalDtlList[selOutIndex].availVol;
					if(selAvailVol == '0'){
						$("#selectTransOutCustBal_"+selOutIndex).attr("checked", false);
						CommonUtil.layer_tip("可用份额为0，不可转出！");
						return false;
					}
				});
				
			});
			
			// 设置录入中的总份额
			$("#totalVol").val(CommonUtil.formatAmount(totalOutVol));
		}
	},
	
	/**
	 * 校验输入份额(事件:onkeyup)
	 */
	validatorOutVol : function(index, availVol, thisObj){
		// console.log(availVol + " "+ thisObj.value);
		var outVol = thisObj.value;
		if(!/^[0-9]+\.?[0-9]{0,2}$/.test(outVol)){CommonUtil.layer_tip('只能输入数字且小数点后两位');thisObj.value='';}
		
		var cnOutVol = CommonUtil.digit_uppercase(outVol);
		$("#outVolCapital_"+index).val(cnOutVol.replace('元', '份'));
		
		if(outVol > availVol ){
			CommonUtil.layer_tip("转出份额不能大于可用份额");
			$(thisObj).css("border-color","red");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		} else{
			$(thisObj).css("border-color","");
		}
	},


	/**
	 * 查询税延基金转托管转出是否存在账户对应关系，调中登是否成功
	 */
	queryAccountRelation:function(custNo){
		if(CommonUtil.isEmpty(custNo)){
			CommonUtil.layer_tip("请先输入客户号或证件号");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		var transactionAccountID = $("#tSellerTxAcctNo").val();
		if(CommonUtil.isEmpty(custNo)){
			CommonUtil.layer_tip("请先输入对方销售人处投资者基金交易账号");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		var branchCode = $("#tOutletCode").val();
		if(CommonUtil.isEmpty(custNo)){
			CommonUtil.layer_tip("请先输入对方网点/席位号");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		var  uri= TmsCounterConfig.QUERY_ACCOUNT_RELATION_URL ||  {};
		var reqparamters = {};
		reqparamters.txAcctNo = custNo;
		reqparamters.branchCode = branchCode;
		reqparamters.transactionAccountID = transactionAccountID;
		reqparamters.taCode = QueryFundInfo.fundInfo.taCode;

		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,11000);
		paramters.needLoad = true;
		CommonUtil.ajaxAndCallBack(paramters, TransfertubeOut.queryAccountRelationResp);
	},

	queryAccountRelationResp:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		var body = data.body || {};
		var partiAppSheetNo = body.partiAppSheetNo;

		if(!CommonUtil.isSucc(respCode)){
			CommonUtil.layer_alert("税延基金转托管查询账户对应关系失败：" + respDesc);
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		// 查询中登查询账户对应关系结果
		TransfertubeOut.accountRelationFlag = '';
		// 起始时间
		TransfertubeOut.startTime = new Date().getTime();
		TransfertubeOut.queryAccountRelationResult(partiAppSheetNo);
		// 查询客户基金持仓
		for (;;){
			TransfertubeOut.endTime = new Date().getTime();
			TransfertubeOut.queryAccountRelationResult(partiAppSheetNo)
			if (TransfertubeOut.accountRelationFlag.length != 0 || (TransfertubeOut.endTime - TransfertubeOut.startTime > 10000)){
				break;
			}
			CommonUtil.sleep(500);
		}
		if (TransfertubeOut.endTime - TransfertubeOut.startTime > 10000){
			CommonUtil.layer_tip("接口超时请重试");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		if (TransfertubeOut.accountRelationFlag == '0'){
			CommonUtil.enabledBtn("confimTransOutBtn");
			CommonUtil.layer_alert("中登没有转入机构的对应关系，提交失败");
			return false;
		}

	},

	/**
	 * 查询税延基金转托管转出是否存在账户对应关系，调中登是否成功
	 */
	queryAccountRelationResult:function(partiAppSheetNo){
		var  uri= TmsCounterConfig.QUERY_ACCOUNT_RELATION_RESULT_URL ||  {};
		var reqparamters = {};
		reqparamters.partiAppSheetNo = partiAppSheetNo;
		reqparamters.needLoad = true;

		var paramters = CommonUtil.buildReqParams(uri, reqparamters,false,null,null);
		CommonUtil.ajaxAndCallBack(paramters, TransfertubeOut.queryAccountRelationResultResp);
	},

	queryAccountRelationResultResp:function(data){
		var respCode = data.code || '';
		var body = data.body || '';

		if(CommonUtil.isSucc(respCode) && body.length != 0){
			if (body === '0'){
				CommonUtil.enabledBtn("confimTransOutBtn");
				TransfertubeOut.accountRelationFlag = '0';
				CommonUtil.layer_alert("中登没有转入机构的对应关系，提交失败");
			}
			if (body === '1'){
				TransfertubeOut.accountRelationFlag = '1';
			}

		}
	},
	
	/***
	 * 确认转托管转出
	 */	
	confirm : function(action, dealAppNo, checkDtlOrder){
		CommonUtil.disabledBtn("confimTransOutBtn");

		if(CommonUtil.isEmpty(QueryCustInfo.custInfo.custNo)){
			CommonUtil.layer_tip("请先选择用户");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		var alset = '';
		var taxDalayFundCode = '';
		$("input[type='checkbox']:checked").each(function(index, element) {
			var jsonStr = element.value;
			var content = JSON.parse(jsonStr);
			var productCode = content.productCode;
			var productName = content.productName;
			var cpAcctNo = content.cpAcctNo;
			var protocolNo = content.protocolNo;
			var fundType = content.fundType;
			var fundSubType = content.fundSubType;
			var taxDelayFlag = content.taxDelayFlag;
			var id = productCode + '_' + cpAcctNo + '_' + protocolNo
			// $("#" + id).find()
			// //校验养老FOF不允许转托管转出
			// if("10" === fundType &&( '104' === fundSubType || '105' === fundSubType)){
			// 	alset = "养老FOF(" + productCode + "-" + productName +")不允许转托管转出";
			// }

			/**
			 * 转托管方式,1-一次转托管；2-两次转托管
			 */
			var chgTrusteeMode = content.chgTrusteeMode;
			var tSellerTxAcctNo = $("#" + id).find(".tSellerTxAcctNo").val();
			if(CommonUtil.isEmpty(tSellerTxAcctNo) && chgTrusteeMode == "1"){
				CommonUtil.layer_tip("转出基金" + productCode +"为一次转托管，对方销售人处投资者基金交易账号不能为空！");
			}
			if ("1" === taxDelayFlag){
				taxDalayFundCode = productCode;
			}

		})
		if (alset !== ''){
			CommonUtil.layer_tip(alset);
			CommonUtil.enabledBtn("confimTransOutBtn");
			return ;
		}

		// 税延基金，会查询是否存在账户对应关系，耗时比较长，最长10秒
		if(taxDalayFundCode !== ''){
			// 调中登查询账户对应关系
			TransfertubeOut.queryAccountRelation(QueryCustInfo.custInfo.custNo);
			if (TransfertubeOut.accountRelationFlag !== '1'){
				return false;
			}
		}

		// 基础格式校验
		var validRst = Valid.valiadateFrom($("#transfertubeOutForm"));
		if(!validRst.status){
			CommonUtil.layer_tip(validRst.msg);
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		// 转出录入信息
		// var fundInfoForm = JSON.stringify(QueryFundInfo.fundInfo);
		var custInfoForm = JSON.stringify(QueryCustInfo.custInfo);
		var transfertubeOutForm = $("#transfertubeOutForm").serializeObject();
		// 经办人信息
		var transactorInfoForm = $("#transactorInfoForm").serializeObject();

		// 转出用户校验
		if(!Validate.validateTransactorInfo(transactorInfoForm,QueryCustInfo.custInfo)){
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		transfertubeOutForm.appDtm = transfertubeOutForm.appDt +'' + transfertubeOutForm.appTm;
		if(CommonUtil.isEmpty(transfertubeOutForm.appTm)){
			CommonUtil.layer_tip("请输入下单时间");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		if(!Valid.valiadTradeTime(transfertubeOutForm.appTm)){
			CommonUtil.layer_tip("申请时间只能在9:30:00到14:59:59之间");
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}

		var selCount = 0;
		var selTotalOutVol = 0;
		var volOutFunds = [];
		
		// 转出的基金份额
		if(action == "add"){
			// 必须至少选一条
			var selectedOutCheckboxs = $("#transOutCustBals").find("input[type='checkbox'][name='checkTransOutBal']:checked");
			if(selectedOutCheckboxs.length <= 0){
				CommonUtil.layer_tip("请先选择转出份额信息");
				CommonUtil.enabledBtn("confimTransOutBtn");
				return false;
			}
			
			$(selectedOutCheckboxs).each(function(index,obj){
				var selOutIndex = $(obj).attr('data-index');
				var outVolFund = {};
				outVolFund.selIndex      = selOutIndex;
				outVolFund.dealDtlAppNo  = null;
				outVolFund.appVol        = $('#outVol_'+selOutIndex).val();//转出份额
				outVolFund.cpAcctNo		 = TransfertubeOut.custBalDtlList[selOutIndex].cpAcctNo;
				outVolFund.bankAcct      = TransfertubeOut.custBalDtlList[selOutIndex].bankAcctNo;
				outVolFund.bankCode      = TransfertubeOut.custBalDtlList[selOutIndex].bankCode;
				outVolFund.protocolNo 	 = TransfertubeOut.custBalDtlList[selOutIndex].protocolNo;
				outVolFund.protocolType	 = TransfertubeOut.custBalDtlList[selOutIndex].protocolType;
				outVolFund.preAppVol	 = TransfertubeOut.custBalDtlList[selOutIndex].availVol;//转出前份额
				outVolFund.preFrznVol	 = TransfertubeOut.custBalDtlList[selOutIndex].unconfirmedVol;//转出前冻结份额
				outVolFund.productChannel= QueryFundInfo.fundInfo.productChannel;//转出产品渠道
				var id = TransfertubeOut.custBalDtlList[selOutIndex].productCode + '_' + TransfertubeOut.custBalDtlList[selOutIndex].cpAcctNo + '_' + TransfertubeOut.custBalDtlList[selOutIndex].protocolNo;
				outVolFund.fundCode      = TransfertubeOut.custBalDtlList[selOutIndex].productCode;//基金代码
				outVolFund.fundName      = TransfertubeOut.custBalDtlList[selOutIndex].productName;//基金名称
				outVolFund.fundShareClass      = TransfertubeOut.custBalDtlList[selOutIndex].fundShareClass;
				outVolFund.productClass      = TransfertubeOut.custBalDtlList[selOutIndex].productClass;
				outVolFund.taCode      = TransfertubeOut.custBalDtlList[selOutIndex].taCode;
				outVolFund.tSellerCode   = $("#" + id).find("#tSellerCode").val();
				outVolFund.tSellerTxAcctNo   = $("#" + id).find(".tSellerTxAcctNo").val();
				outVolFund.tOutletCode   = $("#" + id).find(".tOutletCode").val();

			
				selTotalOutVol = Number(selTotalOutVol) + Number(outVolFund.appVol);
				volOutFunds.push(outVolFund);
			});
			selCount = selectedOutCheckboxs.length;
			
		} else if(action == "update"){
			
			$(checkDtlOrder).each(function(index,obj){
				var outVolFund = {};
				outVolFund.selIndex      = index;
				outVolFund.dealDtlAppNo  = obj.dealDtlAppNo;
				outVolFund.appVol        = $('#outVol_'+index).val();//转出份额
				outVolFund.cpAcctNo		 = obj.cpAcctNo;
				outVolFund.bankAcct      = obj.bankAcct;
				outVolFund.bankCode      = obj.bankCode;
				outVolFund.protocolNo 	 = obj.protocolNo;
				outVolFund.protocolType	 = obj.protocolType;
				outVolFund.preAppVol	 = obj.preAppVol;//转出前份额
				outVolFund.preFrznVol	 = obj.preFrznVol;//转出前冻结份额
				outVolFund.productChannel= QueryFundInfo.fundInfo.productChannel;//转出产品渠道
			
				selTotalOutVol = Number(selTotalOutVol) + Number(CommonUtil.unFormatAmount(outVolFund.appVol));
				volOutFunds.push(outVolFund);
			});
			selCount = checkDtlOrder.length;
		}
		
		// 校验提交的转托管基金转出份额是否为空
		var checkFlag = true;
		$(".outVolClass").css("border-color","");
		$(volOutFunds).each(function(index,obj){
			var outVol = obj.appVol;
			var selIndex = obj.selIndex;
			if(isEmpty(outVol)){
				CommonUtil.layer_tip("转出份额不能为空");
				$("#outVol_"+selIndex).css("border-color","red");
				checkFlag = false;
				CommonUtil.enabledBtn("confimTransOutBtn");
			}
		});
		if(!checkFlag){
			CommonUtil.enabledBtn("confimTransOutBtn");
			return false;
		}
		
		//console.log(selTotalOutVol);
		var statisHtml = '转出笔数：'+selCount+'笔；总转出份额：'+CommonUtil.formatAmount(selTotalOutVol)+'份，大写：'+CommonUtil.digit_uppercase(selTotalOutVol).replace('元', '份')+"。";
		var tipmsg = statisHtml + "<br/>是否确认提交？";
		
		//询问框  
		layer.confirm(tipmsg, {  
		  btn: ['确认','取消']
		}, function(index){  
			
			CommonUtil.disabledBtn("confimTransOutBtn");
			
			// 提交
			var uri = TmsCounterConfig.TRANSFERTUBE_OUT_FUND_CONFIRM_URL ||  {};
			var reqparamters = {"dealAppNo":CommonUtil.isEmpty(dealAppNo) ? null : dealAppNo,
					"custInfoForm":custInfoForm,
					"transOutVolCmd": JSON.stringify(volOutFunds),
					"transfertubeOutForm": JSON.stringify(transfertubeOutForm),
					"transactorInfoForm":JSON.stringify(transactorInfoForm)
				};
			//console.log(reqparamters);
			var paramters = CommonUtil.buildReqParams(uri, reqparamters,true,null,null);
			CommonUtil.ajaxAndCallBack(paramters, TransfertubeOut.callBack);
			
		}, function(){  
			layer.closeAll();
			CommonUtil.enabledBtn("confimTransOutBtn");
		}); 
	},
	
	callBack:function(data){
		var respCode = data.code || '';
		var respDesc = data.desc || '';
		
		if(CommonUtil.isSucc(respCode)){
			CommonUtil.disabledBtn("confimTransOutBtn");
			CommonUtil.layer_tip("提交成功");
		}else{
			CommonUtil.enabledBtn("confimTransOutBtn");
			CommonUtil.layer_alert("提交失败，"+respDesc);
		}
	},




};



