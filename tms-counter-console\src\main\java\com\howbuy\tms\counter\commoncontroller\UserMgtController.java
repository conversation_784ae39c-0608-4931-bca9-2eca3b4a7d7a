/**
 *Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.counter.commoncontroller;

import com.howbuy.interlayer.product.model.UserAccountModel;
import com.howbuy.interlayer.product.service.permission.UserService;
import com.howbuy.tms.common.utils.PasswordHelper;
import com.howbuy.tms.counter.common.Constants;
import com.howbuy.tms.counter.common.TmsCounterResult;
import com.howbuy.tms.counter.common.TmsCounterResultEnum;
import com.howbuy.tms.counter.common.exception.TmsCounterException;
import com.howbuy.tms.counter.common.util.WebUtil;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * @description:(柜台用户管理控制器)
 * <AUTHOR>
 * @date 2018年7月24日 下午2:45:20
 * @since JDK 1.6
 */
@Controller
public class UserMgtController extends AbstractController {

    private Logger logger = LoggerFactory.getLogger(UserMgtController.class);

    @Autowired
    private UserService userService;

    /**
     * 
     * queryLoginUserInfo:(查询登陆个人信息)
     * 
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2018年7月24日 下午6:44:20
     */
    @RequestMapping("tmscounter/queryLoginUserInfo.htm")
    public void queryLoginUserInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info(">>>>>>>查询登陆个人信息");
        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);

        UserAccountModel userAccount = null;
        if (user != null) {
            userAccount = userService.getUserByUserId(user.getUserId());
        }

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        Map<String, Object> body = new HashMap<String, Object>(16);
        body.put("userAccount", userAccount);
        rst.setBody(body);
        WebUtil.write(response, rst);
    }

    /**
     * modifyPwd:(修改用户密码)
     * 
     * @param request
     * @param response
     * @throws Exception
     * <AUTHOR>
     * @date 2018年7月24日 下午2:50:58
     */
    @RequestMapping("tmscounter/modifyPwd.htm")
    public ModelAndView modifyPwd(HttpServletRequest request, HttpServletResponse response) throws Exception {
        logger.info(">>>>>>>修改用户密码");
        String oldPwd = ServletRequestUtils.getStringParameter(request, "oldPwd", null);
        String newPwdOne = ServletRequestUtils.getStringParameter(request, "newPwdOne", null);
        String newPwdTwo = ServletRequestUtils.getStringParameter(request, "newPwdTwo", null);

        if (!StringUtils.equals(newPwdOne, newPwdTwo)) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "两个新密码不相同，请重新输入。");
        }

        UserAccountModel user = (UserAccountModel) request.getSession().getAttribute(Constants.SESSION_USER);
        if (user == null) {
            throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "用户未登陆，不可以修改密码，请登陆！");
        }
        /*
         * if (Constants.SYSTEM_ADMIN.equals(user.getUserName())) { throw new
         * CustomResultException(ReturnCodeNum.SYSTEMERROR.getCode(),
         * "管理员不准修改密码。"); }
         */

        TmsCounterResult rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
        //try {
            String encryptPassword = PasswordHelper.encryptPassword(user.getSalt(), oldPwd);
            if (!encryptPassword.equals(user.getPassword())) {
                throw new TmsCounterException(TmsCounterResultEnum.FAILD.getCode(), "原始密码不正确。");
            }
            String salt = PasswordHelper.generateSalt();
            user.setSalt(salt);
            user.setPassword(PasswordHelper.encryptPassword(salt, newPwdTwo));

            int count = userService.updateUserPwd(user);
            if (count > 0) {
                rst = new TmsCounterResult(TmsCounterResultEnum.SUCC);
            }
        /*
        } catch (Exception e) {
            rst = new TmsCounterResult(TmsCounterResultEnum.FAILD.code, e.getMessage());
            logger.error("modify user password error", e);
        }*/

        WebUtil.write(response, rst);
        return null;
    }

}
