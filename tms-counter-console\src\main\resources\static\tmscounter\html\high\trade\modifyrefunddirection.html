<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <!--[if lt IE 9]>
<script type="text/javascript" src="lib/html5.js"></script>
<script type="text/javascript" src="lib/respond.min.js"></script>
<script type="text/javascript" src="lib/PIE_IE678.js"></script>
<![endif]-->
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui/css/H-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/H-ui.admin.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/Hui-iconfont/1.0.7/iconfont.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/icheck/icheck.css" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/skin/default/skin.css" id="skin" />
    <link rel="stylesheet" type="text/css" href="../../../static/h-ui.admin/css/style.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/jquery-ui-1.9.2.custom.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.css" />
    <link rel="stylesheet" type="text/css" href="../../../lib/jquery/1.9.1/jquery.multiselect.filter.css" />
    <title>柜台交易查询</title>
</head>

<body>
    <nav class="breadcrumb"><i class="Hui-iconfont">&#xe67f;</i> 首页 <span class="c-gray en">&gt;</span> 高端业务-交易下单 <span class="c-gray en">&gt;</span> 修改回款方向 <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a></nav>
    <div class="page-container">
        <div class="containner_all">
        <form id="queryConditonForm">
            <div class="container_box">
                <p class="mainTitle mt10">修改回款方向</p>
                <div class="cp_top mt30">
                    <span class="normal_span">客户号：</span>
                    <input type="text" name="txAcctNo"  id="custNo" placeholder="双击查询客户号">
                    <span class="normal_span ml30">订单号：</span>
                    <input type="text" name="dealNo" placeholder="请输入">
                    <span class="normal_span">产品代码：</span>
                    <input type="text" name="productCode"  id="productCode">
                    <a href="javascript:void(0)" class="btn radius btn-secondary ml30" id="queryTradeBtn">查询</a>
                </div>
                <div>
                    <span class="normal_span ml30">业务类型：</span>
                    <span class="select-box inline">
                       <select id="selectMBusiCode" name="mBusiCode" class="select">
                           <option value="">全部</option>
                          <option value="1120">认购</option>
                          <option value="1122">申购</option>
                          <option value="1124">赎回</option>
                       </select>
                    </span>
                    <span class="normal_span">上报开始日期：</span>
                    <input name="queryBeginDt" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYYMMDD'})">
                    <span class="normal_span ml30">上报结束日期：</span>
                    <input name="queryEndDt" class="input-text laydate-icon" onclick="laydate({isdate: true, format: 'YYYYMMDD'})">
                </div>
            </div>
        </form>
        </div>
    </div>
    <div class="page-container">
        <p class="main_title">查询结果</p>
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
               <thead>
                   <tr class="text-c">
                        <th>选择</th>
                        <th>交易日</th>
                        <th>客户号</th>
                        <th>客户名称</th>
                        <th>证件号</th>
                        <th>基金代码</th>
                        <th>基金简称</th>
                        <th>业务类型</th>
                        <th>中台订单号</th>
                        <th>申请金额</th>
                        <th>申请份额</th>
                        <th>确认金额</th>
                        <th>确认份额</th>
                        <th>申请日期</th>
                        <th>申请时间</th>
                        <th>订单状态</th>
                    </tr>
               </thead>
                <tbody id="rsList">
                </tbody>
            </table>
        </div>
        <div class="clear page_all">
            <div class="fy_part fr mt20" id="pageView"></div>
        </div>
    </div>
    <form id = "refundForm">
        <div class="result2_tab">
            <table class="table table-border table-bordered table-hover table-bg table-sort">
                <tr class="text-c">
                    <th>修改前</th>
                    <th></th>
                    <th>修改后</th>
                    <th></th>
                </tr>
                <tr class="text-c">
                    <td>当前订单状态</td>
                    <td id="orderStatusId"></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr class="text-c">
                    <td>当前回款方向</td>
                    <td id="refundDirectionId"></td>
                    <td class="text-c">修改后的回款方向</td>
                    <td id="newRefundDirectionId"></td>
                </tr>
                <tr class="text-c">
                    <td>回可用余额的金额</td>
                    <td id="refundAmtId"></td>
                    <td>回可用余额的金额</td>
                    <td id="newRefundAmtId"></td>
                </tr>
                <tr class="text-c">
                    <td>回可用余额的备注</td>
                    <td id="refundMemoId"></td>
                    <td>回可用余额的备注</td>
                    <td id="newRefundMemoId"></td>
                </tr>
            </table>
        </div>
        <a href="javascript:void(0)" class="btn radius btn-success ml30" id="confirmBtn">提交</a>
    </form>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.serialize-object.min.js"></script>
    <script type="text/javascript" src="../../../lib/layer/2.1/layer.js"></script>
    <script type="text/javascript" src="../../../lib/laydate/laydate.js"></script>
    <script type="text/javascript" src="../../../lib/laypage/laypage.js"></script>
    <script type="text/javascript" src="../../../static/h-ui/js/H-ui.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/H-ui.admin.js"></script>
    <script type="text/javascript" src="../../../static/h-ui.admin/js/main.js"></script>
    <script type="text/javascript" src="../../../js/baseconfig.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/common.js?v=4.1.12"></script>
    <script type="text/javascript" src="../../../js/config.js?v=20200301002"></script>
    <script type="text/javascript" src="../../../js/high/query/querycustinfosubpage.js"></script>
    <script type="text/javascript" src="../../../js/commonutil.js?v=3.4.94"></script>
    <script type="text/javascript" src="../../../js/high/trade/modifyrefunddirection.js?v=4.1.12"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/ui-1.2.1/jquery-ui.min.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.js"></script>
    <script type="text/javascript" src="../../../lib/jquery/1.9.1/jquery.multiselect.filter.js"></script>
</body>

</html>